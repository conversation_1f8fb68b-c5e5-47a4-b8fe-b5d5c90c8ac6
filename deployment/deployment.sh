#!/bin/bash
set -e

# Function to handle errors
error_handler() {
    local error_message=$1
    echo "::error::$error_message"
    exit 1
}

# Check if KEY_VAULT_NAME is set
if [ -z "${KEY_VAULT_NAME}" ]; then
    error_handler "KEY_VAULT_NAME environment variable is not set"
    exit 1
fi

# Check that profile is set
if [ -z "${PROFILE}" ]; then
    error_handler "PROFILE environment variable is not set"
    exit 1
fi

# Clear any existing Azure authentication
rm -rf ~/.azure

# Login to Azure and ACR
az login --identity --client-id "${SERVER_IDENTITY_CLIENT_ID}" > /dev/null || {
    error_handler "Failed to login to Azure using managed identity"
    exit 1
}

# Verify account login
az acr login --name ${ACR_NAME} || {
    error_handler "Failed to login to ACR"
    exit 1
}

# Create a temporary env file
ENV_FILE=".env"
echo "PROFILE=${PROFILE}" > $ENV_FILE

# Fetch secrets and append to env file
for secret in $(az keyvault secret list --vault-name "${KEY_VAULT_NAME}" --query '[].name' -o tsv); do
    value=$(az keyvault secret show --vault-name "${KEY_VAULT_NAME}" --name "$secret" --query 'value' -o tsv)
    # Convert dashes in ENV variable to underscore
    secret_name=${secret//-/_}  
    echo "$secret_name=$value" >> $ENV_FILE
done

# Clear disk space
docker system prune -af

# Run docker compose with env file
export DOCKER_CLIENT_TIMEOUT=2400 
export COMPOSE_HTTP_TIMEOUT=2400

docker compose pull || {
    error_handler "Failed to pull Docker images"
}

docker compose up -d || {
    error_handler "Failed to start containers"
}


##############################################################
# Create workers using variables from env file
while IFS='=' read -r name value; do
    # Skip empty lines and comments
    [[ -z "$name" || "$name" == \#* ]] && continue
    # Temporarily set each variable just for the worker creation
    export "$name=$value"
done < "$ENV_FILE"

# Check if required environment variables are set
if [ -z "$AZ_RESOURCE_GROUP" ] || [ -z "$PREFECT_AZ_ACI_IDENTITY" ] || [ -z "$PREFECT_API_URL" ] || [ -z "$PREFECT_AZ_VNET_NAME" ] || [ -z "$PREFECT_AZ_SUBNET_NAME" ]; then
    error_handler "Required environment variables not set: AZ_RESOURCE_GROUP, PREFECT_AZ_ACI_IDENTITY, PREFECT_API_URL, PREFECT_AZ_VNET_NAME, PREFECT_AZ_SUBNET_NAME"
fi

# Worker creation loop
for i in 01 02; do
    # Set worker name with index
    WORKER_NAME="prefect-simulation-worker-${i}"
    
    # Use command to check container existence without error output
    if command az container show \
        --name "$WORKER_NAME" \
        --resource-group "$AZ_RESOURCE_GROUP" \
        --query "name" \
        --output tsv &>/dev/null; then
        echo "Worker container $WORKER_NAME already exists, skipping creation"
        continue
    fi

    # Create ACI worker
    echo "Creating worker container $WORKER_NAME..."
    az container create \
        --name $WORKER_NAME \
        --resource-group $AZ_RESOURCE_GROUP \
        --assign-identity $PREFECT_AZ_ACI_IDENTITY \
        --image "prefecthq/prefect:2.20.16-python3.8" \
        --secure-environment-variables PREFECT_API_URL=$PREFECT_API_URL \
        --command-line "/bin/bash -c 'pip install prefect-azure && prefect worker start --pool simulation --type azure-container-instance'" \
        --os-type Linux \
        --memory 8 \
        --cpu 4 \
        --vnet $PREFECT_AZ_VNET_NAME \
        --subnet $PREFECT_AZ_SUBNET_NAME > /dev/null

    echo "✅ Worker container $WORKER_NAME created"
done

# Unset all variables that were in the env file
while IFS='=' read -r name _; do
    [[ -z "$name" || "$name" == \#* ]] && continue
    unset "$name"
done < "$ENV_FILE"

# Clean up env file immediately after docker compose starts
rm $ENV_FILE