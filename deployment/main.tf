terraform {
  backend "azurerm" {
    # Define backend to store tfstate. Secrets will be injected here
    resource_group_name  = ""
    storage_account_name = ""
    container_name       = ""
    key                  = ""
  }

  required_providers {
    # Include Azure plugin
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 4.23.0"
    }
  }
  required_version = "~> 1.11.0"
}

# Configure the Azure Provider
provider "azurerm" {
  features {}
}

locals {
  # Convert to lowercase and remove all non-alphanumeric characters
  clean_env_name = replace(
    lower(var.environment),
    "/[^a-z0-9]/",
    ""
  )

  # Ensure final registry name meets Azure requirements
  registry_name = "${local.clean_env_name}alephappregistry"

  key_vault_name = "${var.environment}-${var.app_name}-kv"

  storage_account_name = "${local.clean_env_name}${var.app_name}storage"
}

# Create a resource group
resource "azurerm_resource_group" "rg" {
  name     = var.resource_group_name
  location = var.location
}

# Create container registry
resource "azurerm_container_registry" "acr" {
  name                = local.registry_name
  resource_group_name = var.resource_group_name
  location            = var.location
  sku                 = "Basic"
}

# Create VNET
resource "azurerm_virtual_network" "vnet" {
  name                = "${var.environment}-${var.location}-vnet"
  address_space       = ["10.0.0.0/16"]
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name
}

resource "azurerm_subnet" "main" {
  name             = "main"
  resource_group_name = azurerm_resource_group.rg.name
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes = ["10.0.0.0/24"]

  lifecycle {
    create_before_destroy = true
  }
}

resource "azurerm_subnet" "aci" {
  name             = "aci"
  resource_group_name = azurerm_resource_group.rg.name
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes = ["********/24"]

  delegation {
    name = "aci-delegation"
    service_delegation {
      name = "Microsoft.ContainerInstance/containerGroups"
    }
  }
}

resource "azurerm_subnet_network_security_group_association" "aci_nsg" {
  subnet_id                 = azurerm_subnet.aci.id
  network_security_group_id = azurerm_network_security_group.prefect_vm_nsg.id
}

# Create Network Security Group
resource "azurerm_network_security_group" "prefect_vm_nsg" {
  name                = "${var.prefect_vm_name}-nsg"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name

  # Allow SSH inbound
  security_rule {
    name                       = "default-allow-ssh"
    priority                   = 1000
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range         = "*"
    destination_port_range     = "22"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  # Allow port 4200 inbound
  # security_rule {
  #   name                       = "AllowMyIpAddressCustom4200Inbound"
  #   priority                   = 1010
  #   direction                  = "Inbound"
  #   access                     = "Allow"
  #   protocol                   = "*"
  #   source_port_range         = "*"
  #   destination_port_range     = "4200"
  #   source_address_prefix      = "*"
  #   destination_address_prefix = "*"
  # }
}

# Create User Assigned Managed Identity
resource "azurerm_user_assigned_identity" "prefect_server_identity" {
  name                = "prefect-server"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
}

# Create public IP for app server
resource "azurerm_public_ip" "prefect_server_pip" {
  name                = "${var.prefect_vm_name}-public-ip"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  allocation_method   = "Static"
  sku                = "Standard"
}

# Create Network Interface for VM
resource "azurerm_network_interface" "prefect_server_nic" {
  name                = "${var.prefect_vm_name}-nic"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name

  ip_configuration {
    name                          = "internal"
    subnet_id                     = azurerm_subnet.main.id
    private_ip_address_allocation = "Static"
    private_ip_address            = var.prefect_vm_private_ip
    public_ip_address_id = azurerm_public_ip.prefect_server_pip.id
  }

  depends_on = [azurerm_subnet.main]

  lifecycle {
    create_before_destroy = true
  }
}

# Associate NSG with the network interface
resource "azurerm_network_interface_security_group_association" "vm_nsg_association" {
  network_interface_id      = azurerm_network_interface.prefect_server_nic.id
  network_security_group_id = azurerm_network_security_group.prefect_vm_nsg.id
}

# Create VM for Prefect Server
resource "azurerm_linux_virtual_machine" "prefect_server" {
  name                            = var.prefect_vm_name
  resource_group_name             = azurerm_resource_group.rg.name
  location                        = azurerm_resource_group.rg.location
  size                            = "Standard_B2ms"
  admin_username                  = var.prefect_vm_username
  admin_password                  = var.prefect_vm_password
  disable_password_authentication = false

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.prefect_server_identity.id]
  }

  network_interface_ids = [
    azurerm_network_interface.prefect_server_nic.id
  ]

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Premium_LRS"
  }

  source_image_reference {
    publisher = "Canonical"
    offer     = "0001-com-ubuntu-server-jammy"
    sku       = "22_04-lts"
    version   = "latest"
  }

  custom_data = base64encode((templatefile("${path.module}/templates/prefect-server-config.tpl", {
    admin_username = var.prefect_vm_username
  })))

  depends_on = [azurerm_network_interface.prefect_server_nic]
}

# Create custom role definition for Container Instance
resource "azurerm_role_definition" "aci_contributor" {
  name        = "Container Instances Contributor (${var.environment})"
  scope       = azurerm_resource_group.rg.id
  description = "Can create, delete, and monitor container instances."

  permissions {
    actions = [
      "Microsoft.ManagedIdentity/userAssignedIdentities/assign/action",
      "Microsoft.Resources/deployments/*",
      "Microsoft.ContainerInstance/containerGroups/*",
      "Microsoft.Resources/subscriptions/resourceGroups/read",
      "Microsoft.Network/virtualNetworks/subnets/*"  
    ]
    not_actions = []
  }

  assignable_scopes = [
    azurerm_resource_group.rg.id
  ]
}

# Create User Assigned Identity for ACI
resource "azurerm_user_assigned_identity" "aci_identity" {
  name                = "prefect-aci"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
}

# Assign custom role to ACI identity
resource "azurerm_role_assignment" "aci_contributor" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = azurerm_role_definition.aci_contributor.name
  principal_id         = azurerm_user_assigned_identity.aci_identity.principal_id
}

# Assign AcrPull role to ACI identity
resource "azurerm_role_assignment" "acr_pull" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_user_assigned_identity.aci_identity.principal_id
}

# Assign Network User role to ACI identity
resource "azurerm_role_assignment" "network_user" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "Windows 365 Network User"
  principal_id         = azurerm_user_assigned_identity.aci_identity.principal_id
}

# Create public IP for app server
resource "azurerm_public_ip" "app_server_pip" {
  name                = "${var.app_vm_name}-public-ip"
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  allocation_method   = "Static"
  sku                = "Standard"
}

# Create Network Interface for app VM
resource "azurerm_network_interface" "app_server_nic" {
  name                = "${var.app_vm_name}-nic"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name

  ip_configuration {
    name                          = "internal"
    subnet_id                     = azurerm_subnet.main.id
    private_ip_address_allocation = "Static"
    private_ip_address            = var.app_vm_private_ip
    public_ip_address_id = azurerm_public_ip.app_server_pip.id
  }

  depends_on = [azurerm_subnet.main, azurerm_network_interface.prefect_server_nic]

  lifecycle {
    create_before_destroy = true
  }
}

# Create Network Security Group for app server
resource "azurerm_network_security_group" "app_server_nsg" {
  name                = "${var.app_vm_name}-nsg"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name

  # Allow SSH inbound
  security_rule {
    name                       = "default-allow-ssh"
    priority                   = 1000
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range         = "*"
    destination_port_range     = "22"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }

  # Allow HTTPS inbound
  security_rule {
    name                       = "AllowAnyHTTPSInbound"
    priority                   = 1030
    direction                  = "Inbound"
    access                     = "Allow"
    protocol                   = "Tcp"
    source_port_range         = "*"
    destination_port_range     = "443"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }
}

# Associate NSG with the network interface
resource "azurerm_network_interface_security_group_association" "app_server_nsg_association" {
  network_interface_id      = azurerm_network_interface.app_server_nic.id
  network_security_group_id = azurerm_network_security_group.app_server_nsg.id
}

data "azurerm_public_ip" "app_server_pip_data" {
  name                = azurerm_public_ip.app_server_pip.name
  resource_group_name = azurerm_resource_group.rg.name
}

data "azurerm_client_config" "current" {}

# Create key vault for storing secrets
resource "azurerm_key_vault" "app_vault" {
  name                = local.key_vault_name
  resource_group_name = azurerm_resource_group.rg.name
  location            = azurerm_resource_group.rg.location
  tenant_id          = data.azurerm_client_config.current.tenant_id
  sku_name           = "standard"

  # Enable RBAC instead of access policies
  enable_rbac_authorization = true
}

# Assign role for VMs to be able to pull from keyvault. 
resource "azurerm_role_assignment" "vm_secret_user" {
  scope                = azurerm_key_vault.app_vault.id
  role_definition_name = "Key Vault Secrets User"
  principal_id         = azurerm_user_assigned_identity.prefect_server_identity.principal_id
}

# Assign custom role to ACI identity
resource "azurerm_role_assignment" "vm_aci_contributor" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = azurerm_role_definition.aci_contributor.name
  principal_id         = azurerm_user_assigned_identity.prefect_server_identity.principal_id
}

# Assign role for VMs to pull container images from ACR
resource "azurerm_role_assignment" "vm_acr_user" {
  scope                = azurerm_resource_group.rg.id
  role_definition_name = "AcrPull"
  principal_id         = azurerm_user_assigned_identity.prefect_server_identity.principal_id
}

# Create VM for Alephapp Server
resource "azurerm_linux_virtual_machine" "app_server" {
  name                            = var.app_vm_name
  resource_group_name             = azurerm_resource_group.rg.name
  location                        = azurerm_resource_group.rg.location
  size                            = "Standard_B4ms"
  admin_username                  = var.app_vm_username
  admin_password                  = var.app_vm_password
  disable_password_authentication = false

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.prefect_server_identity.id]
  }

  network_interface_ids = [
    azurerm_network_interface.app_server_nic.id
  ]

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Premium_LRS"
  }

  source_image_reference {
    publisher = "Canonical"
    offer     = "0001-com-ubuntu-server-jammy"
    sku       = "22_04-lts"
    version   = "latest"
  }

  custom_data = base64encode((templatefile("${path.module}/templates/app-server-config.tpl", {
    public_ip = data.azurerm_public_ip.app_server_pip_data.ip_address
    admin_username = var.app_vm_username
  })))

  depends_on = [azurerm_network_interface.app_server_nic]
}

# Create blob storage account 
resource "azurerm_storage_account" "main" {
  name                     = local.storage_account_name
  resource_group_name      = azurerm_resource_group.rg.name
  location                = azurerm_resource_group.rg.location
  account_tier             = "Standard"
  account_replication_type = "RAGRS"
  account_kind            = "StorageV2"
  min_tls_version         = "TLS1_2"
}