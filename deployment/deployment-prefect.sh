#!/bin/bash
set -e

# Function to handle errors
error_handler() {
    local error_message=$1
    echo "::error::$error_message"
    exit 1
}

# Check if KEY_VAULT_NAME is set
if [ -z "${KEY_VAULT_NAME}" ]; then
    error_handler "KEY_VAULT_NAME environment variable is not set"
    exit 1
fi

# Clear any existing Azure authentication
rm -rf ~/.azure

# Login to Azure and ACR
az login --identity --client-id "${SERVER_IDENTITY_CLIENT_ID}" > /dev/null || {
    error_handler "Failed to login to Azure using managed identity"
    exit 1
}

# Verify account login
az acr login --name ${ACR_NAME} || {
    error_handler "Failed to login to ACR"
    exit 1
}

# Create a temporary env file
ENV_FILE=".env"
touch $ENV_FILE

# List of specific secrets to fetch
declare -A secret_mapping=(
    ["PREFECT-POSTGRES-HOST"]="POSTGRES_HOST"
    ["PREFECT-POSTGRES-DB"]="POSTGRES_DB"
    ["PREFECT-POSTGRES-USER"]="POSTGRES_USER"
    ["PREFECT-POSTGRES-PASSWORD"]="POSTGRES_PASSWORD"
)

# Fetch specific secrets and store without prefix
for prefect_secret in "${!secret_mapping[@]}"; do
    env_var=${secret_mapping[$prefect_secret]}
    value=$(az keyvault secret show --vault-name "${KEY_VAULT_NAME}" --name "$prefect_secret" --query 'value' -o tsv) || {
        error_handler "Failed to fetch secret: $prefect_secret from Key Vault: ${KEY_VAULT_NAME}"
        exit 1
    }
    echo "$env_var=$value" >> $ENV_FILE
done

# Run docker compose with env file
docker compose pull || {
    error_handler "Failed to pull Docker images"
}

docker compose up -d || {
    error_handler "Failed to start containers"
}

# Clean up env file immediately after docker compose starts
rm $ENV_FILE