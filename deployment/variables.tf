variable "app_name" {
  type    = string
  default = "alephapp"
}

variable "resource_group_name" {
  type = string
}

variable "location" {
  type    = string
  default = "southeastasia"
}

variable "environment" {
  type    = string
  default = "uat"
}

variable "prefect_vm_username" {
  type      = string
  default   = ""
  sensitive = true
}

variable "prefect_vm_password" {
  type      = string
  default   = ""
  sensitive = true
}

variable "prefect_vm_private_ip" {
  type        = string
  description = "Static private IP address for the VM"
  default     = "********"
  sensitive   = true
}

variable "prefect_vm_name" {
  type    = string
  default = "prefect-server"
}

variable "app_vm_name" {
  type    = string
  default = "alephapp-server"
}

variable "app_vm_username" {
  type      = string
  default   = ""
  sensitive = true
}

variable "app_vm_password" {
  type      = string
  default   = ""
  sensitive = true
}

variable "app_vm_private_ip" {
  type        = string
  description = "Static private IP address for app VM"
  default     = "********"
  sensitive   = true
}