# Create named output
output "key_vault_name" {
  value = azurerm_key_vault.app_vault.name
  description = "Name of the created Azure Key Vault"
}

output "acr_name" {
  value = azurerm_container_registry.acr.name
  description = "Name of the created Azure Container Registry"
}

output "app_server_pip" {
  value = azurerm_public_ip.app_server_pip.ip_address
  description = "Public IP Address of App Server"
  sensitive = true
}

output "prefect_server_pip" {
  value = azurerm_public_ip.prefect_server_pip.ip_address
  description = "Public IP Address of Prefect Server"
  sensitive = true
}

output "server_identity_client_id" {
  value = azurerm_user_assigned_identity.prefect_server_identity.client_id
  description = "Client ID of the Server VM Identity"
  sensitive = true
}