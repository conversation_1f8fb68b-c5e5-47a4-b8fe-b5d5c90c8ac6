#cloud-config
packages:
  - jq
  - curl
  - sudo

runcmd:
  # Set env variables
  # - echo "export PREFECT_LOGGING_LEVEL=INFO" >> /etc/environment
  # - source /etc/environment

  # Set up Docker apt repository
  - sudo apt-get update

  - sudo apt-get -y install ca-certificates curl

  - sudo install -m 0755 -d /etc/apt/keyrings
  - sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
  - sudo chmod a+r /etc/apt/keyrings/docker.asc
  - echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu $(. /etc/os-release && echo "$${UBUNTU_CODENAME:-$$VERSION_CODENAME}") stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
  
  - sudo apt-get update

  # Install Docker
  - sudo apt-get -y install docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin

  # Verify docker is installed
  - docker version

  # Get admin user and add user to docker group for access to docker
  # - VAULTURL="https://alephapp-prefect-kv.vault.azure.net/"
  # - ACCESS_TOKEN=$(curl "http://169.254.169.254/metadata/identity/oauth2/token?api-version=2018-02-01&resource=https://vault.azure.net" -H Metadata:true -s | jq -r ".access_token")
  # - echo ">>> $ACCESS_TOKEN"
  # - |
  #   ADMIN_USER=$(curl "$VAULTURL/secrets/ALEPHAPP-SERVER-USERNAME?api-version=2016-10-01" -H "Authorization: Bearer $ACCESS_TOKEN" -s | jq -r .value)
  - ADMIN_USER=${admin_username}
  - echo ">>> $ADMIN_USER"
  - sudo usermod -aG docker $ADMIN_USER
  - newgrp docker

  # Install Azure CLI
  - curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash

  # Configure Docker to start on boot
  - systemctl enable docker.service
  - systemctl enable containerd.service
  - systemctl status docker.service
  - systemctl status containerd.service

final_message: VM started after $uptime seconds