{% extends 'base.html' %}


{% block title %}Referral Page{% endblock %}

{% block content %}
    <div class="signup-page d-flex justify-content-center align-items-center poppins-regular">
        <div class="login-page d-flex justify-content-center align-items-center poppins-regular">
            <div class="login-info left p-5">
                <div class="text-center">
                    <img src="{{ url_for('static', filename='images/aleph_logo_login_page.png') }}" 
                        alt="Logo" 
                        class="login-page-logo mb-4 me-3">
                </div>
                <p class="poppins-semibold mb-4 fs-5">Achieve operational excellence in a few clicks</p>
                <div>
                    <div class="d-flex mb-2">
                        <div class="circle"></div>
                        <p>Predict experimentation results</p>
                    </div>
                    <div class="d-flex mb-2">
                        <div class="circle"></div>
                        <p>Troubleshoot inefficiency</p>
                    </div>
                    <div class="d-flex mb-2">
                        <div class="circle"></div>
                        <p>Identify pathway to excellence</p>
                    </div>
                </div>
            </div>
            <div class="user-info border p-5">
                <h3 class="poppins-semibold mb-5 text-center">Refer a Friend</h3>
                <p class="text-center bg-success text-light border fs-5 p-2 mb-3">
                    Your request to create an account has been sent to our team! Please check your inbox (or spam) for next steps.
                </p>
                
                <form method="POST" action="{{ url_for('referral') }}">

                    {{ form.hidden_tag() }}  <!-- CSRF Protection -->
                    
                    <div>
                        {{ form.name(class="fs-5 form-control", placeholder="Name") }}
                        <p class="text-danger">
                            {{ form.name.errors[0] if form.name.errors else '' }}
                        </p>
                        <br>
                    </div>
                    <div>
                        {{ form.email_from(class="email fs-5 form-control", placeholder="From") }}
                        <p class="text-danger">
                            {{ form.email_from.errors[0] if form.email_from.errors else ' ' }}
                        </p>
                        <br>
                    </div>
                    <div>
                        {{ form.email_to(class="fs-5 form-control", placeholder="To") }}
                        <p class="text-danger">
                            {{ form.email_to.errors[0] if form.email_to.errors else ' ' }}
                        </p>
                        <br>
                    </div>
                    <div class="text-center">
                        <button type="submit" 
                                id="ReferralButton" 
                                class="poppins-semibold w-100 m-0 fs-5 btn btn-primary rounded-3">{{ form.submit.label(class="m-0") }}
                        </button>
                    </div>
                </form>
                <div class="d-flex flex-column justify-content-center">
                    <div class="text-center mt-2">
                        <a href="{{ url_for('login') }}" 
                           class="forget-link">Back to Login</a>
                    </div>
                    <p class="text-center text-danger">{{ message }}</p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
