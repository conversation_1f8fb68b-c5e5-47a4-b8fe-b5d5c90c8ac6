{% extends 'base.html' %}


{% block title %}Login{% endblock %}

{% block content %}
    <div class="login-page d-flex justify-content-center align-items-center poppins-regular">
        <div class="login-page d-flex justify-content-center align-items-center poppins-regular">
            <div class="login-info left p-5">
                <div class="text-center">
                    <img src="{{ url_for('static', filename='images/aleph_logo_login_page.png') }}" 
                        alt="Logo" 
                        class="login-page-logo mb-4 me-3">
                </div>
                <p class="poppins-semibold mb-4 fs-5">Achieve operational excellence in a few clicks</p>
                <div>
                    <div class="d-flex mb-2">
                        <div class="circle"></div>
                        <p>Predict experimentation results</p>
                    </div>
                    <div class="d-flex mb-2">
                        <div class="circle"></div>
                        <p>Troubleshoot inefficiency</p>
                    </div>
                    <div class="d-flex mb-2">
                        <div class="circle"></div>
                        <p>Identify pathway to excellence</p>
                    </div>
                </div>
            </div>

            <div class="user-info border p-5">
                <h3 class="poppins-semibold mb-5 text-center">Login to your account</h3>
                
                <form method="POST" action="{{ url_for('login') }}">

                    {{ form.hidden_tag() }}  <!-- CSRF Protection -->
                    
                    <div>
                        {{ form.organization(class="organization-name fs-5 form-control", placeholder="Organization Name") }}
                        <p class="text-danger">
                            {{ form.organization.errors[0] if form.organization.errors else ' ' }}
                        </p>
                        <br>
                    </div>
                    <div>
                        {{ form.email(class="email fs-5 form-control", placeholder="Email") }}
                        <p class="text-danger">
                            {{ form.email.errors[0] if form.email.errors else ' ' }}
                        </p>
                        <br>
                    </div>
                    <div>
                        {{ form.password(class="password fs-5 form-control", placeholder="Password") }}
                        <p class="text-danger">
                            {{ form.password.errors[0] if form.password.errors else ' ' }}
                        </p>
                        <br>
                    </div>
                    <div class="text-center">
                        <button type="submit" 
                                id="LoginButton" 
                                class="login-button poppins-semibold w-100 m-0 fs-5 btn btn-primary rounded-3">{{ form.submit.label(class="m-0") }}
                        </button>
                    </div>
                </form>
                <div class="d-flex flex-column justify-content-center">
                    <div class="text-center mt-2">
                        <a href="{{ url_for('signup') }}" 
                        id="CreateYourAccount" 
                        class="create-account m-0">Create your account</a>
                    </div>
                    <p class="text-center text-danger">{{ message }}</p>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
