import logging
import dash
from dash import Input, Output, html, State, Dash
import dash_bootstrap_components as dbc
import dash_cytoscape as cyto
from dotenv import load_dotenv


cyto.load_extra_layouts()

load_dotenv()

app = Dash(
    __name__,
    use_pages=True,
    external_stylesheets=[
        dbc.themes.BOOTSTRAP,
        dbc.icons.BOOTSTRAP,
        dbc.icons.FONT_AWESOME,
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",
        "https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap",
    ],
)

app.layout = html.Div([dash.page_container])

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,  # Set the logging level
        format="%(asctime)s - %(levelname)s - %(message)s",
    )
    app.run_server(debug=True)
