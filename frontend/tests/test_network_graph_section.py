# TODO Fill in test
# Equipment table tests
def test_change_equipment_name():
    raise NotImplementedError()


def test_change_equipment_type():
    raise NotImplementedError()


def test_add_equipment():
    raise NotImplementedError()


def test_delete_equipment():
    raise NotImplementedError()


# Connections table tests
def test_change_stream_type():
    raise NotImplementedError()


def test_change_upstream_equipment():
    raise NotImplementedError()


def test_change_downstream_equipment():
    raise NotImplementedError()


def test_add_stream():
    raise NotImplementedError()


def test_delete_stream():
    raise NotImplementedError()


def test_delete_equipment_deletes_streams():
    raise NotImplementedError()


def test_deselect_equipment_resets_stream_settings():
    raise NotImplementedError()


# Reactions table test
def test_add_reaction():
    raise NotImplementedError()


def test_delete_reaction():
    raise NotImplementedError()
