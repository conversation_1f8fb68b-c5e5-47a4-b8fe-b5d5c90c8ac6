import pytest
from unittest.mock import patch
from datetime import datetime


from frontend.utils.uuid_service import UUIDService
from frontend.components.column_mapping_table import (
    validate_column_mapping_on_timestep_change,
    validate_column_mapping_on_timeset_change,
    validate_column_mapping_on_var_id_change,
    update_variable_options,
)
from frontend.sections.model_setup_section.layout import start_model_training


@pytest.fixture
def variables_store() -> dict:
    return {
        "var-uuid-1": "Heat Loss",
        "var-uuid-2": "Outlet Temperature",
        "var-uuid-3": "Surface Area",
    }


@pytest.fixture
def columns() -> list:
    return ["col1", "col2", "col3"]


@pytest.fixture
def base_row_data():
    return [
        {
            "id": UUIDService.generate_id(),
            "var_id": "var-uuid-1",
            "column_name": "col1",
            "is_timestep": False,
            "is_timeset": False,
        },
        {
            "id": UUIDService.generate_id(),
            "var_id": None,
            "column_name": "col2",
            "is_timestep": True,
            "is_timeset": False,
        },
        {
            "id": UUIDService.generate_id(),
            "var_id": None,
            "column_name": "col3",
            "is_timeset": True,
            "is_timestep": False,
        },
    ]


def test_toggle_timestep_on(variables_store, base_row_data):
    """Test only one column name is selected as timestep"""
    updated_index = 0
    base_row_data[updated_index]["is_timestep"] = True

    updated_row_data = validate_column_mapping_on_timestep_change(
        updated_index, base_row_data, variables_store
    )

    for i, row in enumerate(updated_row_data):
        if i == updated_index:
            assert row["is_timestep"] == True
            assert row["is_timeset"] == False
            continue
        assert row["is_timestep"] == False


def test_toggle_timeset_on(variables_store, base_row_data):
    """Test only one column name is selected as timeset"""
    updated_index = 0
    base_row_data[updated_index]["is_timeset"] = True

    updated_row_data = validate_column_mapping_on_timeset_change(
        updated_index, base_row_data, variables_store
    )

    for i, row in enumerate(updated_row_data):
        if i == updated_index:
            assert row["is_timeset"] == True
            assert row["is_timestep"] == False
            continue
        assert row["is_timeset"] == False


def test_options_update_on_var_select(variables_store, base_row_data):
    """Test variable options are updated when new variable is mapped"""
    updated_index = 1
    new_var_id = list(variables_store.keys())[1]
    base_row_data[updated_index]["var_id"] = new_var_id

    updated_row_data = validate_column_mapping_on_var_id_change(
        updated_index, base_row_data, variables_store
    )

    for i, row in enumerate(updated_row_data):
        options = set(opt["value"] for opt in row["var_options"])
        if i == updated_index:
            assert new_var_id in options
            assert row["var_id"] == new_var_id
            continue
        assert new_var_id not in options


def test_update_var_options(variables_store, base_row_data):
    """Test variable options are updated when new variable is mapped"""
    updated_index = 1
    new_var_id = list(variables_store.keys())[1]
    base_row_data[updated_index]["var_id"] = new_var_id

    updated_row_data = update_variable_options(base_row_data, variables_store)

    for i, row in enumerate(updated_row_data):
        options = set(opt["value"] for opt in row["var_options"])
        if i == updated_index:
            assert new_var_id in options
            assert row["var_id"] == new_var_id
            continue
        assert new_var_id not in options


@pytest.fixture
def mock_api():
    with patch("frontend.sections.model_setup_section.layout.api") as mock:
        yield mock


@pytest.fixture
def valid_input():
    return {
        "n_clicks": 1,
        "user_store": {
            "user_id": "test-user",
            "name": "test-user",
            "email": "<EMAIL>",
            "datetime": datetime.now(),
        },
        "project_store": {"project_name": "test-project"},
        "model_type": "first_principles",
        "training_regime": "training_data",
        "encoded_training_data": "data=csv, test-data",
        "toast_children": [],
    }


def test_start_model_training_success(mock_api, base_row_data, valid_input):
    """Test successful model training start"""
    mock_api.post.return_value = ({"status": "success"}, True)

    modal_is_open, toast_children = start_model_training(
        valid_input["n_clicks"],
        valid_input["user_store"],
        valid_input["project_store"],
        valid_input["model_type"],
        valid_input["training_regime"],
        valid_input["encoded_training_data"],
        base_row_data,
        valid_input["toast_children"],
    )

    assert modal_is_open is False
    assert len(toast_children) == 1
    assert "success" in toast_children[0].children[0].children
    mock_api.post.assert_called_once()


def test_start_model_training_missing_training_data(
    mock_api, base_row_data, valid_input
):
    """Test validation error when training data is missing"""
    valid_input["encoded_training_data"] = None

    modal_is_open, toast_children = start_model_training(
        valid_input["n_clicks"],
        valid_input["user_store"],
        valid_input["project_store"],
        valid_input["model_type"],
        valid_input["training_regime"],
        valid_input["encoded_training_data"],
        base_row_data,
        valid_input["toast_children"],
    )

    assert len(toast_children) == 1
    assert "Please upload training data" in toast_children[0].children[0].children
    mock_api.post.assert_not_called()


def test_start_model_training_missing_column_mapping(
    mock_api, base_row_data, valid_input
):
    """Test validation error when column mapping is incomplete"""
    # Set var_id to None to simulate unmapped column
    base_row_data[0]["var_id"] = None
    base_row_data[0]["is_timestep"] = False
    base_row_data[0]["is_timeset"] = False

    modal_is_open, toast_children = start_model_training(
        valid_input["n_clicks"],
        valid_input["user_store"],
        valid_input["project_store"],
        valid_input["model_type"],
        valid_input["training_regime"],
        valid_input["encoded_training_data"],
        base_row_data,
        valid_input["toast_children"],
    )

    assert len(toast_children) == 1
    assert "Missing columns" in toast_children[0].children[0].children
    assert base_row_data[0]["column_name"] in toast_children[0].children[0].children
    mock_api.post.assert_not_called()


def test_start_model_training_timestep_without_timeset(
    mock_api, base_row_data, valid_input
):
    """Test validation error when timestep is set without timeset"""
    # Set timestep without timeset
    base_row_data[1]["is_timestep"] = True
    base_row_data[2]["is_timeset"] = False

    modal_is_open, toast_children = start_model_training(
        valid_input["n_clicks"],
        valid_input["user_store"],
        valid_input["project_store"],
        valid_input["model_type"],
        valid_input["training_regime"],
        valid_input["encoded_training_data"],
        base_row_data,
        valid_input["toast_children"],
    )

    assert len(toast_children) == 1
    assert (
        "Please ensure to define timeset if you have selected timestep"
        in toast_children[0].children[0].children
    )
    mock_api.post.assert_not_called()


def test_start_model_training_api_error(mock_api, base_row_data, valid_input):
    """Test handling of API error response"""
    mock_api.post.return_value = ({"error": "API Error"}, False)

    modal_is_open, toast_children = start_model_training(
        valid_input["n_clicks"],
        valid_input["user_store"],
        valid_input["project_store"],
        valid_input["model_type"],
        valid_input["training_regime"],
        valid_input["encoded_training_data"],
        base_row_data,
        valid_input["toast_children"],
    )

    assert len(toast_children) == 1
    assert "Server error" in toast_children[0].children[0].children
    mock_api.post.assert_called_once()
