import pytest
from unittest.mock import patch
from datetime import datetime
from dash.exceptions import PreventUpdate

from frontend.utils.uuid_service import UUIDService
from frontend.sections.sensor_section.layout import (
    save_sensor_mapping,
    open_sensor_mapping_modal,
)


@pytest.fixture
def mock_api():
    with patch("frontend.sections.sensor_section.layout.api") as mock:
        yield mock


@pytest.fixture
def base_row_data():
    return [
        {
            "id": UUIDService.generate_id(),
            "var_id": "var-uuid-1",
            "column_name": "col1",
            "is_timestep": False,
            "is_timeset": False,
        },
        {
            "id": UUIDService.generate_id(),
            "var_id": None,
            "column_name": "col2",
            "is_timestep": True,
            "is_timeset": False,
        },
        {
            "id": UUIDService.generate_id(),
            "var_id": None,
            "column_name": "col3",
            "is_timeset": True,
            "is_timestep": False,
        },
    ]


@pytest.fixture
def valid_input():
    return {
        "n_clicks": 1,
        "user_store": {
            "user_id": "test-user",
            "name": "test-user",
            "email": "<EMAIL>",
            "datetime": datetime.now(),
        },
        "project_store": {"project_name": "test-project"},
        "toast_children": [],
    }


def test_save_sensor_mapping_success(mock_api, base_row_data, valid_input):
    """Test successful sensor mapping save"""
    mock_api.post.return_value = ({"status": "success"}, True)

    modal_is_open, toast_children = save_sensor_mapping(
        valid_input["n_clicks"],
        valid_input["user_store"],
        valid_input["project_store"],
        base_row_data,
        valid_input["toast_children"],
    )

    assert modal_is_open is False
    assert len(toast_children) == 1
    assert "success" in toast_children[0].children[0].children
    mock_api.post.assert_called_once()


def test_save_sensor_mapping_missing_column_mapping(
    mock_api, base_row_data, valid_input
):
    """Test validation error when column mapping is incomplete"""
    # Set var_id to None to simulate unmapped column
    base_row_data[0]["var_id"] = None
    base_row_data[0]["is_timestep"] = False
    base_row_data[0]["is_timeset"] = False

    modal_is_open, toast_children = save_sensor_mapping(
        valid_input["n_clicks"],
        valid_input["user_store"],
        valid_input["project_store"],
        base_row_data,
        valid_input["toast_children"],
    )

    assert len(toast_children) == 1
    assert "Missing columns" in toast_children[0].children[0].children
    assert base_row_data[0]["column_name"] in toast_children[0].children[0].children
    mock_api.post.assert_not_called()


def test_save_sensor_mapping_timestep_without_timeset(
    mock_api, base_row_data, valid_input
):
    """Test validation error when timestep is set without timeset"""
    # Set timestep without timeset
    base_row_data[1]["is_timestep"] = True
    base_row_data[2]["is_timeset"] = False

    modal_is_open, toast_children = save_sensor_mapping(
        valid_input["n_clicks"],
        valid_input["user_store"],
        valid_input["project_store"],
        base_row_data,
        valid_input["toast_children"],
    )

    assert len(toast_children) == 1
    assert "Please ensure to define timeset" in toast_children[0].children[0].children
    mock_api.post.assert_not_called()


def test_save_sensor_mapping_api_error(mock_api, base_row_data, valid_input):
    """Test handling of API error response"""
    mock_api.post.return_value = ({"error": "API Error"}, False)

    modal_is_open, toast_children = save_sensor_mapping(
        valid_input["n_clicks"],
        valid_input["user_store"],
        valid_input["project_store"],
        base_row_data,
        valid_input["toast_children"],
    )

    assert len(toast_children) == 1
    assert "Server error" in toast_children[0].children[0].children
    mock_api.post.assert_called_once()


@pytest.fixture
def plant_config_response():
    return {
        "data": {
            "equipments": [
                {
                    "equipmentId": "heater1",
                    "setpoints": [{"uuid_str": "var-uuid-1", "title": "Temperature"}],
                    "conditions": [{"uuid_str": "var-uuid-2", "title": "Pressure"}],
                }
            ],
            "connections": [
                {
                    "upstreamEquipment": "heater1",
                    "downstreamEquipment": "cooler1",
                    "setpoints": [{"uuid_str": "var-uuid-3", "title": "Flow Rate"}],
                    "conditions": [],
                }
            ],
        }
    }


@pytest.fixture
def sensor_mapping_response():
    return {
        "data": {
            "csvCol2varUUID": {"temp": "var-uuid-1", "flow": "var-uuid-3"},
            "timestep_col": "time",
            "timeset_col": "date",
        }
    }


def test_open_modal_no_clicks(valid_input):
    """Test modal doesn't open when there are no clicks"""
    valid_input["n_clicks"] = None

    with pytest.raises(PreventUpdate):
        open_sensor_mapping_modal(
            valid_input["n_clicks"],
            valid_input["user_store"],
            valid_input["project_store"],
            valid_input["toast_children"],
        )


def test_open_modal_success(
    mock_api, valid_input, plant_config_response, sensor_mapping_response
):
    """Test successful modal opening with existing sensor mapping"""
    mock_api.get.side_effect = [
        (plant_config_response, True),
        (sensor_mapping_response, True),
    ]

    modal_is_open, variables_store, row_data, toast_children = (
        open_sensor_mapping_modal(
            valid_input["n_clicks"],
            valid_input["user_store"],
            valid_input["project_store"],
            valid_input["toast_children"],
        )
    )

    assert modal_is_open is True
    assert len(variables_store) == 3
    assert "var-uuid-1" in variables_store
    assert len(row_data) == 4  # 2 mapped columns + timestep + timeset
    assert len(toast_children) == 0
    assert mock_api.get.call_count == 2


def test_open_modal_missing_project_name(valid_input):
    """Test error handling when project name is missing"""
    valid_input["project_store"]["project_name"] = None

    with pytest.raises(KeyError) as exc_info:
        open_sensor_mapping_modal(
            valid_input["n_clicks"],
            valid_input["user_store"],
            valid_input["project_store"],
            valid_input["toast_children"],
        )

    assert "Missing plant configuration name" in str(exc_info.value)


def test_open_modal_plant_config_api_error(mock_api, valid_input):
    """Test error handling when plant configuration API call fails"""
    mock_api.get.return_value = ({"error": "API Error"}, False)

    modal_is_open, variables_store, row_data, toast_children = (
        open_sensor_mapping_modal(
            valid_input["n_clicks"],
            valid_input["user_store"],
            valid_input["project_store"],
            valid_input["toast_children"],
        )
    )

    assert modal_is_open is True
    assert variables_store == {}
    assert row_data == []
    assert len(toast_children) == 1
    assert "Failed to get plant configuration" in toast_children[0].children[0].children
    mock_api.get.assert_called_once()


def test_open_modal_sensor_mapping_api_error(
    mock_api, valid_input, plant_config_response
):
    """Test handling of sensor mapping API error"""
    mock_api.get.side_effect = [
        (plant_config_response, True),
        ({"error": "API Error"}, False),
    ]

    modal_is_open, variables_store, row_data, toast_children = (
        open_sensor_mapping_modal(
            valid_input["n_clicks"],
            valid_input["user_store"],
            valid_input["project_store"],
            valid_input["toast_children"],
        )
    )

    assert modal_is_open is True
    assert len(variables_store) == 3
    assert len(row_data) == 0
    assert len(toast_children) == 1
    assert "Failed to get sensor mapping" in toast_children[0].children[0].children
    assert mock_api.get.call_count == 2
