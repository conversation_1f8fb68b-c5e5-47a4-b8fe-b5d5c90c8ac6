from frontend.sections.kpi_section.layout import update_selected_kpis


def test_update_selected_kpis():
    selected_rows = [
        {"name": "Pressure", "expression": "{{Pressure}}", "variables": ["Pressure"]}
    ]

    output = update_selected_kpis(selected_rows)

    assert len(output) == len(selected_rows)
    assert output[0]["name"] == selected_rows[0]["name"]
    assert output[0]["expression"] == selected_rows[0]["expression"]
    assert output[0]["variables"] == selected_rows[0]["variables"]
