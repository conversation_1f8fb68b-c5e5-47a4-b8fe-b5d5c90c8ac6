from backend.core._atlas.entities import VALID_EQUIPMENTS
from frontend.utils.enums import EquipmentType


def test_equipment_types():
    """Test to check that EquipmentType enums have the same values as their BE counterpart"""

    for equipment_class in VALID_EQUIPMENTS:
        class_name = equipment_class.__name__
        assert class_name == EquipmentType[class_name].value

    assert len(VALID_EQUIPMENTS) == len(EquipmentType)


# TODO Fill in test
def test_stream_types():
    raise NotImplementedError()
