var dagfuncs = (window.dashAgGridFunctions = window.dashAgGridFunctions || {});

dagfuncs.dynamicDownstreamEquipmentOptions = function (params) {
    console.log('dynamicDownstreamEquipmentOptions params: ', params)
    return {values: params.data.downstream_equipment_options}
};

dagfuncs.dynamicMainEquipmentOptions = function (params) {
    console.log('dynamicMainEquipmentOptions params: ', params)
    return {values: params.data.main_equipment_options}
};

dagfuncs.dynamicMaterialTypeOptions = function (params) {
    console.log('dynamicMainEquipmentOptions params: ', params)
    return {values: params.data.material_type_options}
};

dagfuncs.discreteSelectionOptions = function (params) {
    console.log('discreteSelectionOptions params: ', params)
    return {values: params.data.options}
}

dagfuncs.sensorTypeOptions = function (params) {
    console.log('sensorTypeOptions params: ', params)
    return {values: params.data.sensor_type_options}
}

dagfuncs.placementIdOptions = function (params) {
    console.log('placementIdOptions params: ', params)
    return {values: params.data.placement_id_options}
}

dagfuncs.experimentEquipmentOptions = function (params) {
  console.log('experimentEquipmentOptions params: ', params)
  return {values: params.data.name_options}
}

// Model Training Config
dagfuncs.modelTrainingVariableOptions = function (params) {
  console.log('modelTrainingVariableOptions params: ', params)
  return {values: params.data.var_options}
}

dagfuncs.getVariableLabel = function (params) {
  // Converts variable uid to variable label for display
  console.log('getVariableLabel params: ', params)
  if (!params.value || !params.data.var_options) return '';
  const option = params.data.var_options.find(opt => opt.value === params.value);
  return option ? option.label : params.value;
}

dagfuncs.filterByVariableLabel = function (params) {
  // Converts variable uid to variable label, and checks if it contains a given filter text.
  // This is a filter function. See Text Matchers in https://dash.plotly.com/dash-ag-grid/text-filters
  console.log("filterParams", params)
  if (!params.value || !params.data.var_options) return false;

  const option = params.data.var_options.find(opt => opt.value === params.value);
  const filterText = params.filterText;

  if (!option) return false;  // safeguard

  const label = option.label;
  console.log("label", label)
  console.log("filterText", filterText)
  return label.toLowerCase().includes(filterText.toLowerCase());
}

// dagfuncs.dynamicMaterialTypeOptions = function (params) {
//     console.log('dynamicMainEquipmentOptions params: ', params)
//     return {options: params.data.material_type_options, value: 'default'}
// };


//https://community.plotly.com/t/using-dash-core-components-dropdown-as-an-ag-grid-cell-editor/74898
dagfuncs.DCC_Dropdown = class { 

    // gets called once before the renderer is used
  init(params) {
    // create the cell
    this.params = params;
    this.ref = React.createRef();

    // function for when Dash is trying to send props back to the component / server
    var setProps = (props) => {
        if (props.value) {
            // updates the value of the editor
            this.value = props.value;

            // re-enables keyboard event
            delete params.colDef.suppressKeyboardEvent

            // tells the grid to stop editing the cell
            params.api.stopEditing();

            // sets focus back to the grid's previously active cell
            this.prevFocus.focus();
        }
    }
    this.eInput = document.createElement('div')
    console.log('here!!!!')
    console.log('params.values', params.values)

    // renders component into the editor element
    ReactDOM.render(React.createElement(window.dash_core_components.Dropdown, {
        options: params.values, 
        value: params.value, 
        ref: this.ref, 
        setProps, 
        style: {
          width: params.column.actualWidth, 
          position: 'absolute',
          zIndex: 999
        },
        clearable: params.clearable
    }), this.eInput)

    // allows focus event
    this.eInput.tabIndex = "0"

    // sets editor value to the value from the cell
    this.value = params.value;
  }

  // gets called once when grid ready to insert the element
  getGui() {
    return this.eInput;
  }

  focusChild() {
    // mousedown event
    const clickEvent = new MouseEvent('mousedown', {
        view: window,
        bubbles: true
    });

    // needed to delay and allow the component to render
    setTimeout(() => {
        var inp = this.eInput.getElementsByClassName('Select-control')[0]
        inp.tabIndex = '1';

        // disables keyboard event
        this.params.colDef.suppressKeyboardEvent = (params) => {
               const gridShouldDoNothing = params.editing
               return gridShouldDoNothing;
           }
        inp.focus();
        // shows dropdown options
        // inp.dispatchEvent(clickEvent)

    // Move cursor to end after opening dropdown
    // const input = this.eInput.querySelector('input');
    // if (input) {
    //   input.value = this.params.value || "hello world"; // Set initial value to cell value or empty string
    //   // const valueLength = input.value.length;
    //   // input.setSelectionRange(valueLength, valueLength); // Position cursor at end
    // }
    }, 100)
  }

  // focus and select can be done after the gui is attached
  afterGuiAttached() {
    // stores the active cell
    this.prevFocus = document.activeElement

    // adds event listener to trigger event to go into dash component
    this.eInput.addEventListener('focus', this.focusChild())

    // triggers focus event
    this.eInput.focus();
  }

  // returns the new value after editing
  getValue() {
    return this.value;
  }

  // any cleanup we need to be done here
  destroy() {
    // sets focus back to the grid's previously active cell
    this.prevFocus.focus();
  }
}