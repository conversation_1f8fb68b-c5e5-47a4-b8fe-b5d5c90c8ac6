/* ######################   css for poppins font   ######################  */
.poppins-thin {
    font-family: "Poppins", sans-serif;
    font-weight: 100;
    font-style: normal;
  }
  
  .poppins-extralight {
    font-family: "Poppins", sans-serif;
    font-weight: 200;
    font-style: normal;
  }
  
  .poppins-light {
    font-family: "Poppins", sans-serif;
    font-weight: 300;
    font-style: normal;
  }
  
  .poppins-regular {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-style: normal;
  }
  
  .poppins-medium {
    font-family: "Poppins", sans-serif;
    font-weight: 500;
    font-style: normal;
  }
  
  .poppins-semibold {
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    font-style: normal;
  }
  
  .poppins-bold {
    font-family: "Poppins", sans-serif;
    font-weight: 700;
    font-style: normal;
  }
  
  .poppins-extrabold {
    font-family: "Poppins", sans-serif;
    font-weight: 800;
    font-style: normal;
  }
  
  .poppins-black {
    font-family: "Poppins", sans-serif;
    font-weight: 900;
    font-style: normal;
  }
  
  .poppins-thin-italic {
    font-family: "Poppins", sans-serif;
    font-weight: 100;
    font-style: italic;
  }
  
  .poppins-extralight-italic {
    font-family: "Poppins", sans-serif;
    font-weight: 200;
    font-style: italic;
  }
  
  .poppins-light-italic {
    font-family: "Poppins", sans-serif;
    font-weight: 300;
    font-style: italic;
  }
  
  .poppins-regular-italic {
    font-family: "Poppins", sans-serif;
    font-weight: 400;
    font-style: italic;
  }
  
  .poppins-medium-italic {
    font-family: "Poppins", sans-serif;
    font-weight: 500;
    font-style: italic;
  }
  
  .poppins-semibold-italic {
    font-family: "Poppins", sans-serif;
    font-weight: 600;
    font-style: italic;
  }
  
  .poppins-bold-italic {
    font-family: "Poppins", sans-serif;
    font-weight: 700;
    font-style: italic;
  }
  
  .poppins-extrabold-italic {
    font-family: "Poppins", sans-serif;
    font-weight: 800;
    font-style: italic;
  }
  
  .poppins-black-italic {
    font-family: "Poppins", sans-serif;
    font-weight: 900;
    font-style: italic;
  }
  
  /*  ######################   css for roboto font    ###################### */
  .roboto-thin {
    font-family: "Roboto", sans-serif;
    font-weight: 100;
    font-style: normal;
  }
  
  .roboto-light {
    font-family: "Roboto", sans-serif;
    font-weight: 300;
    font-style: normal;
  }
  
  .roboto-regular {
    font-family: "Roboto", sans-serif;
    font-weight: 400;
    font-style: normal;
  }
  
  .roboto-medium {
    font-family: "Roboto", sans-serif;
    font-weight: 500;
    font-style: normal;
  }
  
  .roboto-bold {
    font-family: "Roboto", sans-serif;
    font-weight: 700;
    font-style: normal;
  }
  
  .roboto-black {
    font-family: "Roboto", sans-serif;
    font-weight: 900;
    font-style: normal;
  }
  
  .roboto-thin-italic {
    font-family: "Roboto", sans-serif;
    font-weight: 100;
    font-style: italic;
  }
  
  .roboto-light-italic {
    font-family: "Roboto", sans-serif;
    font-weight: 300;
    font-style: italic;
  }
  
  .roboto-regular-italic {
    font-family: "Roboto", sans-serif;
    font-weight: 400;
    font-style: italic;
  }
  
  .roboto-medium-italic {
    font-family: "Roboto", sans-serif;
    font-weight: 500;
    font-style: italic;
  }
  
  .roboto-bold-italic {
    font-family: "Roboto", sans-serif;
    font-weight: 700;
    font-style: italic;
  }
  
  .roboto-black-italic {
    font-family: "Roboto", sans-serif;
    font-weight: 900;
    font-style: italic;
  }
  
  /* .body {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      overflow-x: hidden;
      background: red;
    } */
  
  /* ######################   CSS for button   ###################### */
  
  .button {
    width: 120px;
    height: 40px;
    text-transform: capitalize;
  }
  
  /* ######################   CSS for Inputs   ###################### */
  
  .input {
    width: 15vw;
  }
  
  .input:hover {
    cursor: pointer;
  }
  
  /* #numeric-input:invalid {
      border-color: #dc3545;
      padding-right: calc(1.5em + 0.75rem);
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right calc(0.375em + 0.1875rem) center;
      background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
      outline: none;
    } */
  
  /* ######################   css for aleph dashboard   ###################### */
  * {
    padding: 0px;
    margin: 0px;
  }
  
  ::-webkit-scrollbar {
    width: 12px;
  }
  
  ::-webkit-scrollbar-track {
    background: transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 6px;
    border: 3px solid transparent;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .sidebar-logo {
    height: 60px;
  }
  
  /* change css for active sidebar link  */
  .nav-pills .nav-link.active {
    background-color: white;
  }
  
  h3 {
    margin-bottom: 0px;
    margin-left: 10px;
  }
  
  .handle-graphs {
    display: flex;
    justify-content: space-between;
    padding: 10px 0px;
    margin-top: 20px;
  }
  
  .dropdown {
    width: 18vw;
  }
  
  .dropdown:hover {
    cursor: pointer;
  }
  
  .dashboard-data {
    padding: 10px;
    border: none;
    background: rgb(56 68 83);
    border: 1px solid black;
    border-radius: 5px;
    font-weight: 700;
    color: white;
    font-size: 18px;
  }
  
  .logout-btn:hover {
    cursor: pointer;
    background-color: rgb(3, 5, 155);
    border: 1px solid rgb(4, 0, 12);
  }
  
  .search-bar {
    padding: 10px;
    border-radius: 10px;
  }
  
  .hide-arrow .accordion-button::after {
    display: none;
  }
  
  .no-border .accordion-item {
    border: none;
  }
  .no-border .accordion-button {
    border: none;
  }
  
  .no-border .accordion-collapse {
    border: none;
  }
  
  .download-btn {
    min-width: 130px;
    width: fit-content;
  }
  
  h2 {
    font-weight: 600;
  }
  
  h3 {
    margin: 0px;
  }
  
  .nav-link {
    color: white;
  }
  
  .nav-pills {
    font-weight: 700;
    --bs-nav-pills-link-active-color: black;
    --bs-nav-pills-link-active-bg: rgb(173, 172, 172);
  }
  
  .support-link {
    font-size: 25px;
    color: white;
  }
  
  /* CSS to show data column-wise  */
  .column-four {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    column-gap: 3%;
    row-gap: 50px;
    padding-bottom: 50px;
  }
  
  .column-three {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 3%;
    row-gap: 50px;
    padding-bottom: 50px;
  }
  
  .column-two {
    display: grid;
    grid-template-columns: 1fr 1fr;
    column-gap: 3%;
    row-gap: 50px;
    padding-bottom: 50px;
  }
  
  /* css for horizonatal line  */
  
  .horizontal-line {
    /* border-bottom: 1px solid #cccbcb; */
    padding-top: 50px;
    background-color: #ebe9e9;
  }
  
  /* Light mode styles */
  .light-mode {
    background-color: #ffffff;
    color: #000000;
  
    table {
      color: black;
    }
  
    .section-template {
      background-color: white;
      color: black;
    }
  
    .navbar {
      background-color: white;
    }
  
    .dash-graph {
      background-color: white;
    }
  
    .dashboard-content {
      background-color: #ebe9e9;
    }
  
    .sidebar {
      background-color: #2a3286;
    }
  
    .current-date {
      color: black;
    }
  
    .breadcrumb-item {
      color: black;
    }
  }
  
  .light-mode .dash-graph {
    background-color: #ffffff;
    color: #000000;
  }
  
  /* Dark mode styles */
  .dark-mode {
    background-color: #1e1e1e;
    color: #ffffff;
  
    table {
      color: black;
    }
  
    .navbar {
      background-color: black;
    }
  
    .dash-graph {
      background-color: black;
    }
  
    .dashboard-content {
      background-color: black;
    }
  
    .sidebar {
      background-color: black;
    }
  
    .current-date {
      color: white;
    }
  
    .breadcrumb-item {
      color: white;
    }
    .breadcrumb-item + .breadcrumb-item::before {
      color: white;
    }
  
    .horizontal-line{
      background-color: black;
      border-bottom: 1px solid white;
    }
  }
  
  .dark-mode .dash-graph {
    background-color: #1e1e1e;
    color: #ffffff;
  }
  
  button {
    margin: 10px;
    padding: 10px;
    font-size: 16px;
  }
  
  #toggle-button {
    background-color: #007bff;
    color: #ffffff;
    border: none;
    cursor: pointer;
  }
  
  #toggle-button:hover {
    background-color: #0056b3;
  }
  



  /* # Callback to toggle light/dark mode
# @app.callback(
#     [
#         Output("app-container", "className"),
#         Output("sun-icon", "style"),
#         Output("moon-icon", "style"),
#     ],
#     Input("toggle-switch", "value"),
# )
# def toggle_mode(value):
#     print("Sdsd")
#     if 1 in value:
#         sun_style = {"display": "block"}
#         moon_style = {"display": "none"}
#         return "light-mode", sun_style, moon_style
#     else:
#         sun_style = {"display": "none"}
#         moon_style = {"display": "block"}
#         return "dark-mode", sun_style, moon_style */
