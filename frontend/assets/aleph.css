@import "./css/bootstrap.css";
@import "./css/bootstrap-utilities.css";

/* ######################   css for poppins font   ######################  */
.poppins-thin {
  font-family: "Poppins", sans-serif;
  font-weight: 100;
  font-style: normal;
}

.poppins-extralight {
  font-family: "Poppins", sans-serif;
  font-weight: 200;
  font-style: normal;
}

.poppins-light {
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  font-style: normal;
}

.poppins-regular {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.poppins-medium {
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-style: normal;
}

.poppins-semibold {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-style: normal;
}

.poppins-bold {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.poppins-extrabold {
  font-family: "Poppins", sans-serif;
  font-weight: 800;
  font-style: normal;
}

.poppins-black {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-style: normal;
}

.poppins-thin-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 100;
  font-style: italic;
}

.poppins-extralight-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 200;
  font-style: italic;
}

.poppins-light-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 300;
  font-style: italic;
}

.poppins-regular-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 400;
  font-style: italic;
}

.poppins-medium-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 500;
  font-style: italic;
}

.poppins-semibold-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 600;
  font-style: italic;
}

.poppins-bold-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 700;
  font-style: italic;
}

.poppins-extrabold-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 800;
  font-style: italic;
}

.poppins-black-italic {
  font-family: "Poppins", sans-serif;
  font-weight: 900;
  font-style: italic;
}


/*  ######################   css for roboto font    ###################### */
.roboto-thin {
  font-family: "Roboto", sans-serif;
  font-weight: 100;
  font-style: normal;
}

.roboto-light {
  font-family: "Roboto", sans-serif;
  font-weight: 300;
  font-style: normal;
}

.roboto-regular {
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  font-style: normal;
}

.roboto-medium {
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  font-style: normal;
}

.roboto-bold {
  font-family: "Roboto", sans-serif;
  font-weight: 700;
  font-style: normal;
}

.roboto-black {
  font-family: "Roboto", sans-serif;
  font-weight: 900;
  font-style: normal;
}

.roboto-thin-italic {
  font-family: "Roboto", sans-serif;
  font-weight: 100;
  font-style: italic;
}

.roboto-light-italic {
  font-family: "Roboto", sans-serif;
  font-weight: 300;
  font-style: italic;
}

.roboto-regular-italic {
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  font-style: italic;
}

.roboto-medium-italic {
  font-family: "Roboto", sans-serif;
  font-weight: 500;
  font-style: italic;
}

.roboto-bold-italic {
  font-family: "Roboto", sans-serif;
  font-weight: 700;
  font-style: italic;
}

.roboto-black-italic {
  font-family: "Roboto", sans-serif;
  font-weight: 900;
  font-style: italic;
}

.body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  background: red;
}

/* ######################   CSS for button   ###################### */

.button {
  min-width: 120px;
  height: 40px;
  text-transform: capitalize;
}

/* ######################   CSS for Inputs   ###################### */

.input {
  width: 15vw;
}

.input:hover {
  cursor: pointer;
}

/* #numeric-input:invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
  outline: none;
} */

/* ######################   css for aleph dashboard   ###################### */
* {
  padding: 0px;
  margin: 0px;
}

::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 6px;
  border: 3px solid transparent;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.sidebar-logo {
  height: 60px;
}

/* change css for active sidebar link  */
.nav-pills .nav-link.active {
  background-color: #1A2B5F;
  color: white;
}

.nav-link {
  color: #627789;
  margin-top: 10px;
}

/* .nav-pills {
  font-weight: 700;
  --bs-nav-pills-link-active-color: black;
  --bs-nav-pills-link-active-bg: rgb(173, 172, 172);
} */

h3 {
  margin-bottom: 0px;
  margin-left: 10px;
}

.handle-graphs {
  display: flex;
  justify-content: space-between;
  padding: 10px 0px;
  margin-top: 20px;
}

.dropdown {
  width: 18vw;
}

.dropdown:hover {
  cursor: pointer;
}

.dashboard-data {
  padding: 10px;
  border: none;
  background: rgb(56 68 83);
  border: 1px solid black;
  border-radius: 5px;
  font-weight: 700;
  color: white;
  font-size: 18px;
}

.logout-btn:hover {
  cursor: pointer;
  background-color: rgb(3, 5, 155);
  border: 1px solid rgb(4, 0, 12);
}

.search-bar {
  padding: 10px;
  border-radius: 10px;
}

.hide-arrow .accordion-button::after {
  display: none;
}

.no-border .accordion-item {
  border: none;
}
.no-border .accordion-button {
  border: none;
}

.no-border .accordion-collapse {
  border: none;
}

.download-btn {
  min-width: 130px;
  width: fit-content;
}

h2 {
  font-weight: 600;
}

h3 {
  margin: 0px;
}

.support-link {
  font-size: 25px;
  color: white;
}

/* CSS to show data column-wise  */
.column-four {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  column-gap: 3%;
  row-gap: 50px;
  padding-bottom: 50px;
}

.column-three {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  column-gap: 3%;
  row-gap: 50px;
  padding-bottom: 50px;
}

.column-two {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 3%;
  row-gap: 50px;
  padding-bottom: 50px;
}

/* css for horizonatal line  */

.horizontal-line {
  border-bottom: 1px solid #cccbcb;
  margin-top: 40px;
  margin-bottom: 40px;
}

/* Light mode styles */
.light-mode {
  background-color: #ffffff;
  color: #000000;

  table {
    color: black;
  }

  .navbar {
    background-color: white;
  }

  .dash-graph {
    background-color: white;
  }

  .dashboard-content {
    background-color: white;
  }

  .sidebar {
    background-color: white;
  }

  .current-date {
    color: black;
  }

  .breadcrumb-item {
    color: black;
  }
}

.light-mode .dash-graph {
  background-color: #ffffff;
  color: #000000;
}

/* Dark mode styles */
.dark-mode {
  background-color: #1e1e1e;
  color: #ffffff;

  table {
    color: black;
  }

  .navbar {
    background-color: black;
  }

  .dash-graph {
    background-color: black;
  }

  .dashboard-content {
    background-color: black;
  }

  .sidebar {
    background-color: black;
  }

  .current-date {
    color: white;
  }

  .breadcrumb-item {
    color: white;
  }
  .breadcrumb-item + .breadcrumb-item::before {
    color: white;
  }
}

.dark-mode .dash-graph {
  background-color: #1e1e1e;
  color: #ffffff;
}

button {
  margin: 10px;
  padding: 10px;
  font-size: 16px;
}

#toggle-button {
  background-color: #007bff;
  color: #ffffff;
  border: none;
  cursor: pointer;
}

#toggle-button:hover {
  background-color: #0056b3;
}

/* css for login page  */
.login-page,
.signup-page {
  width: 100%;
  height: 100vh;

  .circle {
    width: 1vw; /* Width of the circle */
    height: 1vw; /* Height of the circle */
    background-color: #2182ec; /* Background color of the circle */
    border-radius: 50%;
    margin-right: 15px;
  }

  .user-info {
    width: 500px;
    height: 510px;

    .login-button {
      width: 100px;
    }

    .forget-link {
      text-decoration: none;
    }
    .forget-link:hover {
      text-decoration: underline;
    }
    .create-account {
      text-decoration: none;
    }
    .create-account:hover {
      text-decoration: underline;
    }
  }

  .login-info,
  .signup-info {
    width: 500px;
    height: 510px;

    .login-page-logo,
    .signup-page-logo {
      height: 100px;
    }
  }
}

.signup-page {
  width: 100%;
  height: 100vh;

  .circle {
    width: 1vw; /* Width of the circle */
    height: 1vw; /* Height of the circle */
    background-color: #2182ec; /* Background color of the circle */
    border-radius: 50%;
    margin-right: 15px;
  }

  .user-info {
    width: 500px;
    height: 590px;

    .create-account-button {
      width: 100%;
    }

    .create-account {
      text-decoration: none;
    }

    .create-account:hover {
      text-decoration: underline;
    }
  }

  .login-info {
    width: 500px;
    height: 590px;

    .login-page-logo {
      height: 100px;
    }
  }
}

/* .plant-entities { */
.equipment-cards {
  .equipment-card {
    .table-input-fields {
      width: 200px;
    }
  }
}
/* } */

.plant-configuration {
  input {
    width: 100px;
  }
  .dropdown {
    width: 200px;
  }

  .equip-condition-slider {
    width: 200px;
  }
  table {
    th {
      height: 75px;
    }
    td {
      height: 120px;
    }
  }
}

.experimentation-setup {
  .form-label {
    min-width: 20%;
  }
  .input {
    width: 10vw;
  }
  .dropdown {
    min-width: 10vw;
  }
}

.troubleshooting-setup {
  .form-label {
    min-width: 20%;
  }
  .dropdown {
    min-width: 330px;
  }
}

/* ######################   CSS for Dash AG Grid   ###################### */
/* *********************  dcc Dropdown Custom Cell Editor Component  ********************* */
.ag-cell {
  overflow: visible !important; 
}

/* Ensure the grid container allows overflow if dropdown exceeds table */
.ag-root-wrapper {
  overflow: visible !important;
}

/* Ensure the dash dropdown menu appears above grid */
/* .Select-menu-outer {
  z-index: 100 !important;
  position: absolute !important;
  top: 100% !important;
  width: 100% !important;
} */

/* Format input & dropdown menu sub-components so that they do not overlap */
.ag-popup-editor .Select {
  display: flex !important;
  flex-direction: column !important;
  margin-top: 3px !important;
}

/* Needed to make sure input control does not overlap dropdown options */
.ag-popup-editor .Select-control {
  position: relative !important;
}

/* Set color and font for text in DCC dropdown cell editor component */
.ag-popup-editor  .VirtualizedSelectOption, .ag-popup-editor  .Select-input > input, .ag-popup-editor .Select-value-label {
  font-family: "Roboto" !important;
  color: var(--light) !important;
  font-size: calc(var(--ag-font-size) + 1px) !important;
}

/* Left align chosen value, options, input cursor, and no results found default option with AG Grid cell text */
.ag-popup-editor .Select-value, 
.ag-popup-editor .VirtualizedSelectOption, 
.ag-popup-editor .Select-input, 
.ag-popup-editor .Select-noresults {
  padding-left: calc(var(--ag-cell-horizontal-padding) - 1px) !important;
}

/* *********************  General Settings for AG Grid  ********************* */
/* General settings for AG Grid */
.ag-theme-alpine {
  --ag-wrapper-border-radius: 1rem !important;
  --ag-header-foreground-color: var(--dark) !important;
  --ag-header-background-color: var(--secondary) !important;
  --ag-font-family: "Roboto" !important;
  --ag-font-size: 1rem !important;
  --ag-header-column-separator-display: none !important;
  --ag-header-column-resize-handle-display: none !important;
  --ag-border-color: #E9ECF2 !important;
  --ag-odd-row-background-color: white !important;
  --ag-data-color: var(--light) !important;
  --ag-popup-shadow: 5px 5px 10px rgba(0,0,0,0.3) !important;
}

.toast {
  z-index: 1200 !important;
}

/* Allow text wrapping for column headers */
.ag-header-cell-label {
  white-space: normal !important;
  text-overflow: unset !important;
  overflow-wrap: break-word !important;
}

.modal-content {
  border-radius: var(--bs-border-radius-xxl) !important; /* Adjust the value for desired roundness */
  border-width: 0 !important; /* Remove modal border */
}

/* *********************  Custom pagination  ********************* */
/* Customize pagination items */
.custom-pagination .page-item .page-link {
  color: var(--dark) !important; /* Change color */
  background-color: transparent !important;
  border-radius: var(--bs-border-radius-lg) !important; /* Make edges rounded */
  font-weight: medium !important;
  border: none !important;
  font-size: 14px !important;
  padding: 0.61rem 1rem !important;
}

/* Style for active pagination link */
.custom-pagination .page-item.active .page-link {
  background-color: var(--primary) !important; /* Custom background color */
  color: var(--white) !important;
}

/* Styling for first and last buttons */
.custom-pagination .page-item:first-child .page-link, /* First button */
.custom-pagination .page-item:last-child .page-link {  /* Last button */
    color: var(--white) !important;
    background-color: var(--primary) !important /* Darker background for first/last buttons */
}


/* Style for disabled pagination link */
.custom-pagination .page-item.disabled .page-link {
  background-color: var(--secondary) !important; /* Custom background color */
  color: var(--light) !important;
}

/* Hover effect for pagination items */
/* .custom-pagination .page-item .page-link:hover {
  background-color: #e2e6ea;
  color: #343a40;
} */


/* ######################   CSS for Dash Loading Screen   ###################### */

/* .react-entry-point {
  display: flex;
  justify-content: center;
  align-items: center;
} */


/* Credit: https://github.com/lukehaas/css-loaders/blob/step2/css/load4.css */
._dash-loading {
  color: var(--primary);
  font-size: 10px;
  margin: 100px auto;
  width: 1em;
  height: 1em;
  border-radius: 50%;
  position: absolute;
  margin: auto;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  text-indent: -9999em;
  -webkit-animation: load4 1.3s infinite linear;
  animation: load4 1.3s infinite linear;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
}
@-webkit-keyframes load4 {
  0%,
  100% {
    box-shadow: 0 -3em 0 0.2em, 2em -2em 0 0em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 0;
  }
  12.5% {
    box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em, 3em 0 0 0, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
  }
  25% {
    box-shadow: 0 -3em 0 -0.5em, 2em -2em 0 0, 3em 0 0 0.2em, 2em 2em 0 0, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
  }
  37.5% {
    box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 0, 2em 2em 0 0.2em, 0 3em 0 0em, -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
  }
  50% {
    box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 0em, 0 3em 0 0.2em, -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
  }
  62.5% {
    box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 0, -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
  }
  75% {
    box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
  }
  87.5% {
    box-shadow: 0em -3em 0 0, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
  }
}
@keyframes load4 {
  0%,
  100% {
    box-shadow: 0 -3em 0 0.2em, 2em -2em 0 0em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 0;
  }
  12.5% {
    box-shadow: 0 -3em 0 0, 2em -2em 0 0.2em, 3em 0 0 0, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
  }
  25% {
    box-shadow: 0 -3em 0 -0.5em, 2em -2em 0 0, 3em 0 0 0.2em, 2em 2em 0 0, 0 3em 0 -1em, -2em 2em 0 -1em, -3em 0 0 -1em, -2em -2em 0 -1em;
  }
  37.5% {
    box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 0, 2em 2em 0 0.2em, 0 3em 0 0em, -2em 2em 0 -1em, -3em 0em 0 -1em, -2em -2em 0 -1em;
  }
  50% {
    box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 0em, 0 3em 0 0.2em, -2em 2em 0 0, -3em 0em 0 -1em, -2em -2em 0 -1em;
  }
  62.5% {
    box-shadow: 0 -3em 0 -1em, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 0, -2em 2em 0 0.2em, -3em 0 0 0, -2em -2em 0 -1em;
  }
  75% {
    box-shadow: 0em -3em 0 -1em, 2em -2em 0 -1em, 3em 0em 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0.2em, -2em -2em 0 0;
  }
  87.5% {
    box-shadow: 0em -3em 0 0, 2em -2em 0 -1em, 3em 0 0 -1em, 2em 2em 0 -1em, 0 3em 0 -1em, -2em 2em 0 0, -3em 0em 0 0, -2em -2em 0 0.2em;
  }
}