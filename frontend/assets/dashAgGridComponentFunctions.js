var dagcomponentfuncs = window.dashAgGridComponentFunctions = window.dashAgGridComponentFunctions || {};

dagcomponentfuncs.Button = function (props) {
    console.log('button props: ', props)

    const {setData, data, color, text, iconClass, buttonClass} = props;

    function onClick() {
        setData();
    }

    return React.createElement(
        window.dash_bootstrap_components.Button,
        {
            onClick,
            color,
            className: buttonClass
        },
        React.createElement('div', {className: 'd-flex align-items-center justify-content-center'}, 
            React.createElement('i', {className: iconClass}), 
            React.createElement('h5', {className: "m-0 ps-2"}, text),
    ));
};

dagcomponentfuncs.OptionalButton = function (props) {
    console.log('OptionalButton props: ', props)

    const {setData, data, color, text, iconClass, buttonClass} = props;

    function onClick() {
        setData();
    }

    return React.createElement(
        data.is_var == false ? window.dash_bootstrap_components.Button : 'Div',
        {
            onClick,
            color,
            className: buttonClass
        },
        data.is_var == false ? React.createElement('i', {className: iconClass}, text) : '' 
    );
};

dagcomponentfuncs.OptionalButtonV2 = function (props) {
    console.log('OptionalButtonV2 props: ', props)

    const {setData, data, color, text, iconClass, buttonClass, value} = props;

    function onClick() {
        setData();
    }

    return React.createElement(
        value != false ? window.dash_bootstrap_components.Button : 'Div',
        {
            onClick,
            color,
            className: buttonClass
        },
        value != false ? React.createElement('i', {className: iconClass}, text) : '' 
    );
};

dagcomponentfuncs.ProgressBar = function (props) {
    console.log('ProgressBar props: ', props)

    const {setData, data, color, value} = props;

    // function onClick() {
    //     setData();
    // }

    return React.createElement('div', {className: 'd-flex align-items-center justify-content-center'}, 
            React.createElement(window.dash_bootstrap_components.Progress, {value, color, style: {height: "3px", width: '150px'}}), 
            React.createElement('h5', {className: "m-0 ps-2"}, `${value}%`),
        );
};

dagcomponentfuncs.ScenarioVariablesOverlay = function (props) {
    return React.createElement(
        'div',
        {
            style: {
                padding: 10,
            },
        },
        props.message
    );
};