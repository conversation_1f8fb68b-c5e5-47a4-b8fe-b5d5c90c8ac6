FROM lennardong/sonic:dwsim84-postgre

WORKDIR /code

# Copy poetry files
COPY pyproject.toml /code/pyproject.toml

# Configure and install dependencies with poetry
RUN pip install poetry && \
    poetry config virtualenvs.create false \
    && poetry install --no-root --no-interaction --no-ansi

# Copy application code
COPY ./backend /code/backend
COPY ./frontend /code/frontend

ENV PYTHONPATH="/code"

# Command to run Celery worker
CMD ["poetry", "run", "celery", "--app=frontend.app.celery_app", "worker", "--loglevel=INFO"]