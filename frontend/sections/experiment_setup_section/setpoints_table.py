from dash import html

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock
from dash.development.base_component import Component


column_defs = [
    # columns: id, name, lower_bound, lower_value, upper_value, upper_bound, unit, row_index
    {
        "headerName": "Name",
        "field": "name",
        "editable": False,
        "tooltipField": "name",
        "cellClass": "overflow-hidden",
    },
    {
        "headerName": "",
        "field": "lower_bound",
        "editable": False,
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },
    {
        "headerName": "Lower Range",
        "field": "lower_value",
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },
    {
        "headerName": "Upper Range",
        "field": "upper_value",
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },
    {
        "headerName": "",
        "field": "upper_bound",
        "editable": False,
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },
]


def setpoints_table(id: str, title: str) -> Component:
    table = PaginatedTableAIO(id=id, column_defs=column_defs)
    block = TableBlock(title, table=table, has_add_button=False, has_del_button=False)

    return html.Div([block.render()], className="w-100")
