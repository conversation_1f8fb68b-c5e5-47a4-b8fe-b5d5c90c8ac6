from dash import html

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock


column_defs = [
    # columns: id, entity, condition, value
    {
        "headerCheckboxSelection": True,  # Enable header checkbox in this column
        "checkboxSelection": True,  # Enable checkboxes for row selection
        "headerName": "",
        "maxWidth": 40,
        "editable": False,
    },
    {
        "headerName": "Equipment/ Stream",
        "field": "entity_name",
        "sortable": True,
        "editable": False,
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },
    {
        "headerName": "Conditions",
        "field": "label",
        "sortable": True,
        "editable": False,
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },
    {
        "headerName": "Value",
        "field": "value",
    },
]

grid_options = {"rowSelection": "multiple", "suppressRowClickSelection": True}


def variable_calibration_table(id: str, title: str):
    table = PaginatedTableAIO(id=id, column_defs=column_defs, grid_options=grid_options)
    block = TableBlock(title, table=table, has_add_button=False, has_del_button=False)

    return html.Div([block.render()], className="w-100")
