from dash import html, dcc, callback, Input, Output, State, no_update, ctx, Patch
from dash.development.base_component import Component
from dash.exceptions import PreventUpdate
from datetime import datetime
import logging, json
from typing import Dict, Any, Optional, Set, Tuple, List, Union
import pandas as pd
import base64
import io

from frontend.utils.api_service import APIServiceProvider
from frontend.utils.toast_manager import ToastManager
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.base_container import Container
from frontend.components.frame import Frame
from frontend.components.base_dropdown import Dropdown
from frontend.components.base_spinner import Spinner
from frontend.components.base_input import InputField
from frontend.components.base_button import Button
from frontend.components.base_upload import Upload
from frontend.components.base_progress_bar import ProgressBar
from frontend.utils.toast_manager import ToastManager
from .variable_calibration_table import variable_calibration_table
from frontend.utils.auth import User
from frontend.models.calibration_models import (
    CalibrationVariable,
    CalibrationResult,
    CalibrationResultGraph,
)


api = APIServiceProvider().get_api_service()
logger = logging.getLogger()
DEFAULT_SECONDS = 10 * 60  # Time that the progress bar is set for 100%


##################################################################################
# LAYOUT
##################################################################################
def calibration_setup_section() -> Component:
    return Container(
        children=[
            html.H2("Calibration Setup", className="my-2 fw-semibold"),
            Frame(
                [
                    Dropdown(
                        [],
                        id="calibration-plant-config-select",
                        className="text-dark h5",
                        clearable=False,
                    )
                ],
                title="Choose your model...",
                id="calibration-plant-config-select-frame",
            ),
            html.Div(
                [
                    Upload(
                        id="calibration-sensor-data-upload",
                        children=[
                            Button(
                                id="calibration-sensor-data-upload-button",
                                children=[
                                    html.I(
                                        className="fa-solid fa-arrow-up-from-bracket"
                                    ),
                                    "Upload Sensor Data",
                                    Spinner(
                                        id="calibration-sensor-data-upload-button-spinner",
                                        size="sm",
                                        spinner_style={"display": "none"},
                                    ),
                                ],
                                className="bg-primary text-secondary rounded-3 d-flex align-items-center gap-2",
                            )
                        ],
                        className="d-inline-flex",
                    ),
                    variable_calibration_table(
                        id="variable-calibration-table",
                        title="Select equipment/ stream to calibrate: ",
                    ),
                ],
                id="calibration-upload-container",
                style={"display": "none"},
            ),
            html.Div(
                html.Div(
                    [
                        Button(
                            id="calibration-setup-save-button",
                            children=[
                                Spinner(
                                    id="calibration-setup-save-button-spinner",
                                    size="sm",
                                    spinner_style={"display": "none"},
                                ),
                                "Run",
                                InputField(
                                    id="calibration-setup-save-button-timestamp",
                                    className="d-none",
                                ),
                            ],
                            outline=True,
                            color="primary",
                            className="rounded-3 d-flex align-items-center gap-2",
                        ),
                    ],
                    className="d-flex justify-content-end py-3",
                ),
                style={"display": "none"},
                id="calibration-setup-save-container",
            ),
            html.Div(id="calibration-progress-bar-container"),
            dcc.Interval(
                id="calibration-progress-interval",
                n_intervals=0,
                interval=1000,
                disabled=True,
            ),
            html.Div(id="calibration-toast-container"),
            dcc.Store(id="calibration-setup-store", data={}),
        ]
    )


##################################################################################
# CALLBACKS
##################################################################################
@callback(
    Output("calibration-plant-config-select", "options"),
    Output("calibration-toast-container", "children"),
    Input("auth-store", "modified_timestamp"),
    State("auth-store", "data"),
    State("calibration-toast-container", "children"),
    prevent_initial_call=True,
)
def get_existing_plant_configs_on_section_load(
    ts: int, user_store: dict, toast_children: list
):
    """Get user existing plant configurations and load into dropdown"""

    toast_manager = ToastManager(toast_children)
    headers = User.get_headers_from_store(user_store)
    response, success = api.get(f"/plant-configurations", headers=headers)

    if not success:
        print("Failed to get plant configurations.", flush=True)
        toast = toast_manager.make_toast(
            ["Failed to get plant configurations."], "warning"
        )
        toast_manager.add_toast(toast)
        return no_update, toast_manager.toasts

    data = response["data"]
    options = [
        {
            "label": plant_config["name"],
            "value": plant_config["name"],
        }
        for plant_config in data
    ]

    return options, toast_manager.toasts


@callback(
    Output(
        PaginatedTableAIO.ids.table("variable-calibration-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("calibration-setup-store", "data", allow_duplicate=True),
    Output("calibration-upload-container", "style", allow_duplicate=True),
    Output("calibration-setup-save-container", "style", allow_duplicate=True),
    Output("calibration-toast-container", "children", allow_duplicate=True),
    Output("calibration-result-section", "style", allow_duplicate=True),
    Input("calibration-plant-config-select", "value"),
    State("auth-store", "data"),
    State("calibration-toast-container", "children"),
    prevent_initial_call=True,
)
def get_plant_config_on_select(
    plant_config_name: str, user_store: dict, toast_children: list
):
    """Get existing plant configuration details based on user selection
    and load variable calibration table"""

    print()
    print(f"-----------get_plant_config_on_select Callback----------")

    encoded_name = plant_config_name
    headers = User.get_headers_from_store(user_store)

    toast_manager = ToastManager(toast_children)
    store = {}
    default_return_on_fail = [
        no_update,
        no_update,
        {"display": "none"},
        {"display": "none"},
        toast_manager.toasts,
        {"display": "none"},
    ]

    # Get sensor mappings for chosen plant config
    response, success = api.get(
        f"/plant-configurations/{encoded_name}/sensors", headers=headers
    )
    if not success:
        error_msg = f"Failed to get sensor mappings for {encoded_name}. "
        print(f"{error_msg} | {response['error']}", flush=True)
        toast_manager.make_and_add_toast([error_msg], "warning")
        return default_return_on_fail

    sensor_mapping = response["data"]["csvCol2varUUID"]
    store["sensor_mapping"] = sensor_mapping
    store["timeset_col"] = response["data"]["timeset_col"]
    store["timestep_col"] = response["data"]["timestep_col"]

    if not sensor_mapping:
        error_msg = f"No sensor mappings for {encoded_name} have been defined. "
        print(error_msg, flush=True)
        toast_manager.make_and_add_toast([error_msg], "warning")
        return default_return_on_fail

    var_uids_w_mapping = set(sensor_mapping.values())

    # Get existing plant config details
    response, success = api.get(
        f"/plant-configurations/{encoded_name}", headers=headers
    )
    if not success:
        error_msg = f"Failed to get plant configuration details for {encoded_name}. "
        print(f"{error_msg} | {response['error']}", flush=True)
        toast_manager.make_and_add_toast([error_msg], "warning")
        return default_return_on_fail

    # Store condition variables in store as UUID-entity key value pairs
    data = response["data"]
    row_data = []
    for entity in data["equipments"]:
        for var in entity["conditions"]:
            if var["uuid_str"] not in var_uids_w_mapping:
                continue
            row_data.append(
                CalibrationVariable(
                    uid=var["uuid_str"],
                    entity_name=entity["equipmentId"],
                    label=var["title"],
                )
            )

    for entity in data["connections"]:
        for var in entity["conditions"]:
            if var["uuid_str"] not in var_uids_w_mapping:
                continue
            row_data.append(
                CalibrationVariable(
                    uid=var["uuid_str"],
                    entity_name=f"{entity['upstreamEquipment']}->{entity['downstreamEquipment']}",
                    label=var["title"],
                )
            )

    row_data = CalibrationVariable.hydrate(row_data)

    if not row_data:
        error_msg = f"No variables are available for calibration for {encoded_name}. "
        print(error_msg, flush=True)
        toast_manager.make_and_add_toast([error_msg], "warning")
        return default_return_on_fail

    # TODO ZL Check if plant config has surrogate model.

    return (
        row_data,
        store,
        {"display": "block"},
        {"display": "block"},
        no_update,
        {"display": "none"},
    )


@callback(
    Output("calibration-sensor-data-upload", "contents"),
    Output("calibration-toast-container", "children", allow_duplicate=True),
    Input("calibration-sensor-data-upload", "contents"),
    State("calibration-toast-container", "children"),
    State("calibration-setup-store", "data"),
    prevent_initial_call=True,
)
def validate_sensor_data_on_upload(contents: str, toast_children: list, store: dict):
    """Validate sensor data uploaded by user.

    Checks that:
    - File is in CSV format.
    - Timestep, timeset, and a minimum of 1 additional column that matches sensor mapping are present.
    """

    toast_manager = ToastManager(toast_children)

    # Decode data and validate that file is csv using pandas
    content_type, content_string = contents.split(",")
    decoded = base64.b64decode(content_string)

    try:
        df = pd.read_csv(io.StringIO(decoded.decode("utf-8")))
    except Exception as e:
        print(f"Please upload file in CSV format | {e}", flush=True)
        toast_manager.make_and_add_toast(
            ["Please upload file in CSV format. "], "warning"
        )
        return None, toast_manager.toasts

    uploaded_column_headers = set(df.columns)
    mapped_column_headers = set(store["sensor_mapping"].keys())
    timeset_col = store.get("timeset_col")
    timestep_col = store.get("timestep_col")
    are_columns_present = (
        len(uploaded_column_headers & mapped_column_headers) >= 1
        and timeset_col in uploaded_column_headers
        and timestep_col in uploaded_column_headers
    )

    if not are_columns_present:
        error_msg = f"There are missing column headers in your dataset. Please ensure that '{timeset_col}', '{timestep_col}', and at least one of the following are available: {mapped_column_headers}"
        print(error_msg, flush=True)
        toast_manager.make_and_add_toast([error_msg], "warning")
        return None, toast_manager.toasts

    raise PreventUpdate


@callback(
    Output(
        PaginatedTableAIO.ids.table("variable-calibration-table"),
        "selectedRows",
        allow_duplicate=True,
    ),
    Output(
        PaginatedTableAIO.ids.table("variable-calibration-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Input(
        PaginatedTableAIO.ids.table("variable-calibration-table"), "cellValueChanged"
    ),
    State(PaginatedTableAIO.ids.table("variable-calibration-table"), "selectedRows"),
    State(PaginatedTableAIO.ids.table("variable-calibration-table"), "rowData"),
    prevent_initial_call=True,
)
def validate_table_on_value_change(
    cell_changed,
    selected_rows: List[Dict],
    row_data: List[Dict],
):
    """Validate data when user changes row value column"""
    print()
    print(f"-----------validate_table_on_value_change Callback----------")

    updated_col = cell_changed[0]["colId"]
    row_index = cell_changed[0]["rowIndex"]

    rows = CalibrationVariable.bootstrap(row_data)
    selected_vars = CalibrationVariable.bootstrap(selected_rows)

    # Validate variable calibration table data when user changes value
    if updated_col == "value":
        var_changed = rows[row_index]

        # Remove selection if value is changed, regardless of whether value is None or Float
        selected_vars = [var for var in selected_vars if var.id != var_changed.id]
        return (
            CalibrationVariable.hydrate(selected_vars),
            CalibrationVariable.hydrate(rows),
        )

    raise PreventUpdate


@callback(
    Output(
        PaginatedTableAIO.ids.table("variable-calibration-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Input(PaginatedTableAIO.ids.table("variable-calibration-table"), "selectedRows"),
    State(PaginatedTableAIO.ids.table("variable-calibration-table"), "rowData"),
    prevent_initial_call=True,
)
def validate_table_on_selected_row_change(
    selected_rows: List[Dict],
    row_data: List[Dict],
):
    """Validate data when user changes row selection"""

    rows = CalibrationVariable.bootstrap(row_data)
    selected_vars = CalibrationVariable.bootstrap(selected_rows)

    selected_row_ids = set(var.id for var in selected_vars)
    for row in rows:
        row.value = None if row.id in selected_row_ids else row.value

    return CalibrationVariable.hydrate(rows)


@callback(
    Output("calibration-progress-bar-container", "children", allow_duplicate=True),
    Output("calibration-toast-container", "children", allow_duplicate=True),
    Output("calibration-progress-interval", "disabled", allow_duplicate=True),
    Output("calibration-result-section", "style"),
    Output("calibration-setup-save-button-timestamp", "value", allow_duplicate=True),
    Input("calibration-setup-save-button", "n_clicks"),
    State(PaginatedTableAIO.ids.table("variable-calibration-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("variable-calibration-table"), "selectedRows"),
    State("calibration-sensor-data-upload", "contents"),
    State("calibration-toast-container", "children"),
    prevent_initial_call=True,
)
def save_calibration_setup(
    save_clicks,
    row_data,
    selected_rows,
    contents,
    toast_children,
):
    """Validate calibration setup data and start calibration process if validations pass."""

    toast_manager = ToastManager(toast_children)
    rows = CalibrationVariable.bootstrap(row_data)
    selected_vars = CalibrationVariable.bootstrap(selected_rows)
    selected_row_ids = set(var.id for var in selected_vars)
    errors = []

    # Validate that data has been uploaded
    if not contents:
        toast = toast_manager.make_toast(["Please upload sensor data. "], "warning")
        toast_manager.add_toast(toast)
        return (
            no_update,
            toast_manager.toasts,
            no_update,
            {"display": "none"},
            no_update,
        )

    # Validate that variables not selected for calibration should have a specified value
    vars_w_missing_values = []
    for row in rows:
        if row.id not in selected_row_ids and row.value is None:
            vars_w_missing_values.append(f"{row.entity_name} - {row.label}")

    if vars_w_missing_values:
        errors.append(
            f"Please ensure these variables not chosen for calibration have values: {vars_w_missing_values}"
        )

    if errors:
        toast = toast_manager.make_toast(errors, "warning")
        toast_manager.add_toast(toast)
        return (
            no_update,
            toast_manager.toasts,
            no_update,
            {"display": "none"},
            no_update,
        )

    children = (
        ProgressBar(
            id="calibration-progress-bar",
            value=0,
            animated=True,
            style={"height": "5px"},
            color="primary",
        ),
    )

    return (
        children,
        no_update,
        False,
        {"display": "none"},
        datetime.now().isoformat(),
    )


@callback(
    Output("calibration-progress-bar", "value"),
    Input("calibration-progress-interval", "n_intervals"),
)
def update_progress(n):
    value = min(n * 100 / DEFAULT_SECONDS, 100)
    return value


@callback(
    Output("calibration-progress-bar-container", "children", allow_duplicate=True),
    Output("calibration-progress-interval", "disabled", allow_duplicate=True),
    Output("calibration-progress-interval", "n_intervals", allow_duplicate=True),
    Output("calibration-result-store", "data", allow_duplicate=True),
    Output("calibration-toast-container", "children", allow_duplicate=True),
    Input("calibration-progress-interval", "disabled"),
    State("calibration-plant-config-select", "value"),
    State(PaginatedTableAIO.ids.table("variable-calibration-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("variable-calibration-table"), "selectedRows"),
    State("calibration-toast-container", "children"),
    State("auth-store", "data"),
    State("calibration-sensor-data-upload", "contents"),
    prevent_initial_call=True,
    background=True,
    running=[
        (Output("calibration-setup-save-button", "disabled"), True, False),
        (
            Output(
                "calibration-setup-save-button-spinner",
                "spinner_style",
            ),
            {"display": "inline-block"},
            {"display": "none"},
        ),
    ],
)
def run_calibration(
    is_interval_disabled,
    plant_config_name: str,
    row_data,
    selected_rows,
    toast_children,
    user_store,
    contents: str,
):
    """Send data to BE to run calibration"""

    headers = User.get_headers_from_store(user_store)
    user_id = User.pull_user_id_from_store(user_store)
    toast_manager = ToastManager(toast_children)

    print("Calibration running...")
    content_type, content_string = contents.split(",")
    rows = CalibrationVariable.bootstrap(row_data)
    selected_vars = CalibrationVariable.bootstrap(selected_rows)

    fixed_vars_mapping = {var.uid: var.value for var in rows if var.value}

    data = {
        "user_id": user_id,
        "atlas_label": plant_config_name,
        "conditions_to_calibrate": list(var.uid for var in selected_vars),
        "conditions_to_fix": fixed_vars_mapping,
        "calibration_samples": content_string,
    }

    print("---------------------------------------")
    print("CalibrationConfig sent to BE = ")
    print(data, flush=True)
    print("---------------------------------------")

    response, success = api.post(f"/calibration", data=data, headers=headers)

    if not success:
        print(
            f"Calibration failed to run for {plant_config_name} | {response['error']}",
            flush=True,
        )
        new_toast = toast_manager.make_toast(
            ["Failed to run calibration. Please try again later."], category="warning"
        )
        toast_manager.add_toast(new_toast)
        return [], True, 0, no_update, toast_manager.toasts

    print("---------------------------------------")
    print("CalibrationResponse received from BE = ")
    print(response, flush=True)
    print("---------------------------------------")

    data = response["data"]["data"]

    # TESTING WITH MOCK
    # import json

    # with open(
    #     "frontend/sections/calibration_result_section/mock_calibration_results.json"
    # ) as f:
    #     response = json.load(f)
    #     success = True

    # data = response["data"]

    table_results = CalibrationResult.bootstrap_from_raw_data(data["table"]["data"])
    data["table_data"] = CalibrationResult.hydrate(table_results)

    # Store line chart data
    filter_options = []
    line_chart_data = []
    plot1_data = data["chart1"]
    for entity_name, variables in plot1_data["pri_data"].items():
        for var_name, points in variables.items():
            filter_options.append(f"{entity_name} {var_name}")
            data_to_add = [
                CalibrationResultGraph(
                    entity_name=entity_name,
                    var_name=var_name,
                    variable=f"{entity_name} {var_name}",
                    timestep=i + 1,
                    value=points[i],
                )
                for i in range(len(points))
            ]
            line_chart_data += data_to_add

    for entity_name, variables in plot1_data["sec_data"].items():
        for var_name, points in variables.items():
            data_to_add = [
                CalibrationResultGraph(
                    entity_name=entity_name,
                    var_name=var_name,
                    variable=f"{entity_name} {var_name} simulated",
                    timestep=i + 1,
                    value=points[i],
                )
                for i in range(len(points))
            ]
            line_chart_data += data_to_add

    # Store bar chart data
    bar_chart_data = []
    plot2_data = data["chart2"]
    for entity_name, variables in plot2_data["pri_data"].items():
        for var_name, points in variables.items():
            data_to_add = [
                CalibrationResultGraph(
                    entity_name=entity_name,
                    var_name=var_name,
                    variable=f"{entity_name} {var_name}",
                    timestep=i + 1,
                    value=points[i],
                )
                for i in range(len(points))
            ]
            bar_chart_data += data_to_add

    data["bar_chart"] = {
        "data": CalibrationResultGraph.hydrate(bar_chart_data),
        "title": plot2_data["title"],
        "x_axis_title": plot2_data["axes"][0],
        "y_axis_title": plot2_data["axes"][1],
    }

    data["line_chart"] = {
        "data": CalibrationResultGraph.hydrate(line_chart_data),
        "title": plot1_data["title"],
        "x_axis_title": plot1_data["axes"][0],
        "y_axis_title": plot1_data["axes"][1],
    }

    print(f"Calibration results store: {data}", flush=True)
    return [], True, 0, data, toast_manager.toasts
