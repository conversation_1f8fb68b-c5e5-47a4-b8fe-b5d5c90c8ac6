from dash import html, callback, Input, Output, State, ctx, no_update
from dash.development.base_component import Component
from frontend.components.table_block import TableBlock
from frontend.components.base_table import BaseTable
from frontend.components.base_button import Button
from frontend.components.base_modal import Modal
from typing import Dict, List, Any, Tuple

column_defs = [
    # columns: name, equipment
    {
        "headerName": "Variable Name",
        "field": "name",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
        "cellClass": "overflow-hidden",
        "tooltipField": "name",
    },
    {
        "headerName": "Equipment/ Stream",
        "field": "equipment",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
        "cellClass": "overflow-hidden",
        "tooltipField": "equipment",
    },
    {
        "headerName": "Type",
        "field": "entity_type",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
        "cellClass": "overflow-hidden",
        "tooltipField": "entity_type",
    },
]

grid_options = {
    "suppressMenuHide": True,
    "enableCellTextSelection": True,
}


def variable_listings_table(id: str) -> Component:
    table = BaseTable(
        id, column_defs=column_defs, grid_options=grid_options, editable=False
    )
    block = TableBlock("", table=table, has_add_button=False, has_del_button=False)

    return html.Div([block.render()], className="w-100")
