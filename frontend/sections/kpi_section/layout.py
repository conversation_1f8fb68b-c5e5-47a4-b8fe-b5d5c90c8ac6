from dash.development.base_component import Component
from dash import html, dcc, callback, Input, Output, State, no_update
import re
from sympy.parsing.sympy_parser import parse_expr
from sympy.core.sympify import SympifyError
from tokenize import TokenError
from typing import Dict, List, Any, Tuple, Set
from collections import defaultdict
from datetime import datetime

from frontend.components.base_button import Button
from frontend.components.base_radio import Radio
from frontend.components.base_tooltip import Tooltip
from frontend.components.base_modal import Modal
from frontend.components.base_modal_body import ModalBody
from frontend.components.base_modal_footer import ModalFooter
from frontend.components.base_modal_header import ModalHeader
from frontend.components.base_input import InputField
from frontend.components.base_label import Label
from frontend.components.base_textarea import TextArea
from frontend.components.base_toast import Toast
from frontend.components.base_spinner import Spinner
from frontend.components.base_container import Container
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.utils.toast_manager import ToastManager
from frontend.utils.auth import User
from .variables_table import variables_table
from .kpi_table import kpi_table
from .variable_listings_table import variable_listings_table

from frontend.utils.api_service import APIServiceProvider
from frontend.utils.url_encoder_service import UrlEncoderService
from frontend.utils.uuid_service import UUIDService

import dash_bootstrap_components as dbc


api = APIServiceProvider().get_api_service()


def kpi_section(project_name: str = "") -> Component:
    decoded_project_name = UrlEncoderService.decode(project_name)

    return Container(
        children=[
            html.H2("Define KPIs", className="my-2 fw-semibold"),
            html.Div(
                [
                    Label(
                        "Do you want to define KPIs to your current plant configuration?",
                        className="text-light h4 m-0",
                    ),
                    Radio(
                        id="is-kpi-definable",
                        options=[
                            {"label": "Yes", "value": True},
                            {"label": "No", "value": False},
                        ],
                        value=False,
                        className="d-flex gap-2",
                    ),
                ],
                className="d-flex gap-4 align-items-center",
            ),
            html.Div(
                [
                    html.Div(
                        [
                            html.Div(
                                [
                                    variables_table("variables-table"),
                                ],
                                className="w-100",
                            ),
                            html.Div([kpi_table()], className="w-50"),
                        ],
                        className="d-flex",
                    ),
                    html.Div(
                        [
                            Button(
                                id="kpi-save-button",
                                children=[
                                    Spinner(
                                        id="kpi-save-button-spinner",
                                        size="sm",
                                        spinner_style={"display": "none"},
                                    ),
                                    InputField(
                                        id="kpi-save-button-timestamp",
                                        className="d-none",
                                    ),
                                    "Save",
                                ],
                                outline=True,
                                color="primary",
                                className="rounded-3 d-flex align-items-center gap-2",
                            ),
                        ],
                        className="d-flex justify-content-end py-3",
                    ),
                ],
                id="kpi-section",
                style={"display": "none"},
            ),
            Modal(
                id="create-kpi-modal",
                children=[
                    ModalBody(
                        [
                            html.H2(
                                "Add KPIS", className="fw-semibold text-center py-3"
                            ),
                            variable_listings_table("variable-listings-table"),
                            html.Div(
                                [
                                    Label("Name", className="h5 fw-medium text-dark"),
                                    InputField(
                                        type="text",
                                        id="create-kpi-name",
                                        placeholder="Enter KPI Name",
                                        className="h5 fw-medium text-light",
                                    ),
                                ],
                                className="px-2",
                            ),
                            html.Div(
                                [
                                    html.Div(
                                        [
                                            Label(
                                                "Expression",
                                                className="h5 fw-medium text-dark",
                                            ),
                                            html.I(
                                                id="kpi-expression-tooltip",
                                                className="fa-solid fa-circle-question mb-2",
                                            ),
                                            Tooltip(
                                                [
                                                    html.H5(
                                                        "Please ensure all variables are enclosed within double braces. ",
                                                        className="m-0 fw-normal",
                                                    ),
                                                    html.I(
                                                        "E.g. {{variable_name}}",
                                                        className="m-0 fw-normal",
                                                    ),
                                                    html.H5(
                                                        "Tip: Triple-click Variable Name to copy and paste into expression. ",
                                                        className="m-0 fw-normal",
                                                    ),
                                                ],
                                                target="kpi-expression-tooltip",
                                                className="bg-white text-black rounded-2 border-1 border-info p-2 shadow-sm",
                                            ),
                                        ],
                                        className="d-flex align-items-center gap-2",
                                    ),
                                    TextArea(
                                        id="create-kpi-expression",
                                        placeholder="Enter an expression",
                                        value="",
                                        className="h5 fw-medium text-light",
                                    ),
                                ],
                                className="px-2",
                            ),
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="create-kpi-modal-cancel-button",
                                children=[
                                    html.I(className="fa-solid fa-xmark me-2"),
                                    "Cancel",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="create-kpi-modal-add-button",
                                children=[
                                    html.I(className="fa-solid fa-check me-2"),
                                    "Confirm",
                                ],
                                className="bg-primary text-secondary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
                size="lg",
            ),
            Toast(
                id="kpi-toast",
                is_open=False,
            ),
            # Store is used for storing the project/config name
            dcc.Store(id="kpi-store", data={"project_name": decoded_project_name}),
            dcc.Store(id="kpi-errors-store"),
            html.Div(id="kpi-toast-container"),
        ]
    )


@callback(
    Output("kpi-section", "style"),
    Output(PaginatedTableAIO.ids.table("variables-table"), "rowData"),
    Output("variable-listings-table", "rowData"),
    Output(PaginatedTableAIO.ids.table("kpi-table"), "rowData"),
    Output(PaginatedTableAIO.ids.table("variables-table"), "selectedRows"),
    Output(
        PaginatedTableAIO.ids.table("variables-table"),
        "columnSize",
    ),
    Output(
        "variable-listings-table",
        "columnSize",
    ),
    Output(
        PaginatedTableAIO.ids.table("kpi-table"),
        "columnSize",
    ),
    Output("kpi-errors-store", "data"),
    Output("is-kpi-definable", "value"),
    Input("is-kpi-definable", "value"),
    State("kpi-store", "data"),
    State("auth-store", "data"),
    prevent_initial_call=True,
)
def show_kpis(
    is_kpi_definable: bool, store: Dict[str, Any], user_store: Dict[str, Any]
):
    """Display KPIs retrieved from BE"""

    headers = User.get_headers_from_store(user_store)

    if is_kpi_definable:
        # Call BE and populate tables
        project_name = store["project_name"]

        # Check if plant config exists and is defined
        response, success = api.get(
            f"/plant-configurations/{project_name}", headers=headers
        )
        if not success or not response["data"]:
            return (
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                {
                    "category": "warning",
                    "messages": ["Failed to get plant configuration."],
                    "timestamp": datetime.now(),
                },
                False,
            )

        # Query KPI variables from BE
        response, success = api.get(
            f"/plant-configurations/{project_name}/kpi-variables", headers=headers
        )
        if not success:
            return (
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                {
                    "category": "warning",
                    "messages": ["Failed to get KPI variables."],
                    "timestamp": datetime.now(),
                },
                False,
            )

        # Store in Variables table
        variables_row_data = []
        variables_mapping = {}
        data = response["data"]

        for var in data:
            row = {
                "id": UUIDService.generate_id(),
                "name": var["variableName"],
                "unit": var["variableUnit"],
                "tagged_sensor": var["variableSensor"],
                "tagged_entity": var["variableEntity"],
                "entity_type": var["variableEntityType"],
                "expression": "{{" + var["variableName"] + "}}",
                "delete": "Delete",
                "is_var": True,
                "variables": [var["variableName"]],
            }
            variables_row_data.append(row)
            variables_mapping[row["name"]] = row

        print(f"variables_row_data = {variables_row_data}")

        variable_listings_row_data = []
        for var in data:
            row = {
                "id": UUIDService.generate_id(),
                "name": var["variableName"],
                "equipment": var["variableEntity"],
                "entity_type": var["variableEntityType"],
            }
            variable_listings_row_data.append(row)

        print(f"variable_listings_row_data = {variables_row_data}")

        # Query KPIs defined from BE
        response, success = api.get(
            f"/plant-configurations/{project_name}/kpis", headers=headers
        )
        if not success:
            return (
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                {
                    "category": "warning",
                    "messages": ["Failed to get KPIs."],
                    "timestamp": datetime.now(),
                },
                False,
            )

        kpi_row_data, selected_rows = [], []
        data = response["data"]

        for kpi in data:
            # Separate system-generated vs user-generated KPIs
            if "{{" + kpi["name"] + "}}" == kpi["expression"]:
                selected_rows.append(variables_mapping[kpi["name"]])
            else:
                row = {
                    "id": UUIDService.generate_id(),
                    "name": kpi["name"],
                    "unit": "",
                    "tagged_sensor": "",
                    "tagged_entity": "",
                    "entity_type": "",
                    "expression": kpi["expression"],
                    "delete": "Delete",
                    "is_var": False,
                    "variables": kpi["variables"],
                }
                variables_row_data.append(row)
                selected_rows.append(row)

            row = {
                "id": UUIDService.generate_id(),
                "name": kpi["name"],
                "expression": kpi["expression"],
                "variables": kpi["variables"],
            }
            kpi_row_data.append(row)

        print(f"kpi_row_data = {kpi_row_data}")
        print(f"selected_rows = {selected_rows}")

        return (
            {"display": "block"},
            variables_row_data,
            variable_listings_row_data,
            kpi_row_data,
            selected_rows,
            "sizeToFit",
            "sizeToFit",
            "sizeToFit",
            no_update,
            no_update,
        )
    else:
        # Hide tables
        return (
            {"display": "none"},
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
        )


@callback(
    Output("kpi-toast-container", "children"),
    Input("kpi-errors-store", "modified_timestamp"),
    State("kpi-errors-store", "data"),
    State("kpi-toast-container", "children"),
    prevent_initial_call=True,
)
def display_toasts(ts, store, toast_children):
    toast_manager = ToastManager(toast_children)
    category = store.get("category", "info")
    messages = store.get("messages", [])

    if not messages:
        return no_update

    new_toast = toast_manager.make_toast(messages, category)
    toast_manager.add_toast(new_toast)
    return toast_manager.toasts


# @callback(
#     Output(PaginatedTableAIO.ids.table("variables-table"), "rowData"),
#     Output("variable-listings-table", "rowData"),
#     Output(PaginatedTableAIO.ids.table("kpi-table"), "rowData"),
#     Output(PaginatedTableAIO.ids.table("variables-table"), "selectedRows"),
#     Input("kpi-section-load", "children"),
#     State("kpi-store", "data"),
# )
# def get_existing_kpi_and_presets_on_page_load(children, store):
#     print()
#     print(f"-----------get_existing_kpi_and_presets_on_page_load Callback----------")

#     # Get project name and encode
#     project_name = UrlEncoderService.encode(store["project_name"])

#     # Query KPI variables from BE
#     # TODO: [REMOVE] Temporary mock
#     # response, success = api.get(f'/plant-configurations/{project_name}/kpi-variables')
#     response = {
#         "data": [
#             {
#                 "variableName": "Temperature1",
#                 "variableUnit": "K",
#                 "variableSensor": "S-0022",
#                 "variableEquipment": "HX-001",
#             },
#             {
#                 "variableName": "Temperature2",
#                 "variableUnit": "K",
#                 "variableSensor": "S-0022",
#                 "variableEquipment": "HX-001",
#             },
#             {
#                 "variableName": "Pressure",
#                 "variableUnit": "Pa",
#                 "variableSensor": "S-0011",
#                 "variableEquipment": "RX-001",
#             },
#             {
#                 "variableName": "Power In",
#                 "variableUnit": "W",
#                 "variableSensor": "S-001",
#                 "variableEquipment": "RX-001",
#             },
#             {
#                 "variableName": "Power Out",
#                 "variableUnit": "W",
#                 "variableSensor": "S-0002",
#                 "variableEquipment": "RX-001",
#             },
#             {
#                 "variableName": "Total Power",
#                 "variableUnit": "W",
#                 "variableSensor": "S-0003",
#                 "variableEquipment": "RX-001",
#             },
#         ]
#     }
#     success = True
#     if not success:
#         return no_update, no_update, no_update, no_update

#     # Store in Variables table
#     variables_row_data = []
#     data = response["data"]
#     for var in data:
#         row = {
#             "id": UUIDService.generate_id(),
#             "name": var["variableName"],
#             "unit": var["variableUnit"],
#             "tagged_sensor": var["variableSensor"],
#             "tagged_equipment": var["variableEquipment"],
#             "expression": "{{" + var["variableName"] + "}}",
#             "delete": "Delete",
#             "is_var": True,
#             "variables": ["{{" + var["variableName"] + "}}"],
#         }
#         variables_row_data.append(row)

#     print(f"variables_row_data = {variables_row_data}")

#     variable_listings_row_data = []
#     for var in data:
#         row = {
#             "id": UUIDService.generate_id(),
#             "name": var["variableName"],
#         }
#         variable_listings_row_data.append(row)

#     print(f"variable_listings_row_data = {variables_row_data}")

#     # Query KPIs defined from BE
#     # TODO: [REMOVE] Temporary mock
#     # response, success = api.get(f'/plant-configurations/{project_name}/kpis')
#     response = {
#         "data": [
#             {
#                 "name": "Temperature Delta",
#                 "variables": ["Temperature1", "Temperature2"],
#                 "expression": "{{Temperature2}}-{{Temperature1}}",
#             },
#             {
#                 "name": "Efficiency",
#                 "variables": ["Power In", "Power Out", "Total Power"],
#                 "expression": "({{Power In}}-{{Power Out}})/{{Total Power}}",
#             },
#         ]
#     }
#     success = True
#     if not success:
#         return no_update, no_update, no_update, no_update

#     kpi_row_data, selected_rows = [], []
#     data = response["data"]
#     for kpi in data:
#         row = {
#             "id": UUIDService.generate_id(),
#             "name": kpi["name"],
#             "unit": "",
#             "tagged_sensor": "",
#             "tagged_equipment": "",
#             "expression": kpi["expression"],
#             "delete": "Delete",
#             "is_var": False,
#             "variables": kpi["variables"],
#         }
#         variables_row_data.append(row)
#         selected_rows.append(row)

#         row = {
#             "id": UUIDService.generate_id(),
#             "name": kpi["name"],
#             "expression": kpi["expression"],
#             "variables": kpi["variables"],
#         }
#         kpi_row_data.append(row)

#     print(f"kpi_row_data = {kpi_row_data}")
#     print(f"selected_rows = {selected_rows}")

#     return variables_row_data, variable_listings_row_data, kpi_row_data, selected_rows


@callback(
    Output("create-kpi-modal", "is_open", allow_duplicate=True),
    Input("variables-table-add-button", "n_clicks"),
    State("kpi-store", "data"),
    prevent_initial_call=True,
)
def open_kpi_modal(
    n_clicks: int, store: Dict[str, Any]
) -> Tuple[bool, List[Dict[str, Any]], Dict[str, Any]]:
    """Opens KPI modal"""
    if n_clicks:
        return True
    return no_update


@callback(
    Output("create-kpi-modal", "is_open", allow_duplicate=True),
    Input("create-kpi-modal-cancel-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_kpi_modal(n_clicks: int) -> bool:
    """Closes KPI modal"""
    if n_clicks:
        return False
    return no_update


@callback(
    Output(PaginatedTableAIO.ids.table("kpi-table"), "rowData", allow_duplicate=True),
    Input(PaginatedTableAIO.ids.table("variables-table"), "selectedRows"),
    prevent_initial_call=True,
)
def update_selected_kpis(selected_rows):
    print()
    print(f"-----------update_selected_kpis Callback----------")

    print(f"selected_rows = {selected_rows}")

    kpi_row_data = [
        {
            "id": UUIDService.generate_id(),
            "name": row["name"],
            "expression": row["expression"],
            "variables": row["variables"],
        }
        for row in selected_rows
    ]

    return kpi_row_data


@callback(
    Output("create-kpi-modal", "is_open", allow_duplicate=True),
    Output(
        PaginatedTableAIO.ids.table("variables-table"), "rowData", allow_duplicate=True
    ),
    Output("kpi-errors-store", "data", allow_duplicate=True),
    Output("create-kpi-name", "value"),
    Output("create-kpi-expression", "value"),
    Input("create-kpi-modal-add-button", "n_clicks"),
    State("create-kpi-name", "value"),
    State("create-kpi-expression", "value"),
    State(PaginatedTableAIO.ids.table("variables-table"), "rowData"),
    prevent_initial_call=True,
)
def add_kpi(n_clicks, name, expression, variables_row_data):
    """Add user-defined KPI and closes modal"""
    print()
    print(f"-----------add_kpi Callback----------")

    # Check that fields are not empty
    if not name or not expression:
        return (
            no_update,
            no_update,
            {
                "category": "warning",
                "messages": ["Please enter a name and expression"],
                "timestamp": datetime.now(),
            },
            no_update,
            no_update,
        )

    # Validate name is unique
    existing_names = set(row["name"] for row in variables_row_data)
    print(existing_names)

    if name in existing_names:
        print(f"Name already exists: {name}")
        return (
            no_update,
            no_update,
            {
                "category": "warning",
                "messages": ["Please choose another name"],
                "timestamp": datetime.now(),
            },
            no_update,
            no_update,
        )

    variables = get_variables_from_expression(expression)

    # Ensure that at least 1 variable is chosen
    if len(variables) < 1:
        return (
            no_update,
            no_update,
            {
                "category": "warning",
                "messages": ["Please choose at least 1 variable"],
                "timestamp": datetime.now(),
            },
            no_update,
            no_update,
        )

    # Validate expression
    try:
        new_expression = replace_variables_with_dummy(variables, expression)
        validate_variables(variables, existing_names)
        validate_expression(new_expression)
    except ValueError as error:
        print(f"error = {error}")
        print(type(error))
        return (
            no_update,
            no_update,
            {
                "category": "warning",
                "messages": [str(error)],
                "timestamp": datetime.now(),
            },
            no_update,
            no_update,
        )

    new_row = {
        "id": UUIDService.generate_id(),
        "name": name,
        "unit": "",
        "tagged_sensor": "",
        "tagged_entity": "",
        "entity_type": "",
        "expression": expression,
        "delete": "Delete",
        "is_var": False,
        "variables": list(variables),
    }
    variables_row_data.append(new_row)

    return (
        False,
        variables_row_data,
        {
            "category": "success",
            "messages": ["Added KPI successfully"],
            "timestamp": datetime.now(),
        },
        None,
        None,
    )


@callback(
    Output(
        PaginatedTableAIO.ids.table("variables-table"), "rowData", allow_duplicate=True
    ),
    Input(PaginatedTableAIO.ids.table("variables-table"), "cellRendererData"),
    State(PaginatedTableAIO.ids.table("variables-table"), "rowData"),
    prevent_initial_call=True,
)
def delete_kpi(row, variables_row_data):
    """Delete user-defined KPI"""
    print()
    print(f"-----------delete_kpi Callback----------")

    print(f"row = {row}")
    row_id = row["rowId"]
    variables_row_data = [row for row in variables_row_data if row["id"] != row_id]
    return variables_row_data


@callback(
    Output("kpi-save-button-spinner", "spinner_style"),
    Output("kpi-save-button-timestamp", "value"),
    Input("kpi-save-button", "n_clicks"),
    prevent_initial_call=True,
)
def start_save_kpis(n_clicks: int):
    if n_clicks:
        return {"display": "block"}, datetime.now().isoformat()
    return no_update


@callback(
    Output("kpi-errors-store", "data", allow_duplicate=True),
    Output("kpi-save-button-spinner", "spinner_style", allow_duplicate=True),
    Input("kpi-save-button-timestamp", "value"),
    State(PaginatedTableAIO.ids.table("kpi-table"), "rowData"),
    State("kpi-store", "data"),
    State("auth-store", "data"),
    prevent_initial_call=True,
)
def save_kpis(ts: str, kpi_row_data, store, user_store: Dict[str, Any]):
    """Saves KPIs"""
    print()
    print(f"-----------save_kpis Callback----------")

    print(f"kpi_row_data = {kpi_row_data}")

    # Save KPIs to BE
    # Get project name and encode
    project_name = store["project_name"]
    headers = User.get_headers_from_store(user_store)

    data = [
        {
            "name": row["name"],
            "variables": row["variables"],
            "expression": row["expression"],
        }
        for row in kpi_row_data
    ]
    print(f"data posted = {data}")

    response, success = api.post(
        f"/plant-configurations/{project_name}/kpis", data=data, headers=headers
    )
    if not success:
        toast_message = "Failed to update KPIs."
        return {
            "category": "warning",
            "messages": [toast_message],
            "timestamp": datetime.now(),
        }, {"display": "none"}

    toast_message = "KPIs saved successfully!"
    return {
        "category": "success",
        "messages": [toast_message],
        "timestamp": datetime.now(),
    }, {"display": "none"}


def get_variables_from_expression(expression: str) -> Set[str]:
    """Gets variables from expression
    Raises ValueError if values are malformed"""

    pattern = r"\{\{(.*?)\}\}"
    variables = re.findall(pattern, expression)
    print(f"Variables retrieved from {expression} = {variables}")
    return set(variables)


def replace_variables_with_dummy(variables: Set, expression: str) -> str:
    """Replace variables enclosed with braces to regular variables"""
    dummy_value = "a"
    for var in variables:
        # Create a regex pattern for the variable enclosed in double braces
        pattern = r"\{\{" + re.escape(var) + r"\}\}"
        # Replace all occurrences with the dummy value
        expression = re.sub(pattern, dummy_value, expression)

    print(f"Replaced expression = {expression}")
    return expression


def validate_variables(actual: Set, reference: Set) -> None:
    intersection = actual & reference
    if intersection != actual:
        invalid_variables = list(actual - intersection)
        raise ValueError(f"Invalid variables found: {', '.join(invalid_variables)}")
    print("Variables valid")


def validate_expression(expression: str):
    # Check for orphaned braces
    orphaned_pattern = r"[\{\}]"
    if bool(re.search(orphaned_pattern, expression)):
        raise ValueError(
            "Please ensure all variables are enclosed within double braces."
        )

    try:
        parse_expr(expression, evaluate=False)
    except SyntaxError or SympifyError as e:
        raise ValueError("Invalid expression")
    except TokenError as e:
        raise ValueError("Unmatched parentheses or unrecognized characters")
    print("Expression valid")
