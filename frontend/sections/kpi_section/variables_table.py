from dash import html, callback, Input, Output, State, ctx, no_update
from dash.development.base_component import Component
from frontend.components.table_block import TableBlock
from frontend.components.base_table import BaseTable
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.base_button import Button
from frontend.components.base_modal import <PERSON><PERSON>
from typing import Dict, List, Any, Tuple

column_defs = [
    # columns: id, name, tagged_equipment, tagged_sensor, unit, expression, delete, is_var
    {
        "headerCheckboxSelection": True,  # Enable header checkbox in this column
        "checkboxSelection": True,  # Enable checkboxes for row selection
        "headerName": "",
        "maxWidth": 40,
        "editable": False,
    },
    {
        "headerName": "Variable",
        "field": "name",
        "cellClass": "overflow-hidden",
        "tooltipField": "name",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },
    {
        "headerName": "Equipment/ Stream",
        "field": "tagged_entity",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
        "cellClass": "overflow-hidden",
        "tooltipField": "tagged_entity",
    },
    {
        "headerName": "Tagged Sensor",
        "field": "tagged_sensor",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },
    {
        "headerName": "Type",
        "field": "entity_type",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
        "cellClass": "overflow-hidden",
        "tooltipField": "entity_type",
    },
    {
        "headerName": "Unit",
        "field": "unit",
        "maxWidth": 100,
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },
    {
        "headerName": "Expression",
        "field": "expression",
        "cellClass": "overflow-hidden",
        "tooltipField": "expression",
    },
    {
        "headerName": "",
        "field": "delete",
        "cellRenderer": "OptionalButton",
        "cellRendererParams": {
            "color": "primary",
            "iconClass": "fa-solid fa-trash-can",
            "buttonClass": "btn-sm text-danger bg-transparent border-0",
            "text": "",
        },
        "cellClass": "d-flex align-items-center justify-content-center",
        "maxWidth": 40,
        "editable": False,
    },
]

grid_options = {
    "rowSelection": "multiple",
    "suppressRowClickSelection": True,
    "suppressMenuHide": True,
    "pagination": True,
    "suppressPaginationPanel": True,
    "paginationPageSize": 8,
    # "domLayout": "autoHeight",
}


def variables_table(id: str) -> Component:
    table = PaginatedTableAIO(
        id=id, column_defs=column_defs, grid_options=grid_options, editable=False
    )
    block = TableBlock("Available Variables", table=table, has_del_button=False)

    return html.Div([block.render()], className="w-100")
