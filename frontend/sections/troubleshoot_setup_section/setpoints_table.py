from dataclasses import dataclass
from typing import List
from dash import html

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock
from frontend.utils.uuid_service import UUIDService
from dash.development.base_component import Component
from pydantic import BaseModel, Field, RootModel


@dataclass
class DiagnosisSetpoint:
    id: str
    name: str
    lower_bound: float
    upper_bound: float
    value: float
    lower_range: float
    upper_range: float
    unit: str
    row_index: int


class DiagnosisSetpointTable(RootModel[List[DiagnosisSetpoint]]):
    pass


column_defs = [
    # columns: id, name, lower_bound, value, upper_bound, unit, row_index
    {
        "headerName": "Name",
        "field": "name",
        "editable": False,
        "tooltipField": "name",
        "cellClass": "overflow-hidden",
    },
    {
        "headerName": "",
        "field": "lower_bound",
        "editable": False,
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
        "tooltipField": "lower_bound",
        "cellClass": "overflow-hidden",
    },
    {
        "headerName": "Lower Range",
        "field": "lower_range",
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
        "tooltipField": "lower_range",
        "cellClass": "overflow-hidden",
    },
    {
        "headerName": "Value",
        "field": "value",
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
        "tooltipField": "value",
        "cellClass": "overflow-hidden",
    },
    {
        "headerName": "Upper Range",
        "field": "upper_range",
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
        "tooltipField": "upper_range",
        "cellClass": "overflow-hidden",
    },
    {
        "headerName": "",
        "field": "upper_bound",
        "editable": False,
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
        "tooltipField": "upper_bound",
        "cellClass": "overflow-hidden",
    },
]


def setpoints_table(id: str, title: str) -> Component:
    table = PaginatedTableAIO(id=id, column_defs=column_defs)
    block = TableBlock(title, table=table, has_add_button=False, has_del_button=False)

    return html.Div([block.render()], className="w-100")
