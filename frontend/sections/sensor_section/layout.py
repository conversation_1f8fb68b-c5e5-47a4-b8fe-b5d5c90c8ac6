import base64
import io
from dash.development.base_component import Component
from dash import html, dcc, callback, Input, Output, State, no_update
from dash.exceptions import PreventUpdate
import pandas as pd
from frontend.components.base_button import Button
from frontend.components.base_modal import Modal
from frontend.components.base_modal_body import ModalBody
from frontend.components.base_modal_footer import ModalFooter
from frontend.components.base_tooltip import Tooltip
from frontend.components.base_container import Container
from frontend.components.base_upload import Upload
from frontend.components.base_spinner import Spinner
from frontend.utils.toast_manager import ToastManager
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.utils.api_service import APIServiceProvider
from frontend.utils.url_encoder_service import UrlEncoderService
from frontend.utils.auth import User
from frontend.utils.enums import StatusTagGenerator
from frontend.utils.uuid_service import UUIDService
from frontend.components.column_mapping_table import (
    column_mapping_table,
    update_variable_options,
    validate_column_mapping_on_var_id_change,
    validate_column_mapping_on_timeset_change,
    validate_column_mapping_on_timestep_change,
)

from typing import Dict, List, Any, Tuple
import uuid
from collections import defaultdict
from datetime import datetime

api = APIServiceProvider().get_api_service()


##################################################################################
# LAYOUT
##################################################################################
def sensor_section(project_name: str = "") -> Component:
    decoded_project_name = UrlEncoderService.decode(project_name)

    return Container(
        children=[
            html.Div(
                [
                    html.H2(
                        ["Sensor Mapping"],
                        className="my-2 fw-semibold",
                    ),
                    html.Div(
                        id="sensor-mapping-status",
                        className="d-flex",
                    ),
                ],
                className="d-flex gap-4 align-items-center",
            ),
            html.Div(
                [
                    Button(
                        id="sensor-mapping-configure-button",
                        children=[
                            html.I(className="fa-solid fa-gears me-2"),
                            "Configure",
                        ],
                        className="bg-secondary text-primary rounded-3",
                    ),
                ],
                className="d-flex gap-2 justify-content-start align-items-center my-2",
            ),
            Modal(
                id="sensor-mapping-configure-modal",
                children=[
                    ModalBody(
                        [
                            html.H2(
                                [
                                    "Sensor Mapping",
                                    html.I(
                                        id="sensor-mapping-tooltip",
                                        className="fa-solid fa-circle-question m-0",
                                    ),
                                    Tooltip(
                                        html.H5(
                                            "Only SI units are accepted. Please ensure that your sensor data is in the right units.",
                                            className="m-0 fw-normal",
                                        ),
                                        target="sensor-mapping-tooltip",
                                        className="bg-white text-black rounded-2 border-1 border-info p-2 shadow-sm",
                                    ),
                                ],
                                className="fw-semibold justify-content-center py-3 d-flex align-items-center gap-2",
                            ),
                            html.Div(
                                [
                                    html.Div(
                                        [
                                            Upload(
                                                children=[
                                                    Button(
                                                        id="sensor-mapping-data-upload-button",
                                                        children=[
                                                            html.I(
                                                                className="fa-solid fa-arrow-up-from-bracket"
                                                            ),
                                                            "Upload Sample Sensor Data",
                                                            Spinner(
                                                                id="sensor-mapping-data-upload-button-spinner",
                                                                size="sm",
                                                                spinner_style={
                                                                    "display": "none"
                                                                },
                                                            ),
                                                        ],
                                                        className="bg-primary text-secondary rounded-3 d-flex align-items-center gap-2",
                                                    )
                                                ],
                                                id="sensor-mapping-data-upload",
                                                className="d-inline-flex",
                                            ),
                                            Button(
                                                id="sensor-mapping-configure-modal-remove-button",
                                                children=[
                                                    html.I(
                                                        className="fa-solid fa-trash-can me-2"
                                                    ),
                                                    "Remove Mappings",
                                                ],
                                                className="bg-secondary text-primary rounded-3",
                                            ),
                                        ],
                                        className="d-flex gap-2",
                                    ),
                                    column_mapping_table(
                                        id="sensor-mapping-config-table",
                                        title="Sensor Mapping",
                                    ),
                                ],
                                className="px-2",
                            ),
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="sensor-mapping-configure-modal-cancel-button",
                                children=[
                                    html.I(className="fa-solid fa-xmark me-2"),
                                    "Cancel",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="sensor-mapping-configure-modal-save-button",
                                children=[
                                    html.I(className="fa-solid fa-check"),
                                    "Save",
                                    Spinner(
                                        id="sensor-mapping-configure-modal-save-button-spinner",
                                        size="sm",
                                        spinner_style={"display": "none"},
                                    ),
                                ],
                                className="bg-primary text-secondary rounded-3 d-flex align-items-center gap-2",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
                size="xl",
            ),
            # Store is used for storing the project/config name
            dcc.Store(
                id="sensor-mapping-project-store",
                data={"project_name": decoded_project_name},
            ),
            dcc.Store(id="sensors-store", data={"project_name": decoded_project_name}),
            dcc.Store(id="sensor-mapping-variables-store"),
            dcc.Interval(
                id="sensor-mapping-status-checker",
                interval=5000,
            ),
            html.Div(id="sensors-toast-container"),
        ]
    )


##################################################################################
# CALLBACKS
##################################################################################
@callback(
    Output("sensor-mapping-configure-modal", "is_open", allow_duplicate=True),
    Output("sensor-mapping-variables-store", "data", allow_duplicate=True),
    Output(
        PaginatedTableAIO.ids.table("sensor-mapping-config-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("sensors-toast-container", "children", allow_duplicate=True),
    Output("sensor-mapping-data-upload", "contents", allow_duplicate=True),
    Input("sensor-mapping-configure-button", "n_clicks"),
    State("auth-store", "data"),
    State("sensor-mapping-project-store", "data"),
    State("sensors-toast-container", "children"),
    prevent_initial_call=True,
)
def open_sensor_mapping_modal(
    n_clicks: int,
    user_store: Dict[str, Any],
    project_store: Dict[str, Any],
    toast_children: list,
):
    """Show sensors section"""

    if not n_clicks:
        raise PreventUpdate

    toast_manager = ToastManager(toast_children)

    # Get sensor mapping data
    project_name = project_store.get("project_name")
    if not project_name:
        raise KeyError(f"Missing plant configuration name.")
    headers = User.get_headers_from_store(user_store)

    # Get plant config variable data
    response, success = api.get(
        f"/plant-configurations/{project_name}", headers=headers
    )
    if not success:
        print(
            f"Failed to get plant configuration {project_name}: {response['error']}",
            flush=True,
        )
        toast = toast_manager.make_toast(
            [f"Failed to get plant configuration {project_name}: {response['error']}"],
            "warning",
        )
        toast_manager.add_toast(toast)
        return (
            True,
            {},
            [],
            toast_manager.toasts,
            None,
        )

    data = response["data"]
    variables_store = {}
    for entity in data["equipments"]:
        for var in entity["setpoints"] + entity["conditions"]:
            variables_store[var["uuid_str"]] = (
                f"{entity['equipmentId']} - {var['title']}"
            )

    for entity in data["connections"]:
        for var in entity["setpoints"] + entity["conditions"]:
            variables_store[var["uuid_str"]] = (
                f"{entity['upstreamEquipment']}->{entity['downstreamEquipment']} - {var['title']}"
            )
    print(f"Variables store: {variables_store}", flush=True)

    # Get existing sensor mapping
    response, success = api.get(
        f"/plant-configurations/{project_name}/sensors",
        headers=headers,
    )
    if not success:
        print(
            f"Failed to get sensor mapping for {project_name}: {response['error']}",
            flush=True,
        )
        toast = toast_manager.make_toast(
            [f"Failed to get sensor mapping for {project_name}: {response['error']}"],
            "warning",
        )
        toast_manager.add_toast(toast)
        return (
            True,
            variables_store,
            [],
            toast_manager.toasts,
            None,
        )

    data = response["data"]
    column_mapping = data["csvCol2varUUID"]
    timestep_column = data["timestep_col"]
    timeset_column = data["timeset_col"]

    row_data = []
    if timeset_column:
        row_data.append(
            {
                "id": UUIDService.generate_id(),
                "var_id": None,
                "column_name": timeset_column,
                "var_options": None,
                "is_timestep": False,
                "is_timeset": True,
            }
        )

    if timestep_column:
        row_data.append(
            {
                "id": UUIDService.generate_id(),
                "var_id": None,
                "column_name": timestep_column,
                "var_options": None,
                "is_timestep": True,
                "is_timeset": False,
            }
        )

    row_data += [
        {
            "id": UUIDService.generate_id(),
            "var_id": uuid,
            "column_name": column_name,
            "var_options": None,
            "is_timestep": False,
            "is_timeset": False,
        }
        for column_name, uuid in column_mapping.items()
    ]

    # Update variable options for each row
    row_data = update_variable_options(row_data, variables_store)

    return True, variables_store, row_data, toast_manager.toasts, None


@callback(
    Output("sensor-mapping-configure-modal", "is_open", allow_duplicate=True),
    Input("sensor-mapping-configure-modal-cancel-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_sensor_mapping_modal(n_clicks: int):
    """Closes Sensor Mapping modal"""
    if n_clicks:
        return False
    return no_update


@callback(
    Output(PaginatedTableAIO.ids.table("sensor-mapping-config-table"), "rowData"),
    Output("sensor-mapping-data-upload", "contents"),
    Output("sensors-toast-container", "children", allow_duplicate=True),
    Input("sensor-mapping-data-upload", "contents"),
    State("sensor-mapping-variables-store", "data"),
    State("sensors-toast-container", "children"),
    prevent_initial_call=True,
    running=[
        (Output("sensor-mapping-data-upload-button", "disabled"), True, False),
        (
            Output("sensor-mapping-data-upload-button-spinner", "spinner_style"),
            {"display": "inline-block"},
            {"display": "none"},
        ),
    ],
)
def process_uploaded_sensor_data(
    contents: str,
    variables_store: dict,
    toast_children: list,
):
    """Validates CSV file containing sensor data

    Ensures that the file uploaded is in CSV format.
    Populates Sensor Mapping Config table for user to specify column to variable mapping.
    """

    if not contents:
        raise PreventUpdate

    # Decode data
    content_type, content_string = contents.split(",")
    decoded = base64.b64decode(content_string)
    toast_manager = ToastManager(toast_children)

    # Validate that file is csv using pandas
    try:
        df = pd.read_csv(io.StringIO(decoded.decode("utf-8")))
    except Exception as e:
        print(f"Please upload file in CSV format | {e}", flush=True)
        toast = toast_manager.make_toast(
            ["Please upload file in CSV format."],
            "warning",
        )
        toast_manager.add_toast(toast)
        return [], None, toast_manager.toasts

    # Parse headers and populate table
    columns = list(df.columns)
    var_options = [
        {
            "label": var_name,
            "value": uuid,
        }
        for uuid, var_name in variables_store.items()
    ]
    print(f"Columns parsed = {columns}", flush=True)
    print(f"Variable options = {var_options}", flush=True)

    row_data = [
        {
            "id": UUIDService.generate_id(),
            "var_id": None,
            "column_name": column,
            "var_options": var_options,
            "is_timestep": False,
            "is_timeset": False,
        }
        for column in columns
    ]
    return row_data, no_update, no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("sensor-mapping-config-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Input(
        PaginatedTableAIO.ids.table("sensor-mapping-config-table"), "cellValueChanged"
    ),
    State(PaginatedTableAIO.ids.table("sensor-mapping-config-table"), "rowData"),
    State("sensor-mapping-variables-store", "data"),
    prevent_initial_call=True,
)
def validate_column_mapping_table(
    cell_changed: List[Dict[str, Any]],
    row_data: list,
    variables_store: dict,
):
    """Validates table when cell values change.
    This ensures that each variable can only be mapped to one CSV column.
    """
    updated_col = cell_changed[0]["colId"]
    updated_row_index = cell_changed[0]["rowIndex"]

    # When user maps column to a new variable
    if updated_col == "var_id":
        return validate_column_mapping_on_var_id_change(
            updated_row_index=updated_row_index,
            row_data=row_data,
            variables_store=variables_store,
        )

    # When user selects a column as timestep
    if updated_col == "is_timestep":
        return validate_column_mapping_on_timestep_change(
            updated_row_index=updated_row_index,
            row_data=row_data,
            variables_store=variables_store,
        )

    # When user selects a column as timeset
    if updated_col == "is_timeset":
        return validate_column_mapping_on_timeset_change(
            updated_row_index=updated_row_index,
            row_data=row_data,
            variables_store=variables_store,
        )

    return no_update


@callback(
    Output("sensor-mapping-status", "children"),
    Input("sensor-mapping-status-checker", "n_intervals"),
    State("auth-store", "data"),
    State("sensor-mapping-project-store", "data"),
)
def check_sensor_mapping_status(_: int, user_store: dict, project_store: dict):
    """Updates sensor mapping status

    Checks the BE for sensor mapping status every interval of 5s.
    Displays the corresponding status if there is one, on BE error, display error fetching status.
    """
    headers = User.get_headers_from_store(user_store)
    project_name = project_store["project_name"]

    response, success = api.get(
        f"/plant-configurations/{project_name}/sensors",
        None,
        headers,
    )
    if not success:
        return StatusTagGenerator.generate_status_tag(
            status=StatusTagGenerator.ERROR.value
        )

    # If sensor mapping exists, return COMPLETED status. Else return INCOMPLETE.
    status = (
        StatusTagGenerator.COMPLETED.value
        if response["data"]["csvCol2varUUID"]
        else StatusTagGenerator.INCOMPLETE.value
    )

    return StatusTagGenerator.generate_status_tag(status=status)


@callback(
    Output("sensor-mapping-configure-modal", "is_open", allow_duplicate=True),
    Output("sensors-toast-container", "children", allow_duplicate=True),
    Input("sensor-mapping-configure-modal-save-button", "n_clicks"),
    State("auth-store", "data"),
    State("sensor-mapping-project-store", "data"),
    State(
        PaginatedTableAIO.ids.table("sensor-mapping-config-table"),
        "rowData",
    ),
    State("sensors-toast-container", "children"),
    prevent_initial_call=True,
    running=[
        (
            Output("sensor-mapping-configure-modal-save-button", "disabled"),
            True,
            False,
        ),
        (
            Output(
                "sensor-mapping-configure-modal-save-button-spinner", "spinner_style"
            ),
            {"display": "inline-block"},
            {"display": "none"},
        ),
    ],
)
def save_sensor_mapping(
    n_clicks: int,
    user_store: dict,
    project_store: dict,
    row_data: list,
    toast_children: list,
):
    """Save sensor mapping

    Extract data from table and inputs, format and send to BE.
    """
    headers = User.get_headers_from_store(user_store)
    project_name = project_store["project_name"]
    toast_manager = ToastManager(toast_children)

    errors = []
    print("save pressed!!!", flush=True)

    # Extract, validate, and format data to be sent to BE
    timestep_rows = [row for row in row_data if row["is_timestep"]]
    timestep_column = None if not timestep_rows else timestep_rows[0]["column_name"]

    timeset_rows = [row for row in row_data if row["is_timeset"]]
    timeset_column = None if not timeset_rows else timeset_rows[0]["column_name"]

    # Validate that if timestep is selected, timeset should also be selected (i.e. check XOR returns true)
    if (timeset_column is None) ^ (timestep_column is None):
        errors.append(
            f"Please ensure to define timeset if you have selected timestep, and vice versa."
        )

    column_mapping: dict = {}
    missing_columns: list = []
    for row in row_data:
        if not row["var_id"] and not row["is_timestep"] and not row["is_timeset"]:
            missing_columns.append(row["column_name"])
        # Exclude time related columns from column mapping
        if row["is_timestep"] or row["is_timeset"]:
            continue
        column_mapping[row["column_name"]] = row["var_id"]

    if missing_columns:
        errors.append(
            f"Please ensure all columns are mapped. Missing columns: {missing_columns}"
        )

    if errors:
        toast = toast_manager.make_toast(
            errors,
            "warning",
        )
        toast_manager.add_toast(toast)
        return no_update, toast_manager.toasts

    data = {
        "csvCol2varUUID": column_mapping,
        "timestep_col": timestep_column,
        "timeset_col": timeset_column,
    }

    print("---------------------------------------")
    print("SensorMappingConfig sent to BE = ")
    print(data, flush=True)
    print("---------------------------------------")

    # Send data to BE
    response, success = api.post(
        f"/plant-configurations/{project_name}/sensors",
        data,
        headers,
    )

    if not success:
        print(f"Failed to save sensor mappings | {response['error']}", flush=True)
        toast = toast_manager.make_toast(
            [
                "Server error: Failed to save sensor mappings. Please try again at a later time."
            ],
            "warning",
        )
        toast_manager.add_toast(toast)
        return no_update, toast_manager.toasts

    toast = toast_manager.make_toast(
        ["Sensor mappings saved successfully!"],
        "success",
    )
    toast_manager.add_toast(toast)

    return False, toast_manager.toasts


@callback(
    Output(
        PaginatedTableAIO.ids.table("sensor-mapping-config-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Input("sensor-mapping-configure-modal-remove-button", "n_clicks"),
    prevent_initial_call=True,
)
def clear_sensor_mappings(n_clicks: int):
    """Remove sensor mappings"""
    if not n_clicks:
        raise PreventUpdate

    return []
