from dash import html, callback, Input, Output, State, ctx, no_update
from dash.development.base_component import Component

import uuid
from pprint import pprint
from frontend.components.base_table import BaseTable
from frontend.components.table_block import TableBlock
from typing import Dict, List, Tuple, Any

sensor_type_options = ["Pressure Sensor", "Temperature Sensor"]

column_defs = [
    # columns: id, placement_id, sensor_type, sensor_name, placement_id_options, sensor_type_options
    {
        "headerCheckboxSelection": True,  # Enable header checkbox in this column
        "checkboxSelection": True,  # Enable checkboxes for row selection
        "headerName": "",
        "maxWidth": 40,
        "editable": False,
    },
    {
        "headerName": "Sensor Name",
        "field": "sensor_name",
        "tooltipField": "sensor_name",
    },
    {
        "headerName": "Sensor Placement",
        "field": "placement_id",
        "cellEditor": "agSelectCellEditor",
        "cellEditorParams": {
            "function": "placementIdOptions(params)",
        },
    },
    {
        "headerName": "Sensor Type",
        "field": "sensor_type",
        "cellEditor": "agSelectCellEditor",
        "cellEditorParams": {
            "function": "sensorTypeOptions(params)",
        },
    },
    {
        "headerName": "Sensor Unit",
        "field": "sensor_unit",
        "editable": False,
        "maxWidth": 100,
    },
]


# Main table layout
def sensors_table() -> Component:
    table = BaseTable(id="sensors-table", column_defs=column_defs)
    block = TableBlock("Sensor Mapping", table=table)

    return html.Div([block.render()], className="w-100")


@callback(
    Output("sensors-table", "rowData", allow_duplicate=True),
    Input("sensors-table-add-button", "n_clicks"),
    Input("sensors-table-delete-button", "n_clicks"),
    State("sensors-table", "selectedRows"),
    State("sensors-table", "rowData"),
    State("sensors-store", "data"),
    prevent_initial_call=True,
)
def add_or_delete_sensors(
    add_clicks: int,
    delete_clicks: int,
    selected_rows: List[Dict[str, Any]],
    row_data: List[Dict[str, Any]],
    store: Dict[str, Any],
):
    """Configure add and delete of rows for Sensors table"""
    print()
    print(f"-----------add_or_delete_sensors Callback----------")

    print(store)
    print(row_data)

    triggered_id = ctx.triggered_id
    # User clicked Add
    if triggered_id == "sensors-table-add-button":
        placement_id_list = list(store["sensors"].keys())
        first_placement_id = placement_id_list[0]
        first_label = list(store["sensors"][first_placement_id].keys())[0]
        first_unit = store["sensors"][first_placement_id][first_label]
        sensor_type_options = [_ for _ in store["sensors"][first_placement_id].keys()]

        new_row = {
            "id": str(uuid.uuid4()),
            "placement_id": first_placement_id,
            "sensor_type": first_label,
            "sensor_unit": first_unit,
            "sensor_name": None,
            "placement_id_options": placement_id_list,
            "sensor_type_options": sensor_type_options,
        }

        row_data.append(new_row)
        return row_data

    # User clicked Delete
    elif triggered_id == "sensors-table-delete-button" and selected_rows:
        selected_id = selected_rows[0]["id"]
        row_data = [row for row in row_data if row["id"] != selected_id]
        return row_data

    return no_update


# On change
# 1. Change placement -> Set default type selection, update options
@callback(
    Output("sensors-table", "rowData", allow_duplicate=True),
    Input("sensors-table", "cellValueChanged"),
    State("sensors-store", "data"),
    State("sensors-table", "rowData"),
    prevent_initial_call=True,
)
def update_sensor_type_selection(
    cell_changed: List[Dict[str, Any]],
    store: Dict[str, Any],
    row_data: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    """Update sensor type selection and dropdown options when user changes placement selection"""
    print()
    print(f"-----------update_sensor_type_selection Callback----------")
    print(f"cell_changed = {cell_changed[0]}")

    row_index = cell_changed[0]["rowIndex"]
    updated_col = cell_changed[0]["colId"]
    updated_row = cell_changed[0]["data"]
    placement_id = updated_row["placement_id"]

    print(f"row_data = {row_data}")

    # User selected a new placement. Set sensor type selection to default and update the options available
    if updated_col == "placement_id":
        sensor_type_options = list(store["sensors"][placement_id].keys())
        first_label = sensor_type_options[0]
        first_unit = store["sensors"][placement_id][first_label]

        # Update sensor type selection
        updated_row["sensor_type"] = first_label

        # Update sensor type dropdown options
        updated_row["sensor_type_options"] = sensor_type_options

        # Update units displayed
        updated_row["sensor_unit"] = first_unit

        row_data[row_index] = updated_row
        print(f"updated row_data = {row_data}")

        return row_data

    # User selected a new sensor type, update unit displayed
    elif updated_col == "sensor_type":
        new_sensor_type = updated_row["sensor_type"]
        updated_row["sensor_unit"] = store["sensors"][placement_id][new_sensor_type]

        row_data[row_index] = updated_row
        print(f"updated row_data = {row_data}")

        return row_data

    return no_update
