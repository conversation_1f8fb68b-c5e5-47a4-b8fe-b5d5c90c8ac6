from dash import html, dcc, callback, Input, Output, State, no_update, ctx, Patch
from dash.development.base_component import Component
from dash.exceptions import PreventUpdate
import logging, json

from frontend.utils.api_service import APIServiceProvider
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.base_container import Container
from frontend.components.base_dropdown import Dropdown
from frontend.components.base_graph import Graph
from frontend.utils.graph_functions import (
    generate_horizontal_bar_chart,
    generate_line_chart,
)
from .calibration_results_table import calibration_results_table
from frontend.models.calibration_models import CalibrationResult, CalibrationResultGraph


api = APIServiceProvider().get_api_service()
logger = logging.getLogger()


##################################################################################
# LAYOUT
##################################################################################
def calibration_result_section() -> Component:
    return html.Div(
        id="calibration-result-section",
        children=[
            Container(
                children=[
                    html.H2("Calibration Results", className="my-2 fw-semibold"),
                    html.H3("Summary", className="my-2 fw-bold"),
                    html.H4("", id="calibration-summary-text", className="my-3"),
                    # Results Table
                    html.H3(
                        "",
                        id="calibration-results-table-title",
                        className="my-2 fw-bold",
                    ),
                    html.H4("", id="calibration-results-table-text", className="my-3"),
                    calibration_results_table(id="calibration-results-table"),
                    html.H3("Analysis", className="my-2 fw-bold"),
                    html.H4("", id="calibration-analysis-text", className="my-3"),
                    # Results graphs
                    Dropdown(
                        id="calibration-results-variable-filter",
                        className="m-0 h5",
                        placeholder="Variable",
                        multi=False,
                        clearable=False,
                    ),
                    # Reference: https://plotly.com/python/horizontal-vertical-shapes/
                    html.Div(
                        children=Graph(
                            id="calibration-result-line-chart",
                            className="rounded-4 overflow-hidden",
                        ),
                        className="p-2 my-2",
                    ),
                    html.Div(
                        children=Graph(
                            id="calibration-result-bar-chart",
                            className="rounded-4 overflow-hidden",
                        ),
                        className="p-2 my-2",
                    ),
                ],
            ),
            dcc.Store(id="calibration-result-store"),
        ],
        style={"display": "none"},
    )


##################################################################################
# CALLBACKS
##################################################################################
@callback(
    Output("calibration-summary-text", "children"),
    Output("calibration-analysis-text", "children"),
    Output("calibration-results-table-title", "children"),
    Output("calibration-results-table-text", "children"),
    Output(
        PaginatedTableAIO.ids.table("calibration-results-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("calibration-results-variable-filter", "options", allow_duplicate=True),
    Output("calibration-results-variable-filter", "value", allow_duplicate=True),
    Output("calibration-result-line-chart", "figure", allow_duplicate=True),
    Output("calibration-result-bar-chart", "figure", allow_duplicate=True),
    Output("calibration-result-section", "style", allow_duplicate=True),
    Input("calibration-result-store", "modified_timestamp"),
    State("calibration-result-store", "data"),
    prevent_initial_call=True,
)
def display_calibration_results(ts: int, store: dict):
    """Display calibration results when results are returned and stored in store."""

    # Generate filter options
    bar_chart_data = CalibrationResultGraph.bootstrap(store["bar_chart"]["data"])
    filter_options = sorted(list(set(row.variable for row in bar_chart_data)))
    default_var = filter_options[0]

    # Filter dataframes and generate graph figures
    line_chart, bar_chart = generate_charts_from_store(
        store=store, filter_value=default_var
    )

    return (
        store["summary_text"],
        store["analysis_text"],
        store["table"]["title"],
        store["table"]["summary_text"],
        store["table_data"],
        filter_options,
        default_var,
        line_chart,
        bar_chart,
        {"display": "block"},
    )


@callback(
    Output("calibration-result-line-chart", "figure", allow_duplicate=True),
    Output("calibration-result-bar-chart", "figure", allow_duplicate=True),
    Input("calibration-results-variable-filter", "value"),
    State("calibration-result-store", "data"),
    prevent_initial_call=True,
)
def filter_calibration_results(value: str, store: dict):
    return generate_charts_from_store(store=store, filter_value=value)


def generate_charts_from_store(store: dict, filter_value: str):
    bar_chart_data = CalibrationResultGraph.bootstrap(store["bar_chart"]["data"])
    line_chart_data = CalibrationResultGraph.bootstrap(store["line_chart"]["data"])

    bar_chart_df = CalibrationResultGraph.to_df(bar_chart_data)
    line_chart_df = CalibrationResultGraph.to_df(line_chart_data)

    # Filter by values. If no values given, show all by default.
    bar_chart_df = bar_chart_df[
        bar_chart_df[CalibrationResultGraph.variable_title].str.startswith(filter_value)
    ]
    line_chart_df = line_chart_df[
        line_chart_df[CalibrationResultGraph.variable_title].str.startswith(
            filter_value
        )
    ]

    bar_chart = generate_horizontal_bar_chart(
        df=bar_chart_df,
        x=CalibrationResultGraph.timestep_title,
        y=CalibrationResultGraph.value_title,
        title=store["bar_chart"]["title"],
        x_axis_title=store["bar_chart"]["x_axis_title"],
        y_axis_title=store["bar_chart"]["y_axis_title"],
        orientation="v",
        color=CalibrationResultGraph.variable_title,
    )
    line_chart = generate_line_chart(
        df=line_chart_df,
        x=CalibrationResultGraph.timestep_title,
        y=CalibrationResultGraph.value_title,
        color=CalibrationResultGraph.variable_title,
        title=store["line_chart"]["title"],
        x_axis_title=store["line_chart"]["x_axis_title"],
        y_axis_title=store["line_chart"]["y_axis_title"],
    )

    return line_chart, bar_chart


# [FOR TESTING] Test display of mock calibration result in page load
# @callback(
#     Output("calibration-result-store", "data"),
#     Input("calibration-result-section", "children"),
# )
# def test_w_mock_calibration_results(_):
#     import json

#     with open(
#         "frontend/sections/calibration_result_section/mock_calibration_results.json"
#     ) as f:
#         response = json.load(f)
#         success = True

#     data = response["data"]
#     table_results = CalibrationResult.bootstrap_from_raw_data(data["table"]["data"])
#     data["table_data"] = CalibrationResult.hydrate(table_results)

#     # Store line chart data
#     filter_options = []
#     line_chart_data = []
#     plot1_data = data["chart1"]
#     for entity_name, variables in plot1_data["pri_data"].items():
#         for var_name, points in variables.items():
#             filter_options.append(f"{entity_name} {var_name}")
#             data_to_add = [
#                 CalibrationResultGraph(
#                     entity_name=entity_name,
#                     var_name=var_name,
#                     variable=f"{entity_name} {var_name}",
#                     timestep=i + 1,
#                     value=points[i],
#                 )
#                 for i in range(len(points))
#             ]
#             line_chart_data += data_to_add

#     for entity_name, variables in plot1_data["sec_data"].items():
#         for var_name, points in variables.items():
#             data_to_add = [
#                 CalibrationResultGraph(
#                     entity_name=entity_name,
#                     var_name=var_name,
#                     variable=f"{entity_name} {var_name} simulated",
#                     timestep=i + 1,
#                     value=points[i],
#                 )
#                 for i in range(len(points))
#             ]
#             line_chart_data += data_to_add

#     # Store bar chart data
#     bar_chart_data = []
#     plot2_data = data["chart2"]
#     for entity_name, variables in plot2_data["pri_data"].items():
#         for var_name, points in variables.items():
#             data_to_add = [
#                 CalibrationResultGraph(
#                     entity_name=entity_name,
#                     var_name=var_name,
#                     variable=f"{entity_name} {var_name}",
#                     timestep=i + 1,
#                     value=points[i],
#                 )
#                 for i in range(len(points))
#             ]
#             bar_chart_data += data_to_add

#     data["bar_chart"] = {
#         "data": CalibrationResultGraph.hydrate(bar_chart_data),
#         "title": plot2_data["title"],
#         "x_axis_title": plot2_data["axes"][0],
#         "y_axis_title": plot2_data["axes"][1],
#     }

#     data["line_chart"] = {
#         "data": CalibrationResultGraph.hydrate(line_chart_data),
#         "title": plot1_data["title"],
#         "x_axis_title": plot1_data["axes"][0],
#         "y_axis_title": plot1_data["axes"][1],
#     }

#     print(f"Calibration results store: {data}", flush=True)
#     return data
