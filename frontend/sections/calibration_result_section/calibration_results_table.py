from dash import html

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock


column_defs = [
    # columns: id, entity, condition, value
    {
        "headerName": "Equipment/ Stream",
        "field": "entity_name",
        "sortable": True,
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },
    {
        "headerName": "Conditions",
        "field": "label",
        "sortable": True,
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },
    {
        "headerName": "Model Value",
        "field": "model_value",
    },
    {
        "headerName": "Calibrated Value",
        "field": "calibrated_value",
    },
]

grid_options = {
    "rowSelection": "multiple",
    "suppressRowClickSelection": True,
}


def calibration_results_table(id: str):
    table = PaginatedTableAIO(
        id=id,
        column_defs=column_defs,
        grid_options=grid_options,
        editable=False,
    )
    block = TableBlock("", table=table, has_add_button=False, has_del_button=False)

    return html.Div([block.render()], className="w-100")
