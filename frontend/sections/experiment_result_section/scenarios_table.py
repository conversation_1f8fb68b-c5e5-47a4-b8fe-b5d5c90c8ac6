from dash import html

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock


column_defs = [
    # columns: id, scenario, kpi_value, equipment_spec
    {
        "headerName": "Scenario",
        "field": "scenario",
    },
    {
        "headerName": "KPI Value",
        "field": "kpi_value",
        "sortable": True,
        "valueFormatter": {"function": "d3.format(',.2f')(params.value)"},
    },
]

grid_options = {
    "rowSelection": "single",
}


def scenarios_table(id: str):
    table = PaginatedTableAIO(
        id=id,
        column_defs=column_defs,
        editable=False,
        grid_options=grid_options,
    )
    block = TableBlock("", table=table, has_add_button=False, has_del_button=False)

    return html.Div([block.render()], className="w-50")
