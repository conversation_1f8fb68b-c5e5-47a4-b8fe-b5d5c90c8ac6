from dash import html

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableB<PERSON>


def variable_impact_table(id: str, variable_type: str):
    column_defs = [
        # columns: id, entity, type, variable, weightage
        {
            "headerName": "Equipment/ Stream",
            "field": "entity",
            "cellClass": "overflow-hidden",
            "tooltipField": "entity",
        },
        {
            "headerName": variable_type,
            "field": "variable",
            "cellClass": "overflow-hidden",
            "tooltipField": "variable",
        },
        {
            "headerName": "Unit",
            "field": "unit",
            "maxWidth": "100",
            "tooltipField": "unit",
        },
        {
            "headerName": "Weightage",
            "field": "weightage",
            "cellRenderer": "ProgressBar",
            "cellRendererParams": {"color": "primary"},
            "cellClass": "d-flex align-items-center justify-content-center",
        },
    ]

    table = PaginatedTableAIO(
        id=id,
        column_defs=column_defs,
        sortable=False,
        editable=False,
    )
    block = TableBlock("", table=table, has_add_button=False, has_del_button=False)

    return html.Div([block.render()], className="w-100")
