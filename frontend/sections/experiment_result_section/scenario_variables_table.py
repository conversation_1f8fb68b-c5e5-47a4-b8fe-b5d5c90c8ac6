from dash import html

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock


column_defs = [
    # columns: id, variable, entity, type, value
    {
        "headerName": "Variable",
        "field": "variable",
        "tooltipField": "variable",
        "cellClass": "overflow-hidden",
    },
    {
        "headerName": "Equipment/ Stream",
        "field": "entity",
    },
    {
        "headerName": "Type",
        "field": "type",
    },
    {
        "headerName": "Value",
        "field": "value",
        "valueFormatter": {"function": "d3.format(',.2f')(params.value)"},
    },
    {
        "headerName": "Unit",
        "field": "unit",
    },
]

grid_options = {
    "noRowsOverlayComponent": "ScenarioVariablesOverlay",
    "noRowsOverlayComponentParams": {
        "message": "Select a Scenario to View Variable Details",
        "color": "red",
    },
}


def scenario_variables_table(id: str):
    table = PaginatedTableAIO(
        id=id,
        column_defs=column_defs,
        editable=False,
        grid_options=grid_options,
    )
    block = TableBlock("", table=table, has_add_button=False, has_del_button=False)

    return html.Div([block.render()], className="w-100")
