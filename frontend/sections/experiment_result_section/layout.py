from functools import reduce
from pprint import pprint
from typing import Any, Dict, List
from dash.development.base_component import Component
from dash import (
    html,
    dcc,
    callback,
    Input,
    Output,
    State,
    no_update,
    clientside_callback,
)
import plotly.express as px
import pandas as pd
from collections import defaultdict

from frontend.components.base_container import Container
from frontend.components.base_graph import Graph
from frontend.components.base_dropdown import Dropdown
from frontend.components.paginated_table import PaginatedTableAIO
from .variable_impact_table import variable_impact_table
from frontend.components.top_n_variables_table import top_n_variables_table
from .scenarios_table import scenarios_table
from .scenario_variables_table import scenario_variables_table
from frontend.utils.uuid_service import UUIDService
from frontend.utils.graph_functions import (
    generate_pie_chart,
    generate_parallel_coordinates_plot,
)


def experiment_result_section() -> Component:
    return html.Div(
        [
            Container(
                children=[
                    html.H2("Simulation Report", className="my-2 fw-semibold"),
                    html.H3("Summary", className="my-2 fw-bold"),
                    html.H4("", id="summary-text", className="my-3"),
                    html.H4("", id="top-summary-text", className="my-3"),
                    html.Div(
                        [
                            html.Div(
                                Graph(
                                    id="pie-chart",
                                    style={"height": "400px"},
                                    className="rounded-4 overflow-hidden",
                                ),
                                className="w-100 p-2",
                            ),
                            top_n_variables_table("top-n-variables-table"),
                        ],
                        className="d-flex",
                    ),
                ]
            ),
            Container(
                children=[
                    html.H3("Impact on Variables on KPI", className="my-2 fw-bold"),
                    html.H4("", id="impact-summary-text", className="my-3"),
                    html.Div(
                        [
                            variable_impact_table("setpoints-impact-table", "Setpoint"),
                            variable_impact_table(
                                "conditions-impact-table", "Condition"
                            ),
                        ],
                        className="d-flex",
                    ),
                ]
            ),
            Container(
                children=[
                    html.H3("Graphical Representation", className="my-2 fw-bold"),
                    html.Div(
                        [
                            html.H5("Filters: ", className="px-2 mt-2"),
                            html.Div(
                                [
                                    Dropdown(
                                        id="entity-filter",
                                        className="m-0 h5",
                                        placeholder="Equipment/ Stream",
                                        multi=True,
                                    ),
                                    Dropdown(
                                        id="variable-filter",
                                        className="m-0 h5",
                                        multi=True,
                                        placeholder="Variables",
                                    ),
                                ],
                                className="d-flex flex-column px-2 w-100 gap-2",
                            ),
                        ],
                        className="d-flex align-items-start my-2",
                    ),
                    html.Div(
                        Graph(
                            id="parallel-coordinates-plot",
                            style={"height": "800px"},
                            className="rounded-4 overflow-hidden",
                        ),
                        className="w-100 p-2 my-2",
                    ),
                ]
            ),
            Container(
                children=[
                    html.H3("Details of Simulated Data", className="my-2 fw-bold"),
                    html.Div(
                        [
                            scenarios_table("scenarios-table"),
                            scenario_variables_table("scenario-variables-table"),
                        ],
                        className="d-flex",
                    ),
                ]
            ),
            dcc.Store(id="experiment-result-store"),
        ],
        id="experiment-result-section",
        style={"display": "none"},
    )


clientside_callback(
    """
    function(style) {
        const target = document.getElementById('experiment-result-section');
        if (target) {
            target.scrollIntoView({ behavior: 'smooth' });
        }
        return null;
    }
    """,
    Input("experiment-result-section", "style"),
)


@callback(
    Output("summary-text", "children"),
    Output("top-summary-text", "children"),
    Output("pie-chart", "figure"),
    Output("top-n-variables-table", "rowData"),
    Output("impact-summary-text", "children"),
    Output(PaginatedTableAIO.ids.table("setpoints-impact-table"), "rowData"),
    Output(PaginatedTableAIO.ids.table("conditions-impact-table"), "rowData"),
    Output("entity-filter", "options"),
    Output("entity-filter", "value"),
    Output("variable-filter", "options"),
    Output("parallel-coordinates-plot", "figure"),
    Output(PaginatedTableAIO.ids.table("scenarios-table"), "rowData"),
    Output(PaginatedTableAIO.ids.table("scenario-variables-table"), "rowData"),
    Output("experiment-result-section", "style", allow_duplicate=True),
    Output(
        PaginatedTableAIO.ids.table("setpoints-impact-table"), "columnSize"
    ),  # Note: These are needed to make the columns autosized after appearing
    Output(PaginatedTableAIO.ids.table("conditions-impact-table"), "columnSize"),
    Output(PaginatedTableAIO.ids.table("scenarios-table"), "columnSize"),
    Output(PaginatedTableAIO.ids.table("scenario-variables-table"), "columnSize"),
    Output("top-n-variables-table", "columnSize"),
    Input("experiment-result-store", "modified_timestamp"),
    State("experiment-result-store", "data"),
    prevent_initial_call=True,
)
def display_experiment_results(ts, store):
    """Display results section
    Reset parallel coordinates plot filter"""

    print("experiment-result-store")
    pprint(store)

    # Generate pie chart
    pie_chart_data = store["top_impact"]
    pie_chart_df = pd.DataFrame(
        list(pie_chart_data.items()), columns=["variable", "weightage"]
    )
    pie_chart = generate_pie_chart(
        pie_chart_df, "variable", "weightage", f"Top {len(pie_chart_df)-1} Variable(s)"
    )

    # Generate top N variables table
    top_n_variables_row_data = [
        {
            "id": UUIDService.generate_id(),
            "entity": var["entity"],
            "type": var["type"],
            "variable": var["name"],
            "value": round(var["value"], 2),
            "unit": var["unit"],
        }
        for var in store["top_variables"]
    ]

    # Generate setpoints impact table
    setpoints_impact_row_data = [
        {
            "id": UUIDService.generate_id(),
            "entity": var["entity"],
            "type": "Setpoint",
            "variable": var["setpoint"],
            "weightage": round(var["weightage"], 2),
            "unit": var["unit"],
        }
        for var in store["setpoint_impact_summary"]
    ]

    # Generate conditions impact table
    conditions_impact_row_data = [
        {
            "id": UUIDService.generate_id(),
            "entity": var["entity"],
            "type": "Condition",
            "variable": var["condition"],
            "weightage": round(var["weightage"], 2),
            "unit": var["unit"],
        }
        for var in store["condition_impact_summary"]
    ]

    # Generate scenarios row data
    scenarios_row_data = []
    for scenario, vars in store["simulated_summary"].items():
        new_scenario = {
            "id": UUIDService.generate_id(),
            "scenario": scenario,
            "kpi_value": vars[0]["kpi_value"],
        }
        scenarios_row_data.append(new_scenario)

    # Generate entity filter
    entity_filter = list(store["entity_variable_mapping"].keys())
    default_entity = []

    # Generate variable filter
    variable_filter = generate_variable_filter_options(default_entity, store)

    # Generate parallel coordinates plot
    df, columns = generate_parallel_coordinates_data([], [], store)
    labels = generate_labels_with_unit(store, "variable", "unit")
    parallel_coordinates_plot = generate_parallel_coordinates_plot(
        df, "kpi_value", columns, labels
    )

    return (
        store["main_summary_text"],
        store["top_summary_text"],
        pie_chart,
        top_n_variables_row_data,
        store["impact_summary_text"],
        setpoints_impact_row_data,
        conditions_impact_row_data,
        entity_filter,
        default_entity,
        variable_filter,
        parallel_coordinates_plot,
        scenarios_row_data,
        [],
        {"display": "block"},
        "sizeToFit",
        "sizeToFit",
        "sizeToFit",
        "sizeToFit",
        "sizeToFit",
    )


@callback(
    Output("parallel-coordinates-plot", "figure", allow_duplicate=True),
    Input("variable-filter", "value"),
    State("entity-filter", "value"),
    State("experiment-result-store", "data"),
    prevent_initial_call=True,
)
def filter_parallel_coordinates_plot_by_variable(
    selected_variables, selected_entity, store
):
    """Apply filter on parallel coordinates plot by variable"""

    df, columns = generate_parallel_coordinates_data(
        selected_variables, selected_entity, store
    )
    labels = generate_labels_with_unit(store, "variable", "unit")
    parallel_coordinates_plot = generate_parallel_coordinates_plot(
        df, "kpi_value", columns, labels
    )

    return parallel_coordinates_plot


@callback(
    Output("parallel-coordinates-plot", "figure", allow_duplicate=True),
    Output("variable-filter", "value", allow_duplicate=True),
    Output("variable-filter", "options", allow_duplicate=True),
    Input("entity-filter", "value"),
    State("experiment-result-store", "data"),
    prevent_initial_call=True,
)
def filter_parallel_coordinates_plot_by_entity(selected_entity, store):
    """Apply filter on parallel coordinates plot by entity
    Every time user selects an entity, variable filter is cleared and its options are updated
    """

    df, columns = generate_parallel_coordinates_data([], selected_entity, store)
    labels = generate_labels_with_unit(store, "variable", "unit")
    parallel_coordinates_plot = generate_parallel_coordinates_plot(
        df, "kpi_value", columns, {"kpi_value": "KPI Value"}
    )

    variable_filter_options = generate_variable_filter_options(selected_entity, store)

    return parallel_coordinates_plot, [], variable_filter_options


def generate_parallel_coordinates_data(selected_variables, selected_entity, store):
    df = pd.DataFrame(
        sum(store["simulated_summary"].values(), [])
    )  # Join all scenarios together
    print(f"df = {df.to_dict('records')}")
    print(f"selected entities = {selected_entity}")
    print(f"selected vars = {selected_variables}")

    if selected_entity:
        df = df[df["entity"].isin(selected_entity)]

    # Filter by variables. No variables passed means no variables are filtered.
    if selected_variables:
        df = df[df["variable"].isin(selected_variables)]

    pivot_df = df.pivot(
        index=["scenario", "kpi_value"],
        columns="variable",
        values="value",
    )

    columns = list(pivot_df.columns)
    pivot_df = pivot_df.reset_index()
    return pivot_df, columns


def generate_variable_filter_options(
    selected_entity: List[str],
    store: Dict[str, Any],
) -> List[str]:
    variable_filter_options = []
    if selected_entity:
        for entity in selected_entity:
            variable_filter_options.extend(store["entity_variable_mapping"][entity])
    else:
        variable_filter_options = reduce(
            lambda x, y: x + y, list(store["entity_variable_mapping"].values()), []
        )

    return variable_filter_options


def generate_labels_with_unit(store: Dict[str, Any], variable_col: str, unit_col: str):
    mapping = {}
    for var in list(store["simulated_summary"].values())[0]:
        mapping[var[variable_col]] = f"{var[variable_col]} ({var[unit_col]})"
    labels = {**{"kpi_value": "KPI Value"}, **mapping}
    return labels


@callback(
    Output(
        PaginatedTableAIO.ids.table("scenario-variables-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Input(PaginatedTableAIO.ids.table("scenarios-table"), "selectedRows"),
    State("experiment-result-store", "data"),
    prevent_initial_call=True,
)
def display_entity_spec_for_scenario(selected_rows, store):
    """Display the entity specifications when user selects a row in the Scenarios table"""

    print()
    print(f"-----------display_entity_spec_for_scenario Callback----------")

    if not selected_rows:
        return no_update

    selected_scenario = selected_rows[0]["scenario"]
    row_data = store["simulated_summary"][selected_scenario]
    for row in row_data:
        row["id"] = UUIDService.generate_id()

    return row_data


# TODO: [REMOVE] Test display experiment result
# @callback(
#     Output("experiment-result-store", "data"),
#     Input("experiment-result-section", "children"),
# )
# def test(_):
#     import json

#     with open(
#         "frontend/sections/experiment_setup_section/mock_experiment_results.json"
#     ) as f:
#         response = json.load(f)
#         success = True

#     data = response["data"]["data"]

#     # Transform data into format desired
#     simulated_summary, entity_variable_mapping = {}, defaultdict(set)
#     for scenario in data["simulated_summary"]["simulated_data"]:
#         simulated_summary[scenario["scenario"]] = []

#         for entity in scenario["entity_specification"]:
#             for var in entity["variables"]:
#                 entity_variable_mapping[entity["entity"]].add(
#                     generate_var_label(entity["entity"], var["name"])
#                 )
#                 simulated_summary[scenario["scenario"]].append(
#                     {
#                         "entity": entity["entity"],
#                         "variable": generate_var_label(entity["entity"], var["name"]),
#                         "type": var["type"],
#                         "value": var["value"],
#                         "unit": var["unit"],
#                         "kpi_value": scenario["kpi_value"],
#                         "scenario": scenario["scenario"],
#                     }
#                 )
#     entity_variable_mapping = {k: list(v) for k, v in entity_variable_mapping.items()}

#     data["simulated_summary"] = simulated_summary
#     data["entity_variable_mapping"] = entity_variable_mapping

#     return data


# def generate_var_label(entity: str, var: str) -> str:
#     return f"{entity} - {var}"
