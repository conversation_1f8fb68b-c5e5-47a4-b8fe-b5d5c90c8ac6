from abc import ABC, abstractmethod
from dash.development.base_component import Component
from typing import Any, Dict, List, Union, Type
import pandas as pd

from frontend.components.base_label import Label
from frontend.components.base_dropdown import Dropdown
from frontend.components.base_input import InputField
from frontend.utils.enums import ReactionType, ReactionPhase


class BaseReactionConfig(ABC):
    """Strategy interface for managing advanced config components for different reaction types

    Methods
    -------
    get_component_ids()
        Returns a list of IDs for all input components.

    generate_components(mapping: Dict[str, Any])
        Creates components to be displayed.

    get_missing_values(component_representations: List[Dict[str, Any]])
        Returns a list of IDs for components without values.

    get_values_mapping(component_representations: List[Dict[str, Any]])
        Extract values in components and return a dictionary in the form of {id: value, ...}.
    """

    @abstractmethod
    def get_component_ids(self) -> List[str]:
        """Returns list of input component IDs.

        The IDs should be snake case

        Raises
        ------
        NotImplementedError
            Must be implemented by any subclasses.
        """
        raise NotImplementedError

    @abstractmethod
    def generate_components(self, mapping: Dict[str, Any]) -> List[Component]:
        """Creates components used for advanced reaction configuration.

        Parameters
        ----------
        mapping : Dict
            A dictionary with keys being the IDs of the input component and values being their respective values

        Raises
        ------
        NotImplementedError
            Must be implemented by any subclasses.
        """

        raise NotImplementedError

    def get_missing_values(
        self, component_representations: List[Dict[str, Any]]
    ) -> List[str]:
        """Returns a list of IDs for components without values.

        Parameters
        ----------
        component_representations : List[Dict[str, Any]]
            This is a representation of components returned from accessing the children property of the parent container
            Each component is represented as:
            {
                "namespace": str,  # e.g. dash_bootstrap_components
                "props": Dict,  # id and value live here
                "type": str  # e.g. Input, Dropdown
            }
        """

        advanced_selections_df: pd.DataFrame = pd.DataFrame(component_representations)
        ## Find all inputs that have no value / invalid values
        missing_selections_df: pd.DataFrame = advanced_selections_df[
            (
                advanced_selections_df["props"]
                .apply(lambda props: props.get("id", None))
                .isin(self.get_component_ids())
            )
            & (
                advanced_selections_df["props"].apply(
                    lambda props: props.get("value", None) is None
                )
            )
        ]

        return list(missing_selections_df["props"].apply(lambda props: props["id"]))

    def get_values_mapping(
        self, component_representations: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Extract values in components and returns a dictionary in the form of {id: value, ...}.

        Parameters
        ----------
        component_representations: List[Dict[str, Any]]
            This is a representation of components returned from accessing the children property of the parent container
        """

        advanced_selections_df: pd.DataFrame = pd.DataFrame(component_representations)
        filtered_selections_df: pd.DataFrame = advanced_selections_df[
            advanced_selections_df["props"]
            .apply(lambda props: props.get("id", None))
            .isin(self.get_component_ids())
        ].copy()
        filtered_selections_df["id"] = filtered_selections_df["props"].apply(
            lambda props: props.get("id", None)
        )
        filtered_selections_df["value"] = filtered_selections_df["props"].apply(
            lambda props: props.get("value", None)
        )

        return {
            record["id"]: str(record["value"])
            for record in filtered_selections_df.to_dict("records")
        }  # Convert all advanced selection values to string format


class ConversionReactionConfig(BaseReactionConfig):
    """Creates input components for Conversion reaction type configuration and extracts their values."""

    def __init__(self):
        self.reaction_phases: List[Dict[str, str]] = [
            {"label": ReactionPhase.Vapor.name, "value": ReactionPhase.Vapor.value},
            {"label": ReactionPhase.Liquid.name, "value": ReactionPhase.Liquid.value},
            {"label": ReactionPhase.Mixture.name, "value": ReactionPhase.Mixture.value},
        ]

    def get_component_ids(self) -> List[str]:
        return ["reaction_phase", "conversion_expression"]

    def generate_components(self, mapping: Dict[str, Any]) -> List[Component]:
        return [
            Label("Phase", className="h5 fw-medium text-dark"),
            Dropdown(
                id="reaction_phase",
                options=self.reaction_phases,
                clearable=False,
                value=mapping.get("reaction_phase", None),
            ),
            Label(
                "Conversion Fraction (up to 5 decimal places)",
                className="h5 fw-medium text-dark",
            ),
            InputField(
                id="conversion_expression",
                type="number",
                min=0,
                max=1,
                step=0.00001,
                value=mapping.get("conversion_expression", None),
            ),
        ]


class ReactionConfigContext:
    """Context for accessing the right reaction config class"""

    def __init__(self, reaction_type: str):
        self.mapping: Dict[str, Type[BaseReactionConfig]] = {
            ReactionType.Conversion.value: ConversionReactionConfig
        }
        self.strategy: BaseReactionConfig = self._get_strategy(reaction_type)

    def _get_strategy(self, reaction_type: str) -> BaseReactionConfig:
        reaction_config_class: Union[Type[BaseReactionConfig], None] = self.mapping.get(
            reaction_type, None
        )
        if reaction_config_class is None:
            raise ValueError(f"Unknown reaction type: {reaction_type}")

        return reaction_config_class()

    def generate_components(self, mapping: Dict[str, Any]) -> List[Component]:
        return self.strategy.generate_components(mapping)

    def get_missing_values(
        self, component_representations: List[Dict[str, Any]]
    ) -> List[str]:
        return self.strategy.get_missing_values(component_representations)

    def get_values_mapping(
        self, component_representations: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        return self.strategy.get_values_mapping(component_representations)

    def get_component_ids(self) -> List[str]:
        return self.strategy.get_component_ids()
