from dash import html, callback, Input, Output, State, ctx, no_update
import dash_ag_grid as dag
from matplotlib.pyplot import grid
import pandas as pd
import uuid
from pprint import pprint
import copy

from frontend.components.base_label import Label
from frontend.components.table_block import TableBlock
from frontend.components.base_table import BaseTable
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.base_button import Button
from frontend.components.base_modal import Modal
from frontend.components.base_modal_body import ModalBody
from frontend.components.base_modal_footer import ModalFooter
from frontend.components.base_modal_header import ModalHeader
from frontend.components.base_label import Label
from frontend.components.base_dropdown import Dropdown
from frontend.components.base_tooltip import Tooltip
from frontend.utils.enums import Reactor, EquipmentType

from typing import Dict, List, Any, Set, Tuple, Union


column_defs = [
    {
        "headerCheckboxSelection": True,  # Enable header checkbox in this column
        "checkboxSelection": True,  # Enable checkboxes for row selection
        "headerName": "",
        "maxWidth": 40,
        "editable": False,
    },
    {
        "headerName": "Equipment Name",
        "field": "equipment_name",
    },  # Input
    {
        "headerName": "Equipment Type",
        "field": "equipment_type",
        "cellEditor": "agSelectCellEditor",
        "cellEditorParams": {
            "values": [],
        },
        # "cellRenderer": "agSelectCellEditor",
        # "cellRendererParams": {
        #     "values": equipment_types,
        # },
        # "singleClickEdit": True,
    },  # Selection
    {
        "headerName": "",
        "field": "advanced_settings_menu",
        "cellRenderer": "Button",
        "cellRendererParams": {
            "color": "primary",
            "iconClass": "fa-solid fa-gear",
            "buttonClass": "btn-sm text-light bg-transparent border-0",
            "text": "",
        },
        "cellClass": "d-flex align-items-center justify-content-center",
        "maxWidth": 40,
        "editable": False,
    },
]

grid_options = {
    "suppressMovableColumns": True,
    # "singleClickEdit": True,
    "rowSelection": "multiple",
    "suppressRowClickSelection": True,
}

selections_column_defs = [
    {
        "headerName": "Title",
        "field": "title",
        "editable": False,
        "sort": "asc",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },  # Input
    {
        "headerName": "Selection",
        "field": "value",
        "cellEditor": "agSelectCellEditor",
        "cellEditorParams": {
            "function": "discreteSelectionOptions(params)",
        },
        "editable": {"function": "params.data.disabled == false"},
    },  # Dropdown
]

setpoints_column_defs = [
    {
        "headerName": "Title",
        "field": "title",
        "editable": False,
        "cellClass": "overflow-hidden",
        "tooltipField": "title",
        "sort": "asc",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },  # Input
    {
        "headerName": "Lower Bound",
        "field": "lower_bound",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
    {
        "headerName": "Setpoint",
        "field": "value",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
    {
        "headerName": "Upper Bound",
        "field": "upper_bound",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
]

conditions_column_defs = [
    {
        "headerName": "Title",
        "field": "title",
        "editable": False,
        "cellClass": "overflow-hidden",
        "tooltipField": "title",
        "sort": "asc",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },  # Input
    {
        "headerName": "Lower Bound",
        "field": "lower_bound",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
    {
        "headerName": "Condition",
        "field": "value",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
    {
        "headerName": "Upper Bound",
        "field": "upper_bound",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
]


def equipment_table():
    table = PaginatedTableAIO(
        id="equipment-table", column_defs=column_defs, grid_options=grid_options
    )
    block = TableBlock("Equipment", table=table)

    selections_table = BaseTable(
        "selections-table",
        selections_column_defs,
        row_class_rules={"bg-info": "params.data.disabled == true"},
    )
    setpoints_table = BaseTable(
        "setpoints-table",
        setpoints_column_defs,
        row_class_rules={"bg-info": "params.data.disabled == true"},
    )
    conditions_table = BaseTable(
        "conditions-table",
        conditions_column_defs,
        row_class_rules={"bg-info": "params.data.disabled == true"},
    )

    return html.Div(
        [
            html.Div(id="equipment-table-block-load"),
            block.render(),
            Modal(
                id="advanced-settings-1-modal",
                children=[
                    ModalBody(
                        [
                            html.Div(
                                [
                                    html.H2(
                                        "Choose Your Discrete Variables",
                                        className="fw-semibold text-center py-3",
                                    ),
                                    html.I(
                                        id="discrete-variables-tooltip",
                                        className="fa-solid fa-circle-question mb-2",
                                    ),
                                    Tooltip(
                                        html.H5(
                                            "Variables highlighted in gray cannot be edited.",
                                            className="m-0 fw-normal",
                                        ),
                                        target="discrete-variables-tooltip",
                                        className="bg-white text-black rounded-2 border-1 border-info p-2 shadow-sm",
                                    ),
                                ],
                                className="d-flex align-items-center gap-2 justify-content-center",
                            ),
                            selections_table,
                            html.Div(
                                [
                                    Label(
                                        "Reaction Set",
                                        className="h5 fw-medium text-dark",
                                    ),
                                    Dropdown(
                                        id="reaction-set-multi-select",
                                        multi=True,
                                    ),
                                ],
                                id="reaction-set",
                                className="py-2",
                            ),
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="advanced-settings-1-modal-cancel-button",
                                children=[
                                    "Cancel",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="advanced-settings-1-modal-button",
                                children=["Next"],
                                className="bg-primary text-secondary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
            ),
            Modal(
                id="advanced-settings-2-modal",
                size="lg",
                children=[
                    ModalBody(
                        [
                            html.H2(
                                "Define Parameter Values and Constraints",
                                className="fw-semibold text-center py-3",
                            ),
                            html.Div(
                                [
                                    html.H4(
                                        "Setpoints (Process Variables)",
                                        className="fw-semibold text-center m-0",
                                    ),
                                    html.I(
                                        id="setpoint-variables-tooltip",
                                        className="fa-solid fa-circle-question",
                                    ),
                                    Tooltip(
                                        html.H5(
                                            "Variables highlighted in gray cannot be edited.",
                                            className="m-0 fw-normal",
                                        ),
                                        target="setpoint-variables-tooltip",
                                        className="bg-white text-black rounded-2 border-1 border-info p-2 shadow-sm",
                                    ),
                                ],
                                className="d-flex align-items-center gap-2 justify-content-center pb-4",
                            ),
                            setpoints_table,
                            html.Div(
                                [
                                    html.H4(
                                        "Conditions (Equipment Parameters)",
                                        className="fw-semibold text-center m-0",
                                    ),
                                    html.I(
                                        id="condition-variables-tooltip",
                                        className="fa-solid fa-circle-question",
                                    ),
                                    Tooltip(
                                        html.H5(
                                            "Variables highlighted in gray cannot be edited.",
                                            className="m-0 fw-normal",
                                        ),
                                        target="condition-variables-tooltip",
                                        className="bg-white text-black rounded-2 border-1 border-info p-2 shadow-sm",
                                    ),
                                ],
                                className="d-flex align-items-center gap-2 justify-content-center py-4",
                            ),
                            conditions_table,
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="advanced-settings-2-modal-back-button",
                                children=["Back"],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="advanced-settings-2-modal-save-button",
                                children=["Save"],
                                className="bg-primary text-secondary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
            ),
        ],
        className="w-100",
    )


# TODO Somehow this callback cannot be put in table(), maybe have to convert to class
@callback(
    Output(
        PaginatedTableAIO.ids.table("equipment-table"),
        "rowTransaction",
        allow_duplicate=True,
    ),
    Output("graph-store", "data", allow_duplicate=True),
    Input("equipment-table-add-button", "n_clicks"),
    Input("equipment-table-delete-button", "n_clicks"),
    State(PaginatedTableAIO.ids.table("equipment-table"), "selectedRows"),
    State(PaginatedTableAIO.ids.table("equipment-table"), "rowData"),
    State("graph-store", "data"),
    prevent_initial_call=True,
)
def add_or_delete_equipment(add_clicks, delete_clicks, selected_rows, row_data, store):
    """Configure add and delete of rows for equipment table"""
    print()
    print(f"-----------add_or_delete_equipment Callback----------")
    print(f"Current row data: ")
    pprint(row_data)

    triggered_id = ctx.triggered_id
    print(f"triggered_id: {triggered_id}")

    if triggered_id == "equipment-table-add-button":
        default_equipment_type = next(iter(store["equipment_types"]))
        new_row = {
            "id": str(uuid.uuid4()),
            "equipment_name": "",
            "equipment_type": default_equipment_type,
            "equipment_conditions": 100,
            "advanced_settings_menu": "Advanced Settings",
            "selections": {
                title: data["value"]
                for title, data in store["equipment_types"][default_equipment_type][
                    "selections"
                ].items()
            },
            "setpoints": {
                title: (
                    data["bounds"][0],
                    data["value"],
                    data["bounds"][1],
                )
                for title, data in store["equipment_types"][default_equipment_type][
                    "setpoints"
                ].items()
            },
            "conditions": {
                title: (
                    data["bounds"][0],
                    data["value"],
                    data["bounds"][1],
                )
                for title, data in store["equipment_types"][default_equipment_type][
                    "conditions"
                ].items()
            },
        }  # Need a unique ID to use Transaction Updates, see: https://dash.plotly.com/dash-ag-grid/client-side#transaction-updates
        print(f"New rows added: {new_row}")
        store["equipment"][new_row["id"]] = new_row
        # print(f">> Store has been updated: ")
        # pprint(store)
        return {"add": [new_row]}, store

    elif triggered_id == "equipment-table-delete-button" and selected_rows:
        print(f"Rows to delete: {selected_rows}")
        for selected_row in selected_rows:
            store["equipment"].pop(selected_row["id"], None)
        # print(f">> Store has been updated: ")
        # pprint(store)
        return {"remove": selected_rows}, store

    return no_update


@callback(
    Output(
        "graph-store", "data", allow_duplicate=True
    ),  # Choose View refresh instead of row transactions because we need to update dropdown options in all rows
    Output(
        PaginatedTableAIO.ids.table("equipment-table"), "rowData", allow_duplicate=True
    ),
    Input(PaginatedTableAIO.ids.table("equipment-table"), "cellValueChanged"),
    State("graph-store", "data"),
    State(PaginatedTableAIO.ids.table("equipment-table"), "rowData"),
    prevent_initial_call=True,
)
def update_equipment_details(cell_changed, store, row_data):
    """Update the store and Equipment table data when equipment are edited"""
    # Shape of cell_change1d =
    # [{'rowIndex': 1,
    #   'rowId': 'HX-002',
    #   'data': {'id': 'HX-002', 'equipment_name': 'HX-003', 'equipment_type': 'Heat Exchanger'},
    #   'oldValue': 'HX-002',
    #   'value': 'HX-003',
    #   'colId': 'equipment_name',
    #   'timestamp': 1724669489170}]
    print()
    print(f"-----------update_equipment_details Callback----------")

    print(f"cell_changed: {cell_changed}")
    affected_id = cell_changed[0]["data"]["id"]
    updated_col = cell_changed[0]["colId"]
    row = cell_changed[0]["data"]
    row_index = cell_changed[0]["rowIndex"]

    if updated_col == "equipment_name":
        # Revert to previous equipment name if illegal characters found
        if not is_valid_equipment_name(row['equipment_name']): 
            old_name = cell_changed[0]['oldValue']
            row['equipment_name'] = old_name
            row_data[row_index] = row
        store["equipment"][affected_id] = row
        return store, row_data

    elif updated_col == "equipment_type":
        # Reset selections, setpoints and conditions when eqt type is changed
        equipment_type = row["equipment_type"]
        row["selections"] = {
            title: data["value"]
            for title, data in store["equipment_types"][equipment_type][
                "selections"
            ].items()
        }
        row["setpoints"] = {
            title: (
                data["bounds"][0],
                data["value"],
                data["bounds"][1],
            )
            for title, data in store["equipment_types"][equipment_type][
                "setpoints"
            ].items()
        }
        row["conditions"] = {
            title: (
                data["bounds"][0],
                data["value"],
                data["bounds"][1],
            )
            for title, data in store["equipment_types"][equipment_type][
                "conditions"
            ].items()
        }
        row_data[row_index] = row
        store["equipment"][affected_id] = row

        return store, row_data

    return no_update, no_update

def is_valid_equipment_name(name: str) -> bool:
    """Checks if equipment name has any illegal characters"""

    return '_' not in name

##########################################################################################
@callback(
    Output("advanced-settings-1-modal", "is_open", allow_duplicate=True),
    Output("selections-table", "rowData", allow_duplicate=True),
    Output("reaction-set", "style", allow_duplicate=True),
    Output("reaction-set-multi-select", "value", allow_duplicate=True),
    Output("reaction-set-multi-select", "options", allow_duplicate=True),
    Input(PaginatedTableAIO.ids.table("equipment-table"), "cellRendererData"),
    State("graph-store", "data"),
    State(PaginatedTableAIO.ids.table("equipment-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("reactions-table"), "rowData"),
    State("materials-store", "data"),
    prevent_initial_call=True,
)
def open_advanced_settings_1_modal(
    row, store, equipment_row_data, reactions_row_data, materials_store
):  # TODO Settle type annotations
    """Opens the first advanced settings modal for the specified equipment"""
    print()
    print(f"-----------open_advanced_settings_1_modal Callback----------")
    # row: {'colId': 'advanced_settings_menu', 'rowIndex': 0, 'rowId': '0fdaf874-0a78-43fd-bded-7f653d299fb8', 'timestamp': 1725807126121}
    print(f"row: {row}")
    col = row["colId"]
    equipment_row_index = row["rowIndex"]
    equipment_row = equipment_row_data[equipment_row_index]
    equipment_type = equipment_row["equipment_type"]

    equipment_selections = equipment_row["selections"]

    if col == "advanced_settings_menu":
        # id, title, value, options, disabled

        # Dont allow opening of advanced settings if no selections are available. E.g. for Battery in and out
        if not equipment_selections:
            return (no_update, no_update, no_update, no_update, no_update)

        selections_row_data = generate_selections_table_row_data(
            equipment_selections,
            store,
            materials_store,
            equipment_type,
            equipment_row_index,
        )

        reaction_set_style = {"display": "none"}
        reactions, reaction_options = [], []
        if equipment_type in (reactor.value for reactor in Reactor):
            reaction_set_style = {"display": "block"}
            reactions = equipment_row.get("reactions", [])
            reaction_options = [
                {"label": row["name"], "value": row["id"]} for row in reactions_row_data
            ]

        return (
            True,
            selections_row_data,
            reaction_set_style,
            reactions,
            reaction_options,
        )

    return (no_update, no_update, no_update, no_update, no_update)


@callback(
    Output("advanced-settings-1-modal", "is_open", allow_duplicate=True),
    Input("advanced-settings-1-modal-cancel-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_settings_1_modal(n_clicks: int) -> bool:
    if n_clicks:
        return False
    return no_update


@callback(
    Output("selections-table", "rowData"),
    Input("selections-table", "cellValueChanged"),
    State("selections-table", "rowData"),
    State("graph-store", "data"),
    State("materials-store", "data"),
    prevent_initial_call=True,
)
def update_selection_sets(
    cell_changed: List[Dict[str, Any]],
    row_data: List[Dict[str, Any]],
    store: Dict[str, Any],
    materials_store: Dict[str, Any],
):  # TODO Settle type annotations
    """Updates selection set and their disabled states whenever user selects a different option for any selection set"""

    print()
    print(f"-----------update_selection_sets Callback----------")
    print(cell_changed)

    updated_col = cell_changed[0]["colId"]
    print(f"Row data = {row_data}")

    if updated_col == "value":  # Defensive check
        # Update disabled status
        # If disabled, change back to default values
        equipment_type = row_data[0]["equipment_type"]
        equipment_row_index = row_data[0]["equipment_row_index"]
        equipment_selections = {row["title"]: row["value"] for row in row_data}
        row_data = generate_selections_table_row_data(
            equipment_selections,
            store,
            materials_store,
            equipment_type,
            equipment_row_index,
        )
        return row_data
    return no_update


@callback(
    Output("advanced-settings-2-modal", "is_open", allow_duplicate=True),
    Output("advanced-settings-1-modal", "is_open", allow_duplicate=True),
    Output("setpoints-table", "rowData"),
    Output("conditions-table", "rowData"),
    Input("advanced-settings-1-modal-button", "n_clicks"),
    State("graph-store", "data"),
    State("selections-table", "rowData"),
    State("setpoints-table", "rowData"),
    State("conditions-table", "rowData"),
    State(PaginatedTableAIO.ids.table("equipment-table"), "rowData"),
    prevent_initial_call=True,
)
def open_advanced_settings_2_modal(
    n_clicks: int,
    store: Dict[str, Any],
    selections_row_data: List[Dict[str, Any]],
    setpoints_row_data: List[Dict[str, Any]],
    conditions_row_data: List[Dict[str, Any]],
    equipment_row_data: List[Dict[str, Any]],
):  # TODO Settle type annotations
    """Opens the second advanced settings modal for the specified equipment"""

    print()
    print(f"-----------open_advanced_settings_2_modal Callback----------")

    if n_clicks:
        # id, title, lower_bound, value, upper_bound, disabled
        equipment_row_index = selections_row_data[0]["equipment_row_index"]
        equipment_type = selections_row_data[0]["equipment_type"]
        selections = [selection["value"] for selection in selections_row_data]

        # Generate setpoints table
        setpoints = equipment_row_data[equipment_row_index]["setpoints"]
        setpoints_row_data = generate_setpoints_row_data(
            setpoints, store, equipment_type, equipment_row_index, selections
        )

        # Generate conditions table
        conditions = equipment_row_data[equipment_row_index]["conditions"]
        conditions_row_data = generate_conditions_row_data(
            conditions, store, equipment_type, equipment_row_index, selections
        )

        print(f"setpoints = {setpoints_row_data}")
        print(f"conditions = {conditions_row_data}")
        return True, False, setpoints_row_data, conditions_row_data
    return no_update, no_update, no_update, no_update


@callback(
    Output("advanced-settings-2-modal", "is_open"),
    Output("advanced-settings-1-modal", "is_open"),
    Input("advanced-settings-2-modal-back-button", "n_clicks"),
    State("graph-store", "data"),
    prevent_initial_call=True,
)
def close_advanced_settings_2_modal(
    n_clicks: int, store: Dict[str, Any]
):  # TODO Settle type annotations
    """Returns user from the second to the first advanced settings modal for the specified equipment"""
    if n_clicks:
        return False, True
    return no_update, no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("equipment-table"),
        "rowTransaction",
        allow_duplicate=True,
    ),
    Output("graph-store", "data", allow_duplicate=True),
    Output("advanced-settings-2-modal", "is_open", allow_duplicate=True),
    Input("advanced-settings-2-modal-save-button", "n_clicks"),
    State("selections-table", "rowData"),
    State("setpoints-table", "rowData"),
    State("conditions-table", "rowData"),
    State(PaginatedTableAIO.ids.table("equipment-table"), "rowData"),
    State("graph-store", "data"),
    State("reaction-set-multi-select", "value"),
    prevent_initial_call=True,
)
def update_equipment_advanced_settings(
    n_clicks: int,
    selections_row_data: List[Dict[str, Any]],
    setpoints_row_data: List[Dict[str, Any]],
    conditions_row_data: List[Dict[str, Any]],
    equipment_row_data: List[Dict[str, Any]],
    store: Dict[str, Any],
    reactions: List[str],
):
    """Saves new advanced settings to Equipment Table and Store"""
    if n_clicks:
        # Get equipment row and deep copy
        equipment_row_index = selections_row_data[0]["equipment_row_index"]
        new_equipment_row = copy.deepcopy(equipment_row_data[equipment_row_index])

        # Update selections
        new_equipment_row["selections"] = {
            selection_row["title"]: selection_row["value"]
            for selection_row in selections_row_data
        }

        # Update setpoints
        new_equipment_row["setpoints"] = {
            setpoint_row["title"]: (
                setpoint_row["lower_bound"],
                setpoint_row["value"],
                setpoint_row["upper_bound"],
            )
            for setpoint_row in setpoints_row_data
        }

        # Update conditions
        new_equipment_row["conditions"] = {
            condition_row["title"]: (
                condition_row["lower_bound"],
                condition_row["value"],
                condition_row["upper_bound"],
            )
            for condition_row in conditions_row_data
        }

        # Add reactions if equipment type is Reactor
        if new_equipment_row["equipment_type"] in (
            reactor.value for reactor in Reactor
        ):
            new_equipment_row["reactions"] = reactions

        # Update equipment in store
        store["equipment"][new_equipment_row["id"]] = new_equipment_row
        # print("update_equipment_advanced_settings Updated Store = ")
        # pprint(store)

        return {"update": [new_equipment_row]}, store, False
    return no_update, no_update, no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("equipment-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("graph-store", "data", allow_duplicate=True),
    Input("reactions-store", "modified_timestamp"),
    State("graph-store", "data"),
    State("reactions-store", "data"),
    State(
        PaginatedTableAIO.ids.table("equipment-table"),
        "rowData",
    ),
    prevent_initial_call=True,
)
def validate_reaction_sets_on_change(
    ts, graph_store, reactions_store, equipment_row_data
):
    """Validate reactions sets when reactions are added or removed"""

    print()
    print(f"-----------validate_reaction_sets_on_change Callback----------")

    available_reactions = set([reaction["id"] for reaction in reactions_store])
    print(f"available reactions = {available_reactions}")

    for row in equipment_row_data:
        if row["equipment_type"] not in (reactor.value for reactor in Reactor):
            continue
        row["reactions"] = [
            reaction_id
            for reaction_id in row["reactions"]
            if reaction_id in available_reactions
        ]
        graph_store["equipment"][row["id"]] = row

    # print("Graph store after reactions validation = ")
    # pprint(graph_store)

    return equipment_row_data, graph_store


##########################################################################################
def generate_selections_table_row_data(
    equipment_selections: Dict[str, str],
    store: Dict[str, Any],
    materials_store: Dict[str, Any],
    equipment_type: str,
    equipment_row_index: int,
) -> List[Dict[str, Any]]:
    """Generate rowData for Selections Table
    Also updates disabled status based on selection values
    """
    prev_selections_row_data = None
    MAX_RUNS = 10
    run = 0

    while True:
        selections_row_data = []
        for title, value in equipment_selections.items():
            equipment_type_preset = store["equipment_types"][equipment_type][
                "selections"
            ][title]
            options = equipment_type_preset["options"]
            prereq_set: Union[None, Set] = (
                None
                if equipment_type_preset["prerequisites"] is None
                else set(equipment_type_preset["prerequisites"])
            )
            selected_values_set = set(equipment_selections.values())

            disabled = (
                (len(prereq_set & selected_values_set) == 0)
                if prereq_set
                else (prereq_set is None)
            )

            value = (
                value if not disabled else equipment_type_preset["value"]
            )  # Reset to default if prereqs no longer met

            # For shortcut column, users choose from materials they have selected
            if equipment_type == "Shortcut Column" and title in [
                "Light Key Compound",
                "Heavy Key Compound",
            ]:
                options = [
                    mt["material_type"] for mt in materials_store["materials"].values()
                ]
                value = value or None

            row = {
                "id": title,
                "title": title,
                "value": value,
                "options": options,
                "disabled": disabled,  # Check if prerequsities are met and set dropdown to disabled or not
                "equipment_type": equipment_type,
                "equipment_row_index": equipment_row_index,
            }
            selections_row_data.append(row)

        run += 1

        # Compare with previous iteration
        if prev_selections_row_data == selections_row_data:
            break

        if run >= MAX_RUNS:
            print(
                f"Failed to generate selections, max runs of {MAX_RUNS} reached",
                flush=True,
            )
            break

        # Update equipment_selections for next iteration based on new values
        equipment_selections = {
            row["title"]: row["value"] for row in selections_row_data
        }

        # Store current state for next comparison
        prev_selections_row_data = copy.deepcopy(selections_row_data)

    print(f"Loops ran: {run}")
    print(f"selections_row_data: {selections_row_data}")
    return selections_row_data


@callback(
    Output(
        PaginatedTableAIO.ids.table("equipment-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("graph-store", "data", allow_duplicate=True),
    Input("materials-store", "modified_timestamp"),
    State("graph-store", "data"),
    State("materials-store", "data"),
    State(
        PaginatedTableAIO.ids.table("equipment-table"),
        "rowData",
    ),
    prevent_initial_call=True,
)
def validate_heavy_light_key_compounds(
    ts, graph_store, materials_store, equipment_row_data
):
    """Validate heavy and light key compounds in all equipment when materials are added or removed"""

    print()
    print(f"-----------validate_heavy_light_key_compounds Callback----------")

    # print("materials store has changed: ")
    # print(materials_store)

    available_materials = set(
        [mt["material_type"] for mt in materials_store["materials"].values()]
    )
    print(f"available materials = {available_materials}")

    for row in equipment_row_data:
        if row["equipment_type"] != "Shortcut Column":
            continue

        curr_light_key = row["selections"]["Light Key Compound"]
        row["selections"]["Light Key Compound"] = (
            curr_light_key if curr_light_key in available_materials else None
        )
        curr_heavy_key = row["selections"]["Heavy Key Compound"]
        row["selections"]["Heavy Key Compound"] = (
            curr_heavy_key if curr_heavy_key in available_materials else None
        )

        graph_store["equipment"][row["id"]] = row

    # print("Graph store after heavy light compounds validation = ")
    # pprint(graph_store)

    return equipment_row_data, graph_store


def generate_setpoints_row_data(
    setpoints: Dict[str, str],
    store: Dict[str, Any],
    equipment_type: str,
    equipment_row_index: int,
    selections: List[str],
) -> List[Dict[str, Any]]:
    """Generate rowData for Setpoints Table
    Also updates disabled status based on selection values
    """
    setpoints_row_data = []
    for title, value in setpoints.items():
        lower_bound, val, upper_bound = value
        equipment_type_preset = store["equipment_types"][equipment_type]["setpoints"][
            title
        ]
        prereq_set: Union[None, Set] = (
            None
            if equipment_type_preset["prerequisites"] is None
            else set(equipment_type_preset["prerequisites"])
        )
        selected_values_set = set(selections)

        disabled = (
            (len(prereq_set & selected_values_set) == 0)
            if prereq_set
            else (prereq_set is None)
        )

        # Skip showing disabled cont vars
        if disabled:
            continue

        row = {
            "id": title,
            "title": title,
            "lower_bound": (
                lower_bound if not disabled else equipment_type_preset["bounds"][0]
            ),  # Reset to default if prereqs no longer met
            "value": (
                val if not disabled else equipment_type_preset["value"]
            ),  # Reset to default if prereqs no longer met
            "upper_bound": (
                upper_bound if not disabled else equipment_type_preset["bounds"][1]
            ),  # Reset to default if prereqs no longer met
            "disabled": disabled,  # Check if prerequsities are met and set dropdown to disabled or not
            "equipment_type": equipment_type,
            "equipment_row_index": equipment_row_index,
            "unit": equipment_type_preset["unit"],
        }
        setpoints_row_data.append(row)
    print(f"setpoints_row_data: {setpoints_row_data}")
    return setpoints_row_data


def generate_conditions_row_data(
    conditions: Dict[str, str],
    store: Dict[str, Any],
    equipment_type: str,
    equipment_row_index: int,
    selections: List[str],
) -> List[Dict[str, Any]]:
    """Generate rowData for Conditions Table
    Also updates disabled status based on selection values
    """
    conditions_row_data = []
    for title, value in conditions.items():
        lower_bound, val, upper_bound = value
        equipment_type_preset = store["equipment_types"][equipment_type]["conditions"][
            title
        ]
        prereq_set: Union[None, Set] = (
            None
            if equipment_type_preset["prerequisites"] is None
            else set(equipment_type_preset["prerequisites"])
        )
        selected_values_set = set(selections)

        disabled = (
            (len(prereq_set & selected_values_set) == 0)
            if prereq_set
            else (prereq_set is None)
        )

        # Skip showing disabled cont vars
        if disabled:
            continue

        row = {
            "id": title,
            "title": title,
            "lower_bound": (
                lower_bound if not disabled else equipment_type_preset["bounds"][0]
            ),  # Reset to default if prereqs no longer met
            "value": (
                val if not disabled else equipment_type_preset["value"]
            ),  # Reset to default if prereqs no longer met
            "upper_bound": (
                upper_bound if not disabled else equipment_type_preset["bounds"][1]
            ),  # Reset to default if prereqs no longer met
            "disabled": disabled,  # Check if prerequsities are met and set dropdown to disabled or not
            "equipment_type": equipment_type,
            "equipment_row_index": equipment_row_index,
            "unit": equipment_type_preset["unit"],
        }
        conditions_row_data.append(row)
    print(f"conditions_row_data: {conditions_row_data}")
    return conditions_row_data
