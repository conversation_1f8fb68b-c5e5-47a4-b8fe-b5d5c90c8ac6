from dash import html, callback, Input, Output, State, ctx, no_update
import uuid  # Clean up and use service
from pprint import pprint
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.base_table import BaseTable
from frontend.components.table_block import TableBlock
from frontend.components.base_button import Button
from frontend.components.base_modal import Modal
from frontend.components.base_modal_body import ModalBody
from frontend.components.base_modal_footer import ModalFooter
from frontend.components.base_label import Label
from frontend.components.base_dropdown import Dropdown
from frontend.components.base_tooltip import Tooltip
from frontend.utils.enums import StreamType, DummyEquipmentType

from typing import Dict, List, Set, Tuple, Any, Union
import copy
import re

default_option = "Select an option"
disabled_option = "-"


column_defs = [
    {
        "headerCheckboxSelection": True,  # Enable header checkbox in this column
        "checkboxSelection": True,  # Enable checkboxes for row selection
        "headerName": "",
        "maxWidth": 40,
        "editable": False,
    },
    {
        "headerName": "Stream Type",
        "field": "stream_type",
        "cellEditor": "agSelectCellEditor",
        "cellEditorParams": {
            "values": [s.value for s in StreamType],
        },
    },
    # Dropdown
    {
        "headerName": "Main Equipment",
        "field": "main_equipment",
        "cellEditor": "agSelectCellEditor",
        "cellEditorParams": {
            "function": "dynamicMainEquipmentOptions(params)",
        },
        "editable": {"function": f"params.data.main_equipment !== '{disabled_option}'"},
    },
    # Dropdown
    {
        "headerName": "Downstream Equipment",
        "field": "downstream_equipment",
        "cellEditor": "agSelectCellEditor",
        "cellEditorParams": {
            "function": "dynamicDownstreamEquipmentOptions(params)",
        },
        "editable": {
            "function": f"params.data.downstream_equipment !== '{disabled_option}'"
        },
    },
    {
        "headerName": "",
        "field": "advanced_settings_menu",
        "cellRenderer": "OptionalButtonV2",
        "cellRendererParams": {
            "color": "primary",
            "iconClass": "fa-solid fa-gear",
            "buttonClass": "btn-sm text-light bg-transparent border-0",
            "text": "",
        },
        "cellClass": "d-flex align-items-center justify-content-center",
        "maxWidth": 40,
        "editable": False,
    },
]


selections_column_defs = [
    {"headerName": "Title", "field": "title", "editable": False},  # Input
    {
        "headerName": "Selection",
        "field": "value",
        "cellEditor": "agSelectCellEditor",
        "cellEditorParams": {
            "function": "discreteSelectionOptions(params)",
        },
        "editable": {"function": "params.data.disabled == false"},
    },  # Dropdown
]

setpoints_column_defs = [
    {
        "headerName": "Title",
        "field": "title",
        "editable": False,
        "cellClass": "overflow-hidden",
        "tooltipField": "title",
    },  # Input
    {
        "headerName": "Lower Bound",
        "field": "lower_bound",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
    {
        "headerName": "Setpoint",
        "field": "value",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
    {
        "headerName": "Upper Bound",
        "field": "upper_bound",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
]

conditions_column_defs = [
    {
        "headerName": "Title",
        "field": "title",
        "editable": False,
        "cellClass": "overflow-hidden",
        "tooltipField": "title",
    },  # Input
    {
        "headerName": "Lower Bound",
        "field": "lower_bound",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
    {
        "headerName": "Condition",
        "field": "value",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
    {
        "headerName": "Upper Bound",
        "field": "upper_bound",
        "editable": {"function": "params.data.disabled == false"},
        "valueFormatter": {"function": "`${params.value} ${params.data.unit}`"},
    },  # Input
]

grid_options = {"rowSelection": "multiple", "suppressRowClickSelection": True}


# Main table layout
def connections_table():
    table = PaginatedTableAIO(
        id="connections-table", column_defs=column_defs, grid_options=grid_options
    )
    block = TableBlock("Connections", table=table)

    selections_table = BaseTable(
        "stream-selections-table",
        selections_column_defs,
        row_class_rules={"bg-info": "params.data.disabled == true"},
    )
    setpoints_table = BaseTable(
        "stream-setpoints-table",
        setpoints_column_defs,
        row_class_rules={"bg-info": "params.data.disabled == true"},
    )
    conditions_table = BaseTable(
        "stream-conditions-table",
        conditions_column_defs,
        row_class_rules={"bg-info": "params.data.disabled == true"},
    )

    return html.Div(
        [
            block.render(),
            Modal(
                id="stream-settings-1-modal",
                children=[
                    ModalBody(
                        [
                            html.Div(
                                [
                                    html.H2(
                                        "Choose Your Discrete Variables",
                                        className="fw-semibold text-center py-3",
                                    ),
                                    html.I(
                                        id="discrete-stream-variables-tooltip",
                                        className="fa-solid fa-circle-question mb-2",
                                    ),
                                    Tooltip(
                                        html.H5(
                                            "Variables highlighted in gray cannot be edited.",
                                            className="m-0 fw-normal",
                                        ),
                                        target="discrete-stream-variables-tooltip",
                                        className="bg-white text-black rounded-2 border-1 border-info p-2 shadow-sm",
                                    ),
                                ],
                                className="d-flex align-items-center gap-2 justify-content-center",
                            ),
                            selections_table,
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="stream-settings-1-modal-cancel-button",
                                children=[
                                    "Cancel",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="stream-settings-1-modal-button",
                                children=["Next"],
                                className="bg-primary text-secondary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
            ),
            Modal(
                id="stream-settings-2-modal",
                size="lg",
                children=[
                    ModalBody(
                        [
                            html.H2(
                                "Define Parameter Values and Constraints",
                                className="fw-semibold text-center py-3",
                            ),
                            html.Div(
                                [
                                    html.H4(
                                        "Setpoints (Process Variables)",
                                        className="fw-semibold text-center m-0",
                                    ),
                                    html.I(
                                        id="stream-setpoint-variables-tooltip",
                                        className="fa-solid fa-circle-question",
                                    ),
                                    Tooltip(
                                        html.H5(
                                            "Variables highlighted in gray cannot be edited.",
                                            className="m-0 fw-normal",
                                        ),
                                        target="stream-setpoint-variables-tooltip",
                                        className="bg-white text-black rounded-2 border-1 border-info p-2 shadow-sm",
                                    ),
                                ],
                                className="d-flex align-items-center gap-2 justify-content-center pb-4",
                            ),
                            setpoints_table,
                            html.Div(
                                [
                                    html.H4(
                                        "Conditions (Equipment Parameters)",
                                        className="fw-semibold text-center m-0",
                                    ),
                                    html.I(
                                        id="stream-condition-variables-tooltip",
                                        className="fa-solid fa-circle-question",
                                    ),
                                    Tooltip(
                                        html.H5(
                                            "Variables highlighted in gray cannot be edited.",
                                            className="m-0 fw-normal",
                                        ),
                                        target="stream-condition-variables-tooltip",
                                        className="bg-white text-black rounded-2 border-1 border-info p-2 shadow-sm",
                                    ),
                                ],
                                className="d-flex align-items-center gap-2 justify-content-center py-4",
                            ),
                            conditions_table,
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="stream-settings-2-modal-back-button",
                                children=["Back"],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="stream-settings-2-modal-save-button",
                                children=["Save"],
                                className="bg-primary text-secondary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
            ),
        ],
        className="w-100",
    )


@callback(
    Output(
        PaginatedTableAIO.ids.table("connections-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("graph-store", "data", allow_duplicate=True),
    Input("connections-table-add-button", "n_clicks"),
    Input("connections-table-delete-button", "n_clicks"),
    State(PaginatedTableAIO.ids.table("connections-table"), "selectedRows"),
    State(PaginatedTableAIO.ids.table("connections-table"), "rowData"),
    State("graph-store", "data"),
    prevent_initial_call=True,
)
def add_or_delete_connections(
    add_clicks: int,
    delete_clicks: int,
    selected_rows: List[Dict[str, Any]],
    row_data: List[Dict[str, Any]],
    store: Dict[str, Any],
) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """Configure add and delete of rows for connections table"""
    print()
    print(f"-----------Update Transaction Connections Callback----------")
    print(f"Current row data: {row_data}")

    triggered_id = ctx.triggered_id
    print(f"triggered_id: {triggered_id}")
    main_equipment_options = [default_option] + get_equipment_name_options(
        store["equipment"].values()
    )
    downstream_equipment_options = [default_option] + get_equipment_name_options(
        store["equipment"].values()
    )

    # User clicked Add
    if triggered_id == "connections-table-add-button":
        new_row = {
            "id": str(
                uuid.uuid4()
            ),  # Need a unique ID to use Transaction Updates, see: https://dash.plotly.com/dash-ag-grid/client-side#transaction-updates
            "main_equipment_id": None,
            "main_equipment": default_option,
            "main_equipment_options": main_equipment_options,
            "downstream_equipment_id": None,
            "downstream_equipment": default_option,
            "downstream_equipment_options": downstream_equipment_options,
            "stream_type": StreamType.MaterialStream.value,  # Default to Material Stream on creation of new row
            "intermediate_equipment_id": None,  # No recycle
        }
        new_row = reset_to_default_stream_settings(
            store, new_row, StreamType.MaterialStream.value
        )
        print(f"New row added: {new_row}")
        row_data.append(new_row)
        return row_data, no_update

    # User clicked Delete
    elif triggered_id == "connections-table-delete-button" and selected_rows:
        pprint(row_data)
        print(f"Rows to delete: {selected_rows}")
        selected_id_set = set(row["id"] for row in selected_rows)
        # selected_id = selected_rows[0]["id"]
        # selected_row = selected_rows[0]
        for selected_row in selected_rows:
            # Remove connection from store
            selected_row, store = remove_connection_from_store(selected_row, store)
        print(f"Updated connections in Store: {store['connections']}")
        row_data = [row for row in row_data if row["id"] not in selected_id_set]
        return row_data, store
    return no_update, no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("connections-table"),
        "rowData",
        allow_duplicate=True,
    ),  # Using rowData here instead of rowTransaction as we are updating store as well. If we use rowTransaction, store will be updated first and any callbacks listening for store changes will not have updated table rowData
    Output("graph-store", "data", allow_duplicate=True),
    Input("graph-store", "modified_timestamp"),
    State("graph-store", "data"),
    State(PaginatedTableAIO.ids.table("connections-table"), "rowData"),
    prevent_initial_call=True,
)
def refresh_connections_table(
    ts: int, store: Dict[str, Any], row_data: List[Dict[str, Any]]
) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """Refresh equipment dropdown list"""
    print()
    print(f"-----------refresh_connections_table Callback----------")
    print(f"Row data:")
    pprint(row_data)

    updated_equipment_list = get_equipment_name_options(store["equipment"].values())
    updated_mapping = {
        k: row["equipment_name"]
        for k, row in store["equipment"].items()
        if row["equipment_name"] in updated_equipment_list
    }
    existing_equipment_id_list = store["equipment"].keys()

    updated_row_data = []

    for row in row_data:
        main_equipment_id = row["main_equipment_id"]
        downstream_equipment_id = row["downstream_equipment_id"]

        # Remove connection if equipment is no longer present
        if (
            main_equipment_id and main_equipment_id not in existing_equipment_id_list
        ) or (
            downstream_equipment_id
            and downstream_equipment_id not in existing_equipment_id_list
        ):
            row, store = remove_connection_from_store(row, store)
            continue

        # Update equipment names if they are not recycle or battery
        if main_equipment_id and not is_dummy_equipment(main_equipment_id, store):
            row["main_equipment"] = (
                updated_mapping.get(
                    main_equipment_id, None
                )  # There should not be a case where None appears here
                if main_equipment_id
                else default_option
            )
        if downstream_equipment_id and not is_dummy_equipment(
            downstream_equipment_id, store
        ):
            row["downstream_equipment"] = (
                updated_mapping.get(downstream_equipment_id, None)
                if downstream_equipment_id
                else default_option
            )

        # Set options depending on stream type
        row = regenerate_equipment_dropdown_options(row, updated_equipment_list)

        updated_row_data.append(row)

    return updated_row_data, store


@callback(
    Output(
        PaginatedTableAIO.ids.table("connections-table"),
        "rowData",
        allow_duplicate=True,
    ),  # Using rowData here instead of rowTransaction as we are updating store as well. If we use rowTransaction, store will be updated first and any callbacks listening for store changes will not have updated table rowData
    Output("graph-store", "data", allow_duplicate=True),
    Input(PaginatedTableAIO.ids.table("connections-table"), "cellValueChanged"),
    State("graph-store", "data"),
    State(PaginatedTableAIO.ids.table("connections-table"), "rowData"),
    prevent_initial_call=True,
)
def validate_equipment_options(
    cell_changed: List[Dict[str, Any]],
    store: Dict[str, Any],
    row_data: List[Dict[str, Any]],
) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """Callback for when any cell in Connections table is edited.

    Validations for main and downstream equipment are configured here
    Connections in graph store are updated here too
    """
    print()
    print(f"-----------validate_equipment_options Callback----------")
    print(f"cell_changed: {cell_changed}")
    # pprint(store["connections"])
    # pprint(store["equipment"])

    row_index = cell_changed[0]["rowIndex"]
    updated_row = cell_changed[0]["data"]
    updated_col = cell_changed[0]["colId"]
    new_value = cell_changed[0]["value"]
    old_value = cell_changed[0]["oldValue"]
    stream_type = updated_row["stream_type"]
    equipment_list = get_equipment_name_options(store["equipment"].values())
    equipment_dict_by_name = {
        v["equipment_name"]: k
        for k, v in store["equipment"].items()
        if v["equipment_name"] in equipment_list
    }

    # Update equipment dropdown options
    # updated_row = regenerate_equipment_dropdown_options(updated_row, equipment_list)

    # User edited Main Equipment Column
    if updated_col == "main_equipment":
        updated_row["main_equipment_id"] = equipment_dict_by_name.get(
            new_value, None
        )  # None is needed to recognise if default_option is chosen

    # User edited Downstream Equipment column
    elif updated_col == "downstream_equipment":
        updated_row["downstream_equipment_id"] = equipment_dict_by_name.get(
            new_value, None
        )

    # User edited Stream Type column.
    # Remove any existing connections and reset dropdown selections to default
    elif updated_col == "stream_type":
        updated_row["stream_type"] = (
            old_value  # Note: Need to revert stream type before removing connection
        )
        updated_row, store = remove_connection_from_store(updated_row, store)
        updated_row["stream_type"] = (
            new_value  # Note cont'd: And update back to new stream type
        )

        updated_row["main_equipment"] = default_option
        updated_row["main_equipment_id"] = None
        updated_row["downstream_equipment"] = default_option
        updated_row["downstream_equipment_id"] = None

        updated_row["advanced_settings_menu"] = has_stream_settings(
            updated_row["stream_type"]
        )

        # Lower battery limit type modifications
        if stream_type == StreamType.InputStream.value:
            battery_in_prefix = DummyEquipmentType.BatteryInput.value
            battery_in_suffix_list = [
                cn["main_equipment"]
                for cn in row_data[:row_index]
                + row_data[row_index + 1 :]  # skip row being changed
                if cn["stream_type"] == StreamType.InputStream.value
            ]
            main_equipment = generate_unique_name_given_prefix(
                battery_in_prefix, battery_in_suffix_list
            )
            updated_row["main_equipment"] = main_equipment

        # Upper battery limit type modifications
        elif stream_type == StreamType.OutputStream.value:
            battery_out_prefix = DummyEquipmentType.BatteryOutput.value
            battery_out_suffix_list = [
                cn["downstream_equipment"]
                for cn in row_data[:row_index]
                + row_data[row_index + 1 :]  # skip row being changed
                if cn["stream_type"] == StreamType.OutputStream.value
            ]
            downstream_equipment = generate_unique_name_given_prefix(
                battery_out_prefix, battery_out_suffix_list
            )
            updated_row["downstream_equipment"] = downstream_equipment

    # Reset selections, setpoints and conditions when eqt type is changed
    stream_type = updated_row["stream_type"]
    updated_row = reset_to_default_stream_settings(store, updated_row, stream_type)

    # Update equipment dropdown options
    updated_row = regenerate_equipment_dropdown_options(updated_row, equipment_list)

    print(f"Updated row: {updated_row}")

    # Remove connection from store and only add back if stream is fully defined
    updated_row, store = remove_connection_from_store(updated_row, store)
    if (
        updated_row["main_equipment"] != default_option
        and updated_row["downstream_equipment"] != default_option
    ):
        updated_row, store = add_connection_to_store(updated_row, store)

    print(f"Final Updated row = ")
    pprint(updated_row)
    # print(f"Updated connections in store = ")
    # pprint(store["connections"])

    row_data[row_index] = updated_row

    return row_data, store


def remove_connection_from_store(
    connection_row: Dict[str, Any], store: Dict[str, Any]
) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """Takes in a row from connections table and store.
    Removes the connection associated with the row from the store, and updates the row
    """

    updated_row = copy.deepcopy(connection_row)
    updated_store = copy.deepcopy(store)
    stream_type = updated_row["stream_type"]

    # Remove recycle stream
    if updated_row["intermediate_equipment_id"]:
        updated_store["connections"].pop(
            f"{updated_row['intermediate_equipment_id']}-in", None
        )
        updated_store["connections"].pop(
            f"{updated_row['intermediate_equipment_id']}-out", None
        )
        updated_store["equipment"].pop(
            f"{updated_row['intermediate_equipment_id']}", None
        )
        updated_row["intermediate_equipment_id"] = None
        return updated_row, updated_store

    if stream_type == StreamType.InputStream.value:
        updated_store["equipment"].pop(updated_row["main_equipment_id"], None)
        updated_row["main_equipment_id"] = None
    elif stream_type == StreamType.OutputStream.value:
        updated_store["equipment"].pop(updated_row["downstream_equipment_id"], None)
        updated_row["downstream_equipment_id"] = None

    # Remove other streams
    updated_store["connections"].pop(updated_row["id"], None)

    return updated_row, updated_store


def add_connection_to_store(
    connection_row: Dict[str, Any], store: Dict[str, Any]
) -> Tuple[Dict[str, Any], Dict[str, Any]]:
    """Takes in a row from connections table and store.
    Adds the connection associated with the row from the store, and updates the row
    """

    updated_row = copy.deepcopy(connection_row)
    updated_store = copy.deepcopy(store)
    stream_type = updated_row["stream_type"]

    ##### Recycle Stream #####
    # if stream_type == StreamType.RecycleStream.value:
    #     # Create new recycle node and add streams, add to store, and update id in table rowData
    #     recycle_node_id = str(uuid.uuid4())

    #     # Naming of Recycle blocks is as follows:
    #     # All have a prefix of 'Recycle-' and suffix that increments starting from 1 that is padded to 3 digits.
    #     # We take the max suffix (002) and add 1 for the newest Recycle block.
    #     recycle_prefix = DummyEquipmentType.Recycle.value
    #     recycle_suffix_list = [
    #         eq["equipment_name"]
    #         for eq in updated_store["equipment"].values()
    #         if eq["equipment_type"] == DummyEquipmentType.Recycle.value
    #     ]

    #     recycle_node = {  # TODO: Might have to include other columns?
    #         "id": recycle_node_id,
    #         "equipment_name": generate_unique_name_given_prefix(
    #             recycle_prefix, recycle_suffix_list
    #         ),
    #         "equipment_type": DummyEquipmentType.Recycle.value,
    #     }
    #     updated_store["equipment"][recycle_node_id] = recycle_node
    #     recycle_in = {
    #         "main_equipment_id": updated_row["main_equipment_id"],
    #         "downstream_equipment_id": recycle_node_id,
    #         "stream_type": updated_row["stream_type"],
    #     }
    #     updated_store["connections"][f"{recycle_node_id}-in"] = recycle_in
    #     recycle_out = {
    #         "main_equipment_id": recycle_node_id,
    #         "downstream_equipment_id": updated_row["downstream_equipment_id"],
    #         "stream_type": updated_row["stream_type"],
    #     }
    #     updated_store["connections"][f"{recycle_node_id}-out"] = recycle_out
    #     updated_row["intermediate_equipment_id"] = recycle_node_id
    #     return updated_row, updated_store

    if stream_type == StreamType.InputStream.value:
        battery_node_id = str(uuid.uuid4())
        battery_node = {
            "id": battery_node_id,
            "equipment_name": updated_row["main_equipment"],
            "equipment_type": DummyEquipmentType.BatteryInput.value,
        }
        updated_store["equipment"][battery_node_id] = battery_node
        updated_row["main_equipment_id"] = battery_node_id

    elif stream_type == StreamType.OutputStream.value:
        battery_node_id = str(uuid.uuid4())
        battery_node = {
            "id": battery_node_id,
            "equipment_name": updated_row["downstream_equipment"],
            "equipment_type": DummyEquipmentType.BatteryOutput.value,
        }
        updated_store["equipment"][battery_node_id] = battery_node
        updated_row["downstream_equipment_id"] = battery_node_id

    ##### Other stream types #####
    # Add new connection to store
    updated_store["connections"][updated_row["id"]] = {
        "main_equipment_id": updated_row["main_equipment_id"],
        "downstream_equipment_id": updated_row["downstream_equipment_id"],
        "stream_type": updated_row["stream_type"],
    }

    return updated_row, updated_store


def generate_unique_name_given_prefix(prefix: str, name_list: List[str]) -> str:
    # Filter out names without prefix
    pattern = rf"^{prefix}"
    name_list = [name for name in name_list if re.match(pattern, name)]

    # Get highest number count out of names with prefix
    suffix_list = [int(name[len(prefix) :]) for name in name_list]
    suffix = max(suffix_list) + 1 if suffix_list else 1
    return f"{prefix}{suffix:0>3}"


def get_equipment_name_options(equipment_list: List[Dict[str, Any]]) -> List[str]:
    """Given a list of equipment names, filter and return only equipment blocks
    i.e. no recycle or battery limit blocks
    """

    exclusion_regex_list = [
        r"^Recycle-",
        r"^Battery-In-",
        r"^Battery-Out-",
    ]
    compiled_patterns = [re.compile(pattern) for pattern in exclusion_regex_list]

    return [
        eq["equipment_name"]
        for eq in equipment_list
        if not any(
            pattern.search(eq["equipment_name"]) for pattern in compiled_patterns
        )
    ]


def regenerate_equipment_dropdown_options(
    row: Dict[str, Any], equipment_list: List[str]
) -> Dict[str, Any]:
    """Given the a Connections table row and a list of equipment names,
    update the main and downstream equipment options based on stream type.
    """

    updated_row = copy.deepcopy(row)
    stream_type = updated_row["stream_type"]

    if stream_type == StreamType.MaterialStream.value:
        updated_row["downstream_equipment_options"] = [default_option] + [
            e for e in equipment_list if e != updated_row["main_equipment"]
        ]
        updated_row["main_equipment_options"] = [default_option] + [
            e for e in equipment_list if e != updated_row["downstream_equipment"]
        ]
    # elif stream_type == StreamType.RecycleStream.value:
    #     updated_row["downstream_equipment_options"] = [default_option] + [
    #         e for e in equipment_list
    #     ]
    #     updated_row["main_equipment_options"] = [default_option] + [
    #         e for e in equipment_list
    #     ]
    elif stream_type == StreamType.InputStream.value:
        updated_row["downstream_equipment_options"] = [default_option] + [
            e for e in equipment_list
        ]
        updated_row["main_equipment_options"] = [updated_row["main_equipment"]]
    elif stream_type == StreamType.OutputStream.value:
        updated_row["downstream_equipment_options"] = [
            updated_row["downstream_equipment"]
        ]
        updated_row["main_equipment_options"] = [default_option] + [
            e for e in equipment_list
        ]

    return updated_row


def is_dummy_equipment(equipment_id: str, store: Dict[str, Any]) -> bool:
    """Determines if an equipment block is of type Recycle or Battery In/Out based on its ID"""
    return store["equipment"][equipment_id]["equipment_type"] in [
        e.value for e in DummyEquipmentType
    ]


"""
STREAM SETTINGS 
"""


@callback(
    output=dict(
        is_open=Output("stream-settings-1-modal", "is_open", allow_duplicate=True),
        selection_row_data=Output(
            "stream-selections-table", "rowData", allow_duplicate=True
        ),
    ),
    inputs=dict(
        row=Input(PaginatedTableAIO.ids.table("connections-table"), "cellRendererData"),
        store=State("graph-store", "data"),
        connections_row_data=State(
            PaginatedTableAIO.ids.table("connections-table"), "rowData"
        ),
    ),
    prevent_initial_call=True,
)
def open_stream_settings_1_modal(
    row, store, connections_row_data
):  # TODO Settle type annotations
    """Opens the first advanced settings modal for the specified stream"""

    print()
    print(f"-----------open_stream_settings_1_modal Callback----------")
    # row: {'colId': 'advanced_settings_menu', 'rowIndex': 0, 'rowId': '0fdaf874-0a78-43fd-bded-7f653d299fb8', 'timestamp': 1725807126121}

    col = row["colId"]
    connection_row_index = row["rowIndex"]
    connection_row = connections_row_data[connection_row_index]
    stream_type = connection_row["stream_type"]

    connection_selections = connection_row["selections"]

    if col == "advanced_settings_menu":
        # id, title, value, options, disabled

        # Dont allow opening of advanced settings if no selections are available or if connecting equipment have not been defined
        if (
            not connection_selections
            or connection_row["main_equipment"] == default_option
            or connection_row["downstream_equipment"] == default_option
        ):
            return dict(is_open=no_update, selection_row_data=no_update)

        selections_row_data = generate_selections_table_row_data(
            connection_selections,
            store,
            stream_type,
            connection_row_index,
        )

        return dict(
            is_open=True,
            selection_row_data=selections_row_data,
        )

    return dict(is_open=no_update, selection_row_data=no_update)


@callback(
    Output("stream-settings-1-modal", "is_open", allow_duplicate=True),
    Input("stream-settings-1-modal-cancel-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_settings_1_modal(n_clicks: int) -> bool:
    if n_clicks:
        return False
    return no_update


@callback(
    Output("stream-selections-table", "rowData"),
    Input("stream-selections-table", "cellValueChanged"),
    State("stream-selections-table", "rowData"),
    State("graph-store", "data"),
    prevent_initial_call=True,
)
def update_selection_sets(
    cell_changed: List[Dict[str, Any]],
    row_data: List[Dict[str, Any]],
    store: Dict[str, Any],
):  # TODO Settle type annotations
    """Updates selection set and their disabled states whenever user selects a different option for any selection set"""

    print()
    print(f"-----------update_selection_sets Callback----------")
    print(cell_changed)

    updated_col = cell_changed[0]["colId"]
    print(f"Row data = {row_data}")

    if updated_col == "value":  # Defensive check
        # Update disabled status
        # If disabled, change back to default values
        stream_type = row_data[0]["stream_type"]
        connection_row_index = row_data[0]["connection_row_index"]
        connection_selections = {row["title"]: row["value"] for row in row_data}
        row_data = generate_selections_table_row_data(
            connection_selections,
            store,
            stream_type,
            connection_row_index,
        )
        return row_data
    return no_update


@callback(
    Output("stream-settings-2-modal", "is_open", allow_duplicate=True),
    Output("stream-settings-1-modal", "is_open", allow_duplicate=True),
    Output("stream-setpoints-table", "rowData"),
    Output("stream-conditions-table", "rowData"),
    Input("stream-settings-1-modal-button", "n_clicks"),
    State("graph-store", "data"),
    State("stream-selections-table", "rowData"),
    State("stream-setpoints-table", "rowData"),
    State("stream-conditions-table", "rowData"),
    State(PaginatedTableAIO.ids.table("connections-table"), "rowData"),
    prevent_initial_call=True,
)
def open_stream_settings_2_modal(
    n_clicks: int,
    store: Dict[str, Any],
    selections_row_data: List[Dict[str, Any]],
    setpoints_row_data: List[Dict[str, Any]],
    conditions_row_data: List[Dict[str, Any]],
    row_data: List[Dict[str, Any]],
):  # TODO Settle type annotations
    """Opens the second advanced settings modal for the specified connection/stream"""

    print()
    print(f"-----------open_stream_settings_2_modal Callback----------")

    if n_clicks:
        # id, title, lower_bound, value, upper_bound, disabled
        connection_row_index = selections_row_data[0]["connection_row_index"]
        stream_type = selections_row_data[0]["stream_type"]
        selections = [selection["value"] for selection in selections_row_data]

        # Generate setpoints table
        setpoints = row_data[connection_row_index]["setpoints"]
        setpoints_row_data = generate_setpoints_row_data(
            setpoints, store, stream_type, connection_row_index, selections
        )

        # Generate conditions table
        conditions = row_data[connection_row_index]["conditions"]
        conditions_row_data = generate_conditions_row_data(
            conditions, store, stream_type, connection_row_index, selections
        )

        print(f"setpoints = {setpoints_row_data}")
        print(f"conditions = {conditions_row_data}")
        return True, False, setpoints_row_data, conditions_row_data
    return no_update, no_update, no_update, no_update


@callback(
    Output("stream-settings-2-modal", "is_open"),
    Output("stream-settings-1-modal", "is_open"),
    Input("stream-settings-2-modal-back-button", "n_clicks"),
    State("graph-store", "data"),
    prevent_initial_call=True,
)
def close_stream_settings_2_modal(
    n_clicks: int, store: Dict[str, Any]
):  # TODO Settle type annotations
    """Returns user from the second to the first stream settings modal for the specified equipment"""
    if n_clicks:
        return False, True
    return no_update, no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("connections-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("graph-store", "data", allow_duplicate=True),
    Output("stream-settings-2-modal", "is_open", allow_duplicate=True),
    Input("stream-settings-2-modal-save-button", "n_clicks"),
    State("stream-selections-table", "rowData"),
    State("stream-setpoints-table", "rowData"),
    State("stream-conditions-table", "rowData"),
    State(PaginatedTableAIO.ids.table("connections-table"), "rowData"),
    State("graph-store", "data"),
    prevent_initial_call=True,
)
def update_stream_advanced_settings(
    n_clicks: int,
    selections_row_data: List[Dict[str, Any]],
    setpoints_row_data: List[Dict[str, Any]],
    conditions_row_data: List[Dict[str, Any]],
    connections_row_data: List[Dict[str, Any]],
    store: Dict[str, Any],
):
    """Saves new advanced settings to Connections Table and Store"""
    if n_clicks:
        # Get connection row and deep copy
        connection_row_index = selections_row_data[0]["connection_row_index"]
        new_connection_row = copy.deepcopy(connections_row_data[connection_row_index])

        # Update selections
        new_connection_row["selections"] = {
            selection_row["title"]: selection_row["value"]
            for selection_row in selections_row_data
        }

        # Update setpoints
        new_connection_row["setpoints"] = {
            setpoint_row["title"]: (
                setpoint_row["lower_bound"],
                setpoint_row["value"],
                setpoint_row["upper_bound"],
            )
            for setpoint_row in setpoints_row_data
        }

        # Update conditions
        new_connection_row["conditions"] = {
            condition_row["title"]: (
                condition_row["lower_bound"],
                condition_row["value"],
                condition_row["upper_bound"],
            )
            for condition_row in conditions_row_data
        }

        # Update connection in store
        store["connections"][new_connection_row["id"]] = new_connection_row

        # Update row data
        connections_row_data[connection_row_index] = new_connection_row

        return connections_row_data, store, False
    return no_update, no_update, no_update


def generate_selections_table_row_data(
    connection_selections: Dict[str, str],
    store: Dict[str, Any],
    stream_type: str,
    connection_row_index: int,
) -> List[Dict[str, Any]]:
    """Generate rowData for Selections Table
    Also updates disabled status based on selection values
    """
    selections_row_data = []
    for title, value in connection_selections.items():
        stream_type_preset = store["stream_types"][stream_type]["selections"][title]
        options = stream_type_preset["options"]
        prereq_set: Union[None, Set] = (
            None
            if stream_type_preset["prerequisites"] is None
            else set(stream_type_preset["prerequisites"])
        )
        selected_values_set = set(connection_selections.values())

        disabled = (
            (len(prereq_set & selected_values_set) == 0)
            if prereq_set
            else (prereq_set is None)
        )
        value = (
            value if not disabled else stream_type_preset["value"]
        )  # Reset to default if prereqs no longer met

        row = {
            "id": title,
            "title": title,
            "value": value,
            "options": options,
            "disabled": disabled,  # Check if prerequsities are met and set dropdown to disabled or not
            "stream_type": stream_type,
            "connection_row_index": connection_row_index,
        }
        selections_row_data.append(row)
    print(f"connections selections_row_data: {selections_row_data}")
    return selections_row_data


def generate_setpoints_row_data(
    setpoints: Dict[str, str],
    store: Dict[str, Any],
    stream_type: str,
    row_index: int,
    selections: List[str],
) -> List[Dict[str, Any]]:
    """Generate rowData for Setpoints Table
    Also updates disabled status based on selection values
    """
    setpoints_row_data = []
    for title, value in setpoints.items():
        lower_bound, val, upper_bound = value
        stream_type_preset = store["stream_types"][stream_type]["setpoints"][title]
        prereq_set: Union[None, Set] = (
            None
            if stream_type_preset["prerequisites"] is None
            else set(stream_type_preset["prerequisites"])
        )
        selected_values_set = set(selections)

        disabled = (
            (len(prereq_set & selected_values_set) == 0)
            if prereq_set
            else (prereq_set is None)
        )

        row = {
            "id": title,
            "title": title,
            "lower_bound": (
                lower_bound if not disabled else stream_type_preset["bounds"][0]
            ),  # Reset to default if prereqs no longer met
            "value": (
                val if not disabled else stream_type_preset["value"]
            ),  # Reset to default if prereqs no longer met
            "upper_bound": (
                upper_bound if not disabled else stream_type_preset["bounds"][1]
            ),  # Reset to default if prereqs no longer met
            "disabled": disabled,  # Check if prerequsities are met and set dropdown to disabled or not
            "stream_type": stream_type,
            "row_index": row_index,
            "unit": stream_type_preset["unit"],
        }
        setpoints_row_data.append(row)
    print(f"setpoints_row_data: {setpoints_row_data}")
    return setpoints_row_data


def generate_conditions_row_data(
    conditions: Dict[str, str],
    store: Dict[str, Any],
    stream_type: str,
    row_index: int,
    selections: List[str],
) -> List[Dict[str, Any]]:
    """Generate rowData for Conditions Table
    Also updates disabled status based on selection values
    """
    conditions_row_data = []
    for title, value in conditions.items():
        lower_bound, val, upper_bound = value
        stream_type_preset = store["stream_types"][stream_type]["conditions"][title]
        prereq_set: Union[None, Set] = (
            None
            if stream_type_preset["prerequisites"] is None
            else set(stream_type_preset["prerequisites"])
        )
        selected_values_set = set(selections)

        disabled = (
            (len(prereq_set & selected_values_set) == 0)
            if prereq_set
            else (prereq_set is None)
        )

        row = {
            "id": title,
            "title": title,
            "lower_bound": (
                lower_bound if not disabled else stream_type_preset["bounds"][0]
            ),  # Reset to default if prereqs no longer met
            "value": (
                val if not disabled else stream_type_preset["value"]
            ),  # Reset to default if prereqs no longer met
            "upper_bound": (
                upper_bound if not disabled else stream_type_preset["bounds"][1]
            ),  # Reset to default if prereqs no longer met
            "disabled": disabled,  # Check if prerequsities are met and set dropdown to disabled or not
            "stream_type": stream_type,
            "row_index": row_index,
            "unit": stream_type_preset["unit"],
        }
        conditions_row_data.append(row)
    print(f"conditions_row_data: {conditions_row_data}")
    return conditions_row_data


def has_stream_settings(stream_type: str) -> bool:
    """Check if stream has settings based on stream type"""
    return stream_type in [StreamType.InputStream.value]


def reset_to_default_stream_settings(
    store: Dict[str, Any], row: Dict[str, Any], stream_type: str
) -> Dict[str, Any]:
    """Reset stream settings to default based on stream type"""
    row["advanced_settings_menu"] = has_stream_settings(row["stream_type"])
    row["selections"] = {
        title: data["value"]
        for title, data in store["stream_types"][stream_type]["selections"].items()
    }
    row["setpoints"] = {
        title: (
            data["bounds"][0],
            data["value"],
            data["bounds"][1],
        )
        for title, data in store["stream_types"][stream_type]["setpoints"].items()
    }
    row["conditions"] = {
        title: (
            data["bounds"][0],
            data["value"],
            data["bounds"][1],
        )
        for title, data in store["stream_types"][stream_type]["conditions"].items()
    }

    return row
