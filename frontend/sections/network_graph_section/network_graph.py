from dash import html, callback, Input, Output, State, dcc
import dash_cytoscape as cyto
import plotly.colors as pc
from typing import Dict, List, Optional, Any
from frontend.utils.enums import EquipmentType, DummyEquipmentType, EquipmentType_GPROMS
from frontend.components.base_readme import ReadmeAIO

default_stylesheet = [
    {
        "selector": "node",
        "style": {
            "content": "data(label)",
            "font-size": 12,
            "font-family": "Poppins",
            "width": 10,
            "height": 10,
        },
    },
    {
        "selector": "edge",
        "style": {
            "curve-style": "bezier",
            "target-arrow-shape": "triangle",
            "width": 1,
            "arrow-scale": 0.5,
            # 'target-arrow-color': 'red',
            # 'line-color': 'red'
        },
    },
]


image_mapping = {
    **{enum.value: f"/assets/graph_icons/{enum.value}.png" for enum in EquipmentType},
    **{
        enum.value: f"/assets/graph_icons/{enum.value}.png"
        for enum in EquipmentType_GPROMS
    },
}


def network_graph():
    return html.Div(
        [
            cyto.Cytoscape(
                id="network-graph",
                layout={
                    "name": "klay",
                    "klay": {
                        "spacing": 80,
                        "direction": "RIGHT",
                        "nodePlacement": "LINEAR_SEGMENTS",
                    },
                },
                className="border rounded-4",
                style={"width": "100%", "height": "500px"},
                stylesheet=default_stylesheet,
                elements=[],
                zoom=1,
                maxZoom=2,
                minZoom=1,
            ),
            # dcc.Interval(
            #     id="interval-component",
            #     interval=1 * 1000,  # in milliseconds (2 seconds)
            #     n_intervals=0,
            # ),
            # Legend
            ReadmeAIO(
                "network-graph-legend",
                [
                    html.Div(
                        [html.I(className="fa-solid fa-lightbulb me-2"), "Legend"],
                    )
                ],
                [],
            ),
            # Tooltip
            html.Div(id="network-graph-tooltip", className="position-absolute d-none"),
            html.Div(id="network-graph-load", className="d-none"),
        ],
        className="position-relative d-flex flex-column gap-3",
    )


# Past issues:
# - Battery limit edges would not show on subsequent selection; i.e. BL -> B to BL -> A
@callback(
    Output("network-graph", "elements"),
    Input("graph-store", "modified_timestamp"),
    State("graph-store", "data"),
)
def render_network_graph(ts, store):
    """Re-draws the flow diagram when graph-store changes"""

    elements = generate_graph_elements(store)
    return elements


@callback(
    Output("network-graph", "stylesheet"),
    Output(ReadmeAIO.ids.collapse("network-graph-legend"), "children"),
    Input("graph-store", "modified_timestamp"),
    State("network-graph", "stylesheet"),
    State("graph-store", "data"),
    prevent_initial_call=True,
)
def get_equpiment_types_for_legend(children, stylesheet, store):
    """Retrieve equipment types from store and load into legend"""

    existing_equipment_types = sorted(
        set(eq["equipment_type"] for eq in store["equipment"].values())
    )

    # Filter out Dummy nodes in legend
    dummy_types = set(t.value for t in DummyEquipmentType)
    existing_equipment_types = [
        eq_type for eq_type in existing_equipment_types if eq_type not in dummy_types
    ]

    equipment_types_list = [eqt for eqt in store.get("equipment_types", {})]
    additional_styles = [
        {
            "selector": f'[type = "{node_type}"]',
            "style": {
                # "background-image": f"url({image_mapping[node_type]})",
                "background-image": f"url({image_mapping.get(node_type)})",
                "background-fit": "contain",
                "width": 90,
                "height": 90,
                "background-color": "white",
                # "shape": "barrel",
            },
        }
        for node_type in equipment_types_list
        if image_mapping.get(node_type) is not None
    ]
    stylesheet.extend(additional_styles)

    legend_elements = html.Div(
        [
            html.Div(
                [
                    html.Img(
                        src=image_mapping.get(equipment_type, None),
                        style={
                            "width": "35px",
                            "height": "35px",
                        },
                        className="align-middle d-inline-block me-2 object-fit-contain rounded-circle bg-white",
                    ),
                    html.Span(
                        equipment_type,
                        className="align-middle",
                    ),
                ],
                className="align-middle",
            )
            for equipment_type in existing_equipment_types
        ],
        className="d-flex flex-wrap gap-0 column-gap-3",
    )

    return stylesheet, legend_elements


@callback(
    Output("network-graph-tooltip", "children"),
    Output("network-graph-tooltip", "style"),
    Input("network-graph", "mouseoverNodeData"),
    State("network-graph", "mouseoverNode"),
)
def open_tooltip(node_data, node):
    print(f"node = {node}")
    print(f"node_data = {node_data}")
    if node_data and node:
        tooltip_content = f"Node: {node_data['label']}"
        # Get the node's position and adjust tooltip placement
        node_position = node["position"]
        tooltip_style = {
            "position": "absolute",
            "left": f"{node_position['x']}px",
            "top": f"{node_position['y']}px",
            "background-color": "lightgrey",
            "padding": "5px",
            "border-radius": "5px",
            "display": "block",  # Make it visible
            "zIndex": 1000,  # Ensure it appears on top
        }
        return tooltip_content, tooltip_style
    return "", {"display": "none"}  # Hide tooltip if not hovering over a node


def create_node(id: str, label: str, equipment_type: str) -> Dict[str, Any]:
    return {
        "data": {"id": id, "label": label, "type": equipment_type},
    }


def create_edge(id: str, source: str, target: str) -> Dict[str, Any]:
    return {"data": {"id": id, "source": source, "target": target, "label": "."}}


def generate_graph_elements(store: Dict[str, Any]) -> List[Dict[str, Any]]:
    elements = []  # https://dash.plotly.com/cytoscape/elements

    # Add equipment (nodes)
    for equipment_id, detail_dict in store["equipment"].items():
        node = create_node(
            equipment_id, detail_dict["equipment_name"], detail_dict["equipment_type"]
        )
        elements.append(node)

    # Add connections (edges)
    for connection_id, detail_dict in store["connections"].items():
        edge = create_edge(
            connection_id,
            detail_dict["main_equipment_id"],
            detail_dict["downstream_equipment_id"],
        )
        elements.append(edge)
    return elements


###################################################################################################
# Issue: Polling once every second resulted in this bug:
# - User del equipment --> Remove equipment from store but not connection --> Interval generates graph using incomplete store --> Cytoscape error
# - Can consider longer interval if store trigger in render_network_graph() has bugs
# @callback(
#     Output("network-graph", "elements", allow_duplicate=True),
#     Input("interval-component", "n_intervals"),
#     State("network-graph", "elements"),
#     State("graph-store", "data"),
#     prevent_initial_call="initial_duplicate",
# )
# def update_graph_layout_periodically(n_intervals, elements, store):
#     # print("Interval reached")
#     # print(f"Elements in interval = {elements}")
#     # print(f"Store in interval = {store}")
#     elements = generate_graph_elements(store)
#     return elements
