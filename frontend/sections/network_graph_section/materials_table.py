from dash import html, callback, Input, Output, State, ctx, no_update
import uuid
from frontend.components.paginated_table import PaginatedTableAIO

from frontend.components.table_block import TableBlock

from pprint import pprint

from frontend.utils.uuid_service import UUIDService

column_defs = [
    {
        "headerCheckboxSelection": True,  # Enable header checkbox in this column
        "checkboxSelection": True,  # Enable checkboxes for row selection
        "headerName": "",
        "maxWidth": 40,
        "editable": False,
    },
    # {
    #     "headerName": "Material Type",
    #     "field": "material_type",
    #     "cellEditor": "agSelectCellEditor",
    #     "cellEditorParams": {
    #         "function": "dynamicMaterialTypeOptions(params)",
    #     },
    # },
    # Dropdown
    {
        "headerName": "Material Type",
        "field": "material_type",
        "cellEditor": {"function": "DCC_Dropdown"},
        "cellEditorParams": {
            "function": "dynamicMaterialTypeOptions(params)",
            "clearable": False,
        },
        "cellEditorPopup": True,
        # "cellEditorPopupPosition": "under",
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },
    # Dropdown
]

grid_options = {
    "suppressMenuHide": True,
}


# Main table layout
def materials_table():
    table = PaginatedTableAIO(
        id="materials-table", column_defs=column_defs, grid_options=grid_options
    )
    block = TableBlock("Materials", table=table)
    return block.render()


# app.callback() does not work either
@callback(
    Output(
        PaginatedTableAIO.ids.table("materials-table"), "rowData", allow_duplicate=True
    ),
    Output("graph-store", "data", allow_duplicate=True),
    Output(
        PaginatedTableAIO.ids.table("reactions-table"), "rowData", allow_duplicate=True
    ),
    Output("reactions-store", "data", allow_duplicate=True),
    Output("materials-store", "data", allow_duplicate=True),
    Input("materials-table-add-button", "n_clicks"),
    Input("materials-table-delete-button", "n_clicks"),
    State(PaginatedTableAIO.ids.table("materials-table"), "selectedRows"),
    State(PaginatedTableAIO.ids.table("materials-table"), "rowData"),
    State("graph-store", "data"),
    State(PaginatedTableAIO.ids.table("reactions-table"), "rowData"),
    State("materials-store", "data"),
    prevent_initial_call=True,
)
def update_transaction_materials(
    add_clicks,
    delete_clicks,
    selected_rows,
    row_data,
    store,
    reactions_row_data,
    materials_store,
):
    """Configure add and delete of rows for materials table"""
    print()
    print(f"-----------update_transaction_materials Callback----------")
    print(f"Current row data: {row_data}")

    triggered_id = ctx.triggered_id
    print(f"triggered_id: {triggered_id}")
    # print("materials_store")
    # pprint(materials_store)

    if triggered_id == "materials-table-add-button":
        selected_materials = set([m["material_type"] for m in row_data])
        print(f"selected_materials = {selected_materials}")

        remaining_material_names = list(
            set(store["material_types"].keys()) - selected_materials
        )

        remaining_material_options = [
            {
                "label": f"{material_name} ({store['material_types'][material_name]})",  # e.g. Methane (1111-11-1)
                "value": material_name,
            }
            for material_name in remaining_material_names
        ]

        # Do nothing if all materials have been added
        if len(remaining_material_options) == 0:
            return no_update, no_update, no_update, no_update, no_update

        # Generate new row for materials table
        new_material_id = UUIDService.generate_id()
        new_row = {
            "id": new_material_id,
            "material_type": remaining_material_names[0],
            "material_type_options": sorted(
                remaining_material_options, key=lambda option: option["label"].lower()
            ),
        }
        print(f"New row added: {new_row}", flush=True)

        # Add new selected material to materials store
        materials_store["materials"][new_material_id] = new_row

        return row_data + [new_row], no_update, no_update, no_update, materials_store
    elif triggered_id == "materials-table-delete-button" and selected_rows:
        print(f"Rows to delete: {selected_rows}")
        selected_id = selected_rows[0]["id"]
        selected_material_type = selected_rows[0]["material_type"]
        # Remove material from store
        materials_store["materials"].pop(selected_id, None)
        selected_materials = set([m["material_type"] for m in row_data]) - set(
            [selected_material_type]
        )
        print(f"selected_materials = {selected_materials}")

        remaining_material_names = list(
            set(store["material_types"].keys()) - selected_materials
        )

        remaining_material_options = [
            {
                "label": f"{material_name} ({store['material_types'][material_name]})",  # e.g. Methane (1111-11-1)
                "value": material_name,
            }
            for material_name in remaining_material_names
        ]

        # Loop through all materials and update options
        row_data = [
            {
                "id": row["id"],
                "material_type": row["material_type"],
                "material_type_options": sorted(
                    remaining_material_options
                    + [
                        {
                            "label": f"{row['material_type']} ({store['material_types'][row['material_type']]})",
                            "value": row["material_type"],
                        }
                    ],
                    key=lambda option: option["label"].lower(),
                ),
            }
            for row in row_data
            if row["material_type"] != selected_material_type
        ]

        # Remove reactions containing the removed material
        reactions_row_data = [
            row
            for row in reactions_row_data
            if row["details"]["stoichiometry"].get(selected_material_type, None) is None
        ]

        # print(f"Updated store: ")
        # pprint(store)

        return row_data, store, reactions_row_data, reactions_row_data, materials_store
    return no_update, no_update, no_update, no_update, no_update, no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("materials-table"), "rowData", allow_duplicate=True
    ),
    Output("materials-store", "data", allow_duplicate=True),
    Output(
        PaginatedTableAIO.ids.table("reactions-table"), "rowData", allow_duplicate=True
    ),
    Output("reactions-store", "data", allow_duplicate=True),
    Input(PaginatedTableAIO.ids.table("materials-table"), "cellValueChanged"),
    State("graph-store", "data"),
    State(PaginatedTableAIO.ids.table("materials-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("reactions-table"), "rowData"),
    State("materials-store", "data"),
    prevent_initial_call=True,
)
def validate_materials_options(
    cell_changed, store, row_data, reactions_row_data, materials_store
):
    """Update material dropdown selections for all rows when user changes selection in one cell"""
    print()
    print(f"-----------validate_materials_options Callback----------")
    print(f"cell_changed: {cell_changed}")
    # format -> [{'rowIndex': 0, 'rowId': '41ae5a6f-e41d-4e4a-a7c6-7870b7411ffe', 'data': {'id': '41ae5a6f-e41d-4e4a-a7c6-7870b7411ffe', 'material_type': 'Carbon Dioxide', 'material_type_options': ['Select an option', 'Ethane', 'Carbon Dioxide', 'Benzene']}, 'oldValue': 'Benzene', 'value': 'Carbon Dioxide', 'colId': 'material_type', 'timestamp': 1725957198068}]

    updated_row = cell_changed[0]["data"]
    updated_row_id = cell_changed[0]["rowId"]
    old_value = cell_changed[0]["oldValue"]
    new_value = cell_changed[0]["value"]

    materials_store["materials"].pop(updated_row_id, None)

    selected_materials = set([m["material_type"] for m in row_data])
    print(f"selected_materials = {selected_materials}")

    remaining_material_names = list(
        set(store["material_types"].keys()) - selected_materials
    )
    remaining_material_options = [
        {
            "label": f"{material_name} ({store['material_types'][material_name]})",  # e.g. Methane (1111-11-1)
            "value": material_name,
        }
        for material_name in remaining_material_names
    ]

    # Loop through all materials and update options
    row_data = [
        {
            "id": row["id"],
            "material_type": row["material_type"],
            "material_type_options": sorted(
                remaining_material_options
                + [
                    {
                        "label": f"{row['material_type']} ({store['material_types'][row['material_type']]})",
                        "value": row["material_type"],
                    }
                ],
                key=lambda option: option["label"].lower(),
            ),
        }
        for row in row_data
    ]
    print("rowData = ")
    pprint(row_data)

    # If select, add to store, remove from options
    materials_store["materials"][updated_row_id] = updated_row

    # Remove reactions containing the removed material
    reactions_row_data = [
        row
        for row in reactions_row_data
        if row["details"]["stoichiometry"].get(old_value, None) is None
    ]

    return row_data, materials_store, reactions_row_data, reactions_row_data
