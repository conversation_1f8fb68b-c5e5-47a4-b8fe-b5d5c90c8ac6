from dash import html

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock
from frontend.components.base_tooltip import Tooltip
from dash.development.base_component import Component


column_defs = [
    # columns: id, name, details
    {
        "headerCheckboxSelection": True,  # Enable header checkbox in this column
        "checkboxSelection": True,  # Enable checkboxes for row selection
        "headerName": "",
        "maxWidth": 40,
        "editable": False,
    },
    {
        "headerName": "Name",
        "field": "name",
    },
    {
        "headerName": "",
        "field": "configuration",
        "cellRenderer": "Button",
        "cellRendererParams": {
            "color": "primary",
            "iconClass": "fa-solid fa-gear",
            "buttonClass": "btn-sm text-light bg-transparent border-0",
            "text": "",
        },
        "cellClass": "d-flex align-items-center justify-content-center",
        "maxWidth": 40,
    },
]


def reactions_table(id: str) -> Component:
    table = PaginatedTableAIO(id=id, column_defs=column_defs, editable=False)
    block = TableBlock(
        [
            html.Div(
                [
                    "Reactions",
                    html.I(
                        id="reactions-tooltip",
                        className="fa-solid fa-circle-question m-0",
                    ),
                    Tooltip(
                        html.H5(
                            "Reactions are only needed if Reactor equipment are defined.",
                            className="m-0 fw-normal",
                        ),
                        target="reactions-tooltip",
                        className="bg-white text-black rounded-2 border-1 border-info p-2 shadow-sm",
                    ),
                ],
                className="d-flex align-items-center gap-2",
            )
        ],
        table=table,
    )

    return html.Div([block.render()], className="w-100")
