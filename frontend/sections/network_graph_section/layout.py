import datetime
from pydantic import ValidationError
from frontend.components.base_dropdown import Dropdown
from frontend.components.base_input import InputField
from frontend.components.base_label import Label
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.utils.uuid_service import UUIDService
from .network_graph import network_graph
from .equipment_table import equipment_table
from .connections_table import (
    connections_table,
    add_connection_to_store,
    regenerate_equipment_dropdown_options,
)
from .materials_table import materials_table
from .reactions_table import reactions_table
from .stoichiometry_table import stoichiometry_table
from frontend.components.base_button import Button
from frontend.components.base_spinner import Spinner
from frontend.components.base_container import Container
from frontend.components.base_modal import Modal
from frontend.components.base_modal_body import ModalBody
from frontend.components.base_modal_footer import ModalFooter
from frontend.components.frame import Frame
from frontend.utils.api_service import APIServiceProvider
from frontend.utils.url_encoder_service import UrlEncoderService
from frontend.utils.toast_manager import ToastManager
from .reaction_advanced_configurations import ReactionConfigContext
from .connections_table import has_stream_settings
from frontend.utils.enums import (
    IndustryType,
    EngineType,
    Reactor,
    ReactionType,
    StreamType,
    EquipmentType,
    DummyEquipmentType,
    EquipmentEngineMap,
)
from frontend.utils.auth import User

import uuid
from frontend.entities.equipment import Equipment
from typing import Dict, List, Any, Tuple, Type, Union
from dash import html, dcc, callback, Input, Output, State, no_update, ctx
from dash.development.base_component import Component

from pprint import pprint
import concurrent.futures
import pandas as pd
import copy

default_option = "Select an option"
api = APIServiceProvider().get_api_service()


class LayoutConfig:
    _industry_type_options = [
        {"label": industry.value, "value": industry.value} for industry in IndustryType
    ]
    _engine_type_options_mapping = {
        IndustryType.CHEMICAL.value: [
            {"label": EngineType.DWSIM.value, "value": EngineType.DWSIM.value},
        ],
        IndustryType.PHARMACEUTICAL.value: [
            {"label": EngineType.GPROMS.value, "value": EngineType.GPROMS.value},
        ],
    }
    _show_extra_tables = set([(IndustryType.CHEMICAL.value, EngineType.DWSIM.value)])

    @classmethod
    def has_material_and_reactions_tables(cls, industry: str, engine_type: str) -> bool:
        print(
            f"Industry: {industry}, EngineType: {engine_type}, Has Secondary Tables: {(industry, engine_type) in cls._show_extra_tables}",
            flush=True,
        )
        return (industry, engine_type) in cls._show_extra_tables

    @classmethod
    def get_industry_options(cls) -> list:
        return cls._industry_type_options

    @classmethod
    def get_engine_options(cls, industry: str) -> list:
        engine_options = cls._engine_type_options_mapping.get(industry)
        if not engine_options:
            raise ValueError(
                f"Failed to get simulation engines for industry: {industry}"
            )
        return engine_options


##################################################################################
# LAYOUT
##################################################################################
def graph_section(project_name: str = "") -> Component:
    decoded_project_name = UrlEncoderService.decode(project_name)

    return html.Div(
        [
            # Industry and Engine Selection
            Container(
                children=[
                    html.H2(decoded_project_name, className="fw-semibold"),
                    Frame(
                        [
                            Label(
                                "Select the industry for this configuration",
                                className="h5 fw-medium text-dark",
                            ),
                            Dropdown(
                                LayoutConfig.get_industry_options(),
                                id="industry-select",
                                className="text-dark h5",
                                clearable=False,
                            ),
                            Label(
                                "Select simulation engine",
                                className="h5 fw-medium text-dark",
                            ),
                            Dropdown(
                                [],
                                id="sim-engine-select",
                                className="text-dark h5",
                                clearable=False,
                            ),
                            html.Div(
                                [
                                    Button(
                                        id="save-industry-button",
                                        children=[
                                            "Save",
                                        ],
                                        outline=True,
                                        color="primary",
                                        className="rounded-3 d-flex align-items-center gap-2",
                                    ),
                                ],
                                className="d-flex justify-content-end py-3",
                            ),
                        ],
                        title="Select Industry",
                    ),
                ],
            ),
            # Process Flow Diagram
            Container(
                children=[
                    network_graph(),
                ],
                # style={"display": "none"},
                id="network-graph-container",
            ),
            # Plant config tables
            Container(
                children=[
                    html.H2(
                        f"{decoded_project_name} Entities", className="my-2 fw-semibold"
                    ),
                    html.Div(
                        [
                            materials_table(),
                            reactions_table(id="reactions-table"),
                        ],
                        className="d-none",
                        id="secondary-table-container",
                    ),
                    html.Div(
                        [
                            equipment_table(),
                            connections_table(),
                        ],
                        className="d-md-flex",
                        id="main-table-container",
                    ),
                    html.Div(
                        [
                            Button(
                                id="confirm-button",
                                children=[
                                    "Save",
                                ],
                                outline=True,
                                color="primary",
                                className="rounded-3 d-flex align-items-center gap-2",
                            ),
                        ],
                        className="d-flex justify-content-end py-3",
                    ),
                ],
                # style={"display": "none"},
                id="table-container",
            ),
            # Modal to add Reaction
            Modal(
                id="reaction-config-modal",
                children=[
                    ModalBody(
                        [
                            html.H2(
                                "Configure Reaction",
                                className="fw-semibold text-center py-3",
                            ),
                            InputField(
                                id="reaction-id",
                                type="hidden",
                                value=None,
                            ),
                            Label("Reaction Type", className="h5 fw-medium text-dark"),
                            Dropdown(
                                id="reaction-type-select",
                                options=[
                                    {
                                        "label": reactor_type.name,
                                        "value": reactor_type.value,
                                    }
                                    for reactor_type in ReactionType
                                ],
                                clearable=False,
                            ),
                            Label("Name", className="h5 fw-medium text-dark"),
                            InputField(
                                id="reaction-name",
                            ),
                            # Stoichio table
                            stoichiometry_table(id="stoichiometry-table"),
                            html.Div(
                                [],
                                id="reaction-advanced-config",
                            ),
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="reaction-config-modal-cancel-button",
                                children=[
                                    "Cancel",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="reaction-config-modal-button",
                                children=["Save"],
                                className="bg-primary text-secondary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                size="lg",
                keyboard=False,
                backdrop="static",
            ),
            # Confirm modal on plant config save
            Modal(
                id="confirm-save-plant-config-modal",
                children=[
                    ModalBody(
                        [
                            html.H2(
                                "Save Plant Configuration",
                                className="fw-semibold py-3",
                            ),
                            html.H5(
                                [
                                    "Are you sure you want to save?",
                                ],
                            ),
                            html.H5(
                                [
                                    "This will overwrite the previous plant configuration and remove existing KPIs.",
                                ],
                            ),
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="cancel-save-plant-config-modal-button",
                                children=["No"],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="save-plant-config-modal-button",
                                children=[
                                    Spinner(
                                        id="save-plant-config-modal-button-spinner",
                                        size="sm",
                                        spinner_style={"display": "none"},
                                    ),
                                    "Yes",
                                ],
                                className="bg-primary text-secondary rounded-3 d-flex align-items-center gap-2",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
            ),
            dcc.Store(
                id="graph-store",
                data={
                    "project_name": decoded_project_name,
                    "equipment": {},
                    "connections": {},
                    "materials": {},
                    "reactions": {},
                },
            ),
            dcc.Store(id="reactions-store", data=[]),
            dcc.Store(id="materials-store", data={"materials": {}}),
            html.Div(id="graph-section-load"),
            html.Div(id="graph-section-toast-container"),
            InputField(
                id="graph-section-is-template", value=False, style={"display": "none"}
            ),
            InputField(id="save-trigger-id", value="", style={"display": "none"}),
            InputField(
                id="refresh-plant-config-timestamp", value="", style={"display": "none"}
            ),
        ],
        className="d-flex flex-column",
    )


##################################################################################
# CALLBACKS
##################################################################################
@callback(
    Output("sim-engine-select", "options", allow_duplicate=True),
    Input("industry-select", "value"),
    prevent_initial_call=True,
)
def set_simulation_engine_options(industry: str):
    """Returns the sim engine options based on industry selection"""
    return LayoutConfig.get_engine_options(industry)


# TODO Might not need this
# @callback(
#     # Output("network-graph-container", "style"),
#     # Output("table-container", "style"),
#     # Output("main-table-container", "className"),
#     Output("secondary-table-container", "className"),
#     Input("industry-select", "value"),
#     Input("sim-engine-select", "value"),
#     prevent_initial_call=True,
# )
# def show_pfd_and_tables(industry: str, engine: str):
#     """Display PFD and tables when engine is selected"""
#     if not engine:
#         return no_update

#     graph_container_style = table_container_style = {"display": "block"}
#     main_table_class = "d-md-flex"
#     secondary_table_class = (
#         "d-md-flex"
#         if LayoutConfig.has_material_and_reactions_tables(industry, engine)
#         else "d-none"
#     )

#     return (
#         graph_container_style,
#         table_container_style,
#         main_table_class,
#         secondary_table_class,
#     )


@callback(
    Output("confirm-save-plant-config-modal", "is_open", allow_duplicate=True),
    Output("save-trigger-id", "value", allow_duplicate=True),
    Input("confirm-button", "n_clicks"),
    Input("save-industry-button", "n_clicks"),
    prevent_initial_call=True,
)
def open_confirm_save_modal(
    save_whole_clicks: int, save_industry_clicks: int
) -> Tuple[bool, str]:
    return True, str(ctx.triggered_id)


@callback(
    Output("confirm-save-plant-config-modal", "is_open", allow_duplicate=True),
    Input("cancel-save-plant-config-modal-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_confirm_save_modal(n_clicks: int) -> bool:
    return False


@callback(
    Output("graph-section-toast-container", "children", allow_duplicate=True),
    Output("confirm-save-plant-config-modal", "is_open", allow_duplicate=True),
    Output("is-kpi-definable", "value", allow_duplicate=True),
    Output("refresh-plant-config-timestamp", "value"),
    Input("save-plant-config-modal-button", "n_clicks"),
    State("graph-store", "data"),
    State("materials-store", "data"),
    State(PaginatedTableAIO.ids.table("connections-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("reactions-table"), "rowData"),
    State("graph-section-toast-container", "children"),
    State("auth-store", "data"),
    State("save-trigger-id", "value"),
    State("industry-select", "value"),
    State("sim-engine-select", "value"),
    running=[
        (Output("save-plant-config-modal-button", "disabled"), True, False),
        (
            Output("save-plant-config-modal-button-spinner", "style"),
            {"display": "block"},
            {"display": "none"},
        ),
        (
            Output("save-plant-config-modal-button-spinner", "style"),
            {"display": "block"},
            {"display": "none"},
        ),
    ],
    prevent_initial_call=True,
)
def save_plant_config(
    n_clicks: int,
    store: Dict[str, Any],
    materials_store: Dict[str, Any],
    connections_row_data: List[Dict[str, Any]],
    reactions_row_data: List[Dict[str, Any]],
    toast_children: List[Dict[str, Any]],
    user_store: Dict[str, Any],
    save_trigger_id: str,
    industry: str,
    engine: str,
):
    """Send store to BE and update current plant configuration"""

    project_name = store["project_name"]
    headers = User.get_headers_from_store(user_store)
    toast_manager = ToastManager(toast_children)
    errors = []

    # User resets industry and engine -> Update and save empty plant config
    if save_trigger_id == "save-industry-button":
        data = {
            "user_industry": industry,
            "matrix_engine": engine,
            "is_template": False,
            "equipments": [],
            "connections": [],
            "materials": [],
            "reactions": [],
        }
    # User updates existing plant config -> Update plant config
    else:
        equipment_types = set(store["equipment_types"].keys())

        # Validate equipment data
        ## Check that heavy and light key compounds are filled in for Shortcut Column
        for equipment in store["equipment"].values():
            if equipment["equipment_type"] == "Shortcut Column" and (
                not equipment["selections"]["Heavy Key Compound"]
                or not equipment["selections"]["Light Key Compound"]
            ):
                errors.append(
                    f"Please fill in heavy and light key compounds for equipment {equipment['equipment_name']}"
                )

        # Convert store data to request body format
        equipment_data = [
            {
                "equipmentId": equipment["equipment_name"],
                "equipmentType": equipment["equipment_type"],
                "selections": [
                    {"title": k, "value": v} for k, v in equipment["selections"].items()
                ],
                "setpoints": [
                    {
                        "title": k,
                        "value": v[1],
                        "bounds": [v[0], v[2]],
                        "unit": store["equipment_types"][equipment["equipment_type"]][
                            "setpoints"
                        ][k]["unit"],
                    }
                    for k, v in equipment["setpoints"].items()
                ],
                "conditions": [
                    {
                        "title": k,
                        "value": v[1],
                        "bounds": [v[0], v[2]],
                        "unit": store["equipment_types"][equipment["equipment_type"]][
                            "conditions"
                        ][k]["unit"],
                    }
                    for k, v in equipment["conditions"].items()
                ],
                **(
                    {"reactions": equipment.get("reactions", [])}
                    if equipment["equipment_type"]
                    in (reactor.value for reactor in Reactor)
                    else {"reactions": None}
                ),
            }
            for equipment in store["equipment"].values()
            if equipment["equipment_type"] in equipment_types
        ]

        # Add BatteryIn and BatteryOut dummy nodes
        equipment_data.extend(
            [
                {
                    "equipmentId": equipment["equipment_name"],
                    "equipmentType": EquipmentType.BatteryIn.value,
                    "selections": [],
                    "setpoints": [],
                    "conditions": [],
                }
                for equipment in store["equipment"].values()
                if equipment["equipment_type"] == DummyEquipmentType.BatteryInput.value
            ]
        )

        equipment_data.extend(
            [
                {
                    "equipmentId": equipment["equipment_name"],
                    "equipmentType": EquipmentType.BatteryOut.value,
                    "selections": [],
                    "setpoints": [],
                    "conditions": [],
                }
                for equipment in store["equipment"].values()
                if equipment["equipment_type"] == DummyEquipmentType.BatteryOutput.value
            ]
        )

        connections_data = [
            {
                "upstreamEquipment": connection["main_equipment"],
                "downstreamEquipment": connection["downstream_equipment"],
                "type": connection["stream_type"],
                "selections": [
                    {"title": k, "value": v}
                    for k, v in connection["selections"].items()
                ],
                "setpoints": [
                    {
                        "title": k,
                        "value": v[1],
                        "bounds": [v[0], v[2]],
                        "unit": store["stream_types"][connection["stream_type"]][
                            "setpoints"
                        ][k]["unit"],
                    }
                    for k, v in connection["setpoints"].items()
                ],
                "conditions": [
                    {
                        "title": k,
                        "value": v[1],
                        "bounds": [v[0], v[2]],
                        "unit": store["stream_types"][connection["stream_type"]][
                            "conditions"
                        ][k]["unit"],
                    }
                    for k, v in connection["conditions"].items()
                ],
            }
            for connection in connections_row_data
            if (
                connection["main_equipment"] != default_option
                and connection["downstream_equipment"] != default_option
            )
        ]

        if len(connections_data) != len(connections_row_data):
            errors.append("Please fill in all connections.")

        materials_data = [
            {"name": material["material_type"]}
            for material in materials_store["materials"].values()
        ]

        reactions_data = reactions_row_data

        data = {
            "user_industry": industry,
            "matrix_engine": engine,
            "is_template": False,
            "equipments": equipment_data,
            "connections": connections_data,
            "materials": materials_data,
            "reactions": reactions_data,
        }

    print("JSON Data to update plant configuration = ", flush=True)
    print(data, flush=True)

    if errors:
        toast_manager.make_and_add_toast(errors, "warning")
        return toast_manager.toasts, False, False, no_update

    # Reset KPIs
    response, success = api.post(
        f"/plant-configurations/{project_name}/kpis", data=[], headers=headers
    )
    print(f"Reset KPIs response: {response}", flush=True)

    if not success:
        toast_manager.make_and_add_toast([response["error"]], "warning")
        return toast_manager.toasts, False, False, no_update

    # Save plant config to BE
    response, success = api.post(
        f"/plant-configurations/{project_name}", data=data, headers=headers
    )
    print(f"Save plant config response: {response}", flush=True)

    if not success:
        toast_manager.make_and_add_toast([response["error"]], "warning")
        return toast_manager.toasts, False, False, no_update

    toast_manager.make_and_add_toast(["Plant configuration saved."], "success")

    return toast_manager.toasts, False, False, datetime.datetime.now()


def clear_graph_store(store: Dict[str, Any]) -> Dict[str, Any]:
    """Clear the graph store of its contents except for project_name"""

    empty_store = copy.deepcopy(store)
    for key in ["equipment", "connections", "materials", "reactions"]:
        empty_store[key] = {}

    return empty_store


@callback(
    Output("graph-store", "data"),
    Output(PaginatedTableAIO.ids.table("equipment-table"), "rowData"),
    Output(PaginatedTableAIO.ids.table("connections-table"), "rowData"),
    Output(PaginatedTableAIO.ids.table("materials-table"), "rowData"),
    Output(
        "network-graph-load", "children"
    ),  # This is for triggering update of network graph legend
    Output("materials-store", "data"),
    Output(PaginatedTableAIO.ids.table("reactions-table"), "rowData"),
    Output("reactions-store", "data"),
    Output("graph-section-is-template", "value"),
    Output("industry-select", "value"),
    Output("sim-engine-select", "value"),
    Output("sim-engine-select", "options"),
    Output("secondary-table-container", "className"),
    Output(PaginatedTableAIO.ids.table("equipment-table"), "columnDefs"),
    Output("graph-section-toast-container", "children", allow_duplicate=True),
    Input("auth-store", "modified_timestamp"),
    Input("refresh-plant-config-timestamp", "value"),
    State("graph-store", "data"),
    State("materials-store", "data"),
    State("auth-store", "data"),
    State(PaginatedTableAIO.ids.table("equipment-table"), "columnDefs"),
    State("graph-section-toast-container", "children"),
    prevent_initial_call=True,
)
def get_existing_plant_configuration_and_presets_on_page_load(
    ts,
    refresh_plant_config_ts,
    store,
    materials_store,
    user_store,
    equipment_column_defs,
    toast_children,
):
    print()
    print(f"-----------get_existing_plant_configuration Callback----------")

    # Get project name and encode
    project_name = store["project_name"]
    headers = User.get_headers_from_store(user_store)
    params = {"configName": project_name}

    toast_manager = ToastManager(toast_children)

    # Ensure graph store is empty
    store = clear_graph_store(store)

    params = {"configName": project_name}

    # Get presets details from BE
    preset_dict = {
        "equipment_types": ("/presets/equipment-details", params, headers),
        "stream_types": ("/presets/stream-types", params, headers),
        "material_types": ("/presets/material-types", params, headers),
        "sensor_types": ("/presets/sensor-types", params, headers),
        "plant-configuration": (f"/plant-configurations/{project_name}", None, headers),
    }

    with concurrent.futures.ThreadPoolExecutor() as executor:
        responses = list(executor.map(lambda x: api.get(*x), preset_dict.values()))

    # print("############ Responses ################")
    # pprint(responses)
    # Store presets in graph store
    for key, response in zip(list(preset_dict.keys())[:-1], responses[:-1]):
        data, success = response

        if not success:
            error_msg = "Failed to get presets."
            print(error_msg, flush=True)
            toast_manager.make_and_add_toast([error_msg], "warning")
            return (
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                no_update,
                toast_manager.toasts,
            )

        store[key] = data["data"]

    # Load existing configuration into graph store
    configuration_data, success = responses[-1]
    print("############ Get Existing Config data ################")
    pprint(configuration_data)
    if not success:
        error_msg = f"Failed to get plant configuration {project_name}."
        print(error_msg, flush=True)
        toast_manager.make_and_add_toast([error_msg], "warning")
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            toast_manager.toasts,
        )

    store["material_types"] = {
        mt["name"]: mt["cas_number"] for mt in store["material_types"]["items"]
    }

    # Get valid equipment types
    matrix_engine = configuration_data["data"].get("matrix_engine")
    try:
        matrix_engine_enum = EngineType.get_enum_from_value(matrix_engine)
        equipment_type_enum = EquipmentEngineMap.mapping.get(matrix_engine_enum)
        if not equipment_type_enum:
            raise ValueError(
                f"No equipment-engine mapping for simulation engine {matrix_engine_enum.value}"
            )
        valid_equipment_types = equipment_type_enum.valid_equipment_types()
        print(f"Valid equipment types to load: {valid_equipment_types}", flush=True)
    except Exception as e:
        print(
            f"Failed to get equipment for simulation engine {matrix_engine}  | {e}",
            flush=True,
        )
        toast_manager.add_warning_toast(
            [f"Failed to get equipment for simulation engine {matrix_engine}"]
        )
        return (
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            toast_manager.toasts,
        )

    # Update equipment table - equipment type dropdown options
    equipment_type_col_def = next(
        col_def
        for col_def in equipment_column_defs
        if col_def.get("field") == "equipment_type"
    )
    equipment_type_col_def["cellEditorParams"]["values"] = valid_equipment_types

    # Massage data so that we can access equipment types by type and within each type, advanced settings like setpoints by title
    store["equipment_types"] = {
        t["type"]: t
        for t in store["equipment_types"]
        if t["type"] in valid_equipment_types
    }

    for equipment_type in store["equipment_types"].values():
        for advanced_setting in ["setpoints", "conditions", "selections"]:
            equipment_type[advanced_setting] = {
                advanced_setting_item["title"]: advanced_setting_item
                for advanced_setting_item in equipment_type[advanced_setting]
            }

    # Massage data so that we can access stream types by type and within each type, advanced settings like setpoints by title
    store["stream_types"] = {t["type"]: t for t in store["stream_types"]}
    for stream_type in store["stream_types"].values():
        for advanced_setting in ["setpoints", "conditions", "selections"]:
            stream_type[advanced_setting] = {
                advanced_setting_item["title"]: advanced_setting_item
                for advanced_setting_item in stream_type[advanced_setting]
            }

    # Add existing data to Equipment Table
    equipment_row_data = []
    equipment_name_to_id_mapping = (
        {}
    )  # We need a name: id mapping to properly import connections data below

    for eq in configuration_data["data"].get("equipments", None):
        # Replace BatteryIn equipment with DummyBatteryIn nodes
        if eq["equipmentType"] in EquipmentType.dummy_equipment_types():
            continue

        equipment = {
            "id": str(uuid.uuid4()),
            "equipment_name": eq["equipmentId"],
            "equipment_type": eq["equipmentType"],
            "advanced_settings_menu": "Advanced Settings",
            "selections": {item["title"]: item["value"] for item in eq["selections"]},
            "setpoints": {
                item["title"]: (
                    item["bounds"][0],
                    item["value"],
                    item["bounds"][1],
                )
                for item in eq["setpoints"]
            },
            "conditions": {
                item["title"]: (
                    item["bounds"][0],
                    item["value"],
                    item["bounds"][1],
                )
                for item in eq["conditions"]
            },
            "reactions": (
                [_ for _ in eq["reactions"]]
                if eq["equipmentType"] in (reactor.value for reactor in Reactor)
                else None
            ),
        }
        store["equipment"][equipment["id"]] = equipment
        equipment_row_data.append(equipment)
        equipment_name_to_id_mapping[equipment["equipment_name"]] = equipment["id"]

    # Add existing data to Connections Table
    connections_row_data = []
    for cn in configuration_data["data"].get("connections", None):
        if cn["type"] == StreamType.OutputStream.value:
            main_equipment_id = equipment_name_to_id_mapping[cn["upstreamEquipment"]]
            main_equipment = store["equipment"][main_equipment_id]["equipment_name"]
            downstream_equipment_id = None
            downstream_equipment = cn["downstreamEquipment"]
        elif cn["type"] == StreamType.InputStream.value:
            main_equipment_id = None
            main_equipment = cn["upstreamEquipment"]
            downstream_equipment_id = equipment_name_to_id_mapping[
                cn["downstreamEquipment"]
            ]
            downstream_equipment = store["equipment"][downstream_equipment_id][
                "equipment_name"
            ]
        else:
            main_equipment_id = equipment_name_to_id_mapping[cn["upstreamEquipment"]]
            main_equipment = store["equipment"][main_equipment_id]["equipment_name"]
            downstream_equipment_id = equipment_name_to_id_mapping[
                cn["downstreamEquipment"]
            ]
            downstream_equipment = store["equipment"][downstream_equipment_id][
                "equipment_name"
            ]

        connection = {
            "id": str(uuid.uuid4()),
            "main_equipment_id": main_equipment_id,
            "main_equipment": main_equipment,
            "downstream_equipment_id": downstream_equipment_id,
            "downstream_equipment": downstream_equipment,
            "stream_type": cn["type"],
            "intermediate_equipment_id": None,
            "advanced_settings_menu": has_stream_settings(cn["type"]),
            "selections": {item["title"]: item["value"] for item in cn["selections"]},
            "setpoints": {
                item["title"]: (
                    item["bounds"][0],
                    item["value"],
                    item["bounds"][1],
                )
                for item in cn["setpoints"]
            },
            "conditions": {
                item["title"]: (
                    item["bounds"][0],
                    item["value"],
                    item["bounds"][1],
                )
                for item in cn["conditions"]
            },
        }

        connection = regenerate_equipment_dropdown_options(
            connection, list(store["equipment"].keys())
        )
        # store["connections"][connection["id"]] = connection
        connection, store = add_connection_to_store(connection, store)
        connections_row_data.append(connection)

    # Add existing data to Materials Table
    materials_row_data = []
    materials_response_data = configuration_data["data"].get("materials", None)
    selected_materials = set([d["name"] for d in materials_response_data])
    remaining_material_names = list(
        set(store["material_types"].keys()) - selected_materials
    )
    remaining_material_options = [
        {
            "label": f"{material_name} ({store['material_types'][material_name]})",  # e.g. Methane (1111-11-1)
            "value": material_name,
        }
        for material_name in remaining_material_names
    ]

    for mt in materials_response_data:
        material = {
            "id": str(uuid.uuid4()),
            "material_type": mt["name"],
            "material_type_options": sorted(
                remaining_material_options
                + [
                    {
                        "label": f"{mt['name']} ({store['material_types'][mt['name']]})",
                        "value": mt["name"],
                    }
                ],
                key=lambda option: option["label"].lower(),
            ),
        }
        materials_store["materials"][material["id"]] = material
        materials_row_data.append(material)

    reactions_row_data = configuration_data["data"].get("reactions", [])

    industry = configuration_data["data"].get("user_industry")
    engine = configuration_data["data"].get("matrix_engine")

    if industry not in [industry.value for industry in IndustryType] or engine not in [
        engine.value for engine in EngineType
    ]:
        raise ValueError(f"Invalid Industry / Engine: {industry} / {engine}")

    engine_options = LayoutConfig.get_engine_options(industry)

    secondary_table_class = (
        "d-md-flex"
        if LayoutConfig.has_material_and_reactions_tables(industry, engine)
        else "d-none"
    )

    # TODO: Remove this override. This makes all plant configs non-templates
    is_template = False
    # is_template = bool(configuration_data["data"].get("is_template", False))

    # TODO: Load sensors into store

    # print("Store on page load = ")
    # pprint(store)

    return (
        store,
        equipment_row_data,
        connections_row_data,
        materials_row_data,
        "Data loaded",
        materials_store,
        reactions_row_data,
        reactions_row_data,
        is_template,
        industry,
        engine,
        engine_options,
        secondary_table_class,
        equipment_column_defs,
        toast_manager.toasts,
    )


@callback(
    Output("reactions-table-add-button", "disabled"),
    Output("reactions-table-delete-button", "disabled"),
    Output("reaction-config-modal-button", "disabled"),
    Output("materials-table-add-button", "disabled"),
    Output("materials-table-delete-button", "disabled"),
    Output("equipment-table-add-button", "disabled"),
    Output("equipment-table-delete-button", "disabled"),
    Output("connections-table-add-button", "disabled"),
    Output("connections-table-delete-button", "disabled"),
    Output("advanced-settings-2-modal-save-button", "disabled"),
    Output("confirm-button", "disabled"),
    Output(
        PaginatedTableAIO.ids.table("equipment-table"),
        "columnDefs",
        allow_duplicate=True,
    ),
    Output(
        PaginatedTableAIO.ids.table("connections-table"),
        "columnDefs",
        allow_duplicate=True,
    ),
    Output(
        PaginatedTableAIO.ids.table("reactions-table"),
        "columnDefs",
        allow_duplicate=True,
    ),
    Output(
        PaginatedTableAIO.ids.table("materials-table"),
        "columnDefs",
        allow_duplicate=True,
    ),
    Input("graph-section-is-template", "value"),
    State(PaginatedTableAIO.ids.table("equipment-table"), "columnDefs"),
    State(PaginatedTableAIO.ids.table("connections-table"), "columnDefs"),
    State(PaginatedTableAIO.ids.table("reactions-table"), "columnDefs"),
    State(PaginatedTableAIO.ids.table("materials-table"), "columnDefs"),
    prevent_initial_call=True,
)
def disable_editing_for_template(
    is_template: bool,
    equipment_col_def: List[Dict[str, Any]],
    connections_col_def: List[Dict[str, Any]],
    reactions_col_def: List[Dict[str, Any]],
    materials_col_def: List[Dict[str, Any]],
):
    """Disable editing if plant configuration is a template and not user-created.

    Make all tables non-editable and all their buttons disabled.
    """

    n_buttons = 11
    col_defs = (
        equipment_col_def,
        connections_col_def,
        reactions_col_def,
        materials_col_def,
    )

    for col_def in col_defs:
        for col in col_def:
            col["editable"] = False

    if is_template:
        return (True,) * n_buttons + col_defs
    return (False,) * n_buttons + (no_update,) * len(col_defs)


####################

# REACTIONS TABLE


@callback(
    Output(
        PaginatedTableAIO.ids.table("reactions-table"), "rowData", allow_duplicate=True
    ),
    Output("graph-store", "data", allow_duplicate=True),
    Output("reaction-config-modal", "is_open", allow_duplicate=True),
    Output("reaction-name", "value", allow_duplicate=True),
    Output("reaction-type-select", "value", allow_duplicate=True),
    Output(PaginatedTableAIO.ids.table("stoichiometry-table"), "rowData"),
    Output("reaction-id", "value", allow_duplicate=True),
    Output("reactions-store", "data", allow_duplicate=True),
    Output("reaction-advanced-config", "children", allow_duplicate=True),
    Input("reactions-table-add-button", "n_clicks"),
    Input("reactions-table-delete-button", "n_clicks"),
    State(PaginatedTableAIO.ids.table("reactions-table"), "selectedRows"),
    State(PaginatedTableAIO.ids.table("reactions-table"), "rowData"),
    State("graph-store", "data"),
    State(PaginatedTableAIO.ids.table("materials-table"), "rowData"),
    prevent_initial_call=True,
)
def add_or_delete_reactions(
    add_clicks, delete_clicks, selected_rows, row_data, store, materials_row_data
):
    """Configure add or delete of rows for Reactions table"""

    print()
    print(f"-----------add_or_delete_reactions Callback----------")

    triggered_id = ctx.triggered_id

    if triggered_id == "reactions-table-add-button":
        # Open modal
        stoichio_row_data = [
            {
                "id": UUIDService.generate_id(),
                "material_name": material["material_type"],
                "selected": False,
                "base_component": False,
                "stoichiometric_coeff": 0,
            }
            for material in materials_row_data
        ]

        default_reaction_type = ReactionType.Conversion.value

        # Generate advanced reaction config components
        reaction_config_context = ReactionConfigContext(default_reaction_type)
        advanced_selection_components = reaction_config_context.generate_components({})

        return (
            no_update,
            no_update,
            True,
            "",
            default_reaction_type,
            stoichio_row_data,
            None,
            no_update,
            advanced_selection_components,
        )

    elif triggered_id == "reactions-table-delete-button":
        selected_id = selected_rows[0]["id"]

        row_data = [row for row in row_data if row["id"] != selected_id]

        return (
            row_data,
            store,
            no_update,
            no_update,
            no_update,
            no_update,
            no_update,
            row_data,
            no_update,
        )


@callback(
    Output("reaction-config-modal", "is_open", allow_duplicate=True),
    Input("reaction-config-modal-cancel-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_reaction_config_modal(n_clicks):
    if n_clicks:
        return False
    return no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("stoichiometry-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Input(PaginatedTableAIO.ids.table("stoichiometry-table"), "cellValueChanged"),
    State(PaginatedTableAIO.ids.table("stoichiometry-table"), "rowData"),
    prevent_initial_call=True,
)
def validate_base_component(cell_changed, row_data):
    """Ensure that only 1 base component is selected at any time when user selects a new base_component"""

    print()
    print(f"-----------validate_base_component Callback----------")

    updated_col = cell_changed[0]["colId"]
    new_value = cell_changed[0]["value"]
    row_id = cell_changed[0]["data"]["id"]
    print(new_value)
    print(updated_col)

    if updated_col == "base_component" and new_value:
        for row in row_data:
            if row["id"] == row_id:
                row["selected"] = True
                continue
            row["base_component"] = False
        return row_data

    return no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("reactions-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("graph-section-toast-container", "children"),
    Output("reaction-config-modal", "is_open", allow_duplicate=True),
    Output("graph-store", "data", allow_duplicate=True),
    Output("reactions-store", "data", allow_duplicate=True),
    Input("reaction-config-modal-button", "n_clicks"),
    State("reaction-type-select", "value"),
    State("reaction-name", "value"),
    State(
        PaginatedTableAIO.ids.table("stoichiometry-table"),
        "rowData",
    ),
    State("reaction-advanced-config", "children"),
    State("graph-section-toast-container", "children"),
    State(
        PaginatedTableAIO.ids.table("reactions-table"),
        "rowData",
    ),
    State("reaction-id", "value"),
    State("graph-store", "data"),
    prevent_initial_call=True,
)
def save_reaction(
    save_clicks,
    reaction_type,
    reaction_name,
    stoichio_row_data,
    advanced_selections,
    toast_children,
    reactions_row_data,
    reaction_id,
    store,
):
    """Save reaction"""

    print()
    print(f"-----------save_reaction Callback----------")
    print(f"reaction_id = {reaction_id}")

    pprint(advanced_selections)
    toast_manager = ToastManager(toast_children)
    errors = []

    if not reaction_name:
        errors.append("Please enter a valid name.")

    # Validations for Stoichiometry table
    stoic_df = pd.DataFrame(stoichio_row_data)
    stoic_df["stoichiometric_coeff"] = stoic_df["stoichiometric_coeff"].astype("int64")

    ## At least 1 base component must be selected
    if len(stoic_df[(stoic_df["base_component"]) & (stoic_df["selected"])]) != 1:
        errors.append("Please select one base component.")

    ## At least 1 reactant has been chosen
    if (
        len(stoic_df[(stoic_df["selected"]) & (stoic_df["stoichiometric_coeff"] < 0)])
        < 1
    ):
        errors.append("Please select at least one reactant.")

    ## At least 1 product has been chosen
    if (
        len(stoic_df[(stoic_df["selected"]) & (stoic_df["stoichiometric_coeff"] > 0)])
        < 1
    ):
        errors.append("Please select at least one product.")

    ## All selected materials must have stoichiometric_coeff != 0
    if (
        len(stoic_df[(stoic_df["selected"]) & (stoic_df["stoichiometric_coeff"] == 0)])
        > 0
    ):
        errors.append("Please fill in all stoichiometric coefficients.")

    # Validation for advanced selections
    reaction_config_context = ReactionConfigContext(reaction_type)
    ## Find all inputs that have no value / invalid values
    missing_configs = reaction_config_context.get_missing_values(advanced_selections)
    if missing_configs:
        errors.extend(
            [f"Invalid {config.replace('_', ' ')}." for config in missing_configs]
        )

    if errors:
        toast_manager.make_and_add_toast(errors, "warning")

        return no_update, toast_manager.toasts, no_update, no_update, no_update

    filtered_df = stoic_df[stoic_df["selected"]]

    advanced_selections_values = reaction_config_context.get_values_mapping(
        advanced_selections
    )

    # Save reaction
    reaction_row = {
        "name": reaction_name,
        "details": {
            "type": reaction_type,
            "stoichiometry": {
                record["material_name"]: record["stoichiometric_coeff"]
                for record in filtered_df.to_dict("records")
            },
            "base_compound": filtered_df[filtered_df["base_component"]].iloc[0][
                "material_name"
            ],
            "advanced_selections": advanced_selections_values,
        },
    }

    if reaction_id is None:
        reaction_row["id"] = UUIDService.generate_id()
        reactions_row_data.append(reaction_row)
    else:
        reaction_row["id"] = reaction_id
        reactions_row_data = [
            reaction_row if row["id"] == reaction_id else row
            for row in reactions_row_data
        ]

    print("reactions_row_data")
    pprint(reactions_row_data)

    return reactions_row_data, no_update, False, store, reactions_row_data


@callback(
    Output("reaction-config-modal", "is_open", allow_duplicate=True),
    Output("reaction-id", "value", allow_duplicate=True),
    Output("reaction-type-select", "value", allow_duplicate=True),
    Output("reaction-name", "value", allow_duplicate=True),
    Output(
        PaginatedTableAIO.ids.table("stoichiometry-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("reaction-advanced-config", "children", allow_duplicate=True),
    Input(PaginatedTableAIO.ids.table("reactions-table"), "cellRendererData"),
    State("graph-store", "data"),
    State(PaginatedTableAIO.ids.table("reactions-table"), "rowData"),
    State(PaginatedTableAIO.ids.table("materials-table"), "rowData"),
    prevent_initial_call=True,
)
def edit_reaction(row, store, reactions_row_data, materials_row_data):
    """Open modal to allow user to edit reaction"""
    print()
    print(f"-----------edit_reaction Callback----------")

    print(row)

    col = row["colId"]
    reaction_row_id = row["rowId"]

    if col == "configuration":
        reaction_row = [
            row for row in reactions_row_data if row["id"] == reaction_row_id
        ][0]
        reaction_name = reaction_row["name"]
        reaction_type = reaction_row["details"]["type"]

        # Populate Stoichiometry table
        materials_df = pd.DataFrame(materials_row_data)
        materials_df["material_name"] = materials_df["material_type"]
        df = materials_df["material_name"].to_frame()
        df["selected"] = False
        df["base_component"] = False
        df["stoichiometric_coeff"] = 0
        df["id"] = [UUIDService.generate_id() for _ in range(len(df.index))]
        df = df.set_index("material_name", drop=False)
        print(df)

        for material, stoic in reaction_row["details"]["stoichiometry"].items():
            df.loc[material, ("selected")] = True
            df.loc[material, ("stoichiometric_coeff")] = stoic

        df.loc[reaction_row["details"]["base_compound"], ("base_component")] = True
        stoichio_row_data = df.to_dict("records")

        # Fill in advanced reaction configurations
        reaction_config_context = ReactionConfigContext(reaction_type)
        component_ids = reaction_config_context.get_component_ids()
        advanced_selections_mapping = {
            id: reaction_row["details"]["advanced_selections"].get(id, None)
            for id in component_ids
        }
        advanced_selections = reaction_config_context.generate_components(
            advanced_selections_mapping
        )

        return (
            True,
            reaction_row_id,
            reaction_type,
            reaction_name,
            stoichio_row_data,
            advanced_selections,
        )

    return (
        no_update,
        no_update,
        no_update,
        no_update,
        no_update,
    )


# TODO: For testing only
# @callback(
#     Output(PaginatedTableAIO.ids.table("reactions-table"), "rowData"),
#     Input("graph-section-load", "children"),
# )
# def test(children):
#     return [
#         {
#             "id": "231312-1231",
#             "name": "Test 1",
#             "details": {
#                 "type": "Conversion",
#                 "stoichiometry": {
#                     "Benzene": 1,
#                     "Methane": -1,
#                 },
#                 "base_compound": "Methane",
#                 "advanced_selections": {
#                     "reaction_phase": "Vapor",
#                     "reaction_conversion": 10,
#                 },
#             },
#         }
#     ]
