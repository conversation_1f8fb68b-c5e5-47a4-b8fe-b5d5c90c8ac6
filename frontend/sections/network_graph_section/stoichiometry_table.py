from dash import html

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock
from dash.development.base_component import Component


column_defs = [
    {
        "headerName": "Name",
        "field": "material_name",
        "editable": False,
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
        "cellClass": "overflow-hidden",
        "tooltipField": "material_name",
    },
    {
        "headerName": "Include",
        "field": "selected",
        "cellRenderer": "agCheckboxCellRenderer",
        "cellEditor": "agCheckboxCellEditor",
        "editable": True,
        "suppressKeyboardEvent": {"function": "params.event.key === ' '"},
    },
    {
        "headerName": "Base Component",
        "field": "base_component",
        "cellRenderer": "agCheckboxCellRenderer",
        "cellEditor": "agCheckboxCellEditor",
        "editable": True,
        "suppressKeyboardEvent": {"function": "params.event.key === ' '"},
    },
    {
        "headerName": "Stoichoimetric Coefficient",
        "field": "stoichiometric_coeff",
        # This ensures that only whole numbers are allowed
        "valueParser": {"function": "Number(params.newValue).toFixed(0) || 0"},
    },
]


grid_options = {
    "suppressMenuHide": True,
}


def stoichiometry_table(id: str) -> Component:
    table = PaginatedTableAIO(id=id, column_defs=column_defs, grid_options=grid_options)
    block = TableBlock(
        "Components/Stoichiometry",
        table=table,
        has_add_button=False,
        has_del_button=False,
    )

    return html.Div([block.render()], className="w-100")
