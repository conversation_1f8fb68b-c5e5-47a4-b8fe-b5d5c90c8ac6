from dash.development.base_component import Component
from dash import html, dcc, callback, Input, Output, State, no_update
from dash.exceptions import PreventUpdate
from pydantic import BaseModel, Field
from typing import (
    ClassVar,
    Dict,
    List,
    Any,
    Sequence,
    Union,
    TypeVar,
    Type,
)
import base64
import pandas as pd
import io

from frontend.components.base_dropdown import Dropdown
from frontend.components.base_button import Button
from frontend.components.base_upload import Upload
from frontend.components.base_modal import Modal
from frontend.components.base_modal_body import ModalBody
from frontend.components.base_modal_footer import ModalFooter
from frontend.components.base_label import Label
from frontend.components.base_toast import Toast
from frontend.components.base_spinner import Spinner
from frontend.components.base_container import Container
from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.base_graph import Graph
from frontend.utils.toast_manager import ToastManager
from frontend.utils.auth import User
from frontend.utils.enums import ModelType, TrainingRegimeType, StatusTagGenerator
from frontend.components.column_mapping_table import (
    column_mapping_table,
    update_variable_options,
    validate_column_mapping_on_timeset_change,
    validate_column_mapping_on_timestep_change,
    validate_column_mapping_on_var_id_change,
)

from frontend.utils.api_service import APIServiceProvider
from frontend.utils.url_encoder_service import UrlEncoderService
from frontend.utils.uuid_service import UUIDService
from frontend.utils.graph_functions import (
    generate_line_chart,
    generate_horizontal_bar_chart,
)
from frontend.models.base_models import BaseTableData


api = APIServiceProvider().get_api_service()


class LayoutConfig:
    _model_type_options = [
        # {
        #     "label": "Max accuracy  x  Long training time",
        #     "value": ModelType.FIRST_PRINCIPLES.value,
        # },
        {
            "label": "High Accuracy, Long Training",
            "value": ModelType.RNN.value,
        },
        # {
        #     "label": "Low Accuracy, Quick Training",
        #     "value": ModelType.ML.value,
        # },
    ]
    _training_regime_options = [
        {
            "label": "via Training Data",
            "value": TrainingRegimeType.TRAINING_DATA.value,
        }
    ]

    @classmethod
    def get_model_type_options(cls) -> list:
        return cls._model_type_options

    @classmethod
    def get_default_model_type(cls) -> str:
        return ModelType.RNN.value

    @classmethod
    def get_training_regime_options(cls) -> list:
        return cls._training_regime_options

    @classmethod
    def get_default_training_regime(cls) -> str:
        return TrainingRegimeType.TRAINING_DATA.value


class TrainingResultGraph(BaseTableData):
    timestep: int
    value: float
    entity_name: str = Field(..., examples=["HEX-100"])
    var_name: str = Field(..., examples=["Outlet Temperature"])
    variable: str = Field(
        ...,
        description="Full variable name with entity",
        examples=["HEX-100 Outlet Temperature"],
    )

    # Class vars to get pydantic fields as strings
    variable_title: ClassVar[str] = "variable"
    timestep_title: ClassVar[str] = "timestep"
    value_title: ClassVar[str] = "value"

    @classmethod
    def to_df(cls: Type["TrainingResultGraph"], data: Sequence["TrainingResultGraph"]):
        return pd.DataFrame(cls.hydrate(data))


##################################################################################
# LAYOUT
##################################################################################
def model_setup_section(project_name: str = "") -> Component:
    decoded_project_name = UrlEncoderService.decode(project_name)

    return Container(
        children=[
            # ==========================================
            # Main section
            # ==========================================
            html.Div(
                [
                    html.H2("Model Setup", className="my-2 fw-semibold"),
                    html.Div(
                        id="model-setup-status",
                        className="d-flex",
                    ),
                ],
                className="d-flex gap-4 align-items-center",
            ),
            html.Div(
                [
                    Button(
                        id="model-training-configure-button",
                        children=[
                            html.I(className="fa-solid fa-gears me-2"),
                            "Configure",
                        ],
                        className="bg-secondary text-primary rounded-3",
                    ),
                    Button(
                        id="model-training-results-button",
                        children=[
                            html.I(className="fa-solid fa-chart-line me-2"),
                            "See Metrics",
                        ],
                        className="bg-secondary text-primary rounded-3",
                    ),
                ],
                className="d-flex gap-2 justify-content-start align-items-center my-2",
            ),
            # ==========================================
            # Training Config Modal
            # ==========================================
            Modal(
                id="model-training-configure-modal",
                children=[
                    ModalBody(
                        [
                            html.H2(
                                "Setup Model", className="fw-semibold text-center py-3"
                            ),
                            html.Div(
                                [
                                    Label(
                                        "Select Model Type",
                                        className="h5 fw-medium text-dark",
                                    ),
                                    Dropdown(
                                        options=LayoutConfig.get_model_type_options(),
                                        value=LayoutConfig.get_default_model_type(),
                                        id="model-type-select",
                                        className="text-dark h5",
                                        clearable=False,
                                    ),
                                    Label(
                                        "Training Regime",
                                        className="h5 fw-medium text-dark",
                                    ),
                                    Dropdown(
                                        options=LayoutConfig.get_training_regime_options(),
                                        value=LayoutConfig.get_default_training_regime(),
                                        id="training-regime-select",
                                        className="text-dark h5",
                                        clearable=False,
                                    ),
                                    Upload(
                                        children=[
                                            Button(
                                                id="model-training-data-upload-button",
                                                children=[
                                                    html.I(
                                                        className="fa-solid fa-arrow-up-from-bracket"
                                                    ),
                                                    "Upload Training Data",
                                                    Spinner(
                                                        id="model-training-data-upload-button-spinner",
                                                        size="sm",
                                                        spinner_style={
                                                            "display": "none"
                                                        },
                                                    ),
                                                ],
                                                className="bg-primary text-secondary rounded-3 d-flex align-items-center gap-2",
                                            )
                                        ],
                                        id="model-training-data-upload",
                                    ),
                                    column_mapping_table(
                                        id="training-config-table",
                                        title="Training Configuration",
                                    ),
                                ],
                                className="px-2",
                            ),
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="model-training-configure-modal-cancel-button",
                                children=[
                                    html.I(className="fa-solid fa-xmark me-2"),
                                    "Cancel",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="model-training-configure-modal-train-button",
                                children=[
                                    html.I(className="fa-solid fa-check"),
                                    "Train",
                                    Spinner(
                                        id="model-training-configure-modal-train-button-spinner",
                                        size="sm",
                                        spinner_style={"display": "none"},
                                    ),
                                ],
                                className="bg-primary text-secondary rounded-3 d-flex align-items-center gap-2",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
                size="xl",
            ),
            # ==========================================
            # Training Results Modal
            # ==========================================
            Modal(
                id="model-training-results-modal",
                children=[
                    ModalBody(
                        [
                            html.H2(
                                "Model Training Results",
                                className="fw-semibold text-center py-3",
                            ),
                            html.Div(
                                children=[],
                                id="model-training-results-container",
                            ),
                        ]
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="model-training-results-modal-close-button",
                                children=[
                                    html.I(className="fa-solid fa-xmark me-2"),
                                    "Close",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
                size="xl",
            ),
            # Store is used for storing the project/config name
            dcc.Store(
                id="model-setup-project-store",
                data={"project_name": decoded_project_name},
            ),
            dcc.Store(
                id="model-setup-variables-store",
                data={"project_name": decoded_project_name},
            ),
            dcc.Store(
                id="model-setup-results-store",
                data={},
            ),
            dcc.Interval(
                id="model-training-status-checker",
                interval=5000,
            ),
            html.Div(id="model-setup-toast-container"),
        ]
    )


##################################################################################
# CALLBACKS
##################################################################################
@callback(
    Output("model-training-configure-modal", "is_open", allow_duplicate=True),
    Output("model-setup-variables-store", "data", allow_duplicate=True),
    Output("model-type-select", "value", allow_duplicate=True),
    Output("training-regime-select", "value", allow_duplicate=True),
    Output(
        PaginatedTableAIO.ids.table("training-config-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Output("model-setup-toast-container", "children", allow_duplicate=True),
    Input("model-training-configure-button", "n_clicks"),
    State("model-setup-project-store", "data"),
    State("auth-store", "data"),
    State("model-setup-toast-container", "children"),
    prevent_initial_call=True,
)
def open_model_training_modal(
    n_clicks: int,
    project_store: dict,
    user_store: dict,
    toast_children: list,
):
    """Opens Model Training modal

    Gets existing plant configuration data to get entity (equipment/connection) variable names and ids.
    Gets existing Model Training Configuration data and populates UI.
    """
    if not n_clicks:
        raise PreventUpdate

    toast_manager = ToastManager(toast_children)

    # Get model training config data
    project_name = project_store.get("project_name")
    if not project_name:
        raise KeyError(f"Missing plant configuration name.")
    headers = User.get_headers_from_store(user_store)

    # Get plant config variable data
    response, success = api.get(
        f"/plant-configurations/{project_name}", headers=headers
    )
    if not success:
        raise Exception(f"Failed to get plant configuration: {response['error']}")
    data = response["data"]
    variables_store = {}
    for entity in data["equipments"]:
        for var in entity["setpoints"] + entity["conditions"]:
            variables_store[var["uuid_str"]] = (
                f"{entity['equipmentId']} - {var['title']}"
            )

    for entity in data["connections"]:
        for var in entity["setpoints"] + entity["conditions"]:
            variables_store[var["uuid_str"]] = (
                f"{entity['upstreamEquipment']}->{entity['downstreamEquipment']} - {var['title']}"
            )
    print(f"Variables store: {variables_store}", flush=True)

    # TODO Populate on modal open if there is an existing training configuration
    response, success = api.get(
        f"/plant-configurations/{project_name}/model-setup/training-setup",
        headers=headers,
    )
    if not success:
        print(
            f"Failed to get model training configuration: {response['error']}",
            flush=True,
        )
        toast = toast_manager.make_toast(
            [f"Failed to get model training configuration: {response['error']}"],
            "warning",
        )
        toast_manager.add_toast(toast)
        return (
            True,
            variables_store,
            LayoutConfig.get_default_model_type(),
            LayoutConfig.get_default_training_regime(),
            [],
            toast_manager.toasts,
        )

    data = response["data"]
    model_type = data["model_type"]
    training_regime = data["training_regime"]
    column_mapping = data["csvCol2varUUID"]
    timestep_column = data["timestep_col"]
    timeset_column = data["timeset_col"]

    row_data = []
    if timeset_column:
        row_data.append(
            {
                "id": UUIDService.generate_id(),
                "var_id": None,
                "column_name": timeset_column,
                "var_options": None,
                "is_timestep": False,
                "is_timeset": True,
            }
        )

    if timestep_column:
        row_data.append(
            {
                "id": UUIDService.generate_id(),
                "var_id": None,
                "column_name": timestep_column,
                "var_options": None,
                "is_timestep": True,
                "is_timeset": False,
            }
        )

    row_data += [
        {
            "id": UUIDService.generate_id(),
            "var_id": uuid,
            "column_name": column_name,
            "var_options": None,
            "is_timestep": False,
            "is_timeset": False,
        }
        for column_name, uuid in column_mapping.items()
    ]
    # Update variable options for each row
    row_data = update_variable_options(row_data, variables_store)

    return (
        True,
        variables_store,
        model_type,
        training_regime,
        row_data,
        toast_manager.toasts,
    )


@callback(
    Output("model-training-configure-modal", "is_open", allow_duplicate=True),
    Input("model-training-configure-modal-cancel-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_model_training_modal(n_clicks: int):
    """Closes Model Training modal"""
    if n_clicks:
        return False
    return no_update


@callback(
    Output(PaginatedTableAIO.ids.table("training-config-table"), "rowData"),
    Output("model-training-data-upload", "contents"),
    Output("model-setup-toast-container", "children", allow_duplicate=True),
    Input("model-training-data-upload", "contents"),
    State("model-setup-variables-store", "data"),
    State("model-setup-toast-container", "children"),
    prevent_initial_call=True,
    running=[
        (Output("model-training-data-upload-button", "disabled"), True, False),
        (
            Output("model-training-data-upload-button-spinner", "spinner_style"),
            {"display": "inline-block"},
            {"display": "none"},
        ),
    ],
)
def process_uploaded_training_data(
    contents: str,
    variables_store: dict,
    toast_children: list,
):
    """Validates CSV file containing model training data

    Ensures that the file uploaded is in CSV format.
    Populates Training Configuration table for user to specify column to variable mapping.
    """
    # Decode data
    content_type, content_string = contents.split(",")
    decoded = base64.b64decode(content_string)
    toast_manager = ToastManager(toast_children)

    # Validate that file is csv using pandas
    try:
        df = pd.read_csv(io.StringIO(decoded.decode("utf-8")))
    except Exception as e:
        print(f"Please upload file in CSV format | {e}", flush=True)
        toast = toast_manager.make_toast(
            ["Please upload file in CSV format."],
            "warning",
        )
        toast_manager.add_toast(toast)
        return [], None, toast_manager.toasts

    # Parse headers and populate Training Configuration table
    columns = list(df.columns)
    var_options = [
        {
            "label": var_name,
            "value": uuid,
        }
        for uuid, var_name in variables_store.items()
    ]
    print(f"Columns parsed = {columns}", flush=True)
    print(f"Variable options = {var_options}", flush=True)

    row_data = [
        {
            "id": UUIDService.generate_id(),
            "var_id": None,
            "column_name": column,
            "var_options": var_options,
            "is_timestep": False,
            "is_timeset": False,
        }
        for column in columns
    ]
    return row_data, no_update, no_update


@callback(
    Output(
        PaginatedTableAIO.ids.table("training-config-table"),
        "rowData",
        allow_duplicate=True,
    ),
    Input(PaginatedTableAIO.ids.table("training-config-table"), "cellValueChanged"),
    State(PaginatedTableAIO.ids.table("training-config-table"), "rowData"),
    State("model-setup-variables-store", "data"),
    prevent_initial_call=True,
)
def validate_column_mapping_table(
    cell_changed: List[Dict[str, Any]],
    row_data: list,
    variables_store: dict,
):
    """Validates table when cell values change.
    This ensures that each variable can only be mapped to one CSV column.
    """
    updated_col = cell_changed[0]["colId"]
    updated_row_index = cell_changed[0]["rowIndex"]

    # When user maps column to a new variable
    if updated_col == "var_id":
        return validate_column_mapping_on_var_id_change(
            updated_row_index=updated_row_index,
            row_data=row_data,
            variables_store=variables_store,
        )

    # When user selects a column as timestep
    if updated_col == "is_timestep":
        return validate_column_mapping_on_timestep_change(
            updated_row_index=updated_row_index,
            row_data=row_data,
            variables_store=variables_store,
        )

    # When user selects a column as timeset
    if updated_col == "is_timeset":
        return validate_column_mapping_on_timeset_change(
            updated_row_index=updated_row_index,
            row_data=row_data,
            variables_store=variables_store,
        )

    return no_update


@callback(
    Output("model-setup-status", "children"),
    Input("model-training-status-checker", "n_intervals"),
    State("auth-store", "data"),
    State("model-setup-project-store", "data"),
)
def check_model_training_status(_: int, user_store: dict, project_store: dict):
    """Updates model training status

    Checks the BE for model training status every interval of 5s.
    Displays the corresponding status if there is one, on BE error, display error fetching status.
    """
    headers = User.get_headers_from_store(user_store)
    project_name = project_store["project_name"]

    response, success = api.get(
        f"/plant-configurations/{project_name}/model-setup/training-status",
        headers=headers,
    )
    if not success:
        # print(f"Failed to get model training status: {response['error']}", flush=True)
        return StatusTagGenerator.generate_status_tag(
            status=StatusTagGenerator.ERROR.value
        )
    status = response["data"]["status"]

    return StatusTagGenerator.generate_status_tag(status=status)


@callback(
    Output("model-training-configure-modal", "is_open", allow_duplicate=True),
    Output("model-setup-toast-container", "children", allow_duplicate=True),
    Input("model-training-configure-modal-train-button", "n_clicks"),
    State("auth-store", "data"),
    State("model-setup-project-store", "data"),
    State("model-type-select", "value"),
    State("training-regime-select", "value"),
    State("model-training-data-upload", "contents"),
    State(
        PaginatedTableAIO.ids.table("training-config-table"),
        "rowData",
    ),
    State("model-setup-toast-container", "children"),
    prevent_initial_call=True,
    running=[
        (
            Output("model-training-configure-modal-train-button", "disabled"),
            True,
            False,
        ),
        (
            Output(
                "model-training-configure-modal-train-button-spinner", "spinner_style"
            ),
            {"display": "inline-block"},
            {"display": "none"},
        ),
    ],
)
def start_model_training(
    n_clicks: int,
    user_store: dict,
    project_store: dict,
    model_type: str,
    training_regime: str,
    encoded_training_data: str,
    row_data: list,
    toast_children: list,
):
    """Start model training process

    Extract data from table and inputs, format and send to BE to initiate training process.
    """
    headers = User.get_headers_from_store(user_store)
    project_name = project_store["project_name"]
    toast_manager = ToastManager(toast_children)

    errors = []

    # Extract, validate, and format data to be sent to BE
    timestep_rows = [row for row in row_data if row["is_timestep"]]
    timestep_column = None if not timestep_rows else timestep_rows[0]["column_name"]

    timeset_rows = [row for row in row_data if row["is_timeset"]]
    timeset_column = None if not timeset_rows else timeset_rows[0]["column_name"]

    # Validate that if timestep is selected, timeset should also be selected (i.e. check XOR returns true)
    if (timeset_column is None) ^ (timestep_column is None):
        errors.append(
            f"Please ensure to define timeset if you have selected timestep, and vice versa."
        )

    column_mapping: dict = {}
    missing_columns: list = []
    for row in row_data:
        if not row["var_id"] and not row["is_timestep"] and not row["is_timeset"]:
            missing_columns.append(row["column_name"])
        # Exclude time related columns from column mapping
        if row["is_timestep"] or row["is_timeset"]:
            continue
        column_mapping[row["column_name"]] = row["var_id"]

    if missing_columns:
        errors.append(
            f"Please ensure all columns are mapped. Missing columns: {missing_columns}"
        )

    if not encoded_training_data:
        errors.append("Please upload training data.")

    if errors:
        toast = toast_manager.make_toast(
            errors,
            "warning",
        )
        toast_manager.add_toast(toast)
        return no_update, toast_manager.toasts

    # Strip data prefix from data
    content_type, content_string = encoded_training_data.split(",")

    data = {
        "model_type": model_type,
        "training_regime": training_regime,
        "training_data": content_string,
        "csvCol2varUUID": column_mapping,
        "timestep_col": timestep_column,
        "timeset_col": timeset_column,
    }

    print("---------------------------------------")
    print("SurrogateTrainingConfig sent to BE = ")
    print(data, flush=True)
    print("---------------------------------------")
    print(
        f"/plant-configurations/{project_name}/model-setup/training-setup", flush=True
    )

    # Send data to BE
    response, success = api.post(
        f"/plant-configurations/{project_name}/model-setup/training-setup",
        data,
        headers,
    )

    if not success:
        print(f"Failed to start model training | {response['error']}", flush=True)
        toast = toast_manager.make_toast(
            [
                "Server error: Failed to start model training. Please try again at a later time."
            ],
            "warning",
        )
        toast_manager.add_toast(toast)
        return no_update, toast_manager.toasts

    toast = toast_manager.make_toast(
        ["Model training started successfully!"],
        "success",
    )
    toast_manager.add_toast(toast)

    return False, toast_manager.toasts


# TODO Training results modal with graphs and text
@callback(
    Output("model-training-results-modal", "is_open", allow_duplicate=True),
    Output("model-setup-toast-container", "children", allow_duplicate=True),
    Output("model-training-results-container", "children", allow_duplicate=True),
    Output("model-setup-results-store", "data", allow_duplicate=True),
    Input("model-training-results-button", "n_clicks"),
    State("auth-store", "data"),
    State("model-setup-project-store", "data"),
    State("model-setup-toast-container", "children"),
    prevent_initial_call=True,
)
def open_model_training_results_modal(
    n_clicks: int,
    user_store: dict,
    project_store: dict,
    toast_children: list,
):
    """Opens Model Training Results modal"""
    if not n_clicks:
        return no_update, no_update, no_update

    headers = User.get_headers_from_store(user_store)
    project_name = project_store["project_name"]
    toast_manager = ToastManager(toast_children)
    results_store = {}

    # Get results from BE
    response, success = api.get(
        f"/plant-configurations/{project_name}/model-setup/training-results",
        headers=headers,
    )
    if not success:
        print(f"Failed to get model training results | {response['error']}", flush=True)
        toast = toast_manager.make_toast(
            ["Failed to get model training results. Please try again later."], "warning"
        )
        toast_manager.add_toast(toast)
        return no_update, toast_manager.toasts, no_update, results_store

    data = response["data"]

    # NOTE For loading mock data
    # data = load_mock_data()
    # print(f"Surrogate model training results: {data}", flush=True)

    # Transform plot1 data into bar chart
    bar_chart_data = []
    plot1_data = data["plot1"]
    for entity_name, variables in plot1_data["pri_data"].items():
        for var_name, points in variables.items():
            data_to_add = [
                TrainingResultGraph(
                    entity_name=entity_name,
                    var_name=var_name,
                    variable=f"{entity_name} {var_name}",
                    timestep=i + 1,
                    value=points[i],
                )
                for i in range(len(points))
            ]
            bar_chart_data += data_to_add

    bar_chart_df = TrainingResultGraph.to_df(bar_chart_data)
    bar_chart = generate_horizontal_bar_chart(
        df=bar_chart_df,
        x=TrainingResultGraph.variable_title,
        y=TrainingResultGraph.value_title,
        title=plot1_data["title"],
        x_axis_title=plot1_data["axes"][0],
        y_axis_title=plot1_data["axes"][1],
        orientation="v",
    )

    print(f"plot1 store data: {bar_chart_data}", flush=True)

    # Transform plot2 data into line chart
    filter_options = []
    line_chart_data = []
    plot2_data = data["plot2"]
    for entity_name, variables in plot2_data["pri_data"].items():
        for var_name, points in variables.items():
            filter_options.append(f"{entity_name} {var_name}")
            data_to_add = [
                TrainingResultGraph(
                    entity_name=entity_name,
                    var_name=var_name,
                    variable=f"{entity_name} {var_name}",
                    timestep=i + 1,
                    value=points[i],
                )
                for i in range(len(points))
            ]
            line_chart_data += data_to_add

    line_chart_df = TrainingResultGraph.to_df(line_chart_data)
    line_chart = generate_line_chart(
        df=line_chart_df,
        x=TrainingResultGraph.timestep_title,
        y=TrainingResultGraph.value_title,
        color=TrainingResultGraph.variable_title,
        title=plot2_data["title"],
        x_axis_title=plot2_data["axes"][0],
        y_axis_title=plot2_data["axes"][1],
    )

    print(f"plot2 store data: {line_chart_data}", flush=True)

    results_store["bar_chart"] = {
        "data": TrainingResultGraph.hydrate(bar_chart_data),
        "title": plot1_data["title"],
        "x_axis_title": plot1_data["axes"][0],
        "y_axis_title": plot1_data["axes"][1],
    }
    results_store["line_chart"] = {
        "data": TrainingResultGraph.hydrate(line_chart_data),
        "title": plot2_data["title"],
        "x_axis_title": plot2_data["axes"][0],
        "y_axis_title": plot2_data["axes"][1],
    }

    contents = [
        html.H3(children=data["section1_header"], className="my-2 fw-bold"),
        html.H4(children=data["section1_body"], className="my-3"),
        html.H3(children=data["section2_header"], className="my-2 fw-bold"),
        html.H4(children=data["section2_body"], className="my-3"),
        html.Div(
            [
                html.H5("Filters: ", className="px-2 mt-2"),
                html.Div(
                    Dropdown(
                        id="model-setup-results-filter",
                        options=filter_options,
                        multi=True,
                    ),
                    className="w-100",
                ),
            ],
            className="d-flex align-items-start my-2",
        ),
        html.Div(
            id="model-setup-results-bar-chart",
            children=Graph(figure=bar_chart, className="rounded-4 overflow-hidden"),
            className="p-2 my-2",
        ),
        html.Div(
            id="model-setup-results-line-chart",
            children=Graph(figure=line_chart, className="rounded-4 overflow-hidden"),
            className="p-2 my-2",
        ),
    ]
    return True, toast_manager.toasts, contents, results_store


@callback(
    Output("model-setup-results-bar-chart", "children"),
    Output("model-setup-results-line-chart", "children"),
    Input("model-setup-results-filter", "value"),
    State("model-setup-results-store", "data"),
    prevent_initial_call=True,
)
def filter_charts(
    values: list,
    results_store: dict,
):
    bar_chart_data = TrainingResultGraph.bootstrap(results_store["bar_chart"]["data"])
    line_chart_data = TrainingResultGraph.bootstrap(results_store["line_chart"]["data"])

    bar_chart_df = TrainingResultGraph.to_df(bar_chart_data)
    line_chart_df = TrainingResultGraph.to_df(line_chart_data)

    # Filter by values. If no values given, show all by default.
    if values:
        bar_chart_df = bar_chart_df[
            bar_chart_df[TrainingResultGraph.variable_title].isin(values)
        ]
        line_chart_df = line_chart_df[
            line_chart_df[TrainingResultGraph.variable_title].isin(values)
        ]

    bar_chart = generate_horizontal_bar_chart(
        df=bar_chart_df,
        x=TrainingResultGraph.variable_title,
        y=TrainingResultGraph.value_title,
        title=results_store["bar_chart"]["title"],
        x_axis_title=results_store["bar_chart"]["x_axis_title"],
        y_axis_title=results_store["bar_chart"]["y_axis_title"],
        orientation="v",
    )
    line_chart = generate_line_chart(
        df=line_chart_df,
        x=TrainingResultGraph.timestep_title,
        y=TrainingResultGraph.value_title,
        color=TrainingResultGraph.variable_title,
        title=results_store["line_chart"]["title"],
        x_axis_title=results_store["line_chart"]["x_axis_title"],
        y_axis_title=results_store["line_chart"]["y_axis_title"],
    )

    return Graph(figure=bar_chart), Graph(figure=line_chart)


def load_mock_data() -> dict:
    from backend.endpoint.v1.schema.config_dtos import SurrogateTrainingResults

    return SurrogateTrainingResults().model_dump()


@callback(
    Output("model-training-results-modal", "is_open", allow_duplicate=True),
    Input("model-training-results-modal-close-button", "n_clicks"),
    prevent_initial_call=True,
)
def close_model_training_results_modal(n_clicks: int):
    """Closes Model Training Results modal"""
    if n_clicks:
        return False
    return no_update
