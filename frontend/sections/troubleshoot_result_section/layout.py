from functools import reduce
from pprint import pprint
from typing import Any, Dict, List
from dash.development.base_component import Component
from dash import (
    html,
    dcc,
    callback,
    Input,
    Output,
    State,
    no_update,
    clientside_callback,
)
import pandas as pd
from collections import defaultdict

from frontend.components.base_container import Container
from frontend.components.base_graph import Graph
from frontend.components.base_dropdown import Dropdown
from frontend.components.base_progress_bar import ProgressBar
from frontend.components.base_pagination import Pagination
from frontend.components.base_card import Card
from frontend.components.top_n_variables_table import top_n_variables_table
from frontend.utils.uuid_service import UUIDService
from frontend.utils.graph_functions import (
    generate_pie_chart,
    generate_horizontal_bar_chart,
)


default_cards_per_page = 3


def troubleshoot_result_section() -> Component:
    return html.Div(
        [
            Container(
                children=[
                    html.H2("Diagnosis Report", className="my-2 fw-semibold"),
                    html.H3("Summary", className="mt-4 mb-2 fw-bold"),
                    html.H4("", id="troubleshoot-summary-text", className="my-3"),
                    html.H4("", id="troubleshoot-top-summary-text", className="my-3"),
                    html.Div(
                        [
                            html.Div(
                                Graph(
                                    id="troubleshoot-pie-chart",
                                    style={"height": "400px"},
                                    # className="rounded-graph",
                                    className="rounded-4 overflow-hidden",
                                ),
                                className="w-100 p-2",
                            ),
                            top_n_variables_table("troubleshoot-top-n-variables-table"),
                        ],
                        className="d-flex",
                    ),
                ]
            ),
            Container(
                children=[
                    html.H3("Assessment", className="my-2 fw-bold"),
                    html.H4(
                        "",
                        id="troubleshoot-assessment-text",
                        className="my-3",
                    ),
                    html.Div(
                        id="troubleshoot-result-progress-bar-container",
                        className="position-relative pt-3 pb-5 d-none",
                    ),
                    html.H4("", id="sensitivity-analysis-text", className="my-3"),
                    html.Div(
                        [
                            html.H5("Filters: ", className="px-2 mt-2"),
                            html.Div(
                                [
                                    Dropdown(
                                        id="troubleshoot-entity-filter",
                                        className="m-0 h5",
                                        placeholder="Equipment/ Stream",
                                        multi=True,
                                    ),
                                    Dropdown(
                                        id="troubleshoot-variable-filter",
                                        className="m-0 h5",
                                        multi=True,
                                        placeholder="Variables",
                                    ),
                                ],
                                className="d-flex flex-column px-2 w-100 gap-2",
                            ),
                        ],
                        className="d-flex align-items-start my-2",
                    ),
                    html.Div(
                        [
                            Graph(
                                id="troubleshoot-setpoints-bar-chart",
                                style={"height": "500px"},
                                className="rounded-4 overflow-hidden w-100",
                            ),
                            Graph(
                                id="troubleshoot-conditions-bar-chart",
                                style={"height": "500px"},
                                className="rounded-4 overflow-hidden w-100",
                            ),
                        ],
                        className="d-flex p-2 my-2 gap-3",
                    ),
                ]
            ),
            Container(
                children=[
                    html.H3("Diagnosis", className="my-2 fw-bold"),
                    html.Div(
                        [
                            html.Div(
                                [], id="troubleshoot-cards", className="d-flex gap-3"
                            ),
                            html.Div(
                                Pagination(
                                    id="troubleshoot-cards-pagination",
                                    max_value=0,
                                    previous_next=True,
                                    fully_expanded=False,
                                    class_name="custom-pagination py-2 user-select-none",
                                ),
                                className="d-flex justify-content-end",
                            ),
                        ],
                        className="py-2",
                    ),
                ]
            ),
            dcc.Store(id="troubleshoot-result-store"),
        ],
        id="troubleshoot-result-section",
        style={"display": "none"},
    )


clientside_callback(
    """
    function(style) {
        const target = document.getElementById('troubleshoot-result-section');
        if (target) {
            target.scrollIntoView({ behavior: 'smooth' });
        }
        return null;
    }
    """,
    Input("troubleshoot-result-section", "style"),
)


@callback(
    Output("troubleshoot-summary-text", "children"),
    Output("troubleshoot-top-summary-text", "children"),
    Output("troubleshoot-pie-chart", "figure"),
    Output("troubleshoot-top-n-variables-table", "rowData"),
    Output("troubleshoot-assessment-text", "children"),
    Output("troubleshoot-result-progress-bar-container", "children"),
    Output("sensitivity-analysis-text", "children"),
    Output("troubleshoot-entity-filter", "options"),
    Output("troubleshoot-entity-filter", "value"),
    Output("troubleshoot-variable-filter", "options"),
    Output("troubleshoot-result-section", "style", allow_duplicate=True),
    Output("troubleshoot-top-n-variables-table", "columnSize"),
    Output("troubleshoot-cards", "children"),
    Output("troubleshoot-cards-pagination", "max_value"),
    Input("troubleshoot-result-store", "modified_timestamp"),
    State("troubleshoot-result-store", "data"),
    prevent_initial_call=True,
)
def display_troubleshoot_results(ts, store):
    """Display results section and reset filters"""

    print("troubleshoot-result-store")
    pprint(store)

    # Generate pie chart
    pie_chart_data = store["top_impact"]
    pie_chart_df = pd.DataFrame(
        list(pie_chart_data.items()), columns=["variable", "weightage"]
    )

    print(pie_chart_df)
    pie_chart = generate_pie_chart(
        pie_chart_df, "variable", "weightage", f"Top {len(pie_chart_df)-1} Variable(s)"
    )

    # Generate top N variables table
    top_n_variables_row_data = [
        {
            "id": UUIDService.generate_id(),
            "entity": var["entity"],
            "type": var["type"],
            "variable": var["name"],
            "value": round(var["value"], 2),
            "unit": var["unit"],
        }
        for var in store["top_variables"]
    ]

    # Generate assessment progress bar
    current_kpi = store["assessment"]["current_kpi"]
    expected_kpi = store["assessment"]["expected_kpi"]
    upper_bound = store["assessment"]["upper_bound"]
    lower_bound = store["assessment"]["lower_bound"]
    current_kpi_percent = round(
        (current_kpi - lower_bound) * 100 / (upper_bound - lower_bound)
    )
    expected_kpi_percent = round(
        (expected_kpi - lower_bound) * 100 / (upper_bound - lower_bound)
    )
    asesssment_children = [
        ProgressBar(
            [
                ProgressBar(
                    value=current_kpi_percent,
                    bar=True,
                    color="danger",
                ),
                ProgressBar(
                    value=expected_kpi_percent - current_kpi_percent,
                    bar=True,
                    color="success",
                ),
            ],
            style={"height": "50px"},
            className="rounded-4 position-relative",
        ),
        html.Span(
            f"{current_kpi_percent}%",
            className="position-absolute text-center fw-bold",
            style={"left": f"{current_kpi_percent + 0.5}%", "top": "30px"},
        ),
        html.Span(
            f"{expected_kpi_percent}%",
            className="position-absolute text-center fw-bold",
            style={"left": f"{expected_kpi_percent + 0.5}%", "top": "30px"},
        ),
        html.H5(
            "Current Perfomance",
            className="position-absolute text-center",
            style={
                "left": f"{current_kpi_percent}%",
                "top": "80px",
                "transform": "translateX(-50%)",
            },
        ),
        html.H5(
            "Expected Perfomance",
            className="position-absolute text-center",
            style={
                "left": f"{expected_kpi_percent}%",
                "top": "80px",
                "transform": "translateX(-50%)",
            },
        ),
    ]

    # Generate entity filter
    entity_filter = list(store["entity_variable_mapping"].keys())
    default_entity = []

    # Generate variable filter
    variable_filter = generate_variable_filter_options(default_entity, store)

    # Generate diagnosis cards
    raw_card_data = store["analysis"]
    num_card_pages = -(
        -len(raw_card_data) // default_cards_per_page
    )  # Ceiling function
    cards = generate_diagnosis_cards(
        raw_card_data[:default_cards_per_page]
    )  # Show first page

    return (
        store["main_summary_text"],
        store["top_summary_text"],
        pie_chart,
        top_n_variables_row_data,
        store["assessment_summary"],
        asesssment_children,
        store["sensitivity_analysis_text"],
        entity_filter,
        default_entity,
        variable_filter,
        {"display": "block"},
        "sizeToFit",
        cards,
        num_card_pages,
    )


@callback(
    Output("troubleshoot-cards", "children", allow_duplicate=True),
    Input("troubleshoot-cards-pagination", "active_page"),
    State("troubleshoot-result-store", "data"),
    prevent_initial_call=True,
)
def goto_page(page, store):
    """Update diagnosis card container to display the right cards depending on the page"""

    raw_card_data = store["analysis"]
    start_index = (page - 1) * default_cards_per_page
    end_index = start_index + default_cards_per_page
    return generate_diagnosis_cards(raw_card_data[start_index:end_index])


@callback(
    Output("troubleshoot-setpoints-bar-chart", "figure", allow_duplicate=True),
    Output("troubleshoot-conditions-bar-chart", "figure", allow_duplicate=True),
    Input("troubleshoot-variable-filter", "value"),
    State("troubleshoot-entity-filter", "value"),
    State("troubleshoot-result-store", "data"),
    prevent_initial_call=True,
)
def filter_horizontal_bar_chart_by_variable(selected_variables, selected_entity, store):
    """Apply filter on Setpoints and Conditions horizontal bar charts by variable"""

    setpoints_df = generate_horizontal_bar_chart_data(
        selected_variables,
        selected_entity,
        store["setpoint_impact_summary"],
        "setpoint",
    )
    y_labels = generate_labels_with_units(setpoints_df, "setpoint", "unit")
    setpoints_bar_chart = generate_horizontal_bar_chart(
        setpoints_df, "weightage", "setpoint", "Setpoints", "#1A2B5F", y_labels
    )

    conditions_df = generate_horizontal_bar_chart_data(
        selected_variables,
        selected_entity,
        store["condition_impact_summary"],
        "condition",
    )
    y_labels = generate_labels_with_units(conditions_df, "condition", "unit")
    conditions_bar_chart = generate_horizontal_bar_chart(
        conditions_df, "weightage", "condition", "Conditions", "#2186F4", y_labels
    )
    print("Filter by Variable: ")
    print(setpoints_df)
    print(conditions_df)

    return setpoints_bar_chart, conditions_bar_chart


@callback(
    Output("troubleshoot-setpoints-bar-chart", "figure", allow_duplicate=True),
    Output("troubleshoot-conditions-bar-chart", "figure", allow_duplicate=True),
    Output("troubleshoot-variable-filter", "value", allow_duplicate=True),
    Output("troubleshoot-variable-filter", "options", allow_duplicate=True),
    Input("troubleshoot-entity-filter", "value"),
    State("troubleshoot-result-store", "data"),
    prevent_initial_call=True,
)
def filter_horizontal_bar_chart_by_entity(selected_entity, store):
    """Apply filter on Setpoints and Conditions horizontal bar charts by entity
    Every time user selects an entity, variable filter is cleared and its options are updated
    """

    setpoints_df = generate_horizontal_bar_chart_data(
        [], selected_entity, store["setpoint_impact_summary"], "setpoint"
    )
    y_labels = generate_labels_with_units(setpoints_df, "setpoint", "unit")
    setpoints_bar_chart = generate_horizontal_bar_chart(
        setpoints_df, "weightage", "setpoint", "Setpoints", "#1A2B5F", y_labels
    )

    conditions_df = generate_horizontal_bar_chart_data(
        [], selected_entity, store["condition_impact_summary"], "condition"
    )
    y_labels = generate_labels_with_units(conditions_df, "condition", "unit")
    conditions_bar_chart = generate_horizontal_bar_chart(
        conditions_df, "weightage", "condition", "Conditions", "#2186F4", y_labels
    )

    variable_filter_options = generate_variable_filter_options(selected_entity, store)
    return setpoints_bar_chart, conditions_bar_chart, [], variable_filter_options


def generate_diagnosis_cards(card_data):
    """Generate list of card components to display diagnosis cards"""

    cards = []
    for diagnosis in card_data:
        diagnosis_text = [
            i for t in diagnosis["diagnosis_text"].split("\n") for i in [t, html.Br()]
        ]
        card = Card(
            [
                html.Div(
                    [
                        html.H3(diagnosis["entity"], className="fw-bold m-0"),
                        html.H5(
                            f"{diagnosis['name']} ({diagnosis['unit']})",
                            className="m-0 text-wrap fw-bold",
                        ),
                    ],
                    className="d-flex justify-content-between align-items-center mb-3",
                ),
                html.Div(
                    [
                        html.H5("Recommendation", className="m-0"),
                        html.Div(
                            [
                                html.H5(
                                    diagnosis["current_value"],
                                    className="text-danger border rounded-3 p-2 m-0",
                                ),
                                html.I(className="fa-solid fa-arrow-right"),
                                html.H5(
                                    diagnosis["recommended_value"],
                                    className="text-success border rounded-3 p-2 m-0",
                                ),
                            ],
                            className="d-flex gap-2 align-items-center",
                        ),
                    ],
                    className="d-flex justify-content-between align-items-center",
                ),
                html.Hr(className="text-info"),
                html.H3("Diagnosis", className="fw-bold mb-3"),
                html.H4(diagnosis_text),
            ],
            className="p-4 rounded-4 bg-secondary border-0",
            style={"width": f"{round(100/default_cards_per_page) - 0.5}%"},
        )
        cards.append(card)

    return cards


def generate_horizontal_bar_chart_data(
    selected_variables, selected_entity, data, variable_col
):
    df = pd.DataFrame(data, columns=["entity", variable_col, "weightage", "unit"])

    if df.empty:
        return df

    if selected_entity:
        df = df[df["entity"].isin(selected_entity)]

    # Filter by variables. No variables passed means no variables are filtered.
    if selected_variables:
        df = df[df[variable_col].isin(selected_variables)]

    return df


def generate_variable_filter_options(
    selected_entity: List[str],
    store: Dict[str, Any],
) -> List[str]:
    variable_filter_options = []
    if selected_entity:
        for entity in selected_entity:
            variable_filter_options.extend(store["entity_variable_mapping"][entity])
    else:
        variable_filter_options = reduce(
            lambda x, y: x + y, list(store["entity_variable_mapping"].values()), []
        )

    return variable_filter_options


def generate_labels_with_units(df: pd.DataFrame, label_col: str, unit_col: str):
    return list(df[label_col] + " (" + df[unit_col] + ")")


# TODO: [REMOVE] Test display experiment result
# @callback(
#     Output("troubleshoot-result-store", "data"),
#     Input("troubleshoot-result-section", "children"),
# )
# def test(_):
#     import json
#     from datetime import datetime

#     with open(
#         "frontend/sections/troubleshoot_setup_section/mock_troubleshoot_results.json"
#     ) as f:
#         response = json.load(f)
#         success = True

#     data = response["data"]["data"]
#     print("--------------------------------------------")
#     print("Loaded JSON Data = ")
#     print(data)

#     # Generate entity variable mapping for entity and variable filters
#     entity_variable_mapping = defaultdict(set)
#     for sp in data["setpoint_impact_summary"]:
#         sp["setpoint"] = generate_var_label(sp["entity"], sp["setpoint"])
#         entity_variable_mapping[sp["entity"]].add(sp["setpoint"])

#     for cd in data["condition_impact_summary"]:
#         cd["condition"] = generate_var_label(cd["entity"], cd["condition"])
#         entity_variable_mapping[cd["entity"]].add(cd["condition"])

#     entity_variable_mapping = {k: list(v) for k, v in entity_variable_mapping.items()}

#     data["entity_variable_mapping"] = entity_variable_mapping

#     data["timestamp"] = datetime.now()

#     print("store on page load = ")
#     pprint(data)

#     return data


# def generate_var_label(entity: str, var: str) -> str:
#     return f"{entity} - {var}"
