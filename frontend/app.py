"""
 CREDIT: This code was originally adapted for Pages  based on <PERSON><PERSON>'s  article:
   https://dev.to/naderelshehabi/securing-plotly-dash-using-flask-login-4ia2
   https://github.com/naderelshehabi/dash-flask-login

   This version is updated by Dash community member @jinnyzor For more info see:
   https://community.plotly.com/t/dash-app-pages-with-flask-login-flow-using-flask/69507

For other Authentication options see:
  Dash Enterprise:  https://dash.plotly.com/authentication#dash-enterprise-auth
  Dash Basic Auth:  https://dash.plotly.com/authentication#basic-auth

"""

import logging
import os
from functools import wraps
from os import environ as env
from urllib.parse import quote_plus, urlencode

import dash
from dash import html, CeleryManager
import dash_bootstrap_components as dbc
import dash_cytoscape as cyto
from authlib.integrations.flask_client import OAuth
from dash import html, dcc, Output, Input, no_update
from dotenv import find_dotenv, load_dotenv
from flask import Flask, jsonify, redirect, request, session, url_for
from datetime import datetime

from frontend.utils.api_service import APIServiceProvider
from frontend.utils.auth import User
from celery import Celery


celery_app = Celery(
    __name__, broker=os.getenv("REDIS_URL"), backend=os.getenv("REDIS_URL")
)
background_callback_manager = CeleryManager(celery_app)

cyto.load_extra_layouts()

ENV_FILE = find_dotenv()
if ENV_FILE:
    load_dotenv()
else:
    logging.warning("No .env file found")

api = APIServiceProvider().get_api_service()

# Exposing the Flask Server to enable configuring it for logging in
server = Flask(
    __name__,
    static_folder="assets",
)

server.secret_key = env.get("APP_SECRET_KEY", "")
server.config["AUTH0_DOMAIN"] = env.get("AUTH0_DOMAIN", "")

oauth = OAuth(server)

auth0 = oauth.register(
    "auth0",
    client_id=env.get("AUTH0_CLIENT_ID", ""),
    client_secret=env.get("AUTH0_CLIENT_SECRET", ""),
    api_base_url=f'https://{server.config["AUTH0_DOMAIN"]}',
    access_token_url=f'https://{server.config["AUTH0_DOMAIN"]}/oauth/token',
    authorize_url=f'https://{server.config["AUTH0_DOMAIN"]}/authorize',
    client_kwargs={"scope": "openid profile email"},
    server_metadata_url=f'https://{server.config["AUTH0_DOMAIN"]}/.well-known/openid-configuration',
)


@server.before_request
def check_login():
    if request.path in [
        "/plant-configurations",
        "/experiment",
        "/diagnosis",
        "/account",
    ]:
        if "profile" in session:
            return
        else:
            return jsonify({"status": "401", "statusText": "unauthorized access"})
    return


@server.route("/callback", methods=["GET", "POST"])
def callback():
    token = auth0.authorize_access_token()
    session["token"] = token
    resp = auth0.get("userinfo")
    user_info = resp.json()
    session["profile"] = {
        "user_id": user_info["sub"],
        "name": user_info["name"],
        "picture": user_info["picture"],
        "email": user_info["email"],
    }
    return redirect("/plant-configurations")


@server.route("/")
@server.route("/login")
def login():
    return auth0.authorize_redirect(
        redirect_uri=url_for("callback", _external=True, _scheme="http")
    )  # TODO change back to https once domain name is set


@server.route("/logout", methods=["GET"])
def logout():
    session.clear()
    return redirect(
        "https://"
        + server.config["AUTH0_DOMAIN"]
        + "/v2/logout?"
        + urlencode(
            {
                "returnTo": url_for(
                    "login", _external=True, _scheme="http"
                ),  # TODO change back to https once domain name is set
                "client_id": env.get("AUTH0_CLIENT_ID"),
            },
            quote_via=quote_plus,
        )
    )


app = dash.Dash(
    __name__,
    server=server,
    use_pages=True,
    # suppress_callback_exceptions=True,
    external_stylesheets=[
        "https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap",
        "https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap",
    ],
    update_title=None,
    background_callback_manager=background_callback_manager,
)


app.layout = html.Div(
    [
        dash.page_container,
        dcc.Location("url"),
        dcc.Store(
            id="auth-store",
            data={
                "user_id": None,
            },
        ),
    ],
    id="app",
)


# Add callback to update store when user logs in
@app.callback(Output("auth-store", "data"), Input("url", "href"))
def update_user_store(_):
    """Update global user store with session data"""
    if "profile" in session:
        user = User(
            user_id=session["profile"]["user_id"],
            name=session["profile"]["name"],
            email=session["profile"]["email"],
            datetime=datetime.now(),
        )
        return user.push_user_to_store()
    return no_update


if os.getenv("DEBUG", "false").lower() != "false":
    app.enable_dev_tools(debug=True)


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,  # Set the logging level
        format="%(asctime)s - %(levelname)s - %(message)s",
    )
    app.run(
        host="0.0.0.0",
        port=env.get("PORT", "8050"),
        debug=os.getenv("DEBUG", "false").lower() != "false",
        dev_tools_hot_reload_watch_interval=1,
    )
