from typing import List, Dict, Optional
import uuid


class Equipment:
    def __init__(
        self,
        id: Optional[str],
        type: str,
        condition: int,
        advanced_params: List[Dict[str, str]],
        advanced_constraints: List[Dict[str, str]],
    ) -> None:
        self.id = id or uuid.uuid4()
        self.type = type
        self.condition = condition
        self.advanced_params = advanced_params
        self.advanced_constraints = advanced_constraints
