# gunicorn config file

bind = "0.0.0.0:8050"

timeout = 6000

chdir = "/code"

# Number of workers. Each worker takes a request
workers = 1

# Use gevent workers for async handling
worker_class = "gevent"

# loglevel = "debug"  # Set log level to debug

# # Logging configurations
# accesslog = "/code/gunicorn_access.log"  # Path to access log file
# errorlog = "/code/gunicorn_error.log"  # Path to error log file
