import dash
from dash import html
from frontend.components.navbar import navbar
from frontend.sections.calibration_setup_section.layout import calibration_setup_section
from frontend.sections.calibration_result_section.layout import (
    calibration_result_section,
)

dash.register_page(
    module=__name__,
    path_template="/calibration",
    title="Calibration",
)


def layout(**kwargs):
    return html.Div(
        [navbar(), calibration_setup_section(), calibration_result_section()],
        style={"margin-left": "17rem"},
        className="px-3",
    )
