import dash
from dash import html
from frontend.components.navbar import navbar
from frontend.sections.network_graph_section.layout import graph_section
from frontend.sections.model_setup_section.layout import model_setup_section
from frontend.sections.sensor_section.layout import sensor_section
from frontend.sections.kpi_section.layout import kpi_section

dash.register_page(
    module=__name__,
    path_template="/plant-configurations/<project_name>",
    title="Plant Configuration",
)


def layout(project_name: str = "", **kwargs):
    return html.Div(
        [
            navbar(),
            graph_section(project_name=project_name),
            model_setup_section(project_name=project_name),
            sensor_section(project_name=project_name),
            kpi_section(project_name=project_name),
        ],
        style={"margin-left": "17rem"},
        className="px-3",
    )
