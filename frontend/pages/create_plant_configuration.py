import dash
from dash import html, Input, Output, callback, State, no_update, dcc, page_registry
from frontend.components.navbar import navbar
from frontend.components.base_button import Button
from frontend.components.base_readme import ReadmeAIO
from frontend.components.base_table import BaseTable
from frontend.components.table_block import TableBlock
from frontend.components.base_modal import Modal
from frontend.components.base_modal_body import ModalBody
from frontend.components.base_modal_footer import ModalFooter
from frontend.components.base_label import Label
from frontend.components.base_input import InputField
from frontend.components.base_toast import Toast
from frontend.utils.api_service import APIServiceProvider
from frontend.utils.url_encoder_service import UrlEncoderService
from frontend.utils.uuid_service import UUIDService
from frontend.utils.toast_manager import ToastManager
from frontend.utils.auth import User

from datetime import datetime, timezone
from typing import Dict, List, Any

dash.register_page(
    module=__name__,
    path="/plant-configurations",
    title="Create Plant Configuration",
)

api = APIServiceProvider().get_api_service()


column_defs = [
    # columns: id, name, date_created, date_updated, configure, delete, is_template
    {
        "headerName": "Project Name",
        "field": "name",
        "cellClass": "d-flex align-items-center",  # Vertically align table cell content
        "sortable": True,
    },
    {
        "headerName": "Date Created",
        "field": "date_created",
        "cellClass": "d-flex align-items-center",
        "sortable": True,
    },
    {
        "headerName": "Date Updated",
        "field": "date_updated",
        "cellClass": "d-flex align-items-center",
        "sortable": True,
        "sort": "desc",
    },
    {
        "headerName": "",
        "field": "configure",
        "cellRenderer": "Button",
        "cellRendererParams": {
            "color": "primary",
            "iconClass": "fa-solid fa-gear",
            "buttonClass": "btn-sm text-light bg-transparent border-0",
            "text": "",
        },
        # "autoHeight": True,
        "cellClass": "d-flex align-items-center justify-content-center",
        "maxWidth": 40,
    },
    {
        "headerName": "",
        "field": "delete",
        "cellRenderer": "Button",
        "cellRendererParams": {
            "color": "primary",
            "iconClass": "fa-solid fa-trash-can",
            "buttonClass": "btn-sm text-danger bg-transparent border-0",
            "text": "",
        },
        # "autoHeight": True,
        "cellClass": "d-flex align-items-center justify-content-center",
        "maxWidth": 40,
    },
]

grid_options = {"rowHeight": 60}


def layout():
    table = BaseTable(
        id="create-plant-configuration-table",
        column_defs=column_defs,
        row_data=[],
        editable=False,
        grid_options=grid_options,
    )
    block = TableBlock(
        "Select Project to Work On",
        table=table,
        has_del_button=False,
        has_add_button=True,
    )
    return html.Div(
        [
            dcc.Location("url"),
            navbar(),
            html.H2("Plant Configuration", className="text-dark fw-semibold h1"),
            block.render(),
            Modal(
                id="create-plant-configuration-modal",
                children=[
                    ModalBody(
                        html.Div(
                            [
                                Label(
                                    "Name Your Project",
                                    className="text-primary fs-4 fw-semibold",
                                ),
                                InputField(
                                    type="text",
                                    id="create-plant-configuration-name",
                                    placeholder="Process Model",
                                ),
                            ],
                            className="d-flex flex-column align-items-center rounded-4 bg-secondary p-4 w-75 mx-auto",
                        )
                    ),
                    ModalFooter(
                        [
                            Button(
                                id="create-plant-configuration-modal-cancel",
                                children=[
                                    html.I(className="fa-solid fa-xmark me-2"),
                                    "Cancel",
                                ],
                                className="bg-secondary text-primary rounded-3",
                            ),
                            Button(
                                id="create-plant-configuration-modal-submit",
                                children=[
                                    html.I(className="fa-solid fa-check me-2"),
                                    "Confirm",
                                ],
                                className="bg-primary text-secondary rounded-3",
                            ),
                        ],
                        className="border-0 d-flex justify-content-center",
                    ),
                ],
                keyboard=False,
                backdrop="static",
            ),
            Toast(
                id="create-plant-configuration-toast",
                is_open=False,
            ),
            html.Div(id="create-plant-configuration-toast-container"),
        ],
        style={"margin-left": "17rem"},
        className="p-4",
    )


@callback(
    Output("create-plant-configuration-modal", "is_open", allow_duplicate=True),
    Input("create-plant-configuration-table-add-button", "n_clicks"),
    prevent_initial_call=True,
)
def open_modal(n_clicks: int) -> bool:
    if n_clicks:
        return True
    return no_update


@callback(
    Output("create-plant-configuration-modal", "is_open", allow_duplicate=True),
    Input("create-plant-configuration-modal-cancel", "n_clicks"),
    prevent_initial_call=True,
)
def close_modal(n_clicks: int) -> bool:
    if n_clicks:
        return False
    return no_update


@callback(
    Output("create-plant-configuration-table", "rowData", allow_duplicate=True),
    Output("create-plant-configuration-name", "value"),
    Output("create-plant-configuration-modal", "is_open"),
    Output(
        "create-plant-configuration-toast-container", "children", allow_duplicate=True
    ),
    Input("create-plant-configuration-modal-submit", "n_clicks"),
    State("create-plant-configuration-name", "value"),
    State("create-plant-configuration-toast-container", "children"),
    State("create-plant-configuration-table", "rowData"),
    State("auth-store", "data"),
    prevent_initial_call=True,
)
def create_plant_configuration(
    n_clicks: int,
    name: str,
    toast_children: List[Dict[str, Any]],
    row_data,
    user_store: Dict[str, Any],
):
    """Creates a new project from modal
    1. Send project name to BE
    2. On response, reset input field in modal and close modal
    3. Refresh projects table
    """
    print()
    print(f"-----------create_plant_configuration Callback----------")
    print(f"inputfield: {name}")

    toast_manager = ToastManager(toast_children)

    # Ensure name is not empty
    if not name:
        new_toast = toast_manager.make_toast(
            [f"Please fill in a name for your new plant configuration."], "warning"
        )
        toast_manager.add_toast(new_toast)
        return no_update, "", True, toast_manager.toasts

    # Check for duplicate plant config names
    duplicates = [row for row in row_data if row["name"] == name]
    if duplicates:
        new_toast = toast_manager.make_toast(
            [f"{name} has already been taken. Please choose another name."], "warning"
        )
        toast_manager.add_toast(new_toast)
        return no_update, "", True, toast_manager.toasts

    current_dt: datetime = datetime.now(timezone.utc)
    current_dt_str = current_dt.isoformat()
    data = {
        "is_template": False,
        "name": name,
        "createdDateTime": current_dt_str,
        "editedDateTime": current_dt_str,
    }
    print(f"data: {data}")

    headers = User.get_headers_from_store(user_store)
    response, success = api.post("/plant-configurations", data=data, headers=headers)
    print(f"response: {response}")
    if not success:
        new_toast = toast_manager.make_toast([f"Failed to add project."], "warning")
        toast_manager.add_toast(new_toast)
        return no_update, "", False, toast_manager.toasts

    response, success = api.get("/plant-configurations", headers=headers)
    if not success:
        new_toast = toast_manager.make_toast(
            [f"Server error. Please try again later."], "warning"
        )
        toast_manager.add_toast(new_toast)
        return no_update, "", False, toast_manager.toasts
    row_data = generate_row_data(response["data"])

    new_toast = toast_manager.make_toast(
        [f"New plant configuration created successfully."], "success"
    )
    toast_manager.add_toast(new_toast)

    return row_data, "", False, toast_manager.toasts


@callback(
    Output("create-plant-configuration-table", "rowData", allow_duplicate=True),
    Output("url", "href", allow_duplicate=True),
    Output(
        "create-plant-configuration-toast-container", "children", allow_duplicate=True
    ),
    Input("create-plant-configuration-table", "cellRendererData"),
    State("create-plant-configuration-table", "rowData"),
    State("create-plant-configuration-toast-container", "children"),
    State("auth-store", "data"),
    prevent_initial_call=True,
)
def configure_or_delete(row, row_data, toast_children, user_store: Dict[str, Any]):
    """Logic for inline configure and delete in projects table"""

    headers = User.get_headers_from_store(user_store)
    print(f"row = {row}")

    toast_manager = ToastManager(toast_children)

    col = row["colId"]
    row_id = row["rowId"]
    row = [row for row in row_data if row["id"] == row_id][0]
    if col == "configure":
        project_name = UrlEncoderService.encode(row["name"])
        print(project_name)
        current_path = page_registry[__name__]["path"]
        redirect_url = f"{current_path}/{project_name}"
        return no_update, redirect_url, no_update
    elif col == "delete":
        if row["is_template"]:
            return no_update, no_update, no_update

        response, success = api.delete(
            f"/plant-configurations/{row['name']}", headers=headers
        )
        if not success:
            new_toast = toast_manager.make_toast(
                [f"Failed to delete project."], "warning"
            )
            toast_manager.add_toast(new_toast)
            return no_update, no_update, toast_manager.toasts

        response, success = api.get("/plant-configurations", headers=headers)
        if not success:
            new_toast = toast_manager.make_toast(
                [f"Server error. Please try again later."], "warning"
            )
            toast_manager.add_toast(new_toast)
            return no_update, no_update, toast_manager.toasts
        row_data = generate_row_data(response["data"])

        new_toast = toast_manager.make_toast(
            [f"{row['name']} deleted successfully."], "success"
        )
        toast_manager.add_toast(new_toast)

        return row_data, no_update, toast_manager.toasts


@callback(
    Output("create-plant-configuration-table", "rowData"),
    Input("auth-store", "modified_timestamp"),
    State("auth-store", "data"),
    prevent_initial_call=True,
)
def retrieve_projects_on_initial_load(ts, user_store: Dict[str, Any]):
    """Calls BE to retrieve user's existing projects and loads them into table"""

    headers = User.get_headers_from_store(user_store)
    response, success = api.get("/plant-configurations", headers=headers)
    if not success:
        return no_update
    row_data = generate_row_data(response["data"])
    return row_data


def generate_row_data(data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    return [
        {
            "id": UUIDService.generate_id(),
            "name": config["name"],
            "date_created": beautify_datetime_str(config["createdDateTime"]),
            "date_updated": beautify_datetime_str(config["editedDateTime"]),
            "configure": "Configure",
            "delete": "Delete",
            "is_template": config["is_template"],
        }
        for config in data
    ]


def beautify_datetime_str(datetime_str: str) -> str:
    date_format = "%Y-%m-%dT%H:%M:%S.%fZ"
    return datetime.strptime(datetime_str, date_format).strftime("%Y-%m-%d %H:%M")
