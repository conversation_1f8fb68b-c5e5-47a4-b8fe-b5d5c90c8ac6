from dash import html, callback, Output, Input, State, MATCH
import dash_bootstrap_components as dbc
from dash.development.base_component import Component
from typing import Union, List
from frontend.components.base_button import Button


class ReadmeAIO(html.Div):
    # A set of functions that create pattern-matching callbacks of the subcomponents
    class ids:
        button = lambda aio_id: {
            "component": "Readme",
            "subcomponent": "button",
            "aio_id": aio_id,
        }
        collapse = lambda aio_id: {
            "component": "Readme",
            "subcomponent": "collapse",
            "aio_id": aio_id,
        }

    # Make the ids class a public class
    ids = ids

    def __init__(
        self,
        aio_id: str,
        header: Union[str, Component, List[Union[str, Component]]],
        content: Union[str, Component, List[Union[str, Component]]],
    ):
        self.aio_id = aio_id
        self.header = header
        self.content = content

        super().__init__(
            [
                Button(
                    children=self.header,
                    id=self.ids.button(aio_id),
                    outline=True,
                    color="primary",
                    className="rounded-3",
                ),  # TODO: Styling to make button look like just a link
                html.Div(
                    [
                        dbc.Collapse(
                            [html.Div(self.content)],
                            id=self.ids.collapse(aio_id),
                            is_open=False,
                            className="py-2",
                        )
                    ]
                ),
            ],
        )

    @callback(
        Output(ids.collapse(MATCH), "is_open", allow_duplicate=True),
        Input(ids.button(MATCH), "n_clicks"),
        State(ids.collapse(MATCH), "is_open"),
        prevent_initial_call=True,
    )
    def toggle_readme(n_clicks: int, is_open: bool) -> bool:
        if n_clicks:
            return not is_open
        return is_open
