import dash_bootstrap_components as dbc
from dash.development.base_component import Component
from typing import List, Union
from frontend.components.base_modal_body import ModalBody
from frontend.components.base_modal_header import ModalHeader
from frontend.components.base_modal_footer import ModalFooter


class Modal(dbc.Modal):
    def __init__(
        self,
        id: str,
        children: List[Union[ModalBody, ModalHeader, ModalFooter]],
        **kwargs,
    ) -> None:
        self.id = id
        self.children = children
        self.className = "rounded-4 "
        kwargs["className"] = self.className + kwargs.get("className", "")
        kwargs["is_open"] = kwargs.get("is_open", None) or False

        super().__init__(children, centered=True, **kwargs)
