from abc import ABC, abstractmethod
from dash.development.base_component import Component
from typing import Optional


class BaseBlock(ABC):
    def __init__(
        self,
        title: Optional[str] = None,
        readme_title: Optional[str] = None,
        readme: Optional[str] = None,
    ) -> None:
        self.title = title
        self.readme = readme
        self.readme_title = readme_title

    @abstractmethod
    def render(self) -> Component:
        pass
