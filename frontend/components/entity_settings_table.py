from dash import html, callback, Input, Output, State, ctx, no_update

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock
from dash.development.base_component import Component


column_defs = [
    # columns: id, name, setpoints, conditions, name_options, entity_type, entity_super_type
    {
        "headerCheckboxSelection": True,  # Enable header checkbox in this column
        "checkboxSelection": True,  # Enable checkboxes for row selection
        "headerName": "",
        "maxWidth": 40,
        "editable": False,
    },
    {
        "headerName": "Equipment/ Stream",
        "field": "name",
        "cellEditor": "agSelectCellEditor",
        "cellEditorParams": {
            "function": "experimentEquipmentOptions(params)",
        },
    },
    {
        "headerName": "Setpoints",
        "field": "setpoints",
        "editable": False,
        "cellRenderer": "Button",
        "cellRendererParams": {
            "color": "primary",
            "iconClass": "fa-solid fa-gear",
            "buttonClass": "btn-sm text-primary bg-transparent border-0",
            "text": "Input Details",
        },
    },
    {
        "headerName": "Conditions",
        "field": "conditions",
        "editable": False,
        "cellRenderer": "Button",
        "cellRendererParams": {
            "color": "primary",
            "iconClass": "fa-solid fa-gear",
            "buttonClass": "btn-sm text-primary bg-transparent border-0",
            "text": "Input Details",
        },
    },
]


def entity_settings_table(id: str) -> Component:
    table = PaginatedTableAIO(id=id, column_defs=column_defs)
    block = TableBlock(
        "Equipment & Stream Settings",
        table=table,
    )

    return html.Div([block.render()], className="w-100")
