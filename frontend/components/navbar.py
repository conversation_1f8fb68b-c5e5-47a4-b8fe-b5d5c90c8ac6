from dash import html, callback, Output, Input, State, dcc, callback, ALL, no_update
import dash_bootstrap_components as dbc
import logging
from typing import Dict, Any

from frontend.utils.auth import User

logger = logging.getLogger()


def navbar():
    return html.Div(
        [
            dcc.Location("url"),
            html.Div(
                html.Img(
                    src="/assets/images/aleph_logo_new.png",
                    className="sidebar-logo",
                ),
                style={"text-align": "left", "background": ""},
            ),
            html.Div(className="horizontal-line mb-4 mt-2"),
            html.Div(
                dbc.Nav(
                    [
                        dbc.NavLink(
                            [
                                html.I(className="fa-solid fa-gears"),
                                "Plant Configuration",
                            ],
                            href="/plant-configurations",
                            active="partial",
                            id="navlink-plant-configuration",
                            className="d-flex align-items-center gap-2",
                        ),
                        dbc.NavLink(
                            [html.I(className="fa-solid fa-file-pen"), "Experiment"],
                            href="/experiment",
                            active="exact",
                            className="d-flex align-items-center gap-2",
                        ),
                        dbc.NavLink(
                            [
                                html.I(className="fa-solid fa-screwdriver-wrench"),
                                "Diagnosis",
                            ],
                            href="/diagnosis",
                            active="exact",
                            className="d-flex align-items-center gap-2",
                        ),
                        dbc.NavLink(
                            [
                                html.I(className="fa-solid fa-sliders"),
                                "Calibration",
                            ],
                            href="/calibration",
                            active="exact",
                            className="d-flex align-items-center gap-2",
                        ),
                    ],
                    vertical=True,
                    pills=True,
                ),
                className="mt-1 poppins-semibold fs-6",
            ),
            html.Div(
                [
                    html.Div(className="horizontal-line"),
                    html.Div(
                        "Support",
                        style={
                            "color": "black",
                            "font-size": "20px",
                            "font-weight": "bold",
                            "margin-bottom": "0.9rem",
                            "margin-left": "0.5rem",
                        },
                        className="poppins-semibold mt-4",
                    ),
                    dbc.NavLink(
                        [
                            html.I(
                                className="fa-solid fa-circle-info me-1",
                                style={"width": "20px"},
                            ),
                            html.Span(
                                "About / License", style={"margin-left": "0.5rem"}
                            ),
                        ],
                        href="/about",
                        active="exact",
                        style={"margin-left": "0.5rem"},
                        className="ms-3 poppins-semibold mt-3",
                    ),
                    dbc.NavLink(
                        [
                            html.I(
                                className="fa-solid fa-gear me-1",
                                style={"width": "20px"},
                            ),
                            html.Span("Account", style={"margin-left": "0.5rem"}),
                        ],
                        href="/account",
                        active="exact",
                        style={"margin-left": "0.5rem", "margin-top": "0.7rem"},
                        className="ms-3 poppins-semibold mt-3",
                    ),
                ],
                style={"margin-bottom": "1rem", "font-weight": "bold"},
            ),
            html.Div(
                [
                    html.Div(className="horizontal-line"),
                    html.Div(
                        [
                            html.Div(
                                "",
                                id="navbar-name",
                                style={"color": "black"},
                                className="poppins-semibold",
                            ),
                            html.Div(
                                "",
                                id="navbar-email",
                                style={"font-size": "0.9em", "color": "black"},
                                className="mb-2",
                            ),
                            # log_out_button.render(),
                            html.Div(
                                children=[
                                    dbc.Button(
                                        "Logout",
                                        id="logout-button",
                                        color="danger",
                                        className="logout-button poppins-semibold mb-0 w-50",
                                        n_clicks=0,
                                    ),
                                ],
                                className="text-center",
                            ),
                        ],
                        style={"text-align": "center", "margin-top": "1rem"},
                    ),
                ],
                style={"margin-top": "auto", "margin-bottom": "1rem"},
            ),
        ],
        style={
            "position": "fixed",
            "top": 0,
            "left": 0,
            "bottom": 0,
            "width": "16rem",
            "padding": "1rem 1rem",
            # "background-color": "#2A3286",
            # "border-right": "1px solid #c3c1c1",
            "display": "flex",
            "flex-direction": "column",
            "height": "calc(100vh - 2rem)",
        },
        className="sidebar m-3 rounded-4 bg-white",
        id="sideBar",
    )


@callback(
    Output("url", "href"),
    Input("logout-button", "n_clicks"),
    prevent_initial_call=True,
)
def logout(n_clicks):
    """Log user out"""
    if n_clicks:
        return "/logout"
    return no_update


@callback(
    Output("navbar-name", "children"),
    Output("navbar-email", "children"),
    Input("auth-store", "modified_timestamp"),
    State("auth-store", "data"),
    prevent_initial_call=True,
)
def show_user_info(ts: int, user_store: Dict[str, Any]):
    user = User.pull_user_from_store(user_store)
    return user.name, user.email
