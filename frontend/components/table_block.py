from frontend.components.base_block import BaseBlock
from frontend.components.base_button import Button
from frontend.components.base_table import BaseTable
from frontend.components.paginated_table import PaginatedTableAIO
from dash.development.base_component import Component

from dash import html
from typing import Union


class TableBlock(BaseBlock):
    def __init__(
        self,
        *base_props,
        table: Union[BaseTable, PaginatedTableAIO],
        has_add_button: bool = True,
        has_del_button: bool = True,
    ) -> None:
        super().__init__(*base_props)
        self.table = table
        self.has_add_button = has_add_button
        self.has_del_button = has_del_button

    def render(self) -> Component:
        return html.Div(
            [
                html.Div(
                    [
                        (
                            html.H3(self.title, className="my-3 text-dark fw-medium")
                            if self.title
                            else None
                        ),
                        html.Div(
                            [
                                (
                                    Button(
                                        id=f"{self.table.id}-add-button",
                                        children=[
                                            html.I(className="fa-solid fa-plus me-2"),
                                            "Add",
                                        ],
                                        className="rounded-3 bg-primary text-secondary",
                                    )
                                    if self.has_add_button
                                    else None
                                ),
                                (
                                    But<PERSON>(
                                        children=[
                                            html.I(className="fa-solid fa-trash-can")
                                        ],
                                        id=f"{self.table.id}-delete-button",
                                        className="rounded-3 bg-transparent text-danger border-0",
                                    )
                                    if self.has_del_button
                                    else None
                                ),
                            ]
                        ),
                    ],
                    style={
                        "display": "flex",
                        "justify-content": "space-between",
                        "align-items": "center",
                    },
                ),
                html.Div(
                    [self.table],
                ),
                # self.table,
            ],
            className="w-100 rounded-4 p-2",
        )
