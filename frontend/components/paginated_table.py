from dash import html, callback, Output, Input, State, MATCH, no_update
import dash_bootstrap_components as dbc
from frontend.components.base_table import BaseTable
from typing import Optional


class PaginatedTableAIO(html.Div):
    # A set of functions that create pattern-matching callbacks of the subcomponents
    class ids:
        table = lambda id: {
            "component": "PaginatedTable",
            "subcomponent": "table",
            "id": id,
        }
        pagination = lambda id: {
            "component": "PaginatedTable",
            "subcomponent": "pagination",
            "id": id,
        }

    # Make the ids class a public class
    ids = ids

    def __init__(self, id: str, pagination_position: str = "", **kwargs) -> None:
        self.id = id
        self.pagination_pos_options = {
            "left": "justify-content-start",
            "center": "justify-content-center",
            "right": "justify-content-end",
        }
        self.pagination_pos_class = self.pagination_pos_options.get(
            pagination_position, "justify-content-end"
        )
        self.default_grid_options = {
            "pagination": True,
            "suppressPaginationPanel": True,
            "paginationPageSize": 8,
        }
        kwargs["grid_options"] = {
            **self.default_grid_options,
            **kwargs.get("grid_options", {}),
        }

        super().__init__(
            [
                BaseTable(**kwargs, id=self.ids.table(id)),
                html.Div(
                    [
                        dbc.Pagination(
                            id=self.ids.pagination(id),
                            max_value=0,
                            # first_last=True,
                            previous_next=True,
                            fully_expanded=False,
                            class_name="custom-pagination py-2 user-select-none",
                        ),
                    ],
                    className=f"d-flex {self.pagination_pos_class}",
                ),
            ],
        )

    @callback(
        Output(ids.pagination(MATCH), "max_value"),
        Input(ids.table(MATCH), "paginationInfo"),
    )
    def update_pagination_control(pagination_info):
        if pagination_info is None:
            return no_update
        return pagination_info["totalPages"]

    @callback(
        Output(ids.table(MATCH), "paginationGoTo"),
        Input(ids.pagination(MATCH), "active_page"),
        prevent_initial_call=True,
    )
    def goto_page(n):
        if n is None or n == 1:
            return "first"
        # grid pagination starts at zero
        return n - 1
