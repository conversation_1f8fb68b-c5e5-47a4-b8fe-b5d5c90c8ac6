from dash import html, callback, Input, Output, State, ctx, no_update

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock
from dash.development.base_component import Component


column_defs = [
    # columns: id, stream_name, material, composition, entity_type
    {
        "headerName": "Stream",
        "field": "stream_name",
        "editable": False,
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
        "cellClass": "overflow-hidden",
        "tooltipField": "stream_name",
    },
    {
        "headerName": "Material",
        "field": "material",
        "editable": False,
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
    },
    {
        "headerName": "Composition",
        "field": "composition",
        "valueFormatter": {"function": "`${params.value} %`"},
        "type": "rightAligned",
    },
]


grid_options = {
    "suppressMenuHide": True,
}


def material_flow_table(id: str) -> Component:
    table = PaginatedTableAIO(id=id, column_defs=column_defs, grid_options=grid_options)
    block = TableBlock(
        "Material Flow", table=table, has_add_button=False, has_del_button=False
    )

    return html.Div([block.render()], className="w-100")
