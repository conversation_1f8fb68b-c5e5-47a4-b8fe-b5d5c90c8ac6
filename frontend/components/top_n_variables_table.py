from dash import html

from frontend.components.base_table import BaseTable
from frontend.components.table_block import TableBlock


column_defs = [
    # columns: id, entity, type, variable, value, unit
    {
        "headerName": "Equipment/ Stream",
        "field": "entity",
        "cellClass": "overflow-hidden",
        "tooltipField": "entity",
    },
    {
        "headerName": "Type",
        "field": "type",
    },
    {
        "headerName": "Variable",
        "field": "variable",
        "cellClass": "overflow-hidden",
        "tooltipField": "variable",
    },
    {
        "headerName": "Value",
        "field": "value",
        "valueFormatter": {"function": "d3.format(',.2f')(params.value)"},
    },
    {
        "headerName": "Unit",
        "field": "unit",
        "maxWidth": "100",
        "tooltipField": "unit",
    },
]

grid_options = {"domLayout": "normal"}


def top_n_variables_table(id: str):
    table = BaseTable(
        id=id,
        column_defs=column_defs,
        grid_options=grid_options,
        sortable=False,
        editable=False,
    )
    block = TableBlock("", table=table, has_add_button=False, has_del_button=False)

    return html.Div([block.render()], className="w-100")
