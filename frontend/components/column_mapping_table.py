from dash import html
from typing import List

from frontend.components.paginated_table import PaginatedTableAIO
from frontend.components.table_block import TableBlock


column_defs = [
    # cols: id, var_id, column_name, var_options, is_timestep, is_timeset
    # Dropdown
    {
        "headerName": "Column Name",
        "field": "column_name",
        "editable": False,
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
        "sortable": True,
        "maxWidth": 300,
    },
    # Dropdown
    {
        "headerName": "Variable Name",
        "field": "var_id",
        "cellEditor": {"function": "DCC_Dropdown"},
        "cellEditorParams": {
            "function": "modelTrainingVariableOptions(params)",
            "clearable": False,
        },
        "cellEditorPopup": True,
        "valueFormatter": {"function": "getVariableLabel(params)"},
        "tooltipValueGetter": {"function": "getVariableLabel(params)"},
        "filter": True,
        "filterParams": {
            "filterOptions": ["contains"],
            "textMatcher": {"function": "filterByVariableLabel(params)"},
            "maxNumConditions": 1,
            "debounceMs": 200,
        },
        "sortable": True,
    },
    {
        "field": "is_timestep",
        "cellEditor": "agCheckboxCellEditor",
        "cellRenderer": "agCheckboxCellRenderer",
        "headerName": "Is Timestep?",
        "maxWidth": 140,
    },
    {
        "field": "is_timeset",
        "cellEditor": "agCheckboxCellEditor",
        "cellRenderer": "agCheckboxCellRenderer",
        "headerName": "Is Timeset?",
        "maxWidth": 140,
    },
]


def column_mapping_table(id: str, title: str = ""):
    table = PaginatedTableAIO(
        id=id,
        column_defs=column_defs,
    )
    block = TableBlock(
        title,
        table=table,
        has_add_button=False,
        has_del_button=False,
    )

    return html.Div(
        children=[block.render()],
        className="w-100",
    )


def update_variable_options(row_data: list, variables_store: dict) -> list:
    """Updates variable options for each CSV training data column"""
    # Get chosen variable ids
    chosen_variable_ids = set(row["var_id"] for row in row_data)
    all_variable_ids = set(variables_store.keys())
    unchosen_variable_ids = all_variable_ids - chosen_variable_ids

    for row in row_data:
        row["var_options"] = [
            {
                "label": name,
                "value": uuid,
            }
            for uuid, name in variables_store.items()
            if uuid in unchosen_variable_ids | set([row["var_id"]])
        ]
    return row_data


def validate_column_mapping_on_var_id_change(
    updated_row_index: int, row_data: List[dict], variables_store: dict
):
    # Ensure that column name cannot be timestep or timeset if it is mapped to a variable
    row_data[updated_row_index]["is_timestep"] = False
    row_data[updated_row_index]["is_timeset"] = False
    row_data = update_variable_options(row_data, variables_store)

    return row_data


def validate_column_mapping_on_timestep_change(
    updated_row_index: int, row_data: List[dict], variables_store: dict
):
    updated_row_id = row_data[updated_row_index]["id"]

    # Remove variable mapped
    row_data[updated_row_index]["var_id"] = None

    # Ensure that a column cannot be both timeset and timestep
    if (
        row_data[updated_row_index]["is_timeset"]
        and row_data[updated_row_index]["is_timestep"]
    ):
        row_data[updated_row_index]["is_timeset"] = False

    # Ensure that there can only be at most one row (column name) selected as timestep
    for row in row_data:
        if row["id"] == updated_row_id:
            continue
        row["is_timestep"] = False

    row_data = update_variable_options(row_data, variables_store)
    return row_data


def validate_column_mapping_on_timeset_change(
    updated_row_index: int, row_data: List[dict], variables_store: dict
):
    updated_row_id = row_data[updated_row_index]["id"]

    # Remove variable mapped
    row_data[updated_row_index]["var_id"] = None

    # Ensure that a column name cannot be both timeset and timestep
    if (
        row_data[updated_row_index]["is_timeset"]
        and row_data[updated_row_index]["is_timestep"]
    ):
        row_data[updated_row_index]["is_timestep"] = False

    # Ensure that there can only be at most one column name selected as timeset
    for row in row_data:
        if row["id"] == updated_row_id:
            continue
        row["is_timeset"] = False

    row_data = update_variable_options(row_data, variables_store)
    return row_data
