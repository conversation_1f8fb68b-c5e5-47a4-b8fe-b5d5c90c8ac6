from typing import (
    ClassVar,
    Dict,
    List,
    Any,
    Sequence,
    Union,
    Optional,
    Type,
)
from pydantic import BaseModel, Field, field_validator
import pandas as pd

from frontend.models.base_models import BaseTableData
from frontend.utils.uuid_service import UUIDService


class CalibrationVariable(BaseTableData):
    id: str = Field(..., default_factory=UUIDService.generate_id)
    uid: str = Field(..., examples=["var-uuid-1"])
    entity_name: str = Field(..., examples=["HEX-100"])
    label: str = Field(..., examples=["Outlet Temperature"])
    value: Optional[float] = Field(default=None)

    @field_validator("value", mode="before")
    @classmethod
    def value_defaults_to_none(cls, value) -> Optional[float]:
        if value is None:
            return value

        try:
            return float(value)
        except:
            return None
        

class CalibrationResultGraph(BaseTableData):
    timestep: int
    value: float
    entity_name: str = Field(..., examples=["HEX-100"])
    var_name: str = Field(..., examples=["Outlet Temperature"])
    variable: str = Field(
        ...,
        description="Full variable name with entity",
        examples=["HEX-100 Outlet Temperature"],
    )

    # Class vars to get pydantic fields as strings
    variable_title: ClassVar[str] = "variable"
    timestep_title: ClassVar[str] = "timestep"
    value_title: ClassVar[str] = "value"

    @classmethod
    def to_df(cls: Type["CalibrationResultGraph"], data: Sequence["CalibrationResultGraph"]):
        return pd.DataFrame(cls.hydrate(data))
    

class CalibrationResult(BaseTableData):
    id: str = Field(..., default_factory=UUIDService.generate_id)
    # uid: str = Field(..., examples=["var-uuid-1"])
    entity_name: str = Field(..., examples=["HEX-100"])
    label: str = Field(..., examples=["Outlet Temperature"])
    model_value: float
    calibrated_value: float

    @classmethod
    def bootstrap_from_raw_data(
        cls, data_dict: Union[Dict[str, List], None]
    ) -> List["CalibrationResult"]:
        if not data_dict:
            return []
        try:
            data_length = len(list(data_dict.values())[0])
            return [
                cls(
                    entity_name=data_dict["entity"][i],
                    label=data_dict["condition"][i],
                    model_value=data_dict["model_value"][i],
                    calibrated_value=data_dict["calibrated_value"][i],
                )
                for i in range(data_length)
            ]
        except Exception as e:
            print(
                f"Failed to bootstrap from raw data. Check that the right column is mapped to fields in CalibrationResult. | {e}",
                flush=True,
            )
            raise ValueError(e)