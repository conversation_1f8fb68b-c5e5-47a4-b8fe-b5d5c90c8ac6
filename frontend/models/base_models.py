from typing import (
    ClassVar,
    Dict,
    List,
    Any,
    Sequence,
    Union,
    TypeVar,
    Type,
)
from pydantic import BaseModel, Field


T = TypeVar("T", bound="BaseTableData")


class BaseTableData(BaseModel):
    """Base Pydantic class for table row data and possibly, graph data
    
    This class converts raw AG Grid table row data to a list of Pydantic objects for safer access of attributes. 
    """

    @classmethod
    def hydrate(cls: Type[T], vars: Sequence[T]) -> List[Dict]:
        return [var.model_dump() for var in vars]

    @classmethod
    def bootstrap(
        cls: Type[T], vars: Union[Sequence[Dict[str, Any]], None]
    ) -> Sequence[T]:
        if vars is None:
            return []
        return [cls.model_validate(var) for var in vars]