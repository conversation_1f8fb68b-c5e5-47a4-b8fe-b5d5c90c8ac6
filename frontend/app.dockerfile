# FROM python:3.8.18-slim
FROM lennardong/sonic:dwsim84-postgre

# RUN mkdir -p /app
WORKDIR /code

COPY pyproject.toml /code/pyproject.toml

RUN poetry config virtualenvs.create false \
    && poetry install --no-root --no-interaction --no-ansi

EXPOSE 8050

COPY ./frontend /code/frontend
COPY ./backend /code/backend

ENV PYTHONPATH="/code"

ARG BASE_API_URL
ENV BASE_API_URL=$BASE_API_URL

ARG PREFECT_API_URL
ENV PREFECT_API_URL=$PREFECT_API_URL

ARG DEBUG=false
ENV DEBUG=$DEBUG

CMD ["sh", "-c", "poetry run gunicorn --config frontend/gunicorn_config.py frontend.app:server"]
# CMD ["sh", "-c", "export PYTHONPATH=/code && poetry run uvicorn backend.main:app --host 0.0.0.0 --port 8050 --reload"]


# CMD ["python", "frontend/app.py"]
