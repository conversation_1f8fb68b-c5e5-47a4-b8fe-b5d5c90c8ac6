from typing import Any, Dict, TypeVar
from flask import session
from pydantic import BaseModel, Field
from datetime import datetime

UserType = TypeVar("UserType", bound="User")


class User(BaseModel):
    user_id: str
    name: str
    email: str
    datetime: datetime

    def _serialize(self) -> Dict[str, Any]:
        return self.model_dump()

    def push_user_to_store(self):
        return self._serialize()

    @classmethod
    def _deserialize(cls, user_store: Dict[str, Any]) -> UserType:
        return cls.model_validate(user_store)

    @classmethod
    def pull_user_from_store(cls, user_store: Dict[str, Any]) -> UserType:
        return cls._deserialize(user_store)

    @classmethod
    def pull_user_id_from_store(cls, user_store: Dict[str, Any]) -> str:
        return cls._deserialize(user_store).user_id

    @classmethod
    def get_headers_from_store(cls, user_store: Dict[str, Any]) -> Dict[str, str]:
        return {
            "userId": cls._deserialize(user_store).user_id,
        }
