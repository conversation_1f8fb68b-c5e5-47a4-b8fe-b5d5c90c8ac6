from typing import Dict, List, Optional
import plotly.express as px
import pandas as pd


def generate_pie_chart(df: pd.DataFrame, name: str, value: str, title: str = ""):
    """Load data into Summary pie chart"""

    if df.empty:
        return generate_empty_plot()

    fig = px.pie(
        df,
        values=value,
        names=name,
        hole=0.4,
        # color_discrete_sequence=px.colors.qualitative.Prism,
    )

    # Style title
    fig.update_layout(
        title={
            "text": title,
            "y": 0.94,  # Y position (0.0 - bottom, 1.0 - top)
            "x": 0.03,  # X position (0.0 - left, 1.0 - right)
        },
        title_font=dict(
            family="Poppins",
            size=16,
            color="#000D16",
            weight=500,
        ),
        paper_bgcolor="#F7F9FB",
        legend_font=dict(
            family="Poppins",
            size=14,
        ),
    )

    fig.update_traces(textfont_size=14, insidetextorientation="horizontal")
    return fig


def generate_horizontal_bar_chart(
    df: pd.DataFrame,
    x: str,
    y: str,
    title: str,
    single_color: bool = True,
    y_labels: Optional[List[str]] = None,
    x_axis_title: str = "",
    y_axis_title: str = "",
    orientation: str = "h",
    color: Optional[str] = None,
):
    """Load data into horizontal bar chart plot"""

    if df.empty:
        return generate_empty_plot()

    fig = px.bar(
        df, x=x, y=y, orientation=orientation, text=x, color=color, barmode="group"
    )

    # Style title
    fig.update_layout(
        title={
            "text": title,
            "y": 0.94,  # Y position (0.0 - bottom, 1.0 - top)
            "x": 0.03,  # X position (0.0 - left, 1.0 - right)
        },
        title_font=dict(
            # family="Poppins",
            size=16,
            color="#000D16",
            weight=500,
        ),
        paper_bgcolor="#F7F9FB",
        barcornerradius=12,
        xaxis=dict(
            showticklabels=(orientation == "v"),
            showgrid=False,
            zeroline=False,
            title=x_axis_title,
        ),
        yaxis=dict(
            showgrid=False,
            zeroline=False,
            title=y_axis_title,
        ),
        font=dict(
            family="Poppins",
        ),
    )

    if y_labels:
        fig.update_layout(yaxis=dict(tickvals=list(range(len(df))), ticktext=y_labels))

    fig.update_traces(
        texttemplate=(
            "%{x:.2}%" if orientation == "h" else "%{y:.2}%"
        ),  # Custom text template
        # textposition="outside"        # Position of text
        textfont=dict(
            # family="Poppins",
            size=14,
            color="#343a40",
            weight=500,
        ),
        textposition="outside",
        cliponaxis=False,
    )

    if single_color:
        fig.update_traces(marker=dict(color="#1A2B5F"))

    return fig


def generate_parallel_coordinates_plot(
    df: pd.DataFrame, color: str, variables: List[str], labels: Dict[str, str]
):
    """Load data into parallel coordinates plot"""

    if df.empty:
        return generate_empty_plot()

    color_range = [df[color].min(), df[color].max()]

    fig = px.parallel_coordinates(
        df,
        color=color,
        labels=labels,
        dimensions={var: var for var in variables},
        color_continuous_scale=px.colors.diverging.Tealrose,
        color_continuous_midpoint=2,
        range_color=color_range,
    )

    fig.update_layout(
        font=dict(
            family="Poppins",
        ),
        paper_bgcolor="#F7F9FB",
        margin=dict(l=100, r=175, t=50, b=250),
    )

    fig.update_traces(
        labelfont=dict(size=12),
        tickfont=dict(size=12),
        labelangle=45,
        labelside="bottom",
    )
    return fig


def generate_line_chart(
    df: pd.DataFrame,
    x: str,
    y: str,
    color: str,
    title: str,
    x_axis_title: str = "",
    y_axis_title: str = "",
    markers: bool = True,
):
    """Load data into line chart plot

    Args:
        df (pd.DataFrame): Input DataFrame
        x (str): Column name for x-axis
        y (str): Column name for y-axis
        title (str): Chart title
        markers (bool, optional): Show markers on the line. Defaults to True.
    """

    if df.empty:
        return generate_empty_plot()

    fig = px.line(df, x=x, y=y, color=color, markers=markers)

    # Style title
    fig.update_layout(
        title={
            "text": title,
            "y": 0.94,
            "x": 0.03,
        },
        title_font=dict(
            size=16,
            color="#000D16",
            weight=500,
        ),
        paper_bgcolor="#F7F9FB",
        plot_bgcolor="#F7F9FB",
        xaxis=dict(
            showgrid=True,
            gridwidth=1,
            gridcolor="#E5E5E5",
            zeroline=False,
            title=x_axis_title,
        ),
        yaxis=dict(
            showgrid=True,
            gridwidth=1,
            gridcolor="#E5E5E5",
            zeroline=False,
            title=y_axis_title,
        ),
        font=dict(
            family="Poppins",
        ),
    )

    # fig.update_traces(
    #     line=dict(color=line_color, width=2),
    #     marker=dict(size=8, color=line_color),
    # )

    return fig


def generate_empty_plot():
    return {
        "layout": {
            "xaxis": {"visible": False},
            "yaxis": {"visible": False},
            "annotations": [
                {
                    "text": "No data to show",
                    "xref": "paper",
                    "yref": "paper",
                    "showarrow": False,
                    "font": {
                        "size": 16,
                        "family": "Poppins",
                    },
                }
            ],
            "paper_bgcolor": "#F7F9FB",
            "plot_bgcolor": "#F7F9FB",
        }
    }
