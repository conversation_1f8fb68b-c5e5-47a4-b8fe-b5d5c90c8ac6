from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField
from wtforms.validators import DataRequired, Email, Length


class LoginForm(FlaskForm):
    organization = StringField("Organization Name", validators=[DataRequired()])
    email = StringField("Email", validators=[DataRequired(), Email()])
    password = PasswordField("Password", validators=[DataRequired(), Length(min=6)])
    submit = SubmitField("Login")


class SignupForm(FlaskForm):
    organization = StringField("Organization Name", validators=[DataRequired()])
    job = StringField("Job Position", validators=[DataRequired()])
    email = StringField("Email", validators=[DataRequired(), Email()])
    password = PasswordField("Password", validators=[DataRequired(), Length(min=6)])
    submit = SubmitField("Sign Up")


class ReferralForm(FlaskForm):
    name = <PERSON><PERSON><PERSON>("Organization Name", validators=[DataRequired()])
    email_from = StringField("Email", validators=[DataRequired(), Email()])
    email_to = PasswordField("Password", validators=[DataRequired(), Length(min=6)])
    submit = SubmitField("Send")
