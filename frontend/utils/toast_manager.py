from frontend.components.base_toast import Toast
from dash import html


class ToastManager:
    def __init__(self, toasts):
        self.limit = 5
        self._toasts = (
            [child for child in toasts if child["props"]["is_open"] == True][
                -self.limit :
            ]
            if toasts
            else []
        )

    @property
    def toasts(self):
        return self._toasts

    def add_toast(self, new_toast):
        """Add toast"""
        self._toasts.append(new_toast)

    def make_toast(self, messages, category, dismissable=False):
        """Generate a Toast object"""
        if category == "warning":
            icon_class = "fa-solid fa-triangle-exclamation"
            heading = "Warning"
            color = "danger"
        elif category == "success":
            icon_class = "fa-solid fa-circle-check"
            heading = "Success"
            color = "success"
        else:
            icon_class = "fa-solid fa-circle-info"
            heading = "Info"
            color = "info"

        header = html.Div(
            [
                html.I(className=icon_class),
                html.H6(heading, className="m-0 ps-2 fw-semibold"),
            ],
            className=f"d-flex align-items-center justify-content-center text-{color}",
        )

        return Toast(
            [html.H6(message, className="mb-2") for message in messages],
            dismissable=dismissable,
            is_open=True,
            header=header,
        )

    def make_and_add_toast(self, messages: list, category: str):
        """Makes and adds a warning toast"""
        toast = self.make_toast(messages=messages, category=category)
        self.add_toast(toast)
