from enum import Enum
from typing import List
from frontend.components.base_badge import Badge
from dash import html


class Reactor(Enum):
    ConversionReactor = "ConversionReactor"


class ReactionPhase(Enum):
    Vapor = "vapor_reaction"
    Liquid = "liquid_reaction"
    Mixture = "mixture_reaction"


class StreamType(Enum):
    MaterialStream = "MaterialStream"
    InputStream = "InputStream"
    OutputStream = "OutputStream"
    # RecycleStream = "RecycleStream" # TODO Add in recycle stream once BE implemented


class ReactionType(Enum):
    Conversion = "ConversionReaction"


class EquipmentType(Enum):
    BatteryIn = "BatteryIn"
    BatteryOut = "BatteryOut"
    Heater = "Heater"
    Cooler = "Cooler"
    HeatExchanger = "HeatExchanger"
    AirCooler2 = "AirCooler2"
    OrificePlate = "OrificePlate"
    Compressor = "Compressor"
    Pump = "Pump"
    Expander = "Expander"
    Valve = "Valve"
    StreamMixer = "StreamMixer"
    # StreamSplitter = "StreamSplitter"
    Vessel = "Vessel"
    ShortcutColumn = "ShortcutColumn"
    ConversionReactor = "ConversionReactor"

    @classmethod
    def dummy_equipment_types(cls) -> List[str]:
        return [cls.BatteryIn.value, cls.BatteryOut.value]

    @classmethod
    def valid_equipment_types(cls) -> List[str]:
        return [et.value for et in cls if et.value not in cls.dummy_equipment_types()]


class EquipmentType_GPROMS(Enum):
    PositionSensitiveDetector = "PositionSensitiveDetector"
    SimulationDuration = "SimulationDuration"
    Crystallizer = "Crystallizer"
    TemperatureControl = "TemperatureControl"

    @classmethod
    def dummy_equipment_types(cls) -> List[str]:
        return []

    @classmethod
    def valid_equipment_types(cls) -> List[str]:
        return [et.value for et in cls if et.value not in cls.dummy_equipment_types()]


class DummyEquipmentType(Enum):
    BatteryInput = "BatteryInput"
    BatteryOutput = "BatteryOutput"
    Recycle = "Recycle"


class EntityType(Enum):
    Equipment = "equipment"
    Connection = "connection"


class IndustryType(Enum):
    CHEMICAL = "Chemical"
    PHARMACEUTICAL = "Pharmaceuticals"


class EngineType(Enum):
    DWSIM = "DWSim"
    GPROMS = "GRPROMS"

    @classmethod
    def get_enum_from_value(cls, value: str):
        try:
            enum_obj = next(iter(enum for enum in cls if enum.value == value))
        except Exception as e:
            raise ValueError(f"Failed to convert string to EngineType enum | {e}")
        return enum_obj


class ModelType(Enum):
    # FIRST_PRINCIPLES = "first_principles"
    RNN = "High Accuracy, Long Training"
    # ML = "ml"
    # STATISTICAL = "statistical"


class TrainingRegimeType(Enum):
    TRAINING_DATA = "imported_data"
    ALEPH_SIMULATION = "aleph_simulation"


class EquipmentEngineMap:
    mapping = {
        EngineType.DWSIM: EquipmentType,
        EngineType.GPROMS: EquipmentType_GPROMS,
    }


class StatusTagGenerator(Enum):
    COMPLETED = "Ready"
    IN_PROGRESS = "In Process"
    INCOMPLETE = "Setup Required"
    ERROR = "Error fetching status"

    @classmethod
    def generate_status_tag(cls, status: str, message: str = ""):
        if status not in [s.value for s in cls]:
            raise ValueError(
                f"{status} is not a valid status. Please add it as a new value in the enum."
            )
        if status == cls.COMPLETED.value:
            return Badge(
                color="success",
                children=[
                    html.I(className="fa-solid fa-check me-2"),
                    message or cls.COMPLETED.value,
                ],
            )
        if status == cls.IN_PROGRESS.value:
            return Badge(
                color="warning",
                children=[
                    html.I(className="fa-solid fa-hourglass-half me-2"),
                    message or cls.IN_PROGRESS.value,
                ],
            )
        if status == cls.INCOMPLETE.value:
            return Badge(
                color="danger",
                children=[
                    html.I(className="fa-solid fa-xmark me-2"),
                    message or cls.INCOMPLETE.value,
                ],
            )
        if status == cls.ERROR.value:
            return Badge(
                color="danger",
                children=[
                    html.I(className="fa-solid fa-skull-crossbones me-2"),
                    message or cls.ERROR.value,
                ],
            )
