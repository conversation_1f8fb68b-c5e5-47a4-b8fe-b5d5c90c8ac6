from pydantic import BaseModel, TypeAdapter
import logging
from typing import Type, Any, Union, Sequence
import re
from backend.endpoint.v1.schema import *


logger = logging.getLogger()


class PydanticValidator:
    endpoint_pydantic_mapping = {
        re.compile(r"^\/plant-configurations$"): List[PlantConfig_Metadata],
        re.compile(r"^\/presets\/equipment-details$"): List[PresetEquipmentType],
        re.compile(r"^\/presets\/stream-types$"): List[PresetStreamType],
        re.compile(r"^\/presets\/material-types$"): PresetMaterialTypes,
        re.compile(r"^\/presets\/sensor-types$"): PresetSensorTypes,
        re.compile(r"^\/plant-configurations\/([^\/]+)$"): PlantConfiguration,
        re.compile(r"^\/plant-configurations\/([^\/]+)\/kpis$"): List[KPIItem],
        re.compile(r"^\/plant-configurations\/([^\/]+)\/kpi-variables$"): List[
            KPIVariableItem
        ],
        re.compile(
            r"^\/plant-configurations\/([^\/]+)\/model-setup\/training-status$"
        ): Status,
        re.compile(
            r"^\/plant-configurations\/([^\/]+)\/model-setup\/training-setup$"
        ): SurrogateTrainingConfig,
        re.compile(r"^\/plant-configurations\/([^\/]+)\/sensors$"): SensorMappingConfig,
    }

    @classmethod
    def get_model(cls, endpoint: str) -> Type[BaseModel]:
        for path, model in cls.endpoint_pydantic_mapping.items():
            match = path.match(endpoint)
            if match:
                return model
        raise KeyError("No matching Pydantic model found.")

    @classmethod
    def validate(
        cls,
        pydantic_model: Union[Type[BaseModel], Type[Sequence[BaseModel]]],
        data: Any,
    ) -> Union[BaseModel, Sequence[BaseModel]]:
        type_adapter = TypeAdapter(pydantic_model)
        return type_adapter.validate_python(data)
