from pydantic import ValidationError, TypeAdapter
import requests
from requests import <PERSON>TT<PERSON><PERSON>rror, JSONDecodeError, Response, Timeout
from typing import Optional, Sequence, Tuple, Dict, Any
import logging
import os
from abc import ABC, abstractmethod
from http import HTTPStatus
from frontend.utils.pydantic_validator_service import PydanticValidator


logger = logging.getLogger()


class APIServiceProvider:
    def __init__(self):
        environment: str = os.getenv("PROFILE", "development")
        logger.info(f"Environment = {environment}")

        if environment == "DEVELOPMENT":
            self.api_service: "BaseAPIService" = MockAPIService()
        else:
            self.api_service: "BaseAPIService" = MockAPIService()

    def get_api_service(self) -> "BaseAPIService":
        return self.api_service


class BaseAPIService(ABC):
    @abstractmethod
    def get(
        self, endpoint: str, params: Dict[str, Any] = {}, headers: Dict[str, Any] = {}
    ) -> <PERSON><PERSON>[Dict[str, Any], bool]:
        pass

    @abstractmethod
    def post(
        self, endpoint: str, data: Dict[str, Any] = {}, headers: Dict[str, Any] = {}
    ) -> Tuple[Dict[str, Any], bool]:
        pass

    @abstractmethod
    def delete(
        self, endpoint: str, params: Dict[str, Any] = {}, headers: Dict[str, Any] = {}
    ) -> Tuple[Dict[str, Any], bool]:
        pass


class MockAPIService(BaseAPIService):
    def __init__(self):
        self.base_url = os.getenv("BASE_API_URL", "http://0.0.0.0:8000/api/v1")
        self.headers = {}
        self.timeout = 600

    def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, str]] = None,
        headers: Dict[str, str] = {},
    ) -> Tuple[Dict[str, Any], bool]:
        url: str = f"{self.base_url}{endpoint}"
        headers = {**self.headers, **headers}

        try:
            response: Response = requests.get(
                url, params=params, headers=headers, timeout=self.timeout
            )

            # Raises an error for bad status codes
            response.raise_for_status()

            # Validates response body
            pydantic_model = PydanticValidator.get_model(endpoint)
            response_model = PydanticValidator.validate(pydantic_model, response.json())

            return {"data": response.json()}, True

        except HTTPError as e:
            logger.error(
                f"GET {url} failed with status code {e.response.status_code}: {e.response.text}"
            )
            return {
                "status_code": e.response.status_code,
                "error": e.response.text,
            }, False
        except JSONDecodeError as e:
            logger.error(
                f"GET {url} returned invalid json {e.response.text if e.response else ''}"
            )
            return {
                "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "error": f"Invalid json {e.response.text if e.response else ''}",
            }, False
        except KeyError as e:
            logger.error(f"Pydantic model not defined for GET {url}")
            return {
                "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "error": f"Pydantic model not defined for GET {url}",
            }, False
        except ValidationError as e:
            logger.error(f"Pydantic validation failed for GET {url}")
            return {
                "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "error": f"Pydantic model not defined for GET {url}",
            }, False
        except Timeout:
            logger.error(f"GET {url} timed out after {self.timeout} seconds")
            return {
                "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "error": f"Request timed out",
            }, False

    def post(
        self,
        endpoint: str,
        data: Optional[Dict[str, str]] = None,
        headers: Dict[str, str] = {},
    ) -> Tuple[Dict[str, Any], bool]:
        url: str = f"{self.base_url}{endpoint}"
        headers = {**self.headers, **headers}

        try:
            response: Response = requests.post(
                url, json=data, headers=headers, timeout=self.timeout
            )
            response.raise_for_status()
            if response.content:
                return {"data": response.json()}, True
            return {
                "status_code": response.status_code,
            }, True
        except HTTPError as e:
            logger.error(
                f"POST {url} failed with status code {e.response.status_code}: {e.response.text}"
            )
            return {
                "status_code": e.response.status_code,
                "error": e.response.text,
            }, False
        except JSONDecodeError as e:
            logger.error(
                f"POST {url} returned invalid json {e.response.text if e.response else ''}"
            )
            return {
                "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "error": f"Invalid json {e.response.text if e.response else ''}",
            }, False
        except Timeout:
            logger.error(f"POST {url} timed out after {self.timeout} seconds")
            return {
                "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "error": f"Request timed out",
            }, False

    def delete(
        self,
        endpoint: str,
        params: Optional[Dict[str, str]] = None,
        headers: Dict[str, str] = {},
    ) -> Tuple[Dict[str, Any], bool]:
        url: str = f"{self.base_url}{endpoint}"
        headers = {**self.headers, **headers}

        try:
            response: Response = requests.delete(
                url, params=params, headers=headers, timeout=self.timeout
            )
            response.raise_for_status()  # Raises an error for bad status codes
            return {
                "status_code": response.status_code,
            }, True
        except HTTPError as e:
            logger.error(
                f"DELETE {url} failed with status code {e.response.status_code}: {e.response.text}"
            )
            return {
                "status_code": e.response.status_code,
                "error": e.response.text,
            }, False
        except JSONDecodeError as e:
            logger.error(
                f"DELETE {url} returned invalid json {e.response.text if e.response else ''}"
            )
            return {
                "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "error": f"Invalid json {e.response.text if e.response else ''}",
            }, False
        except Timeout:
            logger.error(f"DELETE {url} timed out after {self.timeout} seconds")
            return {
                "status_code": HTTPStatus.INTERNAL_SERVER_ERROR.value,
                "error": f"Request timed out",
            }, False

    # You can add more methods like put, delete, etc., as needed.
