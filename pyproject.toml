[tool.poetry]
name = "sonic"
version = "0.1.0"
description = "base python environment for Aleph codebase"
authors = ["lennardong <<EMAIL>>"]
license = "MIT"
include = []

[tool.poetry.dependencies]
python = "~3.8"

# DS
optuna = "^4.3.0"
torch = "^2.0.0"
matplotlib="*"
numpy = "*"
pandas = "*"
scikit-learn = "^1.3.2"
scikit-optimize = "^0.9.0"
scipy = "^1.10.1"
seaborn = "~0.13.2"
networkx = "*"
pyswarms = "~1.3.0"
pydacefit = "~1.0.1"

# CODE CLEANLINESS
pytest = "^7.4.0"
black = "^24.2.0"
ipykernel = "^6.29.2"

# PYTORCH 
# torch = "~2.1.0"
# torchvision = "=0.17.0"
# torchaudio = "=2.2.0"
# transformers = "=4.37.2"
# pytorch-lightning = "=2.2.0"

# KERAS
# tensorflow-io-gcs-filesystem = { version = "0.27.0"}
# tensorflow = "2.12.0"
# keras="*"


# DOTNET
pythonnet = "=3.0.3"

# WEB FRAMEWORKS
flask = "~2.3.2"
flask-cors = "~3.0.10"
uvicorn = { version = "~0.27.1", extras = ["standard"] }
fastapi = {extras = ["standard"], version = "^0.115.0"}

# SERIALIZATION
dataclasses_json = "~0.6.4"
jsonpickle = "~3.0.2"
marshmallow = "~3.19.0"

# MESSAGING
pika = "~1.3.1"
python-logging-rabbitmq = "~2.2.0"

# DATABASE
mysql-connector-python = "~8.0.31"
pyvis = "^0.3.2"
fuzzywuzzy = {extras = ["speedup"], version = "^0.18.0"}
openpyxl = "^3.1.2"
ipython = "^7.31.1"
pipdeptree = "^2.23.1"
salib = "^1.4"
sqlalchemy = "^2.0.35"
psycopg2 = "^2.9.9"
ordered-set = "^4.1.0"

# FRONTEND
dash = "^2.17.0"
plotly = "^5.22.0"
dash-bootstrap-components = "^1.6.0"
dash-cytoscape = "^1.0.2"
dash-core-components = "^2.0.0"
dash-html-components = "^2.0.0"
dash_ag_grid = "^31.2.0"
xhtml2pdf = "^0.2.16"
sympy = "^1.13.3"
flask-login = "^0.6.3"
flask-wtf = "^1.2.1"
inflection = "^0.5.1"
python-dotenv = "^1.0.1"
authlib = "^1.0"
requests = "^2.27.1"
deepdiff = "^8.1.1"
jsonpath-ng = "^1.7.0"
tabulate = "^0.9.0"
gunicorn = "^23.0.0"
gevent = "*"
celery = "^5.3.0"
redis = "^4.5.0"


# ORCHAESTRATION
prefect = "^2.20"
prefect_azure = {version = ">=0.3.11", extras = ["blob_storage"]}
aiohttp = "^3.0"
# aiohttp = ">=3.9, <4.0"
azure-ai-ml = "^1.27.1"


[tool.poetry.group.dev.dependencies]
jupyter = "^1.0.0"
notebook = "^7.2.0"
pytest = "^7.4.0"
black = "^24.2.0"
# mypy = "^0.780" - already commented
isort = "^5.12.0"

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
line_length = 88

[tool.pytest.ini_options]
testpaths = ["tests"]

[tool.poetry.scripts]
start = "source.main:main"
test = "pytest"
format = "black ."
#type-check = "mypy backend"

[[tool.poetry.source]]
name = "PyPI"
priority = "supplemental"

[[tool.poetry.source]]
name = "pypi-sg"
url = "https://pypi.sgp1.vultrobjects.com/simple/"
priority = "primary"

[[tool.poetry.source]]
name = "nvidia"
url = "https://pypi.ngc.nvidia.com"
priority = "supplemental"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

# Explanation of Version Specifiers:
# ^: Allows updates that do not modify the leftmost non-zero element. For example, ^1.2.3 permits anything from 1.2.3 to, but not including, 2.0.0.
# =: Pins to an exact version.
# ~: The tilde specifier allows patch-level changes if a minor version is specified or minor-level changes if only a major version is specified. For Python, ~3.8 ensures any version 3.8.x is acceptable, without moving to 3.9.

