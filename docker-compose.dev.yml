services:
  backend_db:
    env_file:
    - .env
    build:
      context: ./backend/infrastructure/_db
      dockerfile: postgre.dockerfile
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - aleph
  backup:
    env_file:
      - .env
    image: postgres:13
    volumes:
      - postgres_backup:/backups
      - ./backend/infrastructure/_db:/scripts
    depends_on:
      - backend_db
    networks:
      - aleph
    entrypoint: []
    command: bash -c "chmod +x /scripts/backup.sh && /scripts/backup.sh"
    restart: "no"
  backend_webserver:
    env_file:
      - .env
    build:
      context: .
      dockerfile: backend/app.dockerfile
    ports:
      - "8000:8000"
    volumes:
      - backend_logs:/backend/logs
    networks:
      - aleph
    depends_on:
      - backend_db
  frontend_webserver:
    env_file:
      - .env
    build: 
      context: .
      dockerfile: frontend/app.dockerfile
    ports: 
    - "8050:8050"
    networks:
      - aleph
  celery:
    env_file:
    - .env
    build: 
      context: .
      dockerfile: frontend/celery.dockerfile
    depends_on:
      - redis
    networks:
      - aleph
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    networks:
      - aleph

networks:
  aleph:
    name: aleph

volumes:
  postgres_data:
  postgres_backup: 
  backend_logs: