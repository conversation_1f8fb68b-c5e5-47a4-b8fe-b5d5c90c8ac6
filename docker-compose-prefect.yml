name: prefect 

services:
  prefect_server:
    env_file:
    - .env
    image: ${ACR_NAME}.azurecr.io/prefect_server:${IMAGE_TAG}
    ports:
      - "4200:4200"
    depends_on:
      - prefect_db
    networks:
      - prefect
  prefect_db: 
    env_file:
    - .env
    image: ${ACR_NAME}.azurecr.io/prefect_db:${IMAGE_TAG}
    ports: 
    - "5433:5432"  # Host port at 5433 to prevent clash with app
    networks:
      - prefect
    volumes:
      - /data/postgres:/var/lib/postgresql/data
  prefect_backup: 
    env_file:
    - .env
    image: ${ACR_NAME}.azurecr.io/prefect_backup:${IMAGE_TAG}
    volumes:
      - /data/backups:/backups
    depends_on:
      - prefect_db
    networks:
      - prefect
    restart: "no"

networks:
  prefect:
    name: prefect