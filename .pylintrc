[MASTER]
ignore=test
py-version=3.8

[MESSAGES CONTROL]
# Disable almost everything for developer speed
disable=all

# Only enable checks for serious errors and critical docstrings
enable=import-error,
       undefined-variable,
       used-before-assignment,
       return-outside-function,
       not-callable,
       no-member,
       # missing-module-docstring
       missing-class-docstring,
       missing-function-docstring

[REPORTS]
output-format=text
reports=no
score=no

[FORMAT]
max-line-length=120

[TYPECHECK]
ignored-modules=*
ignored-classes=*
generated-members=*

[BASIC]
argument-naming-style=any
variable-naming-style=any
function-naming-style=any
method-naming-style=any
attr-naming-style=any