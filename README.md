## Surrogate Model Implementation Plan

### Milestone 1: Pass SurrogateMock for training a model that is TS and non-TS

Based on patterns in the _optimizer implementation, we'll create a structurally similar design for the surrogate model functionality. The implementation will follow a layered architecture with clear separation of concerns.

### Implementation Plan by Layer

#### 1. Enums Layer
**File: `_enums.py`**

```python
class SurrogateModelType(str, Enum):
    """Types of surrogate models available"""
    RANDOM_FOREST = "random_forest"
    RANDOM_FOREST_TS = "random_forest_timeseries"
    GRADIENT_BOOSTING = "gradient_boosting"
    GRADIENT_BOOSTING_TS = "gradient_boosting_timeseries"
    RNN_TS = "rnn_timeseries"

class TrainingStatus(str, Enum):
    """Status of surrogate model training process"""
    PENDING = "pending"
    OPTIMIZING = "optimizing"
    TRAINING = "training"
    EVALUATING = "evaluating"
    READY = "ready"
    FAILED = "failed"

class ModelFramework(str, Enum):
    """Backend ML framework used for implementation"""
    SKLEARN = "sklearn"
    PYTORCH = "pytorch"
    XGBOOST = "xgboost"
    LIGHTGBM = "lightgbm"
```

**Tasks:**
- [ ] Implement core enum types in _enums.py
- [ ] Add forward references for type hinting
- [ ] Test enum conversions and utility methods

#### [OK] 2. Protocols Layer
**File: `surrogate_interfaces.py`**

LO COMMENTS
- this should live in backend/core/interfaces as `surrogate_interface.py`
- this should follow codestyle of other interfaces (e.g metis_interfaces.py): define a custom errors related tothe interface. This is for future traceability.
- consider using a ABC and defining any necessary inits. inits should only be for configurations, never for data.

```python
class ISurrogate(Protocol):
    def create_trainer(self, model_type: SurrogateModelType) -> BaseSurrogateTrainer:
        """Create a trainer instance for the specified model type"""
        ...
        
    def train(self, df_x: pd.DataFrame, df_y: pd.DataFrame, 
             timestep_col: Optional[str] = None,
             model_parameters: Optional[Dict[str, Any]] = None) -> Tuple[BaseSurrogateModel, TrainingResult]:
        """Train a model with provided data and parameters"""
        ...
    
    def evaluate(self, model: BaseSurrogateModel, df_x: pd.DataFrame, 
                df_y: pd.DataFrame) -> EvaluationResult:
        """Evaluate trained model performance"""
        ...
        
    def save_model(self, model: BaseSurrogateModel, 
                  metadata: Dict[str, Any]) -> str:
        """Save model to registry and return model ID"""
        ...
        
    def save_training_config(self, training_metadata: SurrogateTrainingMetadata) -> None:
        """Save training configuration metadata"""
        ...
        
    def get_training_config(self, atlas_label: str, user_id: str) -> Optional[SurrogateTrainingMetadata]:
        """Get training configuration by atlas label and user ID"""
        ...
        
    def get_training_config_by_id(self, training_job_id: str) -> Optional[SurrogateTrainingMetadata]:
        """Get training configuration by job ID"""
        ...
        
    def update_training_config(self, training_metadata: SurrogateTrainingMetadata) -> None:
        """Update existing training configuration"""
        ...
```

**Tasks:**
- [ ] Define ISurrogate protocol with required methods
- [ ] Add proper type hints and method signatures
- [ ] Document protocol methods

#### 3. Value Objects Layer
**File: `valueobjects.py`**

LO COMMENTS:
- this should use pydantic classes. 
- it should have validation with error accumulation
- for reference, check backend/core/_optimizer/valueobjects.py

```python
@dataclass(frozen=True)
class ModelParameters:
    """Immutable container for model parameters"""
    values: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return self.values.copy()
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelParameters':
        return cls(values=data.copy())

@dataclass(frozen=True)
class ModelMetrics:
    """Model evaluation metrics"""
    rmse: Optional[float] = None
    mae: Optional[float] = None
    r2: Optional[float] = None
    forecast_mae: Optional[float] = None
    forecast_mape: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Optional[float]]:
        return {k: v for k, v in asdict(self).items() if v is not None}
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelMetrics':
        return cls(**{k: v for k, v in data.items() if k in inspect.signature(cls).parameters})

@dataclass(frozen=True)
class TrainingResult:
    """Results from model training"""
    model_id: str
    duration: float
    epochs_completed: Optional[int] = None
    early_stopping_triggered: bool = False

@dataclass(frozen=True)
class EvaluationResult:
    """Results from model evaluation"""
    metrics: ModelMetrics
    feature_importance: Dict[str, float] = field(default_factory=dict)
    predictions: Optional[pd.DataFrame] = None
```

**Tasks:**
- [ ] Implement value objects for model parameters
- [ ] Implement value objects for metrics
- [ ] Implement value objects for training and evaluation results
- [ ] Add validation logic and error handling
- [ ] Add serialization/deserialization methods

#### 4. Entities Layer
**File: `entities.py`**

LO COMMENTS:
- this should use pydantic classes. 
- it should have validation with error accumulation
- for reference, check backend/core/_optimizer/entities.py

```python
@dataclass
class SurrogateTrainingMetadata:
    """Metadata for surrogate training process"""
    training_job_id: str
    status: TrainingStatus = TrainingStatus.PENDING
    atlas_label: Optional[str] = None
    user_id: Optional[str] = None
    model_type: Optional[SurrogateModelType] = None
    model_framework: Optional[ModelFramework] = None
    timestep_col: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    optimization_id: Optional[str] = None
    model_registry_id: Optional[str] = None
    metrics: Optional[Dict[str, float]] = None
    feature_importance: Optional[Dict[str, float]] = None
    hyperparameters: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = asdict(self)
        # Convert datetime objects to ISO format strings
        for key in ['created_at', 'started_at', 'completed_at']:
            if result[key] is not None:
                result[key] = result[key].isoformat()
        # Convert enum values to strings
        if result['status'] is not None:
            result['status'] = result['status'].value
        if result['model_type'] is not None:
            result['model_type'] = result['model_type'].value
        if result['model_framework'] is not None:
            result['model_framework'] = result['model_framework'].value
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SurrogateTrainingMetadata':
        data_copy = data.copy()
        # Convert ISO format strings back to datetime objects
        for key in ['created_at', 'started_at', 'completed_at']:
            if data_copy.get(key):
                try:
                    data_copy[key] = datetime.fromisoformat(data_copy[key])
                except (ValueError, TypeError):
                    data_copy[key] = None
        
        # Convert string values to enums
        if isinstance(data_copy.get('status'), str):
            data_copy['status'] = TrainingStatus(data_copy['status'])
        if isinstance(data_copy.get('model_type'), str):
            data_copy['model_type'] = SurrogateModelType(data_copy['model_type'])
        if isinstance(data_copy.get('model_framework'), str):
            data_copy['model_framework'] = ModelFramework(data_copy['model_framework'])
            
        return cls(**data_copy)
```

**Tasks:**
- [ ] Create SurrogateTrainingMetadata entity class
- [ ] Add proper conversion methods (to_dict, from_dict)
- [ ] Implement validation and error accumulation
- [ ] Test serialization and deserialization

#### 5. Base Classes Layer
**File: `surrogate_base.py`**

```python
class BaseSurrogateModel(ABC):
    """Abstract base class for all surrogate models"""
    
    @abstractmethod
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions with the model"""
        ...
    
    @abstractmethod
    def get_native_model(self) -> Any:
        """Get the underlying native model implementation"""
        ...
    
    @abstractmethod
    def get_model_type(self) -> SurrogateModelType:
        """Get the model type"""
        ...
    
    @abstractmethod
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance if available"""
        ...
    
    @abstractmethod
    def save(self, path: str) -> None:
        """Save model to disk"""
        ...
    
    @classmethod
    @abstractmethod
    def load(cls, path: str) -> 'BaseSurrogateModel':
        """Load model from disk"""
        ...

class BaseSurrogateTrainer(ABC):
    """Abstract base class for model trainers"""
    
    # Random seed management for reproducibility
    RANDOM_SEED = 42
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def _set_random_seeds(self) -> None:
        """Set random seeds for reproducibility"""
        np.random.seed(self.RANDOM_SEED)
        random.seed(self.RANDOM_SEED)
        # Framework-specific seeds to be set in subclasses
    
    @abstractmethod
    def train(self, df_x: pd.DataFrame, df_y: pd.DataFrame, 
             timestep_col: Optional[str] = None,
             model_parameters: Optional[Dict[str, Any]] = None) -> Tuple[BaseSurrogateModel, TrainingResult]:
        """Train a model on provided data"""
        ...
    
    @abstractmethod
    def evaluate(self, model: BaseSurrogateModel, df_x: pd.DataFrame, 
                df_y: pd.DataFrame) -> EvaluationResult:
        """Evaluate model performance"""
        ...
```

**Tasks:**
- [ ] Define base model class with core interface methods
- [ ] Define base trainer class with common functionality
- [ ] Define specialized trainer base classes for time-series and non-time-series models
- [ ] Implement common utilities and helper methods

#### 6. Implementations Layer
**File: `surrogate_mock.py`**

```python
class MockSurrogateModel(BaseSurrogateModel):
    """Mock implementation for testing"""
    
    def __init__(self, model_type: SurrogateModelType, is_timeseries: bool = False):
        self.model_type = model_type
        self.is_timeseries = is_timeseries
        self.input_cols = []
        self.output_cols = []
        self.feature_importances = {}
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Generate random predictions"""
        return np.random.random((len(X), len(self.output_cols) or 1))
    
    def get_native_model(self) -> Any:
        """Return self as native model"""
        return self
    
    def get_model_type(self) -> SurrogateModelType:
        return self.model_type
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Return mock feature importance"""
        if not self.feature_importances and self.input_cols:
            importances = np.random.random(len(self.input_cols))
            importances = importances / importances.sum()
            self.feature_importances = {col: float(imp) for col, imp in zip(self.input_cols, importances)}
        return self.feature_importances
    
    def save(self, path: str) -> None:
        """Mock save operation"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'wb') as f:
            pickle.dump(self, f)
    
    @classmethod
    def load(cls, path: str) -> 'MockSurrogateModel':
        """Mock load operation"""
        with open(path, 'rb') as f:
            return pickle.load(f)

class SurrogateMock(ISurrogate):
    """Mock implementation of ISurrogate for testing"""
    
    def __init__(self):
        self.training_configs = {}  # Store training configs by ID
        self.models = {}  # Store trained models
        
    def create_trainer(self, model_type: SurrogateModelType) -> BaseSurrogateTrainer:
        """Create appropriate trainer based on model type"""
        if model_type in [SurrogateModelType.RANDOM_FOREST_TS, SurrogateModelType.GRADIENT_BOOSTING_TS, SurrogateModelType.RNN_TS]:
            return MockTimeSeriesTrainer()
        else:
            return MockFlatTrainer()
    
    def train(self, df_x: pd.DataFrame, df_y: pd.DataFrame, 
             timestep_col: Optional[str] = None,
             model_parameters: Optional[Dict[str, Any]] = None) -> Tuple[BaseSurrogateModel, TrainingResult]:
        """Train model using appropriate trainer"""
        # Determine model type based on presence of timestep_col
        if timestep_col:
            model_type = SurrogateModelType.RANDOM_FOREST_TS
        else:
            model_type = SurrogateModelType.RANDOM_FOREST
            
        trainer = self.create_trainer(model_type)
        return trainer.train(df_x, df_y, timestep_col, model_parameters)
    
    def evaluate(self, model: BaseSurrogateModel, df_x: pd.DataFrame, 
                df_y: pd.DataFrame) -> EvaluationResult:
        """Evaluate model using appropriate trainer"""
        # Get appropriate trainer based on model type
        trainer = self.create_trainer(model.get_model_type())
        return trainer.evaluate(model, df_x, df_y)
    
    def save_model(self, model: BaseSurrogateModel, metadata: Dict[str, Any]) -> str:
        """Save mock model"""
        model_id = metadata.get("training_job_id", str(uuid.uuid4()))
        self.models[model_id] = model
        return model_id
    
    def save_training_config(self, training_metadata: SurrogateTrainingMetadata) -> None:
        """Save training configuration"""
        self.training_configs[training_metadata.training_job_id] = training_metadata
    
    def get_training_config(self, atlas_label: str, user_id: str) -> Optional[SurrogateTrainingMetadata]:
        """Get latest training config for atlas label and user"""
        matching_configs = [
            config for config in self.training_configs.values()
            if config.atlas_label == atlas_label and config.user_id == user_id
        ]
        if not matching_configs:
            return None
        # Return the most recent config
        return max(matching_configs, key=lambda c: c.created_at)
    
    def get_training_config_by_id(self, training_job_id: str) -> Optional[SurrogateTrainingMetadata]:
        """Get training config by ID"""
        return self.training_configs.get(training_job_id)
    
    def update_training_config(self, training_metadata: SurrogateTrainingMetadata) -> None:
        """Update existing training config"""
        self.training_configs[training_metadata.training_job_id] = training_metadata
```

**Tasks:**
- [ ] Implement MockSurrogateModel for testing
- [ ] Implement MockTimeSeriesTrainer for time-series data
- [ ] Implement MockFlatTrainer for non-time-series data  
- [ ] Implement SurrogateMock service
- [ ] Create simple test cases for both TS and non-TS models

#### 7. Utilities Layer
**File: `utilities.py`**

```python
def validate_dataframe(df: pd.DataFrame, name: str = "dataframe") -> None:
    """Validate that a dataframe is not empty and has expected structure"""
    if df is None:
        raise ValueError(f"{name} cannot be None")
    if not isinstance(df, pd.DataFrame):
        raise ValueError(f"{name} must be a pandas DataFrame, got {type(df)}")
    if df.empty:
        raise ValueError(f"{name} cannot be empty")

def train_test_split_timeseries(df: pd.DataFrame, timestep_col: str, 
                               test_size: float = 0.2) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Split dataframe for time series respecting temporal order"""
    validate_dataframe(df, "time series dataframe")
    if timestep_col not in df.columns:
        raise ValueError(f"Timestep column '{timestep_col}' not found in dataframe")
    
    # Sort by timestamp column
    df_sorted = df.sort_values(by=timestep_col)
    
    # Calculate split point
    split_idx = int(len(df_sorted) * (1 - test_size))
    
    # Split data
    train_df = df_sorted.iloc[:split_idx].copy()
    test_df = df_sorted.iloc[split_idx:].copy()
    
    return train_df, test_df
```

**Tasks:**
- [ ] Implement data validation utilities
- [ ] Implement time-series specific utilities
- [ ] Create helper functions for common operations

#### 8. Integration
**File: `__init__.py`**

```python
# Import public classes and interfaces
from ._enums import SurrogateModelType, TrainingStatus, ModelFramework
from .valueobjects import ModelParameters, ModelMetrics, TrainingResult, EvaluationResult
from .entities import SurrogateTrainingMetadata
from .surrogate_base import BaseSurrogateModel, BaseSurrogateTrainer
from ._protocols import ISurrogate
from .surrogate_mock import SurrogateMock
```

**Tasks:**
- [ ] Define public API surface in _imports.py
- [ ] Update __init__.py to expose the correct classes
- [ ] Check for circular dependencies and resolve with proper typing

### Implementation Checklist Summary

1. **Core Structure**:
   - [ ] Create directory structure
   - [ ] Set up import hierarchy to avoid circular dependencies

2. **Enum Layer**:
   - [ ] Define SurrogateModelType
   - [ ] Define TrainingStatus
   - [ ] Define ModelFramework

3. **Protocol Layer**:
   - [ ] Define ISurrogate protocol

4. **Value Objects Layer**:
   - [ ] Implement ModelParameters
   - [ ] Implement ModelMetrics
   - [ ] Implement TrainingResult
   - [ ] Implement EvaluationResult

5. **Entities Layer**:
   - [ ] Implement SurrogateTrainingMetadata with validation

6. **Base Classes Layer**:
   - [ ] Implement BaseSurrogateModel
   - [ ] Implement BaseSurrogateTrainer
   - [ ] Add specialized base trainers

7. **Implementations Layer**:
   - [ ] Implement MockSurrogateModel
   - [ ] Implement MockTimeSeriesTrainer and MockFlatTrainer
   - [ ] Implement SurrogateMock

8. **Utilities Layer**:
   - [ ] Implement data validation utilities
   - [ ] Implement time-series helpers

9. **Testing**:
   - [ ] Create test cases for SurrogateMock
   - [ ] Verify both time-series and non-time-series functionality
