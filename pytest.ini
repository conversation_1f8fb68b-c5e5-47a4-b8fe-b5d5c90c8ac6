[pytest]
# General options
addopts = -v -s --color=yes --no-header 

# Enable live log call with lower threshold (enable logging during test execution)
log_cli = true 
log_cli_level = INFO
log_cli_format = [%(levelname)-8s] %(message)s
log_auto_indent = true

# Setup test paths
testpaths =
    frontend/tests
    backend/tests
python_files = test_*.py
markers =
    frontend: marks tests as frontend
    backend: marks tests as backend

# Set Python path
pythonpath = .

####################

# Additional comments for clarity:
# -v: Verbose output
# -s: Disable output capture (allows print statements to be shown)
# --color=yes: Enable colored output

# log_cli_level = WARNING: This setting effectively mutes most collection logs
# log_cli = true: This enables live logging during test execution (live log call)