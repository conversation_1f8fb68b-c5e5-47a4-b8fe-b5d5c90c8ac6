"""
This is a simple wrapper over different ML frameworks like pytorch, sklearn and xgboost.

It needs to do the following:
    - 1. PREDICT
        - when doing predict, it needs to do proper  transformations and inverse transformations, returning a dataframe. 
        - when doing predict, it needs to be aware if its a timeseries model and transoform the dataframe accordingly
    - 2. SERIALIZE / DESERIALIZE
        - it needs to be able to serialize and deserialize, ideally to format that can be stored in SQL databases. (advise on this)
        - this will later be used in a repo pattern for persistence in postgre, mlflow or folder. If blob storage is preferred, to point out

    - Key Methodsd
"""

from __future__ import annotations
from typing import Dict, List, Optional, Tuple, Any, Union, cast, TypeVar, Generic
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
import numpy.typing as npt
import torch
import pickle
import typing as T
import io
import base64


from ..valueobjects import VOMetadata_General
from ..transformers import SurrogateDataTransformer



TModel = TypeVar("TModel")

class BaseSurrogateModel(Generic[TModel],ABC):
    """
    Abstract Base Class for trained models capable of making predictions and
    providing essential metadata for logging, inspection, and serving.
    """
    def __init__(
            self,
            native_model: TModel,
            datatransformer: SurrogateDataTransformer
    ):
        self._native_model = native_model
        self.datatransformer: SurrogateDataTransformer = datatransformer
    
    
    # PROPERTIES
    #---------------------------------------------------
    
    @property
    def native_model(self) -> Any:
        """Access to the underlying native model instance"""
        if self._native_model is None:
            raise ValueError("No native model available")
        return self._native_model
        
    @property
    def output_names(self) -> Optional[List[str]]:
        """Names of the output/target columns for predictions"""
        return self.datatransformer._initial_y_variable_cols
    
    @property
    def is_timeseries(self) -> bool:
        return self.datatransformer.is_timeseries

    @property
    def feature_names_in_(self) -> List[str]:
        """
        List of feature names expected by model
        
        Raises:
            ValueError: If feature names are not available
        """
        return self.datatransformer._initial_x_variable_cols
    
    # ABSTRACT METHODS
    #---------------------------------------------------
    
    @abstractmethod
    def predict(self, data: pd.DataFrame, *, fill_missing_timesteps: bool = False) -> pd.DataFrame:
        """Make predictions on new data"""
        pass
    
    @abstractmethod
    def serialize_native_model(self) -> str:
        pass
    
    @classmethod
    @abstractmethod
    def deserialize_native_model(cls, data)-> TModel:
        pass




class PyTorchSurrogateModel(BaseSurrogateModel[torch.nn.Module]):

    def __init__(self, native_model: torch.nn.Module, datatransformer: SurrogateDataTransformer, device: Optional[str] = None):
        super().__init__(native_model, datatransformer)
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        self._native_model.to(self.device)

    def predict(self, data: pd.DataFrame, *, fill_missing_timesteps: bool = False) -> pd.DataFrame:
        """Make predictions using PyTorch model with proper device handling"""
        # Prepare model
        self._native_model.eval()
        
        # Transform input data
        arr_x, _ = self.datatransformer.transform(data, None, fill_missing_timesteps=fill_missing_timesteps)
        
        # Convert to tensor and move to correct device
        x_tensor = torch.tensor(arr_x, dtype=torch.float32).to(self.device)
        
        # Get predictions
        with torch.no_grad():
            y_pred = self._native_model(x_tensor)
        
        # Move back to CPU and convert to numpy
        y_pred_numpy = y_pred.cpu().numpy()
        
        # Inverse transform
        return self.datatransformer.inv_transform(y_pred_numpy)

    def to(self, device: str) -> 'PyTorchSurrogateModel':
        """Move model to specified device"""
        self.device = device
        self._native_model.to(device)
        return self

    def serialize_native_model(self) -> str:
        """Serialize complete PyTorch model to base64-encoded string"""
        import io
        import base64
        import torch
        
        buffer = io.BytesIO()
        
        # Move to CPU temporarily for serialization
        original_device = self.device
        self._native_model.cpu()
        
        # Save the entire model (architecture + weights)
        torch.save(self._native_model, buffer)
        
        # Restore device
        if original_device != 'cpu':
            self._native_model.to(original_device)
            
        return base64.b64encode(buffer.getvalue()).decode('utf-8')

    @classmethod
    def deserialize_native_model(cls, data: str) -> torch.nn.Module:
        """Deserialize complete PyTorch model from base64 string"""
        import io
        import base64
        import torch
        
        buffer = io.BytesIO(base64.b64decode(data))
        model = torch.load(buffer, map_location='cpu')
        return model
