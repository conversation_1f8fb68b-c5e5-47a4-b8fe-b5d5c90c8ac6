# Design Scope: `BaseSurrogateModel` Class

## 1. Scope

This document outlines the design and implementation plan for the `BaseSurrogateModel` abstract base class and its concrete implementations (e.g., `PyTorchSurrogateModel`). This class serves as a unified wrapper over different ML frameworks (PyTorch, scikit-learn, XGBoost), providing a consistent interface for prediction and serialization while handling the appropriate data transformations.

## 2. Goals

* Provide a framework-agnostic prediction interface for ML models
* Handle data transformations consistently across all model types
* Support both tabular and time series data formats
* Enable serialization/deserialization of models to support persistence
* Expose model metadata and capabilities for inspection
* Maintain clean separation between domain logic and infrastructure concerns

## 3. Key Concepts & Principles

* **Adapter Pattern**: Wrap framework-specific implementation details behind a unified interface
* **Dependency Injection**: Accept data transformers and native models rather than creating them internally
* **Separation of Concerns**: Model logic separate from persistence infrastructure
* **Domain-Driven Design**: Focus on the problem domain rather than technical implementation details
* **Interface Segregation**: Clear separation between prediction, metadata, and serialization concerns
* **Immutability**: Model objects are generally immutable after initialization

## 4. High-Level Design

The `BaseSurrogateModel` will be an abstract generic class defining the core interface for all surrogate models. Concrete implementations (e.g., `PyTorchSurrogateModel`, `SKLearnSurrogateModel`) will adapt specific ML frameworks to this interface. The model will work with the `SurrogateDataTransformer` to handle data processing during prediction.

## 5. Detailed Design & Implementation Stubs

### 5.1 Base Abstract Class

```python
from __future__ import annotations
from typing import Dict, List, Optional, Tuple, Any, Union, TypeVar, Generic
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
import numpy.typing as npt

from ..valueobjects import VOMetadata_General
from ..transformers import SurrogateDataTransformer

TModel = TypeVar("TModel")

class BaseSurrogateModel(Generic[TModel], ABC):
    """
    Abstract Base Class for trained models wrapping various ML frameworks.
    Provides unified interface for prediction, serialization, and metadata access.
    
    Key capabilities:
    1. Framework-agnostic prediction API
    2. Input/output transformation handling
    3. Serialization/deserialization
    4. Metadata and capabilities inspection
    """
    def __init__(
            self,
            native_model: TModel,
            datatransformer: SurrogateDataTransformer,
            metadata: Optional[VOMetadata_General] = None
    ):
        self._native_model = native_model
        self.datatransformer = datatransformer
        self._metadata = metadata
    
    # PROPERTIES
    #---------------------------------------------------
    
    @property
    def native_model(self) -> Any:
        """Access to the underlying native model instance"""
        if self._native_model is None:
            raise ValueError("No native model available")
        return self._native_model
        
    @property
    def output_names(self) -> Optional[List[str]]:
        """Names of the output/target columns for predictions"""
        return self.datatransformer._initial_y_variable_cols
    
    @property
    def is_timeseries(self) -> bool:
        """Whether this is a time series model"""
        return self.datatransformer.is_timeseries

    @property
    def feature_names_in_(self) -> List[str]:
        """List of feature names expected by model"""
        return self.datatransformer._initial_x_variable_cols
    
    @property 
    def metadata(self) -> Optional[VOMetadata_General]:
        """Model metadata"""
        return self._metadata
    
    # PREDICTION INTERFACE
    #---------------------------------------------------
    
    @abstractmethod
    def predict(self, X: Union[pd.DataFrame, np.ndarray]) -> np.ndarray:
        """
        Make predictions using the model.
        
        Args:
            X: Input features as DataFrame or numpy array
            
        Returns:
            Predictions as numpy array
        """
        pass
    
    def predict_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Make predictions directly from a DataFrame and return results as DataFrame.
        Automatically handles data transformations.
        
        Args:
            df: Input DataFrame with features
            
        Returns:
            DataFrame with predictions
        """
        # Implementation will use predict() and convert results to DataFrame
        pass
    
    # SERIALIZATION INTERFACE
    #---------------------------------------------------
    @abstractmethod
    def serialize_native_model(self) -> bytes:
        """
        Serialize only the native model to bytes, excluding transformer and metadata.
        This focuses on the raw underlying ML framework model.
        
        Returns:
            Binary representation of the native model only
        """
        pass

    @classmethod
    @abstractmethod
    def deserialize(
        cls, 
        data: bytes, 
        datatransformer: SurrogateDataTransformer,
        metadata: Optional[VOMetadata_General] = None
        # TODO model config to be injected here as well
    ) -> 'BaseSurrogateModel':
        """
        Deserialize model from bytes, with required data transformer injection.
        
        Args:
            data: Binary model data
            datatransformer: Required transformer for data processing
            metadata: Optional metadata
            
        Returns:
            Instantiated model
        """
        pass
    
    # MODEL INSPECTION
    #---------------------------------------------------
    
    def get_model_card(self) -> Dict[str, Any]:
        """
        Get a comprehensive model card with metadata and capabilities.
        
        Returns:
            Dictionary with model information
        """
        info = {
            "model_type": self.__class__.__name__,
            "framework_type": self._get_framework_name(),
            "is_timeseries": self.is_timeseries,
            "feature_count": len(self.feature_names_in_),
            "output_count": len(self.output_names) if self.output_names else 0,
            "feature_names": self.feature_names_in_,
            "output_names": self.output_names,
        }
        
        # Add metadata if available
        if self._metadata:
            info.update({
                "metadata": self._metadata.model_dump(),
            })
            
        return info
    
    @abstractmethod
    def _get_framework_name(self) -> str:
        """Get the name of the ML framework used"""
        pass
```

### 5.2 PyTorch Implementation

```python
import torch
import io
import pickle
from typing import Union, Optional

class PyTorchSurrogateModel(BaseSurrogateModel[torch.nn.Module]):
    """
    PyTorch-specific implementation of BaseSurrogateModel.
    Handles PyTorch model prediction and serialization concerns.
    """
    
    def __init__(
        self,
        native_model: torch.nn.Module,
        datatransformer: SurrogateDataTransformer,
        metadata: Optional[VOMetadata_General] = None,
        device: str = "cpu"
    ):
        super().__init__(native_model, datatransformer, metadata)
        self.device = device
        self._native_model.to(device)
        self._native_model.eval()  # Set to evaluation mode
    
    def predict(self, X: Union[pd.DataFrame, np.ndarray]) -> np.ndarray:
        """
        Make predictions with PyTorch model.
        
        Args:
            X: Input features as DataFrame or numpy array
            
        Returns:
            Predictions as numpy array
        """
        # Transform input if necessary
        if isinstance(X, pd.DataFrame):
            X_transformed = self.datatransformer.transform_X(X)
        else:
            X_transformed = X
            
        # Convert to tensor
        X_tensor = torch.tensor(X_transformed, dtype=torch.float32, device=self.device)
        
        # Make prediction
        with torch.no_grad():
            y_pred_tensor = self._native_model(X_tensor)
            
        # Convert back to numpy
        y_pred = y_pred_tensor.cpu().numpy()
        
        # Inverse transform if needed
        if hasattr(self.datatransformer, 'inverse_transform_y'):
            y_pred = self.datatransformer.inverse_transform_y(y_pred)
            
        return y_pred
    
    def predict_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Make predictions and return as DataFrame with named columns.
        
        Args:
            df: Input DataFrame with features
            
        Returns:
            DataFrame with predictions
        """
        # Get predictions
        y_pred = self.predict(df)
        
        # Create DataFrame from predictions
        if self.output_names:
            return pd.DataFrame(y_pred, columns=self.output_names, index=df.index)
        else:
            return pd.DataFrame(y_pred, index=df.index)
    
    # ----------------------
    # SERIALIZATION

    def serialize_native_model(self) -> bytes:
        """
        Serialize only the PyTorch model to bytes, excluding transformer.
        
        Returns:
            Binary representation of the PyTorch model
        """
        buffer = io.BytesIO()
        torch.save({
            'model_state_dict': self._native_model.state_dict(),
            'model_class': type(self._native_model).__name__,
            'model_module': type(self._native_model).__module__,
            'architecture_params': self._extract_architecture_params()
        }, buffer)
        
        buffer.seek(0)
        return buffer.getvalue()
    
    def _extract_architecture_params(self) -> Dict[str, Any]:
        """
        Extract architecture parameters required to reconstruct the model.
        This is model-specific and would need customization.
        
        Returns:
            Dictionary of architecture parameters
        
        TODO look at model config for this
        """
        # This is a simplification - actual implementation would depend on model type
        return {
            # Example parameters that might be needed to reconstruct the model
            'input_dim': getattr(self._native_model, 'input_dim', None),
            'hidden_dims': getattr(self._native_model, 'hidden_dims', None),
            'output_dim': getattr(self._native_model, 'output_dim', None)
        }
    
    @classmethod
    def deserialize(
        cls, 
        data: bytes, 
        datatransformer: SurrogateDataTransformer,
        metadata: Optional[VOMetadata_General] = None
    ) -> 'PyTorchSurrogateModel':
        """
        Deserialize PyTorch model from bytes with required data transformer.
        
        Args:
            data: Binary model data
            datatransformer: Required transformer for data processing
            metadata: Optional metadata
            
        Returns:
            Instantiated model
        """
        buffer = io.BytesIO(data)
        # TODO enable gpu loading? 
        saved_data = torch.load(buffer, map_location="cpu")
        
        # Import model class
        model_class_name = saved_data['model_class']
        model_module_name = saved_data.get('model_module', '__main__')
        
        # Dynamic import
        try:
            # TODO what is this doing?
            import importlib
            module = importlib.import_module(model_module_name)
            model_class = getattr(module, model_class_name)
        except (ImportError, AttributeError):
            raise ValueError(f"Could not load model class {model_class_name} from module {model_module_name}")
        
        # Create model instance using architecture parameters
        TODO use model config
        architecture_params = saved_data.get('architecture_params', {})
        # Filter None values and undefined parameters
        valid_params = {k: v for k, v in architecture_params.items() 
                       if v is not None and k in model_class.__init__.__code__.co_varnames}
        
        model = model_class(**valid_params)
        model.load_state_dict(saved_data['model_state_dict'])
        
        # Default to CPU device
        device = "cpu"
        
        return cls(model, datatransformer, metadata, device)
    
    
    def _get_framework_name(self) -> str:
        """
        Get PyTorch framework name and version.
        
        Returns:
            String representation of framework name and version
        """
        return f"PyTorch {torch.__version__}"
```

## 6. Data Structures

* **Input Data**: `pd.DataFrame` or `np.ndarray` for feature data
* **Output Data**: `np.ndarray` for raw predictions, `pd.DataFrame` for structured predictions
* **Serialized Model**: `bytes` for binary serialized model representation
* **Model Card**: `Dict[str, Any]` for model metadata and capabilities

## 7. Dependencies

* Core Python libraries: `numpy`, `pandas`
* ML frameworks: Depending on implementation (e.g., `torch` for PyTorch models)
* Serialization: `pickle`, `io` (standard library)
* Data transformation: Custom `SurrogateDataTransformer` class

## 8. Implementation Notes and Best Practices

1. **Error Handling**:
   - Add robust error handling for data validation
   - Provide clear error messages for serialization/deserialization failures
   - Check for shape/dimension mismatches before prediction

2. **Serialization Strategy**:
   - For PyTorch: Use `torch.save` for model weights and architecture
   - For scikit-learn: Use `pickle` with protocol 4+ for better performance
   - Always include metadata about model type, framework, and version
   - Store transformer configuration and state alongside the model

3. **Performance Considerations**:
   - Implement batch prediction for large datasets
   - Consider GPU acceleration where appropriate
   - Manage memory usage during serialization of large models

4. **Framework-Specific Notes**:
   - PyTorch: Handle device placement (`cpu`/`cuda`)
   - scikit-learn: Preserve feature names and pre-processing
   - XGBoost: Include feature importance in metadata

## 9. Future Considerations / Out of Scope

* Integration with model registries (MLflow, etc.)
* Monitoring for model drift and performance degradation
* A/B testing capabilities
* Versioned model deployment
* Feature store integration
* Online learning / model updating capabilities
* Explainability beyond basic feature importance

This design provides a clean domain model that handles the core responsibilities of model prediction and serialization while following hexagonal architecture principles by avoiding infrastructure concerns like direct file I/O operations.

# BaseSurrogateModel Specification - Unified ML Model Wrapper

## Overview and Enhanced VOMetadata_General Requirements

After reviewing `VOMetadata_General`, I recommend adding the following fields to enhance model card functionality:

```python
# Additional recommended fields for VOMetadata_General
framework: str = Field(description="ML framework used (PyTorch, scikit-learn, etc)")
framework_version: str = Field(description="Framework version")
python_version: str = Field(description="Python version used for training")
training_info: Dict[str, Any] = Field(
    default_factory=dict,
    description="Training metadata (samples_count, training_duration, etc)"
)
model_limitations: Optional[str] = Field(
    default=None, 
    description="Known limitations or constraints"
)
```

These fields would provide essential context for model deployment and maintenance.

## Updated BaseSurrogateModel Specification

```python
class BaseSurrogateModel(Generic[TModel], ABC):
    """
    Abstract Base Class for trained models wrapping various ML frameworks.
    Provides unified interface for prediction, serialization, and metadata access.
    
    Key capabilities:
    1. Framework-agnostic prediction API
    2. Input/output transformation handling
    3. Serialization/deserialization
    4. Metadata and capabilities inspection
    """
    def __init__(
            self,
            native_model: TModel,
            datatransformer: SurrogateDataTransformer,
            metadata: Optional[VOMetadata_General] = None
    ):
        self._native_model = native_model
        self.datatransformer = datatransformer
        self._metadata = metadata
    
    # PROPERTIES
    #---------------------------------------------------
    
    @property
    def native_model(self) -> Any:
        """Access to the underlying native model instance"""
        if self._native_model is None:
            raise ValueError("No native model available")
        return self._native_model
        
    @property
    def output_names(self) -> Optional[List[str]]:
        """Names of the output/target columns for predictions"""
        return self.datatransformer._initial_y_variable_cols
    
    @property
    def is_timeseries(self) -> bool:
        """Whether this is a time series model"""
        return self.datatransformer.is_timeseries

    @property
    def feature_names_in_(self) -> List[str]:
        """List of feature names expected by model"""
        return self.datatransformer._initial_x_variable_cols
    
    @property 
    def metadata(self) -> Optional[VOMetadata_General]:
        """Model metadata"""
        return self._metadata
    
    # CORE FUNCTIONALITY
    #---------------------------------------------------
    
    @abstractmethod
    def predict(self, X: Union[pd.DataFrame, np.ndarray]) -> Union[pd.DataFrame, np.ndarray]:
        """
        Make predictions using the model.
        
        Args:
            X: Input features as DataFrame or numpy array
            
        Returns:
            Predictions as DataFrame (with column names) or numpy array
        """
        pass
    
    def predict_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Make predictions directly from a DataFrame and return results as DataFrame.
        Automatically handles data transformations.
        
        Args:
            df: Input DataFrame with features
            
        Returns:
            DataFrame with predictions
        """
        pass
    
    # SERIALIZATION
    #---------------------------------------------------
    
    @abstractmethod
    def serialize(self) -> bytes:
        """
        Serialize the model to bytes.
        
        Returns:
            Binary representation of the model
        """
        pass
    
    @classmethod
    @abstractmethod
    def deserialize(cls, data: bytes, metadata: Optional[VOMetadata_General] = None) -> 'BaseSurrogateModel':
        """
        Deserialize model from bytes.
        
        Args:
            data: Binary model data
            metadata: Optional metadata
            
        Returns:
            Instantiated model
        """
        pass
    
    def save(self, path: str) -> None:
        """
        Save model to filesystem.
        
        Args:
            path: Directory or file path
        """
        serialized = self.serialize()
        with open(path, 'wb') as f:
            f.write(serialized)
    
    @classmethod
    def load(cls, path: str, metadata: Optional[VOMetadata_General] = None) -> 'BaseSurrogateModel':
        """
        Load model from filesystem.
        
        Args:
            path: Path to model file
            metadata: Optional metadata
            
        Returns:
            Loaded model
        """
        with open(path, 'rb') as f:
            data = f.read()
        return cls.deserialize(data, metadata)
    
    # MODEL INSPECTION
    #---------------------------------------------------
    
    def get_model_card(self) -> Dict[str, Any]:
        """
        Get a comprehensive model card with metadata and capabilities.
        
        Returns:
            Dictionary with model information
        """
        info = {
            "model_type": self.__class__.__name__,
            "framework_type": self._get_framework_name(),
            "is_timeseries": self.is_timeseries,
            "feature_count": len(self.feature_names_in_),
            "output_count": len(self.output_names) if self.output_names else 0,
            "feature_names": self.feature_names_in_,
            "output_names": self.output_names,
        }
        
        # Add metadata if available
        if self._metadata:
            info.update({
                "metadata": self._metadata.model_dump(),
            })
            
        return info
    
    @abstractmethod
    def _get_framework_name(self) -> str:
        """Get the name of the ML framework used"""
        pass
```

## PyTorch Implementation Example

```python
class PyTorchSurrogateModel(BaseSurrogateModel[torch.nn.Module]):
    """PyTorch-specific implementation of BaseSurrogateModel"""
    
    def __init__(
        self,
        native_model: torch.nn.Module,
        datatransformer: SurrogateDataTransformer,
        metadata: Optional[VOMetadata_General] = None,
        device: str = "cpu"
    ):
        super().__init__(native_model, datatransformer, metadata)
        self.device = device
        self._native_model.to(device)
        self._native_model.eval()  # Set to evaluation mode
    
    def predict(self, X: Union[pd.DataFrame, np.ndarray]) -> np.ndarray:
        """Make predictions with PyTorch model"""
        # Transform input if necessary
        if isinstance(X, pd.DataFrame):
            X_transformed = self.datatransformer.transform_X(X)
        else:
            X_transformed = X
            
        # Convert to tensor
        X_tensor = torch.tensor(X_transformed, dtype=torch.float32, device=self.device)
        
        # Make prediction
        with torch.no_grad():
            y_pred_tensor = self._native_model(X_tensor)
            
        # Convert back to numpy
        y_pred = y_pred_tensor.cpu().numpy()
        
        # Inverse transform if needed
        if hasattr(self.datatransformer, 'inverse_transform_y'):
            y_pred = self.datatransformer.inverse_transform_y(y_pred)
            
        return y_pred
    
    def predict_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Make predictions and return as DataFrame with named columns"""
        # Get predictions
        y_pred = self.predict(df)
        
        # Create DataFrame from predictions
        if self.output_names:
            return pd.DataFrame(y_pred, columns=self.output_names, index=df.index)
        else:
            return pd.DataFrame(y_pred, index=df.index)
    
    def serialize(self) -> bytes:
        """Serialize PyTorch model to bytes"""
        # Save model state dict
        buffer = io.BytesIO()
        torch.save({
            'model_state_dict': self._native_model.state_dict(),
            'model_class': type(self._native_model).__name__,
            'transformer': pickle.dumps(self.datatransformer),
            'metadata': self._metadata.model_dump() if self._metadata else None,
        }, buffer)
        
        buffer.seek(0)
        return buffer.getvalue()
    
    @classmethod
    def deserialize(cls, data: bytes, metadata: Optional[VOMetadata_General] = None) -> 'PyTorchSurrogateModel':
        """Deserialize PyTorch model from bytes"""
        buffer = io.BytesIO(data)
        saved_data = torch.load(buffer, map_location="cpu")
        
        # Recover metadata
        metadata_dict = saved_data.get('metadata')
        if metadata is None and metadata_dict:
            metadata = VOMetadata_General(**metadata_dict)
        
        # Recover transformer
        transformer = pickle.loads(saved_data['transformer'])
        
        # Import and instantiate model class
        model_class_name = saved_data['model_class']
        # This is simplified; in practice you'd need more robust model class resolution
        model_class = globals()[model_class_name]
        
        # Create model instance
        # This assumes model can be instantiated with basic parameters
        # In practice, you'd need to store and recover model architecture
        model = model_class()
        model.load_state_dict(saved_data['model_state_dict'])
        
        return cls(model, transformer, metadata)
    
    def _get_framework_name(self) -> str:
        return f"PyTorch {torch.__version__}"
```

## Serialization Strategy Recommendation

For a fast V1 implementation:

1. **PyTorch Models**: Use `torch.save()` for model weights and architecture
2. **Scikit-learn Models**: Use `joblib` which is optimized for NumPy arrays
3. **Custom Objects**: Use Python's `pickle` with protocol 4+ for better performance
4. **Transformers**: Bundle with the model using `pickle`

For database storage:
- Store serialized binary data in BLOB fields
- Keep metadata in separate structured fields for querying
- Consider using file storage (S3/blob storage) for models >10MB and store only references in DB

## Implementation Notes

1. **Error Handling**: Add robust error handling especially for serialization/deserialization
2. **Versioning**: Track format versions in serialized data
3. **Testing**: Create tests that verify round-trip serialization works correctly
4. **Documentation**: Document expected formats clearly

This approach prioritizes:
- Quick implementation with standard Python serialization
- Framework agnosticism for storage/retrieval 
- Consistent API across model types
- Proper metadata association with models