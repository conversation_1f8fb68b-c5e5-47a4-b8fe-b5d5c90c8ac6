from __future__ import annotations

from ._imports import *
from ._enums import *
import base64
import gzip

from sklearn.metrics import (mean_squared_error, mean_absolute_error, 
                                   r2_score, max_error, mean_absolute_percentage_error)

################

# Base class for all VOs

class VOPydanticBase(BaseModel):
    """Base class for all Value Objects with consistent configuration"""
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        validate_assignment=True,
        # extra='forbid',
        frozen=True,
    )

class VOConfig_ParamBounds(VOPydanticBase):
    """Flexible parameter bounds representation"""
    type: Literal["continuous", "continuous_logscale", "integer", "categorical", "boolean"] = "continuous"
    min_value:float = 0.0
    max_value:float = 0.0
    choices: List[Any] = []
    
    @classmethod
    def continuous(cls, min_val: float, max_val: float) -> 'VOConfig_ParamBounds':
        return cls(type="continuous", min_value=min_val, max_value=max_val)
    
    @classmethod
    def integer(cls, min_val: int, max_val: int) -> 'VOConfig_ParamBounds':
        return cls(type="integer", min_value=min_val, max_value=max_val)
    
    @classmethod
    def categorical(cls, choices: List[Any]) -> 'VOConfig_ParamBounds':
        return cls(type="categorical", choices=choices)
    
    @classmethod
    def boolean(cls) -> 'VOConfig_ParamBounds':
        return cls(type="boolean", choices=[True, False])

class VOConfig_Parameter(VOPydanticBase):
    name: Hyperparams.BaseHPEnum
    value: Any
    tunable: bool = False
    bounds: Optional[VOConfig_ParamBounds] = None

    @field_validator('name', mode='before')
    @classmethod
    def coerce_name_from_str(cls, v):
        """Used to convert str to enum"""
        if isinstance(v, str):
            # Try every subclass of BaseHPEnum
            for enum_cls in Hyperparams.BaseHPEnum.__subclasses__():
                try:
                    return enum_cls(v)
                except ValueError:
                    continue
            raise ValueError(f"‘{v}’ is not a valid BaseHPEnum member")
        return v

    @model_validator(mode='after')
    def validate_value_in_bounds(self) -> 'VOConfig_Parameter':
        """Validate that value is within bounds if specified"""
        if self.bounds and self.value is not None:
            # CONT
            if self.bounds.type == "continuous" and not math.isnan(self.value):
                if self.bounds.min_value is not None and self.value < self.bounds.min_value:
                    raise ValueError(f"Value {self.value} below minimum {self.bounds.min_value}")
                if self.bounds.max_value is not None and self.value > self.bounds.max_value:
                    raise ValueError(f"Value {self.value} above maximum {self.bounds.max_value}")
            # INT
            elif self.bounds.type == "integer":
                if not float(self.value).is_integer():
                    raise ValueError(f"Value {self.value} must be an integer")
                if self.bounds.min_value is not None and self.value < self.bounds.min_value:
                    raise ValueError(f"Value {self.value} below minimum {self.bounds.min_value}")
                if self.bounds.max_value is not None and self.value > self.bounds.max_value:
                    raise ValueError(f"Value {self.value} above maximum {self.bounds.max_value}")
            # CAT
            elif self.bounds.type == "categorical" and self.bounds.choices:
                if self.value not in self.bounds.choices:
                    raise ValueError(f"Value {self.value} not in choices {self.bounds.choices}")
            # BOOL
            elif self.bounds.type == "boolean" and not isinstance(self.value, bool):
                raise ValueError(f"Value {self.value} must be boolean")

        return self

    def update_value(self, new_value: Any) -> 'VOConfig_Parameter':
        """Create new instance with updated value (validation handled by Pydantic)"""
        return self.model_copy(update={"value": new_value})
        
class VOConfig_Model(VOPydanticBase):
    """Complete model configuration with typed parameters"""
    algorithm: EnumSurrogateAlgorithm
    parameters: List[VOConfig_Parameter] = Field(default_factory=list)
    
    def get_param_value(self, param:Hyperparams.BaseHPEnum, default=None) -> Any:
        """Get parameter value with strong typing"""
        for p in self.parameters:
            if p.name == param:
                return p.value
        return default
    
    def get_param_values(self) -> Dict[Hyperparams.BaseHPEnum, Any]:
        """Get a dict of all param values"""
        return {
            p.name: p.value for
            p in self.parameters
        }
        
    def update_params(self, param_updates: Dict[Hyperparams.BaseHPEnum, Any]) -> 'VOConfig_Model':
        """
        Create new configuration with multiple updated parameter values
        """
        updated_params = []
        
        for p in self.parameters:
            if p.name in param_updates:
                updated_params.append(p.update_value(param_updates[p.name]))
            else:
                updated_params.append(p)
                
        return self.model_copy(update={"parameters": updated_params})

class VOConfig_Training(VOPydanticBase):
    """
    Configuration for model training process with comprehensive support for:
    - Both tree-based models and neural networks
    - Different validation strategies including cross-validation
    - Early stopping and convergence criteria
    - Time series specific configurations
    """
    # Common parameters for all models
    primary_metric: EnumMetricName = Field(
        default=EnumMetricName.RMSE,
        description="Primary metric for model evaluation and early stopping"
    )
    max_iterations: int = Field(
        default=100,
        description="Maximum training iterations (epochs for NNs, trees/rounds for tree models)"
    )
    random_seed: int = Field(
        default=42,
        description="Random seed for reproducibility"
    )
    metric_thresholds: Dict[Union[str, EnumMetricName], float] = Field(
        default_factory=dict,
        description="Minimum metric values required for model acceptance"
    )
    
    # Validation strategy
    validation_strategy: Literal["simple_split", "cross_validation","ts_simple_split", "ts_cross_validation"] = Field(
        default="simple_split",
        description="Validation strategy: simple_split, cross_validation, or ts_cross_validation"
    )
    validation_split: float = Field(
        default=0.2,
        description="Proportion of data to use for validation (for simple_split)"
    )
    cv_folds: Optional[int] = Field(
        default=None,
        description="Number of folds for cross-validation"
    )
    
    # Early stopping (compatible with both NN and trees)
    enable_early_stopping: bool = Field(
        default=True,
        description="Whether to use early stopping"
    )
    early_stopping_rounds: Optional[int] = Field(
        default=10,
        description="Stop after this many iterations without improvement"
    )
    early_stopping_min_delta: float = Field(
        default=0.001,
        description="Minimum change to qualify as improvement"
    )
    
    # Time series specific parameters
    # NOTE - this needs to propogate downstream to evaluate methods and VOTrainingset. currently not implmented, focus is on S2S
    ts_validation_window_size: Optional[int] = Field(
        default=None,
        description="Window size for time series validation (steps/observations)"
    )
    ts_validation_stride: Optional[int] = Field(
        default=None,
        description="Stride between validation windows for time series data"
    )
    ts_forecast_horizon: Optional[int] = Field(
        default=None,
        description="Number of steps to forecast ahead in time series models"
    )

    
    @model_validator(mode='after')
    def validate_config(self) -> 'VOConfig_Training':
        """Validate configuration values with algorithm-specific checks"""
        error_log: List[str] = []
        
        # Common validation
        if self.max_iterations <= 0:
            error_log.append(f"max_iterations must be positive, got {self.max_iterations}")
            
        # Strategy-specific validations    
        if self.validation_strategy == "simple_split":
            if not 0.0 <= self.validation_split < 1.0:
                error_log.append(f"validation_split must be between 0 and 1, got {self.validation_split}")
                
        elif self.validation_strategy == "cross_validation":
            if not self.cv_folds or self.cv_folds < 2:
                error_log.append(f"cv_folds must be at least 2 for cross-validation, got {self.cv_folds}")
                
        elif self.validation_strategy == "ts_cross_validation":
            # Check window size
            if self.ts_validation_window_size is None:
                error_log.append("ts_validation_window_size must be set for time_series_cv")
            elif self.ts_validation_window_size <= 0:
                error_log.append(f"ts_validation_window_size must be positive, got {self.ts_validation_window_size}")
                
            # Set default stride if not provided
            if self.ts_validation_stride is None:
                error_log.append("ts_validation_stride must be provided for time_series_cv")
        
        # Early stopping validation
        if self.enable_early_stopping and self.early_stopping_rounds is not None:
            if self.early_stopping_rounds <= 0:
                error_log.append(f"early_stopping_rounds must be positive, got {self.early_stopping_rounds}")

        if error_log:
            raise ValueError(f"Invalid training configuration: {'; '.join(error_log)}")
            
        return self
    
    @classmethod
    def create_for_tree_model(cls, 
                             model_type: str,
                             quick_mode: bool = False) -> 'VOConfig_Training':
        """
        Create configuration for tree-based models (Random Forest, Gradient Boosting)
        
        Args:
            model_type: Type of tree-based model, use EnumSurrogateAlgorithm values
            quick_mode: If True, uses faster but potentially less accurate settings
        """
        n_trees = 50 if quick_mode else 200
        early_stop = 5 if quick_mode else 20
        
        # Start with common parameters for all tree models
        # TODO update to match revisions
        result = cls(
            primary_metric=EnumMetricName.RMSE,  # Updated to use enum
            max_iterations=n_trees,
            early_stopping_rounds=early_stop,
            enable_early_stopping=True,
            validation_strategy="simple_split",
            validation_split=0.2,
        )
        
        # For time series models, use time series cross-validation
        if model_type in [EnumSurrogateAlgorithm.RANDOM_FOREST_TS.value, EnumSurrogateAlgorithm.GRADIENT_BOOSTING_TS.value]:
            result = result.model_copy(update={
                "validation_strategy": "ts_cross_validation",
                "cv_folds": 3,
                "ts_validation_window_size": 10
            })
        
        return result

    @classmethod
    def create_for_neural_network(cls, quick_mode: bool = False) -> 'VOConfig_Training':
        """
        Create configuration for neural network models (RNN, etc.)
        
        Args:
            quick_mode: If True, uses faster but potentially less accurate settings
        """
        epochs = 20 if quick_mode else 100
        patience = 5 if quick_mode else 15
        
        # Start with base NN config
        result = cls(
            primary_metric=EnumMetricName.RMSE,  # Updated to use enum
            max_iterations=epochs,
            early_stopping_rounds=patience,
            enable_early_stopping=True,
            validation_strategy="simple_split", 
            validation_split=0.2,
            ts_validation_window_size=10,
            ts_forecast_horizon=5,
            ts_validation_stride=1
        )
            
        return result

# DATA
class VODataset(VOPydanticBase):
    """Container for transformed training/inference data with standardized patterns"""
    # NOTE this or evaluate will need some refactoring when we do true timeseries prediction by incorporating window, steps, etc
    
    uid: uuid.UUID = Field(default_factory=uuid.uuid4)
    arr_x: np.ndarray = Field(description="Feature/input variable array")
    arr_y: np.ndarray = Field(description="Target/Resonponse Variable array")
    transformer_uid: uuid.UUID
    colnames_x: List[str]
    colnames_y:List[str] 
    pattern: Literal["tabular", "sequence"] = Field(
        default="tabular",
        description="Data pattern defining expected array shapes"
    )
    
    @model_validator(mode='after')
    def validate_samples(self) -> 'VODataset':
        """Validate data consistency based on pattern"""
        error_log: List[str] = []
        
        # Shape validation based on pattern
        if self.pattern == "tabular":
            # Tabular: x(n_samples, n_features), y(n_samples,) or y(n_samples, n_outputs)
            if len(self.arr_x.shape) != 2:
                error_log.append(f"Tabular x must be 2D (samples, features), got shape {self.arr_x.shape}")
            if len(self.arr_y.shape) not in [1, 2]:
                error_log.append(f"Tabular y must be 1D or 2D, got shape {self.arr_y.shape}")
                
        elif self.pattern == "sequence":
            # Sequence: x(n_samples, n_timesteps, n_features), y(n_samples, n_timesteps_out, n_outputs)
            if len(self.arr_x.shape) != 3:
                error_log.append(f"Sequence x must be 3D (samples, timesteps, features), got shape {self.arr_x.shape}")
            if len(self.arr_y.shape) != 3:
                error_log.append(f"Sequence y must be 3D (samples, timesteps, outputs), got shape {self.arr_y.shape}")
        
        # Consistent sample count check remains the same for both patterns
        n_samples_x = self.arr_x.shape[0]
        n_samples_y = self.arr_y.shape[0]
        if n_samples_x != n_samples_y:
            error_log.append(f"Sample count mismatch: x has {n_samples_x} samples, y has {n_samples_y} samples")
            
        # Feature name validation
        if self.pattern == "tabular":
            n_features = self.arr_x.shape[1]
        else:
            n_features = self.arr_x.shape[2]   # type: ignore
            
        if len(self.colnames_x) != n_features:
            error_log.append(f"Feature count mismatch: {len(self.colnames_x)} column names, {n_features} features")
            
        # Target name validation
        if self.colnames_y:
            if self.pattern == "tabular":
                n_outputs = self.arr_y.shape[1] if len(self.arr_y.shape) > 1 else 1
            else:
                n_outputs = self.arr_y.shape[2]  # For sequence, outputs in 3rd dimension
                
            if len(self.colnames_y) != n_outputs:
                error_log.append(f"Output count mismatch: {len(self.colnames_y)} column names, {n_outputs} outputs")
        
        if error_log:
            raise ValueError(f"Sample validation errors: {'; '.join(error_log)}")

        return self

    @field_serializer('arr_x', 'arr_y')
    def serialize_numpy_array(self, arr: np.ndarray) -> Dict[str, Any]:
        """
        Serialize numpy arrays to JSON-compatible format with compression for large arrays.

        Uses base64 encoding for data with metadata preservation for shape and dtype.
        Applies gzip compression for arrays larger than 1MB to optimize storage.

        Args:
            arr: NumPy array to serialize

        Returns:
            Dictionary containing serialized array data and metadata
        """

        # Convert array to bytes
        array_bytes = arr.tobytes()

        # Determine if compression should be used (threshold: 1MB)
        use_compression = len(array_bytes) > 1024 * 1024

        if use_compression:
            # Compress the bytes using gzip
            compressed_bytes = gzip.compress(array_bytes)
            data_bytes = compressed_bytes
        else:
            data_bytes = array_bytes

        # Encode to base64 for JSON serialization
        encoded_data = base64.b64encode(data_bytes).decode('utf-8')

        return {
            'data': encoded_data,
            'shape': list(arr.shape),
            'dtype': str(arr.dtype),
            'compressed': use_compression,
            'original_size': len(array_bytes)
        }

    @field_validator('arr_x', 'arr_y', mode='before')
    @classmethod
    def deserialize_numpy_array(cls, v: Any) -> np.ndarray:
        """
        Deserialize numpy arrays from JSON-compatible format.

        Handles both compressed and uncompressed data, reconstructing the original
        numpy array with correct shape and dtype.

        Args:
            v: Serialized array data (dict) or existing numpy array

        Returns:
            Reconstructed numpy array

        Raises:
            ValueError: If deserialization fails or data is corrupted
        """
        # If already a numpy array, return as-is (for direct instantiation)
        if isinstance(v, np.ndarray):
            return v

        # If not a dict, it's likely invalid serialized data
        if not isinstance(v, dict):
            raise ValueError(f"Expected dict or numpy array for array field, got {type(v)}")

        try:

            # Extract metadata
            encoded_data = v['data']
            shape = tuple(v['shape'])
            dtype = np.dtype(v['dtype'])
            is_compressed = v.get('compressed', False)
            original_size = v.get('original_size', 0)

            # Decode from base64
            data_bytes = base64.b64decode(encoded_data.encode('utf-8'))

            # Decompress if necessary
            if is_compressed:
                array_bytes = gzip.decompress(data_bytes)
            else:
                array_bytes = data_bytes

            # Verify data integrity
            if original_size > 0 and len(array_bytes) != original_size:
                raise ValueError(f"Data corruption detected: expected {original_size} bytes, got {len(array_bytes)}")

            # Reconstruct numpy array
            arr = np.frombuffer(array_bytes, dtype=dtype).reshape(shape)

            # Create a copy to ensure the array owns its data
            return arr.copy()

        except Exception as e:
            raise ValueError(f"Failed to deserialize numpy array: {str(e)}") from e


# LOGS, SURROGATEMODEL

class VOLog_ModelMetrics(VOPydanticBase):
    """Vector-oriented model metrics storage with per-variable tracking"""
    
    # Core storage - variable names and metrics as vectors
    variable_names: List[str] = Field(
        default_factory=list,
        description="Names of output variables"
    )
    
    # Core regression metrics as vectors
    rmse_values: List[float] = Field(
        default_factory=list,
        description="Root Mean Squared Error values per variable"
    )
    mae_values: List[float] = Field(
        default_factory=list,
        description="Mean Absolute Error values per variable"
    )
    r2_values: List[float] = Field(
        default_factory=list,
        description="R-squared coefficient values per variable"
    )
    mse_values: List[float] = Field(
        default_factory=list,
        description="Mean Squared Error values per variable"
    )
    max_error_values: List[float] = Field(
        default_factory=list,
        description="Maximum absolute error values per variable"
    )
    mape_values: List[float] = Field(
        default_factory=list,
        description="Mean Absolute Percentage Error values per variable"
    )
    
    # Time series specific metrics
    ts_mase_values: List[float] = Field(
        default_factory=list,
        description="Mean Absolute Scaled Error values per variable"
    )
    ts_smape_values: List[float] = Field(
        default_factory=list,
        description="Symmetric Mean Absolute Percentage Error values per variable"
    )
    
    # Signal quality metrics
    directional_accuracy_values: List[float] = Field(
        default_factory=list,
        description="Direction prediction accuracy per variable"
    )
    prediction_std_values: List[float] = Field(
        default_factory=list,
        description="Standard deviation of predictions per variable"
    )
    coefficient_of_variation_values: List[float] = Field(
        default_factory=list,
        description="Coefficient of variation per variable"
    )
    normalized_rmse_values: List[float] = Field(
        default_factory=list,
        description="Normalized RMSE per variable"
    )
    
    # Training metadata
    validation_samples_count: Optional[int] = Field(
        default=None,
        description="Number of samples used for validation/evaluation"
    )
    
    # Legacy field for backward compatibility
    output_specific_metrics: Dict[str, Dict[str, float]] = Field(
        default_factory=dict,
        description="Legacy field for backward compatibility"
    )
    
    @model_validator(mode='after')
    def validate_metrics_vectors(self) -> 'VOLog_ModelMetrics':
        """Validate that all metric vectors have the same length as variable_names"""
        if not self.variable_names:
            return self
            
        n_vars = len(self.variable_names)
        error_log: List[str] = []
        
        # Check all metric vectors have the same length
        for field_name, field_value in {
            "rmse_values": self.rmse_values,
            "mae_values": self.mae_values,
            "r2_values": self.r2_values,
            "mse_values": self.mse_values,
            "max_error_values": self.max_error_values,
            "mape_values": self.mape_values,
            "ts_mase_values": self.ts_mase_values,
            "ts_smape_values": self.ts_smape_values,
            "directional_accuracy_values": self.directional_accuracy_values,
            "prediction_std_values": self.prediction_std_values,
            "coefficient_of_variation_values": self.coefficient_of_variation_values,
            "normalized_rmse_values": self.normalized_rmse_values
        }.items():
            if field_value and len(field_value) != n_vars:
                error_log.append(f"{field_name} length ({len(field_value)}) != variable_names length ({n_vars})")
        
        # Validate metric value ranges
        for i, var_name in enumerate(self.variable_names):
            # R-squared should be between -inf and 1
            if self.r2_values and i < len(self.r2_values) and self.r2_values[i] > 1.0:
                error_log.append(f"R-squared for {var_name} ({self.r2_values[i]}) cannot exceed 1.0")
            
            # Error metrics should be non-negative
            for metric_name, values in {
                "RMSE": self.rmse_values,
                "MAE": self.mae_values,
                "MSE": self.mse_values,
                "Max Error": self.max_error_values,
                "MAPE": self.mape_values,
                "MASE": self.ts_mase_values,
                "Prediction StdDev": self.prediction_std_values
            }.items():
                if values and i < len(values) and values[i] is not None and values[i] < 0:
                    error_log.append(f"{metric_name} for {var_name} ({values[i]}) cannot be negative")
            
            # Directional accuracy should be between 0 and 1
            if (self.directional_accuracy_values and i < len(self.directional_accuracy_values) and 
                self.directional_accuracy_values[i] is not None):
                if not 0.0 <= self.directional_accuracy_values[i] <= 1.0:
                    error_log.append(f"Directional accuracy for {var_name} ({self.directional_accuracy_values[i]}) must be between 0 and 1")
        
        if error_log:
            raise ValueError(f"Metrics validation errors: {'; '.join(error_log)}")
            
        return self
    
    # Legacy compatibility properties
    @property
    def rmse(self) -> Optional[float]:
        """Aggregated RMSE across all variables"""
        return self.get_rmse()
    
    @property
    def mae(self) -> Optional[float]:
        """Aggregated MAE across all variables"""
        return self.get_mae()
    
    @property
    def r2(self) -> Optional[float]:
        """Aggregated R² across all variables"""
        return self.get_r2()
    
    @property
    def mse(self) -> Optional[float]:
        """Aggregated MSE across all variables"""
        return self.get_mse()
    
    @property
    def max_error(self) -> Optional[float]:
        """Aggregated max error across all variables"""
        return self.get_max_error()
    
    @property
    def mape(self) -> Optional[float]:
        """Aggregated MAPE across all variables"""
        return self.get_mape()
    
    @property
    def ts_mase(self) -> Optional[float]:
        """Aggregated MASE across all variables"""
        return self.get_ts_mase()
    
    @property
    def ts_smape(self) -> Optional[float]:
        """Aggregated SMAPE across all variables"""
        return self.get_ts_smape()
    
    @property
    def directional_accuracy(self) -> Optional[float]:
        """Aggregated directional accuracy across all variables"""
        return self.get_directional_accuracy()
    
    @property
    def prediction_std(self) -> Optional[float]:
        """Aggregated prediction std across all variables"""
        return self.get_prediction_std()
    
    @property
    def coefficient_of_variation(self) -> Optional[float]:
        """Aggregated coefficient of variation across all variables"""
        return self.get_coefficient_of_variation()
    
    @property
    def normalized_rmse(self) -> Optional[float]:
        """Aggregated normalized RMSE across all variables"""
        return self.get_normalized_rmse()
    
    # Core accessor methods
    def _get_metric_value(self, metric_values: List[float], variable_name: Optional[str] = None) -> Optional[float]:
        """Generic accessor for any metric vector"""
        if not metric_values:
            return None
        
        if variable_name is None:
            # Return aggregate (mean)
            return float(np.mean(metric_values))
        
        try:
            idx = self.variable_names.index(variable_name)
            return metric_values[idx]
        except (ValueError, IndexError):
            raise ValueError(f"Variable '{variable_name}' not found in metrics")
    
    def get_rmse(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get RMSE for specific variable or aggregated"""
        return self._get_metric_value(self.rmse_values, variable_name)
    
    def get_mae(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get MAE for specific variable or aggregated"""
        return self._get_metric_value(self.mae_values, variable_name)
    
    def get_r2(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get R² for specific variable or aggregated"""
        return self._get_metric_value(self.r2_values, variable_name)
    
    def get_mse(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get MSE for specific variable or aggregated"""
        return self._get_metric_value(self.mse_values, variable_name)
    
    def get_max_error(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get max error for specific variable or aggregated"""
        return self._get_metric_value(self.max_error_values, variable_name)
    
    def get_mape(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get MAPE for specific variable or aggregated"""
        return self._get_metric_value(self.mape_values, variable_name)
    
    def get_ts_mase(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get MASE for specific variable or aggregated"""
        return self._get_metric_value(self.ts_mase_values, variable_name)
    
    def get_ts_smape(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get SMAPE for specific variable or aggregated"""
        return self._get_metric_value(self.ts_smape_values, variable_name)
    
    def get_directional_accuracy(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get directional accuracy for specific variable or aggregated"""
        return self._get_metric_value(self.directional_accuracy_values, variable_name)
    
    def get_prediction_std(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get prediction std for specific variable or aggregated"""
        return self._get_metric_value(self.prediction_std_values, variable_name)
    
    def get_coefficient_of_variation(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get coefficient of variation for specific variable or aggregated"""
        return self._get_metric_value(self.coefficient_of_variation_values, variable_name)
    
    def get_normalized_rmse(self, variable_name: Optional[str] = None) -> Optional[float]:
        """Get normalized RMSE for specific variable or aggregated"""
        return self._get_metric_value(self.normalized_rmse_values, variable_name)
    
    def get_value(self, metric: EnumMetricName, variable_name: Optional[str] = None) -> Optional[float]:
        """Get metric value by enum type"""
        metric_map = {
            EnumMetricName.RMSE: self.get_rmse,
            EnumMetricName.MAE: self.get_mae,
            EnumMetricName.R2: self.get_r2,
            EnumMetricName.MSE: self.get_mse,
            EnumMetricName.MAX_ERROR: self.get_max_error,
            EnumMetricName.MAPE: self.get_mape,
            EnumMetricName.TS_MASE: self.get_ts_mase,
            EnumMetricName.TS_SMAPE: self.get_ts_smape,
            EnumMetricName.DIRECTIONAL_ACCURACY: self.get_directional_accuracy,
            EnumMetricName.PREDICTION_STD: self.get_prediction_std,
            EnumMetricName.COEFFICIENT_OF_VARIATION: self.get_coefficient_of_variation,
            EnumMetricName.NORMALIZED_RMSE: self.get_normalized_rmse
        }
        
        if metric not in metric_map:
            raise ValueError(f"Metric {metric} not found in metrics collection")
            
        return metric_map[metric](variable_name)
    
    def is_better_than(self, other: 'VOLog_ModelMetrics', 
                     metric: Union[str, EnumMetricName],
                     variable_name: Optional[str] = None) -> bool:
        """
        Compare this metrics object with another based on a specific metric.
        
        Args:
            other: Metrics to compare against
            metric: Metric to compare (as string or EnumMetricName)
            variable_name: Specific variable to compare (None for aggregate)
            
        Returns:
            True if this object is better, False otherwise
        """
        # Convert string to enum if needed
        if isinstance(metric, str):
            try:
                metric = EnumMetricName(metric)
            except ValueError:
                pass  # Keep as string if not in enum
        
        # Get values to compare
        if isinstance(metric, EnumMetricName):
            this_value = self.get_value(metric, variable_name)
            other_value = other.get_value(metric, variable_name)
            is_higher_better = EnumMetricName.is_higher_better(metric)
        else:
            # Handle string-based metric access (legacy)
            this_method = getattr(self, f"get_{metric}", None)
            other_method = getattr(other, f"get_{metric}", None)
            
            if not this_method or not other_method:
                raise ValueError(f"Unknown metric: {metric}")
                
            this_value = this_method(variable_name)
            other_value = other_method(variable_name)
            
            # For string metrics, follow standard rules
            is_higher_better = metric in ["r2", "directional_accuracy"]
        
        if this_value is None or other_value is None:
            return False
            
        return this_value > other_value if is_higher_better else this_value < other_value
    
    @classmethod
    def create_from_predictions(cls, y_true: np.ndarray, y_pred: np.ndarray, 
                        response_var_labels: List[str]) -> 'VOLog_ModelMetrics':
        """
        Factory method to create metrics from prediction arrays
        
        Args:
            y_true: True target values
            y_pred: Predicted target values
            variable_names: Names of output variables
            
        Returns:
            Populated VOLog_ModelMetrics instance
        """
        # Ensure y_true and y_pred are properly shaped
        if len(y_true.shape) > 2:
            # Reshape 3D arrays to 2D (samples, variables)
            n_samples, n_timesteps, n_vars = y_true.shape
            y_true = y_true.reshape(n_samples * n_timesteps, n_vars)
            y_pred = y_pred.reshape(n_samples * n_timesteps, n_vars)
        
        n_variables = y_true.shape[1] if len(y_true.shape) > 1 else 1
        
        # Single output case handling
        if n_variables == 1 and len(y_true.shape) == 1:
            y_true = y_true.reshape(-1, 1)
            y_pred = y_pred.reshape(-1, 1)
        
        # Validate variable names match data shape
        if len(response_var_labels) != n_variables:
            raise ValueError(f"Variable names count ({len(response_var_labels)}) doesn't match data columns ({n_variables})")
        
        # Initialize value arrays
        rmse_values = []
        mae_values = []
        r2_values = []
        mse_values = []
        max_error_values = []
        mape_values = []
        directional_acc_values = []
        prediction_std_values = []
        coef_var_values = []
        norm_rmse_values = []
        
        # Calculate metrics for each variable
        for i in range(n_variables):
            y_true_col = y_true[:, i]
            y_pred_col = y_pred[:, i]
            
            # Basic metrics
            col_mse = float(mean_squared_error(y_true_col, y_pred_col))
            col_rmse = float(np.sqrt(col_mse))
            col_r2 = float(r2_score(y_true_col, y_pred_col))
            col_mae = float(mean_absolute_error(y_true_col, y_pred_col))
            col_max = float(max_error(y_true_col, y_pred_col))
            col_pred_std = float(np.std(y_pred_col))
            
            rmse_values.append(col_rmse)
            mae_values.append(col_mae)
            r2_values.append(col_r2)
            mse_values.append(col_mse)
            max_error_values.append(col_max)
            prediction_std_values.append(col_pred_std)
            
            # MAPE calculation
            try:
                col_mape = float(mean_absolute_percentage_error(y_true_col, y_pred_col))
                mape_values.append(col_mape)
            except (ValueError, ZeroDivisionError):
                mape_values.append(None)
            
            # Directional accuracy
            if len(y_true_col) > 1:
                true_diffs = np.diff(y_true_col)
                pred_diffs = np.diff(y_pred_col)
                directional_match = np.sign(true_diffs) == np.sign(pred_diffs)
                col_dir_acc = float(np.mean(directional_match))
                directional_acc_values.append(col_dir_acc)
            else:
                directional_acc_values.append(None)
            
            # Coefficient of variation
            col_mean = np.mean(y_pred_col)
            if col_mean != 0:
                col_coef_var = col_pred_std / col_mean
                coef_var_values.append(float(col_coef_var))
            else:
                coef_var_values.append(None)
            
            # Normalized RMSE
            col_range = np.max(y_true_col) - np.min(y_true_col)
            if col_range > 0:
                col_norm_rmse = col_rmse / col_range
                norm_rmse_values.append(float(col_norm_rmse))
            else:
                norm_rmse_values.append(None)
        
        # Create and return metrics object
        metrics = cls(
            variable_names=response_var_labels,
            rmse_values=rmse_values,
            mae_values=mae_values,
            r2_values=r2_values,
            mse_values=mse_values,
            max_error_values=max_error_values,
            mape_values=mape_values,
            directional_accuracy_values=directional_acc_values,
            prediction_std_values=prediction_std_values,
            coefficient_of_variation_values=coef_var_values,
            normalized_rmse_values=norm_rmse_values,
            validation_samples_count=len(y_true)
        )
        
        return metrics

class VOLog_EpochMetrics(VOPydanticBase):
    """Metrics recorded at a single training epoch"""
    epoch: int = Field(description="The epoch number")
    
    # Primary metrics (always present)
    metric_name: EnumMetricName = Field(description="Primary metric name (e.g., 'mse', 'rmse')")
    train_metric: float = Field(description="Training metric value")
    validation_metric: Optional[float] = Field(default=None, description="Validation metric value")
    
    # Secondary metrics (optional)
    secondary_metric_name: Optional[EnumMetricName] = Field(default=None, description="Secondary metric name")
    train_secondary_metric: Optional[float] = Field(default=None, description="Secondary training metric")
    validation_secondary_metric: Optional[float] = Field(default=None, description="Secondary validation metric")
    
    # Sample counts
    train_samples: int = Field(description="Number of training samples")
    validation_samples: Optional[int] = Field(default=None, description="Number of validation samples")

class VOResult_ModelTest(VOPydanticBase):
    """Results from model evaluation on the test set"""
    
    test_metrics: VOLog_ModelMetrics = Field(
        description="Evaluation metrics calculated on the test set"
    )
    feature_importance: Dict[str, float] = Field(
        default_factory=dict,
        description="Feature importance scores from the final model"
    )
    
    # Array-based representation of prediction artifacts
    x_test_uid: Optional[uuid.UUID] = Field(
        default=None,
        description="Reference to stored test feature array"
    )
    prediction_summary: Dict[str, Any] = Field(
        default_factory=dict,
        description="Statistical summary of predictions (distribution characteristics)"
    )
    evaluation_time_ms: Optional[float] = Field(
        default=None,
        description="Time taken for test set evaluation in milliseconds"
    )
    
    @model_validator(mode='after')
    def validate_evaluation(self) -> 'VOResult_ModelTest':
        """Validate evaluation results"""
        error_log: List[str] = []
        
        if self.evaluation_time_ms is not None and self.evaluation_time_ms <= 0:
            error_log.append(f"Evaluation time ({self.evaluation_time_ms}ms) must be positive")
            
        if error_log:
            raise ValueError(f"Evaluation result validation errors: {'; '.join(error_log)}")
            
        return self
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of test evaluation results"""
        return {
            "test_metrics": self.test_metrics.model_dump(mode="json"),
            "feature_importance": {k: float(v) for k, v in self.feature_importance.items()},
            "prediction_summary": self.prediction_summary,
            "evaluation_time_ms": self.evaluation_time_ms
        }

###############

# VO HPO

class VOConfig_HPO(VOPydanticBase):
    """Configuration for hyperparameter optimization"""
    
    # Core HPO settings
    is_enable: bool = Field(
        default=False,
        description="Whether to perform hyperparameter optimization"
    )
    n_trials: int = Field(
        default=20,
        description="Maximum number of HPO trials to run"
    )
    direction: Optional[Literal["minimize", "maximize"]] = Field(
        default=None,
        description="Direction of optimization for primary metric (if None, inferred from metric)"
    )
    
    # Simplified algorithm selection
    sampler: Literal["tpe", "random"] = Field(
        default="tpe",
        description="Sampling algorithm (tpe=Bayesian optimization, random=Random search)"
    )
    pruner: Literal["median", "hyperband", "none"] = Field(
        default="median",
        description="Pruning algorithm (median=Conservative, hyperband=Aggressive, none=No pruning)"
    )
    
    # Resource constraints
    timeout_seconds: Optional[int] = Field(
        default=None,
        description="Maximum time budget for optimization in seconds"
    )
    n_parallel_jobs: int = Field(
        default=-1,
        description="Number of parallel optimization jobs"
    )
    
    @classmethod
    def create_preset(cls, preset_name: str, algorithm_type: EnumSurrogateAlgorithm) -> "VOConfig_HPO":
        """Factory method to create preset HPO configurations for different algorithms"""
        base_config = cls(is_enable=True)
        
        if preset_name == "conservative":
            base_config.n_trials = 10
            base_config.sampler = "tpe"
        elif preset_name == "aggressive":
            base_config.n_trials = 50
            base_config.sampler = "tpe"
            base_config.pruner = "hyperband"
            base_config.n_parallel_jobs = 4
        elif preset_name == "quick":
            base_config.n_trials = 5
            base_config.sampler = "random"
            
        return base_config

class VOLog_HPOTrial(VOPydanticBase):
    """Represents a single hyperparameter optimization trial"""
    
    trial_id: int = Field(
        description="Unique identifier for this trial"
    )
    params: Dict[Hyperparams.BaseHPEnum, Any] = Field(
        description="Parameters used in this trial"
    )
    value: float = Field(
        description="Objective value (metric) achieved by this trial"
    )
    duration_seconds: float = Field(
        default=0.0,
        description="Duration of this trial in seconds"
    )
    is_best: bool = Field(
        default=False,
        description="Whether this is the best trial in the optimization"
    )
    best_value: float
    state: Literal["COMPLETE", "PRUNED", "FAIL"]= Field(
        default = "COMPLETE",
        description= "Trail state"
    )

class VOResult_HPO(VOPydanticBase):
    """Results of a hyperparameter optimization run"""
    
    best_params: Dict[Hyperparams.BaseHPEnum, Any] = Field(
        description="Best parameters found during optimization"
    )
    best_value: float = Field(
        description="Best objective value achieved"
    )
    best_trial_id: int = Field(
        description="ID of the best trial"
    )
    trials: List[VOLog_HPOTrial] = Field(
        description="All trial results"
    )
    runtime_seconds: float = Field(
        description="Total runtime of optimization"
    )
    direction: str = Field(
        description="Direction of optimization (minimize/maximize)"
    )
    parameter_importance: Optional[Dict[Hyperparams.BaseHPEnum, float]] = Field(
        default=None,
        description="Importance score for each parameter (0-100)"
    )

# PERSISTENCE DATA

class VOMetadata_General(VOPydanticBase):
    """Metadata for a surrogate lifecycle"""
    uid: uuid.UUID = Field(default_factory=uuid.uuid4)
    label: str
    user_reference: str
    atlas_reference: str
    surrogate_algo: EnumSurrogateAlgorithm
    description: str
    created_at: datetime = Field(default_factory=lambda: datetime.now(tz=timezone.utc))
    updated_at: Optional[datetime] = None
    tags: Dict[str, str] = Field(default_factory=dict) # default tags: "version", "model_status"
    
    # TODO implement autotagging for version and status.
    
    @field_serializer('created_at', 'updated_at')
    def serialize_datetime(self, dt: datetime) -> str:
        """Serialize datetime to ISO 8601 format with timezone"""
        if dt is None:
            return None
        return dt.isoformat()
    
    @property
    def experiment_name(self):
        return f"surrogate-{self.user_reference}-{self.atlas_reference}-{self.label}"
        
    
    
class VOModelStateMetadata(VOPydanticBase):
    """Model version in the registry"""
    uid: uuid.UUID = Field(default_factory=uuid.uuid4)
    model_name: str  # Logical model name (maps to MLflow registered model)
    version: int
    metadata_uid: uuid.UUID  # Reference to VOMetadata
    created_at: datetime = Field(default_factory=lambda: datetime.now(tz=timezone.utc))
    stage: Literal["Development", "Staging", "Production", "Archived"] = Field(default="Development")
    description: Optional[str] = None