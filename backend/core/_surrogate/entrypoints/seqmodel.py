"""
ENTRY POINT SCRIPT FOR TRAINING SEQUENCE MODELS.

cmd = self._build_training_command(
    entrypoint_script,
    {
        "job_configs_path": job_configs_file,
        "training_data_path": training_data_file,
        "test_data_path": test_data_file,
    }
)

Takes in the following arguments:
    --input-dir: directory with training data and config
    --output-dir: directory for outputs
"""
import json
import os
import logging
import traceback
from pathlib import Path
import torch
from backend.core._surrogate.trainers import Seq2SeqTSTrainer
from backend.core._surrogate import *
from backend.core._surrogate.entities import ENTTrainingJob
from backend.core._surrogate.valueobjects import VODataset

class RunnerConstants:
    ERROR_LOG_FILE = "error.log"
    MODEL_FILE = "raw_model.pt"
    JOB_FILE = "training_job.json"

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console output
    ]
)

# Deserialize job configs
def deserialize_job(job_configs_path: str) -> ENTTrainingJob:
    with open(job_configs_path, 'r') as f:
        job_json = f.read()
    return ENTTrainingJob.model_validate_json(job_json)

# Deserialize training data
def deserialize_vodata(training_data_path: str) -> VODataset:
    with open(training_data_path, 'r') as f:
        training_data_json = f.read()
    return VODataset.model_validate_json(training_data_json)


# Run training
def main(job_configs_path: str, training_data_path: str, test_data_path: str, output_dir: str):
    """
    Main training function that loads data, trains model, and saves outputs.

    Args:
        job_configs_path: Path to job configuration JSON file
        training_data_path: Path to training data JSON file
        test_data_path: Path to test data JSON file (optional)
        output_dir: Directory to save model and job artifacts

    Returns:
        bool: True if training completed successfully, False otherwise
    """
    is_success = False

    # Setup logging to file in output directory
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    log_file = output_path / "training.log"
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
    logging.getLogger().addHandler(file_handler)

    logging.info("=" * 60)
    logging.info("STARTING SEQUENCE MODEL TRAINING")
    logging.info("=" * 60)
    logging.info(f"Job configs path: {job_configs_path}")
    logging.info(f"Training data path: {training_data_path}")
    logging.info(f"Test data path: {test_data_path}")
    logging.info(f"Output directory: {output_dir}")
    logging.info(f"Current working directory: {os.getcwd()}")

    try:
        #==============================
        # DESERIALIZE
        logging.info("Loading job configuration...")
        job = deserialize_job(job_configs_path)
        logging.info(f"Job loaded: {job.metadata.label} (UID: {job.uid})")

        logging.info("Loading training data...")
        training_data = deserialize_vodata(training_data_path)
        logging.info(f"Training data loaded: {training_data.arr_x.shape} features, {training_data.arr_y.shape} targets")

        test_data = None
        if test_data_path:
            logging.info("Loading test data...")
            test_data = deserialize_vodata(test_data_path)
            logging.info(f"Test data loaded: {test_data.arr_x.shape} features, {test_data.arr_y.shape} targets")
        else:
            logging.info("No test data provided")

        #==============================
        # PREPARE INPUTS
        logging.info("Preparing training inputs...")

        # Prepare configs
        metadata = job.metadata
        training_config = job.training_configuration
        model_config = job.model_configuration
        hpo_config = job.hpo_configuration

        logging.info(f"Algorithm: {model_config.algorithm}")
        logging.info(f"Max iterations: {training_config.max_iterations}")
        logging.info(f"HPO enabled: {hpo_config.is_enable if hpo_config else False}")

        # Prepare test data
        x_test = test_data.arr_x if test_data is not None else None
        y_test = test_data.arr_y if test_data is not None else None

        #==============================
        # RUN TRAINING
        logging.info("Starting model training...")

        trainer = Seq2SeqTSTrainer()
        native_model, update_job = trainer.train(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config,
            job = job,
            x_test=x_test,
            y_test=y_test
        )

        logging.info("Training completed successfully!")
        logging.info(f"Updated job status: {update_job.status}")
        logging.info(f"Training runtime: {update_job.runtime_seconds} seconds")

        #==============================
        # SAVE OUTPUTS TO SPECIFIED DIRECTORY
        logging.info("Saving model and job artifacts...")

        # Define output file paths
        model_path = output_path / RunnerConstants.MODEL_FILE
        job_path = output_path / RunnerConstants.JOB_FILE

        # Save model
        logging.info(f"Saving model to: {model_path}")
        if native_model is None:
            raise ValueError("Training produced None model - cannot save")

        torch.save(native_model, str(model_path))

        # Verify model file was created
        if not model_path.exists():
            raise RuntimeError(f"Failed to create model file at {model_path}")
        model_size = model_path.stat().st_size
        logging.info(f"Model saved successfully ({model_size} bytes)")

        # Save updated job entity
        logging.info(f"Saving job entity to: {job_path}")
        with open(job_path, 'w') as f:
            f.write(update_job.model_dump_json(indent=2))

        # Verify job file was created
        if not job_path.exists():
            raise RuntimeError(f"Failed to create job file at {job_path}")
        job_size = job_path.stat().st_size
        logging.info(f"Job entity saved successfully ({job_size} bytes)")

        # Also save legacy filenames for backward compatibility
        legacy_model_path = output_path / "raw_model.pt"
        legacy_job_path = output_path / "training_job.json"

        torch.save(native_model, str(legacy_model_path))
        with open(legacy_job_path, 'w') as f:
            f.write(update_job.model_dump_json(indent=2))
        logging.info("Legacy filenames also saved for backward compatibility")

        # List all files created
        output_files = list(output_path.iterdir())
        logging.info(f"Output directory contents: {[f.name for f in output_files]}")

        # SIGNAL SUCCESS
        logging.info("Training completed successfully!")
        is_success = True
        return True

    except Exception as e:
        # Log the error for debugging
        logging.error("=" * 60)
        logging.error("TRAINING FAILED WITH EXCEPTION")
        logging.error("=" * 60)
        logging.error(f"Exception: {str(e)}")
        logging.error(f"Exception type: {type(e).__name__}")
        logging.error("Full traceback:")
        logging.error(traceback.format_exc())

        # Try to save error information to output directory
        try:
            error_file = output_path / "error.log"
            with open(error_file, 'w') as f:
                f.write(f"Training failed with exception: {str(e)}\n")
                f.write(f"Exception type: {type(e).__name__}\n")
                f.write(f"Full traceback:\n{traceback.format_exc()}")
            logging.error(f"Error details saved to: {error_file}")
        except Exception as save_error:
            logging.error(f"Failed to save error log: {save_error}")

        # Ensure we return failure
        is_success = False

    finally:
        logging.info("=" * 60)
        logging.info(f"TRAINING FINISHED - SUCCESS: {is_success}")
        logging.info("=" * 60)

    return is_success

if __name__ == "__main__":
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description="Train a surrogate model")
    parser.add_argument("--job_configs_path", type=str, required=True, help="Path to job configuration file")
    parser.add_argument("--training_data_path", type=str, required=True, help="Path to training data file")
    parser.add_argument("--test_data_path", type=str, required=False, help="Path to test data file")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory for model and job artifacts")
    
    args = parser.parse_args()
    
    is_success = main(args.job_configs_path, args.training_data_path, args.test_data_path, args.output_dir)
    
    if is_success:
        print("Training completed successfully!")
    else:
        print("Training failed!")
    
    sys.exit(0 if is_success else 1)


