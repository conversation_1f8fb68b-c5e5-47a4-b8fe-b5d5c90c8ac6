"""
ENTRY POINT SCRIPT FOR TRAINING SEQUENCE MODELS. 

cmd = self._build_training_command(
    entrypoint_script,
    {
        "job_configs_path": job_configs_file,
        "training_data_path": training_data_file,
        "test_data_path": test_data_file,
    }
)

Takes in the following arguments:
    --input-dir: directory with training data and config
    --output-dir: directory for outputs
"""
import json
import torch
from backend.core._surrogate.trainers import Seq2SeqTSTrainer
from backend.core._surrogate import *
from backend.core._surrogate.entities import ENTTrainingJob
from backend.core._surrogate.valueobjects import VODataset

# Deserialize job configs
def deserialize_job(job_configs_path: str) -> ENTTrainingJob:
    with open(job_configs_path, 'r') as f:
        job_json = f.read()
    return ENTTrainingJob.model_validate_json(job_json)

# Deserialize training data
def deserialize_vodata(training_data_path: str) -> VODataset:
    with open(training_data_path, 'r') as f:
        training_data_json = f.read()
    return VODataset.model_validate_json(training_data_json)


# Run training
def main(job_configs_path: str, training_data_path: str, test_data_path: str, output_dir: str):
    is_success = False
    

    try:
        #============================== 
        # DESERIALIZE
        job = deserialize_job(job_configs_path)
        training_data = deserialize_vodata(training_data_path)
        test_data = deserialize_vodata(test_data_path) if test_data_path else None

        #============================== 
        # PREPARE INPUTS

        # Prepare configs
        metadata = job.metadata
        training_config = job.training_configuration
        model_config = job.model_configuration
        hpo_config = job.hpo_configuration

        # Prepare test data
        x_test = test_data.arr_x if test_data is not None else None
        y_test = test_data.arr_y if test_data is not None else None

        #============================== 
        # RUN TRAINING

        trainer = Seq2SeqTSTrainer()
        native_model, update_job = trainer.train(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config,
            job = job,
            x_test=x_test,
            y_test=y_test
        )

        #============================== 
        # PICKLE AND SAVE IN COMPUTE.
        
        # NOTE the naming convention is hardcoded to align with submit_training_job in AzureTrainingRunner. This needs refactoring so its safer.
        torch.save(native_model, "raw_model.pt")
        with open( "training_job.json", 'w') as f:
            f.write(update_job.model_dump_json(indent=2))
        
        # SIGNAL DONE
        return True

    except Exception as e:
        # Log the error for debugging
        logging.error(f"ERROR: Training failed with exception: {str(e)}")
        logging.error(f"Exception type: {type(e).__name__}")

        # In production, you might want to log to a file or monitoring system
        import traceback
        logging.error("Full traceback:")
        traceback.print_exc()

        # Ensure we return failure
        is_success = False
    
    return is_success

if __name__ == "__main__":
    import argparse
    import sys
    
    parser = argparse.ArgumentParser(description="Train a surrogate model")
    parser.add_argument("--job_configs_path", type=str, required=True, help="Path to job configuration file")
    parser.add_argument("--training_data_path", type=str, required=True, help="Path to training data file")
    parser.add_argument("--test_data_path", type=str, required=False, help="Path to test data file")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory for model and job artifacts")
    
    args = parser.parse_args()
    
    is_success = main(args.job_configs_path, args.training_data_path, args.test_data_path, args.output_dir)
    
    if is_success:
        print("Training completed successfully!")
    else:
        print("Training failed!")
    
    sys.exit(0 if is_success else 1)


