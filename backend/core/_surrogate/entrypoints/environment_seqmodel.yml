name: azureml-env
channels:
  - conda-forge
  - pytorch
  - defaults
dependencies:
  # Core Python packages
  - python>=3.8,<3.12
  
  # PyTorch (compatible with CUDA 12.1 base image)
  - pytorch>=2.0.0,<2.3.0
  
  # Data Science Core
  - numpy
  - pandas
  - scipy>=1.10.1,<2.0.0
  - scikit-learn>=1.3.2
  - matplotlib
  - seaborn>=0.13.0,<0.14.0
  
  # Optimization and ML Tools
  - networkx
  
  # Pip dependencies (packages not available or better from PyPI)
  - pip
  - pip:
    - optuna>=4.3.0
    - scikit-optimize>=0.9.0
    - pydacefit>=1.0.1,<1.1.0