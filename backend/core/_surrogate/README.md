# Surrogate Model Module
(Headers done by LO. Contents filled in by AI then amended by LO)

## Overview
The `_surrogate` module provides a framework for creating, training, and using surrogate models to represent complex systems. The implementation follows a layered architecture with clear separation of concerns, making it extensible and maintainable.

The main concept is to take a declarative approach to ML by using pydantic ValueObjects.
The implementation takes a stateless approach as much as possible to enable future distributed training.

## Architecture
The surrogate model implementation is structured across the following layers:

### Enums Layer (`_enums.py`)
Defines key enumeration types used throughout the module:
- `EnumSurrogateAlgorithm`: Available surrogate model types (Random Forest, RNN, etc.)
- `EnumTrainingStatus`: Status of model training process
- `EnumMetricName`: Supported metrics for model evaluation
- `Hyperparams`: Algorithm-specific parameter names for various model types and HPO. 

### Value Objects Layer (`valueobjects.py`)
Immutable data structures representing core concepts:
- Model parameters
- Evaluation metrics
- Training results

### Entities Layer (`entities.py`)
Contains domain entities with business logic such as training metadata.

### Surrogate Models Layer (`models/model_base.py`)
- `BaseSurrogateModel`: Abstract base class for all surrogate models
- `PyTorchSurrogateModel`: Implementation for PyTorch-based models
- `models/model_sklearn.py`: Implementation for scikit-learn models (outdated!)

### Trainers Layer (`trainers/`)
- `trainer_base.py`: Abstract base class for model trainers
- `trainer_rf.py`: Random Forest trainer implementation (outdated !)
- `trainer_rnn.py`: RNN/LSTM trainer implementation
- `_torchblocks.py`: PyTorch building blocks for neural network models
- uses Optuna OOTB for HPO.

### 8. Data Transformers (`transformers/`)
Handles data preprocessing, normalization, and feature engineering.

## Key Features

- **Model Agnostic Design**: Common interface for different model types (scikit-learn, PyTorch, etc.)
- **Serialization**: Models can be serialized/deserialized for storage and retrieval
- **Time Series Support**: Specialized implementations for time series forecasting
- **Feature Importance**: Support for extracting and utilizing feature importance
- **Metrics Collection**: Standardized evaluation metrics across different model types

## Usage

The surrogate module is designed to be integrated within the larger application. Typical usage flow:

1. Select appropriate surrogate model type for the problem
2. Prepare data with appropriate transformers
3. Train model with appropriate hyperparameters
4. Evaluate model performance
5. Save model for later use
6. Load and use model for predictions

## Future Enhancements

- Models - Additional model types (e.g., LightGBM, LGBM)
    - SeqModel w RandomForest
    - SeqModel w Gradient Boosted Trees (LightB GBM)
    - SeqModel w transformer, potentially finetuned
- Online learning capabilities
- MLOps - Additional adaptor for MLFlow for experiment tracking and model registry. Currently uses a simple filesystem repo
- Compute - Additional infrastructure for distributed training / compute management. Currently done on the server VM. Valid for pfizer but more will be needed (LO will be implementing Ray for Qatar. Intent is to port that implementation here)