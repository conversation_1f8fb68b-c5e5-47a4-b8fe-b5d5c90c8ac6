from __future__ import annotations
from enum import Enum, unique
from typing import (
    TYPE_CHECKING,
    Any,
    Dict,
    Generic,
    List,
    Optional,
    Type,
    Tuple,
    TypeVar,
    Union,
    Protocol,
    runtime_checkable,
)

def enum_lookup_from_string(enum_cls: Type[BaseEnum], enum_str: str) -> Union[str, BaseEnum]:
    """
    Given a string, search through all enums in this namespace. If it can be found, return the enum. Else, return the string
    """
    for member in enum_cls:
        if member.value == enum_str:
            return member
    return enum_str

@unique
class BaseEnum(Enum):
    pass

class EnumSurrogateAlgorithm(BaseEnum):
    """Types of surrogate models available"""
    RANDOM_FOREST = "random_forest"
    RANDOM_FOREST_TS = "random_forest_ts"
    GRADIENT_BOOSTING = "gradient_boosting"
    GRADIENT_BOOSTING_TS = "gradient_boosting_ts"
    RNN_TS = "rnn_ts"

class EnumTrainingStatus(BaseEnum):
    """Status of surrogate model training process"""
    PENDING = "Pending"
    TRAINING = "Training"
    COMPLETE = "Complete"
    EARLY_STOPPED = "Early Stopped"
    FAILED = "Failed"

class EnumMetricName(str, Enum):
    """Supported metrics for model evaluation"""
    # Core regression metrics
    RMSE = "rmse"
    MAE = "mae" 
    R2 = "r2"
    MSE = "mse"
    MAX_ERROR = "max_error"
    MAPE = "mape"
    
    # Time series specific metrics
    TS_MASE = "ts_mase"
    TS_SMAPE = "ts_smape"
    
    # Signal quality metrics
    DIRECTIONAL_ACCURACY = "directional_accuracy"
    PREDICTION_STD = "prediction_std"
    COEFFICIENT_OF_VARIATION = "coefficient_of_variation"
    NORMALIZED_RMSE = "normalized_rmse"
    
    @classmethod
    def is_higher_better(cls, metric: 'EnumMetricName') -> bool:
        """Return True if higher values of this metric indicate better performance"""
        return metric in [cls.R2, cls.DIRECTIONAL_ACCURACY]

class Hyperparams:
    """
    Namespace for algorithm-specific parameter names (Refined V1),
    grouped by model type for developer clarity. Includes a richer set
    of common HPO parameters.
    """
    class BaseHPEnum(BaseEnum):
        """Base class for parameter enums."""
        pass

    # --- Random Forest Parameters (Refined V1) ---
    class RF(BaseHPEnum):
        """Parameter names for RandomForest algorithms (Refined V1)."""
        # Core structure/size (common tuning targets)
        N_ESTIMATORS = "n_estimators"  # Number of trees in the forest
        MAX_DEPTH = "max_depth"  # Maximum depth of the trees
        MIN_SAMPLES_SPLIT = "min_samples_split"  # Minimum samples required to split a node
        MIN_SAMPLES_LEAF = "min_samples_leaf"  # Minimum samples required at a leaf node
        MIN_WEIGHT_FRACTION_LEAF = "min_weight_fraction_leaf" # Min weighted fraction at a leaf node
        MAX_LEAF_NODES = "max_leaf_nodes"  # Grow trees with max_leaf_nodes
        MIN_IMPURITY_DECREASE = "min_impurity_decrease" # Threshold for split improvement

        # Feature sampling
        MAX_FEATURES = "max_features"  # Number/fraction of features to consider for best split

        # Bootstrapping & Evaluation (common config & HPO)
        BOOTSTRAP = "bootstrap"  # Whether bootstrap samples are used
        OOB_SCORE = "oob_score"  # Whether to use out-of-bag samples for scoring (config flag, sometimes tuned)

        # Other essential config/reproducibility
        CRITERION = "criterion"  # Function to measure split quality (e.g., 'mse', 'gini')
        RANDOM_STATE = "random_state"  # Seed for reproducibility (log essential)

    # --- Gradient Boosting Parameters (Refined V1) ---
    class GB(BaseHPEnum):
        """Parameter names for Gradient Boosting algorithms (Refined V1)."""
        # Core boosting process (most common tuning targets)
        N_ESTIMATORS = "n_estimators"  # Number of boosting stages / trees
        LEARNING_RATE = "learning_rate_gb"  # Shrinks the contribution of each tree
        SUBSAMPLE = "subsample"  # Fraction of samples used for fitting trees (row sampling)
        ALPHA = "alpha"  # Parameter for specific loss functions (e.g., quantile loss)

        # Tree structure (applies to individual trees within the ensemble, common tuning targets)
        MAX_DEPTH = "max_depth"  # Maximum depth of the individual trees
        MIN_SAMPLES_SPLIT = "min_samples_split"  # Minimum samples required to split a node
        MIN_SAMPLES_LEAF = "min_samples_leaf"  # Minimum samples required at a leaf node
        MIN_WEIGHT_FRACTION_LEAF = "min_weight_fraction_leaf" # Min weighted fraction at a leaf
        MAX_LEAF_NODES = "max_leaf_nodes"  # Max leaf nodes per tree
        MIN_IMPURITY_DECREASE = "min_impurity_decrease" # Threshold for split improvement
        MIN_CHILD_WEIGHT = "min_child_weight" # Min sum of instance weight (XGBoost/LightGBM)

        # Feature sampling (common HPO)
        MAX_FEATURES = "max_features"  # Number/fraction of features to consider for best split (per tree)
        COLSAMPLE_BYTREE = "colsample_bytree" # Fraction of features per tree (XGBoost/LightGBM)
        # Consider adding COLSAMPLE_BYLEVEL, COLSAMPLE_BYNODE in v2 if needed

        # Regularization (common HPO in modern GB)
        REG_ALPHA = "reg_alpha" # L1 regularization term
        REG_LAMBDA = "reg_lambda" # L2 regularization term

        # Training Control / Early Stopping (essential for GB training & HPO)
        VALIDATION_FRACTION = "validation_fraction"  # Fraction of training data for early stopping
        N_ITER_NO_CHANGE = "n_iter_no_change"  # Number of iterations with no improvement
        TOL = "tol"  # Tolerance for early stopping
        # Note: PATIENCE is similar to N_ITER_NO_CHANGE depending on library

        # Other essential config/reproducibility
        LOSS_FUNCTION = "loss_function" # The loss function being optimized. use "mse", "mae" 
        CRITERION = "criterion" # Split quality criterion (less common to tune in GB than in RF)
        RANDOM_STATE = "random_state"  # Seed for reproducibility (log essential)

    # --- Neural Network Parameters (Refined V1) ---
    class NN(BaseHPEnum):
        """Parameter names for Neural Network algorithms (Refined V1), including RNNs."""
        # Data / Batching (essential training config & HPO)
        BATCH_SIZE = "batch_size"  # Mini-batch size

        # Architecture / Structure (core tuning targets, including RNN specifics)
        INPUT_SIZE = "input_size" # Dimension of the input features (structural, log essential)
        OUTPUT_SIZE = "output_size" # Dimension of the output (structural, log essential)
        HIDDEN_SIZE = "hidden_size"  # Number of features in hidden layers (RNN, Feedforward, etc.)
        SEQ_NUM_LAYERS = "seq_num_layers"
        DENSE_NUM_LAYERS = "dense_num_layers"  # Number of layers
        RNN_TYPE = "rnn_type"  # Type of RNN cell (LSTM, GRU, SimpleRNN, if applicable and tunable)
        BIDIRECTIONAL = "bidirectional"  # Use bidirectional layers (if applicable and tunable)
        EMBEDDING_DIM = "embedding_dim"  # Dimension of embeddings (if applicable and tunable)
        ACTIVATION_FUNCTION = "activation_function" # Default activation for hidden layers

        # Regularization & Dropout (common HPO)
        DROPOUT = "dropout"  # Dropout probability (general)
        WEIGHT_DECAY = "weight_decay"  # L2 penalty for regularization (often an optimizer param, but key HPO)
        GRADIENT_CLIP_VAL = "gradient_clip_val" # Value for gradient norm clipping (important for RNNs)

        # Optimization (essential training config & HPO targets)
        LEARNING_RATE = "learning_rate"  # Base learning rate
        OPTIMIZER_TYPE = "optimizer_type"  # Optimization algorithm (e.g., 'Adam', 'SGD')
        LOSS_FUNCTION = "loss_function" # The loss function used

        # Common Optimizer Parameters (for Adam/AdamW primarily, common HPO)
        OPTIMIZER_BETA1 = "optimizer_beta1"
        OPTIMIZER_BETA2 = "optimizer_beta2"
        OPTIMIZER_EPS = "optimizer_eps"

        # Learning Rate Scheduling (Type is HPO, common params are HPO)
        LR_SCHEDULER_TYPE = "lr_scheduler_type" # Type of LR scheduler (e.g., 'StepLR', 'ReduceLROnPlateau')
        LR_SCHEDULER_STEP_SIZE = "lr_scheduler_step_size" # For StepLR, CyclicLR etc.
        LR_SCHEDULER_GAMMA = "lr_scheduler_gamma"     # For StepLR, ExponentialLR etc.
        LR_SCHEDULER_PATIENCE = "lr_scheduler_patience" # For ReduceLROnPlateau
        LR_SCHEDULER_MIN_LR = "lr_scheduler_min_lr" # Minimum LR for ReduceLROnPlateau

        # Training Process Control (essential training config & HPO)
        EPOCHS = "epochs"  # Maximum number of training epochs
        EARLY_STOPPING = "early_stopping"  # Whether to use early stopping (config flag, tunable strategy)
        PATIENCE = "patience"  # Number of epochs with no improvement for early stopping
        MIN_DELTA = "min_delta" # Minimum change to qualify as an improvement for early stopping

        # Other essential config/reproducibility
        # Consider adding BATCH_FIRST, PADDING_VALUE if sequence handling is a core tuning point

    @classmethod
    def get_enum_from_str(cls, enum_str: str) -> Union[str, Hyperparams.BaseHPEnum]:
        # Given a string, search through all enums in this namespace. If it can be found, return the enum. Else, return the string
        for enum_class in cls.BaseHPEnum.__subclasses__():
            for member in enum_class:
                if member.value == enum_str:
                    return member
        return enum_str
    
    @classmethod
    def convert_dict_to_enum(cls, _dict: Dict[Union[str, Hyperparams.BaseHPEnum], Any]) -> Dict[Hyperparams.BaseHPEnum, Any]:
        """
        Convert a dictionary with string or enum keys to a dictionary with only enum keys.
        
        Args:
            _dict: Dictionary with string or enum keys
            
        Returns:
            Dictionary with only enum keys
            
        Raises:
            ValueError: If any key cannot be converted to an enum (hybrid dictionary)
        """
        result = {}
        
        # Get all enum subclasses
        enum_classes = cls.BaseHPEnum.__subclasses__()
        
        for key, value in _dict.items():
            # If already an enum, just use it directly
            if isinstance(key, cls.BaseHPEnum):
                result[key] = value
                continue
                
            # Try to convert string to enum
            enum_key = None
            for enum_class in enum_classes:
                try:
                    enum_key = enum_class(key)
                    break
                except (ValueError, KeyError):
                    continue
            
            if enum_key is None:
                raise ValueError(f"Could not convert key '{key}' to an enum. Hybrid dictionaries not allowed.")
                
            result[enum_key] = value
                
        return result
