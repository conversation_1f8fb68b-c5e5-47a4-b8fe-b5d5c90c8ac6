<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0" version="26.0.16">
  <diagram name="Page-1" id="p7N5egsznA6cPENumIB1">
    <mxGraphModel dx="1434" dy="774" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="aDWpQtctgrqHPTnMcKUU-1" value="&lt;b&gt;Input Layer&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="30" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-2" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="430" as="sourcePoint" />
            <mxPoint x="455" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-3" value="&lt;b&gt;RNN Layer (GRU/LSTM/Vanilla RNN)&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="120" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="150" as="sourcePoint" />
            <mxPoint x="455" y="190" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-5" value="&lt;b&gt;Layer Norm&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="190" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-6" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-5" target="aDWpQtctgrqHPTnMcKUU-7">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="220" as="sourcePoint" />
            <mxPoint x="455" y="260" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-7" value="&lt;b&gt;DNN Layer 1&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="270" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-8" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="300" as="sourcePoint" />
            <mxPoint x="455" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="455" y="330" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-9" value="&lt;b&gt;Layer Norm&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="360" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-13" value="Filter Block" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="550" y="310" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-16" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="430" as="sourcePoint" />
            <mxPoint x="460" y="330" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-17" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="240" as="sourcePoint" />
            <mxPoint x="800" y="240" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-19" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="aDWpQtctgrqHPTnMcKUU-13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="330" as="sourcePoint" />
            <mxPoint x="660" y="329.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-20" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="390" as="sourcePoint" />
            <mxPoint x="454.5" y="440" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-21" value="&lt;b&gt;DNN Layer 2&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="500" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-22" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="570" as="sourcePoint" />
            <mxPoint x="455" y="630" as="targetPoint" />
            <Array as="points">
              <mxPoint x="455" y="600" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-54" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-23" target="aDWpQtctgrqHPTnMcKUU-21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-23" value="&lt;b&gt;Layer Norm&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="630" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-24" value="Filter Block" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="550" y="580" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-25" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-24">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="700" as="sourcePoint" />
            <mxPoint x="460" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-26" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="470" as="sourcePoint" />
            <mxPoint x="750" y="470" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-27" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="470" as="sourcePoint" />
            <mxPoint x="750" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-28" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="aDWpQtctgrqHPTnMcKUU-24">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="600" as="sourcePoint" />
            <mxPoint x="660" y="599.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-30" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="240" as="sourcePoint" />
            <mxPoint x="800" y="1150" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-31" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="560" as="sourcePoint" />
            <mxPoint x="770" y="560" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-32" value="&lt;h1&gt;&lt;b&gt;+&lt;/b&gt;&lt;/h1&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="740" y="550" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-35" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" target="aDWpQtctgrqHPTnMcKUU-36">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="455" y="740" as="sourcePoint" />
            <mxPoint x="455" y="780" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-36" value="&lt;b&gt;DNN Layer 3&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="790" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-37" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="820" as="sourcePoint" />
            <mxPoint x="455" y="880" as="targetPoint" />
            <Array as="points">
              <mxPoint x="455" y="850" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-38" value="&lt;b&gt;Layer Norm&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="880" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-39" value="Filter Block" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="550" y="830" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-40" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-39">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="950" as="sourcePoint" />
            <mxPoint x="460" y="850" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-41" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="460" y="760" as="sourcePoint" />
            <mxPoint x="750" y="760" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-42" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="760" as="sourcePoint" />
            <mxPoint x="750" y="850" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-43" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="aDWpQtctgrqHPTnMcKUU-39">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="849.5" as="sourcePoint" />
            <mxPoint x="660" y="849.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-44" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="810" as="sourcePoint" />
            <mxPoint x="770" y="810" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-45" value="&lt;h1&gt;&lt;b&gt;+&lt;/b&gt;&lt;/h1&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="740" y="800" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-48" value="Repeat until n layers" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="510" y="1000" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-49" value="&lt;b&gt;GeLU Activation&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="410" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-50" value="" style="endArrow=classic;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-49" target="aDWpQtctgrqHPTnMcKUU-21">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="455" y="470" as="sourcePoint" />
            <mxPoint x="454.5" y="520" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-53" value="&lt;b&gt;GeLU Activation&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="710" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-55" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="660" as="sourcePoint" />
            <mxPoint x="454.5" y="710" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-57" value="&lt;b&gt;GeLU Activation&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="960" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-58" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="910" as="sourcePoint" />
            <mxPoint x="454.5" y="960" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-61" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="1080" as="sourcePoint" />
            <mxPoint x="454.5" y="990" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-62" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="1080" as="sourcePoint" />
            <mxPoint x="454.5" y="1170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-63" value="&lt;b&gt;DNN Layer n&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="1170" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-64" value="" style="endArrow=none;dashed=1;html=1;dashPattern=1 3;strokeWidth=2;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="1074" as="sourcePoint" />
            <mxPoint x="460" y="1074" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-65" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="1080" as="sourcePoint" />
            <mxPoint x="750" y="1240" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-71" value="&lt;b&gt;Layer Norm&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="1270" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-93" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-72" target="aDWpQtctgrqHPTnMcKUU-71">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-72" value="&lt;b&gt;GeLU Activation&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="1330" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-73" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" target="aDWpQtctgrqHPTnMcKUU-72">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="1300" as="sourcePoint" />
            <mxPoint x="454.5" y="1350" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-74" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" target="aDWpQtctgrqHPTnMcKUU-71">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="1200" as="sourcePoint" />
            <mxPoint x="455" y="1250" as="targetPoint" />
            <Array as="points">
              <mxPoint x="455" y="1230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-75" value="Filter Block" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="600" y="1220" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-76" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-75">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="570" y="1320" as="sourcePoint" />
            <mxPoint x="460" y="1240" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-77" value="" style="endArrow=classic;html=1;rounded=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" target="aDWpQtctgrqHPTnMcKUU-75">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="750" y="1240" as="sourcePoint" />
            <mxPoint x="720" y="1219.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-86" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="800" y="1149.5" as="sourcePoint" />
            <mxPoint x="770" y="1149.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-87" value="&lt;h1&gt;&lt;b&gt;+&lt;/b&gt;&lt;/h1&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="740" y="1140" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-92" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="aDWpQtctgrqHPTnMcKUU-72">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="464.5" y="1310" as="sourcePoint" />
            <mxPoint x="455" y="1400" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-94" value="&lt;b&gt;Output DNN Layer&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="1400" width="430" height="30" as="geometry" />
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-95" value="" style="endArrow=classic;html=1;rounded=0;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="454.5" y="1430" as="sourcePoint" />
            <mxPoint x="455" y="1470" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="aDWpQtctgrqHPTnMcKUU-97" value="&lt;b&gt;Output Layer&lt;/b&gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82B366;" vertex="1" parent="1">
          <mxGeometry x="240" y="1470" width="430" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
