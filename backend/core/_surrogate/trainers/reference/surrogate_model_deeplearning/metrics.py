import numpy as np


class Surrogate_Model_Metric:
    """
    Class used to calculate metrics for surrogate model.

    Attributes
    ----------
        1) metrics: returns MAPE, MSE, RMSE, Cross-Correlation based on true and predicted values
    
    Methods
    -------
        1) __init__: Constructor of the class.
        2) calc_MAPE: Helper function to calculate the MAPE.
        3) calc_MSE: Helper function to calculate the MSE.
        4) calc_RMSE: Helper function to calculate the RMSE.
        5) calc_cross_corr: Helper function to calculate the Cross Correlation.
    
    """

    def __init__(self, y_true=None, y_pred=None):
        """
        Constructor of the class. 

        Parameters
        ----------
            y_true: true values
            y_pred: predicted values
        """
        if y_true is None and y_pred is None:
             self.metrics = {
            "MAPE": None,
            "SMAPE": None,
            "MSE": None,
            "RMSE": None,
            "Cross-Correlation": None
                       }
        else:
            self.metrics = {
                "MAPE": self.calc_MAPE(y_true, y_pred),
                "SMAPE": self.calc_SMAPE(y_true, y_pred),
                "MSE": self.calc_MSE(y_true, y_pred),
                "RMSE": self.calc_RMSE(y_true, y_pred),
                "Cross-Correlation": self.calc_cross_corr(y_true, y_pred)
                        }


    def calc_MAPE(self, y_true, y_pred, eps=1e-20):
        """
        Helper function to calculate the MAPE.

        Parameters
        ----------
            y_true: true values
            y_pred: predicted values
        """
        denom_V = np.vectorize(lambda x: max(x, eps))

        denom_y = denom_V(y_true)                       
        
        return np.mean(np.abs((y_true - y_pred) / (denom_y)), axis=(0, 1)) * 100

    def calc_MSE(self, y_true, y_pred):
        """
        Helper function to calculate the MSE.

        Parameters
        ----------
            y_true: true values
            y_pred: predicted values
        """
        return np.mean((y_true - y_pred) ** 2, axis=(0, 1))
    
    def calc_RMSE(self, y_true, y_pred):
        """
        Helper function to calculate the RMSE.

        Parameters
        ----------
            y_true: true values
            y_pred: predicted values
        """
        return np.sqrt(np.mean((y_true - y_pred) ** 2, axis=(0, 1)))

    def calc_cross_corr(self, y_true, y_pred):
        """
        Helper function to calculate the cross correlation.

        Parameters
        ----------
            y_true: true values
            y_pred: predicted values
        """

        batch_size, rows, cols = y_true.shape
        
        # Compute Pearson correlation for each batch and column
        cors = np.array([
            [
                np.corrcoef(y_true[b, :, i], y_pred[b, :, i])[0, 1] 
                if np.std(y_true[b, :, i]) > 0 and np.std(y_pred[b, :, i]) > 0 else 0.0
                for i in range(cols)
            ] 
            for b in range(batch_size)
        ])  

        avg_cors = np.mean(cors, axis=0)  

        return avg_cors  

    def calc_SMAPE(self, y_true, y_pred, eps=1e-20):

        return np.mean(np.abs((y_true - y_pred) / (((y_true+y_pred)/2)+eps)), axis=(0, 1)) * 100