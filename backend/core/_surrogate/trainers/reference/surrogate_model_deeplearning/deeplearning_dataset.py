import warnings
warnings.filterwarnings("ignore")

import os
import pathlib
import sys

import pandas as pd
import numpy as np

import torch
from torch.utils.data import Dataset

from sklearn.model_selection import train_test_split

cur_path  = pathlib.Path(__file__).resolve().parent

sys.path.append(str(cur_path.parent.parent))

sys.path.append(str(cur_path.parent))
from surrogateModel.dataset import dataset_build

from sklearn.preprocessing import StandardScaler, MinMaxScaler

RANDOM_SEED = 42

class base_dataset():
    """
        This class is created to transform the dataset in a vectorized format using the NumPy library to be able to be used for deep learning. The data
        is first extracted from the dataset class and processed to remove incomplete simulations and scaled using a Standard Scaler(can be implemented to 
        both X and y). The data is then transformed to arrays where X is represented as a two dimensional vector with the shape (no_cases, no_input_vars) and
        output with the shape (no_cases, no_timesteps, no_of_output_vars). They are then split into a training, test and validation set.

        Attributes:
        -----------
            1) main_dataset: raw processed dataset- excludes failed cases
            2) n_time_steps: number of time steps 
            3) X_base, y_base: X and y array which have not been standardized
            4) X, y: standardized versions of X_base and y_base
            5) scaler_X, scaler_y: standard scalers trained on X_base and y_base
            6) training_data, validation_data, testing_data
    """
    def __init__(self, surrogate_model_dataset : dataset_build.dataset, y_transform=True, test_val_split=0.2, y_scaling="Standard"):
        """
        Constructor of the Class.

        Parameters
        ----------
            surrogate_model_dataset: dataset_build.dataset
                dataset class of the surrogate model dataset
            y_transform: boolean
                condition to standardize y
            test_val_split: float
                portion of data to be used for testing and valdiation.
             y_scaling: str
                Scale y either by "Standard scaling"/"MinMax scaling"

        """
        # creating maindataset
        self.main_dataset = surrogate_model_dataset.get_training_data()
        n_time_steps = len(self.main_dataset[["Time"]].drop_duplicates())
        self.n_time_steps = n_time_steps
        time_step_count_group_by = self.main_dataset.groupby(["Case_No"]).count().reset_index()
        time_step_count_group_by = time_step_count_group_by[time_step_count_group_by["Time"]==n_time_steps]
        cases = set(time_step_count_group_by["Case_No"])
        self.main_dataset = self.main_dataset[self.main_dataset["Case_No"].isin(cases)]

        # spltting into X and y
        X_base = self.main_dataset[self.main_dataset["Case_No"].isin(cases)][surrogate_model_dataset.model.input_name_list]
        y_base = self.main_dataset[self.main_dataset["Case_No"].isin(cases)][surrogate_model_dataset.model.output_name_list]
        self.X_base, self.y_base = X_base.copy(), y_base.copy() 
        
        if y_transform == True:
            if y_scaling=="Standard":
                self.scaler_X, self.scaler_y = StandardScaler().fit(X_base), StandardScaler().fit(y_base)
            elif y_scaling=="MinMax":
                self.scaler_X, self.scaler_y = StandardScaler().fit(X_base), MinMaxScaler(feature_range=(1,2)).fit(y_base)
            X, y = self.scaler_X.transform(X_base), self.scaler_y.transform(y_base)
        else:
            self.scaler_X = StandardScaler().fit(X_base)
            X, y = self.scaler_X.transform(X_base), y_base
        self.X, self.y = X.copy(), y.copy()
        
        # transform datasets
        X = np.mean(np.reshape(np.array(X), (len(cases), n_time_steps, len(X_base.columns))), axis=1) 
        y = np.reshape(np.array(y), (len(cases), n_time_steps, len(y_base.columns)))
        
        # split_dataset
        train_X, split_X, train_y, split_y = train_test_split(X,y, test_size=test_val_split)
        cv_X, test_X, cv_y, test_y = train_test_split(split_X, split_y, test_size=0.5)
        self.training_data = train_X, train_y
        self.cross_val_data = cv_X, cv_y
        self.test_data = test_X, test_y

class pytorch_dataset(Dataset):
    """
    Subclass of the abstract Dataset class of the pyTorch library. Ensures a standardized way to load and retrieve data from a dataset based
    on key and index.

    Attributes
    ----------
        1) data_map: dictionary that maps index to a corresponding X and y
    Methods
    -------
        1) __init__: Constructor of the class.
        2) create_map: Helper function to create data_map attribute. 
        3) __len__: Returns size of dataset.
        4) __getitem__: Method to return data from dataset based on index.
    """
    def __init__(self, b_dataset:base_dataset, kind):
        """
        Constructor of class.
        
        Parameters
        ----------
            b_dataset: base_dataset
            kind: can be "training"/"validation"/"testing"
        """
        if kind == "training":
            dataset_array = b_dataset.training_data
        elif kind == "validation":
            dataset_array = b_dataset.cross_val_data
        elif kind == "testing":
            dataset_array = b_dataset.test_data
        self.data_map = self.create_map(dataset_array)

    def create_map(self, data_array):
        """
        Helper function to create data_map attribute.
        
        Parameters
        ----------
            data_array: (X, y) data
        """
        X, y = data_array[0], data_array[1]
        data_map = {}
        for i in range(X.shape[0]):
            data_map[i] = (X[i, :], y[i, :])
        return data_map


    def __len__(self):
        """
        Returns size of dataset.
        """
        return len(self.data_map.keys())

    def __getitem__(self, index):
        """
        Method to return data from dataset based on index.
        Parameters
        ----------
            index: int
        """
        return {"inputs" : torch.Tensor(self.data_map[index][0]),  "outputs": torch.Tensor(self.data_map[index][1])}


