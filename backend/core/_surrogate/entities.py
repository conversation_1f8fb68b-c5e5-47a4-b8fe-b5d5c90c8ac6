from __future__ import annotations
from ._imports import *
from ._enums import *
from ._utilities import *
from .valueobjects import (

    # METADATA
    VOModelStateMetadata,
    VOMetadata_General,

    # TRAINIING
    VOConfig_Training,
    VOConfig_Model,
    VOConfig_HPO,
    VOLog_ModelMetrics,
    VOLog_EpochMetrics,
    VOResult_ModelTest,

    # HPO
    VOResult_HPO

)


class ENTBase(BaseModel):
    """Base class for all entities"""
    uid: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the entity")

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
    )

class ENTTrainingJob(ENTBase):
    """Record of a single model training job with comprehensive tracking"""
    
    # SETUP
    metadata: VOMetadata_General
    training_configuration: VOConfig_Training
    model_configuration: VOConfig_Model
    hpo_configuration: Optional[VOConfig_HPO] = None
    training_data_uid: Optional[uuid.UUID] = None
    
    # HPO
    hpo_results: Optional[VOResult_HPO] = Field(
        default=None, 
        description="Results from hyperparameter optimization if performed"
    )
    hpo_best_metrics: Optional[VOLog_ModelMetrics] = Field(
        default=None,
        description="Metrics for the best model from HPO"
    )
    # PROCESS
    epoch_metrics: List[VOLog_EpochMetrics] = Field(
        default_factory=list,
        description="History of metrics at each epoch during training"
    )
    
    # FINAL
    results: Optional[VOResult_ModelTest] = Field(
        default=None,
        description="Full evaluation results including predictions and residuals"
    )

    # ADMIN LOGS
    status: str = Field(default=EnumTrainingStatus.PENDING.value)  # Default value for new jobs
    early_stopping_triggered: bool = Field(
        default=False,
        description="Whether early stopping was triggered"
    )
    runtime_seconds: int = 0
    error_message: Optional[str] = None
    
    # ENVIRONMENT
    environment_info: Dict[str, str] = Field(default_factory=dict)
    
    @property
    def is_successful(self) -> bool:
        """Whether the training job completed successfully"""
        return self.status == EnumTrainingStatus.COMPLETE.value and self.error_message is None
    
    @property
    def has_hpo_results(self) -> bool:
        """Check if this job has HPO results"""
        return self.hpo_results is not None and bool(self.hpo_results.trials)
    
        
    def log_epoch_metric(
            self,
            metric: VOLog_EpochMetrics
    ):
        # Defensive
        if not isinstance(metric, VOLog_EpochMetrics):
            raise TypeError("wrong type")
        
        self.epoch_metrics.append(metric)
        

    def get_metric_history(self, 
                          metric_name: Union[str, EnumMetricName], 
                          dataset: str = "train") -> List[float]:
        """
        Get history of a specific metric over training epochs.
        
        Args:
            metric_name: Name of the metric (string or enum)
            dataset: Which dataset to get metrics for ("train" or "validation")
            
        Returns:
            List of metric values ordered by epoch
            
        Raises:
            ValueError: If the metric or dataset is not available
        """
        # TODO implement and fix
        # Handle string or enum input for metric name
        if isinstance(metric_name, EnumMetricName):
            metric_str = metric_name.value
        else:
            metric_str = metric_name
        
        # Check dataset type
        if dataset not in ["train", "validation"]:
            raise ValueError(f"Dataset must be 'train' or 'validation', got '{dataset}'")
        
        # Extract metrics
        result = []
        for epoch_data in self.epoch_metrics:
            metrics_obj = (epoch_data.train_metrics if dataset == "train" 
                           else epoch_data.validation_metrics)
            
            # Skip if no metrics for this dataset
            if metrics_obj is None:
                continue
                
            # Get the metric value
            try:
                metric_value = getattr(metrics_obj, metric_str)
                if metric_value is not None:
                    result.append(float(metric_value))
            except AttributeError:
                # Skip metrics that don't have this attribute
                pass
        
        if not result:
            raise ValueError(f"No {metric_str} values found for {dataset} dataset")
        
        return result
    


    def get_learning_curves(self, 
                           metric_name: Union[str, EnumMetricName]) -> Dict[str, List[float]]:
        """
        Get learning curves data for plotting training and validation metrics over epochs.
        
        Args:
            metric_name: Name of metric to extract
            
        Returns:
            Dictionary with 'train' and 'validation' metrics over epochs
            
        Raises:
            ValueError: If the metric is not available
        TODO this doesn't work. to fix
        """
        curves = {}
        
        # Try to get training metrics
        try:
            curves['train'] = self.get_metric_history(metric_name, "train")
        except ValueError:
            curves['train'] = []
            
        # Try to get validation metrics
        try:
            curves['validation'] = self.get_metric_history(metric_name, "validation")
        except ValueError:
            curves['validation'] = []
            
        # Ensure we have at least some data
        if not curves['train'] and not curves['validation']:
            raise ValueError(f"No learning curve data available for {metric_name}")
            
        return curves
        

    def update_status(self, status: Union[str, EnumTrainingStatus], message: Optional[str] = None):
        # TODO update so status is a list of tuples with timestamps
        """
        Update the status of the training job.
        
        Args:
            status: New status string or enum
            message: Optional message for status explanation
        """
        # Convert enum to string if needed
        if isinstance(status, EnumTrainingStatus):
            status_str = status.value
        else:
            status_str = status
            
        # Update status with message if provided
        if message:
            self.status = f"{status_str}: {message}"
        else:
            self.status = status_str
            
        # Set early stopping flag if relevant
        if "early stopping" in status_str.lower() or (message and "early stopping" in message.lower()):
            self.early_stopping_triggered = True

##########

class ENTRegistryMetadata(ENTBase):
    """
    Entity responsible for tracking model versions, their deployment status,
    and relationships to training jobs
    """
    name: str = Field(description="Registry name/identifier")
    description: Optional[str] = None
    models: List[VOModelStateMetadata] = Field(default_factory=list)
    
    # Track model deployment and usage
    active_model_version_uid: Optional[uuid.UUID] = Field(
        default=None, 
        description="Currently active model version for serving"
    )
    
    # Relationship tracking
    training_job_mapping: Dict[uuid.UUID, uuid.UUID] = Field(
        default_factory=dict,
        description="Maps model version UIDs to their originating training job UIDs"
    )
    
    # Version tracking
    last_version: int = Field(default=0, description="Latest version number")
    
    def add_model_version(
        self, 
        metadata: VOMetadata_General,
        training_job_uid: Optional[uuid.UUID] = None,
        description: Optional[str] = None,
        stage: Literal["Development", "Staging", "Production", "Archived"] = "Development"
    ) -> VOModelStateMetadata:
        """
        Add a new model version to the registry
        
        Args:
            metadata: Model metadata
            training_job_uid: Optional UID of training job that produced this model
            description: Optional description of this specific version
            stage: Initial deployment stage for the model
            
        Returns:
            The newly created model version info
        """
        self.last_version += 1
        
        model_version = VOModelStateMetadata(
            model_name=metadata.label,
            version=self.last_version,
            metadata_uid=metadata.uid,
            stage=stage,
            description=description
        )
        
        self.models.append(model_version)
        
        # Store relationship to training job if provided
        if training_job_uid:
            self.training_job_mapping[model_version.uid] = training_job_uid
            
        return model_version
    
    def get_model_version(self, version_uid: uuid.UUID) -> Optional[VOModelStateMetadata]:
        """Get model version by its UID"""
        for model in self.models:
            if model.uid == version_uid:
                return model
        return None
    
    def get_latest_version(self, model_name: str) -> Optional[VOModelStateMetadata]:
        """Get the latest version of a model by name"""
        versions = [m for m in self.models if m.model_name == model_name]
        if not versions:
            return None
        return max(versions, key=lambda x: x.version)
    
    def get_active_version(self) -> Optional[VOModelStateMetadata]:
        """Get the currently active model version"""
        if not self.active_model_version_uid:
            return None
            
        return self.get_model_version(self.active_model_version_uid)
    
    def set_model_stage(
        self, 
        version_uid: uuid.UUID, 
        stage: Literal["Development", "Staging", "Production", "Archived"]
    ) -> bool:
        """
        Update the stage of a model version
        
        Args:
            version_uid: UID of model version to update
            stage: New stage
        
        Returns:
            True if successful, False if model not found
        """
        model = self.get_model_version(version_uid)
        if not model:
            return False
            
        # Handle promoting to production - only one model should be in production
        if stage == "Production":
            # Move any existing production models to staging
            for m in self.models:
                if m.stage == "Production":
                    m.stage = "Staging"
                    
            # Update active model reference
            self.active_model_version_uid = version_uid
        
        # If demoting the active model, clear the active reference
        if model.stage == "Production" and stage != "Production":
            if self.active_model_version_uid == version_uid:
                self.active_model_version_uid = None
                
        # Update the model's stage
        model.stage = stage
        return True