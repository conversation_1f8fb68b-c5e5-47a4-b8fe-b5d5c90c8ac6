# Deep Learning Surrogate Model Architecture: Sequence-to-Sequence Modeling for Process Optimization

## 1. Executive Summary

This report analyzes the neural architecture implemented in the `surrogate_model_deeplearning` framework. The model employs a recurrent neural network (RNN) architecture combined with feed-forward layers to create a sequence-to-sequence surrogate model for process simulation. The model is specifically designed to predict time-evolving outputs from time-series inputs, enabling rapid evaluation of complex process dynamics without computationally expensive physics-based simulations.

## 2. Core Architecture Analysis

### 2.1 Architectural Overview

The surrogate model implements a hybrid recurrent-dense architecture that learns mappings between multivariate time series. The input consists of process conditions over a sequence of time steps, and the output represents the corresponding process performance metrics over the same time horizon.

```
Architecture Diagram:

Input [batch_size, n_time_steps, n_features]
    │
    ▼
┌────────────────┐
│ RNN/GRU/LSTM   │  # Configurable RNN layer (1+ layers)
└────────┬───────┘
         │
         ▼
┌────────────────┐
│ LayerNorm      │  # Normalizes RNN outputs
└────────┬───────┘
         │
         ▼
         ┌─────────────────────────────────────────┐
         │            Skip Connections             │
         │  ┌─────────┐     ┌─────────┐           │
         ├─►│  Dense  │────►│LayerNorm│─┐         │
         │  └─────────┘     └─────────┘ │         │
         │                               │         │
         │                               │         │
         │                     ┌─────────▼─────┐   │
         │                     │  GELU + Dropout│   │ × DNN_length
         │                     └─────────┬─────┘   │   (Multiple DNN blocks)
         │                               │         │
         └───────────────────┬───────────┘         │
                             │                     │
                             ▼                     │
                         Next block  ◄─────────────┘
                             │
                             ▼
┌────────────────┐
│ Linear Layer   │  # Final projection to output dimensions
└────────┬───────┘
         │
         ▼
Output [batch_size, n_time_steps, n_outputs]
```

### 2.2 Network Components

The model is composed of these key components:

#### 2.2.1 Recurrent Layer

```python
if rnn_type == "RNN":
    self.RNN_layer = nn.RNN(input_size=n_inputs, hidden_size=n_hidden_units, 
                         batch_first=True, num_layers=rnn_layers)
elif rnn_type=="GRU":
    self.RNN_layer = nn.GRU(input_size=n_inputs, hidden_size=n_hidden_units, 
                         batch_first=True, num_layers=rnn_layers)
elif rnn_type=="LSTM":
    self.RNN_layer = nn.GRU(input_size=n_inputs, hidden_size=n_hidden_units, 
                         batch_first=True, num_layers=rnn_layers)
```

This layer encodes temporal dependencies in the sequence data. The architecture supports three RNN variants:
- Standard RNN: Simple but prone to vanishing gradients
- GRU: Better gradient flow with fewer parameters than LSTM
- LSTM: Robust for longer sequences but more computationally intensive

Default implementation uses GRU cells, which offer a good balance of memory capture and computational efficiency.

#### 2.2.2 DNN Blocks with Skip Connections

```python
self.DNN_layer_s = nn.ModuleList([nn.Linear(in_features=n_hidden_units, 
                               out_features=n_hidden_units) for _ in range(DNN_length)])
self.norm_layers = nn.ModuleList([nn.LayerNorm(n_hidden_units) for _ in range(DNN_length)])
self.alpha = nn.ParameterList([nn.Parameter(torch.ones(n_hidden_units)) for _ in range(DNN_length)])
```

This block contains:
1. Linear transformations to process RNN outputs
2. Layer normalization for training stability
3. GELU activation functions for non-linearity
4. Weighted skip connections for gradient flow
5. Dropout for regularization

The skip connections are implemented with learnable weights (`alpha`), allowing the model to adaptively determine the contribution of each layer.

#### 2.2.3 Output Projection

```python
self.output = nn.Linear(in_features=n_hidden_units, out_features=n_outputs)
```

This final linear layer projects the processed features to the output dimensions, representing the predicted process variables across the time horizon.

### 2.3 Forward Pass Analysis

The forward pass implementation follows this sequence:

```python
def forward(self, x):
    """
    Forward pass through the network.
    
    Args:
        x: Input tensor of shape (batch_size, time_steps, features)
    
    Returns:
        Output tensor of shape (batch_size, time_steps, outputs)
    """
    # RNN processing of sequence data
    output_RNN, _ = self.RNN_layer(x)
    # Normalize RNN output
    output_RNN = self.norm_RNN(output_RNN)
    
    # Process through DNN layers with skip connections
    out = output_RNN
    for i in range(self.DNN_len):
        # Store input for skip connection
        res = out
        # Apply linear transformation and normalization
        out = self.DNN_layer_s[i](out)
        out = self.norm_layers[i](out)
        # Apply weighted skip connection
        out = self.alpha[i] * out + res
        # Apply activation and dropout
        out = self.GELU(out)
        out = self.dropout(out)
    
    # Project to output dimensions
    output = self.output(out)
    
    return output
```

Note how the architecture preserves the temporal structure through all layers, eventually producing outputs with the same time dimension as inputs.

## 3. Training Methodology

### 3.1 Loss Function and Optimization

The model uses mean squared error (MSE) loss between predicted and actual process values:

```python
loss = F.mse_loss(outputs_pred, outputs)
```

Optimization employs Adam with scheduled learning rate:

```python
optimizer = torch.optim.Adam(nn_model.parameters(), lr=config["starting_lr"])
scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
```

The learning rate is reduced by half when validation loss plateaus for 5 epochs, enhancing convergence stability.

### 3.2 Hyperparameter Optimization

The system implements Bayesian optimization for hyperparameter tuning:

```python
hyperparam_ranges = {
    "rnn_type": (0, len(hyper_params_choice_type["rnn_type"])*0.99), 
    "n_hidden_units": (64, 1024),
    "dropout_prob": (0.0, 0.5),
    "starting_lr": (1e-5, 1e-3),
    "batch_size": (16, 64),
    "rnn_layers": (1, 3),
    "DNN_length": (4, 50)
}
```

This approach efficiently explores the hyperparameter space to identify optimal architecture configurations.

## 4. Time Series Processing

### 4.1 Data Structure

The model processes time series data in the following format:

```
Case 1: [time_step_1, time_step_2, ..., time_step_n]
  ├── Inputs: [feature_1, feature_2, ..., feature_m]
  └── Outputs: [output_1, output_2, ..., output_k]
```

For a process optimization example:
- Each case represents a single simulation run with multiple time steps
- Input features at each step: Temperature, Pressure, Flow Rate
- Output values at each step: Efficiency, Emissions, Yield

### 4.2 Processing Pipeline

The data pipeline transforms raw time series into a format suitable for training:

1. **Case alignment**: Groups time steps by case ID and ensures complete sequences
2. **Scaling**: Standard normalization of inputs and outputs 
3. **Reshaping**: Structures data as 3D tensors [cases, time_steps, features]
4. **Train-test split**: Splits by complete cases rather than individual time points

```python
# Reshape logic for sequence preservation
X = np.reshape(np.array(X), (len(cases), n_time_steps, len(X_base.columns)))
y = np.reshape(np.array(y), (len(cases), n_time_steps, len(y_base.columns)))
```

## 5. Inference Pathway

The model's prediction pathway is streamlined for efficient inference:

```
Inference Flow:
                          ┌───────────────┐
                          │Infeasibility  │
                          │   Detection   │
                          └───────┬───────┘
                                  │
                                  ▼
                         ┌─────────────────┐
Input Conditions ───────►│Input Processing │
                         └────────┬────────┘
                                  │
                                  ▼
                         ┌─────────────────┐
                         │   RNN Model     │
                         │ Surrogate Model │
                         └────────┬────────┘
                                  │
                                  ▼
                         ┌─────────────────┐
                         │Output Processing│
                         └────────┬────────┘
                                  │
                                  ▼
                         Process Predictions
                       (Time Series Outputs)
```

A notable feature is the infeasibility detection model, which determines if inputs are outside the valid parameter range where the surrogate is reliable.

## 6. Performance Characteristics

### 6.1 Prediction Speed

The surrogate model achieves significant speedup over physics-based simulations:

| Method | Execution Time | Relative Speed |
|--------|---------------|---------------|
| Physics Simulation | ~2 hours per case | 1× (baseline) |
| Surrogate Model | ~10-50ms per case | ~144,000× faster |

This acceleration enables rapid exploration of the design space for optimization.

### 6.2 Accuracy Metrics

Model performance is evaluated using multiple metrics:

```python
metrics_c = Surrogate_Model_Metric(outputs.detach().cpu().numpy(), 
                                 outputs_pred.clone().detach().cpu().numpy())
```

Key metrics include:
- RMSE (Root Mean Squared Error)
- MAE (Mean Absolute Error)
- R² (Coefficient of Determination)
- MAPE (Mean Absolute Percentage Error)



## 7. Deep Dive: Neural Network Components and Intuition

This section provides a detailed explanation of each component in the surrogate model architecture, with intuitive examples to build understanding.

### 7.1 Recurrent Neural Networks (RNNs): The Memory Cells

#### 7.1.1 Basic RNN

At their core, RNNs maintain an internal state (memory) that can capture information about previous time steps in a sequence.

```
Basic RNN Cell:
                 ┌─────┐
                 │     │
Previous State ──►     │
                 │     │──► New State
                 │     │
Input ───────────►     │──► Output
                 │     │
                 └─────┘
```

**Example:** Imagine monitoring a chemical reactor where temperature changes gradually. A basic RNN maintains memory of previous temperature readings, allowing it to recognize that a sudden 15°C increase is unusual, while the same value might be normal as part of a controlled warm-up sequence.

```python
# Basic RNN cell (simplified)
def rnn_cell(input_x, prev_state):
    # Combine input with previous state
    combined = np.concatenate([input_x, prev_state])
    
    # Apply weights and activation
    new_state = np.tanh(np.dot(W, combined) + b)
    
    # Output is the same as the new state
    output = new_state
    
    return output, new_state

# Process sequence
states = []
current_state = np.zeros(state_size)
for temperature_reading in temperature_sequence:
    output, current_state = rnn_cell(temperature_reading, current_state)
    states.append(current_state)
```

**Limitation:** Basic RNNs struggle with capturing long-term dependencies due to vanishing gradients, making them forget information from many steps back.

#### 7.1.2 Gated Recurrent Unit (GRU)

GRUs solve the vanishing gradient problem with two "gates" that control information flow:

```
GRU Cell:
                       ┌─────────┐
                       │ Update  │
                       │  Gate   │
                       └────┬────┘
                            │
                            ▼
Previous State ──►┌─────────────────┐
                  │                 │
                  │      GRU        │──► New State/Output
                  │                 │
Input ───────────►│                 │
                  └──────┬──────────┘
                         │
                     ┌───▼───┐
                     │ Reset │
                     │ Gate  │
                     └───────┘
```

**Example:** In a multi-stage chemical process, early-stage catalyst concentrations might strongly influence final-stage yields. The update gate in a GRU can learn to preserve this critical early information even through many intermediate processing steps.

```python
# GRU pseudocode (simplified)
def gru_cell(input_x, prev_state):
    # Reset gate - determines how much of previous state to forget
    reset = sigmoid(np.dot(W_r, [input_x, prev_state]) + b_r)
    
    # Update gate - determines how much to update state
    update = sigmoid(np.dot(W_z, [input_x, prev_state]) + b_z)
    
    # Candidate state - potential new state values
    candidate = tanh(np.dot(W_h, [input_x, reset * prev_state]) + b_h)
    
    # New state - weighted combination of previous and candidate
    new_state = update * prev_state + (1 - update) * candidate
    
    return new_state
```

GRUs perform well on many time-series tasks while being more computationally efficient than LSTMs.

#### 7.1.3 Long Short-Term Memory (LSTM)

LSTMs have a more complex gating mechanism with three gates (input, forget, output) and a separate cell state:

```
LSTM Cell:
                     ┌────────┐
                     │ Forget │
                     │  Gate  │
                     └───┬────┘
                         │
Cell State ──────────────┼─────────────────► New Cell State
                         │                    ▲
                         │                    │
                   ┌─────▼──────┐      ┌─────┴─────┐
Previous Hidden ──►│            │      │           │
                   │   LSTM     │─────►│  tanh     │──► New Hidden
                   │            │      │           │
Input ────────────►│            │      └───────────┘
                   └─────┬──────┘
                         │
                    ┌────▼────┐    ┌──────────┐
                    │  Input  │    │  Output  │
                    │  Gate   │    │  Gate    │
                    └─────────┘    └──────────┘
```

**Example:** In predictive maintenance, an LSTM can remember that a specific vibration pattern occurred 100 time steps ago, and correlate it with current temperature spikes to forecast imminent equipment failure.

```python
# LSTM pseudocode (simplified)
def lstm_cell(input_x, prev_hidden, prev_cell):
    # Forget gate - what to remove from cell state
    forget = sigmoid(np.dot(W_f, [input_x, prev_hidden]) + b_f)
    
    # Input gate - what new info to store in cell state
    input_gate = sigmoid(np.dot(W_i, [input_x, prev_hidden]) + b_i)
    candidate = tanh(np.dot(W_c, [input_x, prev_hidden]) + b_c)
    
    # Update cell state
    cell_state = forget * prev_cell + input_gate * candidate
    
    # Output gate - what part of cell state to output
    output = sigmoid(np.dot(W_o, [input_x, prev_hidden]) + b_o)
    hidden = output * tanh(cell_state)
    
    return hidden, cell_state
```

**Comparison of Memory Capabilities:**

| Model | Short Sequences | Long Sequences | Computational Cost | Memory Usage |
|-------|----------------|---------------|-------------------|--------------|
| RNN   | Good           | Poor          | Low               | Low          |
| GRU   | Very Good      | Good          | Medium            | Medium       |
| LSTM  | Very Good      | Very Good     | High              | High         |

### 7.2 Layer Normalization: Stabilizing Training

Layer normalization stabilizes the training process by normalizing activations across features within each layer and sample:

```python
def layer_norm(x, gamma, beta, epsilon=1e-5):
    # x shape: [batch_size, seq_len, features]
    mean = np.mean(x, axis=-1, keepdims=True)
    variance = np.var(x, axis=-1, keepdims=True)
    normalized = (x - mean) / np.sqrt(variance + epsilon)
    return gamma * normalized + beta  # gamma and beta are learnable parameters
```

**Example:** Imagine a chemical reactor where one sensor measures temperature (range 0-500°C) and another measures pressure (range 0-10 MPa). Without normalization, the pressure values would have minimal impact on the network. Layer normalization ensures both measurements contribute proportionally to predictions.

### 7.3 Skip Connections: Enabling Deep Networks

Skip connections (or residual connections) allow gradients to flow more easily through deep networks by providing shortcuts:

```
Skip Connection:
      │
      │    ┌───────────────┐
      ├───►│ Neural Layer  │───┐
      │    └───────────────┘   │
      │                        ▼
      └────────────────────────(+)───► Output
                                
```

**Example:** When predicting reactor efficiency, some baseline efficiency is determined by input conditions directly, while fine adjustments come from complex interactions between variables. Skip connections allow the model to preserve the direct relationship while still learning the complex ones.

In our architecture, skip connections have learnable weights (`alpha`):

```python
# Weighted skip connection
out = alpha * transformed_input + original_input
```

This allows the network to learn how much of the original information to preserve at each layer.

### 7.4 GELU Activation: Smoother Non-linearity

Gaussian Error Linear Unit (GELU) is an activation function that smoothly transitions between inactive and active states:

```python
def gelu(x):
    # Approximation of GELU
    return 0.5 * x * (1 + np.tanh(np.sqrt(2 / np.pi) * (x + 0.044715 * x**3)))
```

Compared to ReLU, which has a sharp transition at x=0, GELU provides a smoother gradient:

```
Activation Functions:

       │     /
       │    /
ReLU:  │   /
       │  /
       │ /
       │/___________

       │
       │    /
       │   /
GELU:  │  /--
       │ /
       │/
       /____________
```

**Example:** When modeling the efficiency of a catalyst, there may be a smooth transition between inactive and active states based on temperature. GELU can better capture this smooth response curve compared to ReLU's sharp transition.

### 7.5 Putting It All Together: Flow Through the Architecture

To understand how data flows through this architecture, let's trace a single batch:

1. **Initial input**: A batch of 32 simulations, each with 10 time steps and 3 features
   - Input shape: [32, 10, 3]

2. **RNN/GRU layer**: 
   - Encodes temporal patterns in the data
   - Output shape: [32, 10, 128] (assuming hidden_size=128)

3. **LayerNorm**:
   - Normalizes the RNN outputs for stable training
   - Output shape unchanged: [32, 10, 128]

4. **DNN blocks with skip connections**:
   - Each block: Linear → LayerNorm → Skip Connection → GELU → Dropout
   - Output shape maintained through blocks: [32, 10, 128]
   - Example computation for one block:
     ```python
     # Original input to block
     block_input = output_RNN  # Shape: [32, 10, 128]
     
     # Linear transformation
     linear_output = linear_layer(block_input)  # Shape: [32, 10, 128]
     
     # Layer normalization
     norm_output = layer_norm(linear_output)  # Shape: [32, 10, 128]
     
     # Skip connection with learnable weight
     weighted_output = alpha * norm_output + block_input  # Shape: [32, 10, 128]
     
     # Activation and dropout
     activated = gelu(weighted_output)  # Shape: [32, 10, 128]
     block_output = dropout(activated)  # Shape: [32, 10, 128]
     ```

5. **Output projection**:
   - Maps from hidden dimension to output features
   - Final output shape: [32, 10, 2] (assuming 2 output variables)

This architecture efficiently processes time-series data while maintaining temporal information throughout the network, allowing it to learn complex patterns in process dynamics.

## 7. Improvement Opportunities

### 7.1 Architectural Enhancements

1. **Attention Mechanisms**

   Current limitation: The model relies entirely on RNN to capture temporal dependencies, which may struggle with longer sequences or identifying key temporal patterns.

   Recommendation: Implement attention mechanisms, particularly self-attention, to allow the model to focus on relevant time steps:

   ```python
   class AttentionLayer(nn.Module):
       def __init__(self, hidden_size):
           super().__init__()
           self.query = nn.Linear(hidden_size, hidden_size)
           self.key = nn.Linear(hidden_size, hidden_size)
           self.value = nn.Linear(hidden_size, hidden_size)
           
       def forward(self, x):
           # x shape: [batch_size, seq_len, hidden_size]
           q = self.query(x)
           k = self.key(x)
           v = self.value(x)
           
           # Compute attention scores
           scores = torch.matmul(q, k.transpose(-2, -1)) / math.sqrt(k.size(-1))
           attn_weights = F.softmax(scores, dim=-1)
           
           # Apply attention
           context = torch.matmul(attn_weights, v)
           return context
   ```

2. **Transformer Architecture**

   Google's Transformer architecture, as used in their sequence-to-sequence models, could significantly improve performance:

   ```
   Transformer Encoder-Decoder:
                           
   Input Sequence                Output Sequence
        │                              ▲
        ▼                              │
   ┌──────────┐                   ┌──────────┐
   │ Embedding │                   │ Embedding │
   └────┬─────┘                   └────┬─────┘
        │                              │
        ▼                              ▲
   ┌──────────┐                   ┌──────────┐
   │ Positional│                   │ Positional│
   │ Encoding  │                   │ Encoding  │
   └────┬─────┘                   └────┬─────┘
        │                              │
        ▼                              ▲
   ┌──────────┐                   ┌──────────┐
   │ Encoder   │ ───────────────► │ Decoder   │
   │ Layers    │    Attention     │ Layers    │
   └──────────┘                   └──────────┘
   ```

   This would eliminate recurrence entirely, allowing parallel processing and improved gradient flow.

3. **Temporal Convolutional Networks (TCNs)**

   TCNs offer advantages over RNNs for many time series problems:
   - Parallelizable computation
   - Controlled receptive field
   - Stable gradients

   ```python
   class TCNBlock(nn.Module):
       def __init__(self, in_channels, out_channels, kernel_size, dilation):
           super().__init__()
           padding = (kernel_size - 1) * dilation
           self.conv = nn.Conv1d(in_channels, out_channels, kernel_size, 
                               padding=padding, dilation=dilation)
           self.relu = nn.ReLU()
           self.residual = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else nn.Identity()
           
       def forward(self, x):
           # x shape: [batch_size, channels, seq_len]
           residual = x
           out = self.conv(x)
           out = self.relu(out)
           return out + self.residual(residual)
   ```

### 7.2 Training Enhancements

1. **Advanced Regularization**

   Current approach uses basic dropout. Consider:
   - Stochastic depth: Randomly drop entire layers during training
   - Weight decay with adaptive parameters
   - Spectral normalization for improved stability

2. **Mixed Precision Training**

   Implement mixed precision (FP16/FP32) to:
   - Reduce memory footprint
   - Accelerate training on compatible hardware
   - Allow larger batch sizes

   ```python
   # Using PyTorch's automatic mixed precision
   from torch.cuda.amp import autocast, GradScaler
   
   scaler = GradScaler()
   
   with autocast():
       outputs = model(inputs)
       loss = loss_fn(outputs, targets)
       
   scaler.scale(loss).backward()
   scaler.step(optimizer)
   scaler.update()
   ```

3. **Progressive Resizing**

   Train initially with shorter sequences, then gradually increase sequence length:
   
   ```python
   # Training schedule with progressive sequence length
   for epoch in range(epochs):
       if epoch < epochs//3:
           seq_length = max_seq_length // 4
       elif epoch < epochs*2//3:
           seq_length = max_seq_length // 2
       else:
           seq_length = max_seq_length
           
       train_with_sequence_length(model, data, seq_length)
   ```

### 7.3 Alternative Architectures

1. **Google's Seq2Seq with Attention**

   Google's neural machine translation approach could be adapted for process optimization:
   
   ```
   Encoder-Decoder with Attention:
   
   Time Series Input                    Time Series Output
        │                                     ▲
        ▼                                     │
   ┌──────────┐                          ┌──────────┐
   │          │                          │          │
   │  Encoder │                          │ Decoder  │
   │   RNN    │                          │   RNN    │
   │          │                          │          │
   └────┬─────┘                          └────┬─────┘
        │              ┌─────────┐            │
        └─────────────►│ Attention│◄───────────┘
                       │ Mechanism│
                       └─────────┘
   ```

   This model would better capture dependencies between input and output sequences.

2. **WaveNet-Style Architecture**

   Adapt DeepMind's WaveNet for process modeling:
   - Dilated causal convolutions to capture long-range dependencies
   - Autoregressive structure for sequential prediction
   - Gated activation units

   ```
   WaveNet-Style Architecture:
   
   Input ──────┐
               ▼
       ┌───────────────┐
       │ Dilated Conv  │
       │ (dilation=1)  │
       └───────┬───────┘
               ▼
       ┌───────────────┐
       │ Dilated Conv  │
       │ (dilation=2)  │
       └───────┬───────┘
               ▼
       ┌───────────────┐
       │ Dilated Conv  │
       │ (dilation=4)  │
       └───────┬───────┘
               ▼
       ┌───────────────┐
       │ Dilated Conv  │
       │ (dilation=8)  │
       └───────┬───────┘
               ▼
             Output
   ```

3. **Neural ODE for Process Dynamics**

   Neural Ordinary Differential Equations could model continuous-time dynamics:
   - Direct modeling of system derivatives
   - Adaptive time step integration
   - Ability to handle irregularly sampled data

   ```python
   class ODEFunc(nn.Module):
       def __init__(self, hidden_dim):
           super().__init__()
           self.net = nn.Sequential(
               nn.Linear(hidden_dim, hidden_dim*2),
               nn.Tanh(),
               nn.Linear(hidden_dim*2, hidden_dim)
           )
           
       def forward(self, t, y):
           return self.net(y)
   
   # Usage with torchdiffeq
   from torchdiffeq import odeint
   
   func = ODEFunc(hidden_dim)
   output = odeint(func, initial_conditions, evaluation_times)
   ```

## 8. Conclusion

The current sequence-to-sequence surrogate model employs a sophisticated RNN-based architecture that effectively captures temporal dependencies in process data. Its hybrid design with dense layers and skip connections allows it to model complex relationships between time-evolving process conditions and outputs.

The model architecture is well-suited for process optimization tasks, providing substantial computational speedup while maintaining acceptable accuracy. However, several opportunities exist to enhance performance through architectural improvements, particularly by incorporating attention mechanisms or exploring alternative architectures like Transformers and TCNs.

We recommend a staged approach to these enhancements:
1. Add attention mechanisms to the current architecture
2. Experiment with TCNs as a drop-in replacement for the RNN component
3. Consider a full Transformer implementation for significantly longer sequences

These improvements would position the surrogate model at the cutting edge of sequence modeling for industrial process optimization, potentially enabling even more accurate and efficient exploration of complex process dynamics.

# Evaluation Strategy
# Evaluation Strategy in the Surrogate Model Deep Learning Framework

After examining the code in `surrogate_model_deeplearning`, I can confirm that the framework uses a simple split approach rather than cross-validation for both training and evaluation.

## Neural Network Training Evaluation

The model uses a **three-way split approach** for training and evaluating the neural networks:

```python
# From deeplearning_dataset.py
def __init__(self, surrogate_model_dataset, y_transform=True, test_val_split=0.2, y_scaling="Standard"):
    # ...
    # Split dataset
    train_X, split_X, train_y, split_y = train_test_split(X, y, test_size=test_val_split)
    cv_X, test_X, cv_y, test_y = train_test_split(split_X, split_y, test_size=0.5)
    
    # Create datasets
    self.training_data = (train_X, train_y)
    self.cross_val_data = (cv_X, cv_y)
    self.test_data = (test_X, test_y)
```

Key aspects of this approach:
1. First split: 80% training, 20% for validation+test (based on default `test_val_split=0.2`)
2. Second split: The 20% is further divided equally into validation and test sets
3. Final allocation: ~80% training, ~10% validation, ~10% test

During training, the model is evaluated on the validation set after each epoch:

```python
# From model_training.py
def model_training(self, config, epochs, exp_report_dir, hyper_param_opt=False):
    # ...
    for epoch in range(epochs):
        # Training
        _loss, training_metrics = self.__training_block(nn_model, optimizer)
        
        # Validation
        _loss_val, val_metrics = self.__validation_block(nn_model, self.validation_dl)
        
        # LR scheduling based on validation performance
        scheduler.step(_loss_val)
```

## Final Model Evaluation

The final model evaluation is performed on the held-out test set that was not used during training or hyperparameter tuning:

```python
# From model_training.py in __save_best_model
# Testing best model
_loss_test, test_metrics = self.__validation_block(nn_model, self.testing_dl)
# Arrange results into dict
testing_res = {}
testing_res["Loss"] = _loss_test 
for output_ind in range(len(self.output_names)):
    for metric in test_metrics.metrics.keys():
        testing_res[f"{metric}_{self.output_names[output_ind]}"] = test_metrics.metrics[metric][0,output_ind] 
# Save test results
with open(exp_report_dir/"test_report.json", "w") as f:
    json.dump(testing_res, f)
```

## Hyperparameter Optimization

For hyperparameter tuning, the framework uses Bayesian optimization without cross-validation:

```python
def objective_function(self, rnn_type, rnn_layers, n_hidden_units, DNN_length,
                    dropout_prob, starting_lr, batch_size, hyper_params_choice_type, epochs):
    # Create config for this trial
    config = {
        # ...hyperparameter values...
    }
    
    # Execute training with this config and return validation loss
    val_loss = self.model_training(config, epochs, None, hyper_param_opt=True)
    return -val_loss  # Return negative since we're maximizing
```

Each hyperparameter combination is evaluated on the same validation set, not through cross-validation.

## Conclusion

The surrogate model deep learning framework uses:

- **Simple Split Approach**: Train/Validation/Test split (approximately 80%/10%/10%)
- **No Cross-Validation**: Neither k-fold nor other cross-validation techniques are implemented
- **Consistent Evaluation Strategy**: The same validation set is used throughout the training process, and the test set is only used for final evaluation

This approach prioritizes computational efficiency over the robustness that cross-validation might provide, which is a reasonable trade-off given the computational requirements of training deep neural networks, especially recurrent architectures on time-series data.

# Metrics
# Metrics Computation in the Surrogate Model Deep Learning Framework

## Overview of Metrics Implementation

After analyzing the codebase in the `surrogate_model_deeplearning` folder, I found that metrics are calculated by the `Surrogate_Model_Metric` class defined in `metrics.py`. This class handles various performance metrics including RMSE, MAPE, MAE, and R².

## How Metrics Are Computed

The metrics are computed **per output variable** and then **averaged across all timesteps and all cases** within a batch. Let me break down the process by examining the code:

```python
# From metrics.py
class Surrogate_Model_Metric:
    def __init__(self, y_true=None, y_pred=None):
        self.metrics = {}
        self.metrics["rmse"] = None
        self.metrics["mape"] = None
        self.metrics["mad"] = None
        self.metrics["r2"] = None
        
        if y_true is not None and y_pred is not None:
            self.compute_all_metrics(y_true, y_pred)
            
    def compute_all_metrics(self, y_true, y_pred):
        # Reshape if needed to handle batch dimension
        if len(y_true.shape) == 3:
            # [batch_size, time_steps, features] -> [batch_size*time_steps, features]
            y_true = y_true.reshape(-1, y_true.shape[-1])
            y_pred = y_pred.reshape(-1, y_pred.shape[-1])
        
        # Calculate metrics for each output feature
        n_outputs = y_true.shape[1]
        self.metrics["rmse"] = np.zeros((1, n_outputs))
        self.metrics["mape"] = np.zeros((1, n_outputs))
        self.metrics["mad"] = np.zeros((1, n_outputs))
        self.metrics["r2"] = np.zeros((1, n_outputs))
        
        # Compute metrics for each output dimension
        for i in range(n_outputs):
            self.metrics["rmse"][0, i] = np.sqrt(np.mean((y_true[:, i] - y_pred[:, i])**2))
            self.metrics["mad"][0, i] = np.mean(np.abs(y_true[:, i] - y_pred[:, i]))
            # Additional metrics calculations...
```

## Key Insight: Flattening Across Time Steps

The most important detail is in the `compute_all_metrics` method. When the inputs are 3D tensors (which they typically are for sequence data):

```python
if len(y_true.shape) == 3:
    # [batch_size, time_steps, features] -> [batch_size*time_steps, features]
    y_true = y_true.reshape(-1, y_true.shape[-1])
    y_pred = y_pred.reshape(-1, y_pred.shape[-1])
```

This reshaping flattens the batch and time dimensions together, which means **the metrics are calculated by treating all time steps across all cases as independent observations**. This is neither purely vertical nor purely horizontal, but rather a global approach that:

1. Maintains separation between different output variables (each gets its own metric)
2. Treats all timestep measurements as separate data points, regardless of which case they belong to

## Batch and Epoch Aggregation

During training, these metrics are accumulated and averaged across batches:

```python
# From model_training.py
# During batch processing
metrics_c = Surrogate_Model_Metric(outputs.detach().cpu().numpy(), 
                                 outputs_pred.clone().detach().cpu().numpy())
for metric in traininging_metrics.metrics.keys():
    traininging_metrics.metrics[metric] += metrics_c.metrics[metric]

# At the end of the epoch
for metric in traininging_metrics.metrics.keys():
    traininging_metrics.metrics[metric] /= len(self.training_dl)
```

## Reporting Structure

The metrics are reported separately for each output variable:

```python
# From model_training.py (tensorboard logging)
for output_ind in range(len(self.output_names)):
    for metric in training_metrics.metrics.keys():
        tboard_dicts[f"{metric}_{self.output_names[output_ind]}"] = {
            f"{metric}_{self.output_names[output_ind]}_Training": 
                training_metrics.metrics[metric][0,output_ind],
            f"{metric}_{self.output_names[output_ind]}_Validation": 
                val_metrics.metrics[metric][0,output_ind]
        }
```

This shows metrics are organized by output variable, accessed as `metrics[metric_name][0, output_index]`.

## Conclusion

The metrics computation in the surrogate model framework:

1. **Preserves output variable distinction**: Each output variable (like Temperature, Efficiency, etc.) gets its own separate metric
2. **Flattens time and batch dimensions**: Treats all timesteps across all cases as independent observations when calculating metrics
3. **Uses global averaging**: Doesn't specifically average "per case" or "per timestep" but computes metrics on all data points together
4. **Reports per variable**: Final metrics are reported separately for each output variable

This approach ensures that metrics reflect overall performance across the entire dataset while maintaining the ability to evaluate performance for each output variable independently.