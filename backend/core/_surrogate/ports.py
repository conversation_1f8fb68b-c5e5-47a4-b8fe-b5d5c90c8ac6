from __future__ import annotations
import abc
from typing import Op<PERSON>, Tu<PERSON>, Any, Callable
from .valueobjects import *
from .transformers import *
from .trainers import get_default_trainer_registry
from .trainers import *
from .entities import *
from .ports import SurrogateTrainerPort
from ._enums import *

class TrainingRunnerPort(abc.ABC):
    """
    Abstract port for training execution infrastructure.

    This port abstracts the execution of training jobs, allowing for different
    execution strategies (local, cloud, distributed) while maintaining the same
    interface. Following hexagonal architecture principles, this port defines
    the contract between the core domain (ISurrogate) and infrastructure adapters.
    """

    def __init__(self, trainer_registry: Optional[TrainerRegistry] = None):
        """
        Initialize the training runner with optional trainer registry.

        Args:
            trainer_registry: Optional TrainerRegistry instance. If None, uses default registry.
        """
        self.trainer_registry = trainer_registry or get_default_trainer_registry()

    @abstractmethod
    def submit_training_job(
        self,
        job: ENTTrainingJob,
        training_data: VODataset,
        test_data: Optional[VODataset] = None,
        logging_callback: Optional[Callable] = None
    ) -> Tuple[Any, ENTTrainingJob]:
        """
        Submit a training job for execution.

        This method abstracts the training execution, allowing for different
        execution strategies while maintaining consistent input/output contracts.

        Args:
            metadata: Training metadata including algorithm and identifiers
            training_data: Dataset for model training
            training_config: Training configuration (validation strategy, metrics, etc.)
            model_config: Model-specific configuration parameters
            job: Training job entity to track execution state
            test_data: Optional test dataset for evaluation
            hpo_config: Optional hyperparameter optimization configuration
            logging_callback: Optional callback for training progress logging

        Returns:
            Tuple of (native_model, updated_job) where:
            - native_model: The trained model in its native format (e.g., torch.nn.Module, sklearn model)
            - updated_job: ENTTrainingJob with execution results, metrics, and status

        Raises:
            SurrogateTrainingError: If training execution fails
            SurrogateConfigError: If configuration is invalid

        Note:
            The return type uses Any for native_model to support different model types
            (PyTorch, scikit-learn, etc.) while maintaining type safety at the adapter level.
        """
        pass

    @abstractmethod
    def setup_infrastructure(self) -> None:
        """
        Set up required infrastructure for training execution.

        This method handles any infrastructure setup required before training
        can be executed. For local runners, this might be a no-op. For cloud
        runners, this could involve setting up compute resources, containers,
        or distributed training clusters.

        Raises:
            SurrogateInfrastructureError: If infrastructure setup fails
        """
        pass

    @abstractmethod
    def teardown_infrastructure(self) -> None:
        """
        Clean up infrastructure resources after training completion.

        This method handles cleanup of any resources allocated during setup.
        Following the principle of resource management, this ensures no
        resource leaks in long-running applications.

        Raises:
            SurrogateInfrastructureError: If cleanup fails
        """
        pass
