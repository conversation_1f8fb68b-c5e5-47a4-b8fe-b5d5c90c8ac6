"""
# NOTE
Use lazy import for all internal imports to avoid circular dependencies.
"""
from __future__ import annotations
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    Dict,
    Generic,
    Iterable,
    List,
    Optional,
    Set,
    Tuple,
    Type,
    TypeVar,
    Union,
    overload,
    Protocol,
    runtime_checkable,
    get_type_hints,
    Literal,
    get_args,
    Iterator,
)

import sklearn.model_selection
"""
Data splitting utilities for surrogate models
"""
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split, KFold
from .valueobjects import VODataset, VOConfig_Training
from typing import Tuple, Optional
import sklearn

def split_data_simple(
        data: VODataset,
        validation_ratio: float,
        random_seed: int = 42
) -> Tuple[VODataset, VODataset]:
    """
    Create a simple random split for arrays.
    Correctly splits for seq data on timeset dimension (Dim0)
    Guarantees min 5 samples per partition.
    """
    n_samples = data.arr_x.shape[0]
    
    min_val_samples = 5
    min_train_samples = 5
    min_samples= int(min_val_samples / validation_ratio) if validation_ratio > 0 else min_train_samples
    
    if n_samples < min_samples:
        raise ValueError(
            f"Dataset has insufficient samples to meet minimum partition samples: x: {data.arr_x.shape}, y: {data.arr_y.shape}"
        )
    
    indices = np.arange(n_samples)
    train_indices, val_indices = sklearn.model_selection.train_test_split(
        indices,
        test_size = validation_ratio,
        random_state= random_seed
    )
    
    # Create Datasets
    train_data = VODataset(
        arr_x=data.arr_x[train_indices],
        arr_y=data.arr_y[train_indices],
        transformer_uid=data.transformer_uid,
        colnames_x=data.colnames_x,
        colnames_y=data.colnames_y,
        pattern=data.pattern
    )
    
    val_data =VODataset(
        arr_x=data.arr_x[val_indices],
        arr_y=data.arr_y[val_indices],
        transformer_uid=data.transformer_uid,
        colnames_x=data.colnames_x,
        colnames_y=data.colnames_y,
        pattern=data.pattern
    )
    
    return train_data, val_data


import os
import multiprocessing

def get_optimal_num_workers():
    """Determine optimal number of workers based on system resources"""

    # Start with CPU count
    cpu_count = multiprocessing.cpu_count()
    
    # Check if running in a container with CPU limits
    if os.path.exists('/sys/fs/cgroup/cpu/cpu.cfs_quota_us'):
        try:
            with open('/sys/fs/cgroup/cpu/cpu.cfs_quota_us') as f:
                quota = int(f.read())
            with open('/sys/fs/cgroup/cpu/cpu.cfs_period_us') as f:
                period = int(f.read())
            if quota > 0:
                # Container CPU limit exists, calculate allowed CPUs
                container_cpus = max(1, int(quota / period))
                cpu_count = min(cpu_count, container_cpus)
        except:
            pass  # Fall back to CPU count if reading cgroup fails
    
    # Use at most 4 workers by default, leaving some CPUs for other tasks
    # For very small datasets, fewer workers may be more efficient
    return min(4, max(0, cpu_count - 1))

import numpy.typing as npt
import logging

class UtilsForArrays:
    @staticmethod
    def reshape_ts_array(arr: npt.NDArray):
        """
        reshapes a [timeset, timestep, variables] shape to 2D array. 
        Order of array follows timeset
        """
        # Defensive
        if len(arr.shape) != 3:
            logging.warning(f"No reshaping done: array shape not TS")
            return arr
        
        n_timesets, n_timesteps, n_variables = arr.shape
        return arr.reshape (n_timesets * n_timesteps, -1)

    
import base64
import pandas as pd
from io import StringIO

def decode_and_filter_csv(base64_csv: str, columns: list[str]) -> pd.DataFrame:
    """Decode base64 CSV, filter columns, and return DataFrame."""
    import pandas.errors
    csv_str = base64.b64decode(base64_csv).decode("utf-8")
    try:
        df = pd.read_csv(StringIO(csv_str))
    except pandas.errors.EmptyDataError:
        raise ValueError("Empty CSV data: No columns or data found in the provided CSV")
    
    missing = [col for col in columns if col not in df.columns]
    if missing:
        raise ValueError(f"Missing columns in CSV: {missing}")
    return df[columns]