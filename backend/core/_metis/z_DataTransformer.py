from __future__ import annotations
from ._imports import *

from backend.core._sharedutils import data_precision

#######################################################
# GLOBAL SETTINGS - Setting data precision
set_precision = int(3)
#######################################################


class z_DataTransformer:
    def __init__(
        self,
        input_var_val: pd.DataFrame,
        output_var_val: pd.DataFrame,
        output_var_range: List[float],
        fix_var_names: List[str],
        nfix_var_names: List[str],
        bound_change_factor: float = 0.10,
        bound_change_iter: int = 10,
    ) -> None:
        """
        Instantiate the object of class DataTransformer.
        It is used to filter data based on the KPI value range and then transform relative to reference data.
        Args:
            input_var_val (dataframe): A dataframe with input variable values representing current state.
            output_var_val (dataframe): A dataframe with output variable values for the given input variable values.
            output_var_range (list): A list with the lower and upper bound on the expected output variable value.
            fix_var_names (list): A list of fixed variable names.
            nfix_var_names (list): A list of non-fixed variable names.
            bound_change_factor (float, optional): A fractional float value used for updating the given kpi_bounds when empty filtered dataframe is obtained.
            bound_change_iter (int, optinal): Number of iterations over which bounds are updated until a non-empty filtered dataframe is obtianed.

        Returns:
            None
        """
        # Defensive mechanism to check if input reference value is a single point
        if input_var_val.shape[0] == 1:
            logging.info(
                f"Single point for input variable reference value is specified!"
            )
            self.input_var_val = input_var_val
            self.fix_var_names = fix_var_names
            self.nfix_var_names = nfix_var_names
        else:
            logging.error(
                f"Expected one point for input variable reference value and got {input_var_val.shape[0]} points."
            )
        self.output_var_val = output_var_val
        self.output_var_names = list(output_var_val.columns)
        self.output_var_range = output_var_range
        self.bound_change_factor = bound_change_factor
        self.bound_change_iter = bound_change_iter

    def _filter_data(self, summary: pd.DataFrame) -> Tuple[pd.DataFrame, float]:
        """
        Filter the summarized results based on the KPI range.
        If the filtered dataframe is empty, then the bounds are expanded until at least one sample is present.
        This is an internal method.

        Args:
            summary (dataframe): A dataframe with summarized results comprising samples and responses.

        Returns:
            filtered_results (dataframe): A dataframe of filtered results.
            flag_range (float): Code indicating if the specified range is violated.
        """
        # Check if the specified KPI range is outside the summary.
        min_output_summary = float(summary[self.output_var_names[0]].min())
        max_output_summary = float(summary[self.output_var_names[0]].max())

        # Check if the specified range is outside the evaluated outputs
        if (
            max_output_summary <= self.output_var_range[0]
            or min_output_summary >= self.output_var_range[1]
        ):
            flag_range = 1
            # Filter the summarized results dataframe based on the specified output variable bounds.
            filtered_results = copy.deepcopy(summary)

        else:
            flag_range = 0
            # Filter the summarized results dataframe based on the specified output variable bounds.
            filtered_results = summary[
                (summary[self.output_var_names[0]] <= self.output_var_range[1])
                & (summary[self.output_var_names[0]] >= self.output_var_range[0])
            ]

        # Avoid returning non-empty filtered dataframe by loosening lower bound of output variable value.
        if filtered_results.shape[0] == 0:
            logging.warning(
                f"The filtered summary for the specified bounds is empty {filtered_results}. The bounds will be expanded until a non-empty filtered summary is returned {self.output_var_range}."
            )

            # Run through iterations
            change_iter = 0
            updated_kpi_range = self.output_var_range
            while (
                filtered_results.shape[0] == 0 & change_iter <= self.bound_change_iter
            ):
                updated_kpi_bounds = [
                    (1 - self.bound_change_factor) * updated_kpi_range[0],
                    updated_kpi_range[1],
                ]
                filtered_results = summary[
                    (summary[self.output_var_names[0]] <= updated_kpi_bounds[1])
                    & (summary[self.output_var_names[0]] >= updated_kpi_bounds[0])
                ]
                change_iter = change_iter + 1
            else:
                logging.info(
                    f"After changing the bounds, the following filtered summary is obtained {filtered_results}. Total iterations completed are {change_iter}."
                )

        else:
            logging.info(
                f"The following filtered summary is obtained {filtered_results}."
            )
        filtered_results.reset_index(inplace=True, drop=True)
        filtered_results = data_precision.set_dataframe_precision(
            dataframe=filtered_results, precision=set_precision
        )

        return filtered_results, flag_range

    def _get_reference_df(self, filtered_sample_count: int) -> pd.DataFrame:
        """
        This method generates a reference dataframe for the given input variable value.
        This is an internal method.

        Args:
            filtered_sample_count (int): Number of sample count in filtered data.

        Returns:
            reference_data (dataframe): A dataframe of reference input variables and output variable data.

        """
        # Generate specified reference dataframe
        reference_input = pd.concat(
            [self.input_var_val] * filtered_sample_count, ignore_index=True
        )
        reference_output = pd.concat(
            [self.output_var_val] * filtered_sample_count, ignore_index=True
        )
        reference_data = pd.concat([reference_input, reference_output], axis=1)
        logging.info(
            f"The reference dataframe is generated with {filtered_sample_count} rows based on the input variable values and its associated response {reference_data}."
        )

        return reference_data

    def transform_data(
        self, summary: pd.DataFrame
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        This method generates transformed data for the given summarized data and reference data.
        Args:
            summary (dataframe): A dataframe with the summarized results.
        Returns:
            impact_samples (dataframe): An array of impact samples.
            impact_responses (dataframe): An array of impact responses.
        """
        # Generate filtered results
        self.filtered_results, self.flag_range = self._filter_data(summary=summary)

        # Generate refernce data
        filtered_sample_count = self.filtered_results.shape[0]
        reference_data = self._get_reference_df(
            filtered_sample_count=filtered_sample_count
        )

        # Generate differences for each variable.
        delta_data = self.filtered_results - reference_data
        logging.info(
            f"The relative differences based on reference data are computed {delta_data}."
        )
        # Build dataframes for impact_samples and impact_responses
        impact_samples = delta_data.loc[:, self.nfix_var_names]
        logging.info(
            f"Array of impactful input variables is obtained {impact_samples}."
        )
        impact_responses = delta_data.loc[:, self.output_var_names]
        logging.info(
            f"Array of impactful output variables is obtained {impact_responses}."
        )

        return impact_samples, impact_responses
