# Design Principles for Analytical Classes
 
## Core Architecture Principles

### Configuration/Data Separation
- Configuration: Parameters that define algorithm behavior belong in the constructor
- Data: Inputs to be processed should be method parameters, never constructor arguments
- Rationale: Enables reuse of the same configured algorithm across multiple datasets
```python
# CORRECT
class Analyzer:
    def __init__(self, algorithm_params, tolerance=0.01):
        self.params = algorithm_params
        
    def analyze(self, df_samples):
        # Process df_samples using self.params
        
# INCORRECT - data in constructor
class BadAnalyzer:
    def __init__(self, df_samples, params):
        self.df = df_samples  # Wrong! Data in constructor
```

### Stateful vs Stateless Design
- Configuration State: Keep algorithm parameters, configuration, and setup in instance state
- Computation State: Never persist input data or results between method calls
- Idempotency: Methods should produce the same output given the same input. Therefore, if there is non-deterministic outputs, always specify a seed
```python
# CORRECT
def process_data(self, df_samples):
    results = self._compute(df_samples)
    return results
    
# INCORRECT - storing data state
def process_data(self, df_samples):
    self.samples = df_samples  # Wrong! Storing data state
    self.results = self._compute()  # Wrong! Results in state
    return self.results
```

### Factory Method Pattern
- Default Configurations: Provide class methods that return pre-configured instances
- Use Cases: Each factory method should align with a specific analytical use case
- Naming: Use clear, descriptive names that indicate the configuration purpose
```python
@classmethod
def create_exploration_config(cls):
    """Configuration optimized for exploratory analysis."""
    return cls(sample_count=100, method="Sobol")
    
@classmethod
def create_detailed_analysis_config(cls):
    """Configuration for detailed sensitivity analysis.""" 
    return cls(sample_count=1000, method="Morris")
```

## Implementation Standards

### Input/Output Consistency
- Primary Data Format: Pandas DataFrames for both input and output data
- Standard Naming: Use consistent variable names
    - df_samples: For experimental design/input samples
    - df_results: For analysis outputs and results
- Return Types: Methods should return complete result objects, not partial data
    - Prefer dataclasses or dataframes over tuples
    - Include metadata and summary statistics with raw results

```python
def analyze_sensitivity(self, df_samples, df_results):
    """
    Returns:
        Dict containing:
            - summary: Overall statistics
            - importance: Feature importance DataFrame
            - rankings: Ranked factors DataFrame
    """
```

### Error Handling and Validation
- Input Validation: Validate inputs early and explicitly
- Descriptive Errors: Raise specific exceptions with helpful messages
- Fail Fast: Detect and report configuration errors at initialization time
- Graceful Degradation: Provide fallbacks for non-critical failures
```python
def __init__(self, method="Sobol"):
    if method not in ["Sobol", "Morris", "FAST"]:
        raise ValueError(f"Method '{method}' not supported. Choose from: Sobol, Morris, FAST")
```

### Method Organization
- Public Interface: Provide clear, well-documented public methods
- Private Helpers: Implement detailed logic in private methods (prefixed with _)
- Consistent Structure:
    - __init__: Configuration only
    - Public methods: Data processing entry points
    - _helper_methods: Implementation details

## Documentation and Design

### Documentation Standards
- Class Purpose: Clear description of the analytical capability
- Configuration Parameters: Document each parameter with type, default, and purpose
- Method Documentation: For each method document:
    - Purpose and analytical approach
    - Parameter descriptions with types
    - Return value structure with types
    - Examples of typical usage

## Extension Points
- Strategy Pattern: Use composition over inheritance for algorithm variants
- Plugin Architecture: Define clear interfaces for extension points
- Configuration Hooks: Allow customization of underlying algorithms
```python
def set_custom_algorithm(self, algorithm_func):
    """Allows setting a custom algorithm implementation."""
    self._algorithm_implementation = algorithm_func
```

## Examples of Correct Pattern
```python
class SensitivityAnalyzer:
    """Performs sensitivity analysis using various sampling methods.
    
    This class implements a configurable sensitivity analysis that can be:
    1. Configured once with sampling parameters
    2. Applied to multiple datasets
    3. Produce consistent, comprehensive results
    """
    
    def __init__(self, method="Sobol", sample_count=1000, second_order=True):
        """Configure the sensitivity analysis.
        
        Args:
            method: Sampling method to use
            sample_count: Number of samples to generate
            second_order: Whether to compute second-order effects
        """
        self.method = method
        self.sample_count = sample_count
        self.second_order = second_order
        
    @classmethod
    def create_fast_analysis(cls):
        """Factory for quick approximate analysis."""
        return cls(method="FAST", sample_count=250, second_order=False)
        
    def generate_samples(self, variable_ranges):
        """Generate experimental design samples.
        
        Args:
            variable_ranges: Dict mapping variable names to (min, max) tuples
            
        Returns:
            df_samples: DataFrame with columns for each variable
        """
        # Implementation details
        
    def analyze_results(self, df_samples, df_results):
        """Calculate sensitivity indices.
        
        Args:
            df_samples: DataFrame with input samples
            df_results: DataFrame with corresponding results
            
        Returns:
            Dict containing sensitivity analysis results
        """
        # Implementation details
```
By following these principles, analytical classes will be more maintainable, reusable, and easier to integrate into larger systems.

# Deep Dive: DataTransformer
Use case: 
# Deep Dive: DataTransformer

## Current Implementation Review

The `DataTransformer` class transforms simulation data into "impact data" for diagnostic analysis. Its core responsibilities:

1. Filtering simulated data based on KPI value ranges
2. Creating reference data based on baseline values 
3. Computing deltas between filtered data and reference data

Looking at the implementation and its use in `run_diagnosis()`:

```python
# In run_diagnosis:
nfix_summary = pd.concat([nfix_samples, renamed_responses], axis=1)
await self._update_dt(atlas=atlas)
impact_samples, impact_responses = self.dt.transform_data(summary=nfix_summary)
```

### Issues with Current Implementation

1. **Violates Configuration/Data Separation**
   - Mixes configuration parameters with data in constructor
   - Stores input data as state (`self.input_var_val`, `self.output_var_val`)
   - Persists computation results (`self.filtered_results`)

2. **Complex State Management**
   - Requires two-phase initialization (constructor + `_update_dt`)
   - Maintains partial state across method calls
   - Exposes implementation details via public properties

3. **Limited Reusability**
   - Tightly coupled to specific diagnostic workflow
   - Class structure adds complexity without significant benefits
   - Difficult to test in isolation due to state dependencies

## Refactoring Strategy 1: Functional Approach

Transform into stateless functions that follow our principles:

```python
def filter_data_by_output_range(
    df: pd.DataFrame, 
    output_col: str,
    output_range: Tuple[float, float],
    bound_expansion_factor: float = 0.10,
    max_expansions: int = 10
) -> Tuple[pd.DataFrame, bool]:
    """Filter data based on output variable range with adaptive bounds."""
    # Implementation

def compute_impact_values(
    filtered_data: pd.DataFrame,
    reference_point: pd.DataFrame,
    input_cols: List[str],
    output_cols: List[str]
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Compute delta between filtered data and reference point."""
    # Implementation

# Usage in workflow:
filtered_data, out_of_range = filter_data_by_output_range(
    summary_df, 
    output_col=output_var_names[0],
    output_range=output_range
)

impact_samples, impact_responses = compute_impact_values(
    filtered_data,
    reference_point=current_input_vals,
    input_cols=input_var_names,
    output_cols=output_var_names
)
```

Benefits:
- Clear separation of concerns
- No state management issues
- Easier to test and reason about
- Functions can be composed for complex transformations

## Refactoring Strategy 2: Redesigned Class with Factory Methods

Redesign as a properly configured analysis tool:

```python
class DiagnosticDataTransformer:
    """Transforms simulation data into diagnostic impact data."""
    
    def __init__(
        self,
        output_var_range: Tuple[float, float],
        bound_expansion_factor: float = 0.10,
        max_expansions: int = 10
    ):
        """Initialize with configuration parameters only."""
        self.output_range = output_var_range
        self.bound_expansion_factor = bound_expansion_factor
        self.max_expansions = max_expansions
    
    @classmethod
    def create_default_config(cls) -> 'DiagnosticDataTransformer':
        """Factory for default configuration."""
        return cls(output_var_range=(0.0, 100.0))
    
    @classmethod
    def create_precise_config(cls) -> 'DiagnosticDataTransformer':
        """Factory for precise configuration."""
        return cls(output_var_range=(0.0, 100.0), 
                   bound_expansion_factor=0.05,
                   max_expansions=20)
    
    def transform_data(
        self,
        data: pd.DataFrame,
        reference_point: pd.DataFrame,
        output_col: str,
        input_cols: List[str]
    ) -> DiagnosticResult:
        """Transform data into diagnostic impact values."""
        # Implementation that returns a proper result object
        # No state is stored between calls
```

Benefits:
- Configuration separated from data
- No persisted state between method calls  
- Factory methods provide common configurations
- Returns complete result objects

## Recommendation

The functional approach (Strategy 1) is recommended because:

1. The transformation is fundamentally a data pipeline operation
2. There's no need for persistent state between operations
3. Functions are easier to test and compose
4. The current class doesn't provide enough value to justify its complexity

This approach aligns with our design principles while simplifying the code and improving maintainability.


## V3
# Deep Dive: Unified Data Operations with DatasetEngine

## Overlapping Responsibilities in Current Design

Analyzing the existing `SummaryResults` and `DataTransformer` classes reveals significant overlap in their core functionality:

### SummaryResults Features
- Stores input samples and output responses
- Sorts and filters data by KPI values
- Computes statistical summaries (mean, std)
- Applies thresholds for filtering
- Manages feature importance analysis results

### DataTransformer Features
- Stores reference input/output data
- Filters data based on output ranges
- Applies adaptive bounds expansion
- Computes deltas between filtered data and references
- Generates transformed "impact" datasets

Both classes share a common pattern of taking DataFrame inputs, applying transformations, and generating statistical outputs - but both violate our design principles by mixing configuration with data storage.

## Unified DatasetEngine Design

A better approach is to create a unified `DatasetEngine` that handles all DataFrame operations while maintaining proper configuration/data separation:

```
+-----------------------------------------------------+
|                   DatasetEngine                     |
+-----------------------------------------------------+
| Configuration Parameters:                           |
| - precision: int                                    |
| - significance_threshold: float                     |
| - kpi_threshold: float                              |
| - bound_expansion_factor: float                     |
| - max_expansions: int                               |
+-----------------------------------------------------+
| Data Operations:                                    |
| + filter_by_range()                                 |
| + filter_by_threshold()                             |
| + compute_statistics()                              |
| + compute_impact()                                  |
| + analyze_feature_importance()                      |
| + summarize_importance()                            |
| + get_top_contributors()                            |
+-----------------------------------------------------+
| Factory Methods:                                    |
| :: create_default_config()                          |
| :: create_detailed_analysis_config()                |
| :: create_diagnostic_config()                       |
+-----------------------------------------------------+
```

## Implementation Sketch

```python
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Union
import pandas as pd
import numpy as np

@dataclass
class FeatureImportanceResult:
    """Container for feature importance analysis results."""
    importance_df: pd.DataFrame
    selected_features: List[str]
    summary_importance: Dict[str, float]
    sorted_data: pd.DataFrame

@dataclass
class ImpactAnalysisResult:
    """Container for impact analysis results."""
    impact_samples: pd.DataFrame
    impact_responses: pd.DataFrame
    filtered_data: pd.DataFrame
    out_of_range: bool
    statistics: Dict[str, pd.DataFrame]

class DatasetEngine:
    """
    Unified engine for dataset operations including filtering, transformation, 
    impact analysis, and feature importance.
    
    This class follows the principle of configuration-as-state and 
    data-as-parameters, allowing the same configured engine to be reused
    across multiple datasets.
    """
    
    def __init__(
        self,
        precision: int = 3,
        significance_threshold: float = 0.0,
        kpi_threshold: float = 95.0,
        bound_expansion_factor: float = 0.10,
        max_expansions: int = 10
    ):
        """Initialize with configuration parameters only."""
        self.precision = precision
        self.significance_threshold = significance_threshold
        self.kpi_threshold = kpi_threshold
        self.bound_expansion_factor = bound_expansion_factor
        self.max_expansions = max_expansions
        
    @classmethod
    def create_default_config(cls) -> 'DatasetEngine':
        """Factory for default configuration."""
        return cls()
    
    @classmethod
    def create_diagnostic_config(cls) -> 'DatasetEngine':
        """Factory for diagnostic configuration."""
        return cls(kpi_threshold=0.0, significance_threshold=5.0)
        
    def filter_by_range(
        self,
        df: pd.DataFrame,
        column: str,
        value_range: Tuple[float, float],
        adaptive: bool = True
    ) -> Tuple[pd.DataFrame, bool]:
        """Filter dataframe by value range with optional adaptive bounds."""
        # Implementation logic from DataTransformer._filter_data
        
    def compute_statistics(
        self,
        df: pd.DataFrame,
        columns: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """Compute descriptive statistics for dataframe."""
        # Implementation logic from SummaryResults._get_result_statistics
        
    def compute_impact(
        self,
        data: pd.DataFrame,
        reference_point: pd.DataFrame,
        input_cols: List[str],
        output_cols: List[str]
    ) -> ImpactAnalysisResult:
        """Compute delta between data and reference point."""
        # Combined logic from DataTransformer.transform_data
        
    def analyze_feature_importance(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        var_names: List[str],
        importance_values: List[float]
    ) -> FeatureImportanceResult:
        """Process feature importance results."""
        # Implementation logic from SummaryResults
        
    def get_filtered_statistics(
        self,
        df: pd.DataFrame,
        output_col: str,
        variables: List[str],
    ) -> Dict[str, Dict[str, float]]:
        """Get statistical summaries for filtered data."""
        # Implementation logic from SummaryResults._get_summary_statistics
```

## Usage in Workflow

```python
# Initialize with configurations at service startup
default_engine = DatasetEngine.create_default_config()
diagnostic_engine = DatasetEngine.create_diagnostic_config()

# In run_experiment:
# 1. Filter and analyze data
filtered_df, out_of_range = default_engine.filter_by_range(
    df=summary_df, 
    column=output_var_names[0],
    value_range=(0, 100)
)

# 2. Compute statistics
stats_df = default_engine.compute_statistics(filtered_df)

# 3. Process feature importance
importance_results = default_engine.analyze_feature_importance(
    X=samples, 
    y=responses,
    var_names=input_var_names,
    importance_values=percent_mean_importance
)

# In run_diagnosis:
# 1. Compute impact analysis
impact_results = diagnostic_engine.compute_impact(
    data=summary_df,
    reference_point=current_input_vals,
    input_cols=input_var_names,
    output_cols=output_var_names
)

# 2. Analyze impacted features
importance_results = diagnostic_engine.analyze_feature_importance(
    X=impact_results.impact_samples, 
    y=impact_results.impact_responses,
    var_names=input_var_names,
    importance_values=percent_mean_importance
)
```

## Benefits of Unified Approach

1. **Clean Separation of Configuration and Data**: All data is passed as parameters, never stored in instance state
2. **Reusable Components**: The same engine can be applied to multiple datasets
3. **Simplified Workflow**: Clear, consistent API for all data operations
4. **Better Testability**: Functions with well-defined inputs and outputs
5. **Reduced Code Duplication**: Common operations implemented once
6. **Proper Return Types**: Complete result objects instead of tuple unpacking

This approach transforms the data manipulation logic from stateful, complex classes into a streamlined, functional data processing engine that follows our design principles.