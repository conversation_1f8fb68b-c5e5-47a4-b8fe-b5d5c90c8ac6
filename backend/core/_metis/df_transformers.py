from __future__ import  annotations

from backend.core._metis._imports import <PERSON><PERSON>, pd
from backend.core._sharedutils.data_precision import pd
from ._imports import *


#############################

# DATA PROCESSING

@dataclass
class DataTransformDTO:
    """
    Container for diagnostic data transformation results.

    This dataclass encapsulates the outputs of the data transformation process:
    - Filtered input variables expressed as deviations from baseline
    - Filtered KPI values expressed as deviations from baseline
    - Status information about the filtering process

    Attributes:
        df_transformed_samples: DataFrame containing deltas of input variables from baseline
        df_transformed_responses: DataFrame containing deltas of KPI values from baseline
        ax0_selection_mask: Index of selected rows from original dataframes
        out_of_range: Flag indicating if KPI ranges were outside available data (True if filtering failed)
        logs: Optional dictionary containing processing logs and additional metadata
    """
    df_transformed_samples: pd.DataFrame
    df_transformed_responses: pd.DataFrame
    index_selection: pd.Index
    out_of_range: bool
    logs: Optional[Dict] = None
    

#############################


class BaseDataTransformer(ABC):
    """
    Abstract base class for data processors that transform simulation data.

    All data processors must implement the process method with the required
    parameters, but can add additional keyword-only parameters as needed.
    """
    def filter_indices_in_hypercube(
        self,
        points: np.ndarray,
        lower_bounds: np.ndarray,
        upper_bounds: np.ndarray,
        source_index: pd.Index
    ) -> pd.Index:
        """
        Find points that lie within an N-dimensional hyperrectangle using
        vectorized geometric operations.

        Args:
            points: Array of points (each point is a vector of coordinates)
                Example shape: (1000, 3) for 1000 points in 3D space
                Example data: [[1.2, 3.4, 0.5], [2.1, 1.8, 3.2], ...]
            lower_bounds: Lower bounds of the hyperrectangle for each dimension
                Example shape: (3,) for 3D bounds
                Example data: [1.0, 2.0, 0.0]
            upper_bounds: Upper bounds of the hyperrectangle for each dimension
                Example shape: (3,) for 3D bounds
                Example data: [3.0, 4.0, 5.0]
            source_index: Index from the original DataFrame to maintain referential integrity
                Example: pd.Index([100, 101, 102, ...])

        Returns:
            Index of rows from the original DataFrame that fall within the N-dimensional shape
                Example: pd.Index([100, 105, 108, ...]) if points at indices 0, 5, 8, ... are inside
        """
        # Check if each point lies within the hyperrectangle (true geometric containment)
        # A point is inside if it's >= lower_bounds AND <= upper_bounds in ALL dimensions
        inside_mask = np.all((points >= lower_bounds) & (points <= upper_bounds), axis=1)

        # Return the actual indices rather than a boolean mask
        return source_index[inside_mask]

    @abstractmethod
    def transform(
        self,
        samples_df: pd.DataFrame,
        response_df: pd.DataFrame,
        **kwargs
    ) -> DataTransformDTO:
        """
        Process simulation data according to implementation-specific rules.

        Args:
            samples_df: DataFrame containing input variables
            samples_baseline: Dictionary mapping column names to baseline values
            response_df: DataFrame containing KPI values with columns as different KPIs
            response_baseline: Dictionary mapping response column names to baseline values
            **kwargs: Additional keyword arguments specific to each implementation

        Returns:
            DataTransformResult containing processed data and metadata
        """
        pass


class z_DiagnosticDataTransformer(BaseDataTransformer, ConfigBase):
    """
    Processes simulation data into diagnostic insights by filtering and computing deviations.

    This class handles filtering of data based on output variable ranges
    and computation of deltas from reference points. It follows the
    configuration-as-state principle, where only configuration parameters
    are stored in the instance.

    Supports both single-response and multi-response scenarios, with N-dimensional
    hyperrectangle filtering for the latter.
    """

    def __init__(
        self,
        bound_expansion_factor: float = 0.10,
        max_expansions: int = 10,
        precision: int = 3
    ):
        """
        Initialize with configuration parameters only.

        Args:
            bound_expansion_factor: Factor to expand bounds if no results found
            max_expansions: Maximum number of bound expansions to attempt
            precision: Decimal precision for output values
        """
        self.bound_expansion_factor = bound_expansion_factor
        self.max_expansions = max_expansions
        self.precision = precision

    @classmethod
    def create_dev_config(cls) -> 'z_DiagnosticDataTransformer':
        """Create a development environment configuration with more aggressive bound expansion."""
        return cls(bound_expansion_factor=0.15, max_expansions=5)

    @classmethod
    def create_production_config(cls) -> 'z_DiagnosticDataTransformer':
        """Create a production environment configuration with conservative bound expansion."""
        return cls(bound_expansion_factor=0.05, max_expansions=15)

    def _filter_data(
        self,
        samples_df: pd.DataFrame,
        response_df: pd.DataFrame,
        response_bounds:Dict[str, Tuple[float, float]]
    ) -> Tuple[pd.DataFrame, pd.DataFrame, bool, pd.Index]:
        """
        Filter samples and response data based on KPI value ranges using hyperrectangle filtering.

        Args:
            samples_df: DataFrame containing input variables
            response_df: DataFrame containing KPI values with columns as different KPIs
            kpi_bounds: Dictionary mapping response column names to (min, max) bounds
                        If None, uses min/max of data for each response

        Returns:
            Tuple of:
            - filtered_samples: DataFrame of filtered sample results
            - filtered_responses: DataFrame of filtered response results
            - out_of_range: Boolean flag indicating if any specified range is violated
            - matching_indices: Index of selected rows from original dataframes
        """

        # DEFENSIVE

        # Validate that all specified KPIs exist in the response_df
        missing_kpis = [kpi for kpi in response_bounds.keys() if kpi not in response_df.columns]
        if missing_kpis:
            raise ValueError(f"KPI bounds specified for non-existent responses: {missing_kpis}")

        # Check if all bounds are outside data range
        min_outputs = response_df[list(response_bounds.keys())].min().to_dict()
        max_outputs = response_df[list(response_bounds.keys())].max().to_dict()

        all_out_of_range = all(
            max_outputs[kpi] <= bounds[0] or min_outputs[kpi] >= bounds[1]
            for kpi, bounds in response_bounds.items()
        )
        if all_out_of_range:
            logging.debug(f"All specified output ranges are outside data bounds")
            return samples_df.copy(), response_df.copy(), True, pd.Index([])

        # LOGIC

        # Prep
        response_array = response_df[list(response_bounds.keys())].values
        lower_bounds = np.array([bounds[0] for bounds in response_bounds.values()])
        upper_bounds = np.array([bounds[1] for bounds in response_bounds.values()])

        # Filter - now passing the original index for reference
        matching_indices: pd.Index = self.filter_indices_in_hypercube(
            response_array, lower_bounds, upper_bounds, response_df.index
        )

        # Bounds expansion if needed
        if len(matching_indices) == 0:
            logging.warning(
                f"The filtered data for the specified bounds is empty. Attempting bound expansion."
            )

            change_iter = 0
            expansion_factor = self.bound_expansion_factor

            while len(matching_indices) == 0 and change_iter < self.max_expansions:
                # Calculate new bounds directly with the expansion factor
                lower_bounds_expanded = lower_bounds * (1 - expansion_factor * (change_iter + 1))
                upper_bounds_expanded = upper_bounds * (1 + expansion_factor * (change_iter + 1))

                matching_indices = self.filter_indices_in_hypercube(
                    response_array, lower_bounds_expanded, upper_bounds_expanded, response_df.index
                )
                change_iter += 1

            logging.debug(
                f"After {change_iter} iterations of bound expansion, filtered data has {len(matching_indices)} valid points."
            )

        # Fail case
        if len(matching_indices) == 0:
            logging.warning(f"Could not find any points within bounds even after expansion")
            return samples_df.copy(), response_df.copy(), True, pd.Index([])

        # Filter dataframes based on indices - using .loc for index-based selection
        filtered_samples = samples_df.loc[matching_indices].copy()
        filtered_responses = response_df.loc[matching_indices].copy()

        return filtered_samples, filtered_responses, False, matching_indices

    def transform(
        self,
        samples_df: pd.DataFrame,
        response_df: pd.DataFrame,
        *,
        samples_baseline: Dict[str, float],
        kpi_baseline: Dict[str, float],
        kpi_range: Dict[str, Tuple[float, float]] = {},
        **kwargs
    ) -> DataTransformDTO:
        """
        Transform simulation data by filtering based on KPI ranges and computing deviations from baseline values.

        The transformation process consists of two main steps:
        1. Filter data points where KPI values fall within specified ranges
        2. Calculate deviations (deltas) between filtered data and baseline values

        Args:
            samples_df: DataFrame containing original simulation input variables
            samples_baseline: Dictionary mapping input column names to reference/baseline values
            response_df: DataFrame containing original simulation KPI values (columns=different KPIs)
            kpi_baseline: Dictionary mapping KPI names to reference/baseline values
            kpi_range: Dictionary mapping KPI names to (min, max) bounds for filtering
            **kwargs: Additional arguments (ignored in this implementation)

        Returns:
            DataTransformDTO containing:
            - df_transformed_samples: filtered input values expressed as deltas from baseline
            - df_transformed_responses: filtered KPI values expressed as deltas from baseline
            - ax0_selection_mask: Index of selected rows from original dataframes
            - out_of_range: whether specified KPI ranges were outside available data
            - logs: optional processing metadata
        """
        # Validate inputs
        if not set(samples_baseline.keys()).issubset(set(samples_df.columns)):
            missing_cols = set(samples_baseline.keys()) - set(samples_df.columns)
            raise ValueError(f"Baseline contains keys not found in samples: {missing_cols}")

        if not set(kpi_baseline.keys()).issubset(set(response_df.columns)):
            missing_cols = set(kpi_baseline.keys()) - set(response_df.columns)
            raise ValueError(f"Baseline contains keys not found in responses: {missing_cols}")

        # Filter
        sample_df_filtered, response_df_filtered, out_of_range, matching_indices = self._filter_data(
            samples_df, response_df, kpi_range
        )

        # Calculate impact as difference from baseline
        samples_baseline_df = pd.DataFrame(
            {col: [samples_baseline.get(col, 0.0)] * len(sample_df_filtered)
             for col in sample_df_filtered.columns},
            index=sample_df_filtered.index
        )
        impact_samples = sample_df_filtered - samples_baseline_df

        # Calculate response impact for each response column
        response_baseline_df = pd.DataFrame(
            {col: [kpi_baseline.get(col, 0.0)] * len(response_df_filtered)
             for col in response_df_filtered.columns},
            index=response_df_filtered.index
        )
        impact_responses = response_df_filtered - response_baseline_df

        return DataTransformDTO(
            df_transformed_samples=impact_samples,
            df_transformed_responses=impact_responses,
            index_selection=matching_indices,
            out_of_range=out_of_range
        )


#############################

class DataframeFilters:
    """
    Static methods to do filtering for DFs
    """
    @staticmethod
    def _filter_indices_in_hypercube(
        points: np.ndarray,
        lower_bounds: np.ndarray,
        upper_bounds: np.ndarray,
        source_index: pd.Index
    ) -> pd.Index:
        """
        Find points that lie within an N-dimensional hyperrectangle using
        vectorized geometric operations.

        Args:
            points: Array of points (each point is a vector of coordinates)
            lower_bounds: Lower bounds of the hyperrectangle for each dimension
            upper_bounds: Upper bounds of the hyperrectangle for each dimension
            source_index: Index from the original DataFrame to maintain referential integrity

        Returns:
            Index of rows from the original DataFrame that fall within the N-dimensional shape
        """
        # Check if each point lies within the hyperrectangle (true geometric containment)
        # A point is inside if it's >= lower_bounds AND <= upper_bounds in ALL dimensions
        inside_mask = np.all((points >= lower_bounds) & (points <= upper_bounds), axis=1)

        # Return the actual indices rather than a boolean mask
        return source_index[inside_mask]
    
    @staticmethod
    def filter_df_by_dynamic_bounds(
        df: pd.DataFrame,
        column_bounds: Dict[str, Tuple[float, float]],
        *,
        expansion_factor: float =0.1,
        min_hits: int = 10,
        max_expansions: int = 25,
    ) ->Tuple[ pd.DataFrame, bool ]: 
        """
        Filter DataFrame to keep only rows where column values are within the specified bounds.
        If fewer than min_hits rows match, gradually expand the bounds up to max_expansions times.

        Args:
            df: DataFrame to filter
            column_bounds: Dictionary mapping column names to (min, max) bounds
            expansion_factor: Factor to expand bounds if min_hits not reached
            min_hits: Minimum number of matching rows required
            max_expansions: Maximum number of bound expansions to attempt

        Returns:
            Tuple of:
            - filtered_df: DataFrame with only rows where values are within bounds
            - bounds_expanded: Boolean indicating if bound expansion was used
        """

        # DEFENSIVE
        missing_cols = [col for col in column_bounds.keys() if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Column bounds specified for non-existent columns: {missing_cols}")

        # Prepare arrays for vectorized filtering
        columns_to_filter = list(column_bounds.keys())
        data_array = df[columns_to_filter].values
        lower_bounds = np.array([bounds[0] for bounds in column_bounds.values()])
        upper_bounds = np.array([bounds[1] for bounds in column_bounds.values()])
        
        # DEFENSIVE
        min_values = df[columns_to_filter].min().to_dict()
        max_values = df[columns_to_filter].max().to_dict()
        all_out_of_range = all(
            max_values[col] <= bounds[0] or min_values[col] >= bounds[1]
            for col, bounds in column_bounds.items()
        )
        if all_out_of_range:
            raise ValueError("No valid samples found within the specified bounds. All filter constraints are outside the available data range.")

        # Initial filtering
        matching_indices = DataframeFilters._filter_indices_in_hypercube(
            data_array, lower_bounds, upper_bounds, df.index
        )
        
        # If not enough hits, try expanding bounds
        bounds_expanded = False
        if len(matching_indices) < min_hits:
            bounds_expanded = True
            expansion_iter = 0
            
            while len(matching_indices) < min_hits and expansion_iter < max_expansions:
                # Calculate expanded bounds
                current_expansion = expansion_factor * (expansion_iter + 1)
                lower_bounds_expanded = lower_bounds * (1 - current_expansion)
                upper_bounds_expanded = upper_bounds * (1 + current_expansion)
                
                matching_indices = DataframeFilters._filter_indices_in_hypercube(
                    data_array, lower_bounds_expanded, upper_bounds_expanded, df.index
                )
                expansion_iter += 1
        
        # Return filtered DataFrame
        return df.loc[matching_indices].copy(), bounds_expanded

    @staticmethod
    def filter_df_by_bounds(
        df: pd.DataFrame,
        column: str,
        bounds: Tuple[Optional[float], Optional[float]]
    ) -> pd.DataFrame:
        """
        Filter DataFrame to keep only rows where column values are within the specified bounds.

        Uses an explicit approach by first creating a Series with all False values,
        then setting True for rows that meet our criteria.

        Args:
            df: DataFrame to filter
            column: Name of the column to filter on
            bounds: Tuple of (lower_bound, upper_bound), either can be None

        Returns:
            DataFrame with only rows where column values are within bounds
        """
        # Validate column exists in dataframe
        if column not in df.columns:
            raise KeyError(f"Column '{column}' not found in DataFrame")

        # Get bound values with sensible defaults if None
        raw_lower, raw_upper = bounds
        lower_bound = raw_lower if raw_lower is not None else df[column].min() - 1
        upper_bound = raw_upper if raw_upper is not None else df[column].max() + 1

        # Explicitly create mask starting with all False, then set True for rows to keep
        # This preserves the index from the original DataFrame
        include_mask = pd.Series(False, index=df.index)
        include_mask = include_mask | ((df[column] >= lower_bound) & (df[column] <= upper_bound))

        # Using .copy() creates a completely independent DataFrame
        # Without .copy(), df.loc[] returns a view that shares memory with the original
        return df.loc[include_mask].copy()

    @staticmethod
    def filter_df_by_index(
            df: pd.DataFrame,
            index: pd.Index
    ) -> pd.DataFrame:
        """
        Filter DataFrame to only include rows whose indices are in the provided index.

        Index may be a superset of df.index, meaning some indices might not exist in the DataFrame.
        This method safely handles that case by returning only rows where indices are present 
        in both the provided index and the DataFrame.

        Args:
            df: DataFrame to filter
            index: Index of rows to keep

        Returns:
            DataFrame with only the rows whose indices exist in both df and the provided index
        """
        common_indices = df.index & index # type: ignore

        # Return filtered DataFrame using .loc with the common indices
        return df.loc[common_indices].copy()