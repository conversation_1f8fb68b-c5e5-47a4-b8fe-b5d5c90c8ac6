import logging
import copy
import pandas as pd
import numpy as np
import asyncio

from typing import List, Tuple, Dict, Union, Optional

import backend.infrastructure._db.repo as re
import backend.application.example_repo as eg
import backend.core._atlas.aggregates as at
import backend.core._matrix.matrix_dwsim as ma
from backend.core._metis import z_SG_review_SurrogateAtlas
from backend.core._metis.z_ProblemSpecification import z_ProblemSpecification
from backend.core._metis.z_FeatureEliminator import z_FeatureEliminator
from backend.core._metis.z_DataTransformer import z_DataTransformer
from backend.core._metis.z_SummaryResults import SummaryResults
from backend.core._athena.DiagnosticMap import Diagnostics

from backend.infrastructure._orchaestration.flows.SimulationChunked import (
    SimulationChunked,
)
from backend.infrastructure._db.dto import AtlasJSONSerializer

from sklearn.inspection import permutation_importance

from backend.infrastructure._db.repo import FolderRepoForDWSim

# [ ] - create a repo
repo = re.z_InProcessRepoForAtlas()


#######################
# EXCEPTIONS
class ConfigNotFound(Exception):
    pass


class ModelError(Exception):
    pass


class SimulationFailed(Exception):
    pass


#######################
dwsim_repo = FolderRepoForDWSim("backend/artefacts/bin/dwsim/", "simulation_tests")


class WorkflowManager:
    def __init__(
        self,
        wf_name: str,
        wf_type: str,
        wf_sim_id: str,
        problem_specification: z_ProblemSpecification,
        feature_eliminator: z_FeatureEliminator,
        data_transformation: Union[z_DataTransformer, None],
        atlas: Optional[at.AtlasRoot] = None,
    ):
        """
        This object is instantiated to execute the selected workflow.

        Args:
            wf_name: Name of the object generated.
            wf_type: Type of the workflow.
            wf_sim_id: Simulation ID to be used in workflow.
            ps_config: Configuration for the problem specification.
            fe_config: Configuration for the feature elimination.
            dt_config: Configuration for the data transformation.
        """
        # Check if the optional data transformation is specified for the troubleshooting.
        self.name: str = wf_name
        self.type: str = wf_type
        self.sim_id: str = wf_sim_id
        self.ps: z_ProblemSpecification = problem_specification
        self.fe: z_FeatureEliminator = feature_eliminator
        self.dt: Optional[z_DataTransformer] = data_transformation
        self._atlas = atlas
        # Check specification of the data transformation object.
        if wf_type == "troubleshoot" and data_transformation == None:
            raise AttributeError(
                "data_transformation: Expected valid attribute and recieved None!"
            )

    async def run_experiment(
        self, atlas: Optional[at.AtlasRoot] = None
    ) -> SummaryResults:
        """
        This method is called on the workflow object to run experiment and generate results.

        Args:

        Returns:
            smr_exp_results (object): An instance of SummaryExpResults class.
        """
        # Get atlas object
        atlas = self._atlas or self.z_get_atlas(config_name=self.sim_id)

        # Generate samples, renamed samples, renamed responses, variable map.
        samples, renamed_samples, renamed_responses, in_var_name_map, kpi_name_map = (
            await self._perform_simulations(atlas)
        )

        # Get the uids of non-fixed variable pretty names from the var_name_map for renaming.
        rename_nfix_var = [
            in_var_name_map.get(key, None)
            for key in list(in_var_name_map.keys())
            if key in self.ps.input_var_names
        ]
        logging.info(f"UIDs for non-fixed variables are obtained: {rename_nfix_var}.")

        # Choose non-fixed variable from renamed samples to be used for feature elimination. Note renamed_samples has both fixed and non-fixed variables.
        renamed_nfix_samples = renamed_samples.loc[:, rename_nfix_var]
        logging.info(
            f"Using dataframe containing non-fixed variables samples for feature elimination of size {renamed_nfix_samples.shape}."
        )

        # Selected important features
        selected_features, percent_mean_importance = self._perform_feature_elimination(
            samples=renamed_nfix_samples, responses=renamed_responses
        )

        # Generate summary results
        smr_exp_results = SummaryResults(
            input_var_names=self.ps.input_var_names,
            output_var_names=self.ps.output_var_names,
            samples=samples,
            responses=renamed_responses,
            selected_features=selected_features,
            percent_mean_importance=percent_mean_importance,
            var_contribution_threshold=0.0,
            kpi_threshold=95.0,
        )

        # Generate experiment summary results
        smr_exp_results.build_exp_results
        

        return smr_exp_results

    async def run_diagnosis(self):
        """
        This method is called on the workflow object to run diagnosis and generate results.

        Args:

        Returns:
            smr_dgn_results (object): An instance of SummaryDgnResults class.
        """
        # Get atlas object
        atlas = self._atlas or self.z_get_atlas(config_name=self.sim_id)

        # Generate samples, renamed samples, renamed responses, variable map.
        samples, renamed_samples, renamed_responses, in_var_name_map, kpi_name_map = (
            await self._perform_simulations(atlas)
        )

        # Choose non-fixed variable from samples to be used for data transformation. Note samples has both fixed and non-fixed variables.
        nfix_samples = samples.loc[:, self.ps.input_var_names]
        logging.info(
            f"Using dataframe containing non-fixed variables samples for data transformation of size {nfix_samples.shape}."
        )

        # Combine the simulated data for building Data Transformer
        nfix_summary = pd.concat([nfix_samples, renamed_responses], axis=1)
        logging.info(
            f"Successfully built dataframe by combining renamed samples and renamed responses."
        )

        # Update data transformation object
        await self._update_dt(atlas=atlas)
        logging.info(f"Updated Data Transformation object successfully.")

        # Generate transformed data
        impact_samples, impact_responses = self.dt.transform_data(summary=nfix_summary)
        logging.info(
            f"Obtained impact samples dataframe of size {impact_samples.shape} and impact responses dataframe of size {impact_responses.shape}."
        )

        # Perform important feature selection
        selected_features, percent_mean_importance = self._perform_feature_elimination(
            samples=impact_samples, responses=impact_responses
        )
        logging.info(
            f"Feature selection using the specified impact samples and responses is finished - selected features: {selected_features} and percent mean importance: {percent_mean_importance}."
        )

        # Generate summary results object using all the simulated data
        smr_trbl_results = SummaryResults(
            input_var_names=self.ps.input_var_names,
            output_var_names=self.ps.output_var_names,
            samples=nfix_samples,
            responses=renamed_responses,
            selected_features=selected_features,
            percent_mean_importance=percent_mean_importance,
            var_contribution_threshold=5.0,
            kpi_threshold=0.0,
        )

        # Generate summary results object using impact data
        smr_trbl_impact_results = SummaryResults(
            input_var_names=self.ps.input_var_names,
            output_var_names=self.ps.output_var_names,
            samples=impact_samples,
            responses=impact_responses,
            selected_features=selected_features,
            percent_mean_importance=percent_mean_importance,
            var_contribution_threshold=5.0,
            kpi_threshold=0.0,
        )

        # Execute summary results object
        smr_trbl_results.build_exp_results

        # Execute impact summary results object
        smr_trbl_impact_results.build_exp_results

        # Execute generation of filtered summary results for imapct summary object
        smr_trbl_impact_results.get_dgn_var_statistics(
            filtered_data=self.dt.filtered_results
        )

        return atlas, smr_trbl_results, smr_trbl_impact_results

    async def _update_dt(self, atlas: at.AtlasRoot):
        """
        Generate output variable value using the atlas and update DataTransformer object.

        Args:
            atlas (object): The atlas object that can run and return simulated data.

        Returns:

        """
        if self.dt != None:
            if self.dt.output_var_val.empty:
                # Convert samples to Atlas compatible samples by converting pretty names to uid names
                all_in_var_names = (
                    self.ps.input_var_names + self.ps.fixed_input_var_names
                )
                logging.info(
                    f"Pretty variable names {all_in_var_names} are passed for uid generation."
                )
                all_in_var_name_uids = self._convert_in_var_names_to_uidstr(
                    in_var_names=all_in_var_names, atlas=atlas
                )

                # Replace variable names of columns in samples dataframe with uids
                in_var_name_map = dict(zip(all_in_var_names, all_in_var_name_uids))
                renamed_samples = self.dt.input_var_val.rename(
                    mapper=in_var_name_map, axis=1
                )
                logging.info(
                    f"The pretty column names in samples dataframe are renamed with uids using map: {in_var_name_map}."
                )

                # Run simulations for the given current input sample to generate response
                kpi_name_map, renamed_responses = await self._run_simulation(
                    simulation_id=self.sim_id,
                    atlas=atlas,
                    samples=renamed_samples,
                    kpi_name=self.ps.output_var_names[0],
                )

                self.dt.output_var_val = copy.deepcopy(renamed_responses)
                self.dt.output_var_names = self.ps.output_var_names

                logging.info(
                    f"The curernt output variable value in data tranformation object is updated to {renamed_responses}!"
                )

            else:
                logging.warning(
                    f"The current output variable value in data transformation object will be updated with new value based on the configuration!"
                )
        else:
            logging.warning(
                f"The data transformation object is not instantitated. Instantiate before updating!"
            )

    @staticmethod
    def z_get_atlas(config_name: str) -> re.AtlasRoot:
        """
        Get the specified Atlas model from repo.

        Args:
            config_name (str): Name of the configuration.

        Returns:
            atlas (object): Atlas object representing semantic domain model.
        """
        key = repo.get_key_from_label(config_name)
        if key == None:
            atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
            logging.warning(
                f"The specified configuration {config_name} does not exist in repo. Defaulting to Industrial Natural Gas Boiler With Preheating Trains"
            )
            if atlas == None:
                raise ConfigNotFound("Plant configuration does not exist")
            return atlas
        else:
            model_w_metadata = repo.load(key)
            assert model_w_metadata is not None
            atlas = model_w_metadata.atlas_obj
            logging.info(
                f"Atlas object for the specified configuration {config_name} is loaded successfully."
            )
            return atlas

    @staticmethod
    def _convert_in_var_names_to_uidstr(
        in_var_names: List[str], atlas: at.AtlasRoot, delim: str = "_"
    ) -> List[str]:
        """
        Converts in_var_names to uids of variable, which comes fom EquipmentSpecifiction
        Args:
            in_var_names (list): A list of strings representing variable names. "Fuel Flow_Temperature" "Fuel Flow_MassFraciton Methane" Compound_Mix
            atlas (object): Instance of atlas object.
            delim (str): Deliminator to be used for splitting variable names.

        Returns:
            in_var_name_uid (list): A list of uid strings representing variable names.
        """
        remap = {}
        in_var_name_uids = []

        for name in in_var_names:
            entity_label, var_stringify = [term.strip() for term in name.split(delim)]
            var: Optional[at.VOBaseVariable] = None
            logging.info(
                f"The variable {name} has entity label: {entity_label} and variable ui label: {var_stringify}."
            )
            # Go through all the variables from the collection to get specific item
            for variable in atlas.variables_collection.items:
                if (
                    variable.parent.label == entity_label
                    # NOTE review below to be aware of gotcha for string keys
                    and var_stringify # Carbon dioxide <- comes from CompoundUUIDCollection
                    == atlas.variables_collection.get_ui_label(variable) # Carbon Dioxide <- comes from ui_label of VariableUUIDCollection.
                ):
                    var = variable
                    logging.info(f"Variable object is obtained successfully.")
                    break
            # If variable is not present in the collection then it will raise key error.
            if var is None:
                raise KeyError(
                    f"{name},eqp:`{entity_label}`,var:`{var_stringify}` not found in variables. Ensure the variable name is correctly specified."
                )

            # Build the list of uids corresponding to the pretty name.
            logging.info(f"The variable {name} has uid: {str(var.uid)}.")
            in_var_name_uids.append(str(var.uid))

        logging.info(
            f"The specified variable pretty names are converted to uids {in_var_name_uids}."
        )
        return in_var_name_uids

    @staticmethod
    async def _run_simulation(
        simulation_id: str,
        atlas: at.AtlasRoot,
        samples: pd.DataFrame,
        kpi_name: str,
    ) -> Tuple[Dict[str, str], pd.DataFrame]:
        """
        Execute simulation to generate data for the given samples and KPI.

        Args:
            simulation_id (str): A string representing name of the simulation id.
            atlas (object): An instance of atlas object for running simulation.
            samples (dataframe): A pandas dataframe containing samples.
            kpi_name (str): Name of the KPI.

        Returns:
            kpi_name_map (dict): A dictionary with kpi name and its uid.
            renamed_responses (dataframe): A dataframe containing KPI values.
        """
        # Set up simulation
        # matrix = ma.MatrixDWSim(simulation_id)
        # matrix.attach_model(atlas, share_model_state=True)
        # matrix.setup_simulation()
        # logging.info(
        #     f"Matrix simulation has been setup with the specified atlas model."
        # )

        # Get the KPI object
        kpi_uid = atlas.kpi_collection.get_uid(label=kpi_name)
        logging.info(f"Specified KPI: {kpi_name} has uid: {kpi_uid}.")
        kpi_object = atlas.kpi_collection.get_item(kpi_uid)
        logging.info(
            f"Successfully retrieved KPI object: {kpi_object.label} and will be used for response generation."
        )

        # Get KPI var name map
        kpi_name_map = dict(zip([kpi_name], [kpi_object.label]))

        # Run simulations to obtain KPI values
        # kpi_results = []
        # for row in range(samples.shape[0]):
        #     logging.info(f"Running simulation iteration: {row}.")
        #     atlas.deserialize_values(data=samples, row_index=row)
        #     matrix.run_simulation()
        #     logging.info(f"Successfully finished running simulation: {row}.")
        #     kpi_results.append(kpi_object.get_kpi_value())
        #     logging.info(
        #         f"KPI results are appended with latest results: {kpi_results}."
        #     )

        try:
            # TODO @ZL: Shift this into PrefectConfig
            chunk_size: int = 10
            chunks: List[Tuple[int, int]] = [
                (n * chunk_size, n * chunk_size + chunk_size)
                for n in range(len(samples) // chunk_size + 1)
            ]
            params_list = [
                dict(
                    simulation_id=simulation_id,
                    atlas_json=AtlasJSONSerializer.serialize(atlas),
                    kpi_name=kpi_name,
                    sample_dict=samples.iloc[left_index:right_index]
                    .reset_index(drop=True)
                    .to_dict(),
                )
                for left_index, right_index in chunks
            ]
            kpi_results = await SimulationChunked.run_deployments(
                params_list=params_list
            )
            # kpi_results = []
            print(f"KPI results = {kpi_results}")

        except Exception as e:
            error_msg = f"Failed to run simulations to experiment {kpi_name} for simulation {simulation_id}: {e}"
            logging.error(error_msg)
            raise SimulationFailed(error_msg)

        renamed_responses = pd.DataFrame(kpi_results, columns=[kpi_object.label])
        logging.info(
            f"Finished running all the simulation and obtained the responses dataframe of size: {renamed_responses.shape} with columns: {renamed_responses.columns}."
        )
        return kpi_name_map, renamed_responses

    def _generate_samples(self) -> pd.DataFrame:
        """
        Generate specified number of samples.

        Args:

        Returns:
            samples (dataframe): A dataframe of size (K [sample_count], N [input_dimensions]) of samples for function evaluation.
        """
        # Convert the samples array to dataframe
        samples = pd.DataFrame(
            self.ps.z_generate_samples, columns=self.ps.input_var_names
        )
        logging.info(
            f"The samples are generated based on the specified method {samples}."
        )

        return samples

    async def _perform_simulations(
        self,
        atlas: at.AtlasRoot,
    ) -> Tuple[
        pd.DataFrame, pd.DataFrame, pd.DataFrame, Dict[str, str], Dict[str, str]
    ]:
        """
        Generate experiment results by taking in the inputs.

        Args:
            atlas (object): The atlas object that can run and return simulated data.

        Returns:
            samples (dataframe): A dataframe of samples used for experiment with pretty name.
            renamed_samples (dataframe): A dataframe of samples used for experiment with uid name.
            renamed_responses (dataframe): A dataframe of kpis generated in experimemt.
            in_var_name_map (dict): A dictionary with in_var_name as key and in_var_name_uid as value.
            kpi_name_map (dict): A dictionary with kpi_name as key and kpi_name_uid as value.
        """
        # Generate samples from the problem spec.
        samples = self.ps.z_generate_samples
        logging.info(
            f"The samples dataframe of size {samples.shape} is generated successfully with column names {samples.columns}."
        )

        # Convert samples to Atlas compatible samples by converting pretty names to uid names
        all_in_var_names = self.ps.input_var_names + self.ps.fixed_input_var_names
        logging.info(
            f"Pretty variable names {all_in_var_names} are passed for uid generation."
        )
        all_in_var_name_uids = self._convert_in_var_names_to_uidstr(
            in_var_names=all_in_var_names, atlas=atlas
        )

        # Replace variable names of columns in samples dataframe with uids
        in_var_name_map = dict(zip(all_in_var_names, all_in_var_name_uids))
        renamed_samples = samples.rename(mapper=in_var_name_map, axis=1)
        logging.info(
            f"The pretty column names in samples dataframe are renamed with uids using map: {in_var_name_map}."
        )

        # Run simulations using the renamed samples to generate responses.
        kpi_name_map = {}

        kpi_name_map, renamed_responses = await self._run_simulation(
            simulation_id=self.sim_id,
            atlas=atlas,
            samples=renamed_samples,
            kpi_name=self.ps.output_var_names[0],
        )

        return (
            samples,
            renamed_samples,
            renamed_responses,
            in_var_name_map,
            kpi_name_map,
        )

    def _get_important_features(
        self, samples: pd.DataFrame, responses: pd.DataFrame
    ) -> Tuple[List[bool], List[float], List[float], List[float]]:
        """
        Using the given samples and responses, select important features and compute their importance.

        Args:
            samples (dataframe): A dataframe of samples.
            responses (dataframe): A dataframe of responses.

        Returns:
            selected_features (list): List of boolean indicating if a feature is important.
            mean_importance (list): List of floats indicating the average importance of selected features.
            std_importance (list): List of floats indicating the variance in importance of selected features.
            percent_mean_importance (list): List of floats indicating the percent average importance of selected features.
        """
        # Build feature elimination model and find selected features.
        self.fe.selector = self.fe.selector.fit(samples, responses)
        selected_features = list(self.fe.selector.support_)
        logging.info(
            f"The feature selector object is trained using the generated data. Following features are selected {selected_features}."
        )

        # Generate contribution.
        sample_selected = samples.loc[:, self.fe.selector.support_]
        self.fe.estimator.fit(sample_selected, responses)
        result = permutation_importance(
            self.fe.estimator,
            sample_selected,
            responses,
            n_repeats=10,
            random_state=42,
        )
        percent_mean_importance = [
            (
                ele * 100 / sum(result.importances_mean)
                if not pd.isna(ele * 100 / sum(result.importances_mean))
                else 0.0
            )
            for ele in result.importances_mean
        ]
        mean_importance = result.importances_mean
        std_importance = result.importances_std

        logging.info(
            f"The feature importance on the selected features have been computed. The mean and std importance of features are {mean_importance, std_importance}."
        )

        logging.info(
            f"The normalized feature importance on the selected features have been computed. The percent mean importance of features are {percent_mean_importance}."
        )

        return (
            selected_features,
            mean_importance,
            std_importance,
            percent_mean_importance,
        )

    def _perform_feature_elimination(
        self,
        samples: pd.DataFrame,
        responses: pd.DataFrame,
    ) -> Tuple[List[bool], List[float]]:
        """
        This helper method performs feeature elimination by taking sample data and kpis.

        Args:
            samples (dataframe): A dataframe of samples.
            responses (dataframe): A dataframe of responses.

        Returns:
            selected_variables (list): A list of bools showing selected variables.
            percent_mean_importance (list): A list containing percent contribution of the selected variables.
        """

        # Perform feature elimination analysis.
        selected_features, mean_importance, std_importance, percent_mean_importance = (
            self._get_important_features(samples=samples, responses=responses)
        )
        logging.info(
            f"Completed performing feature elimination - selected features {selected_features} and their importance {percent_mean_importance}."
        )

        return selected_features, percent_mean_importance
