from __future__ import  annotations
from ._imports import *
from backend.core._sharedutils.data_precision import *

from sklearn.feature_selection import RFE
from sklearn.ensemble import RandomF<PERSON>tRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearReg<PERSON>, <PERSON>, <PERSON><PERSON>
from sklearn.svm import SVR
from sklearn.inspection import permutation_importance

#############################

# ENUMS for column

@unique
class AnalyzerCols(Enum):
    """Base class for analysis column identifiers.
    """
    
    @classmethod
    def all_columns(cls) -> List[str]:
        """Get all column identifiers from this enum."""
        return [item.value for item in cls]

@unique
class AnalyserColEnums_FS(AnalyzerCols):
    """Feature sensitivity analysis column identifiers."""
    COL_LABEL_X = "column_label_x"  # Common identifier column for all analysis types
    SELECTED = "selected"
    IMPORTANCE_MEAN = "importance_mean"
    IMPORTANCE_STD = "importance_std"
    IMPORTANCE_MEAN_PERCENT = "importance_percent"

@unique
class AnalyzerCols_Stats(AnalyzerCols):
    """Correlation analysis column identifiers."""
    PEARSON = "corr_pearson"
    SPEARMAN = "corr_spearman"

#############################

# ANALYTICAL CLASSES

class BaseAnalyzer(ABC):

    def _add_rows_with_defaults(self, df: pd.DataFrame, row_indices: List[str]) -> pd.DataFrame:
        """
        Helper function to add new rows to a DataFrame with appropriate default values based on column dtypes.
        
        Args:
            df: Source DataFrame with existing structure and dtypes
            row_indices: List of indices for rows to be added
            
        Returns:
            DataFrame with new rows added using appropriate default values
        """
        if not row_indices:
            return df
            
        # Define default value strategies for different data types
        default_strategies = {
            'bool': False,
            'int': 0,
            'int64': 0,
            'float': 0.0, 
            'float64': 0.0,
            'object': None,
            'category': None,
            'datetime64': pd.NaT,
            'timedelta64': pd.NaT
        }
        
        # Create default values for each column based on its dtype
        default_values = {}
        for col in df.columns:
            dtype_name = str(df[col].dtype).lower()
            # Use the strategy if available, otherwise use fallback
            default_values[col] = default_strategies.get(dtype_name, None)
                
        # Create DataFrame with appropriate defaults
        new_rows = pd.DataFrame(
            {col: [val] * len(row_indices) for col, val in default_values.items()},
            index=row_indices
        )
        
        # Combine with original DataFrame
        return pd.concat([df, new_rows])

    @abstractmethod
    def analyse(self, x: pd.DataFrame, y: pd.DataFrame) -> pd.DataFrame:
        raise NotImplementedError()

class FeatureSensitivityAnalyzer(BaseAnalyzer, ConfigBase):
    """Configurable feature selection strategy.
    
    This class implements feature selection algorithms that can be:
    1. Configured once with hyperparameters
    2. Applied to multiple datasets
    3. Produce consistent, comprehensive results
    
    As a principle:
    The class initialization concerns itself with Configuration. 
    The methods concern themselves with data
    
    Example usage:
    # Create configuration at service startup
    default_selector = FeatureSelector.create_default()
    prod_selector = FeatureSelector(features_to_select=0.5, estimator_name="random_forest")
    """
    
    def __init__(
        self,
        estimator_name: str = "random_forest",
        features_to_select: float = 0.80,
        elimination_step: int = 1,
        n_repeats: int = 10,
        random_state: int = 42,
        kernel: str = "linear",
        precision: int = 3
    ):
        """Initialize selection strategy configuration.
        
        Args:
            estimator_name: ML model to use for selection
            features_to_select: Fraction of features to select
            elimination_step: Features to remove per iteration
            n_repeats: Permutation importance repetitions
            random_state: Random seed for reproducibility 
            kernel: Kernel for SVM (if applicable)
        """
        self.estimator_name = estimator_name
        self.features_to_select = features_to_select
        self.elimination_step = elimination_step
        self.n_repeats = n_repeats
        self.random_state = random_state
        self.kernel = kernel
        self.precision = precision
        
        logging.info(f"Configured feature selector with {estimator_name}, selecting {features_to_select*100:.1f}% of features")

    
    @classmethod
    def create_dev_config(cls) -> 'FeatureSensitivityAnalyzer':
        """Create a development environment configuration with faster analysis."""
        return cls(
            estimator_name="random_forest",  
            features_to_select=0.6,
            elimination_step=2,
            n_repeats=5,
        )
    
    @classmethod
    def create_production_config(cls) -> 'FeatureSensitivityAnalyzer':
        """Create a production environment configuration with more robust analysis."""
        return cls(
            estimator_name="random_forest",
            features_to_select=0.8,
            elimination_step=1,
            n_repeats=20,
        )
    
    def _get_estimator(self):
        """Create estimator instance based on configuration.
        
        Args:
            n_features: Used to calculate absolute feature count
        """
        # Same implementation as before, but using the parameters from self
        if self.estimator_name == "linear":
            return LinearRegression()
        elif self.estimator_name == "ridge":
            return Ridge()
        elif self.estimator_name == "lasso":
            return Lasso()
        elif self.estimator_name == "random_forest":
            return RandomForestRegressor(random_state=self.random_state)
        elif self.estimator_name == "gradient_boosting":
            return GradientBoostingRegressor(random_state=self.random_state)
        elif self.estimator_name == "support_vector":
            return SVR(kernel=self.kernel) #type: ignore
        else:
            return RandomForestRegressor(random_state=self.random_state)
    
    
    def analyse(self, x: pd.DataFrame, y: pd.DataFrame, *, include_unselected: bool = False) -> pd.DataFrame:
        """
        Analyze feature importance using RFE and permutation importance.
        
        Args:
            x: Feature dataframe with samples (rows) and features (columns)
            y: Target dataframe with response values (currently only uses first column)
            include_unselected: Whether to include unselected features in the results 
                When False, only returns selected features (more efficient for large feature sets)
            
        Returns:
            - DataFrame with features as rows and the metrics as columns. 
            - Column values defined by `FSColumns` Enums
            
            Results are always sorted by importance (descending).
        """
        # Convert y DataFrame to Series (using first column only)
        if y.shape[1] > 1:
            logging.warning("This is a work in progress feature that only analyzes the first response variable at the moment.")
        
        # Extract first column as a Series for analysis
        y_series = y.iloc[:, 0]
        
        # Calculate absolute feature count from percentage
        n_features = x.shape[1]
        n_features_to_select = max(1, math.floor(self.features_to_select * n_features))
        
        # Create estimator
        estimator = self._get_estimator()
        selector = RFE(
            estimator,
            n_features_to_select=n_features_to_select,
            step=self.elimination_step
        )
        
        try:
            # Perform feature selection
            selector.fit(x, y_series)
            feature_mask = selector.support_
            
            # If no features were selected, raise ValueError
            if not any(feature_mask):
                raise ValueError("Analysis not possible, no features to analyse")
            
            # Get selected features data and names
            selected_features = x.columns[feature_mask].tolist()
            x_selected = x.loc[:, feature_mask]
            
            # Fit model on selected features only
            estimator.fit(x_selected, y_series)
            
            # Calculate importance for selected features
            importance_result = permutation_importance(
                estimator,
                x_selected, 
                y_series,
                n_repeats=self.n_repeats,
                random_state=self.random_state
            )
            
            # First create the DataFrame with the right structure using enum constants
            df_results = pd.DataFrame(
                index=selected_features,
                columns=[
                    AnalyserColEnums_FS.SELECTED.value,
                    AnalyserColEnums_FS.IMPORTANCE_MEAN.value, 
                    AnalyserColEnums_FS.IMPORTANCE_STD.value, 
                    AnalyserColEnums_FS.IMPORTANCE_MEAN_PERCENT.value,
                    AnalyserColEnums_FS.COL_LABEL_X.value
                    ]
            )
            
            # Basic vals
            df_results[AnalyserColEnums_FS.SELECTED.value] = True
            df_results[AnalyserColEnums_FS.IMPORTANCE_MEAN.value] = importance_result.importances_mean # type: ignore
            df_results[AnalyserColEnums_FS.IMPORTANCE_STD.value] = importance_result.importances_std # type: ignore
            df_results[AnalyserColEnums_FS.COL_LABEL_X.value] = df_results.index 
            
            # Derived values
            total_importance = df_results[AnalyserColEnums_FS.IMPORTANCE_MEAN.value].sum() or 1.0  # Avoid division by zero
            df_results[AnalyserColEnums_FS.IMPORTANCE_MEAN_PERCENT.value] = df_results[AnalyserColEnums_FS.IMPORTANCE_MEAN.value].apply(
                lambda imp: (100 * imp / total_importance) if not pd.isna(imp) else 0.0
            )
            
            # Optional: Add in rows for unselected feature
            if include_unselected:
                unselected_features = [f for f in x.columns if f not in selected_features]
                df_results = self._add_rows_with_defaults(df_results, unselected_features)
                # Set identifier for unselected features
                for feature in unselected_features:
                    df_results.at[feature, AnalyserColEnums_FS.COL_LABEL_X.value] = feature
            
            logging.debug(f"Feature importance analysis completed with {len(selected_features)} selected features")
            
            # Cleanup
            df_results = df_results.sort_values(AnalyserColEnums_FS.IMPORTANCE_MEAN.value, ascending=False)
            df_results = set_dataframe_precision(df_results, self.precision)
            
            return df_results
        
        except Exception as e:
            raise RuntimeError(f"Feature importance analysis failed: {str(e)}")

#############################

