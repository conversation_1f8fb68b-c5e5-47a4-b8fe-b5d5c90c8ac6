from __future__ import annotations
from ._imports import *

from SALib import ProblemSpec
from backend.config.z_loader import MetisConfig

DEFAULT_CONFIG = MetisConfig.load_development_config()


class z_ProblemSpecification:

    def __init__(
        self,
        sample_name: str = "<PERSON><PERSON>",
        input_var_names: List[str] = [],
        input_var_bounds: List[list] = [],
        fixed_input_var_names: List[str] = [],
        fixed_input_var_bounds: List[list] = [],
        output_var_names: List[str] = [],
        sample_count: Optional[int] = DEFAULT_CONFIG.ps_sample_count,
        calc_second_order: Optional[bool] = True,
        group_names: Optional[Union[list, None]] = None,
    ) -> None:
        """
        Instantiate the object of class ProblemSpecification.

        Args:
            # DATASET
            input_var_names (List[str]): A list of strings referring to inputs (decision variables and/or parameters).
            input_var_bounds (List[list]): A list containing [lower_bound, upper_bound] for each input.
            fixed_input_var_names (List[str]): A list of strings referring to inputs that are fixed (fixed decision variables).
            fixed_input_var_bounds (List[list]): A list containing [lower_bound, upper_bound] for each fixed input.
            output_var_names (List[str]): A list of strings referring to outputs (dependent variables and/or calculated parameters).

            # SAMPLING PARAMETERS
            sample_name (str): Name of the sample generation method.
            sample_count (int, optional): An integer specifying the number of samples to be evaluated to use for sensitivity analyses.
            calc_second_order (bool, optional): Second order calculation to be used for analyses. By default it is True.
            group_names (List[str], optional): A list of strings referring to groups of each input variables.

        Returns:
            None
        """

        # Assign sampling method name
        if sample_name in ["Sobol", "Morris", "FAST", "RBD-FAST"]:
            self.sample_name = sample_name
        else:
            logging.error(
                f"The specified sample method name is not valid {sample_name}. One of the following methods should be specified [Sobol, Morris, FAST, RBD-FAST]."
            )

        self.input_var_names = input_var_names
        self.fixed_input_var_names = fixed_input_var_names
        self.count_factors = len(self.input_var_names)
        self.fixed_count_factors = len(self.fixed_input_var_names)
        self.output_var_names = output_var_names

        # Check if specified bounds are logical.
        if len(fixed_input_var_bounds) == self.fixed_count_factors:
            self.fixed_input_var_samples = [ele[0] for ele in fixed_input_var_bounds]
        else:
            self.fixed_input_var_samples = []
            logging.error(
                f"Size of fixed input variable bounds list does not match with the expected fixed input variable count. Expected fixed input variable count is {self.fixed_count_factors}!"
            )

        # Check if specified bounds are logical.
        if len(input_var_bounds) == self.count_factors:
            self.input_var_bounds = input_var_bounds
        else:
            logging.error(
                f"Size of input variable bounds list does not match with the expected input variable count. Expected input variable count is {self.count_factors}!"
            )

        # Get the key word arguments for analysis.
        self.sample_count = sample_count
        self.calc_second_order = calc_second_order

        # Get the groups if specified.
        self.group_names = group_names
        if not self.group_names == None and len(self.group_names) != self.count_factors:
            logging.error(
                f"Size of group names list does not match with the expected input variable count. Expected input variable count is {self.count_factors}!"
            )

        # Generate problem specification
        self.problem_spec = ProblemSpec(
            {
                "names": self.input_var_names,
                "groups": self.group_names,
                "bounds": self.input_var_bounds,
                "outputs": self.output_var_names,
            }
        )
        logging.info(
            f"The problem specification is instantiated successfully {self.problem_spec}."
        )

    @property
    def z_generate_samples(self):
        return self.generate_samples()

    def generate_samples(self) -> pd.DataFrame:
        """
        Generate samples for the specified sampling method and add the fixed values of the fixed input variables.

        Args:

        Returns:
            df_data (dataframe): A dataframe of all samples based on the provided methods.
        """
        # Generate samples based on the specified sampling method name.
        if self.sample_name == "Sobol":
            samples = self._generate_sobol_samples()
        elif self.sample_name == "Morris":
            samples = self._generate_morris_samples()
        elif self.sample_name == "FAST":
            samples = self._generate_fast_samples()
        elif self.sample_name == "RBD-FAST":
            samples = self._generate_rbdfast_samples()
        else:
            # Return empty dataframe if sampling method is not chosen
            samples = np.array([])

        # Dataframe containing the samples for non-fixed variables.
        df_samples = pd.DataFrame(samples, columns=self.input_var_names)

        # Dataframe containing the fixed samples for fixed variables.
        fixed_samples = np.tile(self.fixed_input_var_samples, (df_samples.shape[0], 1))
        df_fixed_samples = pd.DataFrame(
            fixed_samples, columns=self.fixed_input_var_names
        )

        # Build combined dataframe with all samples
        df_data = pd.concat([df_samples, df_fixed_samples], axis=1)

        return df_data

    def _save_samples(self, file_path: Union[Optional[str], None]):
        """
        Save the samples as CSV file.
        """
        if file_path == None:
            file_path = (
                str(self.sample_name)
                + "_"
                + str(self.sample_count)
                + "_"
                + str(datetime.datetime.timestamp)
            )
        df_samples = self.z_generate_samples
        df_samples.to_csv(file_path)

    def _generate_sobol_samples(self):
        """
        Generate Sobol samples.
        """
        self.problem_spec.sample_sobol(
            self.sample_count, calc_second_order=self.calc_second_order
        )
        samples = self.problem_spec.samples
        return samples

    def _generate_morris_samples(self):
        """
        Generate Morris samples.
        """
        self.problem_spec.sample_morris(self.sample_count)
        samples = self.problem_spec.samples
        return samples

    def _generate_fast_samples(self):
        """
        Generate FAST samples.
        """
        self.problem_spec.sample_fast(self.sample_count)
        samples = self.problem_spec.samples
        return samples

    def _generate_rbdfast_samples(self):
        """
        Generate RBD-FAST samples.
        """
        self.problem_spec.sample_latin(self.sample_count)
        samples = self.problem_spec.samples
        return samples


# if __name__ == "__main__":
#     sample_name = "Sobol"
#     input_var_names = [
#         "Fuel Flow Temp",
#         "Fuel Flow Pressure",
#         "Fuel Flow Mass Flow",
#         "Air Flow Temp",
#         "Air Flow Pressure",
#         "Air Flow Mass Flow",
#         "Oil Feed Temp",
#         "Oil Feed Pressure",
#         "Oil Feed Mass Flow",
#         "Benzene Toluene Feed Temp",
#         "Benzene Toluene Feed Pressure",
#         "Benzene Toluene Feed Mass Flow",
#         "HEX-100 Global HTC",
#         "HEX-100 Heat Loss",
#         "HEX-101 Global HTC",
#         "HEX-101 Heat Loss",
#     ]
#     input_var_bounds = [
#         [20, 35],
#         [1, 2],
#         [20, 30],
#         [40, 80],
#         [1, 3],
#         [400, 800],
#         [40, 60],
#         [1, 2],
#         [3000, 4000],
#         [20, 40],
#         [1, 2],
#         [3000, 4000],
#         [80, 140],
#         [0, 10],
#         [5, 15],
#         [0, 10],
#     ]

#     fixed_input_var_names = ["Fuel Methane", "Fuel CO2", "Air O2"]
#     fixed_input_var_bounds = [[0.6, 0.6], [0.4, 0.4], [1.0, 1.0]]

#     output_var_names = ["OPEX"]
#     sample_count = 5

#     test_prob_spec = ProblemSpecification(
#         sample_name=sample_name,
#         input_var_names=input_var_names,
#         input_var_bounds=input_var_bounds,
#         fixed_input_var_names=fixed_input_var_names,
#         fixed_input_var_bounds=fixed_input_var_bounds,
#         output_var_names=output_var_names,
#         sample_count=sample_count,
#         calc_second_order=False,
#     )
#     print(test_prob_spec.generate_samples.head(5))
#     # test_prob_spec._save_samples(file_path="Dummy Data.csv")
