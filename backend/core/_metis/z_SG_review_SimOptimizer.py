from __future__ import annotations
from typing import List, Tuple, Union
import sympy
import logging
from scipy.optimize import direct, differential_evolution, shgo, dual_annealing, minimize, Bounds
from skopt import gp_minimize, forest_minimize
from skopt.space import Real
import numpy as np
import pyswarms as ps

# TODO SG to review for keep or refactor
class SimOptimizer:

    def __init__(self, optimization_name: str, simulation_object: object, obj_func_name: str, dec_var_names: List[str], dec_var_values: list, dec_var_bounds: List[list],
                 constraints: List[str], constraint_bounds: List[tuple], **kwargs) -> None:
        """
        Instantiate the object of class SimOptimizer.

        Args:
            optimization_name (str): Name of the object.
            simulation_object (object): Simulation wrapper object.
            obj_func_name (str): Name of the objective function used for optimization.
            dec_var_names (list[str]): List containing names of decision variables.
            dec_var_values (list[float]): List containing estimate values of decision variable values.
            dec_var_bounds (list[list]): List containing the lower bounds and upper bounds of the decision variables.
            constraints (list[str]): List containing string of expressions of constraints.
            constraint_bounds (list[tuple]): List containing tuple of bounds on constraints.
            constraint_penalties (optional, list[float]): List containing the penalties for the specified constraints.
            update_penalties (optional, bool): A boolean indicating whether penalties to be updated during optimization.
            penalty_cushion (optional, float): A multiplier to penalties that adds cushion to (over-estimates) penalty values.
            tolerance (optional, float): A fraction tolerance limit on constraint bounds. Especially, useful for equality constraints to terminate with numerical tolerance.

        Returns:
            None
        """

        self.optimization_name = optimization_name
        self.simulation_object = simulation_object
        self.obj_func_name = obj_func_name
        self.dec_var_names = dec_var_names
        self.dec_var_count = len(dec_var_names)

        # Check for the sanity of the decision variable array size
        if len(dec_var_values) == self.dec_var_count:
            self.dec_var_values = dec_var_values
        else:
            logging.error(f"Size of decision variable values list does not match with the expected decision variable count. Expected decision variable count is {self.dec_var_count}!")
            
        # Check for the sanity of the decision variable bounds array size
        if len(dec_var_bounds[0]) == self.dec_var_count and len(dec_var_bounds[1]) == self.dec_var_count:
            self.dec_var_low_bounds = dec_var_bounds[0]
            self.dec_var_up_bounds = dec_var_bounds[1]
            self.dec_var_bounds = Bounds(self.dec_var_low_bounds, self.dec_var_up_bounds)
        else:
             logging.error(f"Size of lower and/or upper bound arrays does not match with the expected decision variable count. Expected decision variable count is {self.dec_var_count}!")       

        self.constraints = constraints
        self.constraint_count = len(self.constraints)
        # Check for the sanity of the constraint penalties
        constraint_penalties = kwargs.get('constraint_penalties', self.constraint_count * [500])

        # Check if there are one or more constraints specified and instantiate.
        if self.constraint_count > 0:
            if len(constraint_penalties) == self.constraint_count:
                self.constraint_penalties = constraint_penalties
                self.penalities_summary = [constraint_penalties, ]
                self.update_penalties = kwargs.get('update_penalties', True)
                self.penalty_cushion = kwargs.get('penalty_cushion', 25)
                self.tolerance = kwargs.get('tolerance', 1e-3)
            else:
                logging.error(f"Size of penalty array does not match with the expected constraint count. Expected constraint count is {self.constraint_count}!")

            # Check for the sanity of the bounds on constraints
            if len(constraint_bounds) == self.constraint_count:
                self.constraint_bounds = [((1 - self.tolerance)*ele[0], (1 + self.tolerance)*ele[1]) for ele in constraint_bounds]
            else:
                logging.error(f"Size of constraint bound array does not match with the expected constraint count. Expected constraint count is {self.constraint_count}!")
            
            # Get and assign constraint vars and objects
            self.con_objects, self.con_object_vars, self.con_var_set = self.generate_constraint_objects(constraints=self.constraints)
            logging.info(f"SimOptimizer object instantiated with the given inputs!")
        else:
            self.constraint_penalties = constraint_penalties
            self.con_objects = []
            self.con_object_vars = []
            self.con_var_set = []
            self.constraint_bounds = []

        # Instantiate objective function summary
        self.obj_val_summary = []
        self.pen_obj_val_summary = []

    def generate_constraint_objects(self, constraints: List[str]) -> Tuple[dict, dict, list]:
        """
        Generate constraint objects using Sympify.

        Args:
            constraints (list[str]): List containing the string expressions of constraints.
            
        Returns:
            dict_con_objects (dict): Dictionary with constraint string as key and the constraint object as value.
            dict_con_object_vars (dict): Dictionary with constraint string as key and the list of involved variables as value.
            con_vars (list[str]): List containing the names of variables involves in constraints.
        """

        dict_con_objects = {}
        dict_con_object_vars = {}
        con_vars = []
        for num_ele in range(self.constraint_count):
            current_con_obj = sympy.sympify(constraints[num_ele])
            dict_con_objects[constraints[num_ele]] = current_con_obj
            current_con_obj_vars = [str(ele) for ele in current_con_obj.free_symbols]
            dict_con_object_vars[constraints[num_ele]] = current_con_obj_vars
            con_vars.extend(current_con_obj_vars)
        
        con_var_set = [str(ele) for ele in set(con_vars)]
        logging.info(f"generate_constraint_objects() - Constraint objects and associated artefacts are derived successfully. {dict_con_objects, dict_con_object_vars, con_var_set}")
        
        return dict_con_objects, dict_con_object_vars, con_var_set
    
    def compute_constraint_values(self, con_objects: dict, con_object_vars: dict, con_var_values: List[float], constraint_bounds: List[tuple]) -> Tuple[list, list]:
        """
        Given variable values compute the constraints and associated violations.

        Args:
            con_objects (dict): Dictionary containing the key value pairs of string expressions of constraints and associated objects.
            con_object_vars (dict)): Dictionary containing the key value pairs of string expression of constraints and associated list of variables.
            con_var_values (list[float]): List containing the float values for the constraint variables.
            constraint_bounds (list[tuple]): List containing tuple of bounds on the constraints.
            
        Returns:
            constraint_values (list[int]): List containing the constraint value based on the given variable values.
            constraint_violation (list[tuple]): List containing the tuples of violation of all the constraints.
        """
        dict_con_var_values = dict(zip(self.con_var_set, con_var_values))
                
        constraint_values = []
        constraint_violation = []
        for num_ele in range(self.constraint_count):
            current_con_var_values = {key:val for key, val in dict_con_var_values.items() if key in con_object_vars[self.constraints[num_ele]]}
            subs_con_value = con_objects[self.constraints[num_ele]].subs(current_con_var_values)
            con_value = subs_con_value.evalf()
            con_flag = (0 <= con_value - constraint_bounds[num_ele][0]) & (0 <= constraint_bounds[num_ele][1] - con_value)
            con_violation = (np.float64(con_value) - constraint_bounds[num_ele][0], constraint_bounds[num_ele][1] - np.float64(con_value))
            constraint_values.insert(num_ele, con_flag)
            constraint_violation.insert(num_ele, con_violation)
        
        logging.info(f"compute_constraint_values() - Constraint values and their violations are successfully computed. {constraint_values, constraint_violation}")
       
        return constraint_values, constraint_violation
    
    def compute_penalty(self, constraint_values: list, constraint_violation: List[tuple], constraint_penalties: List[float]) -> float:
        '''
        Given the constraint values, constraint violations, and penalties, compute the penalty value.

        Args:
            constraint_values (list): List containing Bools of constraint violations.
            constraint_violation (list[tuple]): List containing tuple of violation on lower and upper bounds.
            constraint_penalties (list[float]): List containing penalty values for constraints.

        Returns:
            cum_penalty (float): A cumulative penalty value. 
        '''

        # Check if one or more constraints are specified, and compute penalty.
        if self.constraint_count > 0:
            penalty = []
            for num_ele in range(self.constraint_count):
                if not constraint_values[num_ele]:
                    current_penalty = abs(min(constraint_violation[num_ele]) * constraint_penalties[num_ele] * (1+0.01*self.penalty_cushion))
                else:
                    current_penalty = 0
                penalty.insert(num_ele, current_penalty)

            cum_penalty = sum(penalty)
            logging.info(f"compute_penalty() - Penalties are successfully computed for the given constraint values. {penalty, cum_penalty}")
        else:
            cum_penalty = 0

        return cum_penalty 

    def compute_optimal_results(self, dec_var: List[float], *args) -> tuple:
        '''
        Given the decision variable values and other arguments, compute the penalized objective function.

        Args:
            dec_var (list[float]): List containing float values of all decision variables.
            args (list): List of additional arguments for computing the penalized objective function.
            args[0]: A string identifier for the objective function.
        
        Returns:
            opt_obj_val (float): An optimal objective function value.
            opt_constraint_values (list[int]): List containing the constraint values based on the optimal variable values.
            opt_constraint_violation (list[tuple]): List containing the tuples of violation of all the constraints for the optimal variable values.
        '''

        # Compute objective function value.
        opt_obj_val = self.simulation_object.get_fun_var_values(obj_func=args[0], con_var_set=self.con_var_set, dec_var_names=self.dec_var_names, dec_var=dec_var)
        
        # Given constraint variables, check constraint violation and its magnitude.
        opt_constraint_values, opt_constraint_violation = self.compute_constraint_values(con_objects=self.con_objects, con_object_vars=self.con_object_vars,
                                                                                         con_var_values=opt_obj_val[1:], constraint_bounds=self.constraint_bounds)
        
        logging.info(f"compute_optimal_results() - Optimal results are successfully computed. {opt_obj_val, opt_constraint_values, opt_constraint_violation}")

        return opt_obj_val[0], opt_constraint_values, opt_constraint_violation

    def get_penalized_objective_function_val(self, dec_var: Union[List[float], np.ndarray]) -> float:
        '''
        Given the decision variable values and other arguments, compute the penalized objective function.

        Args:
            dec_var (list[float]): List containing float values of all decision variables.
        
        Returns:
            pen_obj_val (float): A penalized objective function value.
        '''
        # Compute objective function value.
        fun_var_val = self.simulation_object.get_fun_var_values(obj_func=self.obj_func_name, con_var_set=self.con_var_set, dec_var_names=self.dec_var_names, dec_var=dec_var)
        self.obj_val_summary.append(fun_var_val[0])

        # Check if one or more constraints are specified and compute their value
        if self.constraint_count > 0:
            # Given constraint variables, check constraint violation and its magnitude.
            constraint_values, constraint_violation = self.compute_constraint_values(con_objects=self.con_objects, con_object_vars=self.con_object_vars,
                                                                                     con_var_values=fun_var_val[1:], constraint_bounds=self.constraint_bounds)
            
            # Check for updating in constraint penalty values.
            if self.update_penalties:
                self.constraint_penalties = [max(self.obj_val_summary)] * self.constraint_count
                self.penalities_summary.append(self.constraint_penalties)
        
        else:
            constraint_values = []
            constraint_violation = []
        
        # Compute constraint cumulative penalty.
        cum_pen = self.compute_penalty(constraint_values=constraint_values, constraint_violation=constraint_violation, constraint_penalties=self.constraint_penalties)
        pen_obj_val = fun_var_val[0] + cum_pen
        self.pen_obj_val_summary.append(pen_obj_val)
        logging.info(f"compute_penalized_objective_function() - Penalized objective function value is successfully computed. {pen_obj_val}")

        return pen_obj_val
        
    def compute_penalized_objective_function(self, dec_var: Union[List[float], np.ndarray]) -> Union[float, np.ndarray]:
        '''
        Given the decision variable values and other arguments, compute the penalized objective function.

        Args:
            dec_var (list[float] or np.ndarray): List containing float values of all decision variables.
        
        Returns:
            pen_obj_val_array (float, np.ndarray): A penalized objective function value.
        '''
        # Check the instance type of the input decision variable
                
        if isinstance(dec_var, np.ndarray) and dec_var.ndim > 1:
            k_samples = dec_var.shape[0]
            pen_obj_val_list = []
            for num_ele in range(k_samples):
                current_pen_obj_val = self.get_penalized_objective_function_val(dec_var=dec_var[num_ele, :])
                pen_obj_val_list.insert(num_ele, current_pen_obj_val)
            
            pen_obj_val_array = np.array(pen_obj_val_list)
            
            logging.info(f"The penalized objective function is computed for the given array input. {pen_obj_val_array}")
            
        elif isinstance(dec_var, list) or (isinstance(dec_var, np.ndarray) and dec_var.ndim ==1):
            pen_obj_val_array = self.get_penalized_objective_function_val(dec_var=dec_var)
                       
            logging.info(f"The penalized objective function is computed for the given list input. {pen_obj_val_array}")

        else:
            pen_obj_val_array = None
            logging.error(f"The decision variable value supplied is not of type array or list. {dec_var}")
                    
        return pen_obj_val_array
       
    def run_optimization(self, optimization_approach: str, **kwargs):
        '''
        Given the optimization settings, execute optimization.

        Args:
            obj_func_name (str): String identifier of the objective function name.
            optimization_approach (str): String identifier of the optimization algorithm to be used.
            max_iter (optional, int): Maximum number of iterations for optimization.
            local_method_name (optional, str): Name of the local method. Only applicable when optimization_approach is "local".
        Returns:
            results (object): Object with optimal results.
        '''

        max_iter = kwargs.get('max_iter', 10*len(self.dec_var_names))
        if optimization_approach == 'Local':
            options = {'maxiter': max_iter, 'disp': True}
            local_methods = ['Nelder-Mead', 'Powell', 'CG', 'BFGS', 'Newton-CG', 'L-BFGS-B', 'TNC', 'COBYLA', 'COBYQA', 'SLSQP',
                             'trust-constr', 'dogleg', 'trust-ncg', 'trust-krylov', 'trust-exact']
            local_method_name = kwargs.get('local_method_name', 'Nelder-Mead')
            if local_method_name not in local_methods:
                logging.error(f"The local optimizer method name is incorrect and does not match with any from the available methods. {local_methods}")
                
            results = minimize(fun=self.compute_penalized_objective_function, x0=np.array(self.dec_var_values),
                               bounds=self.dec_var_bounds, method=local_method_name, options=options)
            x_opt = results.x
            
        elif optimization_approach == 'Direct':
            results = direct(self.compute_penalized_objective_function, bounds=self.dec_var_bounds, maxfun=max_iter)
            x_opt = results.x
        
        elif optimization_approach == 'Differential_Evolution':
            results = differential_evolution(func=self.compute_penalized_objective_function, bounds=self.dec_var_bounds, maxiter=max_iter)
            x_opt = results.x

        elif optimization_approach == 'SHGO':
            options = {'maxiter': max_iter}
            results = shgo(func=self.compute_penalized_objective_function, bounds=self.dec_var_bounds, options=options)
            x_opt = results.x
        
        elif optimization_approach == 'GP_Minimize':
            dimensions = [(ele_low, ele_high) for ele_low, ele_high in zip(self.dec_var_low_bounds, self.dec_var_up_bounds)]
            results = gp_minimize(func=self.compute_penalized_objective_function, dimensions=dimensions, n_initial_points=10*len(self.dec_var_names),
                                  n_calls=max_iter, verbose=True)
            x_opt = results.x

        elif optimization_approach == 'PSO':
            # Get optional parameters for PySwarms
            c1 = kwargs.get('c1', 0.50)
            c2 = kwargs.get('c2', 0.50)
            w = kwargs.get('w', 0.90)
            n_particles = kwargs.get('n_particles', 10)
            options = {'c1': c1, 'c2': c2, 'w': w} 
            bounds = (self.dec_var_low_bounds, self.dec_var_up_bounds)
            pso_opt = ps.single.GlobalBestPSO(n_particles=n_particles, dimensions=len(self.dec_var_names), options=options, bounds=bounds)
            obj_val, x_opt = pso_opt.optimize(self.compute_penalized_objective_function, iters=max_iter, verbose=True)

        # Compute the optimal results
        opt_obj_val, opt_constraint_values, opt_constraint_violation = self.compute_optimal_results(x_opt, self.obj_func_name)
        
        logging.info(f"The optimizer computed the optimal results successfully. /n {opt_obj_val, opt_constraint_violation, opt_constraint_violation}")
        
        return x_opt, opt_obj_val, opt_constraint_values, opt_constraint_violation