from typing import Tuple, List, Dict, Optional

import logging
import pandas as pd
import numpy as np

from backend.core._sharedutils import data_precision

#######################################################
# GLOBAL SETTINGS - Setting data precision
set_precision = int(3)
#######################################################


class SummaryResults:
    def __init__(
        self,
        input_var_names: List[str],
        output_var_names: List[str],
        samples: pd.DataFrame,
        responses: pd.DataFrame,
        selected_features: List[bool],
        percent_mean_importance: List[float],
        var_contribution_threshold: float = 0.0,
        kpi_threshold: float = 95.0,
    ) -> None:
        """
        Instantiate the object of class SummaryResults for generating sensitivity analysis results.

        Args:
            input_var_names (list): A list of strings referring to inputs (decision variables and/or parameters).
            output_var_names (list): A list of strings referring to outputs (dependent variables and/or calculated parameters).
            samples (dataframe): A dataframe of samples.
            responses (dataframe): A dataframe of KPI values.
            selected_features (list): A list of booleans indicating which variables are selected from in_var_names.
            percent_mean_importance (list): A list of percent importance on selected variables.
            var_contribution_threshold (float, optional): A float indicating threshold to be applied to get summary.
            kpi_threshold (float, optional):

        Returns:
            None
        """
        self.input_var_names = input_var_names
        self.output_var_names = output_var_names
        self.samples = samples
        self.responses = responses
        self.selected_features = selected_features
        self.percent_mean_importance = percent_mean_importance
        self.var_contribution_threshold = var_contribution_threshold
        self.kpi_threshold = kpi_threshold

        logging.info(f"The summary results object is instantiated successfully.")

    @property
    def build_exp_results(self):
        """
        Generate the experiment results using the specified attributes.
        """
        # Get sorted summary of results
        self.sorted_summary = self._get_sorted_results()
        # Get statistics of the results
        self.stat_results = self._get_result_statistics(results=self.sorted_summary)
        # Get variable importance
        self.impact_var_importance = self._get_impact_variable_importance()
        # Get summary importance
        self.summarised_variables, self.summary_importance = (
            self._get_importance_summary(
                impact_variable_importance=self.impact_var_importance
            )
        )
        # Get summary of selected variables
        self.summary_mean, self.summary_std = self._get_summary_statistics(
            results=self.sorted_summary,
            summarised_variables=list(self.impact_var_importance.keys()),
        )
        logging.info(f"Experiment results are built successfully in SummaryResults.")

    def get_dgn_var_statistics(self, filtered_data: pd.DataFrame):
        """
        Generate filtered statistics using the given data for displaying diagnosis/troubleshooting results

        Args:
            filtered_data (dataframe): A dataframe containing filtered data based on KPI condition in troubleshooting.

        Returns:
            None
        """
        filtered_summary_mean = filtered_data.mean()
        filtered_summary_std = filtered_data.std()

        # Set data precision for summary mean and std
        filtered_summary_mean = data_precision.set_dataframe_precision(
            dataframe=filtered_summary_mean, precision=set_precision
        )
        filtered_summary_std = data_precision.set_dataframe_precision(
            dataframe=filtered_summary_std, precision=set_precision
        )
        self.dgn_filter_mean = filtered_summary_mean.to_dict()
        self.dng_filter_std = filtered_summary_std.to_dict()

    def get_filtered_statistics(self, filtered_data: pd.DataFrame):
        """
        Generate filtered statistics using the given data for displaying diagnosis/troubleshooting results

        Args:
            filtered_data (dataframe): A dataframe containing filtered data based on KPI condition in troubleshooting.

        Returns:
            None
        """
        self.filtered_summary_mean, self.filtered_summary_std = (
            self._get_summary_statistics(
                results=filtered_data,
                summarised_variables=list(filtered_data.columns),
            )
        )

    def _get_sorted_results(self) -> pd.DataFrame:
        """
        Sort the dataframe in descending order (high KPI value to low KPI value).

        Args:

        Returns:
            sorted_summary (dataframe): A dataframe of sorted summary.
        """
        # Collate dataframe using samples and responses
        results = pd.concat([self.samples, self.responses], axis=1)
        logging.info(f"The results summary have been prepared {results}.")

        # Generate sorted summary dataframe
        sorted_summary = results.sort_values(
            by=self.output_var_names[0], ascending=False
        )
        # Set data precision for the results
        sorted_summary = data_precision.set_dataframe_precision(
            dataframe=sorted_summary, precision=set_precision
        )

        logging.info(
            f"The sorted summarized results have been computed {sorted_summary}."
        )

        return sorted_summary

    def _get_result_statistics(self, results: pd.DataFrame) -> pd.DataFrame:
        """
        Get statistics of the sorted results.

        Args:
            results (dataframe): A dataframe submitted to get statistics.

        Returns:
            stat_results (dataframe): A dataframe of statistics.
        """
        # Generate statistics of summarized results
        stat_results = results.describe(percentiles=[0.05, 0.25, 0.5, 0.75, 0.95])
        # Set data precision for the results
        stat_results = data_precision.set_dataframe_precision(
            dataframe=stat_results, precision=set_precision
        )

        logging.info(
            f"The statistics of summarized results have been computed {stat_results}."
        )

        return stat_results

    def _get_impact_variable_importance(self) -> Dict[str, float]:
        """
        This method gives impact variable importance for the selected variables and percent mean importance.

        Args:

        Returns:
            impact_variable_importance (dict): A dictionary containing variable names and its importance value.
        """
        # Generate selected variable names
        in_var_names_array = np.array(self.input_var_names)
        selected_variable_names = list(in_var_names_array[self.selected_features])
        logging.info(
            f"The selected features boolean mask {self.selected_features} is applied on specified variable names {in_var_names_array} and obtained selected variable names {selected_variable_names}."
        )
        # Set data precision for the results
        percent_mean_importance = data_precision.set_list_precision(
            data_list=self.percent_mean_importance, precision=set_precision
        )

        # Generate summary of impact variables and importance
        impact_variable_importance = dict(
            zip(selected_variable_names, percent_mean_importance)
        )
        logging.info(f"Variable impact is obtained {impact_variable_importance}.")
        return impact_variable_importance

    def _get_importance_summary(
        self, impact_variable_importance: Dict[str, float]
    ) -> Tuple[List[str], Dict[str, float]]:
        """
        This generates summary of variable importance results.

        Args:
            impact_variable_importance (dict): A dictionary containing variable names and its importance value.

        Returns:
            summarised_variables (list): A list containing names of the variables used in summary.
            summary_importance (dict): A dictionary containing the variable names and their contribution based on contribution threshold.
        """
        # Summary of importance contribution
        summary_importance = {}
        summarised_variables = []
        other_importance = 0.0

        for ele_key in list(impact_variable_importance.keys()):
            if (
                impact_variable_importance[ele_key] >= self.var_contribution_threshold
            ):  # Test for var contribution above 0.
                summary_importance[ele_key] = impact_variable_importance[ele_key]
                summarised_variables.append(ele_key)
                logging.info(
                    f"Variable {ele_key} has impact {impact_variable_importance[ele_key]} above specified threshold {self.var_contribution_threshold} and added to summary."
                )

            else:
                other_importance = (
                    other_importance + impact_variable_importance[ele_key]
                )
                logging.info(
                    f"Variable {ele_key} has impact {impact_variable_importance[ele_key]} below specified threshold {self.var_contribution_threshold} and is not added to summary."
                )

        summary_importance["Others"] = round(other_importance, set_precision)
        logging.info(
            f"Successfully obtained the summarized variables: {summarised_variables} and impact summary: {summary_importance}."
        )

        return summarised_variables, summary_importance

    def _filter_results(self, results: pd.DataFrame) -> pd.DataFrame:
        """
        Filter the given dataframe based on the KPI threshold set at the object level.

        Args:
            results (dataframe): A dataframe with results for filtering.

        Returns:
            filtered_results (datafrme): A dataframe with the filtered results.
        """
        # Summary of variables and their average values to achieve above threshold KPI performance.
        kpi_filter_val = (
            self.kpi_threshold * 0.01 * (results.loc[:, self.output_var_names[0]].max())
        )
        filtered_results = results[results[self.output_var_names[0]] >= kpi_filter_val]
        logging.info(
            f"Successfully obtained filtered dataframe of size {filtered_results.shape} using the KPI threshold {self.kpi_threshold}"
        )

        return filtered_results

    def _get_summary_statistics(
        self, results: pd.DataFrame, summarised_variables: List[str]
    ) -> Tuple[dict, dict]:
        """
        Generate the statistics like mean and std for results.

        Args:
            results (dataframe): A dataframe for which the statistics to be computed.

        Returns:
            summary_mean (dict): A dictionary summarizing mean value of the selected variables.
            summary_std (dict): A dictionary summarizing std value of the selected variables.

        """
        # Filter the summary data
        filtered_results = self._filter_results(results=results)
        # Get mean and std for the selected variables
        sorted_summary_mean = filtered_results.loc[:, summarised_variables].mean()
        sorted_summary_std = filtered_results.loc[:, summarised_variables].std()

        # Set data precision for summary mean and std
        sorted_summary_mean = data_precision.set_dataframe_precision(
            dataframe=sorted_summary_mean, precision=set_precision
        )
        sorted_summary_std = data_precision.set_dataframe_precision(
            dataframe=sorted_summary_std, precision=set_precision
        )
        summary_mean = sorted_summary_mean.to_dict()
        summary_std = sorted_summary_std.to_dict()

        logging.info(
            f"Summary report is generated - summary_mean: {summary_mean} and summary_std: {summary_std}."
        )

        return summary_mean, summary_std

    def _get_response_summary(self, results: pd.DataFrame):
        """
        This generates mean and standard deviation of expected KPI.

        Args:
            results (dataframe): A dataframe with results to compute mean and std.

        Returns:
            mean_expected_kpi (float): Mean KPI value in the expected assessment.
            std_expected_kpi (float): Standard deviation of KPI in the expected assessment.
            min_kpi (float): Minimum KPI value.
            max_kpi (float): Maximum KPI value.
        """
        # Get the filtered results and their statistics
        filtered_results = self._filter_results(results=results)
        stat_results = self._get_result_statistics(results=results)
        # Cmpute the mean and std
        mean_expected_kpi_val = float(filtered_results[self.output_var_names[0]].mean())
        mean_expected_kpi = round(mean_expected_kpi_val, set_precision)
        std_expected_kpi_val = float(filtered_results[self.output_var_names[0]].std())
        std_expected_kpi = round(std_expected_kpi_val, set_precision)
        # Compute minimum and maximum
        min_kpi_val = float(stat_results.at["min", self.output_var_names[0]])
        min_kpi = round(min_kpi_val, set_precision)
        max_kpi_val = float(stat_results.at["max", self.output_var_names[0]])
        max_kpi = round(max_kpi_val, set_precision)

        return mean_expected_kpi, std_expected_kpi, min_kpi, max_kpi
