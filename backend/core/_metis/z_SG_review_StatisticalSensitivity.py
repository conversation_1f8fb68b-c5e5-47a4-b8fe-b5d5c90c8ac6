from __future__ import annotations
from ._imports import *

from SALib import ProblemSpec


class StatisticalSensitivity:

    def __init__(
        self,
        sensitivity_name: str,
        input_var_names: List[str],
        input_var_bounds: List[list],
        output_var_names: List[str],
        samples: np.n<PERSON><PERSON>,
        responses: np.n<PERSON><PERSON>,
        **kwargs,
    ) -> None:
        """
        Instantiate the object of class StatisticalSensitivity.

        Args:
            sensitivity_name (str): Name of the sensitivity analysis name.
            input_var_names (List[str]): A list of strings referring to inputs (decision variables and/or parameters).
            input_var_bounds (List[list]): A list containing [lower_bound, upper_bound] for each input.
            output_var_names (List[str]): A list of strings referring to outputs (dependent variables and/or calculated parameters).
            samples (array): An array of samples used for evaluation.
            responses (array): An array of responses used for evaluation.
            group_names (List[str], optional): A list of strings referring to groups of each input variables.

        Returns:
            None
        """
        # Assign analysis method name.
        if sensitivity_name in ["So<PERSON>", "Morris", "FAST", "RBD-FAST"]:
            self.sensitivity_name = sensitivity_name
        else:
            logging.error(
                f"The specified sample method name is not valid {sensitivity_name}. One of the following methods should be specified [So<PERSON>, Morris, FAST, RBD-FAST]."
            )
        group_names = kwargs.get("group_names", None)

        # Create problem specification object
        self.problem_spec = ProblemSpec(
            {
                "names": input_var_names,
                "groups": group_names,
                "bounds": input_var_bounds,
                "outputs": output_var_names,
            }
        )
        logging.info(
            f"The problem specification is instantiated successfully {self.problem_spec}."
        )

        # Assign samples and responses to the problem specification
        self.problem_spec.samples = samples
        self.problem_spec.results = responses
        logging.info(
            f"The samples and responses are assigned to the problem spec object."
        )

    def analyze_results(self) -> np.ndarray:
        """
        Analyze the samples and responses to compute sensitivity indices.

        Args:

        Returns:
            analyzed_results (array): An array of results analyzed using the specified data.

        """
        if self.sensitivity_name == "Sobol":
            self.problem_spec.analyze_sobol() # Type: ignore
            analyzed_results = self.problem_spec.analysis
        elif self.sensitivity_name == "Morris":
            self.problem_spec.analyze_morris()
            analyzed_results = self.problem_spec.analysis
        elif self.sensitivity_name == "FAST":
            self.problem_spec.analyze_fast()
            analyzed_results = self.problem_spec.analysis
        elif self.sensitivity_name == "RBD-FAST":
            self.problem_spec.analyze_rbd_fast()
            analyzed_results = self.problem_spec.analysis

        logging.info(
            f"The sensitivity analysis successfully computed. The analyzed results are {analyzed_results}."
        )

        return analyzed_results
