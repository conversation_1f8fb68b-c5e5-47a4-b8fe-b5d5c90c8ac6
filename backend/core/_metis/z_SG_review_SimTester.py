from __future__ import annotations
from ._imports import *


class SimTester:
    def __init__(self, name: str) -> None:
        self.name = name
        pass

    def get_fun_values(
        self,
        output_var_names: List[str],
        input_var_names: List[str],
        input_vars: np.ndarray,
    ) -> np.ndarray:

        K = input_vars.shape[0]
        if input_vars.ndim == 1:
            fun_val = self.get_obj_val(dec_var=list(input_vars))
            fun_vals = [fun_val]

        else:
            fun_vals = []
            for num_ele in range(
                K
            ):  # Mimicing how process simulations call will be made
                fun_val = self.get_obj_val(dec_var=input_vars[num_ele])
                fun_vals.insert(num_ele, fun_val)

        fun_val_array = np.array(fun_vals)

        return fun_val_array

    def get_obj_val(self, dec_var: List[float]) -> float:
        obj_val = 3 * dec_var[0] ** 4 + 4 * dec_var[1] ** 2

        return obj_val

    def get_con_var_val(
        self, dec_var: List[float], dec_var_names: List[str], con_var_names: List[str]
    ) -> List[float]:
        con_var_val = []
        for ele in con_var_names:
            num_ele = dec_var_names.index(ele)
            con_var_val.append(dec_var[num_ele])

        return con_var_val

    def get_fun_var_values(
        self,
        obj_func: str,
        con_var_set: List[str],
        dec_var_names: List[str],
        dec_var: List[float],
    ) -> list:
        fun_var_val = []
        if obj_func == "Cost":
            obj_val = self.get_obj_val(dec_var=dec_var)

            con_var_val = self.get_con_var_val(
                dec_var=dec_var, dec_var_names=dec_var_names, con_var_names=con_var_set
            )
            fun_var_val = [obj_val] + con_var_val

        return fun_var_val
