from abc import ABC, abstractmethod

#############################
# SAMPLER
class ConfigBase(ABC):
    """Abstract base class for configurable analytical components.

    Defines standard configuration factory methods that all analytical 
    components should implement to provide consistent configuration
    across different environments.
    """

    @classmethod
    @abstractmethod
    def create_dev_config(cls) -> 'ConfigBase':
        """Create a configuration optimized for development environments.

        Returns a configuration with settings optimized for faster execution,
        more verbose logging, and development-friendly defaults.
        """
        pass

    @classmethod
    @abstractmethod
    def create_production_config(cls) -> 'ConfigBase':
        """Create a configuration optimized for production environments.

        Returns a configuration with settings optimized for reliability,
        performance at scale, and more conservative resource usage.
        """
        pass