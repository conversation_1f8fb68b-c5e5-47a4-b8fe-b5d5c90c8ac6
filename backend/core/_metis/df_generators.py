from __future__ import  annotations
from ._imports import *

from SALib import ProblemSpec


class SampleGenerator(ConfigBase):
    """Experimental design sampler for simulation and sensitivity analysis.

    This class generates input variable combinations using various sampling methods
    such as Sobol, Morris, and FAST. It follows a configuration-based approach where
    the sampling parameters are set at initialization, and the actual variable
    specifications are provided when generating samples.

    Example usage:

    # Create sampler at service startup
    default_sampler = SamplingStrategy.create_default_config()
    detailed_sampler = SamplingStrategy(method="Morris", sample_count=1000)

    # Generate samples within request handlers
    samples_df = default_sampler.generate_samples(
        variable_specs={
            "variables": {
                "temperature": (20, 100),
                "pressure": (1, 10),
                "flow_rate": (5, 50)
            },
            "fixed_variables": {
                "catalyst_type": 1.0
            }
        }
    )
    """

    # Valid sampling methods
    VALID_METHODS = ["Sobol", "Morris", "FAST", "RBD-FAST"]

    def __init__(
        self,
        method: str = "Sobol",
        sample_count: int = 2,
        calc_second_order: bool = True,
        random_seed: int = 42
    ) -> None:
        """Initialize sampling strategy configuration.

        Args:
            method: Sampling method name ('Sobol', 'Morris', 'FAST', 'RBD-FAST')
            sample_count: Number of samples to generate
            calc_second_order: Whether to calculate second-order effects (Sobol only)
            random_seed: Random seed for reproducible sampling

        Raises:
            ValueError: If an invalid sampling method is specified
        """
        if method not in self.VALID_METHODS:
            raise ValueError(
                f"Invalid sampling method: {method}. Must be one of: {', '.join(self.VALID_METHODS)}"
            )

        self.method = method
        self.sample_count = sample_count
        self.calc_second_order = calc_second_order
        self.random_seed = random_seed

        logging.info(
            f"Initialized {method} sampler with {sample_count} samples"
            f"{' and second-order effects' if calc_second_order else ''}"
        )

    @classmethod
    def create_dev_config(cls) -> 'SampleGenerator':
        """Create a development environment configuration with smaller sample sizes."""
        return cls(method="Sobol", sample_count=2, calc_second_order=True)

    @classmethod
    def create_production_config(cls) -> 'SampleGenerator':
        """Create a production environment configuration with robust sampling."""
        return cls(method="Sobol", sample_count=5, calc_second_order=True)

    def generate_samples(
        self,
        bounded_input_variables: Dict[str, Tuple[float, float]],
        fixed_input_variables: Optional[Dict[str, float]] = None,
        *,
        output_names: Optional[List[str]] = None,
        group_names: Optional[List[str]] = None
    ) -> pd.DataFrame:
        """Generate samples for experimental design or sensitivity analysis.

        Args:
            bounded_input_variables: Dictionary mapping variable names to (min, max) tuples
            fixed_input_variables: Dictionary mapping variable names to fixed values
            output_names: Optional list of output variable names
            group_names: Optional list of group names for variables

        Returns:
            DataFrame containing generated samples for all variables

        Raises:
            ValueError: For invalid inputs or inconsistent specifications
        """
        # Validate input parameters
        if not bounded_input_variables:
            raise ValueError("No bounded variables specified for sampling")

        # Initialize defaults
        fixed_input_variables = fixed_input_variables or {}

        # Convert to lists for SALib
        input_var_names = list(bounded_input_variables.keys())
        input_var_bounds = [list(bounded_input_variables[name]) for name in input_var_names]

        fixed_var_names = list(fixed_input_variables.keys())
        fixed_var_values = [fixed_input_variables[name] for name in fixed_var_names]

        # Validate group names if provided
        if group_names and len(group_names) != len(input_var_names):
            raise ValueError(
                f"Group names length ({len(group_names)}) must match number of variables ({len(input_var_names)})"
            )

        # Create problem specification
        samples = self._generate_samples_for_problem(
            input_var_names,
            input_var_bounds,
            output_names or [],
            group_names
        )

        # Create dataframe of samples
        df_samples = pd.DataFrame(samples, columns=input_var_names)

        # Add fixed variables
        if fixed_var_names:
            # Create repeated values for each fixed variable
            fixed_df = pd.DataFrame({
                name: [value] * len(df_samples)
                for name, value in zip(fixed_var_names, fixed_var_values)
            })

            # Combine variable and fixed columns
            df_samples = pd.concat([df_samples, fixed_df], axis=1)

        return df_samples

    def _generate_samples_for_problem(
        self,
        names: List[str],
        bounds: List[List[float]],
        outputs: List[str],
        groups: Optional[List[str]] = None
    ) -> np.ndarray:
        """Create problem specification and generate samples."""
        try:
            # Create problem specification
            problem_spec = ProblemSpec({
                "names": names,
                "bounds": bounds,
                "outputs": outputs,
                "groups": groups
            })

            # Generate samples using method-specific sampling function
            sampling_functions = {
                "Sobol": lambda: problem_spec.sample_sobol( # type: ignore
                    self.sample_count,
                    calc_second_order=self.calc_second_order
                ),
                "Morris": lambda: problem_spec.sample_morris( # type: ignore
                    self.sample_count
                ),
                "FAST": lambda: problem_spec.sample_fast( # type: ignore
                    self.sample_count
                ),
                "RBD-FAST": lambda: problem_spec.sample_latin( # type: ignore
                    self.sample_count
                )
            }

            # Get and execute the appropriate sampling function
            if self.method not in sampling_functions:
                raise ValueError(f"Unsupported sampling method: {self.method}")

            sampling_functions[self.method]()

            # Validate the generated samples
            samples = problem_spec.samples
            self._validate_samples(samples, names, bounds) # type: ignore

            return samples # type: ignore

        except Exception as e:
            raise RuntimeError(f"Sampling generation failed for {self.method}: {str(e)}") from e

    def _validate_samples(self, samples: np.ndarray, names: List[str], bounds: List[List[float]]) -> None:
        """Validate generated samples meet quality criteria.

        Args:
            samples: Generated sample array
            names: Variable names
            bounds: Variable bounds as list of [min, max] pairs

        Raises:
            ValueError: If samples fail validation criteria
        """
        # Check that samples were generated
        if samples is None or samples.size == 0:
            raise ValueError("No samples were generated")

        # Check dimensions
        expected_variables = len(names)
        if samples.shape[1] != expected_variables:
            raise ValueError(f"Generated samples have incorrect dimensions: expected {expected_variables} variables, got {samples.shape[1]}")

        # Check sample count
        expected_samples = self.sample_count
        if self.method == "Sobol" and self.calc_second_order:
            # Sobol with second-order effects generates more samples than requested
            pass
        elif samples.shape[0] < expected_samples:
            raise ValueError(f"Insufficient samples generated: expected at least {expected_samples}, got {samples.shape[0]}")

        # Check for NaN or infinite values
        if np.isnan(samples).any() or np.isinf(samples).any():
            raise ValueError("Generated samples contain NaN or infinite values")

        # Check bounds compliance (SALib should handle this, but verify)
        for i, (lower, upper) in enumerate(bounds):
            column = samples[:, i]
            if np.any(column < lower) or np.any(column > upper):
                raise ValueError(f"Values for variable '{names[i]}' are outside specified bounds [{lower}, {upper}]")

        # Log validation success
        logging.debug(f"Successfully validated {samples.shape[0]} samples for {len(names)} variables")

    def save_samples_to_csv(self, df_samples: pd.DataFrame, file_path: Optional[str] = None) -> str:
        """Save generated samples to CSV file.

        Args:
            df_samples: DataFrame of samples to save
            file_path: Optional file path, generated if not provided

        Returns:
            Path to saved file
        """
        if file_path is None:
            timestamp =datetime.now().strftime("%Y%m%d_%H%M%S")
            file_path = f"{self.method}_{self.sample_count}_{timestamp}.csv"

        df_samples.to_csv(file_path, index=False)
        logging.info(f"Saved {len(df_samples)} samples to {file_path}")

        return file_path