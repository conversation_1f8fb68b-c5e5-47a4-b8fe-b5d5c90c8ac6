from __future__ import annotations
from ._imports import *

from sklearn.feature_selection import RFE
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearReg<PERSON>, <PERSON>, Lasso
from sklearn.svm import SVR


class z_FeatureEliminator:
    def __init__(
        self,
        input_var_names: List[str],
        estimator_name: str = "random_forest",
        features_to_select: float = 0.80,
        elimination_step: int = 1,
        kernel: Optional[str] = "linear",
    ) -> None:
        """
        Instantiate the object of FeatureEliminator class.

        Args:
            input_var_names (list): A list of input variable names.
            estimator_name (str): Name of the estimator to be fitted and used. It is optional and by default 'random_forest' is used.
                                            Acceptable inputs are 'linear', 'ridge, 'lasso', 'random_forest', 'gradient_boosting', 'support_vector'.
            features_to_select (float,): Fraction of features to be selected from all the specified features. By default, 80% of the number of features are selected.
            elimination_step (int): Number of features to be eliminated in every iteration. This is an optional and default value of 1 is taken.
            kernel (str, optional): Kernel to be specified for SVR estimators. This is an optional and default value of 'linear' is taken when SVR is used.

        Returns:
            None
        """
        self.estimator_name = estimator_name
        self.select_features = math.floor(features_to_select * len(input_var_names))
        self.kernel = kernel
        self.estimator = self.get_estimator
        # Build RFE selector object
        self.selector = RFE(
            self.estimator,
            n_features_to_select=self.select_features,
            step=elimination_step,
        )

        logging.info(
            f"The feature selector object is created using estimator - {estimator_name} to select {features_to_select} features."
        )

    @property
    def get_estimator(
        self,
    ) -> Union[
        LinearRegression,
        Ridge,
        Lasso,
        RandomForestRegressor,
        GradientBoostingRegressor,
        SVR,
    ]:
        """
        Instantiate estimator based on estimator name.

        Args:

        Returns:
            estimator (object): Estimator object used for feature elimination.
        """
        # Instantiate estimator
        if self.estimator_name == "linear":
            estimator = LinearRegression()
        elif self.estimator_name == "ridge":
            estimator = Ridge()
        elif self.estimator_name == "lasso":
            estimator = Lasso()
        elif self.estimator_name == "random_forest":
            estimator = RandomForestRegressor()
        elif self.estimator_name == "gradient_boosting":
            estimator = GradientBoostingRegressor()
        elif self.estimator_name == "support_vector":
            estimator = SVR(kernel=self.kernel)
        else:
            estimator = RandomForestRegressor()

        return estimator
