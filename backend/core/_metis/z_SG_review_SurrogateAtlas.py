from __future__ import annotations
from ._imports import *

class SurrogateAtlas:
    def __init__(self, name: str) -> None:
        self.name = name
        pass

    def get_fun_values(
        self,
        output_var_names: List[str],
        input_var_names: List[str],
        input_vars: pd.DataFrame,
    ) -> pd.DataFrame:
        # Check if all the specified variables are provided in variable values.
        in_var_name_set = set(input_var_names)
        in_var_val_name_set = set(list(input_vars.columns))
        if in_var_name_set.issubset(in_var_val_name_set):
            # Mimicing how process simulations call will be made
            K = input_vars.shape[0]
            fun_vals = []
            for num_ele in range(K):
                dec_var_val = input_vars.iloc[num_ele].to_list()
                fun_val = self.get_obj_val(dec_var=dec_var_val)
                fun_vals.insert(num_ele, fun_val)

            # Generate response dataframe
            out_var_val = pd.DataFrame(fun_vals, columns=output_var_names)

        else:
            out_var_val = pd.DataFrame([], columns=output_var_names)
            logging.error(
                f"The specified variable values expected {in_var_name_set} and found {in_var_val_name_set}."
            )

        return out_var_val

    def get_obj_val(self, dec_var: List[float]) -> float:
        obj_val = (
            3 * dec_var[0] ** 3
            + 4 * dec_var[1]
            + 5 * dec_var[2] ** 2
            + dec_var[3]
            + dec_var[4]
        )

        return obj_val
