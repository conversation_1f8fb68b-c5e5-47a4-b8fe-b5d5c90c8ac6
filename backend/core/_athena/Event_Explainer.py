from __future__ import (
    annotations,  # for unquoted annotations in TYPE_CHECKING (forward_vars)
)
from ._imports import *

from lime.lime_tabular import LimeTabularExplainer
from sklearn.model_selection import train_test_split
from backend.core._athena import Anomaly_Finder


# Dataset for testing anomaly detection
def generate_test_data():
    n_samples, n_outliers = 120, 40
    rng = np.random.RandomState(0)
    covariance = np.array([[0.5, -0.1], [0.7, 0.4]])
    cluster_1 = 0.4 * rng.randn(n_samples, 2) @ covariance + np.array([2, 2])  # general
    cluster_2 = 0.3 * rng.randn(n_samples, 2) + np.array([-2, -2])  # spherical
    outliers = rng.uniform(low=-4, high=4, size=(n_outliers, 2))

    X = np.concatenate([cluster_1, cluster_2, outliers])
    y = np.concatenate([np.ones((2 * n_samples), dtype=int), -np.ones((n_outliers), dtype=int)])

    X_train, X_test, y_train, y_test = train_test_split(X, y, stratify=y, random_state=42)

    return X_train, X_test, y_train, y_test

X_train, X_test, y_train, y_test = generate_test_data()
anomaly_clf = Anomaly_Finder.AnomalyDetector(scaling='standardization')
X_train_scaled_data = anomaly_clf.perform_data_scaling(x=X_train)
trained_predicted_labels = anomaly_clf.build_anomaly_classifier(x=X_train_scaled_data)

explainer = LimeTabularExplainer(X_train, feature_names=['feature1', 'feature2'], class_names=['normal', 'anomaly'], discretize_continuous=True)

# Select an instance to explain
instance_idx = 0
instance = X_train[instance_idx]
#.reshape(1, -1)
# Define a predict function for LIME
def predict_fn(X):
    # Return anomaly scores #.reshape(-1, 1)
    return anomaly_clf.anomaly_classifier.decision_path(X)
# val = predict_fn(instance)
# print(val)

# Generate explanation
exp = explainer.explain_instance(instance, predict_fn, num_features=2)

print(exp.as_list())

# iris = load_iris()
# X = iris.data
# y = iris.target

# X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# rfc = RandomForestClassifier(n_estimators=100, random_state=42)

# rfc.fit(X_train, y_train)

# y_pred = rfc.predict(X_test)
# accuracy = accuracy_score(y_test, y_pred)
# print(f"Accuracy: {accuracy:.2f}")


# explainer = lime.lime_tabular.LimeTabularExplainer(X_train, feature_names=iris.feature_names, class_names=iris.target_names, discretize_continuous=True)
# idx = 0  # index of the sample we want to explain
# exp = explainer.explain_instance(X_test[idx], rfc.predict_proba)
# print(exp.available_labels())
# print(exp.predict_proba)
# print(exp.as_list(label=2))
# for i in range(3):
#     print(f"{exp.as_list()[i][0]}: {exp.as_list()[i][1]:.2f}")


# Dataset for testing anomaly detection



