from __future__ import (
    annotations,  # for unquoted annotations in TYPE_CHECKING (forward_vars)
)
from ._imports import *

from sklearn.metrics import r2_score, mean_absolute_error, mean_squared_error, mean_squared_log_error, mean_absolute_percentage_error, median_absolute_error, max_error, explained_variance_score

class ModelPerformance:
    def __init__(self, y_true: np.ndarray, y_pred: np.ndarray) -> None:
        '''
        This instantiates an object of ModelPerformance class which can be used to compute various metrics.
        Args:
            y_true (array): Array with true/expected values of the response/output variables.
            y_pred (array): Array with predicted values of the response/output variables.
        
        Returns:
            None: This method does not return anything.
        '''
        if y_true.shape != y_pred.shape:
            logging.error("The shape of true and predicted arrays must be same!")
        
        self.y_true = y_true
        self.y_pred = y_pred    
        logging.info("ModelPerformance object is successfully instantiated.")

    def compute_r2_score(self, **kwargs: dict) -> np.ndarray:
        '''
        This method computes R2 score for the given data arrays.
        Args:
            This method takes optional key word arguments. If nothing is specified, the method uses default.
            multioutput (str): Method for returning R2 score. By default, 'raw_values' is used.
        Returns:
            r2 (array): Array with R2 score depending upon the aggregation method.
        '''
        multioutput = kwargs.get('multioutput', 'raw_values')

        r2 = r2_score(self.y_true, self.y_pred, multioutput=multioutput)
        logging.info(f"compute_r2_score() - R2 score is successfully computed. {r2}")

        return r2

    def compute_mae_score(self, **kwargs: dict) -> np.ndarray:
        '''
        This method computes Mean Absolute Error for the given arrays.
        Args:
            This method takes optional key word arguments. If nothing is specified, the method uses default.
            multioutput (str): Method for returning MAE score. By default, 'raw_values' is used.
        Returns:
            mae_Score (array): Array with MAE score depending upon the aggregation method.
        '''
        multioutput = kwargs.get('multioutput', 'raw_values')

        mae_score = mean_absolute_error(self.y_true, self.y_pred, multioutput=multioutput)
        logging.info(f"compute_mae_Score() - MAE score is successfully computed. {mae_score}")
        return mae_score

    def compute_mse_score(self, **kwargs: dict) -> np.ndarray:
        '''
        This method computes Mean Squared Error for the given arrays.
        Args:
            This method takes optional key word arguments. If nothing is specified, the method uses default.
            multioutput (str): Method for returning MSE score. By default, 'raw_values' is used.
        Returns:
            mse_score (array): Array with MSE score depening upon the aggregation method.
        '''
        multioutput = kwargs.get('multioutput', 'raw_values')

        mse_score = mean_squared_error(self.y_true, self.y_pred, multioutput=multioutput)
        logging.info(f"compute_mse_score() - MSE score is successfully computed. {mse_score}")
        return mse_score

    def compute_rmse_score(self) -> np.ndarray:
        '''
        This method computes Root Mean Squared Error for the given arrays.
        Args:
            This method does not take any arguments.
        Returns:
            rmse_Score (array): Array with RMSE score.
        '''

        mse_score = self.compute_mse_score(y_true=self.y_true, y_pred=self.y_pred)
        rmse_score = mse_score ** 0.5
        logging.info(f"compute_rmse_score() - RMSE score is successfully computed. {rmse_score}")
        return rmse_score

    # Compute Mean Squared Log Error
    # def compute_msle_score(self) -> np.ndarray:
    #     msle_score = mean_squared_log_error(self.y_true, self.y_pred)
    #     return msle_score

    def compute_mape_score(self) -> np.ndarray:
        '''
        This method computes Mean Absolute Percent Error for the given arrays.
        Args:
            This method does not take any arguments.
        Returns:
            mape_score (score): Array with MAPE score.
        '''

        mape_score = mean_absolute_percentage_error(self.y_true, self.y_pred)
        logging.info(f"compute_mape_score() - MAPE score is successfully computed. {mape_score}")
        return mape_score

    def compute_medae_score(self) -> np.ndarray:
        '''
        This method computes Median Absolute Error for the given arrays.
        Args:
            This method does not take any arguments.
        Returns:
            medae_score (score): Array with MedAE score.
        '''
        
        medae_score = median_absolute_error(self.y_true, self.y_pred)
        logging.info(f"compute_medae_score() - MedAE score is successfully computed. {medae_score}")
        return medae_score

    def compute_ev_score(self, **kwargs) -> np.ndarray:
        '''
        This method computes Explained Variance score.
        Args:
            This method takes optional key word arguments. If nothing is specified, the method uses default.
            multioutput (str): Method for returning EV score. By default, 'raw_values' is used.
        Returns:
            ev_Score (array): Array with EV score depending upon the aggregation method.
        '''
        multioutput = kwargs.get('multioutput', 'raw_values')
        ev_score = explained_variance_score(self.y_true, self.y_pred, multioutput=multioutput)
        logging.info(f"compute_ev_score() - EV score is successfully computed. {ev_score}")
        return ev_score

    def compute_all_scores(self, **kwargs) -> Dict[str, float]:
        '''
        This method computes all the performance metrics and returns them in a dictionary with a timestamp.
        Args:
            This method takes optional key word argument. These are same as the respective internal metric functions. Note that the same key word argument is applied to all the applicable functions.

        Returns:
            Dict[str, float]: Dictionary containing all computed metrics with a timestamp.
        '''
        multioutput = kwargs.get('multioutput', 'raw_values')
        scores = {"timestamp": datetime.now().isoformat(),
                  "r2_score": self.compute_r2_score(multioutput=multioutput),
                  "mae_score": self.compute_mae_score(multioutput=multioutput),
                  "mse_score": self.compute_mse_score(multioutput=multioutput),
                  "rmse_Score": self.compute_rmse_score(),
                  "mape_score": self.compute_mape_score(),
                  "medae_score": self.compute_medae_score(),
                  "ev_Score": self.compute_ev_score(multioutput=multioutput)}
        logging.info(f"compute_all_scores() - All scores are successfully computed. {scores}")
        
        return scores 