from __future__ import (
    annotations,  # for unquoted annotations in TYPE_CHECKING (forward_vars)
)
from backend.core._athena._imports import *
import backend.core._atlas.aggregates as at

####################

@dataclass(frozen=True)
class _Causes:
    """
    Represents a cause of a problem with a piece of equipment.

    The `problem` attribute describes the nature of the problem.
    Additional attributes can be added as needed to capture more details about the cause.
    """

    problem: str
    # add more dimensions as you see fit


# These are the potential causes/problems for a heat exchanger

IMPROPER_FLOW_RATE = _Causes(problem="Improper Flow Rate")
INCORRECT_FLOW_DIRECTION = _Causes(problem="Incorrect Flow Direction")
INSUFFICIENT_MAINTENANCE = _Causes(problem="Insufficient Maintenance")
CLOGGED_TUBES = _Causes(problem="Clogged Tubes")
AIR_ENTRAINMENT = _Causes(problem="Air Entrainment")
EXCESSIVE_TEMPERATURE_VARIATIONS = _Causes(problem="Excessive Temperature Variations")
TUBE_CORROSION = _Causes(problem="Tube Corrosion")
GASKET_FAILURE = _Causes(problem="Gasket Failure")
TUBE_WEAR_OR_EROSION = _Causes(problem="Tube Wear or Erosion")

# These are the potential causes/problems for a heater cooler

OUTLET_TEMPERATURE = _Causes(problem="Outlet Temperature")
PRESSURE_DROP_ACROSS_HEATER = _Causes(problem="Pressure Drop Across Heater")
EFFICIENCY_HEATER = _Causes(problem="Effciency of heater")
LOSS_ON_INLET_PRESSURE = _Causes(problem="Loss on Inlet Pressure")

# These are the potential causes/problems for a heater cooler

PRESSURE_DROP_ACROSS_COOLER = _Causes(problem="Pressure Drop Across Cooler")
EFFICIENCY_COOLER = _Causes(problem="Effciency of cooler")

# These are the potential causes/problems for a pump

BENT_SHAFT = _Causes(problem="Bent Shaft")
CASING_DISTORTED_FROM_EXCESSIVE_PIPE_STRAIN = _Causes(
    problem="Casing Distorted from Excessive Pipe Strain"
)
CAVITATION = _Causes(problem="Cavitation")
CLOGGED_IMPELLAR = _Causes(problem="Clogged Impellar")
DRIVER_IMBALANCE = _Causes(problem="Driver Imbalance")
ELECTRICAL_PROBLEMS_DRIVER = _Causes(problem="Electrical Problems (Driver)")
ENTRAINED_AIR_SUCTION_OR_SEAL_LEAKS = _Causes(
    problem="Entrained Air (Suction or Seal Leaks)"
)
HYDRAULIC_INSTABILITY = _Causes(problem="Hydraulic Instability")
INLET_STRAINER_PARTIALLY_CLOGGED = _Causes(problem="Inlet Strainer Partially Clogged")
INSUFFICIENT_FLOW_THROUGH_PUMP = _Causes(problem="Insufficient Flow Through Pump")
INSUFFICIENT_SUCTION_PRESSURE_NPSH = _Causes(
    problem="Insufficient Suction Pressure (NPSH)"
)
INSUFFICIENT_SUCTION_VOLUME = _Causes(problem="Insufficient Suction Volume")
INTERNAL_WEAR = _Causes(problem="Internal Wear")
LEAKAGE_IN_PIPING_VALVES_VESSELS = _Causes(problem="Leakage in Piping, Valves, Vessels")
MECHANICAL_DEFECTS_WORN_RUSTED_DEFECTIVE_BEARINGS = _Causes(
    problem="Mechanical Defects, Worn, Rusted, Defective Bearings"
)
MISALIGNMENT = _Causes(problem="Misalignment")
NONCONDENSIBLES_IN_LIQUID = _Causes(problem="Noncondensibles in Liquid")
ROTOR_IMBALANCE = _Causes(problem="Rotor Imbalance")
SPECIFIC_GRAVITY_TOO_HIGH = _Causes(problem="Specific Gravity Too High")
SPEED_TO_HIGH = _Causes(problem="Speed To High")
SPEED_TO_LOW = _Causes(problem="Speed To Low")
TOTAL_SYSTEM_HEAD_HIGHER_THAN_DESIGN = _Causes(
    problem="Total System Head Higher than Design"
)
TOTAL_SYSTEM_HEAD_LOWER_THAN_DESIGN = _Causes(
    problem="Total System Head Lower than Design"
)
VISCOSITY_TOO_HIGH = _Causes(problem="Viscosity Too High")

# These are the potential causes/problems for a control valve

EXCESSIVE_PROCESS_PRESSURE = _Causes(problem="Excessive Process Pressure")
IMPROPER_VALVE_POSITIONING = _Causes(problem="Improper Valve Positioning")
VALVE_PLUG_EROSION = _Causes(problem="Valve Plug Erosion")
CAVITATION_LIQUID_SERVICE = _Causes(problem="Cavitation (Liquid Service)")

# These are the potential causes/problems for a compressor

INADEQUATE_SUCTION_FLOW = _Causes(problem="Inadequate Suction Flow")
CLOGGED_AIR_FILTER = _Causes(problem="Clogged Air Filter")
VALVE_FAILURE_INLET_DISCHARGE = _Causes(problem="Valve Failure (Inlet/Discharge)")
EXCESSIVE_MOISTURE_IN_INLET_GAS = _Causes(problem="Excessive Moisture in Inlet Gas")
IMPROPER_LUBRICATION = _Causes(problem="Improper Lubrication")
EXCESSIVE_SUCTION_TEMPERATURE = _Causes(problem="Excessive Suction Temperature")
EXCESSIVE_DISCHARGE_PRESSURE = _Causes(problem="Excessive Discharge Pressure")
WORN_BEARINGS = _Causes(problem="Worn Bearings")
MISALIGNED_COMPONENTS = _Causes(problem="Misaligned Components")
ELECTRICAL_ISSUES_MOTOR = _Causes(problem="Electrical Issues (Motor)")
OVERLOADED_COMPRESSOR = _Causes(problem="Overloaded Compressor")
ROTOR_IMBALANCE = _Causes(problem="Rotor Imbalance")
EXCESSIVE_OIL_CARRYOVER = _Causes(problem="Excessive Oil Carryover")
EXCESSIVE_PRESSURE_PULSATION = _Causes(problem="Excessive Pressure Pulsation")
AIR_LEAKS = _Causes(problem="Air Leaks")
CONTAMINATED_OIL = _Causes(problem="Contaminated Oil")
GAS_RECIRCULATION = _Causes(problem="Gas Recirculation")
WORN_IMPELLERS = _Causes(problem="Worn Impellers")

# These are the potential causes/problems for a CSTR

INADEQUATE_AGITATION_SPEED = _Causes(problem="Inadequate Agitation Speed")
POOR_HEAT_TRANSFER_FLUID_FLOW = _Causes(problem="Poor Heat Transfer Fluid Flow")
HIGH_REACTION_EXOTHERMICITY = _Causes(problem="High Reaction Exothermicity")
HIGH_VISCOSITY_OF_REACTION_MIXTURE = _Causes(
    problem="High Viscosity of Reaction Mixture"
)
INCONSISTENT_FEED_COMPOSITION = _Causes(problem="Inconsistent Feed Composition")
REACTOR_FOULING = _Causes(problem="Reactor Fouling")
CLOGGING_OF_INLETS_OUTLETS = _Causes(problem="Clogging of Inlets/Outlets")
INSUFFICIENT_REACTANT_ADDITION_RATE = _Causes(
    problem="Insufficient Reactant Addition Rate"
)
CATALYST_DEACTIVATION = _Causes(problem="Catalyst Deactivation")
OVERPRESSURE_IN_REACTOR = _Causes(problem="Overpressure in Reactor")
EXCESSIVE_GAS_FORMATION = _Causes(problem="Excessive Gas Formation")
VAPOR_LOCK_IN_COOLING_JACKET = _Causes(problem="Vapor Lock in Cooling Jacket")
IMPROPER_CONTROL_OF_FLOW_RATE = _Causes(problem="Improper Control of Flow Rate")
AIR_WATER_INTRUSION_IN_REACTOR = _Causes(problem="Air/Water Intrusion in Reactor")
INCONSISTENT_CATALYST_LOADING = _Causes(problem="Inconsistent Catalyst Loading")
HIGH_SOLIDS_CONTENT_IN_FEED = _Causes(problem="High Solids Content in Feed")

# These are the potential causes/problems for a Turbine

IMBALANCE_IN_ROTATING_MASS = _Causes(problem="Imbalance in Rotating Mass")
MISALIGNMENT_OF_SHAFT = _Causes(problem="Misalignment of Shaft")
INADEQUATE_LUBRICATION = _Causes(problem="Inadequate Lubrication")
HIGH_OPERATING_TEMPERATURES = _Causes(problem="High Operating Temperatures")
INCONSISTENT_FUEL_QUALITY = _Causes(problem="Inconsistent Fuel Quality")
FOREIGN_OBJECT_DAMAGE = _Causes(problem="Foreign Object Damage")
VIBRATION_FROM_EXTERNAL_SOURCES = _Causes(problem="Vibration from External Sources")
EXCESSIVE_BACKPRESSURE = _Causes(problem="Excessive Backpressure")
WATER_INGRESS = _Causes(problem="Water Ingress")
TURBINE_BLADE_DAMAGE = _Causes(problem="Turbine Blade Damage")
EXCESSIVE_WEAR_ON_BLADES = _Causes(problem="Excessive Wear on Blades")
INCONSISTENT_MAINTENANCE_PRACTICES = _Causes(
    problem="Inconsistent Maintenance Practices"
)
IMPROPER_OPERATION_PROCEDURES = _Causes(problem="Improper Operation Procedures")
LOW_FLOW_CONDITIONS = _Causes(problem="Low Flow Conditions")
HEAT_EXCHANGER_FAILURES = _Causes(problem="Heat Exchanger Failures")
EXCESSIVE_WEAR_ON_BEARINGS = _Causes(problem="Excessive Wear on Bearings")
IMPROPER_TORQUE_ON_FASTENERS = _Causes(problem="Improper Torque on Fasteners")
AGING_EQUIPMENT = _Causes(problem="Aging Equipment")

# These are the potential causes/problems for a Vessel/Separator

IMPROPER_OPERATING_CONDITIONS = _Causes(problem="Improper Operating Conditions")
INADEQUATE_FEED_CONDITIONS = _Causes(problem="Inadequate Feed Conditions")
PHASE_DENSITY_MISMATCH = _Causes(problem="Phase Density Mismatch")
CLOGGED_INLET_OUTLET = _Causes(problem="Clogged Inlet/Outlet")
INADEQUATE_AGITATION = _Causes(problem="Inadequate Agitation")
MECHANICAL_DEFECTS_WORN_PARTS = _Causes(problem="Mechanical Defects (Worn Parts)")
LIQUID_PROPERTIES_VARIABILITY = _Causes(problem="Liquid Properties Variability")
IMPROPER_PHASE_FLOW_RATIOS = _Causes(problem="Improper Phase Flow Ratios")
HIGH_INLET_VELOCITIES = _Causes(problem="High Inlet Velocities")

# These are the potential causes/problems for a Conversion Reactor

# INCONSISTENT_FEED_COMPOSITION
# CLOGGING_OF_INLETS_OUTLETS
# INSUFFICIENT_REACTANT_ADDITION_RATE

# These are the potential causes/problems for an Air Cooler

IMPROPER_FAN_SPEED = _Causes(problem="Improper Fan Speed")
INSUFFICIENT_AIRFLOW = _Causes(problem="Insufficient Airflow")
TUBE_FOULING_INTERNAL = _Causes(problem="Tube Fouling (Internal)")
BLOCKED_FINS_EXTERNAL = _Causes(problem="Blocked Fins (External)")
AMBIENT_AIR_TEMPERATURE_TOO_HIGH = _Causes(problem="Ambient Air Temperature Too High")
AMBIENT_AIR_TEMPERATURE_TOO_LOW = _Causes(problem="Ambient Air Temperature Too Low")
FAN_MOTOR_FAILURE = _Causes(problem="Fan Motor Failure")
EXCESSIVE_PROCESS_FLUID_FLOW_RATE = _Causes(problem="Excessive Process Fluid Flow Rate")
EXCESSIVE_DUST_DEBRIS_ON_FINS = _Causes(problem="Excessive Dust/Debris on Fins")
INADEQUATE_MAINTENANCE = _Causes(problem="Inadequate Maintenance")
FAN_BLADE_DAMAGE = _Causes(problem="Fan Blade Damage")

# These are the potential causes/problems for a PFR

INADEQUATE_CATALYST_LOADING = _Causes(problem="Inadequate Catalyst Loading")
HIGH_REACTION_EXOTHERMICITY = _Causes(problem="High Reaction Exothermicity")
UNEVEN_CATALYST_BED_DISTRIBUTION = _Causes(problem="Uneven Catalyst Bed Distribution")
INCONSISTENT_FEED_COMPOSITION = _Causes(problem="Inconsistent Feed Composition")
HIGH_SOLIDS_CONTENT_IN_FEED = _Causes(problem="High Solids Content in Feed")
INSUFFICIENT_REACTANT_FLOW_RATE = _Causes(problem="Insufficient Reactant Flow Rate")
REACTOR_FOULING = _Causes(problem="Reactor Fouling")
INCONSISTENT_CATALYST_ACTIVITY = _Causes(problem="Inconsistent Catalyst Activity")
CLOGGING_OF_INLET_OUTLET = _Causes(problem="Clogging of Inlet/Outlet")
OVERPRESSURE_IN_REACTOR = _Causes(problem="Overpressure in Reactor")
LEAKING_SEALS = _Causes(problem="Leaking Seals")
EXCESSIVE_GAS_FORMATION = _Causes(problem="Excessive Gas Formation")
THERMAL_RUNAWAY_REACTIONS = _Causes(problem="Thermal Runaway Reactions")
INADEQUATE_HEAT_TRANSFER = _Causes(problem="Inadequate Heat Transfer")
HOT_SPOTS_IN_REACTOR = _Causes(problem="Hot Spots in Reactor")
CATALYST_POISONING = _Causes(problem="Catalyst Poisoning")
IMPROPER_CONTROL_OF_FEED_FLOW_RATE = _Causes(
    problem="Improper Control of Feed Flow Rate"
)
AIR_OR_WATER_INTRUSION = _Causes(problem="Air or Water Intrusion")
PARTICLE_ATTRITION_IN_CATALYST_BED = _Causes(
    problem="Particle Attrition in Catalyst Bed"
)

# This are the potential causes/problems for a Distillation Column

EXCESSIVE_FEED_RATE = _Causes(problem="Excessive Feed Rate")
HIGH_LIQUID_VISCOSITY = _Causes(problem="High Liquid Viscosity")
INADEQUATE_REFLUX_RATIO = _Causes(problem="Inadequate Reflux Ratio")
HIGH_VAPOR_FLOW = _Causes(problem="High Vapor Flow")
TRAY_FOULING_OR_BLOCKAGE = _Causes(problem="Tray Fouling or Blockage")
CONDENSER_INEFFICIENCY = _Causes(problem="Condenser Inefficiency")
REBOILER_FOULING = _Causes(problem="Reboiler Fouling")
IMPROPER_TEMPERATURE_PROFILE = _Causes(problem="Improper Temperature Profile")
POOR_FEED_COMPOSITION_CONTROL = _Causes(problem="Poor Feed Composition Control")
EXCESSIVE_HEAT_INPUT_REBOILER = _Causes(problem="Excessive Heat Input (Reboiler)")
OVERHEAD_CONDENSER_CAPACITY_ISSUES = _Causes(
    problem="Overhead Condenser Capacity Issues"
)
FOAMING_IN_THE_COLUMN = _Causes(problem="Foaming in the Column")
POOR_LIQUID_VAPOR_DISTRIBUTION = _Causes(problem="Poor Liquid/Vapor Distribution")
HIGH_SOLIDS_IN_FEED = _Causes(problem="High Solids in Feed")
EXCESSIVE_CONDENSER_PRESSURE_DROP = _Causes(problem="Excessive Condenser Pressure Drop")
IMPROPER_REBOILER_DUTY = _Causes(problem="Improper Reboiler Duty")
COLUMN_MALDISTRIBUTION = _Causes(problem="Column Maldistribution")
HYDRAULIC_INSTABILITIES = _Causes(problem="Hydraulic Instabilities")
COOLING_WATER_INSUFFICIENCY = _Causes(problem="Cooling Water Insufficiency")
EXCESSIVE_DOWNCOMER_LIQUID_BACK_UP = _Causes(
    problem="Excessive Downcomer Liquid Back-Up"
)

# This are the potential causes/problems for a Distillation Column

INCORRECT_MINIMUM_REFLUX_RATIO_OR_DISTILLATION_BOUNDARY_ASSUMPTIONS = _Causes(
    problem="Incorrect minimum reflux ratio or distillation boundary assumptions"
)
INACCURATE_OPERATING_PRESSURE = _Causes(problem="Inaccurate operating pressure")
WRONG_ESTIMATES_OF_LIGHT_HEAVY_KEY_COMPONENTS = _Causes(
    problem="Wrong estimates of light/heavy key components"
)
INCORRECT_FEED_CONDITION_ASSUMPTIONS_EG_SUBCOOLED_SUPERHEATED = _Causes(
    problem="Incorrect feed condition assumptions (e.g., subcooled, superheated)"
)
INCORRECT_CONDENSER_SETUP = _Causes(problem="Incorrect condenser setup")
IMPROPER_ENERGY_BALANCES = _Causes(problem="Improper energy balances")
INCORRECT_PRODUCT_PURITY_RECOVERY_TARGETS = _Causes(
    problem="Incorrect product purity/recovery targets"
)

# These are the potential causes/problems for a Input Stream
INCORRECT_SUBOPTIMAL_SETPOINTS = _Causes(
    problem="Incorrect and/or suboptimal setpoint."
)


# We then create a menu of causes per equipment type.

# This is the possible problems which might occur in a Heat Exchanger
heat_exchanger_diagnosis = {
    at.ContVarSpecEnum.ColdFluidOutletTemperature: {  # Do we need a logic over here for the insufficient heat transfer scenario something like ColdFluidOutletTemp >= SetPointOfColdFluidTemp
        IMPROPER_FLOW_RATE,
        INCORRECT_FLOW_DIRECTION,
        INSUFFICIENT_MAINTENANCE,
        CLOGGED_TUBES,
        AIR_ENTRAINMENT,
    },
    at.ContVarSpecEnum.HotFluidOutletTemperature: {  # Do we need a logic over here for the insufficient heat transfer scenario something like HotFluidOutletTemp <= SetPointOfHotFluidTemp
        IMPROPER_FLOW_RATE,
        INCORRECT_FLOW_DIRECTION,
        INSUFFICIENT_MAINTENANCE,
        CLOGGED_TUBES,
        AIR_ENTRAINMENT,
    },
    at.ContVarSpecEnum.HotFluidPressureDrop: {
        IMPROPER_FLOW_RATE,
        INSUFFICIENT_MAINTENANCE,
        CLOGGED_TUBES,
        TUBE_WEAR_OR_EROSION,
    },
    at.ContVarSpecEnum.ColdFluidPressureDrop: {
        IMPROPER_FLOW_RATE,
        INSUFFICIENT_MAINTENANCE,
        CLOGGED_TUBES,
        TUBE_WEAR_OR_EROSION,
    },
    at.ContVarSpecEnum.ColdFluidOutletTemperature: {  # Do we need a logic over here for the overheating scenario something like ColdFluidOutletTemp <= SetPointOfColdFluidTemp
        IMPROPER_FLOW_RATE,
        CLOGGED_TUBES,
        AIR_ENTRAINMENT,
        EXCESSIVE_TEMPERATURE_VARIATIONS,
    },
    at.ContVarSpecEnum.HotFluidOutletTemperature: {  # Do we need a logic over here for the overheating scenario something like HotFluidOutletTemp >= SetPointOfHotFluidTemp
        IMPROPER_FLOW_RATE,
        CLOGGED_TUBES,
        AIR_ENTRAINMENT,
        EXCESSIVE_TEMPERATURE_VARIATIONS,
    },
    at.ContVarSpecEnum.TubeFouling: {
        IMPROPER_FLOW_RATE,
        INSUFFICIENT_MAINTENANCE,
        CLOGGED_TUBES,
    },
    at.ContVarSpecEnum.GlobalHeatTransferCoefficient: {
        INSUFFICIENT_MAINTENANCE,
        CLOGGED_TUBES,
        TUBE_WEAR_OR_EROSION,
    },
    at.ContVarSpecEnum.HeatLoss: {
        INSUFFICIENT_MAINTENANCE,
    },
}

heater_diagnosis = {
    at.ContVarSpecEnum.OutletTemperature: {
        IMPROPER_FLOW_RATE,
        OUTLET_TEMPERATURE,
        EFFICIENCY_HEATER,
    },
    at.ContVarSpecEnum.PressureDrop: {
        PRESSURE_DROP_ACROSS_HEATER,
        LOSS_ON_INLET_PRESSURE,
    },
}

cooler_diagnosis = {
    at.ContVarSpecEnum.OutletTemperature: {
        IMPROPER_FLOW_RATE,
        OUTLET_TEMPERATURE,
        EFFICIENCY_COOLER,
    },
    at.ContVarSpecEnum.PressureDrop: {
        PRESSURE_DROP_ACROSS_COOLER,
        LOSS_ON_INLET_PRESSURE,
    },
}

pump_diagnosis = {
    at.ContVarSpecEnum.OutletPressure: {
        CAVITATION,
        CLOGGED_IMPELLAR,
        ENTRAINED_AIR_SUCTION_OR_SEAL_LEAKS,
        INLET_STRAINER_PARTIALLY_CLOGGED,
        INSUFFICIENT_SUCTION_PRESSURE_NPSH,
        INSUFFICIENT_SUCTION_VOLUME,
        INTERNAL_WEAR,
        LEAKAGE_IN_PIPING_VALVES_VESSELS,
        NONCONDENSIBLES_IN_LIQUID,
        SPECIFIC_GRAVITY_TOO_HIGH,
        SPEED_TO_LOW,
        TOTAL_SYSTEM_HEAD_HIGHER_THAN_DESIGN,
        VISCOSITY_TOO_HIGH,
    },
    at.ContVarSpecEnum.Volumetric_flow_rate: {  # TODO Confirm on the property/parameter label
        CAVITATION,
        CLOGGED_IMPELLAR,
        ENTRAINED_AIR_SUCTION_OR_SEAL_LEAKS,
        INLET_STRAINER_PARTIALLY_CLOGGED,
        INSUFFICIENT_SUCTION_PRESSURE_NPSH,
        INSUFFICIENT_SUCTION_VOLUME,
        INTERNAL_WEAR,
        LEAKAGE_IN_PIPING_VALVES_VESSELS,
        NONCONDENSIBLES_IN_LIQUID,
        SPEED_TO_LOW,
        TOTAL_SYSTEM_HEAD_HIGHER_THAN_DESIGN,
        VISCOSITY_TOO_HIGH,
    },
    at.ContVarSpecEnum.Volumetric_flow_rate: {  # TODO Confirm on the property/parameter label
        CAVITATION,
        CLOGGED_IMPELLAR,
        INSUFFICIENT_SUCTION_PRESSURE_NPSH,
        INSUFFICIENT_SUCTION_VOLUME,
        LEAKAGE_IN_PIPING_VALVES_VESSELS,
        SPEED_TO_LOW,
        TOTAL_SYSTEM_HEAD_HIGHER_THAN_DESIGN,
    },
    at.ContVarSpecEnum.PowerRequired: {
        BENT_SHAFT,
        CASING_DISTORTED_FROM_EXCESSIVE_PIPE_STRAIN,
        CLOGGED_IMPELLAR,
        ELECTRICAL_PROBLEMS_DRIVER,
        INTERNAL_WEAR,
        MECHANICAL_DEFECTS_WORN_RUSTED_DEFECTIVE_BEARINGS,
        MISALIGNMENT,
        SPECIFIC_GRAVITY_TOO_HIGH,
        SPEED_TO_HIGH,
        TOTAL_SYSTEM_HEAD_LOWER_THAN_DESIGN,
        VISCOSITY_TOO_HIGH,
    },
    at.ContVarSpecEnum.OutletTemperature: {
        CAVITATION,
        INLET_STRAINER_PARTIALLY_CLOGGED,
        INSUFFICIENT_FLOW_THROUGH_PUMP,
        INSUFFICIENT_SUCTION_VOLUME,
        TOTAL_SYSTEM_HEAD_HIGHER_THAN_DESIGN,
        TOTAL_SYSTEM_HEAD_LOWER_THAN_DESIGN,
    },
}

valve_diagnosis = {
    at.ContVarSpecEnum.PressureDrop: {
        EXCESSIVE_PROCESS_PRESSURE,
        IMPROPER_VALVE_POSITIONING,
        VALVE_PLUG_EROSION,
        CAVITATION_LIQUID_SERVICE,
    }
}

compressor_diagnosis = {
    at.ContVarSpecEnum.OutletPressure: {
        INADEQUATE_SUCTION_FLOW,
        CLOGGED_AIR_FILTER,
        VALVE_FAILURE_INLET_DISCHARGE,
        EXCESSIVE_SUCTION_TEMPERATURE,
        AIR_LEAKS,
        GAS_RECIRCULATION,
        WORN_IMPELLERS,
    },
    at.ContVarSpecEnum.PowerRequired: {
        VALVE_FAILURE_INLET_DISCHARGE,
        EXCESSIVE_SUCTION_TEMPERATURE,
        EXCESSIVE_DISCHARGE_PRESSURE,
        ELECTRICAL_ISSUES_MOTOR,
        OVERLOADED_COMPRESSOR,
        GAS_RECIRCULATION,
    },
    at.ContVarSpecEnum.Pressure: {
        INADEQUATE_SUCTION_FLOW,
        CLOGGED_AIR_FILTER,
        EXCESSIVE_MOISTURE_IN_INLET_GAS,
    },
}

# CSTR_diagnosis = {
#     at.ContVarSpecEnum.Conversion: {
#         INADEQUATE_AGITATION_SPEED,
#         HIGH_VISCOSITY_OF_REACTION_MIXTURE,
#         INCONSISTENT_FEED_COMPOSITION,
#         REACTOR_FOULING,
#         INSUFFICIENT_REACTANT_ADDITION_RATE,
#         AIR_WATER_INTRUSION_IN_REACTOR,
#         HIGH_SOLIDS_CONTENT_IN_FEED,
#     },
#     at.ContVarSpecEnum.OutletTemperature: {
#         POOR_HEAT_TRANSFER_FLUID_FLOW,
#         HIGH_REACTION_EXOTHERMICITY,
#         OVERPRESSURE_IN_REACTOR,
#         EXCESSIVE_GAS_FORMATION,
#         VAPOR_LOCK_IN_COOLING_JACKET,
#         IMPROPER_CONTROL_OF_FLOW_RATE,
#     },
#     at.ContVarSpecEnum.Conversion: {
#         INADEQUATE_AGITATION_SPEED,
#         POOR_HEAT_TRANSFER_FLUID_FLOW,
#         HIGH_VISCOSITY_OF_REACTION_MIXTURE,
#         INCONSISTENT_FEED_COMPOSITION,
#         REACTOR_FOULING,
#         INSUFFICIENT_REACTANT_ADDITION_RATE,
#         CATALYST_DEACTIVATION,
#         OVERPRESSURE_IN_REACTOR,
#         EXCESSIVE_GAS_FORMATION,
#         IMPROPER_CONTROL_OF_FLOW_RATE,
#         INCONSISTENT_CATALYST_LOADING,
#         HIGH_SOLIDS_CONTENT_IN_FEED,
#     },
#     at.ContVarSpecEnum.HeatLoad: {
#         POOR_HEAT_TRANSFER_FLUID_FLOW,
#         HIGH_REACTION_EXOTHERMICITY,
#         VAPOR_LOCK_IN_COOLING_JACKET,
#     },
#     at.PropertyLabelEnum.VOLUMETRIC_FLOW_RATE: {
#         REACTOR_FOULING,
#         CLOGGING_OF_INLETS_OUTLETS,
#         HIGH_SOLIDS_CONTENT_IN_FEED,
#     },
#     at.ContVarSpecEnum.PressureDrop: {REACTOR_FOULING, OVERPRESSURE_IN_REACTOR},
# }

turbine_diagnosis = {
    at.ContVarSpecEnum.PowerGenerated: {
        INCONSISTENT_FUEL_QUALITY,
        EXCESSIVE_BACKPRESSURE,
        TURBINE_BLADE_DAMAGE,
        INCONSISTENT_MAINTENANCE_PRACTICES,
        IMPROPER_OPERATION_PROCEDURES,
        LOW_FLOW_CONDITIONS,
        AGING_EQUIPMENT,
    },
    at.ContVarSpecEnum.Mass_flow_rate: {EXCESSIVE_BACKPRESSURE},
    at.ContVarSpecEnum.AdiabaticEff: {EXCESSIVE_WEAR_ON_BLADES},
    at.ContVarSpecEnum.Mass_flow_rate: {  # This is for fuel consumption → gas turbine
        INCONSISTENT_FUEL_QUALITY
    },
}

separator_diagnosis = {
    at.ContVarSpecEnum.Mass_flow_rate: {  # TODO It will be ratio of gas to liquid mass flow rates
        IMPROPER_OPERATING_CONDITIONS,
        INADEQUATE_FEED_CONDITIONS,
        PHASE_DENSITY_MISMATCH,
        CLOGGED_INLET_OUTLET,
        INADEQUATE_AGITATION,
        MECHANICAL_DEFECTS_WORN_PARTS,
        LIQUID_PROPERTIES_VARIABILITY,
        IMPROPER_PHASE_FLOW_RATIOS,
        HIGH_INLET_VELOCITIES,
    },
    at.ContVarSpecEnum.PowerRequired: {
        IMPROPER_OPERATING_CONDITIONS,
        CLOGGED_INLET_OUTLET,
        MECHANICAL_DEFECTS_WORN_PARTS,
    },
    at.ContVarSpecEnum.PressureDrop: {  # TODO separator doesn't have a parameter label as pressuredrop so can we have a new variable as diff = inlet-outlet pressure
        CLOGGED_INLET_OUTLET
    },
}

# Removing as the enum may not be available
# conversion_reactor_diagnosis = {
#     at.ContVarSpecEnum.ComponentConversions: {
#         INCONSISTENT_FEED_COMPOSITION,
#         INSUFFICIENT_REACTANT_ADDITION_RATE,
#     },
#     at.PropertyLabelEnum.VOLUMETRIC_FLOW_RATE: {CLOGGING_OF_INLETS_OUTLETS},
# }

air_cooler_diagnosis = {
    at.ContVarSpecEnum.OutletFluidTemperature: {
        IMPROPER_FAN_SPEED,
        INSUFFICIENT_AIRFLOW,
        TUBE_FOULING_INTERNAL,
        BLOCKED_FINS_EXTERNAL,
        AMBIENT_AIR_TEMPERATURE_TOO_HIGH,
        FAN_MOTOR_FAILURE,
        EXCESSIVE_PROCESS_FLUID_FLOW_RATE,
        EXCESSIVE_DUST_DEBRIS_ON_FINS,
        INADEQUATE_MAINTENANCE,
    },
    at.ContVarSpecEnum.OutletFluidTemperature: {
        INSUFFICIENT_AIRFLOW,
        AMBIENT_AIR_TEMPERATURE_TOO_LOW,
    },
    at.ContVarSpecEnum.TubeFouling: {
        TUBE_FOULING_INTERNAL,
        BLOCKED_FINS_EXTERNAL,
        EXCESSIVE_DUST_DEBRIS_ON_FINS,
        INADEQUATE_MAINTENANCE,
    },
    at.ContVarSpecEnum.ActualAirFlow: {
        BLOCKED_FINS_EXTERNAL,
        EXCESSIVE_DUST_DEBRIS_ON_FINS,
        INADEQUATE_MAINTENANCE,
    },
    at.ContVarSpecEnum.ElectricalPowerLoad: {
        IMPROPER_FAN_SPEED,
        INSUFFICIENT_AIRFLOW,
        FAN_MOTOR_FAILURE,
        FAN_BLADE_DAMAGE,
    },
    at.ContVarSpecEnum.Volumetric_flow_rate: {
        TUBE_FOULING_INTERNAL,
        EXCESSIVE_PROCESS_FLUID_FLOW_RATE,
        INADEQUATE_MAINTENANCE,
    },
}
# Removing as the enum may not be available
# pfr_diagnosis = {
#     at.ContVarSpecEnum.ComponentConversions: {
#         INADEQUATE_CATALYST_LOADING,
#         UNEVEN_CATALYST_BED_DISTRIBUTION,
#         INCONSISTENT_FEED_COMPOSITION,
#         INSUFFICIENT_REACTANT_FLOW_RATE,
#         REACTOR_FOULING,
#         INCONSISTENT_CATALYST_ACTIVITY,
#         CATALYST_POISONING,
#         IMPROPER_CONTROL_OF_FEED_FLOW_RATE,
#         PARTICLE_ATTRITION_IN_CATALYST_BED,
#     },
#     at.ContVarSpecEnum.OutletTemperature: {
#         HIGH_REACTION_EXOTHERMICITY,
#         OVERPRESSURE_IN_REACTOR,
#         EXCESSIVE_GAS_FORMATION,
#         THERMAL_RUNAWAY_REACTIONS,
#         INADEQUATE_HEAT_TRANSFER,
#         HOT_SPOTS_IN_REACTOR,
#     },
#     at.ContVarSpecEnum.PressureDrop: {
#         HIGH_SOLIDS_CONTENT_IN_FEED,
#         REACTOR_FOULING,
#         CLOGGING_OF_INLET_OUTLET,
#         OVERPRESSURE_IN_REACTOR,
#         LEAKING_SEALS,
#         AIR_OR_WATER_INTRUSION,
#     },
#     at.ContVarSpecEnum.ReactionRate: {
#         INADEQUATE_CATALYST_LOADING,
#         UNEVEN_CATALYST_BED_DISTRIBUTION,
#         INCONSISTENT_FEED_COMPOSITION,
#         INCONSISTENT_CATALYST_ACTIVITY,
#         CATALYST_POISONING,
#         PARTICLE_ATTRITION_IN_CATALYST_BED,
#     },
#     at.ContVarSpecEnum.ComponentConversions: {
#         INADEQUATE_CATALYST_LOADING,
#         UNEVEN_CATALYST_BED_DISTRIBUTION,
#         INCONSISTENT_FEED_COMPOSITION,
#         INSUFFICIENT_REACTANT_FLOW_RATE,
#         REACTOR_FOULING,
#         INCONSISTENT_CATALYST_ACTIVITY,
#         CATALYST_POISONING,
#         IMPROPER_CONTROL_OF_FEED_FLOW_RATE,
#         PARTICLE_ATTRITION_IN_CATALYST_BED,
#     },
# }

distillation_column_diagnosis = {
    at.ContVarSpecEnum.ColumnPressureDrop: {
        EXCESSIVE_FEED_RATE,
        HIGH_LIQUID_VISCOSITY,
        HIGH_VAPOR_FLOW,
        TRAY_FOULING_OR_BLOCKAGE,
        REBOILER_FOULING,
        OVERHEAD_CONDENSER_CAPACITY_ISSUES,
        EXCESSIVE_CONDENSER_PRESSURE_DROP,
        IMPROPER_REBOILER_DUTY,
        HYDRAULIC_INSTABILITIES,
        COOLING_WATER_INSUFFICIENCY,
    },
    at.ContVarSpecEnum.CondenserSpecValue: {
        INADEQUATE_REFLUX_RATIO,
        CONDENSER_INEFFICIENCY,
        IMPROPER_TEMPERATURE_PROFILE,
        OVERHEAD_CONDENSER_CAPACITY_ISSUES,
    },
    at.ContVarSpecEnum.ReboilerSpecValue: {
        REBOILER_FOULING,
        HIGH_SOLIDS_IN_FEED,
        IMPROPER_REBOILER_DUTY,
    },
    at.ContVarSpecEnum.CondenserDuty: {
        EXCESSIVE_FEED_RATE,
        INADEQUATE_REFLUX_RATIO,
        HIGH_VAPOR_FLOW,
        CONDENSER_INEFFICIENCY,
        REBOILER_FOULING,
        IMPROPER_TEMPERATURE_PROFILE,
        POOR_FEED_COMPOSITION_CONTROL,
        EXCESSIVE_HEAT_INPUT_REBOILER,
        IMPROPER_REBOILER_DUTY,
        COOLING_WATER_INSUFFICIENCY,
    },
    at.ContVarSpecEnum.ReboilerDuty: {
        EXCESSIVE_FEED_RATE,
        INADEQUATE_REFLUX_RATIO,
        HIGH_VAPOR_FLOW,
        CONDENSER_INEFFICIENCY,
        REBOILER_FOULING,
        IMPROPER_TEMPERATURE_PROFILE,
        POOR_FEED_COMPOSITION_CONTROL,
        EXCESSIVE_HEAT_INPUT_REBOILER,
        IMPROPER_REBOILER_DUTY,
        COOLING_WATER_INSUFFICIENCY,
    },
}

shortcut_column_diagnosis = {
    at.ContVarSpecEnum.ReboilerDuty: {
        INCORRECT_MINIMUM_REFLUX_RATIO_OR_DISTILLATION_BOUNDARY_ASSUMPTIONS,
        INACCURATE_OPERATING_PRESSURE,
        INCORRECT_FEED_CONDITION_ASSUMPTIONS_EG_SUBCOOLED_SUPERHEATED,
        IMPROPER_ENERGY_BALANCES,
    },
    at.ContVarSpecEnum.CondenserDuty: {
        INACCURATE_OPERATING_PRESSURE,
        INCORRECT_FEED_CONDITION_ASSUMPTIONS_EG_SUBCOOLED_SUPERHEATED,
        INCORRECT_CONDENSER_SETUP,
        IMPROPER_ENERGY_BALANCES,
    },
    at.ContVarSpecEnum.ActualNumberOfTrays: {
        WRONG_ESTIMATES_OF_LIGHT_HEAVY_KEY_COMPONENTS,
        INCORRECT_PRODUCT_PURITY_RECOVERY_TARGETS,
    },
}

input_stream_diagnosis = {
    at.ContVarSpecEnum.Temperature: {INCORRECT_SUBOPTIMAL_SETPOINTS},
    at.ContVarSpecEnum.Pressure: {INCORRECT_SUBOPTIMAL_SETPOINTS},
    at.ContVarSpecEnum.Mass_flow_rate: {INCORRECT_SUBOPTIMAL_SETPOINTS},
    at.CompoundMixSpecEnum.COMPOUNDMIX: {INCORRECT_SUBOPTIMAL_SETPOINTS},
}

# Add diagnosis per equipt


####################

T = TypeVar("T", bound=at.ENTBase)


class Diagnostics:

    def __init__(
        self,
        entity_class: Type[T],
        variable_contributions: Dict[at.VOBaseVariable, float],
        diagnostic_threshold: Optional[float] = 5.0,
    ):
        """
        This instantiates diagnostics object to generate causes using the given information.

        Args:

            entity_class (object type): Type of the entity class.
            variable_contributions (dict): A dictionary of contributions with Variable type as key and its contribution as value.
            diagnostic_threshold (float): A threshold to consider a specific variable for getting its cause.

        Returns:

        """
        self.entity_class = entity_class
        self.variable_contributions = variable_contributions
        # Checking for existence of user-defined diagnostic threshold
        if diagnostic_threshold == None:
            self.diagnostic_threshold = 0.0
        else:
            self.diagnostic_threshold = diagnostic_threshold

        logging.info(f"Diagnostics object is created successfully.")

    def _get_equipment_lookup(self) -> Dict[at.ENTBaseEquipment, Set[_Causes]]:
        """
        Get the diagnosis lookup for the given equipment class.

        Args:

        Returns:
            diagnosis_lookup (dict): A lookup dictionary with Equipment Type as key and set of causes as values.
        """
        diagnosis_lookup = {
            at.Pump: pump_diagnosis,
            at.Valve: valve_diagnosis,
            at.Compressor: compressor_diagnosis,
            at.Expander: turbine_diagnosis,
            at.Heater: heater_diagnosis,
            at.Cooler: cooler_diagnosis,
            at.HeatExchanger: heat_exchanger_diagnosis,
            at.AirCooler2: air_cooler_diagnosis,
            at.Vessel: separator_diagnosis,
            at.ShortcutColumn: shortcut_column_diagnosis,
            at.InputStream: input_stream_diagnosis,
        }

        # TODO: at.ConversionReactor: conversion_reactor_diagnosis,
        # TODO add CSTR class here
        # TODO add PFR class here
        # TODO add Distillation column class here
        # Add other equipment types here

        # Get lookup for the equipment type.
        entity_lookup = diagnosis_lookup.get(self.entity_class, {})

        logging.info(f"Equipment lookup dictionary is obtained {entity_lookup}.")

        return entity_lookup

    def run(self) -> Dict[at.VOBaseVariable, str]:
        """
        Run the diagnostic analysis on the provided equipment.

        Args:

        Returns:
            result (dict): A dictionary containing diagnosis with variable as key and set of causes as values.
        """
        # TODO: Return default text for the variables that are not available in the diagnosis list.
        # Get the correct equipment reference
        equipment_diagnosis_lookup = self._get_equipment_lookup()

        # Filter variable contributions for diagnostic threshold
        filtered_variables = [
            var
            for var in self.variable_contributions.keys()
            if self.variable_contributions[var] >= self.diagnostic_threshold
        ]

        logging.info(f"The variables are filtered successfully {filtered_variables}.")

        # Iterate through variable contributions using UNION or appropriate logic
        result = {}
        for var in filtered_variables:
            causes = equipment_diagnosis_lookup.get(var._variable_enum, set())

            logging.info(f"The causes for {var} are obtained successufully {causes}")
            result[var] = ", ".join(cause.problem for cause in causes)

        logging.info(
            f"The causes for all the filtered variables are obtained {result}."
        )

        return result


if __name__ == "__main__":
    from backend.core._atlas.aggregates import *
    from backend.core._atlas.aggregates import VariableUIDCollection

    test_heater = HeatExchanger("HT-01")

    test_var1 = test_heater.get_variable(at.ContVarSpecEnum.HeatLoss)

    test_diagnostics = Diagnostics(
        entity_class=type(test_heater),
        variable_contributions={test_var1: 100.0},
        diagnostic_threshold=0.0,
    )
    test_result = test_diagnostics.run()

    pprint(test_result)

    test_mat_stream = InputStream("Fuel BL->M-100")
    test_var2 = test_mat_stream.get_variable(at.ContVarSpecEnum.Mass_flow_rate)

    test_diagnostics2 = Diagnostics(
        entity_class=type(test_mat_stream),
        variable_contributions={test_var2: 100.0},
        diagnostic_threshold=0.0,
    )

    test_result2 = test_diagnostics2.run()

    pprint(test_result2)
