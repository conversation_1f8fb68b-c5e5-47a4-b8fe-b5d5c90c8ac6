from __future__ import (
    annotations,  # for unquoted annotations in TYPE_CHECKING (forward_vars)
)
from ._imports import *

#TODO: Inlcude data persistence layer as suggested in Notion doc.

class EconomicAnalytics:
    def __init__(self, raw_mat_names: List[str], prd_mat_names: List[str]) -> None:
        '''
        This instantiates the EconomicAnalytics object and user can call various functions to get economic metrics.

        Args:
            raw_mat_names (list): List of r_mat raw material names (strings).
            prd_mat_names (list): List of p_mat product material names (strings).
        
        Returns:
            None: This does not return anything.

        Raises:
            This does not raise any exceptions.
        '''
        self.__raw_mats: List[str] = raw_mat_names
        self.__prd_mats: List[str] = prd_mat_names
    
    def compute_raw_mat_costs(self, raw_mat_flow: np.ndarray, raw_mat_unit_cost: np.ndarray) -> np.ndarray:
        '''
        This method computes the raw material cost using various raw material flowrates and their unit costs.

        Args:
            raw_mat_flow (array): Array of raw material flowrates of size (n_samples, r_mat).
            raw_mat_unit_cost (array): Array of raw material unit costs of size (1, r_mat).
        
        Returns:
            raw_mat_costs (array): Array of raw material costs of size (n_sample, r_mat).

        '''

        if raw_mat_flow.shape[0] != len(self.__raw_mats):
            logging.error("Input material flow data must have same number of columns as number of raw materials!")
            return None
        
        if raw_mat_unit_cost.shape[0] != len(self.__raw_mats):
            logging.error("Input material cost data must have same number of columns as number of raw materials!")
            return None

        raw_mat_costs = np.multiply(raw_mat_flow, raw_mat_unit_cost)
        logging.info(f"compute_raw_mat_costs() - Raw material costs are computed successfully. {raw_mat_costs}")

        return raw_mat_costs
    
    def compute_prd_mat_prices(self, prd_mat_flow: np.ndarray, prd_mat_unit_price: np.ndarray) -> np.ndarray:
        '''
        This method computes the product material costs using various product material flowrates and their unit costs.

        Args:
            prd_mat_flow (array): Array of product material flowrates of size (n_samples, p_mat).
            prd_mat_unit_cost (array): Array of product material unit costs of size (1, p_mat).
        
        Returns:
            prd_mat_costs (array): Array of product material costs of size (n_sample, p_mat).

        '''
        if prd_mat_flow.shape[0] != len(self.__prd_mats):
            logging.error("Input material flow data must have same number of columns as number of product materials!")
        
        if prd_mat_unit_price.shape[0] == len(self.__prd_mats):
            logging.error("Input amterial cost data must have same number of columns as number of product materials!")
        
        prd_mat_prices = np.multiply(prd_mat_flow, prd_mat_unit_price)
        logging.info(f"compute_prd_mat_prices() - Product material prices are computed successfully. {prd_mat_prices}")

        return prd_mat_prices
    
    def __compute_aggregate(self, mat_cost: np.ndarray) -> np.ndarray:
        '''
        This method computes the aggregate material costs given the material cost/price array.

        Args:
            mat_cost (array): Array of material costs of size (n_samples, n_mat).
        
        Returns:
            aggr_mat_cost (array): Array of aggregate material costs of size (n_samples,).

        '''

        aggr_mat_cost = np.sum(mat_cost, axis=1)
        aggr_mat_cost = aggr_mat_cost.reshape(-1, 1)

        logging.info(f"__compute_aggregate() - Aggregate costs are computed successfully. {aggr_mat_cost}")

        return aggr_mat_cost

    def compute_gross_revenue(self, raw_mat_costs: np.ndarray, prd_mat_prices: np.ndarray) -> np.ndarray:
        '''
        This method computes gross revenue using raw material costs and product material prices.

        Args:
            raw_mat_costs (array): Array with aggregate raw materials costs of size (n_sample,).
            prd_mat_prices (array): Array with aggregate prdoduct materials prices of size (n_sample,).
        
        Returns:
            gross_revenue (array): Array with gross revenue of size (n_samples, )

        '''
        if raw_mat_costs.shape[0] != prd_mat_prices.shape[0]:
            logging.error("The number of rows in raw material cost array and product material price array must be same!")
        
        if raw_mat_costs.ndim != prd_mat_prices.ndim:
            logging.error('The dimensions of raw material cost array and product material price array must be same!')
        
        gross_revenue = prd_mat_prices - raw_mat_costs
        logging.info(f"compute_gross_revenue() - Gross revenue computed successfully. {gross_revenue}")

        return gross_revenue

    def compute_gross_profit(self, gross_revenue: np.ndarray, aggr_utility_costs: np.ndarray) -> np.ndarray:
        '''
        This method computes the gross profit using gross revenue and aggregate utility costs.

        Args:
            gross_revenue (array): Array with gross revenue of size (n_samples, ).
            aggr_utility_costs (array): Array with aggregate utility costs of size (n_samples, ).
        
        Returns:
            gross_profit (array): Array with gross profit of size (n_samples, ).
        '''
        if gross_revenue.ndim != aggr_utility_costs.ndim:
            logging.error("Input arrays must be of same size!")
        
        gross_profit = gross_revenue - aggr_utility_costs
        logging.info(f"compute_gross_profit() - Gross profit computed successfully. {gross_profit}")

        return gross_profit
    
    def compute_gross_profit_aft_tax(self, gross_profit: np.ndarray, carbon_tax: np.ndarray) -> np.ndarray:
        '''
        This method computes gross profit after carbon tax.
        Args:
            gross_profit (array): Array with gross profit data of size (n_samples,)
            carbon_tax (array): Array with carbon tax data of size (n_samples, )
        Returns:
            gross_profit_after_carbon_taxv (array): Array with gross profit after carbon tax of size (n_samples, )
        '''

        if gross_profit.ndim != carbon_tax.ndim:
            logging.error("The input arrays must be of same size!")

        gross_profit_after_carbon_tax = gross_profit - carbon_tax
        logging.info(f"compute_gross_profit_aft_tax() - Profit after carbon tax is computed successfully. {gross_profit_after_carbon_tax}")

        return gross_profit_after_carbon_tax

    def compute_cost_share(self, raw_mat_costs: np.ndarray, aggr_utility_costs: np.ndarray, carbon_tax: np.ndarray, prd_mat_prices: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        '''
        This method computes the contributions of various factors in the total product price.
        Args:
            raw_mat_costs (array): Array with raw material costs of size (n_samples, )
            aggr_utility_costs (array): Array with aggreagte utility costs of size (n_samples, )
            carbon_tax (array): Array with carbon tax cost of size (n_samples, )
            prd_mat_prices (array): Array with product material prices of size (n_samples, )
        
        Returns:
            share_raw_mat_cost (array): Array with % share of raw material cost in product price of size (n_samples, )
            share_utl_cost (array): Array with % share of utility cost in product price of size (n_samples, )
            sahre_carbon_tax (array): Array with % share of carbon tax in product price of size (n_samples, )
            net_prd_margin (array): Array with % share of net product margin of size(n_samples, )
        '''
        aggr_prd_mat_prices  = self.__compute_aggregate(mat_cost=prd_mat_prices)
        if raw_mat_costs.shape[0] != aggr_prd_mat_prices.shape[0]:
            logging.error("Number of rows in raw material cost arrays must be same as the aggregate product material prices!")
        if aggr_utility_costs.ndim != aggr_prd_mat_prices.ndim:
            logging.error("Dimensions of aggregate utlity costs array and product material prices array must be same!")
        if carbon_tax.ndim != aggr_prd_mat_prices.ndim:
            logging.error("Dimensions of carbon tax array and aggregate product price array must be same!")
        share_raw_mat_cost = np.divide(raw_mat_costs, aggr_prd_mat_prices) * 100
        share_utl_cost = np.divide(aggr_utility_costs, aggr_prd_mat_prices) * 100
        share_carbon_tax = np.divide(carbon_tax, aggr_prd_mat_prices) * 100
        share_aggr_raw_mat_cost = self.__compute_aggregate(mat_cost=share_raw_mat_cost)
        net_prd_margin = np.ones(shape=(1, 1)).reshape(-1, 1) * 100 - np.add(np.add(share_aggr_raw_mat_cost, share_utl_cost), share_carbon_tax)
        logging.info(f"compute_cost_share() - The cost shares are computed successfully. {share_aggr_raw_mat_cost, share_utl_cost, share_carbon_tax, net_prd_margin}")
        return share_raw_mat_cost, share_utl_cost, share_carbon_tax, net_prd_margin

        