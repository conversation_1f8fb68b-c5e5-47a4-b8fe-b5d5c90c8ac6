from __future__ import (
    annotations,  # for unquoted annotations in TYPE_CHECKING (forward_vars)
)
from ._imports import *
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.neighbors import LocalOutlierFactor
from sklearn.ensemble import IsolationForest, RandomForestClassifier

class OutlierDetector:
    def __init__(self, **kwargs) -> None:
        '''
        This instantiates the OutlierDetector object and user can call various functions for outlier detection.
        Args:
            This takes optional key word arguments. If not provided, default argument values are used.
            n_neighbours (int): Number of neighbours for a given sample to be classified in a class. Check sk-learn documents for more details.
            algorithm (str): Type of algorithm to be used. Check sk-learn documents for more details.
            leaf_size (int): Check sk-learn documents for more details.
            metric (str): Distance metric used for classifying samples. Check sk-learn documents for more details.
        Return:
            None
        '''
        self.__n_neighbours: int = kwargs.get('n_neighbours', 10)
        self.__algorithm = kwargs.get('algorithm', 'auto')
        self.__leaf_size: int = kwargs.get('leaf_size', 30)
        self.__metric: str = kwargs.get('metric', 'minkowski')
        self.outlier_classifier: LocalOutlierFactor = None #type: ignore  

    def build_outlier_model(self, x: np.ndarray) -> np.ndarray:
        '''
        This method builds and trains outlier detection model for the given data array.
        Args:
            x (array): Array of size k_samples (rows) and n_features (columns) containing data for outlier analysis.
        Returns:
            predicted_labels (array): Array of labels predicted for the data used in training.
        '''
        self.outlier_classifier = LocalOutlierFactor(n_neighbors=self.__n_neighbours, algorithm=self.__algorithm,
                                                     leaf_size=self.__leaf_size, metric=self.__metric)
        predicted_labels = self.outlier_classifier.fit_predict(x)
        logging.info(f"build_outlier_model() - Outlier detection model built successfully. {predicted_labels}")
        return predicted_labels
    
    # def predict_outliers(self, x: np.ndarray) -> np.ndarray:
    #     '''
    #     This method predicts outliers using the trained model for the given data array.
    #     Args:
    #         x (array) : Array of size k_samples (rows) and n_features (columns) containing data for outlier analysis.
    #     Returns:
    #         predicted_labels (array): Array of labels predicted for the given data.
    #     '''
    #     predicted_labels = self.outlier_classifier.predict(X=x)
    #     logging.info(f"predict_outliers() - Outliers predicted successfully. {predicted_labels}")
    #     return predicted_labels
        
# Anomaly Detector
class AnomalyDetector:
    def __init__(self, scaling, **kwargs) -> None:
        '''
        This method instantiates the object of AnomalyDetector class.
        Args:
            This takes optional key word arguments. If not provided, default argument values are used.
            scaling (str): Type of scaling applied on the provided data. 
            normalization_range (str): Range used for normalization when scaling type of normalization is chosen.
            n_estimators (int): Check sk-learn documents for more details.
            max_samples (int/str): Check sk-learn documents for more details.
            contamination (float): Fraction of the total data considered as anomalous.
            max_features (float): Fraction of features are considered. Check sk-learn documents for more details.
            random_state (int): State used for repeatability. Check sk-learn documents for more details.
        Return:
            None

        '''
        self.__scaling: str = scaling
        self.__normalization_range: tuple = kwargs.get('normalization_range', (0, 1))
        self.__n_estimators: int = kwargs.get('n_estimators', 100)
        self.__max_samples = kwargs.get('max_samples', 'auto')
        self.__contamination = kwargs.get('anomaly_fraction', 'auto')
        self.__max_features: float = kwargs.get('max_features', 1.0)
        self.__random_state: int = kwargs.get('random_state', 1234)
        # Normalize data
        if self.__scaling == 'normalization':
            self.scaler = MinMaxScaler(self.__normalization_range)
        # Standadize data
        elif self.__scaling == 'standardization':
            self.scaler = StandardScaler()
        
        self.anomaly_detector: IsolationForest = None #type: ignore
        self.anomaly_classifier: RandomForestClassifier = None #type: ignore

    
    def perform_data_scaling(self, x: np.ndarray) -> Tuple[object, np.ndarray]:
        '''
        This method performs scaling of the provided data.
        Args:
            x (array): Array of size k_samples (rows) and n_features (columns)
        Returns:
            scaled_data (array): Array of size k_samples and n_features containing scaled data.
        '''

        self.scaler.fit(x)
        scaled_data = self.scaler.transform(x)
        logging.info(f"perform_data_scaling() - Data scaling performed successfully. {scaled_data}")
        return scaled_data
    
    def __generate_anomaly_labels(self, x: np.ndarray) -> Tuple[IsolationForest, np.ndarray]:
        '''
        This method builds and trains unsupervised anomaly detection model for the given data.
        Args:
            x (array): Array of size k_samples (rows) and n_features (columns)
        Returns:
            predicted_labels (array): Array of size k_samples containing predicted labels.
        '''

        self.anomaly_detector = IsolationForest(n_estimators=self.n_estimators, max_samples=self.max_samples, max_features=self.max_features, contamination=self.contamination,random_state=self.random_state)
        predicted_labels = self.anomaly_detector.fit_predict(X=x)
        logging.info(f"__generate_anomaly_labels() - Anomalies detected successfully. {predicted_labels}")
        return predicted_labels
    
    def build_anomaly_classifier(self, x: np.ndarray):
        '''
        This method builds supervised anomaly detection model for the given data.
        Args:
            x (array): Array of size k_samples (rows) and n_features (columns)
        Return:
            None
        '''
        
        generated_labels = self.__generate_anomaly_labels(x=x)
        self.anomaly_classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        self.anomaly_classifier.fit(X=x, y=generated_labels)
        logging.info(f"build_anomaly_classifier() - Anomaly classifier is built successfully.")

        return
    
    def find_anomalies(self, x: np.ndarray) -> np.ndarray:
        '''
        This method finds anomalies in the given data using a trained anomaly classifier.
        Args:
            x (array): Array of size k_samples (rows) and n_features (columns)
        Returns:
            y_predicted: Array containing predicted labels of the data.
        '''

        y_predicted = self.anomaly_classifier.predict(X=x)
        logging.info(f"find_anomalies() - Anomalies are labelled successfully. {y_predicted}")
        return y_predicted