"""
This class encapsulates all the responsibilities related to setting up, saving, running,
and interacting with the DWSim model. It acts as a single entry point for managing the
model lifecycle and provides methods for creating interfaces for different types of
objects (equipment, compound mix, material stream).

Arguments:
    model_id: unique name for model
    model_configuration: configuration mapper for the model
    artefact_folder: relative path of folder to save artefacts
    dwsim_property_package: the property package to use for the DWSim model
    template_filename: filename of template binary to use, assumed to be in `artefact_folder`
"""

####################

from __future__ import annotations
from ._imports import *

# .NET interop library
import clr
# Python's way of using .NET environment and I/O classes
from System import Array, Double  # type: ignore
from System import Environment, String  # type: ignore
from System.Collections import ArrayList  # type: ignore
from System.Collections.Generic import Dictionary  # type: ignore
from System.IO import Directory, File, Path  # type: ignore

# INTERNAL

import backend.core._atlas.aggregates as at
from backend.core._matrix.matrix import (
        BaseMatrixSimulator
)

# LOAD DWSIM
import pythonnet
import sys

dwsimpath = "/usr/local/lib/dwsim/"
clr.AddReference(dwsimpath + "CapeOpen.dll")  # type: ignore
clr.AddReference(dwsimpath + "DWSIM.Automation.dll")  # type: ignore
clr.AddReference(dwsimpath + "DWSIM.Interfaces.dll")  # type: ignore
clr.AddReference(dwsimpath + "DWSIM.GlobalSettings.dll")  # type: ignore
clr.AddReference(dwsimpath + "DWSIM.SharedClasses.dll")  # type: ignore
clr.AddReference(dwsimpath + "DWSIM.Thermodynamics.dll")  # type: ignore
clr.AddReference(dwsimpath + "DWSIM.UnitOperations.dll")  # type: ignore
clr.AddReference(dwsimpath + "DWSIM.Inspector.dll")  # type: ignore
clr.AddReference(dwsimpath + "System.Buffers.dll")  # type: ignore
# clr.AddReference(dwsimpath + "DWSIM.Thermodynamics.ThermoC.dll")  # type: ignore # NOTE: this has caused crashes

# Importing now available CLR namespaces
import DWSIM  # type: ignore
from DWSIM.Automation import Automation3  # type: ignore
from DWSIM.GlobalSettings import Settings  # type: ignore
from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType  # type: ignore
from DWSIM.Thermodynamics import PropertyPackages, Streams  # type: ignore
from DWSIM.UnitOperations import UnitOperations  # type: ignore


####################


def get_abs_filepath(rel_filepath: str):
    current_dir = Path(__file__).parent
    filepath = current_dir / rel_filepath
    return filepath


def remap_value_check(variable_key: at.BaseSpecEnum, value, to_endpoint: bool = True):
    """checks a variable against a fixed lookup for any remapping required

    args:
        to_dwsim: true if value is going to dwsim, false if value is coming back from dwsim
    Returns a remapped value
    [ ] we need unit management and a better lookup. This can be a class thing that is extensibel and inits from some
    lookup
    """

    def remap_0to1_0to100(val, to_dwsim: bool):
        """Remaps a 0-1 ratio to 0-100"""
        if to_dwsim:
            return val * 100
        else:
            return val / 100

    strategy = {
        at.ContVarSpecEnum.Efficiency: lambda val, to_dwsim: remap_0to1_0to100(
            val, to_dwsim
        ),
        at.ContVarSpecEnum.HeatTransferEfficiency: lambda val, to_dwsim: remap_0to1_0to100(
            val, to_dwsim
        ),
        at.ContVarSpecEnum.AdiabaticEff: lambda val, to_dwsim: remap_0to1_0to100(
            val, to_dwsim
        ),
        # at.ContVarSpecEnum.PolytropicEff: lambda val, to_dwsim: remap_0to1_0to100(
        #     val, to_dwsim
        # ),
        at.ContVarSpecEnum.ExchangerEfficiency: lambda val, to_dwsim: remap_0to1_0to100(
            val, to_dwsim
        ),
        at.ContVarSpecEnum.PerformanceCurves_Eff: lambda val, to_dwsim: remap_0to1_0to100(
            val, to_dwsim
        ),
    }

    if variable_key in strategy:
        return strategy[variable_key](value, to_endpoint) #type: ignore

    else:
        return value


# HELPER  CLASSES
@dataclass(frozen=True)
class _DWSimEntityAPILookup:
    """
    A data class that represents a lookup for a DWSIM entity object class.

    The `object_type` is the type of the DWSIM entity object.
    The `atlas_class` is the type of the corresponding Atlas equipment class.
    The `unit_operation` is an optional reference to the DWSIM unit operation object.
    """

    object_type: Type
    atlas_class: Union[Type[at.ENTBaseEquipment], Type[at.ENTBaseStream]]
    unit_operation: Optional[Type] = None

    def __deepcopy__(self, memo):
        return self


@dataclass(frozen=True)
class _PropParamAccessPath:
    """
    A data class that represents a path to access a property parameter in a DWSIM entity.

    The `attribute_keyword` is the name of the attribute to access.
    The `attribute_seq` is an optional sequence of attribute names to traverse to get the nested attribute value.
    The `enforced_setval` is an optional value to set the attribute to, if provided.
    """

    attribute_keyword: str
    attribute_seq: Optional[Tuple[str]] = None
    enforced_setval: Optional[Union[float, str]] = None

    def __deepcopy__(self, memo):
        return self


@dataclass(frozen=True)
class _MethodAccessPath:
    getter_method_name: str
    setter_method_name: str
    enforced_setval: Optional[Union[float, str]] = None

    def __deepcopy__(self, memo):
        return self


####################

# Generic Types for DWSIM
# NOTE - to replace with actual dwsim types when CLR and pythonnet is solved

TDWSimEntityUnitObj = TypeVar("TDWSimEntityUnitObj", bound=Any)
TDWSimEntityGraphicObj = TypeVar("TDWSimEntityGraphicObj", bound=Any)
TDWSimEntitySimObj = TypeVar("TDWSimEntitySimObj", bound=Any)
TDWSimModelSim = TypeVar("TDWSimModelSim", bound=Any)
TDWSimModelInterface = TypeVar("TDWSimModelInterface", bound=Any)

# HELPER FUNCTIONS


def _get_nested_obj(
    entity_unitobj: TDWSimEntityUnitObj, attr_seq
) -> TDWSimEntityUnitObj:
    """
    Gets a nested attribute value from the given DWSIM entity unit object.

    Args:
        entity_unitobj (TDWSimEntityUnitObj): The DWSIM entity unit object to retrieve the nested attribute from.
        attr_seq (Sequence[str]): A sequence of attribute names to traverse to get the nested attribute value.

    Returns:
        TDWSimEntityUnitObj: The nested DWSIM entity unit object.
    """
    for attr in attr_seq or []:
        entity_unitobj = getattr(entity_unitobj, attr)
    return entity_unitobj


def _set_attribute_value(entity_unitobj: TDWSimEntityUnitObj, attr_name, value):
    """
    Sets an attribute value on a .NET entity, handling type conversions as needed.
    If the target is a Dictionary and value is not a dict, sets the value to the first dictionary item.

    Args:
        entity: The .NET entity to modify
        attr_name: Name of the attribute to set
        value: Value to set (can be any type - will be handled appropriately)
    """
    try:
        # Get current attribute to check its type
        current_attr = getattr(entity_unitobj, attr_name)

        # Check if target is a .NET Dictionary
        if "Dictionary" in str(type(current_attr)):
            if isinstance(value, dict):
                # If value is a dictionary, update all items
                current_attr.Clear()
                for k, v in value.items():
                    current_attr[k] = v
            else:
                # If value is not a dictionary, set it as the value of the first key
                if len(current_attr) > 0:
                    first_key = list(current_attr.Keys)[0]  # Get first key
                    current_attr[first_key] = value
                else:
                    raise ValueError(
                        f"Cannot set value on empty Dictionary attribute {attr_name}"
                    )
            return

        # Normal attribute setting for non-dictionary types
        setattr(entity_unitobj, attr_name, value)
    except TypeError:
        # Fallback for primitive types that might need integer conversion
        try:
            setattr(entity_unitobj, attr_name, int(value))  # type: ignore
        except (TypeError, ValueError):
            raise TypeError(f"Failed to set attribute {attr_name} with value {value}")


def _get_obj_attribute(entity_unitobj: TDWSimEntityUnitObj, attr_name):
    """
    Gets the value of an attribute on the given entity, handling both public and private fields.
    """
    try:
        # Public Field
        return getattr(entity_unitobj, attr_name)
    except AttributeError:
        # Private Field
        return sharedutils.get_dotnet_private_field(entity_unitobj, attr_name)


def _cast_to_float(val: Union[float, int, Iterable, None]):
    """Recursive function that handles values and casts them to float.

    - If val is None, sets it to 0.0
    - If val is int, casts to float
    - Does the same for values in an interable
    - Does NOT handle NAN or INF cases
    """
    if val is None:
        return 0.0
    elif isinstance(val, (int, float)):
        return float(val)
    elif isinstance(val, (list, tuple)):
        return type(val)(_cast_to_float(item) for item in val)
    return val


# STREAMS


class DWSimStreamInterface:
    DISCRETE_ITEMS_LOOKUP: Dict[
        Union[at.DiscreteItemSpecEnum, at.DiscreteSetSpecEnum],
        Union[_PropParamAccessPath, _MethodAccessPath, Tuple],
    ] = {}
    CONT_VARS_LOOKUP: Dict[at.ContVarSpecEnum, _PropParamAccessPath] = {}
    API_LOOKUP: _DWSimEntityAPILookup = _DWSimEntityAPILookup(
        object_type=ObjectType.Heater,
        unit_operation=UnitOperations.Heater,
        atlas_class=at.Heater,
    )

    def __init__(
        self,
        atlas_reference: at.ENTBase,
        endpoint_manager: MatrixDWSim,
        *,
        prefix: Optional[str] = None,
    ):
        self._atlas_entity = atlas_reference
        self._endpoint_manager = endpoint_manager
        self._disc_items_lookup = copy.deepcopy(self.DISCRETE_ITEMS_LOOKUP)
        self._cont_vars_lookup = copy.deepcopy(self.CONT_VARS_LOOKUP)
        self._prefix = prefix

    @property
    def simulation_id(self) -> str:
        return self._endpoint_manager.label

    @property
    def entity_id(self) -> str:
        if self._prefix:
            return f"{self._prefix}_{self._atlas_entity.label}"

        return self._atlas_entity.label

    @property
    def atlas_entity(self) -> Optional[at.ENTBase]:
        return self._atlas_entity

    @property
    def endpoint_config(self):
        return self.manager.config

    @property
    def manager(self) -> MatrixDWSim:
        return self._endpoint_manager

    @property
    def dwsim_simulator(self) -> TDWSimModelSim:
        assert isinstance(self.manager, MatrixDWSim)
        return self.manager.dwsim_simulator

    @property
    def dwsim_interface(self) -> TDWSimModelInterface:
        assert isinstance(self.manager, MatrixDWSim)
        return self.manager.dwsim_interface

    @property
    def sim_obj(self) -> TDWSimEntitySimObj:
        """
        Queries the DWSim simulator for the object.
        """
        try:
            sim_obj = self.dwsim_simulator.GetObject(self.entity_id)
        except Exception as e:
            raise Exception(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not get object from DWSim - {e}"
            )

        if sim_obj is None:
            logging.warning(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Did not find in endpoint. Returning as None."
            )
        return sim_obj

    @property
    def unit_obj(self) -> TDWSimEntityUnitObj:
        """
        Returns a DWSim unit operation object.
        """
        obj = None
        try:
            obj = self.dwsim_simulator.GetObject(self.entity_id).GetAsObject()
        except Exception as e:
            raise Exception(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not get object from DWSim - {e}"
            )

        if obj is None:
            logging.warning(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Did not find in endpoint. Returning as None."
            )
        return obj

    @property
    def graphic_obj(self) -> TDWSimEntityGraphicObj:
        obj = None
        try:
            obj = self.unit_obj.GraphicObject
        except Exception as e:
            raise Exception(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not get object from DWSim - {e}"
            )

        if obj is None:
            logging.warning(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Did not find in endpoint. Returning as None."
            )
        return obj

    ####################

    # PUSH

    def create_in_endpoint(self):
        """
        Creates an equipment object in the DWSim simulator.
        For simpliciy, class variables are hard-coded to each subclass.

        Raises:
            ValueError: If the object could not be created.
        """
        object_type = self.API_LOOKUP.object_type
        model_object = self.dwsim_simulator.AddObject(object_type, 0, 0, self.entity_id)

        if self.sim_obj == None:
            raise ValueError(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not create object in DWSim"
            )

        logging.warning(
            f"{sharedutils.get_function_name()} - {self.entity_id} - Good News! it was created in endpoint."
        )
        return

    @sharedutils.handle_exceptions
    def _set_endpoint_dvar_discreteitem(
        self,
        discrete_set: at.DiscreteSetSpecEnum,
        discrete_item: at.DiscreteItemSpecEnum,
        skiplist: Optional[List[at.DiscreteSetSpecEnum]] = None,
        **kwargs,
    ):
        """
        Sets the discrete variable in the endpoint, given a discrete item.

        Args
            discrete_set: the set it lives in
            discrete_item: the item value to set
            skiplist: list of discretesets to skip. this can be for sets not present in endpoint (e.g. MaterialStreamType)
        """
        DEFAULT_SKIP_LIST = [at.DiscreteSetSpecEnum.MaterialStreamType]
        skiplist = skiplist if skiplist is not None else DEFAULT_SKIP_LIST

        # DEFENSIVE - skip if not correct type
        if not isinstance(discrete_item, at.DiscreteItemSpecEnum):
            return

        if discrete_set in skiplist:
            logging.info(f"Item in {skiplist}. skipping `{discrete_item}`")
            return

        # LOGIC

        selection_lookup = self._disc_items_lookup[discrete_item]

        # check mapping
        if selection_lookup == None:
            return

        # PropParamAccessPath Type
        elif isinstance(selection_lookup, _PropParamAccessPath):
            # Defensive
            if selection_lookup.enforced_setval is None:
                raise ValueError(
                    f"model_id:`{self.simulation_id}` entity_id:`{self.entity_id}` - {discrete_item} not set correctly in endpoint. Check implementation"
                )

            # Set val
            entity = _get_nested_obj(self.unit_obj, selection_lookup.attribute_seq)
            _set_attribute_value(
                entity,
                selection_lookup.attribute_keyword,
                selection_lookup.enforced_setval,
            )

            logging.info(
                f"{sharedutils.get_function_name()} - {self.simulation_id}.{self.entity_id} - non-enum selection set: `{discrete_item}`, value: `{selection_lookup.enforced_setval}`"
            )

        # MethodAccess Type
        elif isinstance(selection_lookup, _MethodAccessPath):
            try:
                getter_method = getattr(
                    self.unit_obj, selection_lookup.getter_method_name
                )
                val_prev = getter_method()

                setter_method = getattr(
                    self.unit_obj, selection_lookup.setter_method_name
                )
                val_curr = setter_method(selection_lookup.enforced_setval)
            except Exception as e:
                raise ValueError(
                    f"{self.simulation_id},{self.entity_id} - param=`{selection_lookup}`- {e}"
                )

        # STRING BASED SELECTION (e.g. calcpolicy)
        elif isinstance(selection_lookup, tuple) and len(selection_lookup) == 2:
            _attrib, _keyword = selection_lookup
            setattr(
                self.unit_obj,
                _attrib,
                getattr(getattr(self.unit_obj, _attrib), _keyword),
            )
            logging.info(
                f"{sharedutils.get_function_name()} - {self.simulation_id}.{self.entity_id} - enum selection set: `{discrete_item}`, keyword:`{_keyword}`, attribute: `{_attrib}`"
            )
        else:
            raise TypeError(f"wrong type")

        logging.info(
            f"{sharedutils.get_function_name()} - set discrete_item: {discrete_item}"
        )

        return

    @sharedutils.handle_exceptions
    def _set_endpoint_contvar(
        self,
        variable_key: at.ContVarSpecEnum,
        new_val: Union[float, list, tuple],
    ):
        """
        Sets the value of a property or parameter in the endpoint.
        Does not work for DICT endpoints.
        """

        def _set_via_prop(spec: _PropParamAccessPath, new_val):
            # Define val
            attrib_keyword = spec.attribute_keyword
            obj_seq = spec.attribute_seq
            val = spec.enforced_setval or new_val

            # Set correct object to call on
            dwsim_obj = _get_nested_obj(self.unit_obj, obj_seq)

            # Set val in endpoint
            try:
                val_prev = sharedutils.cast_value_dotnet2py(
                    getattr(dwsim_obj, attrib_keyword)
                )
                # For iterables, they are often protected values in endpoint. We workaround it by setting each element
                if isinstance(val, (tuple, list)):
                    for idx, val in enumerate(val):
                        _entity = getattr(dwsim_obj, attrib_keyword)
                        _entity[idx] = val
                else:
                    _set_attribute_value(dwsim_obj, attrib_keyword, val)
                val_curr = sharedutils.cast_value_dotnet2py(
                    getattr(dwsim_obj, attrib_keyword)
                )
            except Exception as e:
                raise ValueError(
                    f"{self.simulation_id},{self.entity_id} - param=`{variable_key}`,val=`{val}` - Could not set val in endpoint- {e}"
                )

            return val_prev, val_curr

        def _set_via_method(spec: _MethodAccessPath, new_val):
            val = new_val
            try:
                getter_method = getattr(self.unit_obj, spec.getter_method_name)
                val_prev = getter_method()

                setter_method = getattr(self.unit_obj, spec.setter_method_name)
                val_curr = setter_method(val)
            except Exception as e:
                raise ValueError(
                    f"{self.simulation_id},{self.entity_id} - param=`{variable_key}`,val=`{val}` - Could not set val in endpoint- {e}"
                )

            return val_prev, val_curr

        # Get attribute dataclass

        new_val = remap_value_check(variable_key, new_val, to_endpoint=True)
        if isinstance(variable_key, at.ContVarSpecEnum):
            attrib_dataclass = self._cont_vars_lookup[variable_key]
        else:
            raise KeyError(
                f"{self.simulation_id()},{self.get_entity_id()} - no keywords found for {variable_key}"
            )

        # Define val
        # Set
        mapper = {
            _PropParamAccessPath: _set_via_prop,
            _MethodAccessPath: _set_via_method,
        }

        func = mapper[type(attrib_dataclass)]
        v_prev, v_curr = func(attrib_dataclass, new_val)

        logging.info(
            f"{sharedutils.get_function_name()} - {self.simulation_id}/{self.entity_id}/{variable_key} - endpoint value set. `{v_prev}` -> `{v_curr}`"
        )

        return v_curr

    def handle_push_contvarcollections(self, collection: at.AbstractVariableCollection):
        """Push continuous variables to endpoint"""
        for var in collection.items:
            assert isinstance(var, at.VOContinuousVariable)
            val = _cast_to_float(var.value)
            assert isinstance(val, (float, tuple, list))
            self._set_endpoint_contvar(var.variable_enum, val)

    def handle_push_discretesetcollection(
        self, collection: at.AbstractVariableCollection
    ):
        """Push discrete variables to endpoint"""
        for var in collection.items:
            self._set_endpoint_dvar_discreteitem(var.variable_enum, var.value)

    def push_to_endpoint(self, strategy: Optional[Dict[Type, Callable]] = None):  #
        """
        Push values from the atlas to the endpoint.

        This method uses an extensible strategy pattern to match collection item types to their respective functions for pushing the values to the endpoint.

        Args:
            strategy(Optional[Dict[Type, Callable]]):
            A mapping of collection item types to their respective push functions, with basic defaults
        """
        DEFAULT_STRATEGY = {
            at.VOContinuousVariable: self.handle_push_contvarcollections,
            at.VODiscreteVariable: self.handle_push_discretesetcollection,
        }
        if self.sim_obj is None:
            self.create_in_endpoint()

        # Skip for energy interface
        if isinstance(self, DWSimEnergyStreamInterface):
            return

        strategy = strategy if strategy is not None else DEFAULT_STRATEGY

        collections: List[at.AbstractVariableCollection] = (
            self.atlas_entity.get_collections()
        )
        # First set all collections except CompoundMassRatio
        for collection in collections:
            if isinstance(collection, at.VarCollectionCompoundMassRatio):
                continue
            func = strategy[collection.itemtype]
            func(collection)
            logging.info(f"Pushed to endpoint: {collection}")

    ####################
    def _get_endpoint_param_cvar_values(
        self,
        keyword: Optional[Any] = None,
    ) -> Dict[
        at.ContVarSpecEnum,
        Union[None, float, list, tuple],
    ]:
        """Gets the value of a DWSim object property or parameter."""

        def _process_value(result):
            """
            Processes the result of a query, handling cases where the result is a dictionary.

            - if dict has 1 item, return value
            -
            """
            if isinstance(result, dict):
                if len(result) == 1:
                    return list(result.values())[0]
                elif len(result) > 1 and keyword is not None:
                    return result[keyword]
                else:

                    first_value = list(result.values())[0]
                    logging.warning(
                        f"{result} - Result has no keyword specification to extract as value. Returning first value. Is this expected?"
                    )
                    return first_value
            return result

        result = {}
        cont_vars = self._atlas_entity.get_variables(
            filter=[at.VarCollectionContinuous], as_dict=True
        )

        # ROUTER
        for contvar_label in cont_vars.keys():
            mapper = {
                at.ContVarSpecEnum: self._cont_vars_lookup,
            }
            attrib_dataclass = mapper[type(contvar_label)][contvar_label]

            val = None

            # Handle PropParamAccessPath
            if isinstance(attrib_dataclass, _PropParamAccessPath):
                dwsim_keyword = attrib_dataclass.attribute_keyword
                dwsim_attrib_seq = attrib_dataclass.attribute_seq
                dwsim_obj = _get_nested_obj(self.unit_obj, dwsim_attrib_seq)
                val = _get_obj_attribute(dwsim_obj, dwsim_keyword)

            # Handle MethodAccessPath
            elif isinstance(attrib_dataclass, _MethodAccessPath):
                getter_method = getattr(
                    self.unit_obj, attrib_dataclass.getter_method_name
                )
                val = getter_method()

            else:
                raise AttributeError(
                    f"no handler found for `{attrib_dataclass}`. Check attrib dataclass."
                )

            val = sharedutils.cast_value_dotnet2py(val)
            val = _process_value(val)
            if isinstance(val, list):
                val = tuple(val)

            val = remap_value_check(contvar_label, val, to_endpoint=False)

            result[contvar_label] = val

        return result

    # SELECTIONSET METHODS

    @sharedutils.handle_exceptions(return_as_dict=True)
    def _get_endpoint_dvar_discreteitems(
        self,
    ) -> Dict[
        at.DiscreteSetSpecEnum,
        at.DiscreteItemSpecEnum,
    ]:
        """
        Get all disc item enum items of entity from endpoint.
        """

        # HELPER FUNCTIONS
        def _query_endpoint_via_methodaccess(
            mapping_sample: _MethodAccessPath,
        ) -> at.DiscreteItemSpecEnum:
            """Gets the atlas discrete item, given fixed return from endpoind"""
            getter_method = getattr(self.unit_obj, mapping_sample.getter_method_name)
            endpoint_return = getter_method()

            # Find the correct DiscreteItemEnum
            _result = next(
                k
                for k, v in self._disc_items_lookup.items()
                if isinstance(v, _MethodAccessPath)
                and v.enforced_setval == endpoint_return
            )

            if _result is None:
                raise ValueError(
                    f"model_id:`{self.simulation_id}` entity_id:`{self.entity_id}` - endpoint selection not found for {mapping_sample}. Check implementation"
                )

            assert isinstance(_result, at.DiscreteItemSpecEnum)
            return _result

        def _query_endpoint_via_tuple(mapping_sample: tuple) -> at.DiscreteItemSpecEnum:  # type: ignore

            _kw = mapping_sample[0]
            endpoint_return = getattr(self.unit_obj, _kw)
            _result = next(  # type: ignore
                (
                    selection
                    for selection, mapping in self._disc_items_lookup.items()
                    if isinstance(mapping, tuple) and mapping[1] == str(endpoint_return)
                ),
                None,
            )

            if _result is None:
                raise ValueError(
                    f"model_id:`{self.simulation_id}` entity_id:`{self.entity_id}` - endpoint selection not found for {mapping_sample}. Check implementation"
                )

            assert isinstance(_result, at.DiscreteItemSpecEnum)
            return _result

        def _query_endpoint_via_accesspath(mapping_sample: _PropParamAccessPath) -> at.DiscreteItemSpecEnum:  # type: ignore
            """
            Gets the endpoint selection via an access path, supporting enum-based selections.
            """
            _kw = mapping_sample.attribute_keyword
            attrib_seq = mapping_sample.attribute_seq
            endpoint_obj = _get_nested_obj(self.unit_obj, attrib_seq)
            endpoint_value = _get_obj_attribute(endpoint_obj, _kw)

            _result = next(  # type: ignore
                (
                    selection
                    for selection, mapping in self._disc_items_lookup.items()
                    if isinstance(mapping, _PropParamAccessPath)
                    and mapping.attribute_keyword == _kw
                    and mapping.enforced_setval == endpoint_value
                ),
                None,
            )

            # Check for no match
            if _result is None:
                raise ValueError(
                    f"model_id:`{self.simulation_id}` entity_id:`{self.entity_id}` - endpoint value `{endpoint_value}` not found for sample: `{mapping_sample}`. Check implementation"
                )

            assert isinstance(_result, at.DiscreteItemSpecEnum)
            return _result

        # LOGIC

        result = {}
        checked = []

        for k, v in self._disc_items_lookup.items():

            # Defensive: those that need overrides have NONE
            if v == None:
                continue

            # PropParamAccessPath
            elif isinstance(v, _PropParamAccessPath):
                kw = v.attribute_keyword
                if kw in checked:
                    continue
                else:
                    checked.append(kw)

                val = _query_endpoint_via_accesspath(v)

            # MethodAccess Type
            elif isinstance(v, _MethodAccessPath):
                kw = v.getter_method_name
                if kw in checked:
                    continue
                else:
                    checked.append(kw)

                val = _query_endpoint_via_methodaccess(v)

            # Tuple Access Path
            elif isinstance(v, tuple) and len(v) == 2:
                kw = v[0]
                if kw in checked:
                    continue
                else:
                    checked.append(kw)

                val = _query_endpoint_via_tuple(v)
            else:
                raise ValueError(f"{k}:{v} not looked-up. Check implementation")

            discrete_enum = self.atlas_entity.get_variable(val).variable_enum
            result[discrete_enum] = val

        return result

    def update_atlas(
        self,
        collection: at.AbstractVariableCollection,
        endpoint_vals: Dict[Any, Any],  # dict of keys and value
        force_update: bool = True,
    ):
        if endpoint_vals is None:
            logging.warning(
                f"update_atlas: `{collection}` has no associated endpoint vals. Is this intended?"
            )

        for var in collection.items:
            var_key = collection._get_key(var)

            # Defensive
            if var_key not in endpoint_vals:
                logging.warning(
                    f"`update_atlas: {var_key}` not in endpoint vals. Is this intended?"
                )
                continue

            # [ ] - forcing override all policies. something wrong with kwarg chain, var force_update passing as None
            force_update = force_update if force_update is not None else True
            self.atlas_entity.set_value(
                var_key, endpoint_vals[var_key], override_all_policies=force_update
            )

    def _handle_pull_discreteitems(
        self, collection: at.AbstractVariableCollection, force_update: bool = True
    ):
        values = self._get_endpoint_dvar_discreteitems()
        self.update_atlas(collection, values, force_update=force_update)

    def _handle_pull_contvar(
        self, collection: at.AbstractVariableCollection, force_update: bool = True
    ):
        values = self._get_endpoint_param_cvar_values()
        self.update_atlas(collection, values, force_update=force_update)

    def pull_from_endpoint(
        self,
        strategy: Optional[Dict[Type, Callable]] = None,  # map VarType to handler
        *,
        force_update: bool = True,
    ) -> Optional[at.ENTBase]:
        """
        Queries the endpoint for the latest values and updates the local entity with them.

        Args:
            strategy (Optional[Dict[Type, Callable]]): A strategy map of collection types to handler functions. The handler functions should take the following arguments:
                - `self`: The current instance of the `DWSimStreamInterface` class.
                - `collection`: The `at.AbstractVariableCollection` instance to be handled.
                - `force_update`: A boolean indicating whether to force the update of the values.
            force_update (bool, optional): Whether to force the update of the values. Defaults to `True`.
        """

        # Defensive
        DEFAULT_STRATEGY = {
            at.VOContinuousVariable: self._handle_pull_contvar,
            at.VODiscreteVariable: self._handle_pull_discreteitems,
        }

        # Check for membership
        if self.sim_obj is None:
            raise ValueError(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not find object in simulator"
            )

        # Skip for energy interface
        if isinstance(self, DWSimEnergyStreamInterface):
            return

        # Assign if None
        strategy = strategy if strategy is not None else DEFAULT_STRATEGY

        # Get collections and route by type
        collections: List[at.AbstractVariableCollection] = (
            self.atlas_entity.get_collections()
        )
        for collection in collections:
            func = strategy[collection.itemtype]
            func(collection, force_update)
            logging.info(f"atlas updated: collection:`{collection}`")

        return self.atlas_entity

    ####################

    # REMOVE

    @sharedutils.handle_exceptions
    def remove_from_endpoint(self):
        """
        Removes the object from endpoint.
        """
        try:
            self.dwsim_simulator.DeleteSelectedObject(None, None, self.graphic_obj)
            logging.info(
                f"{sharedutils.get_function_name()} - {self.entity_id} has been removed from the simulator."
            )
        except Exception as e:
            raise Exception(
                f"{self.simulation_id}, {self.entity_id} - Could not remove endpoint from simulator - {e}"
            )

        if self.sim_obj:
            raise ValueError(
                f"remove_equipment() - {self.entity_id} was not removed from the simulator."
            )


class DWSimEnergyStreamInterface(DWSimStreamInterface):

    # NOTE - do we need to set or access any parameters within an energy stream? -> NONE! so nothing to amend
    DISCRETE_ITEMS_LOOKUP = {}
    CONT_VARS_LOOKUP = {}

    API_LOOKUP: _DWSimEntityAPILookup = _DWSimEntityAPILookup(
        object_type=ObjectType.EnergyStream,
        atlas_class=at.ENTBaseEquipment,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    @property
    def entity_id(self) -> str:
        """Override of entity_id name, extends equipment instance"""
        if self._prefix:
            return f"{self._prefix}_E_{self._atlas_entity.label}"

        return f"E_{ self._atlas_entity.label }"

    def push_to_endpoint(self, *args, **kwargs):
        """
        Push values from atlas to endpoint.
        set collection_mapping to None to shortcircuit pushing any vars
        """
        super().push_to_endpoint(strategy=None)

    def pull_from_endpoint(self, *args, **kwargs):
        super().pull_from_endpoint(strategy=None, force_update=True)


class DWSimMaterialStreamInterface(DWSimStreamInterface):

    DISCRETE_ITEMS_LOOKUP: Dict[
        Union[at.DiscreteItemSpecEnum, at.DiscreteSetSpecEnum],
        Any,
    ] = {
        # CompoundMass
        # at.DiscreteSetSpecEnum.CompoundMix: None,
        # Stream Basis. There is no equivalent on DWSIM so setting none without any get/set overrides will simply pass this
        at.DiscreteItemSpecEnum.MaterialStreamBasis_Mass: None,
        # at.DiscreteItemSpecEnum.MaterialStreamBasis_Mol: None,
        # Flash Specs
        at.DiscreteItemSpecEnum.TemperatureAndPressure: _MethodAccessPath(
            "GetFlashSpec", "SetFlashSpec", "PT"
        ),
        # [ ] - taken out temporarily as it is now only temp pressure
        # at.DiscreteItemEnum.PressureAndEnthalphy: _MethodAccessPath(
        #     "GetFlashSpec", "SetFlashSpec", "PH"
        # ),
        # at.DiscreteItemEnum.PressureAndEntropy: _MethodAccessPath(
        #     "GetFlashSpec", "SetFlashSpec", "PS"
        # ),
        # at.DiscreteItemEnum.PressureaAndVaporFraction: _MethodAccessPath(
        #     "GetFlashSpec", "SetFlashSpec", "PVF"
        # ),
        # at.DiscreteItemEnum.TemperatureAndVaporFraction: _MethodAccessPath(
        #     "GetFlashSpec", "SetFlashSpec", "TVF"
        # ),
    }

    CONT_VARS_LOOKUP: Dict[at.ContVarSpecEnum, Any] = {
        at.ContVarSpecEnum.Temperature: _MethodAccessPath(
            "GetTemperature", "SetTemperature"
        ),
        at.ContVarSpecEnum.Pressure: _MethodAccessPath("GetPressure", "SetPressure"),
        at.ContVarSpecEnum.Molar_Enthalpy: _MethodAccessPath(
            "GetMolarEnthalpy", "SetMolarEnthalpy"
        ),
        at.ContVarSpecEnum.Molar_Entropy: _MethodAccessPath(
            "GetMolarEntropy", "GetMolarEntropy"
        ),
        at.ContVarSpecEnum.Mass_flow_rate: _MethodAccessPath(
            "GetMassFlow", "SetMassFlow"
        ),
        # at.ContVarSpecEnum.Molar_flow_rate: _MethodAccessPath(
        #     "GetMolarFlow", "SetMolarFlow"
        # ),
        # at.ContVarSpecEnum.Volumetric_flow_rate: _MethodAccessPath(
        #     "GetVolumetricFlow", "SetVolumetricFlow"
        # ),
        # NOTE - This is greyed out because we haven't found how to get this value
        # at.PropertyLabelEnum.VAPOR_MOLE_FRAC: _MethodAccessPath(),  # ? THIS WILL NEVER BE SET FOR NOW # THIS IS STILL PROBLEM: NO GETTER
    }

    API_LOOKUP: _DWSimEntityAPILookup = _DWSimEntityAPILookup(
        object_type=ObjectType.MaterialStream,
        atlas_class=at.ENTBaseStream,
    )

    # PUSH OVERRIDES

    def _set_endpoint_compoundmassmix(
        self,
        unit_obj,
        collection: at.VarCollectionCompoundMassRatio,
    ):
        """
        *WARNING*
        IN DWSIM ENDPOINT, this is actually setting overall component mass FLOW (not RATIO).
        multiply mass flow by ratio before setting
        """
        # get dict

        # Get Compound w Indexes

        compounds_in_collection = dict(collection.compoundmassmix.items())
        compounds_in_model = self.manager.atlas.compounds

        compound_mass_by_idx = [
            float(compounds_in_collection.get(c, 0.0)) for c in compounds_in_model
        ]

        # debug_masslist = list(unit_obj.GetOverallMassComposition())
        # print(debug_masslist)

        dot_net_array = Array[float](
            compound_mass_by_idx
        )  # Storing the mass fractions in a new variable

        modified_mol_fractions = unit_obj.MassFractionsToMoleFractions(
            dot_net_array
        )  # Converting the mass fraction to mol fraction
        unit_obj.SetOverallComposition(
            modified_mol_fractions
        )  # Attaching the mol fractions to the stream

        # dotnet_array = Array[Double](compound_mass_by_idx)
        # unit_obj.SetOverallMassComposition(dotnet_array)

        logging.info(
            f"{sharedutils.get_function_name()} - set compoundmix: {compound_mass_by_idx}"
        )

    def _set_endpoint_compoundmassmix_old(
        self,
        unit_obj,
        collection: at.VarCollectionCompoundMassRatio,
    ):
        """
        *WARNING*
        IN DWSIM ENDPOINT, this is actually setting overall component mass FLOW (not RATIO).
        multiply mass flow by ratio before setting
        """
        # get dict
        compoundmassmix_endpoint = {
            self.manager.get_compound_endpoint_label(k): v
            for k, v in collection.compoundmassmix.items()
        }

        # e.g. {Water: 0.8, Hydrogen: 0.2} * mass_flow
        mass_flow = unit_obj.GetMassFlow()
        for k, v in compoundmassmix_endpoint.items():
            mass = float(v * mass_flow)
            unit_obj.SetOverallCompoundMassFlow(k, mass)

        # set all others to 0
        for compound in self._endpoint_manager.atlas.compounds:
            compound_label = self.manager.get_compound_endpoint_label(compound)
            if compound in compoundmassmix_endpoint:
                continue
            unit_obj.SetOverallCompoundMassFlow(compound_label, float(0.0))

        logging.info(
            f"{sharedutils.get_function_name()} - set compoundmix: {compoundmassmix_endpoint}"
        )

    def handle_push_discretesetcollection(
        self, collection: at.AbstractVariableCollection
    ):
        """Override to skip setlabel: MaterialStreamType"""
        assert isinstance(collection, at.VarCollectionDiscreteSet)

        for var in collection.items:
            # Defnesive: skip sets not in dwsim
            if var.variable_enum == at.DiscreteSetSpecEnum.MaterialStreamType:
                continue

            assert isinstance(
                var.value, at.DiscreteItemSpecEnum
            ), f"{var.value} is wrong type, not able to set endpoint"
            self._set_endpoint_dvar_discreteitem(var.variable_enum, var.value)

        return super().handle_push_discretesetcollection(collection)

    def handle_push_compoundmasscollection(
        self, collection: at.AbstractVariableCollection
    ):
        assert isinstance(collection, at.VarCollectionCompoundMassRatio)
        self._set_endpoint_compoundmassmix(self.unit_obj, collection)

    def push_to_endpoint(self, *args, **kwargs):
        """
        Push values from atlas to endpoint.
        Uses an extensible mapping dict to match collection item types to their respective functions
        """
        super().push_to_endpoint()

        # Then set compoundmassmix collection
        compoundmassmix_collection = self.atlas_entity.get_collection(
            at.VarCollectionCompoundMassRatio
        )
        self.handle_push_compoundmasscollection(compoundmassmix_collection)
        logging.info(f"Pushed to endpoint: {compoundmassmix_collection}")

    def _get_endpoint_dvar_compoundmix(self, unit_obj) -> Dict[at.VOCompound, float]:

        def remap_composition(_array: List[float]) -> Dict[at.VOCompound, float]:
            """
            Given a list of indexed values, map index to compound and filter out all zero-values.
            """

            # compound_labels = self.manager.config.compounds # NOTE - this is for global
            compound_labels = [
                self.manager.get_compound_endpoint_label(c)
                for c in self.manager.atlas.compounds
            ]

            result = {}
            for idx, val in enumerate(_array):

                # remove zeros and NaNs
                if val == 0.0 or math.isnan(val) == True:
                    continue

                compound = self.manager.get_vocompound_by_endpoint_label(
                    compound_labels[idx]
                )
                result[compound] = val
            return result

        compoundmix_masslist = list(unit_obj.GetOverallMassComposition())
        compundmassdict = remap_composition(compoundmix_masslist)

        logging.info(
            f"{sharedutils.get_function_name()} - setting atlas with compoundmixes: {compundmassdict}"
        )

        return compundmassdict

    def _handle_pull_compoundmix(
        self, collection: at.AbstractVariableCollection, force_update: bool = True
    ):
        """Gets a dictionary of compound and mass ratio from endpoint, then updates the whole collection."""
        assert isinstance(collection, at.VarCollectionCompoundMassRatio)

        compoundmassdict = self._get_endpoint_dvar_compoundmix(self.unit_obj)

        collection_compounds = set(collection.compounds)
        updated_compounds = set(compoundmassdict.keys())
        discarded_compounds = collection_compounds.difference(updated_compounds)

        # For items in collection_compunds not in updated compounds, remove
        for compound in list(discarded_compounds):
            collection.remove_item(compound)

        # For others, update or create
        for compound, v in compoundmassdict.items():

            if compound not in collection:
                collection.add_item(at.VOCompoundMassRatio(compound, v))

            collection.set_value(compound, v, override_all_policies=force_update)

        # Purge values too low
        # collection.purge_nulls()

    def pull_from_endpoint(self, *args, **kwargs) -> Optional[at.ENTBase]:
        # Kwargs
        force_update = kwargs.get("force_update", None)

        # Mapping
        default_mapping = {
            at.VOContinuousVariable: self._handle_pull_contvar,
            at.VODiscreteVariable: self._handle_pull_discreteitems,
            at.VOCompoundMassRatio: self._handle_pull_compoundmix,
        }
        mapping = args[0] if args and args[0] is not None else default_mapping

        super().pull_from_endpoint(mapping, force_update=force_update) # type: ignore


####################

# EQUIPMENT


class DWSimNullInterface(DWSimStreamInterface):
    """Null interface for entities without DWSIM representation"""

    def push_to_endpoint(self):  # type: ignore
        pass

    def pull_from_endpoint(self):  # type: ignore
        pass

    @property
    def dwsim_simulator(self):
        pass

    @property
    def dwsim_interface(self):
        pass

    @property
    def sim_obj(self) -> Optional[Any]:
        pass

    @property
    def unit_obj(self) -> Optional[Any]:
        pass

    @property
    def graphic_obj(self) -> Optional[DWSIM.Interfaces.IGraphicObject]:
        pass


class DWSimEquipmentInterface:
    """Provides a base class `DWSimEquipmentManager` that manages the interaction the endpoint equipment objects.

    Key Concepts:

    - EquipmentEntity objects are use as drivers for the manager
    - Class variables provide subclass specific mappings between domain enums and end-point specific keywords and functions
    - Subclasses of `DWSimEquipmentManager` should override the class-level mappings and implement any custom logic required for specific equipment types.

    TODO - this class and stream class can be merged into a simpler parent class. Helper functions can become module functions.
    """

    DISCRETE_ITEMS_LOOKUP: Dict[
        Union[at.DiscreteItemSpecEnum, at.DiscreteSetSpecEnum],
        Optional[Union[Tuple[str, str], _PropParamAccessPath]],
    ] = {}
    # TODO - refactor Tuple[str, str] to its own helperclass called EnumAccessPath or something similar
    CONT_VARS_LOOKUP: Dict[at.ContVarSpecEnum, _PropParamAccessPath] = {}
    API_LOOKUP: _DWSimEntityAPILookup = _DWSimEntityAPILookup(
        object_type=ObjectType.Heater,
        unit_operation=UnitOperations.Heater,
        atlas_class=at.Heater,
    )

    def __init__(
        self,
        atlas_reference: Union[at.ENTBaseEquipment, at.ConversionReaction],
        endpoint_manager: MatrixDWSim,
    ):
        self._atlas_entity = atlas_reference
        self._endpoint_manager = endpoint_manager
        self._disc_items_lookup = copy.deepcopy(self.DISCRETE_ITEMS_LOOKUP)
        self._cont_vars_lookup = copy.deepcopy(self.CONT_VARS_LOOKUP)

    # TODO refactor this to a parent class used by EQUIPMENT and STREAM
    @property
    def endpoint_config(self):
        return self.manager.config

    @property
    def simulation_id(self) -> str:
        return self._endpoint_manager.label

    @property
    def entity_id(self) -> str:
        return self._atlas_entity.label

    @property
    def atlas_entity(self) -> Optional[at.ENTBaseEquipment]:
        return self._atlas_entity

    @property
    def manager(self):
        return self._endpoint_manager

    @property
    def dwsim_simulator(self):
        assert isinstance(self.manager, MatrixDWSim)
        return self.manager.dwsim_simulator

    @property
    def dwsim_interface(self):
        assert isinstance(self.manager, MatrixDWSim)
        return self.manager.dwsim_interface

    @property
    def sim_obj(self) -> Optional[Any]:
        """
        Queries the DWSim simulator for the object.
        """
        try:
            sim_obj = self.dwsim_simulator.GetObject(self.entity_id)
        except Exception as e:
            raise Exception(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not get object from DWSim - {e}"
            )

        if sim_obj is None:
            logging.warning(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Did not find in endpoint. Returning as None."
            )
        return sim_obj

    @property
    def unit_obj(self) -> Optional[Any]:
        """
        Returns a DWSim unit operation object.
        """
        obj = None
        try:
            obj = self.dwsim_simulator.GetObject(self.entity_id).GetAsObject()
        except Exception as e:
            raise Exception(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not get object from DWSim - {e}"
            )

        if obj is None:
            logging.warning(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Did not find in endpoint. Returning as None."
            )
        return obj

    @property
    def graphic_obj(self) -> Optional[DWSIM.Interfaces.IGraphicObject]:
        obj = None
        try:
            obj = self.unit_obj.GraphicObject
        except Exception as e:
            raise Exception(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not get object from DWSim - {e}"
            )

        if obj is None:
            logging.warning(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Did not find in endpoint. Returning as None."
            )
        return obj

    ####################

    # PUSH VALS

    # TODO unitfy this with set methods in stream interface, move it into parent OR classless function
    def _set_endpoint_dvar_discreteitem(
        self,
        discrete_set: at.DiscreteSetSpecEnum,
        discrete_item: at.DiscreteItemSpecEnum,
        skiplist: Optional[List[at.DiscreteSetSpecEnum]] = None,
        **kwargs,
    ):
        """
        Sets the discrete variable in the endpoint, given a discrete item.

        Args
            discrete_set: the set it lives in
            discrete_item: the item value to set
            skiplist: list of discretesets to skip. this can be for sets not present in endpoint (e.g. MaterialStreamType)
        """
        DEFAULT_SKIP_LIST = [at.DiscreteSetSpecEnum.MaterialStreamType]
        skiplist = skiplist if skiplist is not None else DEFAULT_SKIP_LIST

        # DEFENSIVE - skip if not correct type
        if not isinstance(discrete_item, at.DiscreteItemSpecEnum):
            return

        if discrete_set in skiplist:
            logging.info(f"Item in {skiplist}. skipping `{discrete_item}`")
            return

        # LOGIC

        selection_lookup = self._disc_items_lookup[discrete_item]

        # check mapping
        if selection_lookup == None:
            return

        # PropParamAccessPath Type
        elif isinstance(selection_lookup, _PropParamAccessPath):
            # Defensive
            if selection_lookup.enforced_setval is None:
                raise ValueError(
                    f"model_id:`{self.simulation_id}` entity_id:`{self.entity_id}` - {discrete_item} not set correctly in endpoint. Check implementation"
                )

            # Set val
            entity = _get_nested_obj(self.unit_obj, selection_lookup.attribute_seq)
            _set_attribute_value(
                entity,
                selection_lookup.attribute_keyword,
                selection_lookup.enforced_setval,
            )

            logging.info(
                f"{sharedutils.get_function_name()} - {self.simulation_id}.{self.entity_id} - non-enum selection set: `{discrete_item}`, value: `{selection_lookup.enforced_setval}`"
            )

        # MethodAccess Type
        elif isinstance(selection_lookup, _MethodAccessPath):
            try:
                getter_method = getattr(
                    self.unit_obj, selection_lookup.getter_method_name
                )
                val_prev = getter_method()

                setter_method = getattr(
                    self.unit_obj, selection_lookup.setter_method_name
                )
                val_curr = setter_method(selection_lookup.enforced_setval)
            except Exception as e:
                raise ValueError(
                    f"{self.simulation_id},{self.entity_id} - param=`{selection_lookup}`- {e}"
                )

        # STRING BASED SELECTION (e.g. calcpolicy)
        elif isinstance(selection_lookup, tuple) and len(selection_lookup) == 2:
            _attrib, _keyword = selection_lookup
            setattr(
                self.unit_obj,
                _attrib,
                getattr(getattr(self.unit_obj, _attrib), _keyword),
            )
            logging.info(
                f"{sharedutils.get_function_name()} - {self.simulation_id}.{self.entity_id} - enum selection set: `{discrete_item}`, keyword:`{_keyword}`, attribute: `{_attrib}`"
            )
        else:
            raise TypeError(f"wrong type")

        logging.info(
            f"{sharedutils.get_function_name()} - set discrete_item: {discrete_item}"
        )

        return

    def _set_endpoint_cvar_default(
        self,
        variable_key: at.ContVarSpecEnum,
        new_val: Union[float, list, tuple],
    ):
        """
        Sets the value of a property or parameter in the endpoint.
        Does not work for DICT endpoints.
        """

        def _set_via_prop(spec: _PropParamAccessPath, new_val):
            # Define val
            attrib_keyword = spec.attribute_keyword
            obj_seq = spec.attribute_seq
            val = spec.enforced_setval or new_val

            # Set correct object to call on
            dwsim_obj = _get_nested_obj(self.unit_obj, obj_seq)

            # Set val in endpoint
            try:
                val_prev = sharedutils.cast_value_dotnet2py(
                    getattr(dwsim_obj, attrib_keyword)
                )
                # For iterables, they are often protected values in endpoint. We workaround it by setting each element
                if isinstance(val, (tuple, list)):
                    for idx, val in enumerate(val):
                        _entity = getattr(dwsim_obj, attrib_keyword)
                        _entity[idx] = val
                else:
                    _set_attribute_value(dwsim_obj, attrib_keyword, val)
                val_curr = sharedutils.cast_value_dotnet2py(
                    getattr(dwsim_obj, attrib_keyword)
                )
            except Exception as e:
                raise ValueError(
                    f"{self.simulation_id},{self.entity_id} - param=`{variable_key}`,val=`{val}` - Could not set val in endpoint- {e}"
                )

            return val_prev, val_curr

        def _set_via_method(spec: _MethodAccessPath, new_val):
            val = new_val
            try:
                getter_method = getattr(self.unit_obj, spec.getter_method_name)
                val_prev = getter_method()

                setter_method = getattr(self.unit_obj, spec.setter_method_name)
                val_curr = setter_method(val)
            except Exception as e:
                raise ValueError(
                    f"{self.simulation_id},{self.entity_id} - param=`{variable_key}`,val=`{val}` - Could not set val in endpoint- {e}"
                )

            return val_prev, val_curr

        # Get attribute dataclass
        new_val = remap_value_check(variable_key, new_val, to_endpoint=True)
        if isinstance(variable_key, at.ContVarSpecEnum):
            attrib_dataclass = self._cont_vars_lookup[variable_key]
        else:
            raise KeyError(
                f"{self.simulation_id},{self.entity_id} - no keywords found for {variable_key}"
            )

        # Define val
        # Set
        mapper = {
            _PropParamAccessPath: _set_via_prop,
            _MethodAccessPath: _set_via_method,
        }

        handler = mapper[type(attrib_dataclass)]
        v_prev, v_curr = handler(attrib_dataclass, new_val)

        logging.info(
            f"{sharedutils.get_function_name()} - {self.simulation_id}/{self.entity_id}/{variable_key} - endpoint value set. `{v_prev}` -> `{v_curr}`"
        )

        return v_curr

    def handle_push_contvarcollections(self, collection: at.AbstractVariableCollection):
        """Push continuous variables to endpoint"""
        for var in collection.items:
            val = _cast_to_float(var.value)
            assert isinstance(val, (float, tuple, list))
            self._set_endpoint_cvar_default(var.variable_enum, val)

    def handle_push_discretesetcollection(
        self, collection: at.AbstractVariableCollection
    ):
        """Push discrete variables to endpoint"""
        for var in collection.items:
            self._set_endpoint_dvar_discreteitem(var.variable_enum, var.value)

    def create_in_endpoint(self):
        """
        Creates an equipment object in the DWSim simulator.
        For simpliciy, class variables are hard-coded to each subclass.

        Raises:
            ValueError: If the object could not be created.
        """
        object_type = self.API_LOOKUP.object_type
        model_object = self.dwsim_simulator.AddObject(object_type, 0, 0, self.entity_id)

        if self.sim_obj == None:
            raise ValueError(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not create object in DWSim"
            )

        logging.warning(
            f"{sharedutils.get_function_name()} - {self.entity_id} - Good News! it was created in endpoint."
        )
        return

    def push_to_endpoint(self, strategy: Optional[Dict[Type, Callable]] = None):  #
        """
        Push values from the atlas to the endpoint.

        This method uses an extensible strategy pattern to match collection item types to their respective functions for pushing the values to the endpoint.

        Args:
            strategy(Optional[Dict[Type, Callable]]):
            A mapping of collection item types to their respective push functions, with basic defaults
        """
        DEFAULT_STRATEGY = {
            at.VOContinuousVariable: self.handle_push_contvarcollections,
            at.VODiscreteVariable: self.handle_push_discretesetcollection,
        }
        if self.sim_obj is None:
            self.create_in_endpoint()

        strategy = strategy if strategy is not None else DEFAULT_STRATEGY

        collections: List[at.AbstractVariableCollection] = (
            self.atlas_entity.get_collections()
        )

        for collection in collections:
            func = strategy[collection.itemtype]
            func(collection)
            logging.info(f"Pushed to endpoint: {collection}")

    ####################

    # GETTERS
    def _get_endpoint_param_cvar_values(
        self,
        keyword: Optional[Any] = None,
    ) -> Dict[
        at.ContVarSpecEnum,
        Union[None, float, list, tuple],
    ]:
        """Gets the value of a DWSim object property or parameter."""

        def _process_value(result):
            """
            Processes the result of a query, handling cases where the result is a dictionary.

            - if dict has 1 item, return value
            -
            """
            if isinstance(result, dict):
                if len(result) == 1:
                    return list(result.values())[0]
                elif len(result) > 1 and keyword is not None:
                    return result[keyword]
                else:

                    first_value = list(result.values())[0]
                    logging.warning(
                        f"{result} - Result has no keyword specification to extract as value. Returning first value. Is this expected?"
                    )
                    return first_value
            return result

        result = {}
        cont_vars = self._atlas_entity.get_variables(
            filter=[at.VarCollectionContinuous], as_dict=True
        )

        # ROUTER
        for contvar_label in cont_vars.keys():
            mapper = {
                at.ContVarSpecEnum: self._cont_vars_lookup,
            }
            attrib_dataclass = mapper[type(contvar_label)][contvar_label]

            val = None

            # Handle PropParamAccessPath
            if isinstance(attrib_dataclass, _PropParamAccessPath):
                dwsim_keyword = attrib_dataclass.attribute_keyword
                dwsim_attrib_seq = attrib_dataclass.attribute_seq
                dwsim_obj = _get_nested_obj(self.unit_obj, dwsim_attrib_seq)
                val = _get_obj_attribute(dwsim_obj, dwsim_keyword)

            # Handle MethodAccessPath
            elif isinstance(attrib_dataclass, _MethodAccessPath):
                getter_method = getattr(
                    self.unit_obj, attrib_dataclass.getter_method_name
                )
                val = getter_method()

            else:
                raise AttributeError(
                    f"no handler found for `{attrib_dataclass}`. Check attrib dataclass."
                )

            val = sharedutils.cast_value_dotnet2py(val)
            val = _process_value(val)
            if isinstance(val, list):
                val = tuple(val)

            val = remap_value_check(contvar_label, val, to_endpoint=False)
            result[contvar_label] = val

        return result

    # @sharedutils.handle_exceptions(return_as_dict=True)
    # TODO refactor get/set methods to endpoint as functions that can be shared acorss classes. inject unit obj and lookups as needed using handlers
    def _get_endpoint_dvar_discreteitems(
        self,
    ) -> Dict[
        at.DiscreteSetSpecEnum,
        at.DiscreteItemSpecEnum,
    ]:
        """
        Get all disc item enum items of entity from endpoint.
        """

        # HELPER FUNCTIONS
        def _query_endpoint_via_methodaccess(
            mapping_sample: _MethodAccessPath,
        ) -> at.DiscreteItemSpecEnum:
            """Gets the atlas discrete item, given fixed return from endpoind"""
            getter_method = getattr(self.unit_obj, mapping_sample.getter_method_name)
            endpoint_return = getter_method()

            # Find the correct DiscreteItemEnum
            _result = next(
                k
                for k, v in self._disc_items_lookup.items()
                if isinstance(v, _MethodAccessPath)
                and v.enforced_setval == endpoint_return
            )

            if _result is None:
                raise ValueError(
                    f"model_id:`{self.simulation_id}` entity_id:`{self.entity_id}` - endpoint selection not found for {mapping_sample}. Check implementation"
                )

            assert isinstance(_result, at.DiscreteItemSpecEnum)
            return _result

        def _query_endpoint_via_tuple(mapping_sample: tuple) -> at.DiscreteItemSpecEnum:  # type: ignore

            _kw = mapping_sample[0]
            endpoint_return = getattr(self.unit_obj, _kw)
            _result = next(  # type: ignore
                (
                    selection
                    for selection, mapping in self._disc_items_lookup.items()
                    if isinstance(mapping, tuple) and mapping[1] == str(endpoint_return)
                ),
                None,
            )

            if _result is None:
                raise ValueError(
                    f"model_id:`{self.simulation_id}` entity_id:`{self.entity_id}` - endpoint selection not found for {mapping_sample}. Check implementation"
                )

            assert isinstance(_result, at.DiscreteItemSpecEnum)
            return _result

        def _query_endpoint_via_accesspath(mapping_sample: _PropParamAccessPath) -> at.DiscreteItemSpecEnum:  # type: ignore
            """
            Gets the endpoint selection via an access path, supporting enum-based selections.
            """
            _kw = mapping_sample.attribute_keyword
            attrib_seq = mapping_sample.attribute_seq
            endpoint_obj = _get_nested_obj(self.unit_obj, attrib_seq)
            endpoint_value = _get_obj_attribute(endpoint_obj, _kw)

            _result = next(  # type: ignore
                (
                    selection
                    for selection, mapping in self._disc_items_lookup.items()
                    if isinstance(mapping, _PropParamAccessPath)
                    and mapping.attribute_keyword == _kw
                    and mapping.enforced_setval == endpoint_value
                ),
                None,
            )

            # Check for no match
            if _result is None:
                raise ValueError(
                    f"model_id:`{self.simulation_id}` entity_id:`{self.entity_id}` - endpoint value `{endpoint_value}` not found for sample: `{mapping_sample}`. Check implementation"
                )

            assert isinstance(_result, at.DiscreteItemSpecEnum)
            return _result

        # LOGIC

        result = {}
        checked = []

        for k, v in self._disc_items_lookup.items():

            # Defensive: those that need overrides have NONE
            if v == None:
                continue

            # PropParamAccessPath
            elif isinstance(v, _PropParamAccessPath):
                kw = v.attribute_keyword
                if kw in checked:
                    continue
                else:
                    checked.append(kw)

                val = _query_endpoint_via_accesspath(v)

            # MethodAccess Type
            elif isinstance(v, _MethodAccessPath):
                kw = v.getter_method_name
                if kw in checked:
                    continue
                else:
                    checked.append(kw)

                val = _query_endpoint_via_methodaccess(v)

            # Tuple Access Path
            elif isinstance(v, tuple) and len(v) == 2:
                kw = v[0]
                if kw in checked:
                    continue
                else:
                    checked.append(kw)

                val = _query_endpoint_via_tuple(v)
            else:
                raise ValueError(f"{k}:{v} not looked-up. Check implementation")

            discrete_enum = self.atlas_entity.get_variable(val).variable_enum
            result[discrete_enum] = val

        return result

    def _handle_pull_discreteitems(
        self, collection: at.AbstractVariableCollection, force_update: bool = True
    ):
        values = self._get_endpoint_dvar_discreteitems()
        self.update_atlas(collection, values, force_update=force_update)

    def _handle_pull_contvar(
        self, collection: at.AbstractVariableCollection, force_update: bool = True
    ):
        values = self._get_endpoint_param_cvar_values()
        self.update_atlas(collection, values, force_update=force_update)

    def pull_from_endpoint(
        self,
        strategy: Optional[Dict[Type, Callable]] = None,  # map VarType to handler
        *,
        force_update: bool = True,
    ) -> Optional[at.ENTBase]:
        """
        Queries the endpoint for the latest values and updates the local entity with them.

        Args:
            strategy (Optional[Dict[Type, Callable]]): A strategy map of collection types to handler functions. The handler functions should take the following arguments:
                - `self`: The current instance of the `DWSimStreamInterface` class.
                - `collection`: The `at.AbstractVariableCollection` instance to be handled.
                - `force_update`: A boolean indicating whether to force the update of the values.
            force_update (bool, optional): Whether to force the update of the values. Defaults to `True`.
        """

        DEFAULT_STRATEGY = {
            at.VOContinuousVariable: self._handle_pull_contvar,
            at.VODiscreteVariable: self._handle_pull_discreteitems,
        }

        # Defensive
        if self.sim_obj is None:
            raise ValueError(
                f"{sharedutils.get_function_name()} - {self.entity_id} - Could not find object in simulator"
            )

        if isinstance(self, DWSimEnergyStreamInterface):
            return

        # assign if None
        strategy = strategy if strategy != None else DEFAULT_STRATEGY

        # Get collections and route by type
        collections: List[at.AbstractVariableCollection] = (
            self.atlas_entity.get_collections()
        )
        for collection in collections:
            func = strategy[collection.itemtype]
            func(collection, force_update)
            logging.info(f"atlas updated: collection:`{collection}`")

        return self.atlas_entity

    @sharedutils.handle_exceptions
    def remove_from_endpoint(self):
        """
        Removes the object from endpoint.
        """
        try:
            self.dwsim_simulator.DeleteSelectedObject(None, None, self.graphic_obj)
            logging.info(
                f"{sharedutils.get_function_name()} - {self.entity_id} has been removed from the simulator."
            )
        except Exception as e:
            raise Exception(
                f"{self.simulation_id}, {self.entity_id} - Could not remove endpoint from simulator - {e}"
            )

        if self.sim_obj:
            raise ValueError(
                f"remove_equipment() - {self.entity_id} was not removed from the simulator."
            )

    def update_atlas(
        self,
        collection: at.AbstractVariableCollection,
        endpoint_vals: Dict[Any, Any],  # dict of keys and value
        force_update: bool = True,
    ):
        if endpoint_vals is None:
            logging.warning(
                f"update_atlas: `{collection}` has no associated endpoint vals. Is this intended?"
            )

        for var in collection.items:
            var_key = collection._get_key(var)

            # Defensive
            if var_key not in endpoint_vals:
                logging.warning(
                    f"`update_atlas: {var_key}` not in endpoint return values. Is this intended?"
                )
                continue

            # [ ] - forcing override all policies. something wrong with kwarg chain, var force_update passing as None
            # Optimistic Updates
            force_update = force_update if force_update is not None else True
            self.atlas_entity.set_value(
                var_key, endpoint_vals[var_key], override_all_policies=force_update
            )


#########################################

# EQUIPMENT IMPLEMENTATION

"""
# EXCHANGERS
atlas.Heater: DWSimHeaterManager,
atlas.Cooler: DWSimCoolerManager,
atlas.HeatExchanger: DWSimHeatExchangerManager,
atlas.AirCooler2: DWSimAirCooler2Manager,
"""


class DWSimHeaterInterface(DWSimEquipmentInterface):
    """
    Heater Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Heater.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Heater_CalculationMode.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.HeatAddedOrRemoved: (
            "CalcMode",
            "HeatAddedRemoved",
        ),
        at.DiscreteItemSpecEnum.TemperatureChange: (
            "CalcMode",
            "TemperatureChange",
        ),
        at.DiscreteItemSpecEnum.OutletTemperature: (
            "CalcMode",
            "OutletTemperature",
        ),
        at.DiscreteItemSpecEnum.OutletVaporMoleFraction: (
            "CalcMode",
            "OutletVaporFraction",
        ),
    }

    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.Efficiency: _PropParamAccessPath("Eficiencia"),
        at.ContVarSpecEnum.PressureDrop: _PropParamAccessPath("DeltaP"),
        at.ContVarSpecEnum.OutletTemperature: _PropParamAccessPath("OutletTemperature"),
        at.ContVarSpecEnum.HeatAddedOrRemoved: _PropParamAccessPath("DeltaQ"),
        at.ContVarSpecEnum.OutletVaporFraction: _PropParamAccessPath(
            "OutletVaporFraction"
        ),
        at.ContVarSpecEnum.TemperatureChange: _PropParamAccessPath("DeltaT"),
    }

    API_LOOKUP: _DWSimEntityAPILookup = _DWSimEntityAPILookup(
        object_type=ObjectType.Heater,
        unit_operation=UnitOperations.Heater,
        atlas_class=at.Heater,
    )


class DWSimCoolerInterface(DWSimEquipmentInterface):
    """
    Cooler Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Cooler.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Cooler_CalculationMode.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.HeatAddedOrRemoved: ("CalcMode", "HeatRemoved"),
        at.DiscreteItemSpecEnum.TemperatureChange: (
            "CalcMode",
            "TemperatureChange",
        ),
        at.DiscreteItemSpecEnum.OutletTemperature: (
            "CalcMode",
            "OutletTemperature",
        ),
        at.DiscreteItemSpecEnum.OutletVaporMoleFraction: (
            "CalcMode",
            "OutletVaporFraction",
        ),
    }
    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.Efficiency: _PropParamAccessPath(
            "Eficiencia"
        ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Cooler.htm
        at.ContVarSpecEnum.PressureDrop: _PropParamAccessPath("DeltaP"),
        at.ContVarSpecEnum.OutletTemperature: _PropParamAccessPath("OutletTemperature"),
        at.ContVarSpecEnum.HeatAddedOrRemoved: _PropParamAccessPath("DeltaQ"),
        at.ContVarSpecEnum.OutletVaporFraction: _PropParamAccessPath(
            "OutletVaporFraction"
        ),
        at.ContVarSpecEnum.TemperatureChange: _PropParamAccessPath("DeltaT"),
    }

    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.Cooler,
        unit_operation=UnitOperations.Cooler,
        atlas_class=at.Cooler,
    )


class DWSimHeatExchangerInterface(DWSimEquipmentInterface):
    """
    HeatExchanger Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_HeatExchanger.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_HeatExchangerCalcMode.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.CalculateHotFluidOutletTemperature: (
            "CalcMode",
            "CalcTempHotOut",
        ),
        at.DiscreteItemSpecEnum.CalculateColdFluidOutletTemperature: (
            "CalcMode",
            "CalcTempColdOut",
        ),
        at.DiscreteItemSpecEnum.CalculateOutletTemperatures: (
            "CalcMode",
            "CalcBothTemp",
        ),
        at.DiscreteItemSpecEnum.CalculateOutletTemperatures_UA: (
            "CalcMode",
            "CalcBothTemp_UA",
        ),
        at.DiscreteItemSpecEnum.CalculateArea: ("CalcMode", "CalcArea"),
        at.DiscreteItemSpecEnum.ShellAndTubesExchangerRating: (
            "CalcMode",
            "ShellandTube_Rating",
        ),
        at.DiscreteItemSpecEnum.ShellAndTubesExchangerFoulingFactor: (
            "CalcMode",
            "ShellandTube_CalcFoulingFactor",
        ),
        at.DiscreteItemSpecEnum.PinchPoint: ("CalcMode", "PinchPoint"),
        at.DiscreteItemSpecEnum.SpecifyHeatTransferEfficiency: (
            "CalcMode",
            "ThermalEfficiency",
        ),
        at.DiscreteItemSpecEnum.SpecifyOutletMolarVaporFraction_Stream1: (
            "CalcMode",
            "OutletVaporFraction1",
        ),
        at.DiscreteItemSpecEnum.SpecifyOutletMolarVaporFraction_Stream2: (
            "CalcMode",
            "OutletVaporFraction2",
        ),
        at.DiscreteItemSpecEnum.FlowDirection_CounterCurrent: (
            "FlowDir",
            "CounterCurrent",
        ),
        at.DiscreteItemSpecEnum.FlowDirection_CoCurrent: ("FlowDir", "CoCurrent"),
        # CUSTOM ENUMS WITH METHOD OVERRIDES
        # TUBE LAYOUT
        at.DiscreteItemSpecEnum.TubeLayout_RotatedTriangle: _PropParamAccessPath(
            "Tube_Layout", ("STProperties",), 0
        ),
        at.DiscreteItemSpecEnum.TubeLayout_Triangle: _PropParamAccessPath(
            "Tube_Layout", ("STProperties",), 1
        ),
        at.DiscreteItemSpecEnum.TubeLayout_RotatedSquare: _PropParamAccessPath(
            "Tube_Layout", ("STProperties",), 2
        ),
        at.DiscreteItemSpecEnum.TubeLayout_Square: _PropParamAccessPath(
            "Tube_Layout", ("STProperties",), 3
        ),
        # FLUID IN TUBES
        at.DiscreteItemSpecEnum.FluidInTubes_Cold: _PropParamAccessPath(
            "Tube_Fluid", ("STProperties",), 0
        ),
        at.DiscreteItemSpecEnum.FluidInTubes_Hot: _PropParamAccessPath(
            "Tube_Fluid", ("STProperties",), 1
        ),
    }

    CONT_VARS_LOOKUP = {
        # Base
        at.ContVarSpecEnum.ColdFluidPressureDrop: _PropParamAccessPath(
            "ColdSidePressureDrop"
        ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_HeatExchanger.htm
        at.ContVarSpecEnum.HotFluidPressureDrop: _PropParamAccessPath(
            "HotSidePressureDrop"
        ),
        at.ContVarSpecEnum.ColdFluidOutletTemperature: _PropParamAccessPath(
            "ColdSideOutletTemperature"
        ),
        at.ContVarSpecEnum.HotFluidOutletTemperature: _PropParamAccessPath(
            "HotSideOutletTemperature"
        ),
        at.ContVarSpecEnum.HeatExchangeArea: _PropParamAccessPath("Area"),
        at.ContVarSpecEnum.HeatLoss: _PropParamAccessPath("HeatLoss"),
        at.ContVarSpecEnum.GlobalHeatTransferCoefficient: _PropParamAccessPath(
            "OverallCoefficient"
        ),
        at.ContVarSpecEnum.HeatExchange: _PropParamAccessPath("Q"),
        at.ContVarSpecEnum.MinimumTemperatureDifference: _PropParamAccessPath("MITA"),
        at.ContVarSpecEnum.HeatTransferEfficiency: _PropParamAccessPath(
            "ThermalEfficiency"
        ),
        at.ContVarSpecEnum.OutletVaporFractionFluid1: _PropParamAccessPath(
            "OutletVaporFraction1"
        ),
        at.ContVarSpecEnum.OutletVaporFractionFluid2: _PropParamAccessPath(
            "OutletVaporFraction2"
        ),
        # ST Properties
        at.ContVarSpecEnum.ShellInSeries: _PropParamAccessPath(
            "Shell_NumberOfShellsInSeries", ("STProperties",)
        ),
        at.ContVarSpecEnum.ShellPasses: _PropParamAccessPath(
            "Shell_NumberOfPasses", ("STProperties",)
        ),
        at.ContVarSpecEnum.InternalDiameterOfShell: _PropParamAccessPath(
            "Shell_Di", ("STProperties",)
        ),
        at.ContVarSpecEnum.BaffleCut: _PropParamAccessPath(
            "Shell_BaffleCut", ("STProperties",)
        ),
        at.ContVarSpecEnum.BaffleSpacing: _PropParamAccessPath(
            "Shell_BaffleSpacing", ("STProperties",)
        ),
        at.ContVarSpecEnum.InternalDiameterOfTube: _PropParamAccessPath(
            "Tube_Di", ("STProperties",)
        ),
        at.ContVarSpecEnum.ExternalDiameterOfTube: _PropParamAccessPath(
            "Tube_De", ("STProperties",)
        ),
        at.ContVarSpecEnum.TubeLength: _PropParamAccessPath(
            "Tube_Length", ("STProperties",)
        ),
        at.ContVarSpecEnum.ThermalConductivityOfTube: _PropParamAccessPath(
            "Tube_ThermalConductivity", ("STProperties",)
        ),
        at.ContVarSpecEnum.PassesPerShell: _PropParamAccessPath(
            "Tube_PassesPerShell", ("STProperties",)
        ),
        at.ContVarSpecEnum.TubesPerShell: _PropParamAccessPath(
            "Tube_NumberPerShell", ("STProperties",)
        ),
        at.ContVarSpecEnum.ShellFouling: _PropParamAccessPath(
            "Shell_Fouling", ("STProperties",)
        ),
        at.ContVarSpecEnum.TubeFouling: _PropParamAccessPath(
            "Tube_Fouling", ("STProperties",)
        ),
        at.ContVarSpecEnum.TubeSpacing: _PropParamAccessPath(
            "Tube_Pitch", ("STProperties",)
        ),
        at.ContVarSpecEnum.RoughnessTube: _PropParamAccessPath(
            "Tube_Roughness", ("STProperties",)
        ),
    }
    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.HeatExchanger,
        unit_operation=UnitOperations.HeatExchanger,
        atlas_class=at.HeatExchanger,
    )


class DWSimAirCooler2Interface(DWSimEquipmentInterface):
    """
    Air Cooler Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_AirCooler2.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_AirCooler2_CalcMode.htm
    """

    # [ ] refactor all tuples to use prop param access path
    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.SpecifyOutletTemp: (
            "CalculationMode",
            "SpecifyOutletTemperature",
        ),
        at.DiscreteItemSpecEnum.SpecifyTubeGeometry: (
            "CalculationMode",
            "SpecifyGeometry",
        ),
        at.DiscreteItemSpecEnum.SpecifyOverallUA: (
            "CalculationMode",
            "SpecifyUA",
        ),
    }
    CONT_VARS_LOOKUP = {
        # Base Attributes
        # TODO refactor proparamaccess path to be a simple list
        at.ContVarSpecEnum.FluidPressureDrop: _PropParamAccessPath("PressureDrop"),
        at.ContVarSpecEnum.OutletFluidTemperature: _PropParamAccessPath(
            "OutletTemperature"
        ),
        at.ContVarSpecEnum.InletAirTemperature: _PropParamAccessPath(
            "AirInletTemperature"
        ),
        at.ContVarSpecEnum.InletAirPressure: _PropParamAccessPath("AirPressure"),
        at.ContVarSpecEnum.ReferenceRotationOfFan: _PropParamAccessPath(
            "ReferenceFanSpeed"
        ),
        at.ContVarSpecEnum.ReferenceAirFlow: _PropParamAccessPath("ReferenceAirFlow"),
        at.ContVarSpecEnum.ActualRotation: _PropParamAccessPath("ActualFanSpeed"),
        at.ContVarSpecEnum.ElectricalPowerConversionFactor: _PropParamAccessPath(
            "ElectricalPowerConversionFactor"
        ),
        at.ContVarSpecEnum.OutletAirTemperature: _PropParamAccessPath(
            "AirOutletTemperature"
        ),
        at.ContVarSpecEnum.OverallUA: _PropParamAccessPath("OverallUA"),
        at.ContVarSpecEnum.HeatExchange: _PropParamAccessPath("HeatLoad"),
        at.ContVarSpecEnum.MaximumHeatExchange: _PropParamAccessPath("MaxHeatExchange"),
        at.ContVarSpecEnum.ExchangerEfficiency: _PropParamAccessPath(
            "ExchangerEfficiency"
        ),
        at.ContVarSpecEnum.TubeFouling: _PropParamAccessPath("Tube_Fouling"),
        at.ContVarSpecEnum.RoughnessTube: _PropParamAccessPath("Tube_Roughness"),
        at.ContVarSpecEnum.PowerRequired: _PropParamAccessPath("ElectricalPowerLoad"),
        at.ContVarSpecEnum.InternalDiameterOfTube: _PropParamAccessPath("Tube_Di"),
        at.ContVarSpecEnum.ExternalDiameterOfTube: _PropParamAccessPath("Tube_De"),
        at.ContVarSpecEnum.TubeLength: _PropParamAccessPath("Tube_Length"),
        at.ContVarSpecEnum.ThermalConductivityOfTube: _PropParamAccessPath(
            "Tube_ThermalConductivity"
        ),
        at.ContVarSpecEnum.NumberOfPasses: _PropParamAccessPath("Tube_PassesPerShell"),
        at.ContVarSpecEnum.NumberOfTubes: _PropParamAccessPath("Tube_NumberPerShell"),
        at.ContVarSpecEnum.ActualAirFlow: _PropParamAccessPath("ActualAirFlow"),
        at.ContVarSpecEnum.TubeSpacing: _PropParamAccessPath("Tube_Pitch"),
    }
    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.AirCooler2,
        unit_operation=UnitOperations.AirCooler2,
        atlas_class=at.AirCooler2,
    )

    def create_in_endpoint(self):
        super().create_in_endpoint()
        self.unit_obj.CreateConnectors()


"""
## PRESSURE CHANGERS
atlas.OrificePlate
atlas.Compressor
atlas.Pump
atlas.Expander
atlas.Valve
"""


class DWSimPumpInterface(DWSimEquipmentInterface):
    """
    Pump Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Pump.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Pump_CalculationMode.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.PerformanceCurve: ("CalcMode", "Curves"),
        at.DiscreteItemSpecEnum.PressureIncrease: ("CalcMode", "Delta_P"),
        at.DiscreteItemSpecEnum.OutletPressure: ("CalcMode", "OutletPressure"),
        at.DiscreteItemSpecEnum.PowerRequired: ("CalcMode", "Power"),
    }
    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.Efficiency: _PropParamAccessPath(
            "Eficiencia"
        ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Pump.htm
        at.ContVarSpecEnum.PressureIncrease: _PropParamAccessPath("DeltaP"),
        at.ContVarSpecEnum.OutletPressure: _PropParamAccessPath("Pout"),
        at.ContVarSpecEnum.OutletTemperature: _PropParamAccessPath("OutletTemperature"),
        at.ContVarSpecEnum.TemperatureChange: _PropParamAccessPath("DeltaT"),
        at.ContVarSpecEnum.PowerRequired: _PropParamAccessPath("DeltaQ"),
        at.ContVarSpecEnum.PerformanceCurves_FlowRate: _PropParamAccessPath(
            "CurveFlow"
        ),
        at.ContVarSpecEnum.PerformanceCurves_Head: _PropParamAccessPath("CurveHead"),
        at.ContVarSpecEnum.PerformanceCurves_Power: _PropParamAccessPath("CurvePower"),
        at.ContVarSpecEnum.PerformanceCurves_Eff: _PropParamAccessPath("CurveEff"),
        at.ContVarSpecEnum.PerformanceCurves_NPSH: _PropParamAccessPath("CurveNPSHr"),
        at.ContVarSpecEnum.PerformanceCurves_SystemHead: _PropParamAccessPath(
            "CurveSysHead"
        ),
    }

    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.Pump,
        unit_operation=UnitOperations.Pump,
        atlas_class=at.Pump,
    )


class DWSimCompressorInterface(DWSimEquipmentInterface):
    """
    Compressor Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Compressor.htm
    CalcModes:https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Compressor_CalculationMode.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.PerformanceCurves_Compressor: (
            "CalcMode",
            "Curves",
        ),
        at.DiscreteItemSpecEnum.PowerRequired_Compressor: (
            "CalcMode",
            "PowerRequired",
        ),
        at.DiscreteItemSpecEnum.OutletPressure_Compressor: (
            "CalcMode",
            "OutletPressure",
        ),
        # at.DiscreteItemSpecEnum.Known_Head_Compressor: ("CalcMode", "Head"),
        at.DiscreteItemSpecEnum.PressureIncrease_Compressor: (
            "CalcMode",
            "Delta_P",
        ),
        at.DiscreteItemSpecEnum.ThermodynamicProcess_Adiabatic: _PropParamAccessPath(
            attribute_keyword="ProcessPath",
            attribute_seq=None,
            enforced_setval=UnitOperations.Compressor.ProcessPathType.Adiabatic,
        ),
        at.DiscreteItemSpecEnum.ThermodynamicProcess_Polytropic: _PropParamAccessPath(
            attribute_keyword="ProcessPath",
            attribute_seq=None,
            enforced_setval=UnitOperations.Compressor.ProcessPathType.Polytropic,
        ),
    }

    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.PressureIncrease: _PropParamAccessPath(
            "DeltaP"
        ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Compressor.htm
        at.ContVarSpecEnum.OutletPressure: _PropParamAccessPath("POut"),
        at.ContVarSpecEnum.AdiabaticEff: _PropParamAccessPath("AdiabaticEfficiency"),
        at.ContVarSpecEnum.PolytropicEff: _PropParamAccessPath("PolytropicEfficiency"),
        at.ContVarSpecEnum.PowerRequired: _PropParamAccessPath("DeltaQ"),
        at.ContVarSpecEnum.OutletTemperature: _PropParamAccessPath("OutletTemperature"),
        at.ContVarSpecEnum.TemperatureChange: _PropParamAccessPath("DeltaT"),
        at.ContVarSpecEnum.AdiabaticCoeff: _PropParamAccessPath("AdiabaticCoefficient"),
        at.ContVarSpecEnum.PolytropicCoeff: _PropParamAccessPath(
            "PolytropicEfficiency"
        ),
        at.ContVarSpecEnum.AdiabaticHead: _PropParamAccessPath("AdiabaticHead"),
        at.ContVarSpecEnum.PolytropicHead: _PropParamAccessPath("PolytropicHead"),
        at.ContVarSpecEnum.PerformanceCurves_FlowRate: _PropParamAccessPath(
            "CurveFlow"
        ),
        at.ContVarSpecEnum.PerformanceCurves_Head: _PropParamAccessPath("CurveHead"),
        at.ContVarSpecEnum.PerformanceCurves_Power: _PropParamAccessPath("CurvePower"),
        at.ContVarSpecEnum.PerformanceCurves_Eff: _PropParamAccessPath("CurveEff"),
        at.ContVarSpecEnum.RotationSpeed: _PropParamAccessPath("Speed"),
    }

    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.Compressor,
        unit_operation=UnitOperations.Compressor,
        atlas_class=at.Compressor,
    )


class DWSimExpanderInterface(DWSimEquipmentInterface):
    """
    Expander Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Expander.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Expander_CalculationMode.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.PerformanceCurves_Expander: (
            "CalcMode",
            "Curves",
        ),
        at.DiscreteItemSpecEnum.OutletPressure_Expander: (
            "CalcMode",
            "OutletPressure",
        ),
        # at.DiscreteItemSpecEnum.Known_Head_Expander: ("CalcMode", "Head"),
        at.DiscreteItemSpecEnum.PressureDecrease_Expander: (
            "CalcMode",
            "Delta_P",
        ),
        at.DiscreteItemSpecEnum.PowerGenerated_Expander: (
            "CalcMode",
            "PowerGenerated",
        ),
        at.DiscreteItemSpecEnum.ThermodynamicProcess_Adiabatic: _PropParamAccessPath(
            attribute_keyword="ProcessPath",
            attribute_seq=None,
            enforced_setval=UnitOperations.Expander.ProcessPathType.Adiabatic,
        ),
        at.DiscreteItemSpecEnum.ThermodynamicProcess_Polytropic: _PropParamAccessPath(
            attribute_keyword="ProcessPath",
            attribute_seq=None,
            enforced_setval=UnitOperations.Expander.ProcessPathType.Polytropic,
        ),
    }
    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.PressureDecrease: _PropParamAccessPath(
            "DeltaP"
        ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Expander.htm
        at.ContVarSpecEnum.OutletPressure: _PropParamAccessPath("POut"),
        at.ContVarSpecEnum.AdiabaticEff: _PropParamAccessPath("AdiabaticEfficiency"),
        at.ContVarSpecEnum.PolytropicEff: _PropParamAccessPath("PolytropicEfficiency"),
        at.ContVarSpecEnum.PowerGenerated: _PropParamAccessPath("DeltaQ"),
        at.ContVarSpecEnum.OutletTemperature: _PropParamAccessPath("OutletTemperature"),
        at.ContVarSpecEnum.TemperatureChange: _PropParamAccessPath("DeltaT"),
        at.ContVarSpecEnum.AdiabaticCoeff: _PropParamAccessPath("AdiabaticCoefficient"),
        at.ContVarSpecEnum.PolytropicCoeff: _PropParamAccessPath(
            "PolytropicCoefficient"
        ),
        at.ContVarSpecEnum.AdiabaticHead: _PropParamAccessPath("AdiabaticHead"),
        at.ContVarSpecEnum.PolytropicHead: _PropParamAccessPath("PolytropicHead"),
        at.ContVarSpecEnum.PerformanceCurves_FlowRate: _PropParamAccessPath(
            "CurveFlow"
        ),
        at.ContVarSpecEnum.PerformanceCurves_Head: _PropParamAccessPath("CurveHead"),
        at.ContVarSpecEnum.PerformanceCurves_Power: _PropParamAccessPath("CurvePower"),
        at.ContVarSpecEnum.PerformanceCurves_Eff: _PropParamAccessPath("CurveEff"),
        at.ContVarSpecEnum.RotationSpeed: _PropParamAccessPath("Speed"),
    }

    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.Expander,
        unit_operation=UnitOperations.Expander,
        atlas_class=at.Expander,
    )


class DWSimValveInterface(DWSimEquipmentInterface):
    """
    Valve Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Valve.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Valve_CalculationMode.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.PressureDrop_Valve: ("CalcMode", "DeltaP"),
        at.DiscreteItemSpecEnum.OutletPressure_Valve: (
            "CalcMode",
            "OutletPressure",
        ),
    }
    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.PressureDrop: _PropParamAccessPath(
            "DeltaP"
        ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Valve.htm
        at.ContVarSpecEnum.OutletPressure: _PropParamAccessPath("OutletPressure"),
    }
    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.Valve,
        unit_operation=UnitOperations.Valve,
        atlas_class=at.Valve,
    )


class DWSimOrificePlateInterface(DWSimEquipmentInterface):
    """
    Orifice Plate Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_OrificePlate.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_OrificePlate_OrificeType.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.Pressure_Tappings_Corner: _PropParamAccessPath(
            attribute_keyword="OrifType",
            attribute_seq=None,
            enforced_setval=UnitOperations.OrificePlate.OrificeType.CornerTaps,
        ),
        at.DiscreteItemSpecEnum.Pressure_Tappings_Flange: _PropParamAccessPath(
            attribute_keyword="OrifType",
            attribute_seq=None,
            enforced_setval=UnitOperations.OrificePlate.OrificeType.FlangeTaps,
        ),
        at.DiscreteItemSpecEnum.Pressure_Tappings_Radius: _PropParamAccessPath(
            attribute_keyword="OrifType",
            attribute_seq=None,
            enforced_setval=UnitOperations.OrificePlate.OrificeType.RadiusTaps,
        ),
    }

    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.CorrectionFactor: _PropParamAccessPath(
            "CorrectionFactor"
        ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_OrificePlate.htm
        at.ContVarSpecEnum.OverallPressureDrop: _PropParamAccessPath(
            "OverallPressureDrop"
        ),
        at.ContVarSpecEnum.OrificePressureDrop: _PropParamAccessPath(
            "OrificePressureDrop"
        ),
        at.ContVarSpecEnum.OrificeBeta: _PropParamAccessPath("Beta"),
        at.ContVarSpecEnum.OrificeDiameter: _PropParamAccessPath("OrificeDiameter"),
        at.ContVarSpecEnum.InternalPipeDiameter: _PropParamAccessPath(
            "InternalPipeDiameter"
        ),
        at.ContVarSpecEnum.Orifice_Temperature_Change: _PropParamAccessPath("DeltaT"),
    }
    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.OrificePlate,
        unit_operation=UnitOperations.OrificePlate,
        atlas_class=at.OrificePlate,
    )


"""
## MIXER/SPLITTERS

atlas.StreamMixer
atlas.StreamSplitter
"""


# class DWSimSplitterInterface(DWSimEquipmentInterface):
#     """
#     Splitter Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Splitter.htm
#     CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Splitter_OpMode.htm
#     """

#     DISCRETE_ITEMS_LOOKUP = {
#         at.DiscreteItemSpecEnum.Stream_Split_Ratios: (
#             "OperationMode",
#             "SplitRatios",
#         ),
#         at.DiscreteItemSpecEnum.Stream_Mass_Flow_Specs: (
#             "OperationMode",
#             "StreamMassFlowSpec",
#         ),
#         at.DiscreteItemSpecEnum.Stream_Mole_Flow_Specs: (
#             "OperationMode",
#             "StreamMoleFlowSpec",
#         ),
#     }
#     CONT_VARS_LOOKUP = {
#         at.ContVarSpecEnum.Stream_Split_FlowSpec: _PropParamAccessPath(
#             "StreamFlowSpec"
#         ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Splitter.htm
#         # TODO - fix the type error
#         at.ContVarSpecEnum.Stream_Split_Ratio: _PropParamAccessPath(
#             attribute_keyword="Ratios"
#         ),
#     }
#     API_LOOKUP = _DWSimEntityAPILookup(
#         object_type=ObjectType.Splitter,
#         unit_operation=UnitOperations.Splitter,
#         atlas_class=at.StreamSplitter,
#     )


class DWSimMixerInterface(DWSimEquipmentInterface):
    """
    Mixer Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Mixer.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Mixer_PressureBehavior.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.Inlet_Minimum: (
            f"PressureCalculation",
            "Minimum",
        ),
        at.DiscreteItemSpecEnum.Inlet_Average: ("PressureCalculation", "Average"),
        at.DiscreteItemSpecEnum.Inlet_Maximum: ("PressureCalculation", "Maximum"),
    }
    CONT_VARS_LOOKUP = {}
    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.Mixer,
        unit_operation=UnitOperations.Mixer,
        atlas_class=at.StreamMixer,
    )


"""

## SEPERATORS

atlas.Vessel: DWSimVesselManager,
"""


class DWSimVesselInterface(DWSimEquipmentInterface):
    """
    Vessel Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Vessel.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Vessel_PressureBehavior.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.Inlet_Minimum: ("PressureCalculation", "Minimum"),
        at.DiscreteItemSpecEnum.Inlet_Average: ("PressureCalculation", "Average"),
        at.DiscreteItemSpecEnum.Inlet_Maximum: ("PressureCalculation", "Maximum"),
        at.DiscreteItemSpecEnum.OverrideSepPressure: _PropParamAccessPath(
            attribute_keyword="OverrideP", attribute_seq=None, enforced_setval=True
        ),
        at.DiscreteItemSpecEnum.NoOverrideSepPressure: _PropParamAccessPath(
            attribute_keyword="OverrideP", attribute_seq=None, enforced_setval=False
        ),
        at.DiscreteItemSpecEnum.OverrideSepTemperature: _PropParamAccessPath(
            attribute_keyword="OverrideT", attribute_seq=None, enforced_setval=True
        ),
        at.DiscreteItemSpecEnum.NoOverrideSepTemperature: _PropParamAccessPath(
            attribute_keyword="OverrideT", attribute_seq=None, enforced_setval=False
        ),
    }
    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.OverrideSaturationTemp: _PropParamAccessPath(
            "FlashTemperature"
        ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Vessel.htm
        at.ContVarSpecEnum.OverrideSaturationPressure: _PropParamAccessPath(
            "FlashPressure"
        ),
    }

    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.Vessel,
        unit_operation=UnitOperations.Vessel,
        atlas_class=at.Vessel,
    )


"""
## COLUMNS

atlas.ShortcutColumn: DWSimShortcutColumnManager,
atlas.DistillationColumn: DWSimDistillationColumnManager,
"""


class DWSimShortcutColumnInterface(DWSimEquipmentInterface):
    """
    Shortcut Column Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_ShortcutColumn.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_ShortcutColumn_CondenserType.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.CalcMode_SC_Total: ("condtype", "TotalCond"),
        at.DiscreteItemSpecEnum.CalcMode_SC_Partial: (
            "condtype",
            "PartialCond",
        ),
        at.DiscreteSetSpecEnum.LightKeyCompound: None,  # Override HACK- refactor this to its own collection
        at.DiscreteSetSpecEnum.HeavyKeyCompound: None,  # Override HACK refactor this to its own collection keyed by compounds
    }

    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.LightKeyMolFraction: _PropParamAccessPath(
            "m_lightkeymolarfrac"
        ),
        at.ContVarSpecEnum.HeavyKeyMolFraction: _PropParamAccessPath(
            "m_heavykeymolarfrac"
        ),
        at.ContVarSpecEnum.RefluxRatio: _PropParamAccessPath("m_refluxratio"),
        at.ContVarSpecEnum.CondenserPressure: _PropParamAccessPath(
            "m_condenserpressure"
        ),
        at.ContVarSpecEnum.ReboilerPressure: _PropParamAccessPath("m_boilerpressure"),
        at.ContVarSpecEnum.MinimumRefluxRatio: _PropParamAccessPath("m_Rmin"),
        at.ContVarSpecEnum.MinimumNumberOfTrays: _PropParamAccessPath("m_Nmin"),
        at.ContVarSpecEnum.ActualNumberOfTrays: _PropParamAccessPath("m_N"),
        at.ContVarSpecEnum.OptimalFeedStage: _PropParamAccessPath("ofs"),
        at.ContVarSpecEnum.StrippingLiquid: _PropParamAccessPath("L_"),
        at.ContVarSpecEnum.RectifyingLiquid: _PropParamAccessPath("L"),
        at.ContVarSpecEnum.StrippingVapor: _PropParamAccessPath("V_"),
        at.ContVarSpecEnum.RectifyingVapor: _PropParamAccessPath("V"),
        at.ContVarSpecEnum.CondenserDuty: _PropParamAccessPath("m_Qc"),
        at.ContVarSpecEnum.ReboilerDuty: _PropParamAccessPath("m_Qb"),
    }

    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.ShortcutColumn,
        unit_operation=UnitOperations.ShortcutColumn,
        atlas_class=at.ShortcutColumn,
    )
    

    ####################
    def create_in_endpoint(self):
        """
        Method override for base method. 
        Enables creation of energy streams for the shortcut column.

        Raises:
            ValueError: If the object could not be created.
        """
        super().create_in_endpoint()
        # Add energy streams
        self._init_energy_streams()
    
    # Add Custom init to add energy streams
    def _init_energy_streams(self):
        '''Custom init to add energy streams to the shortcut column'''
        entity_label = self.atlas_entity.label

        col_object = self.dwsim_simulator.GetObject(self.entity_id).GetAsObject()


        # E1
        e1 = entity_label + "_E1"
        e1_obj = self.dwsim_simulator.AddObject(ObjectType.EnergyStream, 50, 50,e1)
        self._endpoint_manager.dwsim_simulator.ConnectObjects(col_object.GraphicObject, e1_obj.GraphicObject, -1, -1 )
        
        # E2
        e2 = entity_label + "_E2"
        e2_obj = self.dwsim_simulator.AddObject(ObjectType.EnergyStream, 50, 50,e2)
        self._endpoint_manager.dwsim_simulator.ConnectObjects(e2_obj.GraphicObject,col_object.GraphicObject, -1, -1 )

    # GETTERS

    def _get_endpoint_dvar_lightheavycompounds(
        self,
    ) -> Dict[at.DiscreteSetSpecEnum, at.VOCompound]:

        result = dict()

        # Handle light key compound
        light_compound_label = self.unit_obj.m_lightkey
        if light_compound_label:
            result[at.DiscreteSetSpecEnum.LightKeyCompound] = (
                self.manager.get_vocompound_by_endpoint_label(light_compound_label)
            )

        # Handle heavy key compound
        heavy_compound_label = self.unit_obj.m_heavykey
        if heavy_compound_label:
            result[at.DiscreteSetSpecEnum.HeavyKeyCompound] = (
                self.manager.get_vocompound_by_endpoint_label(heavy_compound_label)
            )
        return result

    def _handle_pull_discreteitems(
        self, collection: at.AbstractVariableCollection, force_update: bool = True
    ):
        # Get standard discrete items
        values: Dict[at.DiscreteSetSpecEnum, Any] = (
            self._get_endpoint_dvar_discreteitems()
        )
        # Route compound selection through discrete collection
        compound_values = self._get_endpoint_dvar_lightheavycompounds()

        values.update(compound_values)

        self.update_atlas(collection, values, force_update=force_update)

    # SETTERS

    def _set_endpoint_dvar_keycompounds(
        self, discrete_set: at.DiscreteSetSpecEnum, compound: at.VOCompound
    ):

        # Defensive
        if not isinstance(compound, at.VOCompound):
            return

        map = {
            at.DiscreteSetSpecEnum.LightKeyCompound: "m_lightkey",
            at.DiscreteSetSpecEnum.HeavyKeyCompound: "m_heavykey",
        }

        setattr(
            self.unit_obj,
            map[discrete_set],  # rely on KeyError for wrong items.
            self.manager.get_compound_endpoint_label(compound),
        )

    def handle_push_discretesetcollection(
        self, collection: at.AbstractVariableCollection
    ):
        for var in collection.items:
            var_key = collection._get_key(var)

            # Route compound selection through specialized setter
            if var_key in [
                at.DiscreteSetSpecEnum.LightKeyCompound,
                at.DiscreteSetSpecEnum.HeavyKeyCompound,
            ]:
                self._set_endpoint_dvar_keycompounds(var_key, var.value)
            else:
                # Handle standard discrete items
                self._set_endpoint_dvar_discreteitem(var_key, var.value)


# """
# # REACTORS

# NOTE: this has a secondary base class.

# atlas.RCT_Conversion
# atlas.RCT_Equilibrium
# atlas.RCT_CSTR
# atlas.RCT_PFR

# """

####################


class DWSimConversionReactionInterface(DWSimEquipmentInterface):
    REACTION_PHASE_LABELS = {
        at.DiscreteItemSpecEnum.REACTION_MIXTURE: "Mixture",
        at.DiscreteItemSpecEnum.REACTION_VAPOR: "Vapor",
        at.DiscreteItemSpecEnum.REACTION_LIQUID: "Liquid",
    }

    def create_endpoint_reaction(self, reference: at.ConversionReaction):
        def is_string_number(expression) -> bool:
            if not isinstance(expression, str):
                return False

            assert isinstance(expression, str)
            if all(c.isdigit() or c in ".-" for c in expression):
                return True

        def try_and_cast_to_100_and_str(expression):
            if isinstance(expression, (float, int)):
                return str(expression * 100)

            if is_string_number(expression):
                return str(float(expression) * 100)

            return str(expression)

        # Convert to dwsim compound labels
        reaction_stoich_python = {
            self.endpoint_config.get_compound_label(
                vo_reactionstoich.compound
            ): vo_reactionstoich.value
            for vo_reactionstoich in reference.get_collection(
                at.VarCollectionReactionStoich
            ).items
        }

        # Convert to dotnet dict
        reaction_stoich_dotnet = Dictionary[str, float]()
        for k, v in reaction_stoich_python.items():
            reaction_stoich_dotnet.Add(k, v)

        # Package payload
        reaction_args = {
            "name": reference.label,
            "description": reference.description,
            "compounds_and_stoichoeffs": reaction_stoich_dotnet,
            "basecompound": self.endpoint_config.get_compound_label(
                reference.get_value(at.DiscreteSetSpecEnum.BaseCompound)
            ),
            "reactionphase": self.REACTION_PHASE_LABELS[
                reference.get_value(at.DiscreteSetSpecEnum.ReactionPhase)
            ],
            "conversionExpression": try_and_cast_to_100_and_str(
                reference.get_value(at.DiscreteSetSpecEnum.ConversionExpression)
            ),
        }

        # Create reaction
        reaction_obj = self.dwsim_simulator.CreateConversionReaction(
            *reaction_args.values()
        )
        self.dwsim_simulator.AddReaction(reaction_obj)

    def get_endpoint_reaction(self, label):
        return self.dwsim_simulator.GetReaction(label)

    def push_to_endpoint(self, strategy: Optional[Dict[Type, Callable]] = None):  #
        """
        Push values from the atlas to the endpoint.

        This method uses an extensible strategy pattern to match collection item types to their respective functions for pushing the values to the endpoint.

        Args:
            strategy(Optional[Dict[Type, Callable]]):
            A mapping of collection item types to their respective push functions, with basic defaults
        """
        if self.get_endpoint_reaction(self.atlas_entity.label) != None:
            # if it exists, then skip. We can assume this because its a fixed value
            return

        assert isinstance(self.atlas_entity, at.ConversionReaction)
        self.create_endpoint_reaction(self.atlas_entity)

    def pull_from_endpoint(
        self,
        strategy: Optional[Dict[Type, Callable]] = None,  # map VarType to handler
        *,
        force_update: bool = True,
    ) -> Optional[at.ENTBase]:

        logging.warning(
            "Reactions not configured to be pulled. Refactor this method if needed."
        )
        pass


class DWSimReactorBaseInterface(DWSimEquipmentInterface):
    # MAIN METHODS
    ...


class DWSimConversionReactorInterface(DWSimReactorBaseInterface):
    """
    Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_Reactor_Conversion.htm
    CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_OperationMode.htm
    """

    DISCRETE_ITEMS_LOOKUP = {
        at.DiscreteItemSpecEnum.RCT_Conversion_Adiabatic: (
            "ReactorOperationMode",
            "Adiabatic",
        ),
        at.DiscreteItemSpecEnum.RCT_Conversion_DefineOutletTemp: (
            "ReactorOperationMode",
            "OutletTemperature",
        ),
        at.DiscreteItemSpecEnum.RCT_Conversion_Isothermic: (
            "ReactorOperationMode",
            "Isothermic",
        ),
        # at.DiscreteSetSpecEnum.Conversions: None,
        # at.DiscreteSetSpecEnum.ComponentConversions: None,
    }

    CONT_VARS_LOOKUP = {
        at.ContVarSpecEnum.PressureDrop_ConversionReactor: _PropParamAccessPath(
            "DeltaP"
        ),
        at.ContVarSpecEnum.TemperatureDifference: _PropParamAccessPath("DeltaT"),
        at.ContVarSpecEnum.HeatLoad: _PropParamAccessPath("DeltaQ"),
        at.ContVarSpecEnum.OutletTemperature: _PropParamAccessPath("OutletTemperature"),
    }

    API_LOOKUP = _DWSimEntityAPILookup(
        object_type=ObjectType.RCT_Conversion,
        # unit_operation=UnitOperations.Reactors.Reactor_Conversion,
        atlas_class=at.ConversionReactor,
    )

    ####################

    def create_in_endpoint(self):
        """
        Override that creates equipment AND adds a reactionset to it.
        Done once only
        """
        super().create_in_endpoint()
        self.push_reactionset()

    def push_reactionset(self):
        """
        Creates a reactionset associated to this equipment.

        Requirements
            - reactions have already been created
            - equipment_id is unique as reactionset will be named after this equipment
        """

        assert isinstance(self.atlas_entity, at.ConversionReactor)
        reactionset_label = self.atlas_entity.label
        reactions = [reaction.label for reaction in list(self.atlas_entity.reactionset)]

        # Defensive - skip if created
        if reactionset_label in dict(self.dwsim_simulator.get_ReactionSets()):
            return

        # Create Reactionset
        rset = self.dwsim_simulator.CreateReactionSet(
            reactionset_label, reactionset_label
        )
        self.dwsim_simulator.AddReactionSet(rset)

        # Add Reactions
        """
        public void AddReactionToSet(
        string reactionID,
        string reactionSetID,
        bool enabled,
        int rank
        )
        """
        for idx, reaction in enumerate(reactions):
            # self.dwsim_simulator.AddReactionToSet(
            #     reaction, reactionset_label, True, idx + 1
            # )
            self.dwsim_simulator.AddReactionToSet(reaction, reactionset_label, True, 0)

        # Associate Reactionset to equipment
        self.unit_obj.ReactionSetID = reactionset_label


# class DWSimReactor_EquilibriumManager(DWSIMReactor_Base):
#     """
#     Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_Reactor_Equilibrium.htm
#     CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_OperationMode.htm
#     """

#     DISCRETE_ITEMS_LOOKUP = {
#         DiscreteItem.RCT_Equilibrium_Adiabatic: (
#             "OperationMode",
#             "Adiabatic",
#         ),
#         DiscreteItem.RCT_Equilibrium_DefineOutletTemp: (
#             "OperationMode",
#             "OutletTemperature",
#         ),
#         DiscreteItem.RCT_Equilibrium_Isothermic: (
#             "OperationMode",
#             "Isothermic",
#         ),
#     }
#     CONT_VARS_LOOKUP = {
#         ParameterLabel.PressureDrop: _PropParamAccessPath(
#             "DeltaP"
#         ),  # https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_Reactor_Equilibrium.htm
#         ParameterLabel.TemperatureDifference: _PropParamAccessPath("DeltaT"),
#         ParameterLabel.HeatLoad: _PropParamAccessPath("DeltaQ"),
#         ParameterLabel.InitialGibbsEnergy: _PropParamAccessPath("InitialGibbsEnergy"),
#         ParameterLabel.FinalGibbsEnergy: _PropParamAccessPath("FinalGibbsEnergy"),
#         ParameterLabel.ReactionExtents: _PropParamAccessPath("ReactionExtents"),
#         ParameterLabel.ComponentConversions: _PropParamAccessPath(
#             "ComponentConversions"
#         ),
#         ParameterLabel.OutletTemperature: _PropParamAccessPath("OutletTemperature"),
#     }

#     API_LOOKUP = _DWSimEquipmentAPILookup(
#         object_type=ObjectType.RCT_Equilibrium,
#         # unit_operation=UnitOperations.Reactors.RCT_Equilibrium,
#         atlas_class=atlas.RCT_Equilibrium,
#     )

#     @sharedutils.handle_exceptions
#     def set_reactionset(  # type: ignore
#         self,
#         compounds_and_stoichcoeffs: Dict[atlas.CompoundEnum, float],
#         base_compound: atlas.CompoundEnum,
#         reaction_phase: str,
#         basis: str,
#         t_approach: Optional[float] = 0,
#         in_keq_fT: Optional[str] = "",
#         units: Optional[str] = "",
#         name: Optional[str] = None,
#         description: Optional[str] = "na",
#     ):

#         # Validate inputs

#         self._validate_reaction_inputs(
#             base_compound=base_compound,
#             compounds_and_stoichcoeffs=compounds_and_stoichcoeffs,
#             reaction_phase=reaction_phase,
#             basis=basis,
#         )

#         # Create labels

#         REACTION_NAME, REACTION_SET_NAME = self._create_reaction_ids(name)

#         # Create Reaction Object

#         reaction_args = {
#             "name": REACTION_NAME,
#             "description": description,
#             "compounds_and_stoichoeffs": self._convertdict_enum2str(
#                 compounds_and_stoichcoeffs
#             ),
#             "basecompound": self.model_context.get_mapped_compound(base_compound),
#             "reactionphase": reaction_phase,
#             "basis": basis,
#             "units": units,
#             "Tapproach": sharedutils.cast_value_py2dotnet(t_approach),
#             "InKeq": in_keq_fT,
#         }
#         reaction_obj = self._create_reaction_object(
#             reaction_args, self.sim_obj.CreateEquiplibriumReaction
#         )

#         # Create Reaction Set

#         self._create_reaction_set(
#             reactionset_name=REACTION_SET_NAME,
#             reaction_obj=reaction_obj,
#             reactionset_description=description,
#         )

#         # Associate Eqpt to ReactionSet

#         self.unit_obj.ReactionSetID = REACTION_SET_NAME

#         return self


# class DWSimReactor_CSTRManager(DWSIMReactor_Base):
#     """
#     Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_Reactor_CSTR.htm
#     CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_OperationMode.htm
#     """

#     DISCRETE_ITEMS_LOOKUP = {
#         DiscreteItem.RCT_CSTR_Adiabatic: (
#             "OperationMode",
#             "Adiabatic",
#         ),
#         DiscreteItem.RCT_CSTR_DefineOutletTemp: (
#             "OperationMode",
#             "OutletTemperature",
#         ),
#         DiscreteItem.RCT_CSTR_Isothermic: (
#             "OperationMode",
#             "Isothermic",
#         ),
#     }
#     CONT_VARS_LOOKUP = {
#         ParameterLabel.ReactorVolume: _PropParamAccessPath(
#             "Volume"
#         ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_Reactors_Reactor_CSTR.htm
#         ParameterLabel.HeadSpace: _PropParamAccessPath("Headspace"),
#         ParameterLabel.PressureDrop: _PropParamAccessPath("DeltaP"),
#         ParameterLabel.CatalystAmount: _PropParamAccessPath("CatalystAmount"),
#         ParameterLabel.TemperatureDifference: _PropParamAccessPath("DeltaT"),
#         ParameterLabel.HeatLoad: _PropParamAccessPath("DeltaQ"),
#         ParameterLabel.ComponentConversions: _PropParamAccessPath(
#             "ComponentConversions"
#         ),
#         ParameterLabel.VaporResidenceTime: _PropParamAccessPath("ResidenceTimeV"),
#         ParameterLabel.LiquidResidenceTime: _PropParamAccessPath("ResidenceTimeL"),
#         ParameterLabel.ReactionCoordinate: _PropParamAccessPath("RxiT"),
#         ParameterLabel.ReactionRate: _PropParamAccessPath("Rxi"),
#         ParameterLabel.ReactionHeat: _PropParamAccessPath("DHRi"),
#         ParameterLabel.OutletTemperature: _PropParamAccessPath("OutletTemperature"),
#     }
#     API_LOOKUP = _DWSimEquipmentAPILookup(
#         object_type=ObjectType.RCT_CSTR,
#         # unit_operation=UnitOperations.Reactors.RCT_CSTR,
#         atlas_class=atlas.RCT_CSTR,
#     )

#     @sharedutils.handle_exceptions
#     def set_reactionset(  # type: ignore
#         self,
#         name: str,
#         compounds_and_stoichcoeffs: Dict[atlas.CompoundEnum, float],
#         direct_orders: Dict[atlas.CompoundEnum, float],
#         reverse_orders: Dict[atlas.CompoundEnum, float],
#         base_compound: atlas.CompoundEnum,
#         reaction_phase: str,
#         basis: str,
#         amount_units: Optional[str] = "kmol/m3",
#         rate_units: Optional[str] = "kmol/[m3.s]",
#         a_forward: Optional[float] = 0,
#         e_forward: Optional[float] = 0,
#         a_reverse: Optional[float] = 0,
#         e_reverse: Optional[float] = 0,
#         expr_forward: Optional[str] = "",
#         expr_reverse: Optional[str] = "",
#         description: Optional[str] = "na",
#     ):

#         # Validate inputs

#         self._validate_reaction_inputs(
#             base_compound=base_compound,
#             compounds_and_stoichcoeffs=compounds_and_stoichcoeffs,
#             reaction_phase=reaction_phase,
#             basis=basis,
#         )

#         # Create labels

#         REACTION_NAME, REACTION_SET_NAME = self._create_reaction_ids(name)

#         # Create Reaction Object

#         reaction_args = {
#             "name": REACTION_NAME,
#             "description": description,
#             "compounds_and_stoichcoeffs": self._convertdict_enum2str(
#                 compounds_and_stoichcoeffs
#             ),
#             "directorders": self._convertdict_enum2str(direct_orders),
#             "reverseorders": self._convertdict_enum2str(reverse_orders),
#             "basecompound": self.model_context.get_mapped_compound(base_compound),
#             "reactionphase": reaction_phase,
#             "basis": basis,
#             "amountunits": amount_units,
#             "rateunits": rate_units,
#             "Aforward": a_forward,
#             "Eforward": e_forward,
#             "Areverse": a_reverse,
#             "Ereverse": e_reverse,
#             "Expr_forward": expr_forward,
#             "Expr_reverse": expr_reverse,
#         }
#         reaction_obj = self._create_reaction_object(
#             reaction_args, self.sim_obj.CreateKineticReaction
#         )

#         # Create Reaction Set

#         self._create_reaction_set(
#             reactionset_name=REACTION_SET_NAME,
#             reaction_obj=reaction_obj,
#             reactionset_description=description,
#         )

#         # Associate Eqpt to ReactionSet

#         self.unit_obj.ReactionSetID = REACTION_SET_NAME

#         return self


# class DWSimReactor_PFRManager(DWSimReactor_CSTRManager):
#     """
#     Inherits from CSTRManager.
#     Unit Operation Reference: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_Reactor_PFR.htm
#     CalcModes: https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_OperationMode.htm
#     """

#     DISCRETE_ITEMS_LOOKUP = {
#         DiscreteItem.RCT_PFR_Adiabatic: ("OperationMode", "Adiabatic"),
#         DiscreteItem.RCT_PFR_DefineNonAdiabaticNonIsothermal: (
#             "OperationMode",
#             "NonIsothermalNonAdiabatic",
#         ),
#         DiscreteItem.RCT_PFR_DefineOutletTemp: (
#             "OperationMode",
#             "OutletTemperature",
#         ),
#         DiscreteItem.RCT_PFR_Isothermic: (
#             "OperationMode",
#             "Isothermic",
#         ),
#     }

#     CONT_VARS_LOOKUP = {
#         ParameterLabel.ReactorVolume: _PropParamAccessPath(
#             "Volume"
#         ),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_Reactors_Reactor_PFR.htm
#         ParameterLabel.OutletTemperature: _PropParamAccessPath("OutletTemperature"),
#         ParameterLabel.TemperatureDifference: _PropParamAccessPath("DeltaT"),
#         ParameterLabel.HeatLoad: _PropParamAccessPath("DeltaQ"),
#         ParameterLabel.ComponentConversions: _PropParamAccessPath(
#             "ComponentConversions"
#         ),
#         ParameterLabel.PressureDrop: _PropParamAccessPath("DeltaP"),
#         ParameterLabel.LiquidResidenceTime: _PropParamAccessPath("ResidenceTime"),
#         ParameterLabel.TubeLength: _PropParamAccessPath("Length"),
#         ParameterLabel.NumberOfTubes: _PropParamAccessPath("NumberOfTubes"),
#         ParameterLabel.ReactionCoordinate: _PropParamAccessPath("RxiT"),
#         ParameterLabel.ReactionRate: _PropParamAccessPath("Rxi"),
#         ParameterLabel.ReactionHeat: _PropParamAccessPath("DHRi"),
#         ParameterLabel.CatalystAmount: _PropParamAccessPath("CatalystLoading"),
#         ParameterLabel.CatalystParticleDiameter: _PropParamAccessPath(
#             "CatalystParticleDiameter"
#         ),
#         ParameterLabel.CatalystVoidFraction: _PropParamAccessPath(
#             "CatalystVoidFraction"
#         ),
#     }
#     API_LOOKUP = _DWSimEquipmentAPILookup(
#         object_type=ObjectType.RCT_PFR,
#         # unit_operation=UnitOperations.Reactors.RCT_PFR,
#         atlas_class=atlas.RCT_PFR,
#     )


# ####################


# class DWSimCompoundMixManager(matrix.BaseCompoundManager):
#     pass


####################


class MatrixDWSimConfig:
    """Lightweight class for keyword maps and factories. Customized per software package."""

    # VALIDATIONS
    # Class variable shared by all instances
    PROPERTYPACKAGE_MAPPER = {
        at.PropertyPackageEnum.PENG_ROBINSON: "Peng-Robinson (PR)",
        at.PropertyPackageEnum.COOL_PROP: "CoolProp",
        at.PropertyPackageEnum.IAPWS: "IAPWS-08 Seawater",
        at.PropertyPackageEnum.MODIFIED_UNIFAC: "Modified UNIFAC (NIST)",
        at.PropertyPackageEnum.NRTL: "NRTL",
        at.PropertyPackageEnum.PENG_ROBINSON_1978: "Peng-Robinson 1978 (PR78)",
        at.PropertyPackageEnum.PENG_ROBINSON_SV2M: "Peng-Robinson-Stryjek-Vera 2 (PRSV2-M)",
        at.PropertyPackageEnum.PENG_ROBINSON_SV2VL: "Peng-Robinson-Stryjek-Vera 2 (PRSV2-VL)",
        at.PropertyPackageEnum.RAOULT_LAW: "Raoult's Law",
        at.PropertyPackageEnum.SRK: "Soave-Redlich-Kwong (SRK)",
        at.PropertyPackageEnum.STEAM_TABLES: "Steam Tables (IAPWS-IF97)",
        at.PropertyPackageEnum.UNIQUAC: "UNIQUAC",
    }

    # LOOKUPS
    # This uses dict order for idx numbers
    # This dictionary only consist of CHEMSEP Compounds

    COMPOUND_MAPPER: Dict[str, str] = {
        "811-97-2": "R134a",
        "74-82-8": "Methane",
        "74-84-0": "Ethane",
        "74-98-6": "Propane",
        "106-97-8": "N-butane",
        "109-66-0": "N-pentane",
        "110-54-3": "N-hexane",
        "142-82-5": "N-heptane",
        "111-65-9": "N-octane",
        "111-84-2": "N-nonane",
        "124-18-5": "N-decane",
        "1120-21-4": "N-undecane",
        "112-40-3": "N-dodecane",
        "629-50-5": "N-tridecane",
        "629-59-4": "N-tetradecane",
        "629-62-9": "N-pentadecane",
        "544-76-3": "N-hexadecane",
        "629-78-7": "N-heptadecane",
        "593-45-3": "N-octadecane",
        "629-92-5": "N-nonadecane",
        "629-94-7": "N-heneicosane",
        "629-97-0": "N-docosane",
        "638-67-5": "N-tricosane",
        "646-31-1": "N-tetracosane",
        "629-99-2": "N-pentacosane",
        "630-01-3": "N-hexacosane",
        "593-49-7": "N-heptacosane",
        "630-02-4": "N-octacosane",
        "630-03-5": "N-nonacosane",
        "112-95-8": "N-eicosane",
        "75-28-5": "Isobutane",
        "78-78-4": "Isopentane",
        "107-83-5": "2-methylpentane",
        "96-14-0": "3-methylpentane",
        "591-76-4": "2-methylhexane",
        "589-34-4": "3-methylhexane",
        "592-27-8": "2-methylheptane",
        "589-81-1": "3-methylheptane",
        "589-53-7": "4-methylheptane",
        "3221-61-2": "2-methyloctane",
        "2216-33-3": "3-methyloctane",
        "2216-34-4": "4-methyloctane",
        "5911-04-6": "3-methylnonane",
        "871-83-0": "2-methylnonane",
        "17301-94-9": "4-methylnonane",
        "15869-85-9": "5-methylnonane",
        "463-82-1": "Neopentane",
        "75-83-2": "2,2-dimethylbutane",
        "79-29-8": "2,3-dimethylbutane",
        "15869-87-1": "2,2-dimethyloctane",
        "2532-58-3": "Cis-1,3-dimethylcyclopentane",
        "1759-58-6": "Trans-1,3-dimethylcyclopentane",
        "564-02-3": "2,2,3-trimethylpentane",
        "540-84-1": "2,2,4-trimethylpentane",
        "560-21-4": "2,3,3-trimethylpentane",
        "565-75-3": "2,3,4-trimethylpentane",
        "111-01-3": "Squalane",
        "617-78-7": "3-ethylpentane",
        "590-35-2": "2,2-dimethylpentane",
        "565-59-3": "2,3-dimethylpentane",
        "108-08-7": "2,4-dimethylpentane",
        "562-49-2": "3,3-dimethylpentane",
        "464-06-2": "2,2,3-trimethylbutane",
        "619-99-8": "3-ethylhexane",
        "590-73-8": "2,2-dimethylhexane",
        "584-94-1": "2,3-dimethylhexane",
        "589-43-5": "2,4-dimethylhexane",
        "592-13-2": "2,5-dimethylhexane",
        "563-16-6": "3,3-dimethylhexane",
        "583-48-2": "3,4-dimethylhexane",
        "609-26-7": "2-methyl-3-ethylpentane",
        "1067-08-9": "3-methyl-3-ethylpentane",
        "594-82-1": "2,2,3,3-tetramethylbutane",
        "3522-94-9": "2,2,5-trimethylhexane",
        "16747-30-1": "2,4,4-trimethylhexane",
        "1067-20-5": "3,3-diethylpentane",
        "7154-79-2": "2,2,3,3-tetramethylpentane",
        "1186-53-4": "2,2,3,4-tetramethylpentane",
        "1070-87-7": "2,2,4,4-tetramethylpentane",
        "16747-38-9": "2,3,3,4-tetramethylpentane",
        "15869-80-4": "3-ethylheptane",
        "1071-26-7": "2,2-dimethylheptane",
        "7154-80-5": "3,3,5-trimethylheptane",
        "287-92-3": "Cyclopentane",
        "110-82-7": "Cyclohexane",
        "287-23-0": "Cyclobutane",
        "96-37-7": "Methylcyclopentane",
        "1640-89-7": "Ethylcyclopentane",
        "2040-96-2": "N-propylcyclopentane",
        "1638-26-2": "1,1-dimethylcyclopentane",
        "1192-18-3": "Cis-1,2-dimethylcyclopentane",
        "822-50-4": "Trans-1,2-dimethylcyclopentane",
        "3875-51-2": "Isopropylcyclopentane",
        "16747-50-5": "1-methyl-1-ethylcyclopentane",
        "2040-95-1": "N-butylcyclopentane",
        "108-87-2": "Methylcyclohexane",
        "1678-91-7": "Ethylcyclohexane",
        "1678-92-8": "N-propylcyclohexane",
        "1678-93-9": "N-butylcyclohexane",
        "590-66-9": "1,1-dimethylcyclohexane",
        "2207-01-4": "Cis-1,2-dimethylcyclohexane",
        "6876-23-9": "Trans-1,2-dimethylcyclohexane",
        "638-04-0": "Cis-1,3-dimethylcyclohexane",
        "2207-03-6": "Trans-1,3-dimethylcyclohexane",
        "624-29-3": "Cis-1,4-dimethylcyclohexane",
        "2207-04-7": "Trans-1,4-dimethylcyclohexane",
        "3178-22-1": "Tert-butylcyclohexane",
        "493-01-6": "Cis-decahydronaphthalene",
        "493-02-7": "Trans-decahydronaphthalene",
        "74-85-1": "Ethylene",
        "115-07-1": "Propylene",
        "106-98-9": "1-butene",
        "109-67-1": "1-pentene",
        "592-41-6": "1-hexene",
        "592-76-7": "1-heptene",
        "111-66-0": "1-octene",
        "124-11-8": "1-nonene",
        "821-95-4": "1-undecene",
        "872-05-9": "1-decene",
        "112-41-4": "1-dodecene",
        "1120-36-1": "1-tetradecene",
        "629-73-2": "1-hexadecene",
        "112-88-9": "1-octadecene",
        "3452-07-1": "1-eicosene",
        "590-18-1": "Cis-2-butene",
        "624-64-6": "Trans-2-butene",
        "627-20-3": "Cis-2-pentene",
        "646-04-8": "Trans-2-pentene",
        "7688-21-3": "Cis-2-hexene",
        "4050-45-7": "Trans-2-hexene",
        "622-96-8": "P-ethyltoluene",
        "526-73-8": "1,2,3-trimethylbenzene",
        "95-63-6": "1,2,4-trimethylbenzene",
        "108-67-8": "Mesitylene",
        "538-93-2": "Isobutylbenzene",
        "99-87-6": "P-cymene",
        "105-05-5": "P-diethylbenzene",
        "95-93-2": "1,2,4,5-tetramethylbenzene",
        "115-11-7": "Isobutene",
        "563-46-2": "2-methyl-1-butene",
        "563-45-1": "3-methyl-1-butene",
        "513-35-9": "2-methyl-2-butene",
        "763-29-1": "2-methyl-1-pentene",
        "691-38-3": "4-methyl-cis-2-pentene",
        "674-76-0": "4-methyl-trans-2-pentene",
        "15870-10-7": "2-methyl-1-heptene",
        "2980-71-4": "2-methyl-1-nonene",
        "18516-37-5": "2-methyl-1-undecene",
        "18094-01-4": "2-methyl-1-tridecene",
        "29833-69-0": "2-methyl-1-pentadecene",
        "42764-74-9": "2-methyl-1-heptadecene",
        "52254-50-9": "2-methyl-1-nonadecene",
        "110-83-8": "Cyclohexene",
        "463-49-0": "Propadiene",
        "590-19-2": "1,2-butadiene",
        "106-99-0": "1,3-butadiene",
        "78-79-5": "Isoprene",
        "77-73-6": "Dicyclopentadiene",
        "74-86-2": "Acetylene",
        "74-99-7": "Methylacetylene",
        "689-97-4": "Vinylacetylene",
        "503-17-3": "Dimethylacetylene",
        "107-00-6": "Ethylacetylene",
        "71-43-2": "Benzene",
        "108-88-3": "Toluene",
        "100-41-4": "Ethylbenzene",
        "103-65-1": "N-propylbenzene",
        "104-51-8": "N-butylbenzene",
        "108-38-3": "M-xylene",
        "95-47-6": "O-xylene",
        "106-42-3": "P-xylene",
        "98-82-8": "Cumene",
        "611-14-3": "O-ethyltoluene",
        "620-14-4": "M-ethyltoluene",
        "135-98-8": "Sec-butylbenzene",
        "98-06-6": "Tert-butylbenzene",
        "527-84-4": "O-cymene",
        "535-77-3": "M-cymene",
        "135-01-3": "O-diethylbenzene",
        "141-93-5": "M-diethylbenzene",
        "488-23-3": "1,2,3,4-tetramethylbenzene",
        "527-53-7": "1,2,3,5-tetramethylbenzene",
        "2870-04-4": "2-ethyl-m-xylene",
        "1758-88-9": "2-ethyl-p-xylene",
        "874-41-9": "4-ethyl-m-xylene",
        "934-80-5": "4-ethyl-o-xylene",
        "1074-43-7": "1-methyl-3-n-propylbenzene",
        "1074-55-1": "1-methyl-4-n-propylbenzene",
        "100-18-5": "P-diisopropylbenzene",
        "100-42-5": "Styrene",
        "91-20-3": "Naphthalene",
        "90-12-0": "1-methylnaphthalene",
        "91-57-6": "2-methylnaphthalene",
        "605-02-7": "1-phenylnaphthalene",
        "83-32-9": "Acenaphthene",
        "86-73-7": "Fluorene",
        "85-01-8": "Phenanthrene",
        "206-44-0": "Fluoranthene",
        "129-00-0": "Pyrene",
        "218-01-9": "Chrysene",
        "92-52-4": "Biphenyl",
        "95-13-6": "Indene",
        "496-11-7": "Indane",
        "767-59-9": "1-methylindene",
        "2177-47-1": "2-methylindene",
        "132259-10-0": "Air",
        "630-08-0": "Carbon monoxide",
        "124-38-9": "Carbon dioxide",
        "7783-06-4": "Hydrogen sulfide",
        "10102-43-9": "Nitric oxide",
        "10102-44-0": "Nitrogen dioxide",
        "10024-97-2": "Nitrous oxide",
        "7446-09-5": "Sulfur dioxide",
        "7446-11-9": "Sulfur trioxide",
        "10544-73-7": "Nitrogen trioxide",
        "10544-72-6": "Nitrogen tetroxide",
        "7440-59-7": "Helium-4",
        "7782-41-4": "Fluorine",
        "7439-90-9": "Krypton",
        "7440-63-3": "Xenon",
        "10028-15-6": "Ozone",
        "463-58-1": "Carbonyl sulfide",
        "50-00-0": "Formaldehyde",
        "75-07-0": "Acetaldehyde",
        "78-84-2": "2-methylpropanal",
        "123-38-6": "Propanal",
        "123-72-8": "Butanal",
        "110-62-3": "Pentanal",
        "66-25-1": "Hexanal",
        "111-71-7": "Heptanal",
        "109-87-5": "Methylal",
        "14371-10-9": "t-Cinnamaldehyde",
        "100-52-7": "Benzaldehyde",
        "107-02-8": "Acrolein",
        "124-13-0": "Octanal",
        "124-19-6": "Nonanal",
        "112-31-2": "Decanal",
        "112-44-7": "Undecanal",
        "112-54-9": "Dodecanal",
        "10486-19-8": "Tridecanal",
        "124-25-4": "Tetradecanal",
        "2765-11-9": "Pentadecanal",
        "629-80-1": "Hexadecanal",
        "629-90-3": "Heptadecanal",
        "638-66-4": "Octadecanal",
        "17352-32-8": "Nonadecanal",
        "67-64-1": "Acetone",
        "78-93-3": "Methyl ethyl ketone",
        "96-22-0": "3-pentanone",
        "563-80-4": "Methyl isopropyl ketone",
        "108-94-1": "Cyclohexanone",
        "108-10-1": "Methyl isobutyl ketone",
        "106-35-4": "3-heptanone",
        "123-19-3": "4-heptanone",
        "589-38-8": "3-hexanone",
        "107-87-9": "2-pentanone",
        "591-78-6": "2-hexanone",
        "110-43-0": "2-heptanone",
        "110-12-3": "5-methyl-2-hexanone",
        "75-97-8": "3,3-dimethyl-2-butanone",
        "108-83-8": "Diisobutyl ketone",
        "565-80-0": "Diisopropyl ketone",
        "463-51-4": "Ketene",
        "141-79-7": "Mesityl oxide",
        "67-56-1": "Methanol",
        "64-17-5": "Ethanol",
        "71-23-8": "1-propanol",
        "71-36-3": "1-butanol",
        "71-41-0": "1-pentanol",
        "111-27-3": "1-hexanol",
        "111-70-6": "1-heptanol",
        "111-87-5": "1-octanol",
        "143-08-8": "1-nonanol",
        "112-30-1": "1-decanol",
        "112-42-5": "1-undecanol",
        "112-53-8": "1-dodecanol",
        "112-70-9": "1-tridecanol",
        "112-72-1": "1-tetradecanol",
        "629-76-5": "1-pentadecanol",
        "36653-82-4": "1-hexadecanol",
        "1454-85-9": "1-heptadecanol",
        "112-92-5": "1-octadecanol",
        "1454-84-8": "1-nonadecanol",
        "629-96-9": "1-eicosanol",
        "120-82-1": "1,2,4-trichlorobenzene",
        "541-73-1": "M-dichlorobenzene",
        "95-50-1": "O-dichlorobenzene",
        "106-46-7": "P-dichlorobenzene",
        "108-90-7": "Monochlorobenzene",
        "108-86-1": "Bromobenzene",
        "74-88-4": "Methyl iodide",
        "591-50-4": "Iodobenzene",
        "74-89-5": "Methylamine",
        "75-04-7": "Ethylamine",
        "75-50-3": "Trimethylamine",
        "109-89-7": "Diethylamine",
        "121-44-8": "Triethylamine",
        "108-18-9": "Diisopropylamine",
        "75-31-0": "Isopropylamine",
        "110-86-1": "Pyridine",
        "62-53-3": "Aniline",
        "106-50-3": "P-phenylenediamine",
        "107-15-3": "Ethylenediamine",
        "140-31-8": "N-aminoethyl piperazine",
        "111-40-0": "Diethylenetriamine",
        "110-85-0": "Piperazine",
        "74-90-8": "Hydrogen cyanide",
        "75-05-8": "Acetonitrile",
        "107-13-1": "Acrylonitrile",
        "126-98-7": "Methacrylonitrile",
        "107-12-0": "Propionitrile",
        "98-95-3": "Nitrobenzene",
        "75-52-5": "Nitromethane",
        "79-24-3": "Nitroethane",
        "108-03-2": "1-nitropropane",
        "79-46-9": "2-nitropropane",
        "627-05-4": "1-nitrobutane",
        "88-72-2": "O-nitrotoluene",
        "99-99-0": "P-nitrotoluene",
        "99-08-1": "M-nitrotoluene",
        "121-14-2": "2,4-dinitrotoluene",
        "606-20-2": "2,6-dinitrotoluene",
        "610-39-9": "3,4-dinitrotoluene",
        "619-15-8": "2,5-dinitrotoluene",
        "618-85-9": "3,5-dinitrotoluene",
        "118-96-7": "2,4,6-trinitrotoluene",
        "75-08-1": "Ethyl mercaptan",
        "74-93-1": "Methyl mercaptan",
        "107-03-9": "N-propyl mercaptan",
        "75-66-1": "Tert-butyl mercaptan",
        "513-44-0": "Isobutyl mercaptan",
        "513-53-1": "Sec-butyl mercaptan",
        "111-31-9": "N-hexyl mercaptan",
        "75-33-2": "Isopropyl mercaptan",
        "108-98-5": "Phenyl mercaptan",
        "75-15-0": "Carbon disulfide",
        "75-18-3": "Dimethyl sulfide",
        "110-02-1": "Thiophene",
        "624-89-5": "Methyl ethyl sulfide",
        "3877-15-4": "Methyl n-propyl sulfide",
        "6163-64-0": "Methyl t-butyl sulfide",
        "13286-92-5": "Methyl t-pentyl sulfide",
        "111-47-7": "Di-n-propyl sulfide",
        "352-93-2": "Diethyl sulfide",
        "110-81-6": "Diethyl disulfide",
        "624-92-0": "Dimethyl disulfide",
        "629-19-6": "Di-n-propyl disulfide",
        "110-06-5": "Di-tert-butyl disulfide",
        "20333-39-5": "Ethyl methyl disulfide",
        "30453-31-7": "Ethyl propyl disulfide",
        "882-33-7": "Diphenyl disulfide",
        "95-15-8": "Benzothiophene",
        "69-72-7": "Salicylic acid",
        "119-36-8": "Methyl salicylate",
        "108-65-6": "Propylene glycol monomethyl ether acetate",
        "98-01-1": "Furfural",
        "616-38-6": "Dimethyl carbonate",
        "105-58-8": "DiEthyl Carbonate",
        "623-53-0": "Methyl Ethyl Carbonate",
        "13509-27-8": "Methyl Phenyl Carbonate",
        "3878-46-4": "Ethyl Phenyl Carbonate",
        "102-09-0": "DiPhenyl Carbonate",
        "96-49-1": "Ethylene carbonate",
        "108-32-7": "Propylene carbonate",
        "542-52-9": "DiButyl Carbonate",
        "110-80-5": "2-ethoxyethanol",
        "107-98-2": "Propylene glycol monomethyl ether",
        "53716-82-8": "Cyrene",
        "123-42-2": "Diacetone alcohol",
        "67-63-0": "Isopropanol",
        "78-83-1": "2-methyl-1-propanol",
        "78-92-2": "2-butanol",
        "75-65-0": "2-methyl-2-propanol",
        "75-85-4": "2-methyl-2-butanol",
        "6032-29-7": "2-pentanol",
        "137-32-6": "2-methyl-1-butanol",
        "75-84-3": "2,2-dimethyl-1-propanol",
        "625-25-2": "2-Methyl-2-Heptanol",
        "108-93-0": "Cyclohexanol",
        "108-95-2": "Phenol",
        "108-39-4": "M-cresol",
        "95-48-7": "O-cresol",
        "106-44-5": "P-cresol",
        "4286-23-1": "P-isopropenyl phenol",
        "122-97-4": "3-phenyl-1-propanol",
        "100-51-6": "Benzyl alcohol",
        "526-75-0": "2,3-xylenol",
        "105-67-9": "2,4-xylenol",
        "95-87-4": "2,5-xylenol",
        "576-26-1": "2,6-xylenol",
        "95-65-8": "3,4-xylenol",
        "108-68-9": "3,5-xylenol",
        "527-60-6": "Mesitol",
        "599-64-4": "P-cumylphenol",
        "107-21-1": "Ethylene glycol",
        "111-46-6": "Diethylene glycol",
        "112-27-6": "Triethylene glycol",
        "112-60-7": "Tetraethylene glycol",
        "110-63-4": "1,4-butanediol",
        "56-81-5": "Glycerol",
        "57-55-6": "1,2-propylene glycol",
        "80-05-7": "Bisphenol a",
        "463-57-0": "Methane-diol",
        "4433-56-1": "Ethane-1,1-diol",
        "837-08-1": "O,p-bisphenol a",
        "4792-15-8": "PentaEthylene Glycol",
        "2615-15-8": "HexaEthylene Glycol",
        "110-98-5": "DiPropylene Glycol",
        "1638-16-0": "TriPropylene Glycol",
        "24800-25-7": "TetraPropylene Glycol",
        "21482-12-2": "PentaPropylene Glycol",
        "74388-92-4": "HexaPropylene Glycol",
        "64-19-7": "Acetic acid",
        "79-09-4": "Propionic acid",
        "107-92-6": "N-butyric acid",
        "64-18-6": "Formic acid",
        "142-62-1": "Caproic acid",
        "124-07-2": "Caprylic acid",
        "334-48-5": "Capric acid",
        "143-07-7": "Lauric acid",
        "544-63-8": "Myristic acid",
        "57-10-3": "Palmitic acid",
        "57-11-4": "Stearic acid",
        "79-10-7": "Acrylic acid",
        "79-41-4": "Methacrylic acid",
        "112-80-1": "Oleic acid",
        "60-33-3": "Linoleic acid",
        "463-40-1": "Linolenic acid",
        "144-62-7": "Oxalic acid",
        "124-04-9": "Adipic acid",
        "110-16-7": "Maleic acid",
        "141-82-2": "Malonic acid",
        "65-85-0": "Benzoic acid",
        "118-90-1": "O-toluic acid",
        "99-94-5": "P-toluic acid",
        "88-99-3": "Phthalic acid",
        "100-21-0": "Terephthalic acid",
        "140-10-3": "t-Cinnamic acid",
        "108-24-7": "Acetic anhydride",
        "108-31-6": "Maleic anhydride",
        "107-31-3": "Methyl formate",
        "109-94-4": "Ethyl formate",
        "110-74-7": "N-propyl formate",
        "2551-62-4": "Sulfur hexafluoride",
        "79-20-9": "Methyl acetate",
        "141-78-6": "Ethyl acetate",
        "109-60-4": "N-propyl acetate",
        "108-21-4": "Isopropyl acetate",
        "123-86-4": "N-butyl acetate",
        "110-19-0": "Isobutyl acetate",
        "628-63-7": "N-pentyl acetate",
        "108-05-4": "Vinyl acetate",
        "142-92-7": "N-hexyl acetate",
        "122-79-2": "Phenyl acetate",
        "554-12-1": "Methyl propionate",
        "108-59-8": "Dimethylmalonate",
        "106-70-7": "Methyl caproate",
        "111-11-5": "Methyl caprylate",
        "110-42-9": "Methyl caprate",
        "111-82-0": "Methyl laurate",
        "124-10-7": "Methyl myristate",
        "112-39-0": "Methyl palmitate",
        "112-61-8": "Methyl stearate",
        "538-23-8": "Tricaprylin",
        "621-71-6": "Tricaprin",
        "538-24-9": "Trilaurin",
        "555-45-3": "Trimyristin",
        "555-44-2": "Tripalmitin",
        "555-43-1": "Tristearin",
        "80-62-6": "Methyl methacrylate",
        "112-62-9": "Methyl oleate",
        "112-63-0": "Methyl linoleate",
        "301-00-8": "Methyl linolenate",
        "122-32-7": "Triolein",
        "537-40-6": "Trilinolein",
        "14465-68-0": "Trilenollenin",
        "120-61-6": "Dimethyl terephthalate",
        "93-89-0": "Ethyl benzoate",
        "115-10-6": "Dimethyl ether",
        "60-29-7": "Diethyl ether",
        "1634-04-4": "Methyl tert-butyl ether",
        "994-05-8": "Methyl tert-pentyl ether",
        "108-20-3": "Diisopropyl ether",
        "142-96-1": "Di-n-butyl ether",
        "6863-58-7": "Di-sec-butyl ether",
        "540-67-0": "Methyl ethyl ether",
        "557-17-5": "Methyl n-propyl ether",
        "1860-27-1": "Isopropyl butyl ether",
        "625-44-5": "Methyl isobutyl ether",
        "598-53-8": "Methyl isopropyl ether",
        "637-92-3": "Tert-butyl ethyl ether",
        "919-94-8": "Ethyl tert-pentyl ether",
        "111-34-2": "Butyl vinyl ether",
        "76589-16-7": "2-Methoxy-2-Methyl-Heptane",
        "100-66-3": "Anisole",
        "103-73-1": "Phenetole",
        "112-36-7": "Diethylene glycol diethyl ether",
        "101-84-8": "Diphenyl ether",
        "110-71-4": "1,2-dimethoxyethane",
        "111-96-6": "Diethylene glycol dimethyl ether",
        "112-49-2": "Triethylene glycol dimethyl ether",
        "143-24-8": "Tetraethylene glycol dimethyl ether",
        "1191-87-3": "Pentaethylene glycol dimethyl ether",
        "75-21-8": "Ethylene oxide",
        "109-99-9": "Tetrahydrofuran",
        "123-91-1": "1,4-dioxane",
        "75-56-9": "1,2-propylene oxide",
        "80-15-9": "Cumene hydroperoxide",
        "94-36-0": "Benzoyl peroxide",
        "80-43-3": "Dicumyl peroxide",
        "98-49-7": "P-diisopropylbenzene hydroperoxide",
        "3071-32-7": "Ethylbenzene hydroperoxide",
        "56-23-5": "Carbon tetrachloride",
        "67-66-3": "Chloroform",
        "74-87-3": "Methyl chloride",
        "79-01-6": "Trichloroethylene",
        "75-01-4": "Vinyl chloride",
        "79-00-5": "1,1,2-trichloroethane",
        "75-34-3": "1,1-dichloroethane",
        "107-06-2": "1,2-dichloroethane",
        "75-00-3": "Ethyl chloride",
        "68-12-2": "N,n-dimethylformamide",
        "127-19-5": "N,n-dimethylacetamide",
        "141-43-5": "Monoethanolamine",
        "111-42-2": "Diethanolamine",
        "102-71-6": "Triethanolamine",
        "111-41-1": "N-aminoethyl ethanolamine",
        "109-83-1": "Methylethanolamine",
        "108-01-0": "Dimethylethanolamine",
        "105-59-9": "Methyl DiEthanolAmine",
        "100-37-8": "Diethylethanolamine",
        "110-97-4": "Diisopropanolamine",
        "872-50-4": "N-methyl-2-pyrrolidone",
        "126-33-0": "Sulfolane",
        "67-68-5": "Dimethyl sulfoxide",
        "75-44-5": "Phosgene",
        "76-02-8": "Trichloroacetyl chloride",
        "79-36-7": "Dichloroacetyl chloride",
        "75-87-6": "Trichloroacetaldehyde",
        "79-02-7": "Dichloroacetaldehyde",
        "75-36-5": "Acetyl chloride",
        "107-07-3": "2-chloroethanol",
        "7440-37-1": "Argon",
        "7726-95-6": "Bromine",
        "7782-50-5": "Chlorine",
        "1333-74-0": "Hydrogen",
        "7440-01-9": "Neon",
        "7727-37-9": "Nitrogen",
        "7782-44-7": "Oxygen",
        "7439-97-6": "Mercury",
        "7647-01-0": "Hydrogen chloride",
        "10034-85-2": "Hydrogen iodide",
        "7697-37-2": "Nitric acid",
        "7664-41-7": "Ammonia",
        "7722-84-1": "Hydrogen peroxide",
        "7732-18-5": "Water",
        "4780-79-4": "1-Naphthalenemethanol",
    }

    # EquipmentFactory Lookup
    ENTITY_MAPPER: Dict[
        Type[at.ENTBase],
        Union[
            Type[DWSimEquipmentInterface],
            Type[DWSimStreamInterface],
            Type[DWSimNullInterface],
        ],
    ] = {
        # Pressure Changers
        at.OrificePlate: DWSimOrificePlateInterface,
        at.Compressor: DWSimCompressorInterface,
        at.Pump: DWSimPumpInterface,
        at.Expander: DWSimExpanderInterface,
        at.Valve: DWSimValveInterface,
        # Exchangers
        at.Heater: DWSimHeaterInterface,
        at.Cooler: DWSimCoolerInterface,
        at.HeatExchanger: DWSimHeatExchangerInterface,
        at.AirCooler2: DWSimAirCooler2Interface,
        # # Mixer/Splitters
        at.StreamMixer: DWSimMixerInterface,
        # at.StreamSplitter: DWSimSplitterInterface,
        # Seperators
        at.Vessel: DWSimVesselInterface,
        # Reactors
        at.ConversionReactor: DWSimConversionReactorInterface,
        # at.RCT_Equilibrium: DWSimReactor_EquilibriumManager,
        # at.RCT_CSTR: DWSimReactor_CSTRManager,
        # at.RCT_PFR: DWSimReactor_PFRManager,
        # Columns
        at.ShortcutColumn: DWSimShortcutColumnInterface,
        # at.DistillationColumn: DWSimDistillationColumnManager,
        # Streams
        at.ENTBaseStream: DWSimMaterialStreamInterface,
        at.InputStream: DWSimMaterialStreamInterface,
        at.OutputStream: DWSimMaterialStreamInterface,
        at.MaterialStream: DWSimMaterialStreamInterface,
        # Dummies
        at.BatteryIn: DWSimNullInterface,
        at.BatteryOut: DWSimNullInterface,
        # Reactions
        at.ConversionReaction: DWSimConversionReactionInterface,
    }

    def __init__(
        self,
        valid_packages: Optional[List[str]] = None,
        compound_mapper: Optional[Dict[str, str]] = None,
        entity_mapper: Optional[
            Dict[
                Type[at.ENTBase],
                Union[
                    Type[DWSimEquipmentInterface],
                    Type[DWSimStreamInterface],
                    Type[DWSimNullInterface],
                ],
            ]
        ] = None,
    ):
        """
        Initialize the ModelConfigurationMapper.
        """
        self.valid_packages = valid_packages or copy.deepcopy(
            list(self.PROPERTYPACKAGE_MAPPER.values())
        )
        self.compound_mapper = compound_mapper or copy.deepcopy(self.COMPOUND_MAPPER)
        self.entity_mapper: Dict[
            Type[at.ENTBase],
            Union[
                Type[DWSimStreamInterface],
                Type[DWSimEquipmentInterface],
                Type[DWSimNullInterface],
            ],
        ] = (
            entity_mapper
            if entity_mapper is not None
            else copy.deepcopy(self.ENTITY_MAPPER)
        )

    @property
    def compounds(self) -> List[str]:
        """Returns a fixed list of valid compound labels for dwsim based on whats been added. Can be used for indexing"""
        return list(self.compound_mapper.values())

    def is_valid_property_package(self, property_package: str) -> bool:
        """
        Check if a given property package is valid.
        """
        return property_package in self.PROPERTYPACKAGE_MAPPER.values()

    @sharedutils.handle_exceptions
    def get_compound_idx(
        self,
        compound: at.VOCompound,
    ) -> Tuple[int, str]:
        """
        Get the compound mapping for a given compound VO.

        Args:
            compound (atlas.CompoundEnum): The compound enum to get the mapping for.

        Returns:
            Optional[Tuple[int, str]]: A tuple containing the index and the valid endpoint key for the compound,
                or None if the compound is not valid.
        """
        compound_id = compound.id
        endpoint_label = self.compound_mapper[compound_id]
        idx = list(self.compound_mapper.keys()).index(compound_id)

        return idx, endpoint_label

    def get_compound_id(self, endpoint_label: str) -> str:
        """
        Given an endpoint label, get the compound ID
        """
        reverse_lookup = {v: k for k, v in self.COMPOUND_MAPPER.items()}
        return reverse_lookup[endpoint_label]

    def get_compound_label(self, compound: at.VOCompound) -> str:
        """
        Return the compound label for the endpoint
        """
        if isinstance(compound, at.VOCompound):
            return self.COMPOUND_MAPPER[compound.id]
        elif isinstance(compound, str):
            # Sometimes it passes a string. I can't debug why this is happening
            return self.COMPOUND_MAPPER[compound]
        else:
            raise KeyError(f"Unknown type")

    @sharedutils.handle_exceptions(return_as_list=True)
    def _get_compound_labels(self) -> List[str]:
        """
        Get a list of compound labels for endpoint. Assumes the compound key lookup is exhaustive.

        Raises:
            ValueError: If the compound key lookup is empty.
        """
        keys = list(self.compound_mapper.values())
        if len(keys) == 0:
            raise ValueError("Compound key lookup is empty. Empty list returned")
        return keys

    # def get_entity_class(
    #     self, entity: at.ENTBaseEquipmentAndStream
    # ) -> Union[Type[DWSimStreamInterface], Type[DWSimEquipmentInterface]]:
    #     """
    #     Get the interface type for a given equipment entity.
    #     """
    #     e = self.entity_mapper[
    #         type(entity)
    #     ]  # this automatically raises a KeyError #type: ignore
    #     return e

    @sharedutils.handle_exceptions()
    def get_entity_class(
        self, entity: at.ENTBase
    ) -> Union[Type[DWSimStreamInterface], Type[DWSimEquipmentInterface]]:
        """Get the interface type for a given equipment entity."""
        entity_type = type(entity)
        if entity_type not in self.entity_mapper:
            raise KeyError(f"No interface mapping found for entity type: {entity_type}")
        return self.entity_mapper[entity_type]


####################

class MatrixDWSim(BaseMatrixSimulator, StrMixin):
    """DWSIM implementation of the matrix simulator"""
    
    @classmethod
    def from_config(cls, simulation_label: str, config: Optional[Dict[str, Any]] = None) -> 'MatrixDWSim':
        """Create a DWSIM simulator from a generic config dictionary"""
        config = config or {}
        
        # Extract DWSIM-specific configurations with defaults
        dwsim_config = config.get('dwsim_config')
        if isinstance(dwsim_config, dict):
            # Convert dict to DWSimConfig if needed
            dwsim_config = MatrixDWSimConfig(
                valid_packages=dwsim_config.get('valid_packages'),
                compound_mapper=dwsim_config.get('compound_mapper'),
                entity_mapper=dwsim_config.get('entity_mapper')
            )
        
        return cls(
            simulation_label=simulation_label,
            configs=dwsim_config,
            prop_package=config.get('property_package', "Peng-Robinson (PR)"),
            template_filename=config.get('template_filename', "_init_template.dwxmz")
        )
    
    def __init__(
        self,
        simulation_label: str,
        *,
        configs: Optional[MatrixDWSimConfig] = None,
        prop_package: str = "Peng-Robinson (PR)",
        template_filename: str = "_init_template.dwxmz",
    ):
        """Initialize a DWSIM simulator with specific configuration"""
        super().__init__(simulation_label)
        self._interface_collection: Dict[str, Union[DWSimEquipmentInterface, DWSimStreamInterface]] = {}
        self.config = configs or MatrixDWSimConfig()
        self._dwsim_property_package = prop_package
        self._dwsim_interface = None
        self._dwsim_simulator = None
        self._template_filename = template_filename

    # ABSTRACTMETHODS

    def attach_model(self, domain_model: at.AtlasRoot, share_model_state: bool = True):
        if share_model_state != True:
            domain_model = copy.deepcopy(domain_model)
        domain_model.propogate_compounds_across_streams()  # just in case this was not done
        domain_model.propogate_vars_to_uuid_collection()
        self._atlas = domain_model

    def setup_matrix(self) -> bool:
        """
        Setups the configuration in the endpoint
        """
        self._setup_endpoint()
        return True

    def run_matrix(self) -> at.AtlasRoot:
        """
        Pushes the latest state from the bound atlas
        Runs the simulation
        Pulls the results back to atlas. (Note: this updates ALL variables)
        """
        self._push_from_atlas()
        self._handle_run_model()
        return self._pull_to_atlas()

    ####################

    def __repr__(self):
        return f"<{self.__class__.__name__}(sim_id={self.label})>"

    @property
    def atlas(self) -> at.AtlasRoot:
        if not self._atlas:
            raise ValueError(f"No atlas model associated. Please use `add_model`")
        return self._atlas

    @property
    def label(self) -> str:
        """Returns the ID of the simulation"""
        return self._simulation_label

    @property
    def dwsim_simulator(self) -> TDWSimModelSim:
        if self._dwsim_simulator == None:
            raise AttributeError(
                f"No simulator setup yet. Have you run setup_simulation?"
            )

        return self._dwsim_simulator

    @property
    def dwsim_interface(self) -> TDWSimModelInterface:
        if self._dwsim_interface == None:
            raise AttributeError(
                f"No simulator setup yet. Have you run setup_simulation?"
            )
        return self._dwsim_interface

    @property
    def dwsim_property_package(self) -> str:
        return self._dwsim_property_package

    @dwsim_property_package.setter
    def dwsim_property_package(self, package_name: str):

        # Check if valid against configurator
        if not self.config.is_valid_property_package(package_name):
            raise ValueError(
                f"Invalid property package {package_name}."
                f"Valid options are {self.config.valid_packages}"
            )

        # Set the property package in the DWSim simulator
        if self.dwsim_simulator is None:
            raise ValueError("DWSim simulator not initialized.")

        # self.dwsim_simulator.SetSelectedPropertyPackage(package_name)
        self.dwsim_simulator.CreateAndAddPropertyPackage(package_name)
        self._dwsim_property_package = package_name

    # BATCH ACTIONS

    @staticmethod
    def select_package(compounds: List[at.VOCompound]) -> at.PropertyPackageEnum:
        """
        Sets up the package based on most common intersection of associated packages
        """
        compound_enums = [at.CASCompoundEnum.from_stringify(c.id) for c in compounds]

        packages = [package for c in compound_enums for package in c.get_packages()]
        counts = collections.Counter(packages)
        return (
            counts.most_common(1)[0][0]
            if counts
            else at.PropertyPackageEnum.PENG_ROBINSON
        )

    def _push_from_atlas(self) -> bool:
        """
        Given an atlas entity, push it to a dwsim endpoint
        """

        try:
            # Setup package
            package = self.select_package(self.atlas.compounds)
            package_name = self.config.PROPERTYPACKAGE_MAPPER[package]
            self.dwsim_property_package = package_name

            # Push Reactions
            for reaction in self.atlas.reaction_collection.items:
                assert isinstance(reaction, at.ConversionReaction)
                DWSimConversionReactionInterface(reaction, self).push_to_endpoint()

            # Push Equipment
            for _id in self.atlas.equipments:
                atlas_obj = self.atlas.get_equipment(by_label=_id)

                # Defensive
                if atlas_obj is None:
                    continue

                interface = self.get_interface(atlas_obj)
                interface.push_to_endpoint()

            # Push Streams
            for _id in self.atlas.streams:
                atlas_obj = self.atlas.get_stream(by_label=_id)

                # Defensive
                if atlas_obj is None:
                    continue

                interface = self.get_interface(atlas_obj)
                interface.push_to_endpoint()

            # Connect
            for edge in self.atlas.edgelist:
                self._connect_objects(edge[0], edge[1], edge[2])

        except Exception as e:
            raise ValueError(f"issue with atlas push: {e}")

        return True

    def _pull_to_atlas(self) -> at.AtlasRoot:
        """
        Based on streams and entities already existing in Atlas, search matrix and pull accordingly
        """
        try:
            # Push Equipment
            for _id in self.atlas.equipments:
                atlas_obj = self.atlas.get_equipment(by_label=_id)

                # Defensive
                if atlas_obj is None:
                    continue

                interface = self.get_interface(atlas_obj)
                interface.pull_from_endpoint()

            # Push Streams
            for _id in self.atlas.streams:
                atlas_obj = self.atlas.get_stream(by_label=_id)
                # Defensive
                if atlas_obj is None:
                    continue

                interface = self.get_interface(atlas_obj)
                interface.pull_from_endpoint()

        except Exception as e:
            raise ValueError(f"issue with atlas push: {e}")

        return self.atlas

    # SIMULATOR STUFF

    def _setup_endpoint(self):
        """
        Method to set global configurations for the model file.

        Arguments:
            ModelConfigs - dataclass with package-specific setup instructions

        Raises:
            ValueError - if the model file cannot be loaded or compound cannot be created
        """
        # Load file
        rel_filepath = f"backend/core/_matrix/{self._template_filename}"
        abs_filepath = sharedutils.create_absolute_path_object(rel_filepath)

        try:
            self._dwsim_interface = Automation3()
            self._dwsim_simulator = self.dwsim_interface.LoadFlowsheet(
                str(abs_filepath)
            )
        except Exception as e:
            raise ValueError(f"Failed to load model file: {str(e)}")

        # Add Compounds if atlas present
        if self._atlas is not None:
            for compound in self.atlas.compounds:
                compound_label = self.config.get_compound_label(compound)
                try:
                    self.dwsim_simulator.AddCompound(compound_label)
                except Exception as e:
                    raise KeyError(f"Failed to add compound {compound}: {str(e)}")

        # Add Property Package
        try:
            self.dwsim_simulator.CreateAndAddPropertyPackage(
                self.dwsim_property_package
            )
        except Exception as e:
            raise ValueError(f"Failed to create and add property package: {str(e)}")

        # Sort
        self.dwsim_simulator.AutoLayout()

        logging.info(
            f"{sharedutils.get_function_name()} - {self.dwsim_interface}, {self.dwsim_simulator} setup successfully."
        )

    def _handle_run_model(self):

        Settings.SolverMode = 0
        self.dwsim_simulator.AutoLayout()

        try:
            errors = self.dwsim_interface.CalculateFlowsheet2(self.dwsim_simulator)
        except Exception as e:
            raise ValueError(f"Run model error: {e}")
        if errors:
            raise ValueError(f"Run model error: {errors}")

    # ENTITY STUFF

    def _create_entity_interface(
        self, entity: at.ENTBase
    ) -> Union[DWSimEquipmentInterface, DWSimStreamInterface]:
        """
        Creates an interface object that uses atlas to drive the endpoint.

        Based on mappers in the config.
        """

        Constructor = self.config.get_entity_class(entity)
        interface = Constructor(entity, self)  # type: ignore

        # if isinstance(entity, at.ENTBaseEquipment):
        #     self._attach_estream_to_equipment(entity)

        return interface  # type: ignore  # TODO - fix generic type inheritence

    # def _attach_estream_to_equipment(self, atlas_entity: at.ENTBaseEquipment):
    #     """Create and attach estream to an equipment if it has an appropriate parameter that requires it"""

    #     def create_output_estream():

    #         energy_interface = DWSimEnergyStreamInterface(
    #             atlas_entity, self, prefix="out"
    #         )
    #         energy_interface.push_to_endpoint()
    #         self._add_interface(energy_interface)
    #         self._connect_objects(atlas_entity.label, None, energy_interface.entity_id)

    #     def create_input_e_stream():

    #         energy_interface = DWSimEnergyStreamInterface(
    #             atlas_entity, self, prefix="in"
    #         )
    #         energy_interface.push_to_endpoint()
    #         self._add_interface(energy_interface)
    #         self._connect_objects(None, atlas_entity.label, energy_interface.entity_id)

    #     if isinstance(atlas_entity, at.Pump):
    #         create_input_e_stream()

    #     elif isinstance(atlas_entity, at.Expander):
    #         create_output_estream()

    #     elif isinstance(atlas_entity, at.Compressor):
    #         create_input_e_stream()

    #     elif isinstance(atlas_entity, at.Compressor):
    #         create_input_e_stream()

    #     elif isinstance(atlas_entity, at.Cooler):
    #         create_output_estream()

    #     elif isinstance(atlas_entity, at.Heater):
    #         create_input_e_stream()

    #     elif isinstance(atlas_entity, at.ShortcutColumn):
    #         create_input_e_stream()
    #         create_output_estream()

    #     elif isinstance(atlas_entity, at.ConversionReactor):
    #         create_output_estream()

    #     elif isinstance(atlas_entity, at.AirCooler2):
    #         create_input_e_stream()

    #     else:
    #         pass

    def _connect_objects(
        self,
        upstream_label: Optional[str],
        downstream_label: Optional[str],
        stream_label: str,
    ):
        """
        Given an edgelist, connect the objects in dwsim.
        For single-headed edgelists, pass in None
        """

        # HELPER
        def is_heat_exchanger(label):
            if label is None:
                return False
            return isinstance(
                self.atlas.get_equipment(by_label=label), at.HeatExchanger
            )

        def is_battery_limit(label):
            if label is None:
                return True
            return isinstance(
                self.atlas.get_equipment(by_label=label), (at.BatteryIn, at.BatteryOut)
            )

        def is_hex_to_battery(upstream_label, downstream_label) -> bool:
            """Checks if theres a heat exchanger and batterylimit"""
            return is_heat_exchanger(upstream_label) and is_battery_limit(
                downstream_label
            )

        def is_battery_to_hex(upstream_label, downstream_label) -> bool:
            """Checks if theres a heat exchanger and batterylimit"""
            return is_battery_limit(upstream_label) and is_heat_exchanger(
                downstream_label
            )

        def is_hex_to_notbattery(upstream_label, downstream_label) -> bool:
            """Checks if theres a heat exchanger and NOT batterylimit"""
            return is_heat_exchanger(upstream_label) and not is_battery_limit(
                downstream_label
            )

        def is_notbattery_to_hex(upstream_label, downstream_label) -> bool:
            """Checks if theres a heat exchanger and NOT batterylimit"""
            return is_heat_exchanger(downstream_label) and not is_battery_limit(
                upstream_label
            )

        def get_e_interface(label):
            if label is None:
                return
            return self._get_from_interface_collection(label)

        def get_s_interface(label):
            if label is None:
                return
            return self._get_from_interface_collection(label)

        # LOGIC
        try:
            # Get interface objects
            interfaces = {
                "upstream": get_e_interface(upstream_label),
                "downstream": get_e_interface(downstream_label),
                "stream": get_s_interface(stream_label),
            }

            # Get graphic object, filterout null interface (i.e. BatteryIn/BatteryOut)
            graphics = {
                k: v.graphic_obj
                for k, v in interfaces.items()
                if v != None and not isinstance(v, DWSimNullInterface)
            }

            # HEX
            # BATTERY IS PORT 0
            if is_battery_to_hex(upstream_label, downstream_label) == True:
                # Connect stream to downstream hex
                interfaces["downstream"].unit_obj.ConnectFeedMaterialStream(
                    interfaces["stream"].unit_obj, 1
                )
            elif is_hex_to_battery(upstream_label, downstream_label) == True:
                # Connect upstream hex to stream
                interfaces["upstream"].unit_obj.ConnectProductMaterialStream(
                    interfaces["stream"].unit_obj, 1
                )
            # NOT BATTERY IS PORT 1
            elif is_notbattery_to_hex(upstream_label, downstream_label) == True:
                # Connect NotBattery to Stream
                self.dwsim_simulator.ConnectObjects(
                    interfaces["upstream"].graphic_obj,
                    interfaces["stream"].graphic_obj,
                    -1,
                    -1,
                )
                # Connect stream to hex
                interfaces["downstream"].unit_obj.ConnectFeedMaterialStream(
                    interfaces["stream"].unit_obj, 0
                )
            elif is_hex_to_notbattery(upstream_label, downstream_label) == True:
                # Connect upstream hex to stream
                interfaces["upstream"].unit_obj.ConnectProductMaterialStream(
                    interfaces["stream"].unit_obj, 0
                )
                # Connect stream to eqp
                self.dwsim_simulator.ConnectObjects(
                    interfaces["stream"].graphic_obj,
                    interfaces["downstream"].graphic_obj,
                    -1,
                    -1,
                )

            # BATTERY LIMIT CONDITIONS
            elif is_battery_limit(upstream_label) == True:
                # Connect stream do downstream
                self.dwsim_simulator.ConnectObjects(
                    interfaces["stream"].graphic_obj,
                    interfaces["downstream"].graphic_obj,
                    -1,
                    -1,
                )
            elif is_battery_limit(downstream_label) == True:
                # connect upstream to stream
                self.dwsim_simulator.ConnectObjects(
                    interfaces["upstream"].graphic_obj,
                    interfaces["stream"].graphic_obj,
                    -1,
                    -1,
                )
            else:
                # Connect upstream to stream
                self.dwsim_simulator.ConnectObjects(
                    interfaces["upstream"].graphic_obj,
                    interfaces["stream"].graphic_obj,
                    -1,
                    -1,
                )
                # Connect stream to downstream
                self.dwsim_simulator.ConnectObjects(
                    interfaces["stream"].graphic_obj,
                    interfaces["downstream"].graphic_obj,
                    -1,
                    -1,
                )

        except Exception as e:
            # [ ]- implement function to "check_connection"
            logging.info(
                f"Connection skipped: {upstream_label}->{stream_label}->{downstream_label}. Assumed that connection exists.: {e}"
            )

    ####################

    # INTERFACE
    # TODO REFACTOR THIS TO USE BASE COLLECTION OBJ
    def get_interface(self, entity: at.ENTBase):
        """
        Returns an interface from collection.
        if interface does not exist, creates one and adds it to interface colection
        """
        entity_id = entity.label

        if entity_id in self._interface_collection:
            return self._get_from_interface_collection(entity_id)

        else:
            interface = self._create_entity_interface(entity)
            self._add_interface(interface)
            return interface

    def _get_from_interface_collection(
        self, object_id: str
    ) -> Optional[Union[DWSimEquipmentInterface, DWSimStreamInterface]]:
        """
        Retrieves the equipment interface object for the given object ID from the equipment interface collection.
        """
        equipment_interace = self._interface_collection.get(object_id, None)
        return equipment_interace

    def _add_interface(
        self,
        interface_obj: Union[DWSimStreamInterface, DWSimEquipmentInterface],
    ):
        """adds an interface to the collection"""
        self._interface_collection[interface_obj.entity_id] = interface_obj

    def _remove_entity(self, object_id: str):
        interface = self._get_from_interface_collection(object_id)
        interface.remove_from_endpoint()

        self._interface_collection.pop(object_id, None)

    #####################33
    # Compound Stuff

    def get_vocompound_by_endpoint_label(self, endpoint_label: str) -> at.VOCompound:
        """Given an endpoint compound label, check the config_manager and return the associated VOCompound"""
        compound_lookup = {v: k for k, v in self.config.compound_mapper.items()}
        compound_id = compound_lookup[endpoint_label]
        compound = self.atlas.compounds_collection.get_item_from_compound_id(
            compound_id
        )

        return compound

        # return self.atlas.z_get_compound(compound_id)

    def get_compound_endpoint_label(self, compoundvo: at.VOCompound) -> str:
        """Given a compound vo, retreive the endpoint label from config manager"""
        return self.config.compound_mapper[compoundvo.id]
