from __future__ import annotations
from ._imports import *

# Atlas
if TYPE_CHECKING:
    import backend.core._atlas.aggregates as at


####################


@runtime_checkable
class MatrixProtocol(Protocol):
    def setup_simulation(self) -> bool: ...
    def attach_model(self, domain_model: at.AtlasRoot, share_model_state: bool): ...
    def run_simulation(self) -> at.AtlasRoot: ...


class BaseMatrixSimulator(ABC):
    """Base class for all matrix simulation engines"""
    
    @classmethod
    @abstractmethod
    def from_config(cls, simulation_label: str, config: Optional[Dict[str, Any]] = None) -> 'BaseMatrixSimulator':
        """Factory method to create an instance from a generic config dictionary"""
        pass
        
    def __init__(self, simulation_label: str):
        self._simulation_label = simulation_label
        self._atlas: Optional[at.AtlasRoot] = None
        
    @property
    def label(self) -> str:
        return self._simulation_label

        
    @abstractmethod
    def setup_matrix(self) -> bool:
        """Initialize the simulation engine"""
        pass

    @abstractmethod
    def attach_model(self, domain_model: at.AtlasRoot, share_model_state: bool = True):
        """Connect a domain model to the simulator"""
        pass

    @abstractmethod
    def run_matrix(self) -> at.AtlasRoot:
        """Run the simulation and return updated model"""
        pass
