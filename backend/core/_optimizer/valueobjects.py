from __future__ import annotations
from ._imports import *
from ._enums import *

################

# VO

class VOPydanticBase(BaseModel):
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        validate_assignment=True,
        extra='forbid',
        frozen=True,
    )

class VOOptimizationConfig(VOPydanticBase):
    """Configuration parameters for optimization process"""
    max_trials: int = Field(
        default=20,
        description="Maximum number of trials to run during optimization"
    )
    timeout_seconds: int = Field(
        default=3600,
        description="Maximum time in seconds for the optimization process to run"
    )
    strategy: EnumSearchAlgorithm = Field(
        default=EnumSearchAlgorithm.DIRECT,
        description="Search algorithm to use for optimization"
    )
    max_concurrent_trials: int = Field(
        default=4,
        description="Maximum number of trials to run in parallel"
    )
    resources_per_trial: Dict[str, float] = Field(
        default_factory=lambda: {"cpu": 1.0},
        description="Computational resources allocated per trial, e.g. {'cpu': 1.0, 'gpu': 0.5}"
    )
    pruning: bool = Field(
        default=True,
        description="Whether to enable pruning for trials showing poor performance"
    )
    pruning_grace_iterations: int = Field(
        default=5,
        description="Minimum number of iterations to run before pruning is triggered"
    )
    reduction_factor: int = Field(
        default=3,
        description="Factor by which to reduce resource allocation in successive halving schedulers"
    )
    random_seed: int = Field(
        default=42,
        description="Random seed for reproducibility"
    )
    
    @model_validator(mode='after')
    def validate_optimization_config(self) -> 'VOOptimizationConfig':
        """Validate optimization configuration parameters"""
        error_log : List[str]= []
        
        # Validate compute constraints
        if self.max_concurrent_trials <= 0:
            error_log.append(f"max_concurrent_trials must be positive, got {self.max_concurrent_trials}")

        if not self.resources_per_trial:
            error_log.append("resources_per_trial cannot be empty")

        for resource, amount in self.resources_per_trial.items():
            if amount <= 0:
                error_log.append(f"Resource allocation for {resource} must be positive, got {amount}")
        
        # Validate run constraints
        if self.max_trials <= 0:
            error_log.append(f"max_trials must be positive, got {self.max_trials}")
        
        if self.timeout_seconds <= 0:
            error_log.append(f"timeout_seconds must be positive, got {self.timeout_seconds}")
        
        if self.pruning_grace_iterations <= 0:
            error_log.append(f"grace_period_iterations must be positive, got {self.pruning_grace_iterations}")
        
        if self.reduction_factor <= 1:
            error_log.append(f"reduction_factor must be greater than 1, got {self.reduction_factor}")
        
        # Raise consolidated exception if errors were found
        if error_log:
            raise ValueError(f"Optimization configuration has multiple validation errors: {'; '.join(error_log)}")
        
        return self
    
    @classmethod
    def create_for_local(cls) -> 'VOOptimizationConfig':
        """Create config optimized for local development"""
        return cls(
            max_trials=10,
            max_concurrent_trials=1,
            timeout_seconds=1800,
            resources_per_trial={"cpu": 1.0}
        )
        
    @classmethod
    def create_for_production(cls) -> 'VOOptimizationConfig':
        """Create config optimized for production use"""
        return cls(
            max_trials=50,
            max_concurrent_trials=8,
            resources_per_trial={"cpu": 2.0}
        )

class VOOptParam(VOPydanticBase):
    """Reusable parameter definition across training and optimization"""
    label: str = Field(
            ...,
        description="Unique identifier for the parameter. If in doubt, use variable uid"
    )
    type_: EnumParameterSpaceType = Field(
        default=EnumParameterSpaceType.FLOAT_UNIFORM,
        description="Parameter type determining behavior and constraints"
    )
    min_max_inclusive: Optional[Tuple[Union[int, float], Union[int, float]]] = Field(
        default=None, 
        description="Inclusive min and max bounds for numeric parameters. Required for numeric types."
    )
    options: Optional[List[Any]] = Field(
        default=None,
        description="Available options for categorical parameters. Required for categorical types."
    )
    
    @model_validator(mode='after')
    def validate_parameter_consistency(self) -> 'VOOptParam':
        """Ensure parameter configuration is consistent with its type"""
        error_log : List[str]= []
        param_name = self.label
        
        # Numeric types validation
        if self.type_ in [EnumParameterSpaceType.FLOAT_UNIFORM, EnumParameterSpaceType.FLOAT_LOG_UNIFORM, 
                       EnumParameterSpaceType.FLOAT_NORMAL, EnumParameterSpaceType.INT_UNIFORM]:
            
            # Numeric types require min_max_inclusive
            if self.min_max_inclusive is None:
                error_log.append(f"Numeric parameter '{param_name}' of type '{self.type_}' requires min_max_inclusive")
            
            # Numeric types should not have options
            if self.options is not None and len(self.options) > 0:
                error_log.append(f"Numeric parameter '{param_name}' of type '{self.type_}' should not have options")
            
            # Validate min_max values if they exist
            if self.min_max_inclusive is not None:
                min_val, max_val = self.min_max_inclusive
                
                if min_val is None or max_val is None:
                    error_log.append(f"Parameter '{param_name}' min_max_inclusive cannot contain None values for numeric types")
                elif min_val > max_val:  # Type:ignore
                    error_log.append(f"Parameter '{param_name}' min value cannot be more than max value")
                
                # Log-uniform specific validation
                if self.type_ == EnumParameterSpaceType.FLOAT_LOG_UNIFORM and min_val <= 0:
                    error_log.append(f"Parameter '{param_name}' with LOG_UNIFORM distribution must have min_value > 0")
        
        # Categorical types validation
        if self.type_ in [EnumParameterSpaceType.CATEGORIAL, EnumParameterSpaceType.BOOL]:
            
            # Categorical types require options
            if not self.options:
                error_log.append(f"Categorical parameter '{param_name}' of type '{self.type_}' requires non-empty options")
            
            # Categorical types should not have min_max_inclusive
            if self.min_max_inclusive is not None:
                error_log.append(f"Categorical parameter '{param_name}' of type '{self.type_}' should not have min_max_inclusive")
        
        # Raise consolidated exception if errors were found
        if error_log:
            raise ValueError(f"Parameter '{param_name}' has multiple validation errors: {'; '.join(error_log)}")
        
        return self

    def get_min_max(self) -> Tuple[Union[int, float], Union[int, float]]:
        if self.min_max_inclusive is None:
            raise ValueError(f"Parameter {self.label} has no min_max_inclusive")
        return self.min_max_inclusive

    def get_options(self) -> List:
        if self.options is None:
            raise ValueError(f"Parameter {self.label} has no min_max_inclusive")
        return self.options



    

class VOOptMetric(VOPydanticBase):
    """Definition of a metric to optimize"""
    label: EnumMetrics = Field(
        description="Name of the metric, either a string or predefined metric enum"
    )
    direction: EnumDirection = Field(
        default=EnumDirection.MINIMIZE,
        description="Optimization direction - whether to minimize or maximize the metric"
    )
    target_metric: Optional[EnumMetrics] = Field(
        default=None,
        description="Optional target metric for comparison, if applicable"
    )
    target_bounds: Optional[Tuple[Optional[float], Optional[float], Optional[float]]] = Field(
        default=None,
        description="Optional bounds for the metric as (lower_bound, ideal, upper_bound) tuple"
    )
    
    @model_validator(mode='after')
    def validate_metric_constraints(self) -> 'VOOptMetric':
        """Validate that target_value and target_bounds are properly formed if provided"""
        error_log : List[str]= []
        
        # Validate that if target bounds is specified, target_metric is also specified
        is_target_bounds = self.target_bounds is not None
        is_target_metric = self.target_metric is not None 
        
        if is_target_bounds != is_target_metric:
            error_log.append("target_bounds requires target_metric to be specified")
        
        # Validate that target bounds are properly formed
        if self.target_bounds is not None:
            lower, ideal, upper = self.target_bounds
            
            # Validate they are floats
            if not all(isinstance(x, float) for x in [lower, ideal, upper]):
                error_log.append("target_bounds must be a tuple of floats")
            else:
                # Validate lower is below ideal
                if lower > ideal:  # type: ignore
                    error_log.append("target_bounds lower bound must be less than ideal")
                
                # Validate ideal is below upper
                if ideal > upper:  # type: ignore
                    error_log.append("target_bounds ideal must be less than upper bound")
        
        # Raise consolidated exception if errors were found
        if error_log:
            raise ValueError(f"Metric '{self.label}' has multiple validation errors: {'; '.join(error_log)}")
        
        return self
    
    def is_maximize(self) -> bool:
        return self.direction == EnumDirection.MAXIMIZE
    
    


# Evaluation
class VOSamples(VOPydanticBase):
    """Base class for evaluation contexts using pandas DataFrames"""
    x_samples: pd.DataFrame = Field(
        description="Input features DataFrame with rows representing samples"
    )
    y_samples: pd.DataFrame = Field(
        description="Target values DataFrame with rows matching x_samples"
    )
    
    @model_validator(mode='after')
    def validate_dimensions(self) -> 'VOSamples':
        """Validate that x_samples and y_samples have the same number of rows"""
        error_log : List[str]= []
        
        if len(self.x_samples) != len(self.y_samples):
            error_log.append(f"x_samples and y_samples must have the same number of rows. "
                           f"Got {len(self.x_samples)} and {len(self.y_samples)} respectively.")
        
        if error_log:
            raise ValueError(f"Sample validation errors: {'; '.join(error_log)}")
            
        return self
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with pandas dataframes as records"""
        return {
            "x_samples": self.x_samples.to_dict(orient='records'),
            "y_samples": self.y_samples.to_dict(orient='records')
        }
    
    def to_json(self) -> str:
        """Serialize to JSON string"""
        return json.dumps(self.to_dict())
    
    @classmethod
    def from_dict(cls, obj: Dict[str, Any]) -> 'VOSamples':
        """Create instance from dictionary data"""
        if isinstance(obj.get('x_samples'), list):
            obj['x_samples'] = pd.DataFrame.from_records(obj['x_samples'])
        if isinstance(obj.get('y_samples'), list):
            obj['y_samples'] = pd.DataFrame.from_records(obj['y_samples'])
        return cls(x_samples=obj['x_samples'], y_samples=obj['y_samples'])
    
    @classmethod
    def from_json(cls, json_str: str) -> 'VOSamples':
        """Create instance from JSON string"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    # Overriding standard Pydantic methods to maintain consistent API
    def model_dump(self, **kwargs) -> Dict[str, Any]: #type:ignore
        """Override model_dump to use our custom to_dict method"""
        return self.to_dict()
    
    def model_dump_json(self, **kwargs) -> str: #type:ignore
        """Override model_dump_json to use our custom to_json method"""
        return self.to_json()
    
    @classmethod
    def model_validate(cls, obj: Dict[str, Any], **kwargs): #type:ignore
        """Override model_validate to use our custom from_dict method"""
        if isinstance(obj, dict):
            return cls.from_dict(obj)
        return super().model_validate(obj, **kwargs)
    
    @classmethod
    def model_validate_json(cls, json_data: str, **kwargs): #type:ignore
        """Override model_validate_json to use our custom from_json method"""
        return cls.from_json(json_data)
    
    def get_param_labels(self) -> Set[str]:
        """Get the set of parameter labels from x_samples"""
        return set(self.x_samples.columns)
    
    def get_metric_labels(self) -> Set[str]:
        """Get the set of metric labels from y_samples"""
        return set(self.y_samples.columns)


class VOOptimizationProblem(VOPydanticBase):
    """Definition of the optimization problem"""
    parameters: List[VOOptParam] = Field(
        description="List of parameters to be optimized"
    )
    metrics: List[VOOptMetric] = Field(
        description="List of metrics to evaluate optimization results"
    )
    primary_metric: EnumMetrics = Field(
        description="Name of the primary metric to optimize"
    )
    
    
    @field_validator('parameters')
    def validate_param_names(cls, params_list: List[VOOptParam]) -> List[VOOptParam]:
        """Ensure parameter names are unique"""
        names = [p.label for p in params_list]
        if len(names) != len(set(names)):
            duplicates = [str(name) for name in names if names.count(name) > 1]
            raise ValueError(f"Duplicate parameter names found: {', '.join(set(duplicates))}")
        return params_list
    
    @field_validator('metrics')
    def validate_metric_names(cls, metrics_list: List[VOOptMetric]) -> List[VOOptMetric]:
        """Ensure metric names are unique"""
        names = [str(m.label) for m in metrics_list]
        if len(names) != len(set(names)):
            duplicates = [name for name in names if names.count(name) > 1]
            raise ValueError(f"Duplicate metric names found: {', '.join(set(duplicates))}")
        return metrics_list
    
    @model_validator(mode='after')
    def validate_problem_specification(self) -> 'VOOptimizationProblem':
        """Validate the complete problem specification"""
        error_log = []
        
        # Validate primary metric exists in metrics list
        metric_names = [m.label for m in self.metrics]
        if self.primary_metric not in metric_names:
            error_log.append(f"Primary metric '{self.primary_metric}' not found in metrics: {metric_names}")
        
        # Raise consolidated exception if errors were found
        if error_log:
            raise ValueError(f"Optimization problem has multiple validation errors: {'; '.join(error_log)}")
        
        return self
    
    def get_param_labels(self) -> List[str]:
        """Get list of all parameter names as normalized strings"""
        return [p.label for p in self.parameters]
    
    def get_param(self, name: str) -> VOOptParam:
        """
        Get parameter by name
        """
        for param in self.parameters:
            if name == param.label:
                return param
        raise ValueError(f"Parameter '{name}' not found in parameter space")
    
    def get_metric(self, name: EnumMetrics) -> VOOptMetric:
        """Get metric by name"""
        for metric in self.metrics:
            if name == metric.label:
                return metric
        raise ValueError(f"Metric '{name}' not found in metrics list")

    def get_primary_metric(self):
        return self.get_metric(self.primary_metric)
    
    def get_metric_labels(self) -> List[EnumMetrics]:
        """Get all metric labels"""
        return [m.label for m in self.metrics]

