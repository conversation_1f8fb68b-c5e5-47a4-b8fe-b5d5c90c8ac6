from __future__ import annotations
from enum import Enum
from typing import Protocol, Union, Any


# ENUMS

class EnumTrialStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    PRUNED = "pruned"

class EnumDirection(str, Enum):
    MINIMIZE = "min"
    MAXIMIZE = "max"

class EnumSearchAlgorithm(str, Enum):
    BAYESIAN = "bayesian"
    DIRECT = "direct"
    # PS = "particle_swarm"
    RANDOM = "random"

    ### FUTURE IMPLEMENTATIONS
    # SA = "simulated_annealing"

    ### W SCHEDULERS
    # HYPERBAND = "hyperband"
    # ASHA = "asha"
    # BOHB = "bohb"

    ### DISTRIBUTED 
    # PBT = "population_based_training"

class EnumMetrics(str, Enum):
    # Classification metrics
    # CAT_ACCURACY = "accuracy"  # Classification accuracy
    # CAT_AUC = "auc"  # Area under the ROC curve
    # CAT_F1_SCORE = "f1_score"  # F1 score for classification
    # CAT_PRECISION = "precision"  # Precision score
    # CAT_RECALL = "recall"  # Recall score
    
    # Regression metrics
    REG_MSE = "mse"  # Mean squared error for regression
    REG_MAE = "mae"  # Mean absolute error for regression
    REG_RMSE = "rmse"  # Root mean squared error for regression
    REG_R2_SCORE = "r2_score"  # R-squared score for regression
    # REG_EXPLAINED_VARIANCE = "explained_variance"  # Explained variance
    # REG_MEDIAN_AE = "median_ae"  # Median absolute error
    # REG_MAX_ERROR = "max_error"  # Maximum residual error
    # REG_MAPE = "mape"  # Mean Absolute Percentage Error
    
    # Time series specific metrics
    TS_MAPE = "ts_mape"  # Time Series Mean Absolute Percentage Error
    TS_SMAPE = "ts_smape"  # Time Series Symmetric Mean Absolute Percentage Error
    TS_MASE = "ts_mase"  # Time Series Mean Absolute Scaled Error
    TS_RMSSE = "ts_rmsse"  # Time Series Root Mean Squared Scaled Error
    # TS_FORECAST_BIAS = "ts_forecast_bias"  # Time Series Forecast Bias
    # TS_AUTOCORRELATION = "ts_autocorrelation"  # Time Series Autocorrelation of residuals
    # TS_DTWD = "ts_dtwd"  # Time Series Dynamic Time Warping Distance
    # TS_COVERAGE = "ts_coverage"  # Time Series Prediction Interval Coverage
    # TS_PINBALL = "ts_pinball"  # Time Series Pinball Loss (for quantile forecasts)
    # TS_CRPS = "ts_crps"  # Time Series Continuous Ranked Probability Score

    @classmethod
    def get_enum_from_str(cls, enum_str: str) -> Union[str, EnumMetrics]:
        # Given a string, search through all enums in this namespace. If it can be found, return the enum. Else, return the string
        for member in cls: 
            if member.value == enum_str:
                return member
        return enum_str

class EnumParameterSpaceType(str, Enum):
    FLOAT_UNIFORM = "float_uniform"  
    FLOAT_LOG_UNIFORM = "float_log_uniform"  
    FLOAT_NORMAL = "float_normal"  
    INT_UNIFORM = "int_uniform"  
    CATEGORIAL = "categorical"
    BOOL = "boolean"
