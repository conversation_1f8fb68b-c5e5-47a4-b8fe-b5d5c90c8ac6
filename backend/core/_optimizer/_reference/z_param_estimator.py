import os # python library for path name manipulations
import pathlib
import sys # helps with checking for parameters specific to the system
import numpy as np # python library to execute matrix operations
import pandas as pd #library used for manipulating tabular datasets within memory itself
import json # library for dealing with json files
from scipy.optimize import direct, Bounds # direct optimization and Bounds classes imported from scipy.optimize
cur_path = pathlib.Path(__file__)
sys.path.append(cur_path.resolve().parent.parent)
import model
from run_model_ext import run_model
sys.path.append(cur_path.resolve().parent.parent/"surrogateModel")
from surrogateModel.surrogate_model_old import model_build
from surrogateModel.surrogate_model_deeplearning import surrogate_model_final
import contextlib


class ParamEstimator:
    """
    This class provides a framework for parameter optimization, utilizing a set of
    gPROMS models that represent various experimental setups or alternative system configurations to evaluate the parameters.
    Instance Attribute:
    -------------------
        1) models: list of gpromsModel Class Objects from model.py
        2) inputs_range_dict: Dict of input_names mapped to their boundaries in a tuple format
        3) outputs_wt_dict: Dict of Output names mapped to their weightage to the loss function
        4) experimental_data: dataframe of the experimental data used for Parameter Estimation
        5) cache: a pandas dataframe that is continously being reffered to reduce recompuations in case they have already been carried out.
    Methods
    -------
        1) __init__: Constructor of the parameter estimator class.
        2) __convert_experimental_data_to_data_frame: Helper function to convery json experimental data to a pd.DataFrame object.
        3) __run_multiple_simulations :Helper function to run multiple simulations across multiple models having same inputs and output types.
        4) __match_properties: The function takes each property mentioned in the experimental 
                                data json file and returns a normalized loss for each of the property.
        5) __ParameterEstimationCostFunction: This parameter estimation cost function matches the output variables
        6) ParamEstimation_DIRECT: Main method of the class to estimate the parameters of the Model using DIRECT Algorithm. 
        7) ParamEstimaton_naive_MM: Parameter Estimation Method written for when dimensionality needs to be reduced when using Naive Model Maintainence.
        8) get_output_names: Getter written for getting list of names of model outputs.
        9) get_input_names: Getter written for getting list of names of model inputs.
    """
    
    def __init__(self, models, inputs_range_dict,output_wt_dict, experimneral_data_json,cache=None): #Constructor of the class.
            """
             Constructor of the class object.
            
            Parameters
            ----------
                1) models: list
                    list of gpromsModel Class Objects
                2) inputs_range_dict: dict
                    Dict of input_names mapped to their boundaries in a tuple format. 
                    Example: {"Var1": (Lower_bound, Upper_bound), "Var2": (Lower_bound, Upper_bound), ....}
                3) output_range_dict: dict
                    Dict of Output names mapped to their weightage to the loss function.
                    Example: {"Output1": 100, "Output2": 1, "Output3": 0, "Output4": 10E5, ..... }
                4) experimental_data_json: path of the experimental data json file.
                    Format of the Experimental Data JSON file
                    {"Experiment1": 
                                { "simulation_time": total_sim_time ,
                                  "experimental_data": {
                                                        "Property1": { 
                                                                    "Property1": [ array of readings],
                                                                    "Time": [array of time when readings were recorded]
                                                                    },
                                                        "Property2": { 
                                                                    "Property2": [ array of readings],
                                                                    "Time": [array of time when readings were recorded]
                                                                    },
                                                        .....        
                                                        }

                                },
                    "Experiment2": 
                                { "simulation_time": total_sim_time ,
                                  "experimental_data": {
                                                        "Property1": { 
                                                                    "Property1": [ array of readings],
                                                                    "Time": [array of time when readings were recorded]
                                                                    },
                                                        "Property2": { 
                                                                    "Property2": [ array of readings],
                                                                    "Time": [array of time when readings were recorded]
                                                                    },
                                                        .....        
                                                        }

                                },
                    ......
                    }
                6) cache: path to a csv file that is being used as a cache
            
            """
            self.models = models #list of gPROMS models
            self.inputs_range_dict = inputs_range_dict # list of ranges of parameter values
            self.outputs_wt_dict = output_wt_dict # dictionary mapping output variables to their weightage in loss function evalutation
            self.experimental_data = self.convert_experimental_data_to_data_frame(experimneral_data_json) 

            # caching mechanism
            if cache is None:
                self.cache = None
            elif os.path.isfile(cache):
                self.csv_cache = cache
                self.cache = pd.read_csv(cache).drop(columns=["Unnamed: 0"])
            else:
                self.csv_cache = cache
                self.cache = pd.DataFrame([], columns=self.models[0].input_name_list + ["Function_Eval"])

    def convert_experimental_data_to_data_frame(self, experimental_data_json):
        """
        Helper function to convery json experimental data to a pd.DataFrame object.

        Parameters
        ----------
            experimental_data_json: file path of the experimental data json file.
        Returns
        -------
            data_frames_exp: dataframe representing experimental data
             {"Prperty": df_representing_data_of_that_property}
        """
        with open(experimental_data_json) as exp__: # open the json file.

            exp_dict = json.load(exp__) #load json to dictionary
        
        data_frames_exp = {} #empty dictonary

        # loop to convert "data_frames_exp" in the form of {"Property": DataFrame containing experiment data in a structured format}
        for i in exp_dict: # iterate through each experiment
            for j in exp_dict[i]["experimental_data"]: # iterate through each property
                if j not in data_frames_exp.keys():  # check if df for that property already exists
                    data_frames_exp[j] = pd.DataFrame(exp_dict[i]["experimental_data"][j])
                    data_frames_exp[j]["Experiment"] = i
                else: #concat the nw df to an exisiting df
                    dfs = pd.DataFrame(exp_dict[i]["experimental_data"][j])
                    dfs["Experiment"] = i
                    new_df = pd.concat([data_frames_exp[j], dfs])
                    data_frames_exp[j] = new_df
        
        return data_frames_exp


    def __run_multiple_simulations(self, param_input):

        """
        Helper function to run multiple simulations across multiple models having same inputs and output types.
        Parameters
        ----------
            **args: dict
                dictionary mapping parameter name to value.
                Ex: {"Param1": val1, "Param2": val2, "Param3": val3, ...}
        Returns
        -------
            final_Frame_simulation: combined dataframe of all model simulations
        """
        data_frames_simulation = {}
        
        for i in self.models:
            if isinstance(i, model.gpromsModel):
                # run the simulation
                with contextlib.redirect_stdout(open(os.devnull, 'w')): 
                    output_df, input_df = run_model(i, param_input)
                # combine input and output dfs
                final = pd.merge(left=input_df, right=output_df, how="inner", on=["Time"])
                final["Experiment"] = i.model_name
                # store simulation results for each experiment in a dictionary
                data_frames_simulation[i] = final.dropna() 
            elif isinstance(i, model_build.gPROMS_SurrogateModel):
                data_frames_simulation[i] = i.stratSurrogate_prediction(param_input)
                data_frames_simulation[i]["Experiment"] = i.orginal_model.model_name
            elif isinstance(i, surrogate_model_final.Surrogate_Model):
                data_frames_simulation[i] = i.prediction(param_input)
                data_frames_simulation[i]["Experiment"] = i.actual_model.model_name
        # concatenate all the simulation result dfs
        final_frame_simulation = pd.concat([data_frames_simulation[i] for i in data_frames_simulation])
        return final_frame_simulation

    def __match_properties(self, simulated_data_frame):
        """
        The function takes each property mentioned in the experimental data json file and returns a normalized loss for each of the property. Evaluates the loss
        function.
        Parameters
        ----------
            simulated_data_frame: pandas DataFrame
                dataframe containing simulation data
        Returns
        -------
            loss__ : loss function compute
        """
        properties = self.experimental_data.keys()
        loss_function_contribution = []
        # loop to check the loss function contribution of each property
        for property in properties:
            simulated_property = simulated_data_frame[['Experiment', 'Time', property]] # selecting property from simulated dataframe 
            data_match_frame = pd.merge(left=simulated_property, right=self.experimental_data[property], how="inner", on = ["Experiment","Time"]) # merging property dataframes( simulated and experimental)
            data_match_frame["MSE"] = ( (data_match_frame[f"{property}_x"]) - (data_match_frame[f"{property}_y"]) ) ** 2 # computing the mean square error of recorded measurements
            data_match_frame = data_match_frame.groupby(by = "Experiment").mean()["MSE"] # taking the mean error grouped by each experiment
            loss_function_contribution.append(self.outputs_wt_dict[property] * abs(data_match_frame.mean()) )# normalizing the final mean error

        loss__ = sum(loss_function_contribution) # returning final weighted MSE
        
        return loss__ 
    
    def __ParameterEstimationCostFunction(self,params):
        """
        This parameter estimation cost function matches the output variables.
        Parameters
        ----------
            **args: dict
                dictionary mapping parameter name to value.
                Ex: {"Param1": val1, "Param2": val2, "Param3": val3, ...}
        Returns
        -------
            total_mse: loss function compute
        """
        if self.cache is not None:
            try:
                filters = [
                    self.cache[self.models[0].input_name_list[i]].between(params[i] * 0.999, params[i] * 1.001)
                    for i in range(len(params))
                ]
                print(filters)
                result = self.cache[filters]
                fin = result["Function_Eval"].iloc[0] if not result.empty else None
                if fin is not None:
                    return fin
            except KeyError as e:
                print(f"Cache key error: {e}")
            except IndexError:
                print("Cache lookup returned no results.")

        try:
            final_frame_simulation = self.__run_multiple_simulations(params) #concantenation of simulation of different experiments
        except Exception as error:
            print(f"Simlation Failed! because of {error}")
            return float("inf")
       
        # Check if all experiments ran properly
        experiment_counts = final_frame_simulation["Experiment"].value_counts()

        # Check if any experiment has fewer than the required number of runs
        if (experiment_counts < 5).any():
            print("Too Short!")
            return float("inf")  # representation of a large negative value

        # evaluation of mse if no issues were found in the prior steps                
        total_mse = self.__match_properties(final_frame_simulation)
        
        if self.cache is not None:
            new_Data = pd.DataFrame([list(params) + [total_mse]], columns=list(self.cache.columns))
            self.cache = pd.concat([self.cache, new_Data])
            self.cache.to_csv(self.csv_cache)
        return total_mse
    
    def objective_function(self, params):
            objective = self.__ParameterEstimationCostFunction( params)
            print(f"Final: {objective}")
            return objective
     
    def ParamEstimation_DIRECT(self, len_tol=0.001, vol_tol=1e-6, locally_biased=False, max_iter=100):

        """
        Main method of the class to estimate the parameters of the Model using DIRECT Algorithm. (Create an new method for other Algorithmss.)
        Parameters
        ----------
            len_tol: float
                - If locally_biased=True, terminate the optimization once half of the normalized maximal side length of the 
                    hyperrectangle containing the lowest function value is smaller than len_tol. 
                - If locally_biased=False, terminate the optimization once half of the normalized diagonal 
                of the hyperrectangle containing the lowest function value is smaller than len_tol.
                Must lie between 0 and 1. Default value: 0.001
            vol_tol: float
                Terminate the optimization once the volume of the hyperrectangle containing the lowest function value is 
                smaller than vol_tol of the complete search space. Must lie between 0 and 1. Default is 1e-16.
            locally_biased: boolean
                If True (default), use the locally biased variant of the algorithm known as DIRECT_L. If False, use the original unbiased DIRECT algorithm. 
                For hard problems with many local minima, False is recommended.
            max_iter: int
                Maximum number of iterations.
        Details of parameters retrieved from: https://docs.scipy.org/doc/scipy/reference/generated/scipy.optimize.direct.html

        Returns
        -------
            Optimal Parameters, value, success info, status, additional_info
        """

        parameter_bounds_normalized = Bounds([self.inputs_range_dict[i][0] for i in self.models[0].input_name_list], 
                                             [self.inputs_range_dict[i][1] for i in self.models[0].input_name_list] )  

        
        result = direct(self.objective_function, parameter_bounds_normalized, len_tol=len_tol, vol_tol=vol_tol, locally_biased=locally_biased, maxiter=max_iter)

        final_res = {
            "Minima": result.x,
            "Optimal Value": result.fun,
            "Success": result.success,
            "status": result.status,
            "details": result.message
        }

        return final_res
    
    

    def ParamEstimaton_naive_MM_or_batch_plant(self, frozen_params, param_values, len_tol=0.001, vol_tol=1e-6, locally_biased=False, max_iter=100):
        """
        Parameter Estimation Method written for when dimensionality needs to be reduced when using Naive Model Maintainence.
        
        Parameters
        ----------
            frozen_params: list
                parameters to be frozen
            param_values: dict
                current parameter values
        
        Results
        -------
            final_res
        """
        input_name_list = self.get_input_names()
        def new_obj(unk):
            k = 0
            input = []

            input_name_list = self.get_input_names()
            for i in input_name_list:
                if i in frozen_params:
                    input.append(param_values[i])
                else:
                    input.append(unk[k])
                    k += 1
            return self.objective_function(input)
        # lower bound
        lower_bound = []
        upper_bound = []
        for i in input_name_list:
            if i not in frozen_params:
                lower_bound.append(self.inputs_range_dict[i][0])
                upper_bound.append(self.inputs_range_dict[i][1])
        
        parameter_bounds_normalized = Bounds(lower_bound, upper_bound)  

        result = direct(new_obj, parameter_bounds_normalized, len_tol=len_tol, vol_tol=vol_tol, locally_biased=locally_biased, maxiter=max_iter)

        final_res = {
            "Minima": result.x,
            "Optimal Value": result.fun,
            "Success": result.success,
            "status": result.status,
            "details": result.message
        }
        
        j = 0
        modified_optima = []
        for param_name in input_name_list:
            if param_name in frozen_params:
                modified_optima.append(param_values[param_name])
            else:
                modified_optima.append(final_res["Minima"][j])
                j += 1
        final_res["Minima"] = modified_optima

        return final_res
    
    def get_output_names(self):
        """
        Getter written for getting list of names of model outputs.
        """
        model_ = self.models[0]
        if isinstance(model_, model.gpromsModel ):
            output_name_list = model_.output_name_list
        elif isinstance(model_, surrogate_model_final.Surrogate_Model):
            output_name_list = model_.actual_model.output_name_list
        elif isinstance(model_, model_build.gPROMS_SurrogateModel):
            output_name_list = model_.original_model.output_name_list
        return output_name_list
    
    def get_input_names(self):
        """
        Getter written for getting list of names of model inputs.
        """
        model_ = self.models[0]
        if isinstance(model_, model.gpromsModel ):
            input_name_list = model_.input_name_list
        elif isinstance(model_, surrogate_model_final.Surrogate_Model):
            input_name_list = model_.actual_model.input_name_list
        return input_name_list
