from __future__ import annotations
import typing as T

from ._imports import *
from ._enums import *
from .objectivefuncs.objfunc_base import BaseObjectiveFunction
from .valueobjects import (
    VOOptimizationConfig, 
    VOOptimizationProblem,
    VOSamples,
    VOOptMetric,
    VOOptParam,
)

def generate_parameter_samples(
    params: List[VOOptParam], 
    random_seed: int = 42
) -> Dict[str, Any]:
    """
    Sample parameter values from a list of VOOptParam definitions.
    
    Args:
        params: List of parameter definitions to sample from
        random_seed: Random seed for reproducible sampling
        
    Returns:
        Dictionary mapping parameter labels to sampled values
    """
    random_generator = np.random.RandomState(seed=random_seed)
    parameters = {}
    
    for param in params:
        # Keep the original parameter label (string or enum)
        param_label = param.label
        param_type = param.type_
        
        if param_type == EnumParameterSpaceType.INT_UNIFORM:
            min_val, max_val = param.get_min_max()
            parameters[param_label] = random.randint(min_val, max_val) # type:ignore
            
        elif param_type == EnumParameterSpaceType.FLOAT_UNIFORM:
            min_val, max_val = param.get_min_max()
            parameters[param_label] =random.uniform(min_val, max_val)
            
        elif param_type == EnumParameterSpaceType.FLOAT_LOG_UNIFORM:
            min_val, max_val = param.get_min_max()
            parameters[param_label] = np.exp(random_generator.uniform(
                np.log(max(1e-10, min_val)),
                np.log(max_val)
            ))
            
        elif param_type == EnumParameterSpaceType.FLOAT_NORMAL:
            min_val, max_val = param.get_min_max()
            mean = (max_val + min_val) / 2
            std = (max_val - min_val) / 6  # ~99.7% of values within range
            parameters[param_label] = max(min_val, min(max_val, 
                random_generator.normal(mean, std)))
                
        elif param_type in [EnumParameterSpaceType.CATEGORIAL, EnumParameterSpaceType.BOOL]:
            if param.options:
                parameters[param_label] = random_generator.choice(param.options)
    
    return parameters
