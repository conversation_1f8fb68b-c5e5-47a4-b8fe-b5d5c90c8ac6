from __future__ import annotations
from ._imports import *
from ._enums import *

class ENTBase(BaseModel):
    """Base class for all entities"""
    uid: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the entity")

    model_config = ConfigDict(arbitrary_types_allowed=True)


class ENTMetadata(ENTBase):
    """Metadata for an optimization job"""
    label: str
    user_id: Optional[str] = None
    atlas_id: Optional[str] = None  
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    description: Optional[str] = None
    tags: Dict[str, str] = Field(default_factory=dict)
    
    @field_serializer('created_at')
    def serialize_datetime(self, dt: datetime) -> str:
        """Serialize datetime to ISO 8601 format with timezone"""
        return dt.isoformat()


class ENTTrial(ENTBase):
    """Record of a single optimization trial"""
    trial_label: str
    parameters: Dict[str, Any]   # type: ignore
    metrics: Dict[str, float]   
    status: EnumTrialStatus = EnumTrialStatus.COMPLETED
    iteration: int = 1  # Number of training iterations (for iterative methods)
    runtime_seconds: float = 0.0
    error_message: Optional[str] = None
    
    # Optional tracking of intermediate values - useful for learning curves
    intermediate_values: Dict[str, List[float]] = Field(default_factory=dict)
    

class ENTResult(ENTBase):
    """Complete results from an optimization run"""
    label: str
    best_parameters: Dict[str, Any] # type: ignore
    best_metrics: Dict[EnumMetrics, float] # type: ignore
    trials: List[ENTTrial] = Field(default_factory=list)
    trials_total: int = 0
    trials_completed: int = 0
    duration_seconds: float = 0.0

    def get_learning_curves(self) -> Dict[str, List[float]]:
        """Extract learning curves for primary metric across trials"""
        curves = {}
        for trial in self.trials:
            if trial.intermediate_values:
                for metric, values in trial.intermediate_values.items():
                    if metric not in curves:
                        curves[metric] = []
                    curves[metric].append(values)
        return curves
    
    def get_parameter_importance(self, *, filter_by_val: Tuple[Optional[float],Optional[float]]) -> Dict[str, float]:
        """Simple estimate of parameter importance based on variance"""
        # Skip if not enough completed trials
        if len(self.trials) < 5:
            return {}
            
        param_names = list(self.best_parameters.keys())
        importance = {}
        
        # Get primary metric name (first metric in best_metrics)
        if not self.best_metrics:
            return {param: 0.0 for param in param_names}
            
        primary_metric = next(iter(self.best_metrics))
        
        # Build parameter matrices for completed trials
        completed_trials = [t for t in self.trials if t.status == EnumTrialStatus.COMPLETED]
        
        for param in param_names:
            # Skip if parameter is not numeric
            if not all(isinstance(t.parameters.get(param), (int, float)) for t in completed_trials):
                importance[param] = 0.0
                continue
                
            # Get parameter values and corresponding metrics
            param_values = [t.parameters.get(param, 0) for t in completed_trials]
            metric_values = [t.metrics.get(primary_metric, 0) for t in completed_trials]
            
            # Calculate correlation between parameter and metric
            try:
                correlation = abs(np.corrcoef(param_values, metric_values)[0, 1])
                importance[param] = 0.0 if np.isnan(correlation) else correlation
            except:
                importance[param] = 0.0
                
        # Normalize to sum to 1.0
        total = sum(importance.values())
        if total > 0:
            for param in importance:
                importance[param] /= total

        # Return with Filter by Val
        # TODO

                
        return importance
