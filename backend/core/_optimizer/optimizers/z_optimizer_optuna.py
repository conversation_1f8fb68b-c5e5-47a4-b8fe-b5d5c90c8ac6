from __future__ import annotations

from backend.core._optimizer.valueobjects import VOOptimizationConfig

from .._imports import *
from .optimizer_base import BaseOptimizationRunner
from ..valueobjects import *
from ..entities import *

import optuna
import threading


class OptunaOptimizationRunner(BaseOptimizationRunner):
    """
    Optimization runner using Optuna framework for Bayesian optimization.
    Supports hyperparameter optimization and black-box function optimization.
    """
    def __init__(
        self, 
        random_seed: int = 42,
        fallback_pruner: Optional[optuna.pruners.BasePruner] = None,
        fallback_sampler: Optional[optuna.samplers.BaseSampler] = None,
        timeout_buffer_seconds: int = 5,
        trial_startup_timeout_seconds: int = 30,
        log_level: int = 20  # INFO
    ):
        """
        Initialize OptunaOptimizationRunner with framework-specific configurations.
        
        Args:
            random_seed: Seed for reproducibility across all random operations
            pruner: Custom Optuna pruner for early stopping trials (default: MedianPruner)
            sampler: Custom Optuna sampler for parameter suggestions (default: TPESampler)
            timeout_buffer_seconds: Buffer time to allow clean shutdown before hard timeout
            trial_startup_timeout_seconds: Maximum time to wait for first trial to start
            log_level: Logging level for Optuna (10=DEBUG, 20=INFO, 30=WARNING, etc.)
        """
        self._random_seed = random_seed
        
        # Optuna-specific configurations
        self.fallback_pruner = fallback_pruner or optuna.pruners.MedianPruner(n_startup_trials=5, n_warmup_steps=0)
        
        # Create an optimized TPE sampler with better exploration-exploitation balance
        self.fallback_sampler = fallback_sampler or optuna.samplers.TPESampler(
            seed=random_seed,
            n_startup_trials=10,  # More random exploration before TPE kicks in
            multivariate=True,    # Consider parameter dependencies
            consider_prior=True,  # Use priors for better initial estimates
            prior_weight=1.0,     # Balance prior with observations
            consider_magic_clip=True,  # Heuristic to avoid overfitting
            consider_endpoints=True,   # Include bounds in sampling
        )
        
        self.timeout_buffer_seconds = timeout_buffer_seconds
        self.trial_startup_timeout_seconds = trial_startup_timeout_seconds
        self.log_level = log_level
        
        # Set Optuna's logging level
        optuna.logging.set_verbosity(self.log_level)
        
        # Active studies tracking
        self._active_studies: Dict[str, Tuple[optuna.Study, threading.Event]] = {}
        
        # Set random seeds
        self._set_random_seed(random_seed)
    
    
    def _get_metric_prefix(self):
        """
        Prefix for metric names in Optuna trials.
        Hacky and quick solution to avoid name collisions.
        """
        return "metric_"

    
    def _run_optimization(
        self,
        metadata: ENTMetadata,
        search_config: VOOptimizationConfig,
        problem_spec: VOOptimizationProblem,
        objective_fn: objective_function_protocol
    ) -> ENTResult:
        """
        Runs Optuna-based optimization, mapping VOOptimizationConfig → Optuna primitives.
        """
        job_label = str(metadata.uid)
        stop_event = threading.Event()
        
        # 1) CHOOSE PRUNER
        if search_config.pruning:
            # Configure pruner for early stopping
            n_startup_trials = search_config.pruning_grace_iterations or max(5, search_config.max_trials // 10)
            pruner = optuna.pruners.MedianPruner(
                n_startup_trials=n_startup_trials,
                n_warmup_steps=0
            )
        else:
            pruner = optuna.pruners.NopPruner()
        
        # 2) CHOOSE SAMPLER
        if search_config.strategy == EnumSearchAlgorithm.BAYESIAN:
            sampler = optuna.samplers.TPESampler(
                seed=self._random_seed,
                n_startup_trials=min(10, max(1, search_config.max_trials // 2)),
                multivariate=True,
                consider_prior=True,
                prior_weight=1.0,
                consider_magic_clip=True,
                consider_endpoints=True,
            )
        elif search_config.strategy == EnumSearchAlgorithm.RANDOM:
            sampler = optuna.samplers.RandomSampler(seed=self._random_seed)
        else:
            raise ValueError(f"Unsupported strategy: {search_config.strategy}")
        
        # 3) CREATE STUDY
        study = self._create_study(
            problem_spec,
            metadata.label,
            pruner=pruner,
            sampler=sampler
        )
        self._active_studies[job_label] = (study, stop_event)
        
        # 4) WRAP OBJECTIVE WITH PRUNING HOOK
        optuna_objective = self._create_objective_fn_wrapper(
            problem_spec,
            objective_fn,
            study,
            enable_pruning=search_config.pruning
        )
        
        # 5) CALCULATE TIME & PARALLELISM
        effective_timeout = None
        if search_config.timeout_seconds:
            effective_timeout = max(0, search_config.timeout_seconds - self.timeout_buffer_seconds)
        n_jobs = max(1, search_config.max_concurrent_trials)
        start_time = time.time()

        try:
            study.optimize(
                optuna_objective,
                n_trials=search_config.max_trials,
                timeout=effective_timeout,
                n_jobs=n_jobs,
                catch=(Exception,),
                callbacks=[self._create_stop_callback(stop_event)],
                show_progress_bar=False
            )
        finally:
            self._active_studies.pop(job_label, None)
        
        # 6) COLLECT RESULTS
        trials = [self._create_trial(problem_spec, {"data": t}) for t in study.trials]
        best_params, best_metrics = self._get_best_parameters_and_metrics(study, problem_spec)
        
        duration_seconds = time.time() - start_time
        trials_total = len(study.trials)
        trials_completed = len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
        
        return ENTResult(
            label=job_label,
            best_parameters=best_params,
            best_metrics=best_metrics,
            trials_completed=trials_completed,
            trials_total=trials_total,
            trials=trials,
            duration_seconds=duration_seconds,
        )

    
    
    def stop(self, job_label: str) -> bool:
        """
        Stop an ongoing optimization job.
        """
        job_id_str = str(job_label)  # Ensure we're comparing strings
        if job_id_str in self._active_studies:
            study, stop_event = self._active_studies[job_id_str]
            stop_event.set()  # Signal the job to stop
            return True
        return False
    
    def _create_stop_callback(self, stop_event: threading.Event) -> Callable[[optuna.study.Study, optuna.trial.FrozenTrial], None]:
        """
        Create a callback to check for stop events during optimization.
        
        Args:
            stop_event: Event that signals when to stop
            
        Returns:
            Callback function for Optuna
        """
        def callback(study: optuna.study.Study, trial: optuna.trial.FrozenTrial) -> None:
            if stop_event.is_set():
                # Setting this attribute will cause the optimization to stop after the current trial
                study.stop()
        return callback
    
    def _get_param_map(self, parameters: List[VOOptParam]) -> Dict[str, Dict[str, Any]]:
        """
        Convert our parameter space to Optuna's parameter format.
        """
        param_map = {}
        
        for param in parameters:
            if param.type_ == EnumParameterSpaceType.FLOAT_UNIFORM:
                min_val, max_val = param.min_max_inclusive or (0.0, 1.0)
                param_map[param.label] = {
                    'method': 'suggest_float',
                    'args': {
                        'name': param.label,
                        'low': min_val,
                        'high': max_val,
                        'log': False
                    }
                }
            
            elif param.type_ == EnumParameterSpaceType.FLOAT_LOG_UNIFORM:
                min_val, max_val = param.min_max_inclusive or (1e-5, 1.0)
                param_map[param.label] = {
                    'method': 'suggest_float',
                    'args': {
                        'name': param.label,
                        'low': min_val,
                        'high': max_val,
                        'log': True
                    }
                }
            
            elif param.type_ == EnumParameterSpaceType.INT_UNIFORM:
                min_val, max_val = param.min_max_inclusive or (0, 10)
                param_map[param.label] = {
                    'method': 'suggest_int',
                    'args': {
                        'name': param.label,
                        'low': int(min_val),
                        'high': int(max_val)
                    }
                }
                
            elif param.type_ == EnumParameterSpaceType.CATEGORIAL:
                choices = param.options or ["option1", "option2"]
                param_map[param.label] = {
                    'method': 'suggest_categorical',
                    'args': {
                        'name': param.label,
                        'choices': choices
                    }
                }
                
            elif param.type_ == EnumParameterSpaceType.BOOL:
                param_map[param.label] = {
                    'method': 'suggest_categorical',
                    'args': {
                        'name': param.label,
                        'choices': [True, False]
                    }
                }
            
            else:
                raise ValueError(f"Unsupported parameter type: {param.type_}")
        
        return param_map
    
    def _create_study(
        self, 
        problem_spec: VOOptimizationProblem, 
        study_label: str,
        pruner: Optional[optuna.pruners.BasePruner] = None,
        sampler: Optional[optuna.samplers.BaseSampler] = None
    ) -> optuna.Study:
        """
        Create and configure an Optuna study.
        """
        # Determine direction based on primary metric
        primary_metric = problem_spec.primary_metric
        primary_metric_obj = next(
            (m for m in problem_spec.metrics if m.label == primary_metric), 
            None
        )
        
        if not primary_metric_obj:
            raise ValueError(f"Primary metric {primary_metric} not found in problem specification")
        
        # Convert our direction enum to Optuna direction
        direction = (
            optuna.study.StudyDirection.MINIMIZE 
            if primary_metric_obj.direction == EnumDirection.MINIMIZE 
            else optuna.study.StudyDirection.MAXIMIZE
        )
        
        # Use provided pruner/sampler or instance defaults
        pruner_to_use = pruner if pruner is not None else self.fallback_pruner
        sampler_to_use = sampler if sampler is not None else self.fallback_sampler
        
        # Create study with appropriate sampler and pruner
        study = optuna.create_study(
            study_name=study_label,
            direction=direction,
            sampler=sampler_to_use,
            pruner=pruner_to_use
        )
        
        return study
    
    def _create_objective_fn_wrapper(
        self, 
        problem_spec: VOOptimizationProblem, 
        objective_fn: objective_function_protocol,
        study: optuna.Study,
        enable_pruning: bool = False
    ) -> Callable[[optuna.trial.Trial], float]:
        """
        Create Optuna-compatible objective function wrapper.
        """
        # Get parameter mapping
        param_map = self._get_param_map(problem_spec.parameters)
        primary_metric = problem_spec.primary_metric.value if isinstance(problem_spec.primary_metric, EnumMetrics) else problem_spec.primary_metric
        
        def optuna_objective(trial: optuna.trial.Trial) -> float:
            """Optuna-compatible objective function wrapper."""
            start_time = time.time()
            
            # GENERATE PARAMETER VALUES FOR THIS TRIAL
            param_values = {}
            for param_name, param_info in param_map.items():
                method = getattr(trial, param_info['method'])
                param_values[param_name] = method(**param_info['args'])
            
            try:
                # Call the original objective function
                metrics_dict = objective_fn(param_values, problem_spec)
                
                # EXTRACT THE PRIMARY METRIC VALUE
                if primary_metric not in metrics_dict:
                    raise ValueError(f"Primary metric '{primary_metric}' not found in objective function results")
                
                primary_value = metrics_dict[primary_metric]
                
                # RECORD ALL METRICS AS USER ATTRIBUTES FOR LATER RETRIEVAL
                for metric_name, metric_value in metrics_dict.items():
                    if metric_name != primary_metric:
                        metric_str = metric_name.value if isinstance(metric_name, EnumMetrics) else metric_name
                        metric_prefixed = self._get_metric_prefix() + metric_str
                        trial.set_user_attr(metric_prefixed, metric_value)
                
                # REPORT INTERMEDIATE VALUE FOR PRUNING
                if enable_pruning:
                    # For mathematical test functions or other metrics, report the value
                    trial.report(primary_value, step=1)
                    
                    # Check if this trial should be pruned
                    if trial.should_prune():
                        # When pruned, we raise TrialPruned exception to notify Optuna
                        raise optuna.exceptions.TrialPruned()
                
                end_time = time.time()
                trial.set_user_attr("runtime_seconds", end_time - start_time)
                
                # Store intermediate values for analysis
                if enable_pruning:
                    trial.set_user_attr("intermediate_values", {primary_metric: [primary_value]})
                
                return primary_value
                
            except optuna.exceptions.TrialPruned:
                raise
                
            except Exception as e:
                # Record the error and re-raise
                error_msg = str(e)
                trial.set_user_attr("error_message", error_msg)
                raise e
                
        return optuna_objective
    
    def _create_trial(
        self, 
        problem_spec: VOOptimizationProblem,
        context: Dict[str, Any],
    ) -> ENTTrial:
        """
        Convert an Optuna trial to our ENTTrial format.
        """

        # Map Optuna trial state to our status enum
        status_map = {
            optuna.trial.TrialState.COMPLETE: EnumTrialStatus.COMPLETED,
            optuna.trial.TrialState.PRUNED: EnumTrialStatus.PRUNED,
            optuna.trial.TrialState.FAIL: EnumTrialStatus.FAILED,
            optuna.trial.TrialState.RUNNING: EnumTrialStatus.IN_PROGRESS,
            optuna.trial.TrialState.WAITING: EnumTrialStatus.PENDING,
        }
        optuna_trial = context["data"]
        status = status_map.get(optuna_trial.state, EnumTrialStatus.PENDING)
        
        # Extract parameters
        parameters = optuna_trial.params 
        parameters = self._cast_dictkey2enum_for_params(parameters)
        
        # Extract metrics
        metrics = {}
        # Add primary metric
        if optuna_trial.value is not None:
            metrics[problem_spec.primary_metric] = optuna_trial.value
        
        # Add other metrics from user attributes
        for attr_name, attr_value in optuna_trial.user_attrs.items():
            if attr_name.startswith(self._get_metric_prefix()):
                metric_name = attr_name.replace(self._get_metric_prefix(), "")
                metrics[metric_name] = attr_value
        
        # Extract error message if any
        error_message = optuna_trial.user_attrs.get("error_message")
        
        # Get runtime if available
        runtime_seconds = optuna_trial.user_attrs.get("runtime_seconds", 0.0)
        
        # Extract intermediate values if any
        intermediate_values = {}
        if optuna_trial.intermediate_values:
            step_values = list(optuna_trial.intermediate_values.items())
            step_values.sort(key=lambda x: x[0])  # Sort by step
            intermediate_values = {problem_spec.primary_metric: [value for _, value in step_values]}
        
        return ENTTrial(
            trial_label=str(optuna_trial.number),
            parameters=parameters,
            metrics=metrics,
            status=status,
            iteration=optuna_trial.number,
            runtime_seconds=runtime_seconds,
            error_message=error_message,
            intermediate_values=intermediate_values
        )
    
    def _get_best_parameters_and_metrics(
        self, 
        study: optuna.Study,
        problem_spec: VOOptimizationProblem
    ) -> Tuple[Dict["str", Any], Dict[EnumMetrics, float]]: # type: ignore
        """
        Extract best parameters and metrics from completed study.
        """
        # Handle case where no trials completed
        if not study.trials or not study.best_trial:
            return {}, {}
        
        best_trial = study.best_trial
        
        # Extract best parameters
        best_params = {k: v for k, v in best_trial.params.items()}
        
        # Extract best metrics
        best_metrics = {}
        
        # Add primary metric
        if best_trial.value is not None:
            best_metrics[problem_spec.primary_metric] = best_trial.value
        
        # Add other metrics from user attributes
        for attr_name, attr_value in best_trial.user_attrs.items():
            if attr_name.startswith(self._get_metric_prefix()):
                metric_name = attr_name.replace(self._get_metric_prefix(), "")
                best_metrics[metric_name] = attr_value
        
        best_params = self._cast_dictkey2enum_for_params(best_params)
        best_metrics = self._cast_dictkey2enum_for_metrics(best_metrics)
        
        return best_params, best_metrics
    
                

    def z_run_optimization_v1(
        self,
        metadata: ENTMetadata,
        search_config: VOOptimizationConfig,
        problem_spec: VOOptimizationProblem,
        objective_fn: objective_function_protocol
    ) -> ENTResult:
        """
        Implementation of the optimization process using Optuna.
        """
        job_label = str(metadata.uid)
        stop_event = threading.Event()
        
        # Configure pruning based on early stopping settings
        custom_pruner = None
        custom_sampler = None
        
        if search_config.pruning:
            # Configure pruner for early stopping
            n_startup_trials = search_config.pruning_grace_iterations or max(5, search_config.max_trials // 10)
            custom_pruner = optuna.pruners.MedianPruner(
                n_startup_trials=n_startup_trials,
                n_warmup_steps=0
            )
            
            # Configure sampler with more appropriate startup trials for early stopping
            custom_sampler = optuna.samplers.TPESampler(
                seed=self.random_seed,
                multivariate=True,
                consider_prior=True,
                prior_weight=1.0,
                n_startup_trials=n_startup_trials
            )
        
        # Create and configure the Optuna study
        study = self._create_study(
            problem_spec, 
            metadata.label, 
            pruner=custom_pruner,
            sampler=custom_sampler
        )
        
        # Register the study so it can be stopped if needed
        self._active_studies[job_label] = (study, stop_event)
        optuna_objective = self._create_objective_fn_wrapper(
            problem_spec, 
            objective_fn, 
            study,
            enable_pruning=search_config.pruning
        )
        
        start_time = time.time()
        trials_completed = 0
        trials_total = 0
        trials = []
        
        try:
            # Calculate timeout with buffer
            effective_timeout = None
            if search_config.timeout_seconds:
                effective_timeout = max(0, search_config.timeout_seconds - self.timeout_buffer_seconds)
            
            # Determine number of parallel jobs
            n_jobs = 1  # Default is sequential execution
            if search_config.max_concurrent_trials and search_config.max_concurrent_trials > 1:
                n_jobs = search_config.max_concurrent_trials
                
            # Start optimization
            study.optimize(
                optuna_objective,
                n_trials=search_config.max_trials,
                timeout=effective_timeout,
                n_jobs=n_jobs,
                catch=(Exception,),
                callbacks=[self._create_stop_callback(stop_event)],
                show_progress_bar=False  # Let our system handle progress reporting
            )
            
            trials_completed = len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
            trials_total = len(study.trials)
            
            # Extract results
            best_params, best_metrics = self._get_best_parameters_and_metrics(study, problem_spec)
            
            # Convert Optuna trials to ENTTrial objects
            trials = [
                self._create_trial(problem_spec, {"data": t})
                for t in study.trials
            ]
            
        finally:
            # Clean up
            if job_label in self._active_studies:
                del self._active_studies[job_label]
        
        end_time = time.time()
        runtime_seconds = end_time - start_time
        
        # Create and return result object
        return ENTResult(
            label = job_label,
            best_parameters=best_params,
            best_metrics=best_metrics,
            trials_completed=trials_completed,
            trials_total=trials_total,
            trials=trials,
            duration_seconds=runtime_seconds,
        )