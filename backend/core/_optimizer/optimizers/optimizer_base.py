from __future__ import annotations
from .._imports import *
from ..objectivefuncs.objfunc_base import *
from .._enums import *

from ..entities import ENTMetadata, ENTTrial, ENTResult
from ..valueobjects import VOOptimizationConfig
from ..utilities import *


class BaseOptimizationRunner(ABC):
    """Abstract base class for optimization runners"""
    RANDOM_SEED = 42

    @property
    def random_seed(self) -> int:
        """
        Random seed for reproducibility.
        """
        seed = getattr(self, "_random_seed", self.__class__.RANDOM_SEED)
        return seed

    @random_seed.setter
    def random_seed(self, value: int) -> None:
        """
        Set the random seed for reproducibility.
        """
        self._random_seed = value
        self._set_random_seed(value)

    @classmethod
    def create_from_config(cls, config: VOOptimizationConfig) -> BaseOptimizationRunner:
        """
        Create an instance of BaseOptimizationRunner from a configuration object.
        """

        raise NotImplementedError("Subclasses should implement this method.")

    def fit(
        self,
        metadata: ENTMetadata,
        opt_config: VOOptimizationConfig,
        opt_problem: VOOptimizationProblem,
        objective_fn: BaseObjectiveFunction
    ) -> ENTResult:
        """
        Run optimization with pre-split data.
        This is a template method that handles common setup and delegates to _run_optimization.
        """
        self._validate_objective_function(opt_problem, objective_fn)
        self._set_random_seed()
        return self._run_optimization(metadata, opt_config, opt_problem, objective_fn)
    
    @abstractmethod
    def _run_optimization(
        self,
        metadata: ENTMetadata,
        search_config: VOOptimizationConfig,
        problem_spec: VOOptimizationProblem,
        objective_fn: BaseObjectiveFunction
    ) -> ENTResult:
        """
        Implement the actual optimization algorithm.
        This method should be implemented by subclasses.
        """
        pass
    
    @abstractmethod
    def stop(self, job_label: str) -> bool:
        """
        Stop an ongoing optimization job
        """
        pass
    
    @abstractmethod
    def _get_param_map(self, parameters: List[VOOptParam]) -> Dict[str, Any]:
        """Convert parameter space to framework-specific format."""
        pass
    
    @abstractmethod
    def _create_trial(
        self,
        problem_spec: VOOptimizationProblem,
        context: Dict[str, Any],
    ) -> ENTTrial:
        """
        Create a standardized trial record. Context is used to pass additional information from the optimizer.
        """
        pass

    def _validate_objective_function(
        self,
        optimization_problem: VOOptimizationProblem,
        objective_fn: BaseObjectiveFunction
    ) -> None:
        """
        Validate that the objective function returns metrics that match those in the problem specification.
        
        1. Creates sample parameters based on the problem specification
        2. Executes the objective function with these parameters
        3. Validates that all required metrics are returned
        4. Ensures returned metrics have proper numeric values
        """

        def execute_with_sample_parameters() -> Dict[EnumMetrics, float]:  # type: ignore
            """Execute objective function with sample parameters."""
            try:
                return objective_fn(sample_params, optimization_problem)
            except Exception as e:
                raise ValueError(f"Objective function execution failed with sample parameters: {str(e)}")
        
        def validate_returned_metrics(results: Dict[EnumMetrics, float]) -> List[str]: # type: ignore
            """Validate metrics returned by the objective function."""
            validation_errors = []
            
            # Check for required metrics
            required_metrics = set(optimization_problem.get_metric_labels())
            returned_metrics = set(results.keys())
            
            missing_metrics = required_metrics - returned_metrics
            if missing_metrics:
                validation_errors.append(
                    f"Objective function doesn't return all required metrics. "
                    f"Missing: {missing_metrics}. "
                    f"Required: {required_metrics}. "
                    f"Returned: {returned_metrics}."
                )
            
            # Check for primary metric
            if optimization_problem.primary_metric not in returned_metrics:
                validation_errors.append(
                    f"Objective function doesn't return the primary metric: {optimization_problem.primary_metric}"
                )
            
            # Verify numeric values
            for metric, value in results.items():
                if not isinstance(value, (int, float)):
                    validation_errors.append(
                        f"Metric '{metric}' returned a non-numeric value: {value} of type {type(value)}"
                    )
                    
            return validation_errors
        
        # LOGIC 
        sample_params = generate_parameter_samples(optimization_problem.parameters)
        results = execute_with_sample_parameters()
        error_log = validate_returned_metrics(results)
        
        
        # Raise consolidated exception if errors were found
        if error_log:
            error_message = '\n -'.join(error_log)
            raise ValueError(f"Objective function validation failed with multiple errors:\n{error_message}")
    
    def _set_random_seed(self, seed:int = 42):
        """
        Set random seed for reproducibility
        """
        np.random.seed(seed)
        random.seed(seed)


    def _cast_dictkey2enum_for_metrics(self, dict_: Dict[str, Any])-> Dict[EnumMetrics, Any]: #type:ignore
        """
        """

        # Convert string keys to enum if applicable. If not found, assume the original str is valid
        dict_remapped = {}
        for k, v in list(dict_.items()):
            _key = EnumMetrics.get_enum_from_str(k)
            dict_remapped[_key] = v
        return dict_remapped