import time
from typing import Dict, List, Any, Optional, <PERSON>ple
import typing as T
import numpy as np
from scipy.optimize import direct, Bounds
import dataclasses

from .optimizer_base import BaseOptimizationRunner
from ..utilities import generate_parameter_samples
from ..valueobjects import VOOptimizationConfig, VOOptimizationProblem, VOOptParam
from ..entities import ENTMetadata, ENTTrial, ENTResult
from ..objectivefuncs.objfunc_base import BaseObjectiveFunction
from .._enums import EnumSearchAlgorithm, EnumMetrics, EnumTrialStatus

#============================
# ALGO SPECIFIC CONFIGS

@dataclasses.dataclass
class AlgoSpecificConfigs():
    locally_biased: bool
    vol_tol: float
    len_tol: float
    eps: float
    
allen_config = AlgoSpecificConfigs(
    locally_biased=False,
    vol_tol=1e-6,
    len_tol= 1e-3,
    eps = 1e-4
)

strict_config = AlgoSpecificConfigs(
        locally_biased= True,
        vol_tol=1e-16,
        len_tol= 1e-6,
        eps = 1e-4
)

# ============================
# LOGIC

class ScipyDirectOptimizationRunner(BaseOptimizationRunner):
    """
    Implementation of BaseOptimizationRunner using SciPy's DIRECT algorithm.
    
    The DIRECT algorithm (DIviding RECTangles) is a deterministic global optimization 
    algorithm that systematically searches the parameter space by dividing it into 
    hyperrectangles.
    
    This implementation is stateless as much as possible and uses SciPy's direct function
    to perform the optimization.
    """
    def __init__(self, extra_config: T.Optional [ AlgoSpecificConfigs ] = None):
        self.algo_config: AlgoSpecificConfigs = extra_config or allen_config
    
    @classmethod
    def create_from_config(cls, config: VOOptimizationConfig) -> 'ScipyDirectOptimizationRunner':
        """Create an instance of ScipyDirectOptimizationRunner from a configuration object."""
        runner = cls()
        runner.random_seed = config.random_seed
        return runner
    
    def _run_optimization(
        self,
        metadata: ENTMetadata,
        search_config: VOOptimizationConfig,
        problem_spec: VOOptimizationProblem,
        objective_fn: BaseObjectiveFunction
    ) -> ENTResult:
        """Run optimization using SciPy's DIRECT algorithm.
        
        Args:
            metadata: Metadata for the optimization job
            search_config: Configuration for the optimization process
            problem_spec: Problem specification including parameters and metrics
            objective_fn: Objective function to minimize/maximize
            
        Returns:
            Result of the optimization containing the best parameters and metrics
        """
        # Start timing
        start_time = time.time()
        
        # Setup
        bounds_list = self._get_param_bounds(problem_spec.parameters)
        param_names = problem_spec.get_param_labels()
        wrapped_objective = self._create_wrapped_objective(objective_fn, problem_spec, param_names)
        
        # Store trials during optimization
        trials = []
        trial_counter = 0
        
        # Define callback for tracking progress
        def callback(x):
            nonlocal trial_counter
            # Convert array of parameter values to dictionary
            params_dict = {param_names[i]: x[i] for i in range(len(x))}
            
            # Evaluate the objective function to get metrics
            metrics = objective_fn(params_dict, problem_spec)
            
            # Create a trial object
            trial = self._create_trial(
                problem_spec=problem_spec,
                context={
                    "params": params_dict,
                    "metrics": metrics,
                    "trial_number": trial_counter,
                }
            )
            trials.append(trial)
            trial_counter += 1
        
        # Run SciPy's DIRECT optimization
        try:
            result = direct(
                wrapped_objective, # type: ignore
                bounds=bounds_list,
                maxiter=search_config.max_trials,
                callback=callback,
                vol_tol=self.algo_config.vol_tol,  # Volume tolerance for termination
                len_tol=self.algo_config.len_tol,  # Length tolerance for termination
                eps=self.algo_config.eps,  # Trade-off between local and global search
                locally_biased=self.algo_config.locally_biased,  # Option to Use the DIRECT_L variant for better local refinement. 
            )
            
            # Get the best parameters found
            best_x = result.x
            best_params = {param_names[i]: best_x[i] for i in range(len(best_x))}
            
            # Get the corresponding metrics
            best_metrics = objective_fn(best_params, problem_spec)
            
            # End timing
            duration = time.time() - start_time

            # Ensure at least one trial exists if optimization succeeded but no callbacks happened
            if not trials and best_params:
                trial = self._create_trial(
                    problem_spec=problem_spec,
                    context={
                        "params": best_params,
                        "metrics": best_metrics,
                        "trial_number": 0
                    }
                )
                trials.append(trial)
            
            # Create result object
            opt_result = ENTResult(
                label=metadata.label,
                best_parameters=best_params,
                best_metrics=best_metrics,
                trials=trials,
                trials_total=len(trials),
                trials_completed=len([t for t in trials if t.status == EnumTrialStatus.COMPLETED]),
                duration_seconds=duration
            )
            
            return opt_result
        
        except Exception as e:
            # If optimization fails, create a result with the best trial so far
            if trials:
                # Find the best trial based on the primary metric
                primary_metric = problem_spec.primary_metric
                primary_metric_obj = problem_spec.get_primary_metric()
                is_maximize = primary_metric_obj.is_maximize()
                
                # Sort trials by the primary metric (accounting for minimize/maximize)
                completed_trials = [t for t in trials if t.status == EnumTrialStatus.COMPLETED]
                if completed_trials:
                    sorted_trials = sorted(
                        completed_trials, 
                        key=lambda t: t.metrics.get(str(primary_metric), float('inf')),
                        reverse=is_maximize
                    )
                    best_trial = sorted_trials[0]
                    best_params = best_trial.parameters
                    best_metrics = {metric: best_trial.metrics.get(str(metric), 0.0) for metric in problem_spec.get_metric_labels()}
                else:
                    # If no completed trials, use the first trial
                    best_trial = trials[0]
                    best_params = best_trial.parameters
                    best_metrics = {metric: best_trial.metrics.get(str(metric), 0.0) for metric in problem_spec.get_metric_labels()}
            else:
                # If no trials at all, that means the optimization failed immediately
                # Raise an error indicating the optimization failed
                raise ValueError(f"Optimization failed with no trials completed: {str(e)}")
            
            # End timing
            duration = time.time() - start_time
            
            
            
            # Create result object with what we have
            opt_result = ENTResult(
                label=metadata.label,
                best_parameters=best_params,
                best_metrics=best_metrics,
                trials=trials,
                trials_total=len(trials),
                trials_completed=len([t for t in trials if t.status == EnumTrialStatus.COMPLETED]),
                duration_seconds=duration
            )
            
            return opt_result
    
    def stop(self, job_label: str) -> bool:
        """Stop an ongoing optimization job
        
        Since this implementation doesn't maintain state or run jobs in the background,
        this method always returns False indicating the job wasn't found.
        """
        # This implementation doesn't maintain state or run jobs in the background
        # so there's nothing to stop
        return False
    
    def _get_param_map(self, parameters: List[VOOptParam]) -> Dict[str, Any]:
        """Convert parameter space to framework-specific format.

        Returns:
            Dictionary mapping parameter names to SciPy-specific configuration
        """
        # Not implemented, not used in Direct
        return {}
    
    def _create_trial(
        self,
        problem_spec: VOOptimizationProblem,
        context: Dict[str, Any],
    ) -> ENTTrial:
        """Create a standardized trial record.
        
        Args:
            problem_spec: Problem specification
            context: Additional information about the trial
            
        Returns:
            Trial record object
        """
        params = context.get("params", {})
        metrics = context.get("metrics", {})
        trial_number = context.get("trial_number", 0)
        
        # Convert metrics to string keys if they're EnumMetrics
        metrics_str = {}
        for k, v in metrics.items():
            if isinstance(k, EnumMetrics):
                metrics_str[str(k)] = v
            else:
                metrics_str[k] = v
        
        # Create trial object
        trial = ENTTrial(
            trial_label=f"trial_{trial_number}",
            parameters=params,
            metrics=metrics_str,
            status=EnumTrialStatus.COMPLETED,
            iteration = trial_number,
            runtime_seconds=0.0,  # Not tracking individual trial runtime
        )
        
        return trial
    
    def _get_param_bounds(self, parameters: List[VOOptParam]) -> List[Tuple[float, float]]:
        """Extract parameter bounds for SciPy's DIRECT algorithm.
        
        Returns:
            List of (min, max) pairs for each parameter
        """
        bounds = []
        
        for param in parameters:
            if param.min_max_inclusive is not None:
                lower, upper = param.min_max_inclusive
                bounds.append((float(lower), float(upper)))
            else:
                # For this implementation, we'll assume all parameters are numeric
                raise ValueError(f"Parameter {param.label} does not have min_max_inclusive bounds defined.")
        
        return bounds
    
    def _create_wrapped_objective(
        self,
        objective_fn: BaseObjectiveFunction,
        problem_spec: VOOptimizationProblem,
        param_names: List[str]
    ) -> T.Callable[[np.ndarray, T.Any], float]:
        """Create a wrapper around the objective function for SciPy's DIRECT algorithm.
            
        Returns:
            Wrapped objective function that takes a single array of parameter values
        """
        primary_metric = problem_spec.primary_metric
        is_maximize = problem_spec.get_primary_metric().is_maximize()
        
        def wrapped_objective(x, *args):
            # Convert array of parameter values to dictionary
            params_dict = {param_names[i]: x[i] for i in range(len(x))}
            
            # Call the objective function
            metrics = objective_fn(params_dict, problem_spec)
            
            # Extract the primary metric value
            metric_value = metrics.get(primary_metric, float('inf'))
            
            # SciPy's DIRECT minimizes, so negate if we're maximizing
            return -metric_value if is_maximize else metric_value
        
        return wrapped_objective