from __future__ import annotations

from .._imports import *
from .optimizer_base import *
from ..utilities import generate_parameter_samples

class MockOptimizationRunner(BaseOptimizationRunner):
    """
    Simplified mock implementation of BaseOptimizationRunner for testing.
    Uses random sampling without any actual optimization algorithm.
    """
    def __init__(self, random_seed: int = 42, simulate_failure_rate: float = 0.0):
        """
        Initialize the mock optimizer
        
        Args:
            random_seed: Seed for reproducible parameter sampling
            simulate_failure_rate: Probability of simulated trial failures (0.0 = no failures)
        """
        self.running_jobs: Dict[str, bool] = {}  # Maps job_id to is_running status
        self.simulate_failure_rate = simulate_failure_rate
        self.random_seed = random_seed

    def _run_optimization(
        self,
        metadata: ENTMetadata,
        search_config: VOOptimizationConfig,
        problem_spec: VOOptimizationProblem,
        objective_fn: BaseObjectiveFunction
    ) -> ENTResult:
        """
        Run optimization via random parameter sampling.
        No actual optimization algorithm, just random exploration.
        """
        job_id = str(metadata.uid) or str(uuid.uuid4())
        self.running_jobs[job_id] = True
        
        # Track optimization state
        start_time = time.time()
        completed_trials = []
        failed_trials = []
        best_metrics = None
        best_parameters = None
        best_value = None
        
        # Run trials
        for trial_idx in range(search_config.max_trials):
            # Check if job was stopped
            if not self.running_jobs.get(job_id, False):
                break
                
            # Start trial timing
            trial_start_time = time.time()
            trial_id = f"{job_id}_{trial_idx}"
            
            # Sample parameters using standalone function
            param_map = self._get_param_map(problem_spec.parameters)
            parameters = generate_parameter_samples(
                problem_spec.parameters, 
                random_seed=self.random_seed + trial_idx  # Unique seed per trial
            )
            
            # Simulate random failure if configured
            if random.random() < self.simulate_failure_rate:
                failed_trial = self._create_trial(
                    trial_id=trial_id,
                    parameters=parameters,
                    metrics={},
                    status=EnumTrialStatus.FAILED,
                    runtime_seconds=time.time() - trial_start_time,
                    error_message="Simulated random failure"
                )
                failed_trials.append(failed_trial)
                continue
            
            # Run objective function
            try:
                metrics = objective_fn(parameters, problem_spec)
                
                # Create successful trial record
                trial = self._create_trial(
                    trial_id=trial_id,
                    parameters=parameters,
                    metrics=metrics,
                    status=EnumTrialStatus.COMPLETED,
                    runtime_seconds=time.time() - trial_start_time
                )
                completed_trials.append(trial)
                
                # Track best result
                vo_pri_metric = problem_spec.get_primary_metric()
                is_maximize = vo_pri_metric.is_maximize()
                current_value = metrics[vo_pri_metric.label]
                
                # Update best if better or first
                if best_value is None or (
                    (is_maximize and current_value > best_value) or
                    (not is_maximize and current_value < best_value)
                ):
                    best_value = current_value
                    best_metrics = metrics
                    best_parameters = parameters
                    
            except Exception as e:
                # Handle actual exceptions from objective function
                failed_trial = self._create_trial(
                    trial_id=trial_id,
                    parameters=parameters,
                    metrics={},
                    status=EnumTrialStatus.FAILED,
                    runtime_seconds=time.time() - trial_start_time,
                    error_message=str(e)
                )
                failed_trials.append(failed_trial)
                
        # Cleanup job tracking
        self.running_jobs.pop(job_id, None)
        
        # Return final result
        return ENTResult(
            label=job_id,
            best_parameters=best_parameters or {},
            best_metrics=best_metrics or {},
            trials_total=len(completed_trials) + len(failed_trials),
            trials_completed=len(completed_trials),
            duration_seconds=time.time() - start_time,
            trials=completed_trials + failed_trials
        )
    
    def stop(self, job_id: str) -> bool:
        """Stop a running optimization job"""
        if job_id in self.running_jobs:
            self.running_jobs[job_id] = False
            return True
        return False
    
    def _get_param_map(self, parameters: List[VOOptParam]) -> Dict[str, Dict[str, Any]]:
        """
        Convert parameters to a simple search space format.
        Since we're now using the standalone sample_parameters function,
        this is only kept to satisfy the abstract method requirement.
        """
        search_space = {}
        
        for param in parameters:
            search_space[param.label] = param.model_dump()
            
        return search_space
    
    
    def _create_trial(
        self,
        trial_id: str,
        parameters: Dict[str, Any],
        metrics: Dict[str, float],
        status: EnumTrialStatus = EnumTrialStatus.PENDING,
        iteration: int = 1,
        runtime_seconds: float = 0.0,
        error_message: Optional[str] = None,
        intermediate_values: Optional[Dict[str, List[float]]] = None
    ) -> ENTTrial:
        """Create a standardized trial record using Pydantic model construction"""
        # Create dictionary with all trial fields
        trial_data = {
            "trial_id": trial_id,
            "parameters": parameters,
            "metrics": metrics,
            "status": status,
            "iteration": iteration,
            "runtime_seconds": runtime_seconds,
            "error_message": error_message,
            "intermediate_values": intermediate_values or {}
        }
        
        # Use Pydantic's model_validate to create ENTTrial object from dictionary
        return ENTTrial.model_validate(trial_data)