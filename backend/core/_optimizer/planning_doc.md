# Direct Surrogate Optimization Plan

## Overview

This document outlines the implementation of SciPy's DIRECT optimization algorithm with surrogate models to efficiently find optimal parameters for time series data. The key components are:

1. **ScipyDirectOptimizationRunner**: Implements optimization using SciPy's DIRECT algorithm
2. **SurrogateObjectiveFunction**: Acts as the bridge between the optimization algorithm and the surrogate model, computing various metrics between surrogate predictions and experimental targets

## Problem Statement

We optimize unknown parameters using:

- **Experimental data** (`df_x_partial`): Time series dataset containing measurements with fixed parameters. Column names are UUIDs except for timeset and timestep columns.
- **Target values** (`df_y_true`): Target measurements we're trying to match/optimize for
- **Candidate parameters**: Parameters we're trying to optimize (applied uniformly across all time points for each trial)

The surrogate model needs to predict outputs close to experimental targets by finding the best values for the candidate parameters while keeping the values in df_x_partial constant.

## Architecture

```mermaid
flowchart TD
    A[ScipyDirectOptimizationRunner] --Provides candidate parameter values--> B[SurrogateObjectiveFunction]
    B --Returns metric values--> A
    B --Makes predictions with combined parameters--> C[Surrogate Model]
    C --Returns predicted values--> B
    D[df_x_partial] --Maintained internally--> B
    E[df_y_true] --Maintained internally--> B
    F[VOOptimizationProblem] --Configuration--> A
    F --Metric definitions--> B
    G[AlgoSpecificConfigs] --Algorithm parameters--> A
```

## Implementation Details

### 1. SurrogateObjectiveFunction

The SurrogateObjectiveFunction computes metrics between surrogate model predictions and experimental targets:

```python
class SurrogateObjectiveFunction(BaseObjectiveFunction):
    def __init__(
        self, 
        surrogate_model: BaseSurrogateModel,
        df_x_partial: pd.DataFrame,  # Time series data with timeset/timestep columns and fixed conditions
        df_y_true: pd.DataFrame,     # Target values to optimize against
    ):
        """
        Initialize the surrogate objective function.
        - df_y_true must match shape of initial training y
        - df_x_partial when combined with candidate parameters must match initial training x
        """
        self.surrogate_model = surrogate_model
        self.df_x_partial = df_x_partial
        self.df_y_true = df_y_true
        
        # Register metric calculators
        self.metric_calculators = self._register_metric_calculators()
    
    def _register_metric_calculators(self):
        """Set up mapping between metric names and calculator functions."""
        return {
            EnumMetrics.REG_MSE: self._calculate_mse,
            EnumMetrics.REG_MAE: self._calculate_mae,
            EnumMetrics.REG_RMSE: self._calculate_rmse,
            EnumMetrics.REG_R2_SCORE: self._calculate_r2,
            EnumMetrics.TS_MAPE: self._calculate_mape,
        }
    
    def __call__(
        self,
        parameters: Dict[str, Any],
        optimization_problem: VOOptimizationProblem
    ) -> Dict[EnumMetrics, float]:
        """Calculate metrics using surrogate model predictions and experimental data."""
        # Assemble full parameter dataframe
        df_x_full = self._assemble_df_x(parameters)
        metrics = optimization_problem.metrics
        result = {}
        
        # Get predictions and compute metrics
        df_y_pred = self.surrogate_model.predict(df_x_full)
        for metric in metrics:
            metric_label = metric.label
            calc_func = self.metric_calculators[metric_label]
            result[metric_label] = calc_func(self.df_y_true, df_y_pred)
        
        return result
        
    def _assemble_df_x(self, candidate_parameters: Dict[str, float]) -> pd.DataFrame:
        """Extend the dataframe with candidate parameters (same value across all rows)."""
        df = self.df_x_partial.copy()
        df = df.assign(**candidate_parameters)
        return df
```

The SurrogateObjectiveFunction supports multiple metrics for evaluating predictions:

1. **Mean Squared Error (MSE)**: Standard squared error metric
2. **Mean Absolute Error (MAE)**: More robust to outliers than MSE
3. **Root Mean Squared Error (RMSE)**: Square root of MSE, in same units as target
4. **R² Score**: Coefficient of determination, measure of fit quality 
5. **Mean Absolute Percentage Error (MAPE)**: Percentage error useful for certain applications

Each metric handles multiple output columns by calculating the metric for each column and averaging.

### 2. ScipyDirectOptimizationRunner

The ScipyDirectOptimizationRunner implements the DIRECT algorithm with configurable parameters:

```python
@dataclasses.dataclass
class AlgoSpecificConfigs():
    locally_biased: bool      # Whether to use DIRECT-L variant for better local refinement
    vol_tol: float            # Volume tolerance for termination
    len_tol: float            # Length tolerance for termination
    eps: float                # Trade-off between local and global search

# Predefined configurations
allen_config = AlgoSpecificConfigs(
    locally_biased=False,     # Standard DIRECT (more global exploration)
    vol_tol=1e-6,
    len_tol=1e-3,
    eps=1e-4
)

strict_config = AlgoSpecificConfigs(
    locally_biased=True,      # DIRECT-L (better local refinement)
    vol_tol=1e-16,
    len_tol=1e-6,
    eps=1e-4
)

class ScipyDirectOptimizationRunner(BaseOptimizationRunner):
    def __init__(self, extra_config: Optional[AlgoSpecificConfigs] = None):
        self.algo_config = extra_config or allen_config
    
    def _run_optimization(
        self,
        metadata: ENTMetadata,
        search_config: VOOptimizationConfig,
        problem_spec: VOOptimizationProblem,
        objective_fn: BaseObjectiveFunction
    ) -> ENTResult:
        """Run optimization using SciPy's DIRECT algorithm."""
        # Start timing
        start_time = time.time()
        
        # Setup parameter bounds and get parameter names
        bounds_list = self._get_param_bounds(problem_spec.parameters)
        param_names = problem_spec.get_param_labels()
        
        # Create wrapped objective function
        wrapped_objective = self._create_wrapped_objective(objective_fn, problem_spec, param_names)
        
        # Store trials during optimization
        trials = []
        trial_counter = 0
        
        # Define callback for tracking trials
        def callback(x):
            nonlocal trial_counter
            params_dict = {param_names[i]: x[i] for i in range(len(x))}
            metrics = objective_fn(params_dict, problem_spec)
            trial = self._create_trial(
                problem_spec=problem_spec,
                context={"params": params_dict, "metrics": metrics, "trial_number": trial_counter}
            )
            trials.append(trial)
            trial_counter += 1
        
        # Run SciPy's DIRECT optimization
        try:
            result = direct(
                wrapped_objective,
                bounds=bounds_list,
                maxiter=search_config.max_trials,
                callback=callback,
                vol_tol=self.algo_config.vol_tol,
                len_tol=self.algo_config.len_tol,
                eps=self.algo_config.eps,
                locally_biased=self.algo_config.locally_biased,
            )
            
            # Get the best parameters and metrics
            best_x = result.x
            best_params = {param_names[i]: best_x[i] for i in range(len(best_x))}
            best_metrics = objective_fn(best_params, problem_spec)
            
            # Create result object
            opt_result = ENTResult(
                label=metadata.label,
                best_parameters=best_params,
                best_metrics=best_metrics,
                trials=trials,
                trials_total=len(trials),
                trials_completed=len([t for t in trials if t.status == EnumTrialStatus.COMPLETED]),
                duration_seconds=time.time() - start_time
            )
            
            return opt_result
            
        except Exception as e:
            # Handle optimization failure by returning the best trial so far if possible
            # Otherwise raise an error
            if not trials:
                raise ValueError(f"Optimization failed with no trials completed: {str(e)}")
            
            # Find the best trial based on the primary metric
            primary_metric = problem_spec.primary_metric
            # ...implementation details for salvaging results after failure...
        
    def _create_wrapped_objective(
        self,
        objective_fn: BaseObjectiveFunction,
        problem_spec: VOOptimizationProblem,
        param_names: List[str]
    ) -> Callable[[np.ndarray, Any], float]:
        """Create a wrapper around the objective function for SciPy's DIRECT algorithm."""
        primary_metric = problem_spec.primary_metric
        is_maximize = problem_spec.get_primary_metric().is_maximize()
        
        def wrapped_objective(x, *args):
            # Convert array of parameter values to dictionary
            params_dict = {param_names[i]: x[i] for i in range(len(x))}
            
            # Call the objective function
            metrics = objective_fn(params_dict, problem_spec)
            
            # Extract the primary metric value
            metric_value = metrics.get(primary_metric, float('inf'))
            
            # SciPy's DIRECT minimizes, so negate if we're maximizing
            return -metric_value if is_maximize else metric_value
        
        return wrapped_objective
```

#### Key Features of the ScipyDirectOptimizationRunner:

1. **Algorithm Configuration**: Two preset configurations (allen_config and strict_config) with different optimization behaviors
2. **Objective Function Wrapping**: Automatically handles both minimization and maximization problems
3. **Trial Tracking**: Records each evaluation through the callback mechanism
4. **Error Handling**: Attempts to recover best results even if optimization fails
5. **Metric Conversion**: Converts EnumMetrics keys to strings in trial records

#### Important Behavior Notes:

1. **Objective Function Calls vs. Trials**: The SciPy DIRECT algorithm internally calls the objective function more times than the callback is invoked. This is expected behavior and means that the objective function call count may be greater than the number of recorded trials.

2. **Parameter Scaling**: The algorithm handles parameters with different scales, but extreme differences in scale (many orders of magnitude) may affect optimization performance.

3. **Bounds Handling**: The algorithm strictly respects parameter bounds; the optimum will be at a bound edge if the true optimum lies outside the search space.

## Usage Example

```python
# Create surrogate model (e.g., trained on previous experimental data)
surrogate_model = create_surrogate_model()

# Prepare experimental data with fixed parameters
df_x_partial = pd.DataFrame({
    'timeset': [1, 2, 3, 4, 5],
    'timestep': [0.1, 0.1, 0.1, 0.1, 0.1],
    'fixed_param1': [1.0, 1.0, 1.0, 1.0, 1.0],
    'fixed_param2': [2.0, 2.0, 2.0, 2.0, 2.0]
})

# Target values we're trying to match
df_y_true = pd.DataFrame({
    'target': [10.2, 12.5, 15.1, 16.8, 17.4]
})

# Create objective function
objective_fn = SurrogateObjectiveFunction(
    surrogate_model=surrogate_model,
    df_x_partial=df_x_partial,
    df_y_true=df_y_true
)

# Define optimization problem
problem_spec = VOOptimizationProblem(
    parameters=[
        VOOptParam(
            label="param_a",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(0.0, 10.0)
        ),
        VOOptParam(
            label="param_b",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(-5.0, 5.0)
        )
    ],
    metrics=[
        VOOptMetric(
            label=EnumMetrics.REG_MSE,
            direction=EnumDirection.MINIMIZE
        ),
        VOOptMetric(
            label=EnumMetrics.REG_R2_SCORE,
            direction=EnumDirection.MAXIMIZE
        )
    ],
    primary_metric=EnumMetrics.REG_MSE
)

# Configure optimization run
config = VOOptimizationConfig(
    max_trials=50,
    max_concurrent_trials=1,
    timeout_seconds=300,
    strategy=EnumSearchAlgorithm.DIRECT,
    random_seed=42
)

# Create metadata
metadata = ENTMetadata(
    label="Parameter Optimization",
    description="Finding optimal parameters for the surrogate model",
    uid=uuid.uuid4()
)

# Create and run optimizer
runner = ScipyDirectOptimizationRunner(extra_config=strict_config)
result = runner.fit(metadata, config, problem_spec, objective_fn)

# Access results
print(f"Best parameters: {result.best_parameters}")
print(f"Best metrics: {result.best_metrics}")
print(f"Total trials: {result.trials_total}")
print(f"Optimization duration: {result.duration_seconds} seconds")
```

## About the DIRECT Algorithm

The DIRECT (DIviding RECTangles) algorithm is a deterministic global optimization method that systematically divides the search space into hyperrectangles, focusing on areas that show promise. Key characteristics:

1. **Deterministic**: Given the same problem and parameters, it will always produce the same result
2. **Global Search**: Capable of finding global optima without getting trapped in local minima
3. **No Gradient Information**: Does not require derivative information
4. **Balanced Exploration and Exploitation**: Explores the full parameter space while focusing on promising regions

The implementation offers two variants:
- **Standard DIRECT** (locally_biased=False): Better global exploration
- **DIRECT-L** (locally_biased=True): Better local refinement

## Performance Considerations

1. **Scalability**: Performance can degrade with high-dimensional parameter spaces (>10 dimensions)
2. **Efficiency**: DIRECT makes more objective function evaluations than the number of trials recorded
3. **Convergence**: Adjust the algorithm parameters (vol_tol, len_tol, eps) for the desired balance between solution quality and computation time

Here's how these components will work together with time series data:

```python
# Create surrogate model
surrogate_model = BaseSurrogateModel(...)  # Your surrogate model implementation

# Time series experimental data (contains timeset and timestep columns)
experimental_data = pd.DataFrame(...)  # df_x
target_values = pd.DataFrame(...)      # df_y

# Fixed parameters applied uniformly across all time points
fixed_parameters = {"param1": value1, "param2": value2}

# Search parameters to optimize
parameters = [
    VOOptParam(label="param3", type_=VOEnumParameterType.FLOAT_UNIFORM, min_max_inclusive=(min3, max3)),
    VOOptParam(label="param4", type_=VOEnumParameterType.FLOAT_UNIFORM, min_max_inclusive=(min4, max4))
]

# Create problem specification (no need for samples as SurrogateObjectiveFunction is stateful)
problem = VOOptimizationProblem(
    parameters=parameters,
    metrics=[VOOptMetric(label=VOEnumMetrics.REG_MSE, direction=VOEnumDirection.MINIMIZE)],
    primary_metric=VOEnumMetrics.REG_MSE
)

# Create objective function with experimental data and fixed parameters
objective_fn = SurrogateObjectiveFunction(
    surrogate_model=surrogate_model,
    experimental_data=experimental_data,
    target_values=target_values,
    fixed_parameters=fixed_parameters
)

# Configure and run optimization
config = VOOptimizationConfig(max_trials=100, strategy=VOEnumSearchAlgorithm.DIRECT)
runner = ScipyDirectOptimizationRunner.create_from_config(config)
result = runner.fit(metadata, config, problem, objective_fn)
```

## Key Design Considerations

1. **Separation of concerns**
   - ScipyDirectOptimizationRunner handles optimization algorithm for search parameters only
   - SurrogateObjectiveFunction manages experimental data and combines parameters

2. **Statefulness**
   - SurrogateObjectiveFunction is stateful, maintaining experimental data and fixed parameters
   - The experimental data structure (timeset/timestep) is preserved

3. **Time Series Handling**
   - Both fixed and search parameters are applied uniformly across all time points
   - Predictions maintain the temporal structure of the input data

4. **Performance**
   - DIRECT algorithm efficiently explores parameter space
   - SurrogateObjectiveFunction assembles prediction dataframes efficiently
