from __future__ import annotations
from typing import Protocol, runtime_checkable, Dict, Union, Any, TYPE_CHECKING
from .._enums import EnumMetrics
from abc import abstractmethod, ABC

if TYPE_CHECKING:
    from ..valueobjects import VOOptimizationProblem, EnumMetrics

class BaseObjectiveFunction(ABC):

    @abstractmethod
    def __call__(
            self,
            parameters: Dict[str, Any],
            optimization_problem: VOOptimizationProblem
    ) -> Dict[EnumMetrics, float]:
        """
        Args: parameters: dict of model parameters and the value to set
        """
        ...