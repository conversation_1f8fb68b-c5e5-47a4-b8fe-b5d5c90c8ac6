from typing import Dict, List, Optional, Any, Union
import pandas as pd
import typing as T
import numpy as np
from sklearn import metrics

from .objfunc_base import BaseObjectiveFunction
from ..valueobjects import EnumMetrics, VOOptimizationProblem
from .._enums import EnumMetrics
from backend.core._surrogate.models import BaseSurrogateModel


class SurrogateObjectiveFunction(BaseObjectiveFunction):
    """
    Creates an objective function callable from a surrogate model.
    
    This class conforms to objective_function_protocol by implementing __call__,
    making instances directly usable in optimization runners.
    """
    def __init__(
        self, 
        surrogate_model: BaseSurrogateModel,
        df_x_partial: pd.DataFrame,  # df_x: Time series data with timeset/timestep columns and fixed conditions. Col names to be UUID except for timeset and timesteps
        df_y_true: pd.DataFrame,      # df_y: Target values to optimize against
    ):
        """
        Initialize the surrogate objective function.
        - df_y must match shape of initial training y
        - df_x_partial when combined with features to calibrate must match initial training x
        
        Args
            df_x_partial: DF with setpoint and fixed conditions
            df_y: known results
        """
        self.surrogate_model = surrogate_model
        self.df_x_partial = df_x_partial
        self.df_y_true = df_y_true
        
        # Register metric calculators
        self.calculator_registry: T.Dict[EnumMetrics, T.Callable[[T.Union[pd.Series, pd.DataFrame], T.Union[pd.Series, pd.DataFrame]], float]] = self._register_metric_calculators()
    
    def _register_metric_calculators(self):
        """Set up the mapping between metric names and calculator functions."""
    
        calculators = {
            EnumMetrics.REG_MSE: self._calculate_mse,
            EnumMetrics.REG_MAE: self._calculate_mae,
            EnumMetrics.REG_RMSE: self._calculate_rmse,
            EnumMetrics.REG_R2_SCORE: self._calculate_r2,
            EnumMetrics.TS_MAPE: self._calculate_mape,
        }
        
        return calculators
    def prepare_y_data(self, y_true: pd.DataFrame, y_pred: pd.DataFrame) -> T.Tuple[pd.Series, pd.Series]:
        """
        Prepare data for metric calculation by handling sparse time series data.

        This method:
        1. Identifies non-null measurements in y_true
        2. Transforms both datasets using the surrogate model's transformer
        3. Extracts only the data points where actual measurements exist
        

        Args:
            y_true: DataFrame with ground truth values (may be sparse with NaNs)
            y_pred: DataFrame with predicted values (complete)

        Returns:
            Tuple of (true_values, predicted_values) as pandas Series
        """
        def handle_data_weighting(y_true: pd.DataFrame, y_pred: pd.DataFrame) -> T.Tuple[pd.DataFrame, pd.DataFrame, Any]:
            y_true_filled = self._impute_missing_timesteps(y_true, y_pred)
            null_bool_mask = self._get_null_boolmask(y_true_filled, time_cols)

            merged_data = self._impute_missing_values(y_true_filled, y_pred, null_bool_mask)

            # Step 4: Transform both datasets
            y_true_transformed, y_pred_transformed = self._transform_data(
                merged_data, y_pred, required_cols, transformer
            )
            
            return y_true_transformed, y_pred_transformed, null_bool_mask

        # LOGIC
        transformer = self.surrogate_model.datatransformer


        # Determine required columns
        time_cols = [transformer.timeset_col, transformer.timestep_col] if transformer.is_timeseries else []
        required_cols = list(transformer.y_cols) + time_cols

        # sort Y true by timeset and timestep. 
        y_true = y_true.sort_values(by=time_cols)
        y_pred = y_pred.sort_values(by=time_cols)

        y_true_transformed, y_pred_transformed, non_null_indices = handle_data_weighting(y_true, y_pred)
        comparison_data = self._extract_comparison_data(
            y_true_transformed, y_pred_transformed, non_null_indices
        )

        return comparison_data["True Values"], comparison_data["Predicted Values"]

    def _impute_missing_timesteps(self, y_true: pd.DataFrame, y_pred: pd.DataFrame) -> pd.DataFrame:
        """
        Handle missing timesteps in ground truth data by aligning with prediction time grid.
        
        For time series data, this ensures y_true has the complete timestep structure from 
        
        The values for imputed rows are simply NaN
        """
        transformer = self.surrogate_model.datatransformer
    
        # """ STEP 1: Quick return for non-time series data """
        if not transformer.is_timeseries:
            return y_true.copy()
    
        # Get time column names
        timeset_col = transformer.timeset_col
        timestep_col = transformer.timestep_col
        time_cols = [timeset_col, timestep_col]
        
        # """ STEP 2: Validate timeset consistency 
        #     Example:
        #     y_true_timesets = {0, 1}
        #     y_pred_timesets = {0, 1, 2}
        #     This is valid since all y_true timesets exist in y_pred
        # """
        y_true_timesets = set(y_true[timeset_col].unique()) if timeset_col in y_true.columns else set()
        y_pred_timesets = set(y_pred[timeset_col].unique())
    
        if y_true_timesets and not y_true_timesets.issubset(y_pred_timesets):
            raise ValueError(f"y_true contains timesets {y_true_timesets - y_pred_timesets} not present in y_pred")
    
        # """ STEP 3: Create complete time grid from predictions
        #     Creates a DataFrame with all unique (timeset, timestep) combinations
        #     Example:
        #     timeset | timestep
        #     --------|---------
        #       0     |    0
        #       0     |    1
        #       0     |    2
        #       1     |    0
        #       1     |    1
        #       1     |    2
        # """
        y_pred_time_grid = y_pred[time_cols].drop_duplicates().sort_values(by=time_cols)
        
        # """ STEP 4: Merge ground truth data onto complete time grid
        #     LEFT JOIN ensures ALL timesteps from predictions are preserved
        #     For each (timeset,timestep) in y_pred_time_grid:
        #       - If matching row exists in y_true: use y_true values
        #       - If no matching row: create row with NaN feature values
        #
        #     This is equivalent to iterating:
        #     for each timeset in y_pred_timesets:
        #       for each timestep in y_pred[timeset]:
        #           if (timeset,timestep) exists in y_true:
        #               use y_true values
        #           else:
        #               create new row with NaN values
        # #     For example, after merge we have:
        # #     timeset | timestep | feature1
        # #     --------|----------|--------
        # #       0     |    0     |  5.0   (from y_true)
        # #       0     |    1     |  NaN   (missing in y_true)
        # #       1     |    0     |  7.0   (from y_true)
        # """
        y_true_filled = pd.merge(
            y_pred_time_grid,  # Complete time grid (all timesteps for all timesets)
            y_true,            # Sparse ground truth data (missing some timesteps)
            on=time_cols,      # Join on time columns
            how="left",        # Keep all prediction timesteps
        )

        return y_true_filled

    def _get_null_boolmask(self, y_true: pd.DataFrame, time_cols: List[str]) -> pd.DataFrame:
        """
        Get a boolean mask of non-null measurements in y_true, excluding time columns.
    
        Args:
            y_true: DataFrame with ground truth values
            time_cols: List of time column names to exclude from non-null detection
    
        Returns:
            Boolean DataFrame where True indicates a non-null value to preserve
    
        Example:
            For a DataFrame:
                timeset  timestep  target1  target2
            0        0         0      1.0      NaN
            1        0         1      NaN      2.0
            2        1         0      3.0      4.0
    
            Returns DataFrame mask:
                timeset  timestep  target1  target2
            0    False     False     True    False
            1    False     False    False     True
            2    False     False     True     True
        """
        # Create boolean mask indicating which values are non-null
        mask_df = y_true.notnull()
        
        # Set time columns to False (we don't want to preserve these)
        for col in time_cols:
            if col in mask_df.columns:
                mask_df[col] = False
                
        return mask_df

    def _impute_missing_values(self, y_true: pd.DataFrame, y_pred: pd.DataFrame,
                         non_null_mask: pd.DataFrame) -> pd.DataFrame:
        """
        Impute missing values in y_true with corresponding values from y_pred.
    
        Uses vectorized operations with a boolean mask for higher performance.
    
        Args:
            y_true: DataFrame with ground truth values (may have NaNs)
            y_pred: DataFrame with predicted values (complete)
            non_null_mask: Boolean DataFrame where True indicates values to preserve from y_true
    
        Returns:
            DataFrame with y_pred structure but actual values where available
            
        Example:
            y_true =                 y_pred =                non_null_mask =
               A    B                   A    B                  A     B
            0  1.0  NaN              0  10.0  20.0           0  True  False
            1  NaN  3.0              1  30.0  40.0           1  False True
            
            Result:
               A    B
            0  1.0  20.0  # A from y_true, B from y_pred
            1  30.0  3.0  # A from y_pred, B from y_true
        """
        # """ STEP 1: Start with prediction values """
        merged_data = y_pred.copy()
        
        # """ STEP 2: Quick return if no mask provided """
        if non_null_mask is None or non_null_mask.empty:
            return merged_data
        
        # """ STEP 3: Align indices and columns """
        # Get common columns (excluding time columns already filtered out in the mask)
        common_cols = [col for col in y_true.columns if col in merged_data.columns 
                      and col in non_null_mask.columns and non_null_mask[col].any()]
        
        if not common_cols:
            return merged_data
        
        # """ STEP 4: Vectorized update of values where mask is True """
        for col in common_cols:
            col_mask = non_null_mask[col]
            # Only update where mask is True
            merged_data.loc[col_mask, col] = y_true.loc[col_mask, col]
        
        return merged_data


    def _transform_data(self, y_true_merged: pd.DataFrame, y_pred: pd.DataFrame,
                       required_cols: List[str], transformer) -> T.Tuple[pd.DataFrame, pd.DataFrame]:

        """
        We transform the data by scaler as a form of weighting. 
        We weigh the data by yransform both datasets using the surrogate model's scaler. This is typically a z_scaler

        Args:
            y_true_merged: DataFrame with merged actual values
            y_pred: DataFrame with predicted values
            required_cols: List of required column names
            transformer: Data transformer from surrogate model

        Returns:
            Tuple of transformed DataFrames
        """
        # Filter to required columns
        y_pred_filtered = y_pred[required_cols]
        y_true_filtered = y_true_merged[required_cols]

        # Transform through data transformer with fill_missing_timesteps=True to handle sparse data
        y_true_array = transformer.transform(df_x=None, df_y=y_true_filtered, fill_missing_timesteps=True)[1]
        y_pred_array = transformer.transform(df_x=None, df_y=y_pred_filtered, fill_missing_timesteps=True)[1]

        # Convert arrays back to dataframes
        y_true_df = transformer._reshape_array2df(y_true_array, is_x=False)
        y_pred_df = transformer._reshape_array2df(y_pred_array, is_x=False)

        # Verify shapes match
        if y_pred_df.shape != y_true_df.shape:
            raise ValueError(f"Shape mismatch after transformation: y_pred {y_pred_df.shape} vs y_true {y_true_df.shape}")

        return y_true_df, y_pred_df

    def _extract_comparison_data(self, y_true_df: pd.DataFrame, y_pred_df: pd.DataFrame,
                               non_null_mask: pd.DataFrame) -> pd.DataFrame:
        """
        Extract comparison data for positions where actual measurements exist using vectorized operations.
        
        This method prevents signal dilution by only including actual measurements in metric calculations.
        It aligns columns between DataFrames and excludes time-related columns before extraction.
        
        Important implementation details:
        1. The method flattens the multi-dimensional structure, treating all cells as independent observations
        2. The result is a 2-column DataFrame with no preservation of the original row/column structure
        3. Each row in the result represents a single measurement point from any position in the original data
        
        Args:
            y_true_df: Transformed DataFrame with ground truth values
            y_pred_df: Transformed DataFrame with predicted values
            non_null_mask: Boolean DataFrame where True indicates positions with actual measurements
        
        Returns:
            DataFrame with columns ["True Values", "Predicted Values"] containing flattened measurement pairs
        """
        transformer = self.surrogate_model.datatransformer
        
        # Cols
        time_cols = [transformer.timeset_col, transformer.timestep_col] if transformer.is_timeseries else []
        feature_cols = [col for col in y_true_df.columns if col not in time_cols]
        
        # Ensure both DataFrames have the same feature columns
        common_cols = [col for col in feature_cols if col in y_pred_df.columns and col in non_null_mask.columns]
        
        if not common_cols:
            raise ValueError("No common columns found between y_true, y_pred, and non_null_mask")
        
        # Filter DataFrames and mask to common feature columns
        y_true_filtered = y_true_df[common_cols]
        y_pred_filtered = y_pred_df[common_cols]
        mask_filtered = non_null_mask[common_cols]

        # Ensure all DataFrames have the same shape
        min_rows = min(len(y_true_filtered), len(y_pred_filtered), len(mask_filtered))
        y_true_filtered = y_true_filtered.iloc[:min_rows]
        y_pred_filtered = y_pred_filtered.iloc[:min_rows]
        mask_filtered = mask_filtered.iloc[:min_rows]

        # Convert to NumPy arrays for faster processing
        y_true_array = y_true_filtered.values
        y_pred_array = y_pred_filtered.values
        mask_array = mask_filtered.values

        # Extract values where mask is True using boolean indexing
        true_values = y_true_array[mask_array]
        pred_values = y_pred_array[mask_array]
        
        # Create result DataFrame
        return pd.DataFrame({
            "True Values": true_values,
            "Predicted Values": pred_values
        })




    
    def _calculate_mse(self, y_true_data: T.Union[pd.Series, pd.DataFrame], y_pred_data: T.Union[pd.Series, pd.DataFrame]):
        """Calculate Mean Squared Error, handling multiple columns."""
        
        # Handle multiple columns by calculating metric for each and averaging
        if isinstance(y_true_data, pd.DataFrame) and y_true_data.shape[1] > 1:
            return float(np.mean([metrics.mean_squared_error(y_true_data[col], y_pred_data[col]) 
                                  for col in y_true_data.columns])) # type: ignore
        
        return float(metrics.mean_squared_error(y_true_data, y_pred_data))

    def _calculate_mae(self, y_true_data: T.Union[pd.Series, pd.DataFrame], y_pred_data: T.Union[pd.Series, pd.DataFrame]) -> float:
        """Calculate Mean Absolute Error, handling multiple columns."""
        # Handle multiple columns by calculating metric for each and averaging
        if isinstance(y_true_data, pd.DataFrame) and y_true_data.shape[1] > 1:
            return float(np.mean([metrics.mean_absolute_error(y_true_data[col], y_pred_data[col]) 
                                for col in y_true_data.columns])) # type: ignore
        
        return float(metrics.mean_absolute_error(y_true_data, y_pred_data))
    
    def _calculate_rmse(self, y_true_data: T.Union[pd.Series, pd.DataFrame], y_pred_data: T.Union[pd.Series, pd.DataFrame]) -> float:
        """Calculate Root Mean Squared Error, handling multiple columns."""
        # Handle multiple columns by calculating metric for each and averaging
        if isinstance(y_true_data, pd.DataFrame) and y_true_data.shape[1] > 1:
            return float(np.mean([np.sqrt(metrics.mean_squared_error(y_true_data[col], y_pred_data[col])) 
                                for col in y_true_data.columns]))
        
        return float(np.sqrt(metrics.mean_squared_error(y_true_data, y_pred_data)))
    
    def _calculate_r2(self, y_true_data: T.Union[pd.Series, pd.DataFrame], y_pred_data: T.Union[pd.Series, pd.DataFrame]) -> float:
        """Calculate R² Score, handling multiple columns."""
        # Handle multiple columns by calculating metric for each and averaging
        if isinstance(y_true_data, pd.DataFrame) and y_true_data.shape[1] > 1:
            return float(np.mean([metrics.r2_score(y_true_data[col], y_pred_data[col]) 
                                for col in y_true_data.columns])) # type: ignore
        
        return float(metrics.r2_score(y_true_data, y_pred_data))
    
    def _calculate_mape(self, y_true_data: T.Union[pd.Series, pd.DataFrame], y_pred_data: T.Union[pd.Series, pd.DataFrame]) -> float:
        """Calculate Mean Absolute Percentage Error, handling multiple columns."""
        # Get time column names for grouping (not used in calculation, but could be for future grouping)
        time_cols = []

        if self.surrogate_model.datatransformer is not None:
            dt = self.surrogate_model.datatransformer
            try:
                if dt.timeset_col is not None:
                    time_cols.append(dt.timeset_col)
            except:
                pass
        
        if isinstance(y_true_data, pd.DataFrame):
            # Handle multiple columns
            results = []
            for col in y_true_data.columns:
                # Extract column values
                true_vals = y_true_data[col].values
                pred_vals = y_pred_data[col].values
                
                # Avoid division by zero
                mask = true_vals != 0
                if mask.sum() > 0: # type: ignore
                    mape = np.mean(np.abs((true_vals[mask] - pred_vals[mask]) / true_vals[mask])) * 100
                    results.append(mape)
            
            return float(np.mean(results)) if results else float('nan')
        
        # Handle single array case
        mask = y_true_data != 0
        if mask.sum() > 0:
            return float(np.mean(np.abs((y_true_data[mask] - y_pred_data[mask]) / y_true_data[mask])) * 100)
        return float('nan')

    def prepare_x_data(self, candidate_parameters: Dict[str, float]) -> pd.DataFrame:
        """
        Extend the dataframe with candidate parmeters, making a full X df. 
        Values to be same across all rows
        """
        df = self.df_x_partial.copy()
        df = df.assign(**candidate_parameters)

        return df

    def __call__(
            self,
            parameters: Dict[str, Any],
            optimization_problem: VOOptimizationProblem
    ) -> Dict[EnumMetrics, float]:
        """
        Calculate metrics using surrogate model predictions and experimental data
        """

        # Assemble full X data with optimization parameters
        df_x_full = self.prepare_x_data(parameters)
        metrics_to_compute = optimization_problem.metrics
        result = {}

        # Compute predictions once
        df_y_pred = self.surrogate_model.predict(df_x_full, fill_missing_timesteps=True)

        # Prepare data once using the new implementation
        y_true_data, y_pred_data = self.prepare_y_data(self.df_y_true, df_y_pred)

        # Calculate each requested metric using the prepared data
        for metric in metrics_to_compute:
            metric_label = metric.label
            if metric_label in self.calculator_registry:
                # Pass the prepared data to each calculator
                result[metric_label] = self.calculator_registry[metric_label](y_true_data, y_pred_data)

        return result


