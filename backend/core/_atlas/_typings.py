from datetime import datetime
from typing import TYPE_CHECKING, Any, Callable, Iterable, Tuple, Union

if TYPE_CHECKING:
    from ._singletons import (
        DiscreteSetSpecEnum,
        ContVarSpecEnum,
    )
    from .valueobjects import VODiscreteVariable

####################

NumericType = Union[float, int]
NumericOrIterableType = Union[NumericType, Iterable[NumericType]]
ValueTupleType = Tuple[Any, str, datetime]
_PolicyFunctionProtocol = Callable[
    ...,
    Tuple[Any, bool],
]  # All Classmethod signatures should match this
