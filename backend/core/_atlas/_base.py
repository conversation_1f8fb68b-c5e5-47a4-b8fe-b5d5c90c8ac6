"""

"""

from __future__ import annotations

import inspect
import uuid
import logging
from typing import (
    Iterator,
    Set,
    TypeVar,
    Generic,
    Dict,
    Type,
    List,
    Union,
    Protocol,
    Optional,
    Any,
    Callable,
    overload,
)
from uuid import UUID
import datetime
import abc
from pydantic import BaseModel, Field


class UUIDBase:
    """
    A base class that provides a UUID attribute for subclasses.

    The `UUIDBase` class is a base class that provides a UUID attribute for subclasses. If a `uuid_str` keyword argument is provided to the constructor, the UUID is initialized from that string. Otherwise, a new UUID is generated.

    The `uuid` property returns the UUID associated with the instance.
    """

    def __init__(self, **kwargs):
        """
        Initializes a new instance of the `UUIDBase` class.

        *DEPRECATED*
        If a `uuid_str` keyword argument is provided, the UUID is initialized from that string. Otherwise, a new UUID is generated.

        The `uuid` property returns the UUID associated with the instance.
        """
        uuid_str = kwargs.pop("uuid_str", None)
        if uuid_str:
            logging.warning(
                f"This is a deprecated kwarg. Consider refactoring and reassigning manually to _uid"
            )
            self._uid = uuid.UUID(uuid_str)
        else:
            self._uid = uuid.uuid4()

    @property
    def uid(self) -> uuid.UUID:
        return self._uid


class UUIDPydanticBase(BaseModel):
    """
    A Pydantic base model that provides a UUID field for subclasses.

    The `UUIDPydanticBase` class is a base class that provides a UUID field using Pydantic's
    validation and serialization capabilities. The UUID is automatically generated using
    uuid4() when a new instance is created.

    Attributes:
        uid (UUID): A unique identifier for the instance, automatically generated using uuid4()
    """

    uid: UUID = Field(..., default_factory=uuid.uuid4)


####################


# class CollectionKeyProtocol(Protocol):
#     @property
#     def collection_key(self) -> Any:
#         """REturns the key for collection storage"""


K = TypeVar("K")  # Key type
V = TypeVar("V", bound=Union[UUIDBase, BaseModel])  # Value type


class AbstractCollection(Generic[K, V], abc.ABC):
    """Registry for managing collections of value objects with unique identifiers.

    The collection provides type-safe storage and retrieval of value objects, using their
    enum types for dynamic routing to appropriate specialized collections.

    Type Parameters:
        K: Key type used for storage in collection dictionary
        V: Value type that implements HasCollectionKey protocol
        E: Enum type used for collection routing and type identification

    Collection Mapping:
        Collections declare which enum types (E) they handle, allowing the parent entity
        to route value objects to the correct collection based on their enum type.

    Example:
        class PropertyCollection(AbstractCollection[UUID, VOContinuousVariable, PropertyLabelEnum]):
            # Handles continuous variables with PropertyLabelEnum types
            pass

        class ParameterCollection(AbstractCollection[UUID, VOContinuousVariable, ParameterLabelEnum]):
            # Handles continuous variables with ParameterLabelEnum types
            pass

    The collection map in entities uses E to route value objects to these specialized collections.

    TODO consider breaking this into collections with and without bounds. also move up validation here.
    """

    def __init__(self):
        self._collection: Dict[K, V] = {}

    def __contains__(self, key: K) -> bool:
        return key in self._collection

    # COLLECTION MANAGMENT

    @abc.abstractmethod
    def _get_key(self, item: V) -> K:
        """Defines how to derive a key from an item"""
        # return item.uuid
        raise NotImplementedError()

    @property
    def items(self) -> List[V]:
        return list(self._collection.values())

    @property
    def collection(self):
        return self._collection

    def add_item(self, item: V) -> None:
        self._collection[self._get_key(item)] = item

    def get_item(self, key: K) -> V:
        return self._collection[key]

    def remove_item(self, item: Union[K, V]) -> bool:
        """
        Removes an item from the collection, either by its key or by its value.

        If the provided `item` is a key in the collection, it will be removed from the collection.
        If the provided `item` is a value in the collection, the corresponding key will be removed.
        """
        try:
            # if item is a key
            if item in self._collection:
                self._collection.pop(item)  # type: ignore

            # if item is a value
            _key = self._get_key(item)  # type: ignore
            self._collection.pop(_key)

            return True

        except Exception as e:
            logging.info(
                f"Item not found in collection. Nothing removed. Collection: {self.__class__.__name__}, item: {item}. e: {e}"
            )
            return False

    # Type Generics
    @property
    def keytype(self) -> Type:
        """Get the type of the key"""
        return self.__orig_bases__[0].__args__[0]  # type: ignore

    @property
    def itemtype(self) -> Type:
        """Returns the type of the item"""
        return self.__orig_bases__[0].__args__[1]  # type: ignore


"""

# NOTE: above can be refactored to more dict-like interface. Consider using Pydantic as a base so it gets better serialization support

class AbstractCollection(Generic[K, V], ABC):
    
    def __init__(self):
        self._items: Dict[K, V] = {}

    # Dict-like Protocol
    def __getitem__(self, key: K) -> V:
        return self._items[key]
        
    def __setitem__(self, key: K, value: V) -> None:
        if not isinstance(value, self.itemtype):
            raise TypeError(f"Value must be {self.itemtype}")
        if key != self._get_key(value):
            raise ValueError("Key must match item's derived key")
        self._items[key] = value
        
    def __delitem__(self, key: K) -> None:
        del self._items[key]
        
    def __iter__(self):
        return iter(self._items.values())
        
    def __len__(self):
        return len(self._items)
        
    def __contains__(self, key: K) -> bool:
        return key in self._items

    # Collection Methods    
    @abstractmethod
    def _get_key(self, item: V) -> K:
        raise NotImplementedError
        
    def keys(self) -> List[K]:
        return list(self._items.keys())
        
    def values(self) -> List[V]:
        return list(self._items.values())
        
    def items(self) -> List[Tuple[K, V]]:
        return list(self._items.items())
        
    def clear(self) -> None:
        self._items.clear()
        
    def update(self, other: Dict[K, V]) -> None:
        for k, v in other.items():
            self[k] = v
            
    # Legacy API Support
    def add_item(self, item: V) -> None:
        self[self._get_key(item)] = item
        
    def get_item(self, key: K) -> V:
        return self[key]
        
    def remove_item(self, item: Union[K, V]) -> bool:
        try:
            key = item if isinstance(item, self.keytype) else self._get_key(item)
            del self[key]
            return True
        except KeyError:
            return False

"""


T = TypeVar("T")


class TypedSet(Generic[T]):
    """A set that only accepts items of a specific type or subclass."""

    def __init__(self, base_class: Type[T]):
        self._base_class = base_class
        self._items: Set[Union[T, Type[T]]] = set()

    def add(self, item: Union[T, Type[T]]) -> None:
        """
        Add an item to the set if it's either:
        - An instance of the base class
        - A class that is a subclass of the base class
        """
        if inspect.isclass(item):
            # Item is a class/type
            if not issubclass(item, self._base_class):
                raise TypeError(
                    f"Class must be a subclass of {self._base_class.__name__}"
                )
        else:
            # Item is an instance
            if not isinstance(item, self._base_class):
                raise TypeError(
                    f"Item must be an instance of {self._base_class.__name__}"
                )
        self._items.add(item)

    def remove(self, item: Any) -> None:
        """Remove an item from the set."""
        self._items.remove(item)

    def __contains__(self, item: Any) -> bool:
        """Check if an item is in the set."""
        return item in self._items

    def __iter__(self) -> Iterator[Union[T, Type[T]]]:
        """Return an iterator over the items in the set."""
        return iter(self._items)

    def __len__(self) -> int:
        """Return the number of items in the set."""
        return len(self._items)

    def to_list(self) -> List[Union[T, Type[T]]]:
        """Convert the set to a list of items."""
        if self.is_instance_only():
            return list(self._items)  # Will return List[T]
        elif self.is_class_only():
            return list(self._items)  # Will return List[Type[T]]
        else:
            return list(self._items)  # Will return List[Union[T, Type[T]]]

    def is_instance_only(self) -> bool:
        """Check if this set contains only instances (no classes)."""
        return all(not inspect.isclass(item) for item in self._items)

    def is_class_only(self) -> bool:
        """Check if this set contains only classes (no instances)."""
        return all(inspect.isclass(item) for item in self._items)
