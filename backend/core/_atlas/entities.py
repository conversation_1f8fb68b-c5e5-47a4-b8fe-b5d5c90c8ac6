from __future__ import annotations
from ._imports import *

from .valueobjects import *
from .valueobjects import _ContextAwarePolicies, _ContextFreePolicies

# Hack to avoid circular. Ideal would be to remove entity awareness of aggregate
if TYPE_CHECKING:
    from .aggregates import AtlasRoot

####################
MAX_SAFE_INTEGER = 1e18
MIN_SAFE_INTEGER = -MAX_SAFE_INTEGER

class AbstractVariableCollection(AbstractCollection[K, V]):
    """Base class for variable collections that require value and bounds management"""

    # DELEGATED METHODS - VAL
    @abc.abstractmethod
    def _get_value(self, item: V):
        """Abstract method to decouple item from method to get its value"""
        raise NotImplementedError()

    def get_value(self, key: K):
        """Delegated method"""
        item = self.get_item(key)
        return self._get_value(item)

    def _set_value(self, item: V, value: Any, *, override_all_policies=False, **kwargs):
        """Abstract method to decouple item from method to set its value"""
        raise NotImplementedError()

    def set_value(self, key: K, value: Any, **kwargs):
        """Set the value of an item"""
        item = self.get_item(key)
        self._set_value(item, value, **kwargs)

    # DELEGATED METHODS - BOUNDS
    @abc.abstractmethod
    def _get_bounds(self, item: V):
        """Abstract method to decouple item from method to get its bounds"""
        raise NotImplementedError()

    def get_bounds(self, key: K):
        """Get the bounds of an item"""
        item = self.get_item(key)
        return self._get_bounds(item)

    @abc.abstractmethod
    def _set_bounds(self, item: V, bounds: Any):
        """Abstract method to decouple item from method to set its bounds"""
        raise NotImplementedError()

    def set_bounds(self, key: K, bounds: Any):
        """Set the bounds of an item"""
        item = self.get_item(key)
        self._set_bounds(item, bounds)


class VarCollectionContinuous(
    AbstractVariableCollection[ContVarSpecEnum, VOContinuousVariable]
):

    def _get_key(self, item):
        key = item.variable_enum
        assert isinstance(key, ContVarSpecEnum)
        return key

    # BOUNDS

    def _get_bounds(self, item: VOContinuousVariable):
        return item.bounds

    def _set_bounds(self, item: VOContinuousVariable, bounds: Any):
        item.bounds = bounds

    # VALUE

    def _set_value(
        self,
        item: VOContinuousVariable,
        value: Any,
        *,
        override_all_policies=False,
        **kwargs,
    ):
        item.set_value(value, override_all_policies=override_all_policies, **kwargs)

    def _get_value(self, item: VOContinuousVariable):
        return item.value


class VarCollectionDiscreteSet(
    AbstractVariableCollection[DiscreteSetSpecEnum, VODiscreteVariable]
):
    def _get_key(self, item):
        return item.variable_enum

    # BOUNDS

    def _get_bounds(self, item: VODiscreteVariable):
        return item.bounds

    def _set_bounds(self, item: VODiscreteVariable, bounds: Any):
        item.bounds = bounds

    # VALUE
    def _set_value(
        self,
        item: VODiscreteVariable,
        value: Any,
        *,
        override_all_policies=False,
        **kwargs,
    ):
        item.set_value(value, override_all_policies=override_all_policies, **kwargs)

    def _get_value(self, item: VODiscreteVariable):
        return item.value


# class VarCollectionSplitter(
#     AbstractVariableCollection[SplitStreamSpecEnum, VOContinuousVariable]
# ):

#     def _get_key(self, item):
#         key = item.variable_enum
#         assert isinstance(key, SplitStreamSpecEnum)
#         return key

#     # BOUNDS

#     def _get_bounds(self, item: VOContinuousVariable):
#         return item.bounds

#     def _set_bounds(self, item: VOContinuousVariable, bounds: Any):
#         item.bounds = bounds

#     # VALUE

#     def _set_value(self, item: VOContinuousVariable, value: Any, **kwargs):
#         item.set_value(value, **kwargs)

#     def _get_value(self, item: VOContinuousVariable):
#         return item.value

#     def _validate(self):
#         total = 0
#         for var in self.items:
#             val = var.value
#             assert isinstance(val, (float, int))
#             total += val

#         print(total)
#         return math.isclose(total, 1.0)


####################

# REGISTRY


# TCollection = TypeVar("TCollection", bound=_AbstractVariableCollection)


class _VariableCollectionRouter:

    def __init__(self, collections: List[Type[AbstractVariableCollection]]):
        self._registry: List[AbstractVariableCollection] = [
            collection() for collection in collections
        ]

    def add_collection(self, collection: AbstractVariableCollection):
        """
        Adds a collection to the registry.

        Checks if collection's itemtype already exists. If it does, will raise an error
        """
        if collection in self._registry:
            raise ValueError(f"collection already in registry: {self._registry}")
        self._registry.append(collection)

    def get_collection(
        self,
        *,
        collection_type: Optional[Type[AbstractVariableCollection]] = None,
        variable_type: Optional[Type[VOBaseVariable]] = None,
        key_type: Optional[Union[Type[VOBase], Type[BaseSpecEnum]]] = None,
    ) -> AbstractVariableCollection:
        """Get collection through polymorphic reference types.

        Routes to appropriate collection through:
        1. Collection type (e.g. VarCollectionContinuous)
        2. Variable instance (e.g. )
        4. Key type (VOCompound)
        ??
        """

        collection = None
        if collection_type is not None:
            collection = next(
                collection
                for collection in self._registry
                if isinstance(collection, collection_type)
            )

        elif variable_type is not None:
            collection = next(
                collection
                for collection in self._registry
                if collection.itemtype == variable_type
            )
        elif key_type is not None:
            collection = next(
                collection
                for collection in self._registry
                if collection.keytype == key_type
            )
        else:
            raise AttributeError(f"keyword argument needed for collection")

        if collection is None:
            raise KeyError(
                f"Collection not found for given reference: {collection_type}, {variable_type}, {key_type}"
            )

        return collection

    def get_collections(
        self, filter: Optional[List[Type[AbstractVariableCollection]]] = None
    ) -> List[AbstractVariableCollection]:
        """Returns collections."""
        if filter is None:
            filter = [type(collection) for collection in self._registry]

        return [
            collection for collection in self._registry if type(collection) in filter
        ]

    def add_variable(self, var: Any):
        collection = self.get_collection(variable_type=type(var))
        collection.add_item(var)

    def get_variable(self, var_key: Any) -> VOBaseVariable:
        collection = self.get_collection(key_type=type(var_key))
        return collection.get_item(var_key)


class ENTBase(UUIDBase, StrMixin):
    DISCRETESET_DEFAULTS: Set[VODiscreteVariable] = set()
    CONTVAR_DEFAULTS: Set[VOContinuousVariable] = set()
    CONTVAR_DEFAULTS: Set[VOContinuousVariable] = set()
    COLLECTION_DEFAULTS: List[Type[AbstractVariableCollection]] = [
        VarCollectionDiscreteSet,
        VarCollectionContinuous,
    ]

    def __init__(
        self,
        label: str,
        parent_pmodel: Optional["AtlasRoot"] = None,
        *,
        init_collections_from_class_variables: bool = True,
        collections: Optional[List[Type[AbstractVariableCollection]]] = None,
        **kwargs,
    ):
        super().__init__(**kwargs)
        self._label = label
        self._parent_pmodel = parent_pmodel
        self._collections: _VariableCollectionRouter = _VariableCollectionRouter(
            collections or self.__class__.COLLECTION_DEFAULTS
        )
        self._description = ""
        if init_collections_from_class_variables == True:
            self._init_default_variables()
            self._init_discrete_selections()

    @staticmethod
    def _is_label_unique(label: str, parent: Optional["AtlasRoot"] = None):
        """
        Checks if the given ID is unique within the parent plant.
        """
        if parent is None:
            return True

        return not any(label == e for e in parent.equipments) and not any(
            label == e for e in parent.streams
        )

    @property
    def description(self) -> str:
        return self._description

    @description.setter
    def description(self, value: str):
        self._description = value

    @classmethod
    def get_entity_type(cls) -> str:
        return cls.__name__

    @property
    def entity_type(self) -> str:
        """entity type, driven by class method"""
        return self.__class__.get_entity_type()

    @property
    def label(self):
        return self._label

    @label.setter
    def label(self, value: str):

        # LOGIC
        if not self._is_label_unique(value, parent=self.parent_obj):
            raise ValueError(f"ID {value} is not unique. Please choose a unique ID.")
        self._label

    # FIXME - remove parent. If anything form parent needed, it should be managed at parent level
    @property
    def parent_obj(self) -> Optional[AtlasRoot]:
        return self._parent_pmodel

    @parent_obj.setter
    def parent_obj(self, obj: AtlasRoot):
        """
        Sets the parent plant aggregate for this equipment or stream entity.

        Args:
            value (PlantAggregate): The parent plant aggregate for this entity.
        """
        # Set parent
        self._parent_pmodel = obj

    # ENTITY DEFINITIONS

    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self._label})>"

    def __hash__(self) -> int:
        # Use the item_id for the hash implementation
        return hash(self.label)

    def __eq__(self, other: object) -> bool:
        # Ensure other is an instance of EquipmentEntity and compare item_id
        if not isinstance(other, ENTBase):
            return False
        return self.label == other.label

    def _init_default_variables(self):
        """Dynamically get all class-variables that are sets of ValueObjects.

        Expects class var to be of type Set[VOBaseVariable].
        """

        # Get all class vars that are sets containing VOBase
        defaults = {
            name: value
            for name, value in vars(self.__class__).items()
            if isinstance(value, set)
            and all(isinstance(x, VOBaseVariable) for x in value)
        }

        # Add to collection
        for name, var_set in defaults.items():
            for var in var_set:
                # Create deep copy and assign self as parent
                var_copy = copy.deepcopy(var)
                self.add_variable(var_copy)  # type: ignore

            logging.info(f"`{self.label}`: initialized variables for `{name}`")

    def _init_discrete_selections(self) -> None:
        """Initialize discrete selections with policy override.
        This is necessary to execute the policy-chain toggles"""

        if not hasattr(self, "DISCRETESET_DEFAULTS"):
            return

        # get toggle policy reference
        toggle_policy = _ContextAwarePolicies.toggle_indep_on_setval

        for discrete in self.DISCRETESET_DEFAULTS:
            # Defensive
            if discrete is None:
                continue

            var = self.get_variable(discrete.variable_enum)

            # Swap out policies
            original_policies = var.policies
            toggle_policies = (
                [toggle_policy] if toggle_policy in original_policies else []
            )

            # Set indep toggles, then reassign original policies
            try:
                var.policies = toggle_policies  # type: ignore
                var.set_value(discrete.value)
            finally:
                var.policies = original_policies

    # COLLECTIONS

    def get_collection(self, collection_type: Type[AbstractVariableCollection]):
        return self._collections.get_collection(collection_type=collection_type)

    def get_collections(
        self, filter: Optional[List[Type[AbstractVariableCollection]]] = None
    ):
        return self._collections.get_collections(filter)

    # GENERAL VARIABLE METHODS

    def add_variable(
        self,
        var: VOBaseVariable,
    ):
        """
        SINGLE ENTRY POINT FOR VARIABLE ASSOCIATION.
        Manages parent associations and collection membership.

        - Ensures entity is parent reference to vo (for deterministic UUID)
        - Adds a variable to its relevant collection (e.g. CompoudMassColletion, etc)
        - Also updates the aggregate-level uid registry with the var if there is a parent

        Args
            - var: variable object to add
            - collections: Optional list of collection objects to register the variable in
        """

        # Ensure variable has parent reference (for deterministic UUID)

        var.parent = self
        # HACK - Do not know why uuids are not created unique. redoing it here.
        var._uid = uuid.uuid4()

        # Register in VarCollections
        self._collections.get_collection(variable_type=type(var)).add_item(var)
        # logging.info(f"{var} added to collections")

        # Register in UIDCollections
        if self.parent_obj is not None:
            self.parent_obj.variables_collection.add_item(var)

    def get_variable(
        self, reference: Union[Any, DiscreteItemSpecEnum]
    ) -> Optional[VOBaseVariable[Any, Any, Any]]:
        """Reference is either a collection key or sample DiscreteItemEnum. DiscreteItemEnum assumes that it is part of DiscreteSetCollection"""

        # Case 1: variable retreival by item
        if isinstance(reference, DiscreteItemSpecEnum):
            collection = self._collections.get_collection(
                collection_type=VarCollectionDiscreteSet
            )
            return next(var for var in collection.items if reference in var.bounds)

        # Case 2: Variable retreival by key
        else:
            return self._collections.get_collection(key_type=type(reference)).get_item(
                reference
            )

    def remove_variable(self, variable: VOBaseVariable):
        """Remove variable from entity-level and aggregate-level collections"""
        self._collections.get_collection(variable_type=type(variable)).remove_item(
            variable
        )

    @overload  # Base Case
    def get_variables(
        self,
        *,
        filter: Optional[List[Type[AbstractVariableCollection]]] = None,
    ) -> List[VOBaseVariable]: ...

    @overload
    def get_variables(
        self,
        *,
        filter: Optional[List[Type[AbstractVariableCollection]]] = None,
        as_dict: Literal[False],
    ) -> List[VOBaseVariable]: ...

    @overload
    def get_variables(
        self,
        *,
        filter: Optional[List[Type[AbstractVariableCollection]]] = None,
        as_dict: Literal[True],
    ) -> Dict[Any, VOBaseVariable]: ...

    def get_variables(
        self,
        *,
        filter: Optional[List[Type[AbstractVariableCollection]]] = None,
        as_dict: bool = False,
    ) -> Union[List[VOBaseVariable], Dict[Any, VOBaseVariable]]:
        """
        Get all variables associated with the entity, optionally filtered by type.

        Args:
            filter: A list of variable types to filter by. If provided, only variables of these types will be returned.
            as_dict: If True, returns a dictionary mapping keys to variables. If False, returns a list of variables.

        Returns:
            Either a list of variables or a dictionary of variables, depending on as_dict parameter.
        """
        # Get collections using registry method
        collections = self._collections.get_collections(filter=filter)

        variables = {}
        for collection in collections:
            for item in collection.items:
                variables[collection._get_key(item)] = item

        if not as_dict:
            return list(variables.values())

        return variables

    # DELEGATED VALUE METHODS

    def set_value(
        self, variable_key: Any, value: Any, *, override_all_policies=False, **kwargs
    ):
        """General method to set value of variables.

        Expectations:
        - variable key is unique. (e.g. if VOCompound is a key to CompoundMix, that is all it can key to)
        - NOTE - alternative is that we do tuple-pairs of collection-key to retreive, but hope is to abstract away collections for simplicity
        - variables are all subclassed from `VOBaseVariable`, hence have set_value method or we route it 100% through collections

        Args:
            variable_key: in general, this is a VariableEnum
            value: the value associated with the variable

        Kwargs: passes on to var._set_value
            - apply_contextaware_policies: bool = True,
            - override_all_policies: bool = False
        """
        try:
            collection = self._collections.get_collection(key_type=type(variable_key))
            collection.set_value(
                variable_key,
                value,
                override_all_policies=override_all_policies,
                **kwargs,
            )

            # logging.info(
            #     f"{sharedutils.get_function_name()}- equipment/stream id: {self.label} - Set value of `{variable_key}` to `{value}`. "
            # )
        except Exception as e:
            raise ValueError(
                f"{self.label} - Error setting value of `{variable_key}` to `{value}`. \nError message: {e}"
            )

    def get_value(self, variable_key: Any):
        collection = self._collections.get_collection(key_type=type(variable_key))
        return collection.get_value(variable_key)

    # DELEGATED BOUNDS METHODS. To be done via collections interface.

    def set_bounds(self, variable_key: Any, bounds: Any):
        try:
            collection = self._collections.get_collection(key_type=type(variable_key))
            collection.set_bounds(variable_key, bounds)

            # logging.info(
            #     f"{sharedutils.get_function_name()}- equipment/stream id: {self.label} - Set bounds of `{variable_key}` to `{bounds}`. "
            # )
        except Exception as e:
            raise ValueError(
                f"{self.label} - Error setting bounds of `{variable_key}` to `{bounds}`. \nError message: {e}"
            )

    def get_bounds(self, variable_key: Any):
        try:
            collection = self._collections.get_collection(key_type=type(variable_key))
            return collection.get_bounds(variable_key)

        except Exception as e:
            raise ValueError(
                f"unable to get bounds of {self.label}.{variable_key}: {e}"
            )


####################


# STREAMS


class VarCollectionCompoundMassRatio(
    AbstractVariableCollection[VOCompound, VOCompoundMassRatio]
):
    def _get_key(self, item):
        return item.compound

    # BOUNDS
    def _get_bounds(self, item):
        return item.bounds

    def _set_bounds(self, item, bounds: Any):
        item.bounds = bounds

    # VALUE
    def set_value(
        self, key: VOCompound, value: float, *, override_all_policies=False, **kwargs
    ):
        """Set mass ratio for compound, create if doesn't exist"""
        try:
            # Try get existing
            item = self.get_item(key)
            item.set_value(value, override_all_policies=override_all_policies, **kwargs)
        except KeyError:
            # Create new ratio and add
            new_ratio = VOCompoundMassRatio(key, value)
            self.add_item(new_ratio)

    def _get_value(self, item):
        """Get the compound mass ratio of the item"""
        return item.value

    # Other Values
    @property
    def compounds(self):
        """REturns a list of all compounds"""
        return [var.compound for var in self.items if var.value is not None]

    @property
    def compoundmassmix(self) -> Dict[VOCompound, float]:
        return {k: v.value for k, v in self.collection.items()}

    @compounds.setter
    def compounds(self, compounds: list[VOCompound]):
        # Drop all items not in list
        for compound in set(self.compounds).difference(set(compounds)):
            item = self.get_item(compound)
            if item is not None:
                self.remove_item(item)

        # Create new compounds
        for compound in set(compounds).difference(set(self.compounds)):
            var = VOCompoundMassRatio(compound, 0.0)
            self.add_item(var)

    def equalize(self):
        """Equalize mass ratio of compounds in collection"""
        count = len(self.items)
        for var in self.items:
            var.set_value(1 / count)

    def validate(self):
        total = sum(compound.value for compound in self.items)
        print(total)
        return math.isclose(total, 1.0)

    def purge_nulls(self, threshold: float = 0.0001):
        """Remove compounds with mix less than threshold (default: 0.0001)"""
        for k, v in self.compoundmassmix.items():
            if v > threshold:
                continue
            self.remove_item(k)


class ENTBaseStream(ENTBase):

    CONTVAR_DEFAULTS = {
        # SCALARS
        VOContinuousVariable(ContVarSpecEnum.Temperature, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.Pressure, 0, (0, 100_000_000.0)),
        # TODO - these cannot be found in dwsim API. Taken out for now
        # VOContinuousVariable(PropertyLabelEnum.MOLAR_ENTHALPY, 0, (0, 100_000_000)),
        # VOContinuousVariable(PropertyLabelEnum.MOLAR_ENTROPY, 0, (0, 100_000_000)),
        # NOTE - thhis is removed until we are able to solve getting vapor mole fraction from DWSIM or alternative endpoint
        # VOContinuousVariable(PropertyLabelEnum.VAPOR_MOLE_FRAC, 0, (0, 100_000_000)),
        # FLOWS
        VOContinuousVariable(ContVarSpecEnum.Mass_flow_rate, 10.0, (0, 100_000_000)),
        # VOContinuousVariable(ContVarSpecEnum.Molar_flow_rate, 10.0, (0, 100_000_000)),
        # VOContinuousVariable(
        #     ContVarSpecEnum.Volumetric_flow_rate, 10, (0, 100_000_000)
        # ),
        # COMPOUND FRACTIONS
        # VOContinuousVariable(PropertyLabelEnum.MASS_FRACTION, 0, (0, 1.0)),
        # VOContinuousVariable(PropertyLabelEnum.MOLE_FRACTION, 0, (0, 1.0)),
    }

    DISCRETESET_DEFAULTS = {
        # VODiscreteVariable(
        #     variable_enum=DiscreteSetSpecEnum.MaterialStreamBasis,
        #     init_value=DiscreteItemSpecEnum.MaterialStreamBasis_Mass,
        #     bounds={
        #         DiscreteItemSpecEnum.MaterialStreamBasis_Mass,
        #         # DiscreteItemEnum.MaterialStreamBasis_Mol,
        #     },
        #     # prerequisites_for_selection=({DiscreteItemEnum.MaterialStream_Input},),
        #     policies=[],
        # ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.FlashSpec,
            init_value=DiscreteItemSpecEnum.TemperatureAndPressure,
            bounds={
                DiscreteItemSpecEnum.TemperatureAndPressure,
                # NOTE - these are removed because we can't set vapor mole fract in dwsim endpoint. (technical)
                # DiscreteItemEnum.PressureAndEnthalphy,
                # DiscreteItemEnum.PressureAndEntropy,
                # DiscreteItemEnum.PressureaAndVaporFraction,
                # DiscreteItemEnum.TemperatureAndVaporFraction,
            },
            # prerequisites_for_selection=({DiscreteItemEnum.MaterialStream_Input},),
            policies=[
                _ContextFreePolicies.no_setval_on_oob_discrete,
                # _ContextFreePolicies.no_setval_on_dependent,
                _ContextAwarePolicies.no_setval_on_preqreq_not_met,
                _ContextAwarePolicies.toggle_indep_on_setval,
            ],
        ),
    }
    # Extend collection defaults to include compoundMix
    COLLECTION_DEFAULTS = [
        VarCollectionDiscreteSet,
        VarCollectionContinuous,
        VarCollectionCompoundMassRatio,
    ]

    def __init__(
        self,
        *args,
        compounds: Optional[List[VOCompound]] = None,
        # is_reckonable: bool = False,
        # value_per_kg: Optional[float] = None,
        # ef_co2: Optional[float] = None,
        # ef_ch4: Optional[float] = None,
        # ef_n2o: Optional[float] = None,
        # gwp_ch4: Optional[float] = None,
        # gwp_n2o: Optional[float] = None,
        # calorific_value: Optional[float] = None,
        **kwargs,
    ):
        super().__init__(*args, **kwargs)

        # ACCOUNTING DATA
        # TODO - move all the below into a Collection. To do that, new default VarEnum needed, possible AccountingVar?
        # self._is_reckonable = is_reckonable
        # self._value_per_kg = VOUnitPrimitive(
        #     value_per_kg, ContVarSpecEnum.Dollar_value_per_kg, parent_obj=self
        # )
        # self._ef_co2 = VOUnitPrimitive(ef_co2, ContVarSpecEnum.EF_CO2, parent_obj=self)
        # self._ef_ch4 = VOUnitPrimitive(ef_ch4, ContVarSpecEnum.EF_CH4, parent_obj=self)
        # self._ef_n2o = VOUnitPrimitive(ef_n2o, ContVarSpecEnum.EF_N2O, parent_obj=self)
        # self._gwp_ch4 = VOUnitPrimitive(
        #     gwp_ch4, ContVarSpecEnum.GWP_CH4, parent_obj=self
        # )
        # self._gwp_n2o = VOUnitPrimitive(
        #     gwp_n2o, ContVarSpecEnum.GWP_N2O, parent_obj=self
        # )
        # self._calorific_value = VOUnitPrimitive(
        #     calorific_value, ContVarSpecEnum.Calorific_Value, parent_obj=self
        # )
        if compounds is not None:
            self._init_compoundmix(compounds)

    # COMPOUNDMIX  UTILITIES
    def _init_compoundmix(self, compounds: List[VOCompound]):
        """Initializes compound mix with equal ratios"""
        val = 1 / len(compounds)
        for c in compounds:
            var = VOCompoundMassRatio(c, val)
            self.add_variable(var)

    @property
    def compoundmix(self) -> Dict[VOCompound, float]:
        collection = self.get_collections([VarCollectionCompoundMassRatio])[0]
        assert isinstance(collection, VarCollectionCompoundMassRatio)
        return collection.compoundmassmix

    @property
    def compounds(self) -> List[VOCompound]:
        """Returns list of compounds in mix"""
        collection = self._collections.get_collection(
            collection_type=VarCollectionCompoundMassRatio
        )
        assert isinstance(collection, VarCollectionCompoundMassRatio)
        return collection.compounds

    def equalize_compoundmassmix(self):
        collection = self._collections.get_collection(
            collection_type=VarCollectionCompoundMassRatio
        )
        assert isinstance(collection, VarCollectionCompoundMassRatio)
        collection.equalize()


class InputStream(ENTBaseStream):
    CONTVAR_DEFAULTS = {
        # SCALARS
        VOContinuousVariable(ContVarSpecEnum.Temperature, 300, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.Pressure, 101325, (0, 100_000_000.0)),
        # TODO - these cannot be found in dwsim API. Taken out for now
        # VOContinuousVariable(PropertyLabelEnum.MOLAR_ENTHALPY, 0, (0, 100_000_000)),
        # VOContinuousVariable(PropertyLabelEnum.MOLAR_ENTROPY, 0, (0, 100_000_000)),
        # NOTE - thhis is removed until we are able to solve getting vapor mole fraction from DWSIM or alternative endpoint
        # VOContinuousVariable(PropertyLabelEnum.VAPOR_MOLE_FRAC, 0, (0, 100_000_000)),
        # FLOWS
        VOContinuousVariable(ContVarSpecEnum.Mass_flow_rate, 10, (0, 100_000_000)),
        # VOContinuousVariable(ContVarSpecEnum.Molar_flow_rate, 10, (0, 100_000_000)),
        # VOContinuousVariable(
        #     ContVarSpecEnum.Volumetric_flow_rate, 10, (0, 100_000_000)
        # ),
        # COMPOUND FRACTIONS
        # VOContinuousVariable(PropertyLabelEnum.MASS_FRACTION, 0, (0, 1.0)),
        # VOContinuousVariable(PropertyLabelEnum.MOLE_FRACTION, 0, (0, 1.0)),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.MaterialStreamBasis,
            init_value=DiscreteItemSpecEnum.MaterialStreamBasis_Mass,
            bounds={
                DiscreteItemSpecEnum.MaterialStreamBasis_Mass,
                # DiscreteItemEnum.MaterialStreamBasis_Mol,
            },
            # prerequisites_for_selection=({DiscreteItemEnum.MaterialStream_Input},),
            policies=[],
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.FlashSpec,
            init_value=DiscreteItemSpecEnum.TemperatureAndPressure,
            bounds={
                DiscreteItemSpecEnum.TemperatureAndPressure,
                # NOTE - these are removed because we can't set vapor mole fract in dwsim endpoint. (technical)
                # DiscreteItemEnum.PressureAndEnthalphy,
                # DiscreteItemEnum.PressureAndEntropy,
                # DiscreteItemEnum.PressureaAndVaporFraction,
                # DiscreteItemEnum.TemperatureAndVaporFraction,
            },
            # prerequisites_for_selection=({DiscreteItemEnum.MaterialStream_Input},),
            policies=[
                _ContextFreePolicies.no_setval_on_oob_discrete,
                # _ContextFreePolicies.no_setval_on_dependent,
                _ContextAwarePolicies.no_setval_on_preqreq_not_met,
                _ContextAwarePolicies.toggle_indep_on_setval,
            ],
        ),
    }
    # Extend collection defaults to include compoundMix
    COLLECTION_DEFAULTS = [
        VarCollectionDiscreteSet,
        VarCollectionContinuous,
        VarCollectionCompoundMassRatio,
    ]

    def __init__(self, *args, compounds: List[VOCompound] | None = None, **kwargs):
        super().__init__(*args, compounds=compounds, **kwargs)


class OutputStream(ENTBaseStream):
    CONTVAR_DEFAULTS = {
        # SCALARS
        VOContinuousVariable(ContVarSpecEnum.Temperature, 300, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.Pressure, 101325, (0, 100_000_000.0)),
        # TODO - these cannot be found in dwsim API. Taken out for now
        # VOContinuousVariable(PropertyLabelEnum.MOLAR_ENTHALPY, 0, (0, 100_000_000)),
        # VOContinuousVariable(PropertyLabelEnum.MOLAR_ENTROPY, 0, (0, 100_000_000)),
        # NOTE - thhis is removed until we are able to solve getting vapor mole fraction from DWSIM or alternative endpoint
        # VOContinuousVariable(PropertyLabelEnum.VAPOR_MOLE_FRAC, 0, (0, 100_000_000)),
        # FLOWS
        VOContinuousVariable(ContVarSpecEnum.Mass_flow_rate, 10, (0, 100_000_000)),
        # VOContinuousVariable(ContVarSpecEnum.Molar_flow_rate, 10, (0, 100_000_000)),
        # VOContinuousVariable(
        #     ContVarSpecEnum.Volumetric_flow_rate, 10, (0, 100_000_000)
        # ),
        # COMPOUND FRACTIONS
        # VOContinuousVariable(PropertyLabelEnum.MASS_FRACTION, 0, (0, 1.0)),
        # VOContinuousVariable(PropertyLabelEnum.MOLE_FRACTION, 0, (0, 1.0)),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.MaterialStreamBasis,
            init_value=DiscreteItemSpecEnum.MaterialStreamBasis_Mass,
            bounds={
                DiscreteItemSpecEnum.MaterialStreamBasis_Mass,
                # DiscreteItemEnum.MaterialStreamBasis_Mol,
            },
            # prerequisites_for_selection=({DiscreteItemEnum.MaterialStream_Input},),
            policies=[],
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.FlashSpec,
            init_value=DiscreteItemSpecEnum.TemperatureAndPressure,
            bounds={
                DiscreteItemSpecEnum.TemperatureAndPressure,
                # NOTE - these are removed because we can't set vapor mole fract in dwsim endpoint. (technical)
                # DiscreteItemEnum.PressureAndEnthalphy,
                # DiscreteItemEnum.PressureAndEntropy,
                # DiscreteItemEnum.PressureaAndVaporFraction,
                # DiscreteItemEnum.TemperatureAndVaporFraction,
            },
            # prerequisites_for_selection=({DiscreteItemEnum.MaterialStream_Input},),
            policies=[
                _ContextFreePolicies.no_setval_on_oob_discrete,
                # _ContextFreePolicies.no_setval_on_dependent,
                _ContextAwarePolicies.no_setval_on_preqreq_not_met,
                _ContextAwarePolicies.toggle_indep_on_setval,
            ],
        ),
    }
    # Extend collection defaults to include compoundMix
    COLLECTION_DEFAULTS = [
        VarCollectionDiscreteSet,
        VarCollectionContinuous,
        VarCollectionCompoundMassRatio,
    ]

    def __init__(self, *args, compounds: List[VOCompound] | None = None, **kwargs):
        super().__init__(*args, compounds=compounds, **kwargs)


class MaterialStream(ENTBaseStream):
    CONTVAR_DEFAULTS = {
        # SCALARS
        VOContinuousVariable(ContVarSpecEnum.Temperature, 300, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.Pressure, 101325, (0, 100_000_000.0)),
        # TODO - these cannot be found in dwsim API. Taken out for now
        # VOContinuousVariable(PropertyLabelEnum.MOLAR_ENTHALPY, 0, (0, 100_000_000)),
        # VOContinuousVariable(PropertyLabelEnum.MOLAR_ENTROPY, 0, (0, 100_000_000)),
        # NOTE - thhis is removed until we are able to solve getting vapor mole fraction from DWSIM or alternative endpoint
        # VOContinuousVariable(PropertyLabelEnum.VAPOR_MOLE_FRAC, 0, (0, 100_000_000)),
        # FLOWS
        VOContinuousVariable(ContVarSpecEnum.Mass_flow_rate, 10, (0, 100_000_000)),
        # VOContinuousVariable(ContVarSpecEnum.Molar_flow_rate, 10, (0, 100_000_000)),
        # VOContinuousVariable(
        #     ContVarSpecEnum.Volumetric_flow_rate, 10, (0, 100_000_000)
        # ),
        # COMPOUND FRACTIONS
        # VOContinuousVariable(PropertyLabelEnum.MASS_FRACTION, 0, (0, 1.0)),
        # VOContinuousVariable(PropertyLabelEnum.MOLE_FRACTION, 0, (0, 1.0)),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.MaterialStreamBasis,
            init_value=DiscreteItemSpecEnum.MaterialStreamBasis_Mass,
            bounds={
                DiscreteItemSpecEnum.MaterialStreamBasis_Mass,
                # DiscreteItemEnum.MaterialStreamBasis_Mol,
            },
            # prerequisites_for_selection=({DiscreteItemEnum.MaterialStream_Input},),
            policies=[],
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.FlashSpec,
            init_value=DiscreteItemSpecEnum.TemperatureAndPressure,
            bounds={
                DiscreteItemSpecEnum.TemperatureAndPressure,
                # NOTE - these are removed because we can't set vapor mole fract in dwsim endpoint. (technical)
                # DiscreteItemEnum.PressureAndEnthalphy,
                # DiscreteItemEnum.PressureAndEntropy,
                # DiscreteItemEnum.PressureaAndVaporFraction,
                # DiscreteItemEnum.TemperatureAndVaporFraction,
            },
            # prerequisites_for_selection=({DiscreteItemEnum.MaterialStream_Input},),
            policies=[
                _ContextFreePolicies.no_setval_on_oob_discrete,
                # _ContextFreePolicies.no_setval_on_dependent,
                _ContextAwarePolicies.no_setval_on_preqreq_not_met,
                _ContextAwarePolicies.toggle_indep_on_setval,
            ],
        ),
    }
    # Extend collection defaults to include compoundMix
    COLLECTION_DEFAULTS = [
        VarCollectionDiscreteSet,
        VarCollectionContinuous,
        VarCollectionCompoundMassRatio,
    ]

    def __init__(self, *args, compounds: List[VOCompound] | None = None, **kwargs):
        super().__init__(*args, compounds=compounds, **kwargs)


####################

# EQUIPMENTS


class ENTBaseEquipment(ENTBase):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self._portmap: Dict[str, Dict[int, str]] = {
            "in": {},
            "out": {},
        }

    @sharedutils.handle_exceptions(return_as_self=True)
    def connect_materialstream(
        self,
        material_stream: "ENTBaseStream",
        port_type: str,
        port_num: Optional[int] = None,
    ) -> "ENTBaseEquipment":
        """
        Connects a material stream to an input or output port.

        Args:
            material_stream (MaterialStream): The material stream to connect.
            port_type (str): The type of port to connect to, either "in" or "out".
            port_num (Optional[int]): The port number to connect the stream to. If none given, it will default to the lowest available port.
        """
        ports: Dict[int, str] = self._portmap[port_type]
        if port_num is None:
            port_num = next(i for i in range(len(ports) + 1) if i not in ports)
        ports[port_num] = material_stream.label
        logging.info(
            f"{sharedutils.get_function_name()} - Connected {material_stream.label} to {self.label} on port {port_type}:{port_num}"
        )
        return self

    @sharedutils.handle_exceptions(return_as_self=True)
    def disconnect_materialstream(
        self, material_stream: "ENTBaseStream"
    ) -> "ENTBaseEquipment":
        """
        Disconnects a material stream from either input or output ports.

        Args:
            material_stream (MaterialStream): The stream to disconnect.

        Returns:
            self (EquipmentEntity): Returns self for method chaining.
        """
        for port_type, ports in self._portmap.items():
            for port_num, stream in list(ports.items()):
                if stream == material_stream.label:
                    del ports[port_num]
                    logging.info(
                        f"{sharedutils.get_function_name()} - Stream {material_stream.label} disconnected from {port_type}put port {port_num} of {self.label}."
                    )
                    return self

        raise KeyError(f"Stream {material_stream.label} not found in equipment ports.")

    def get_ports(self) -> Dict[str, Dict[int, str]]:
        """
        Returns the portmap of the equipment.
        """
        return self._portmap

    def get_port(
        self, material_stream: Union["ENTBaseStream", str]
    ) -> Optional[Tuple[str, int]]:
        """
        Returns
            None if material stream is not found.
            (port_type, port_num) if material stream is found.
        """
        _stream_id = (
            material_stream.label
            if isinstance(material_stream, ENTBaseStream)
            else material_stream
        )

        result = None
        for port_type, ports in self._portmap.items():
            for port_num, stream in ports.items():
                if stream == _stream_id:
                    result = (port_type, port_num)
                    break

        return result


########################################

# CONCRETE CLASSES


class BatteryIn(ENTBaseEquipment):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class BatteryOut(ENTBaseEquipment):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


# EXCHANGERS


class Heater(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(ContVarSpecEnum.Efficiency, 1.0, (0.0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.PressureDrop, 0, (-1000.0, 1000.0)),
        VOContinuousVariable(
            ContVarSpecEnum.OutletTemperature, 298.15, (-10_000.0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.TemperatureChange, 0, (-10_000.0, 100_000_000)
        ),
        VOContinuousVariable(ContVarSpecEnum.OutletVaporFraction, 0, (0.0, 1.0)),
        VOContinuousVariable(
            ContVarSpecEnum.HeatAddedOrRemoved, 0, (-10_000.0, 100_000_000)
        ),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CalculationType,
            init_value=DiscreteItemSpecEnum.HeatAddedOrRemoved,
            bounds={
                DiscreteItemSpecEnum.HeatAddedOrRemoved,
                DiscreteItemSpecEnum.TemperatureChange,
                DiscreteItemSpecEnum.OutletTemperature,
                DiscreteItemSpecEnum.OutletVaporMoleFraction,
            },
        )
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class Cooler(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(ContVarSpecEnum.Efficiency, 1.0, (0.0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.PressureDrop, 0, (-1000.0, 1000.0)),
        VOContinuousVariable(
            ContVarSpecEnum.OutletTemperature, 298.15, (-10_000.0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.TemperatureChange, 0, (-10_000.0, 100_000_000)
        ),
        VOContinuousVariable(ContVarSpecEnum.OutletVaporFraction, 0, (0.0, 1.0)),
        VOContinuousVariable(
            ContVarSpecEnum.HeatAddedOrRemoved, 0, (-10_000.0, 100_000_000)
        ),
    }
    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CalculationType,
            init_value=DiscreteItemSpecEnum.HeatAddedOrRemoved,
            bounds={
                DiscreteItemSpecEnum.HeatAddedOrRemoved,
                DiscreteItemSpecEnum.TemperatureChange,
                DiscreteItemSpecEnum.OutletTemperature,
                DiscreteItemSpecEnum.OutletVaporMoleFraction,
            },
        )
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class HeatExchanger(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(
            ContVarSpecEnum.ColdFluidPressureDrop,
            0,
            (-1000.0, 1000.0),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.HotFluidPressureDrop,
            0,
            (-1000.0, 1000.0),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ColdFluidOutletTemperature,
            298.15,
            (-1000.0, 1000.0),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.HotFluidOutletTemperature,
            298.15,
            (-1000.0, 1000.0),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            1000,
            (-1000.0, 1000.0),
        ),
        VOContinuousVariable(ContVarSpecEnum.HeatExchangeArea, 1.0, (-1000.0, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.HeatExchange, 0, (-1000.0, 1000.0)),
        VOContinuousVariable(
            ContVarSpecEnum.MinimumTemperatureDifference,
            0,
            (-1000.0, 1000.0),
        ),
        VOContinuousVariable(ContVarSpecEnum.HeatLoss, 0, (-1000.0, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.HeatTransferEfficiency, 0, (0, 1)),
        VOContinuousVariable(ContVarSpecEnum.OutletVaporFractionFluid1, 0, (0, 1)),
        VOContinuousVariable(ContVarSpecEnum.OutletVaporFractionFluid2, 0, (0, 1)),
        # Tube Vars
        VOContinuousVariable(ContVarSpecEnum.ShellInSeries, 1, (0, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.ShellPasses, 2, (0, 10_000.0)),
        VOContinuousVariable(
            ContVarSpecEnum.InternalDiameterOfShell,
            500,
            (0, 10_000.0),
        ),
        VOContinuousVariable(ContVarSpecEnum.ShellFouling, 0, (0, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.BaffleSpacing, 250, (0, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.BaffleCut, 20, (0, 100)),
        VOContinuousVariable(ContVarSpecEnum.InternalDiameterOfTube, 50, (1, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.ExternalDiameterOfTube, 60, (1, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.TubeLength, 5, (1, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.TubeFouling, 0, (0, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.RoughnessTube, 0.045, (0, 1000.0)),
        VOContinuousVariable(
            ContVarSpecEnum.ThermalConductivityOfTube,
            70,
            (0, 1000.0),
        ),
        VOContinuousVariable(ContVarSpecEnum.PassesPerShell, 2, (0, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.TubesPerShell, 50, (0, 1000.0)),
        VOContinuousVariable(ContVarSpecEnum.TubeSpacing, 70, (0, 1000.0)),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CalculationType,
            init_value=DiscreteItemSpecEnum.CalculateHotFluidOutletTemperature,
            bounds={
                DiscreteItemSpecEnum.CalculateHotFluidOutletTemperature,
                DiscreteItemSpecEnum.CalculateColdFluidOutletTemperature,
                DiscreteItemSpecEnum.CalculateOutletTemperatures,
                DiscreteItemSpecEnum.CalculateOutletTemperatures_UA,
                DiscreteItemSpecEnum.CalculateArea,
                DiscreteItemSpecEnum.ShellAndTubesExchangerRating,
                DiscreteItemSpecEnum.ShellAndTubesExchangerFoulingFactor,
                DiscreteItemSpecEnum.PinchPoint,
                DiscreteItemSpecEnum.SpecifyHeatTransferEfficiency,
                DiscreteItemSpecEnum.SpecifyOutletMolarVaporFraction_Stream1,
                DiscreteItemSpecEnum.SpecifyOutletMolarVaporFraction_Stream2,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.FlowDirection,
            init_value=DiscreteItemSpecEnum.FlowDirection_CounterCurrent,
            bounds={
                DiscreteItemSpecEnum.FlowDirection_CounterCurrent,
                DiscreteItemSpecEnum.FlowDirection_CoCurrent,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.TubeLayout,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.TubeLayout_RotatedTriangle,
                DiscreteItemSpecEnum.TubeLayout_Triangle,
                DiscreteItemSpecEnum.TubeLayout_RotatedSquare,
                DiscreteItemSpecEnum.TubeLayout_Square,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.ShellAndTubesExchangerFoulingFactor},
                {DiscreteItemSpecEnum.ShellAndTubesExchangerRating},
            ),
            policies=[
                _ContextFreePolicies.no_setval_on_oob_discrete,
                # _ContextFreePolicies.no_setval_on_dependent,
                _ContextAwarePolicies.no_setval_on_preqreq_not_met,
                _ContextAwarePolicies.toggle_indep_on_setval,
            ],
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.FluidInTubes,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.FluidInTubes_Cold,
                DiscreteItemSpecEnum.FluidInTubes_Hot,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.ShellAndTubesExchangerFoulingFactor},
                {DiscreteItemSpecEnum.ShellAndTubesExchangerRating},
            ),
            policies=[
                _ContextFreePolicies.no_setval_on_oob_discrete,
                # _ContextFreePolicies.no_setval_on_dependent,
                _ContextAwarePolicies.no_setval_on_preqreq_not_met,
                _ContextAwarePolicies.toggle_indep_on_setval,
            ],
        ),
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class AirCooler2(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(ContVarSpecEnum.FluidPressureDrop, 0, (0, 10_000.0)),
        VOContinuousVariable(
            ContVarSpecEnum.OutletFluidTemperature,
            300,
            (0, 100_000_000),
            is_independent=True,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.InletAirTemperature,
            298.15,
            (0, 10_000.0),
            is_independent=True,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.InletAirPressure,
            101325,
            (101325, 10_000_000),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.OutletAirTemperature,
            298.15,
            (0, 100_000_000),
        ),
        VOContinuousVariable(ContVarSpecEnum.OverallUA, 500, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.HeatExchange, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.MaximumHeatExchange, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.ExchangerEfficiency, 0, (0, 1.0)),
        VOContinuousVariable(
            ContVarSpecEnum.InternalDiameterOfTube,
            50,
            (0, 10_000.0),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ExternalDiameterOfTube,
            60,
            (0, 10_000.0),
        ),
        VOContinuousVariable(ContVarSpecEnum.TubeLength, 5, (0, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.TubeFouling, 0, (0, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.RoughnessTube, 45, (0, 10_000.0)),
        VOContinuousVariable(
            ContVarSpecEnum.ThermalConductivityOfTube,
            70,
            (0, 10_000.0),
        ),
        VOContinuousVariable(ContVarSpecEnum.NumberOfPasses, 1, (1, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.NumberOfTubes, 160, (1, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.TubeSpacing, 40, (0, 100_000_000)),
        VOContinuousVariable(
            ContVarSpecEnum.ReferenceRotationOfFan,
            100,
            (1, 100_000_000),
            is_independent=True,
        ),
        VOContinuousVariable(ContVarSpecEnum.ReferenceAirFlow, 1, (1, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.ActualRotation, 100, (1, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.ActualAirFlow, 1, (0, 100_000_000)),
        VOContinuousVariable(
            ContVarSpecEnum.ElectricalPowerConversionFactor,
            1,
            (0, 1.0),
            is_independent=True,
        ),
        VOContinuousVariable(ContVarSpecEnum.PowerRequired, 0, (0, 1_000_000.0)),
    }
    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CalculationType,  # "calculation_type",
            init_value=DiscreteItemSpecEnum.SpecifyOutletTemp,
            bounds={
                DiscreteItemSpecEnum.SpecifyOutletTemp,
                DiscreteItemSpecEnum.SpecifyTubeGeometry,
                DiscreteItemSpecEnum.SpecifyOverallUA,
            },
        )
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


# PRESSURE CHANGERS


class OrificePlate(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(ContVarSpecEnum.CorrectionFactor, 0, (0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.OverallPressureDrop, 0, (0, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.OrificePressureDrop, 0, (0, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.OrificeBeta, 0.5, (0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.OrificeDiameter, 100, (1, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.InternalPipeDiameter, 200, (1, 10_000.0)),
        VOContinuousVariable(
            ContVarSpecEnum.Orifice_Temperature_Change, 0, (0, 10_000.0)
        ),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.OrificeType,
            init_value=DiscreteItemSpecEnum.Pressure_Tappings_Flange,
            bounds={
                DiscreteItemSpecEnum.Pressure_Tappings_Corner,
                DiscreteItemSpecEnum.Pressure_Tappings_Flange,
                DiscreteItemSpecEnum.Pressure_Tappings_Radius,
            },
        )
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class Compressor(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(ContVarSpecEnum.PressureIncrease, 0, (0, 1000_000_000.0)),
        VOContinuousVariable(ContVarSpecEnum.OutletPressure, 0, (0, 1000_000_000.0)),
        VOContinuousVariable(ContVarSpecEnum.AdiabaticEff, 0.75, (0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.PolytropicEff, 0.75, (0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.PowerRequired, 0, (0, 1_000_000.0)),
        VOContinuousVariable(ContVarSpecEnum.OutletTemperature, 0, (0, 1_000_000.0)),
        VOContinuousVariable(ContVarSpecEnum.TemperatureChange, 0, (0, 1_000_000.0)),
        VOContinuousVariable(ContVarSpecEnum.AdiabaticCoeff, 0, (0, 1_000_000.0)),
        VOContinuousVariable(ContVarSpecEnum.PolytropicCoeff, 0, (0, 1_000_000.0)),
        VOContinuousVariable(ContVarSpecEnum.AdiabaticHead, 0, (0, 1_000_000.0)),
        VOContinuousVariable(ContVarSpecEnum.PolytropicHead, 0, (0, 1_000_000.0)),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_FlowRate, 0, (0, 1_000_000.0)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_Head, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_Power, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_Eff, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(ContVarSpecEnum.RotationSpeed, 1500, (0, 100_000_000)),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CalculationType,
            init_value=DiscreteItemSpecEnum.OutletPressure_Compressor,
            bounds={
                DiscreteItemSpecEnum.OutletPressure_Compressor,
                DiscreteItemSpecEnum.PressureIncrease_Compressor,
                DiscreteItemSpecEnum.PowerRequired_Compressor,
                DiscreteItemSpecEnum.PerformanceCurves_Compressor,
                # DiscreteItemSpecEnum.Known_Head_Compressor,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.ThermodynamicCycles,
            init_value=DiscreteItemSpecEnum.ThermodynamicProcess_Polytropic,
            bounds={
                DiscreteItemSpecEnum.ThermodynamicProcess_Adiabatic,
                DiscreteItemSpecEnum.ThermodynamicProcess_Polytropic,
            },
        ),
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class Pump(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(ContVarSpecEnum.Efficiency, 0.75, (0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.PressureIncrease, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.OutletPressure, 0, (0, 100_000_000)),
        VOContinuousVariable(
            ContVarSpecEnum.OutletTemperature, 298.15, (0, 100_000_000)
        ),
        VOContinuousVariable(ContVarSpecEnum.TemperatureChange, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.PowerRequired, 0, (0, 100_000_000)),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_FlowRate, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_Head, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_Power, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_Eff, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_NPSH, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_SystemHead, 0, (0, 100_000_000)
        ),
    }
    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CalculationType,
            init_value=DiscreteItemSpecEnum.PressureIncrease,
            bounds={
                DiscreteItemSpecEnum.PressureIncrease,
                DiscreteItemSpecEnum.OutletPressure,
                DiscreteItemSpecEnum.PowerRequired,
                DiscreteItemSpecEnum.PerformanceCurve,
            },
        )
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class Expander(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(ContVarSpecEnum.PressureDecrease, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.OutletPressure, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.AdiabaticEff, 0.75, (0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.PolytropicEff, 0.75, (0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.PowerGenerated, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.OutletTemperature, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.TemperatureChange, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.AdiabaticCoeff, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.PolytropicCoeff, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.AdiabaticHead, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.PolytropicHead, 0, (0, 100_000_000)),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_FlowRate, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_Head, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_Power, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PerformanceCurves_Eff, 0, (0, 100_000_000)
        ),
        VOContinuousVariable(ContVarSpecEnum.RotationSpeed, 1500, (0, 100_000_000)),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CalculationType,
            init_value=DiscreteItemSpecEnum.OutletPressure_Expander,
            bounds={
                DiscreteItemSpecEnum.OutletPressure_Expander,
                DiscreteItemSpecEnum.PressureDecrease_Expander,
                DiscreteItemSpecEnum.PowerGenerated_Expander,
                DiscreteItemSpecEnum.PerformanceCurves_Expander,
                # DiscreteItemSpecEnum.Known_Head_Expander,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.ThermodynamicProcess,
            init_value=DiscreteItemSpecEnum.ThermodynamicProcess_Adiabatic,
            bounds={
                DiscreteItemSpecEnum.ThermodynamicProcess_Adiabatic,
                DiscreteItemSpecEnum.ThermodynamicProcess_Polytropic,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.OutletPressure_Expander},
                {DiscreteItemSpecEnum.PressureDecrease_Expander},
                {DiscreteItemSpecEnum.PowerGenerated_Expander},
                {DiscreteItemSpecEnum.PerformanceCurves_Expander},
                # {DiscreteItemSpecEnum.Known_Head_Expander},
            ),
            policies=[
                _ContextFreePolicies.no_setval_on_oob_discrete,
                # _ContextFreePolicies.no_setval_on_dependent,
                _ContextAwarePolicies.no_setval_on_preqreq_not_met,
                _ContextAwarePolicies.toggle_indep_on_setval,
            ],
        ),
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class Valve(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(ContVarSpecEnum.PressureDrop, 0, (0, 100_000_000.0)),
        VOContinuousVariable(ContVarSpecEnum.OutletPressure, 0, (0, 100_000_000.0)),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CalculationType,
            init_value=DiscreteItemSpecEnum.PressureDrop_Valve,
            bounds={
                DiscreteItemSpecEnum.OutletPressure_Valve,
                DiscreteItemSpecEnum.PressureDrop_Valve,
            },
        )
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


# MIXERS AND SPLITTERS


class StreamMixer(ENTBaseEquipment):

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.PressureCalculation,
            init_value=DiscreteItemSpecEnum.Inlet_Minimum,
            bounds={
                DiscreteItemSpecEnum.Inlet_Minimum,
                DiscreteItemSpecEnum.Inlet_Average,
                DiscreteItemSpecEnum.Inlet_Maximum,
            },
        )
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


# class StreamSplitter(ENTBaseEquipment):
#     COLLECTION_DEFAULTS = [
#         VarCollectionDiscreteSet,
#         VarCollectionContinuous,
#         VarCollectionSplitter,
#     ]

#     CONTVAR_DEFAULTS = {
#         VOContinuousVariable(
#             ContVarSpecEnum.Stream_Split_Ratio,
#             (1.0, 0.0, 0.0),
#             (0, 1.0),
#             is_independent=True,
#         ),
#         VOContinuousVariable(
#             ContVarSpecEnum.Stream_Split_FlowSpec,
#             0.5,
#             (0, 100_000_000),
#         ),
#     }

#     DISCRETESET_DEFAULTS = {
#         VODiscreteVariable(
#             variable_enum=DiscreteSetSpecEnum.CalculationType,
#             init_value=DiscreteItemSpecEnum.Stream_Split_Ratios,
#             bounds={
#                 DiscreteItemSpecEnum.Stream_Split_Ratios,
#                 # DiscreteItemSpecEnum.Stream_Mass_Flow_Specs,
#                 # DiscreteItemSpecEnum.Stream_Mole_Flow_Specs,
#             },
#         )
#     }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

#     def _init_splitstream_collection(self):
#         """Initialize splitstream collection with 3 streams"""
#         collection = self.get_collection(VarCollectionSplitter)
#         streams = [
#             SplitStreamSpecEnum.S1,
#             SplitStreamSpecEnum.S2,
#             SplitStreamSpecEnum.S3,
#         ]
#         for stream in streams:
#             var = VOSplitStream(
#                 stream,
#                 0.0,
#                 (0, 1.0),
#                 is_independent=True,
#             )
#             collection.add_item(var)


# SEPERATOR


class Vessel(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(
            ContVarSpecEnum.OverrideSaturationTemp,
            298.15,
            (0, 100_000_000),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.OverrideSaturationPressure,
            101325,
            (101325, 10_000_000),
        ),
        # VOContinuousVariable(
        #     ParameterLabelEnum.PowerRequired,
        #     0,
        #     (0, 10_000_000),
        # ),
    }
    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.PressureCalculation,
            init_value=DiscreteItemSpecEnum.Inlet_Minimum,
            bounds={
                DiscreteItemSpecEnum.Inlet_Minimum,
                DiscreteItemSpecEnum.Inlet_Average,
                DiscreteItemSpecEnum.Inlet_Maximum,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.OverrideTemperature,  # "override_temperature",
            init_value=DiscreteItemSpecEnum.NoOverrideSepTemperature,
            bounds={
                DiscreteItemSpecEnum.OverrideSepTemperature,
                DiscreteItemSpecEnum.NoOverrideSepTemperature,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.OverridePressure,  # "override_pressure",
            init_value=DiscreteItemSpecEnum.NoOverrideSepPressure,
            bounds={
                DiscreteItemSpecEnum.OverrideSepPressure,
                DiscreteItemSpecEnum.NoOverrideSepPressure,
            },
        ),
    }
    # TODO - implmenet energy stream management for conditions outside of overrides.
    # This needs to be only created if Overrides are toggled
    # see https://www.notion.so/alephtechai/Vessel-KeyError-for-collection-fe4744ae10a74ad08d50e13a3302714c?pvs=4 , custom management of energy stream is needed

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


# COLUMNS


class ShortcutColumn(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        # To be left as indep by default
        VOContinuousVariable(ContVarSpecEnum.LightKeyMolFraction, 0, (0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.HeavyKeyMolFraction, 0, (0, 1.0)),
        VOContinuousVariable(ContVarSpecEnum.RefluxRatio, 0, (0, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.CondenserPressure, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.ReboilerPressure, 0, (0, 100_000_000)),
        # Needs to be Dependant
        VOContinuousVariable(ContVarSpecEnum.MinimumRefluxRatio, 0, (0, 10_000)),
        VOContinuousVariable(ContVarSpecEnum.MinimumNumberOfTrays, 0, (0, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.ActualNumberOfTrays, 0, (0, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.OptimalFeedStage, 0, (0, 10_000.0)),
        VOContinuousVariable(ContVarSpecEnum.StrippingLiquid, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.RectifyingLiquid, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.StrippingVapor, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.RectifyingVapor, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.CondenserDuty, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.ReboilerDuty, 0, (0, 100_000_000)),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CondenserType,  # "condenser_type",
            init_value=DiscreteItemSpecEnum.CalcMode_SC_Total,
            bounds={
                DiscreteItemSpecEnum.CalcMode_SC_Total,
                DiscreteItemSpecEnum.CalcMode_SC_Partial,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.LightKeyCompound,  # "light_key_compound",
            init_value=CompoundRef.get_vocompound_from_enum(CASCompoundEnum.Benzene),
            bounds=set(CompoundRef.get_all_dwsim_compounds()),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.HeavyKeyCompound,  # "heavy_key_compound",
            init_value=CompoundRef.get_vocompound_from_enum(CASCompoundEnum.Toluene),
            bounds=set(CompoundRef.get_all_dwsim_compounds()),
        ),
    }

    def __init__(
        self,
        *args,
        **kwargs,
    ):
        super().__init__(*args, **kwargs)
        self.update_bounds_from_plant()

    def update_bounds_from_plant(
        self,
    ):
        """Initializes a the bounds for a compound selection"""
        if self.parent_obj == None:
            return

        # Get compounds from compound registry for plant
        compounds = set(self.parent_obj.compounds)
        disc_vars = self._collections.get_collection(
            collection_type=VarCollectionDiscreteSet
        )
        for var in disc_vars.items:
            # for var in self.discreteVO_collection.values():
            if not isinstance(var.value, VOCompound):
                continue
            var.bounds = compounds
        return


####################

# REACTIONSET


class VarCollectionReactionStoich(
    AbstractVariableCollection[VOCompound, VOReactionStoich]
):
    def _get_key(self, item: VOReactionStoich) -> VOCompound:
        return item.compound

    # BOUNDS
    def _get_bounds(self, item: VOReactionStoich):
        return item.bounds

    def _set_bounds(self, item: VOReactionStoich, bounds: Any):
        item.bounds = bounds

    # VALUE
    def _set_value(self, item: VOReactionStoich, value: Any, **kwargs):
        item.set_value(value, **kwargs)

    def _get_value(self, item: VOReactionStoich):
        return item.value

    # Helper methods
    @property
    def reactions(self) -> List[VOReactionStoich]:
        return list(self.items)

    def add_reactionstoich(self, reaction: VOReactionStoich):
        self.add_item(reaction)

    def remove_reactionstoich(self, reaction: VOReactionStoich):
        self.remove_item(reaction)


class ConversionReaction(ENTBase):
    # NOTE - this to be subclassed in future as reactionsets expand
    REACTIONSET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.BaseCompound,
            # TODO - solve callbacks to get bounds from reactionstoich
            # bounds=lambda self: set(self.reaction_compounds),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.ConversionExpression,
            init_value="",
            bounds=None,
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.ReactionPhase,
            init_value=DiscreteItemSpecEnum.REACTION_LIQUID,
            bounds={
                DiscreteItemSpecEnum.REACTION_MIXTURE,
                DiscreteItemSpecEnum.REACTION_VAPOR,
                DiscreteItemSpecEnum.REACTION_LIQUID,
            },
        ),
    }
    COLLECTION_DEFAULTS = [VarCollectionReactionStoich, VarCollectionDiscreteSet]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._associated_equipment: Set[ENTBaseEquipment] = set()

    @property
    def reaction_compounds(self) -> List[VOCompound]:
        collection = self.get_collection(VarCollectionReactionStoich)
        assert isinstance(collection, VarCollectionReactionStoich)
        return [reactionstoich.compound for reactionstoich in collection.items]

    @property
    def equipments(self):
        return self._associated_equipment

    def add_equipment_reference(self, equipment: ENTBaseEquipment):
        self._associated_equipment.add(equipment)

    def remove_equipment_reference(self, equipment: ENTBaseEquipment):
        self._associated_equipment.remove(equipment)


####################

# REACTORS


class ConversionReactor(ENTBaseEquipment):

    CONTVAR_DEFAULTS = {
        VOContinuousVariable(
            ContVarSpecEnum.PressureDrop_ConversionReactor, 0, (0, 10_000.0)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.TemperatureDifference,
            0,
            (0, 100_000_000),
        ),
        VOContinuousVariable(ContVarSpecEnum.HeatLoad, 0, (0, 100_000_000)),
        VOContinuousVariable(ContVarSpecEnum.OutletTemperature, 1, (0, 100_000_000)),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CalculationType,
            init_value=DiscreteItemSpecEnum.RCT_Conversion_Adiabatic,
            bounds={
                DiscreteItemSpecEnum.RCT_Conversion_Adiabatic,
                DiscreteItemSpecEnum.RCT_Conversion_Isothermic,
                DiscreteItemSpecEnum.RCT_Conversion_DefineOutletTemp,
            },
        ),
    }

    COLLECTION_DEFAULTS = [
        VarCollectionDiscreteSet,
        VarCollectionContinuous,
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._reactionset: Set[ConversionReaction] = set()

    def __deepcopy__(self, memo):
        # ConversionReactor
        new_obj = type(self)(self.label)
        memo[id(self)] = new_obj

        # Copy base attributes
        for k, v in self.__dict__.items():
            if k != "_reactionset":
                setattr(new_obj, k, copy.deepcopy(v, memo))

        # Use existing methods to establish relationships
        for reaction in self._reactionset:
            new_reaction = copy.deepcopy(reaction, memo)
            new_obj.add_reaction(new_reaction)

        return new_obj

    @property
    def reactionset(self) -> Set[ConversionReaction]:
        return self._reactionset

    def add_reaction(self, reaction: ConversionReaction):
        self._reactionset.add(reaction)
        reaction.add_equipment_reference(self)

    def remove_reaction(self, reaction: ConversionReaction):
        self._reactionset.remove(reaction)
        reaction.remove_equipment_reference(self)


class PositionSensitiveDetector(ENTBaseEquipment):
    # #TODO Alen fill in defaults and bounds
    CONTVAR_DEFAULTS = {
        # All are dependent always: This is just a gPROMS sensor block
        VOContinuousVariable(
            ContVarSpecEnum.AverageParticleSize_PSD,
            None,
            (0, 10_000_000),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CoefficientOfVariation_PSD,
            1e-10,
            (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Quantile05_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Quantile10_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Quantile16_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Quantile25_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Quantile50_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Quantile75_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Quantile84_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Quantile90_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Quantile95_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SauterMean_PSD,
            None,
            (0, 10_000_020),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.Span_PSD,
            None,
            (1e-10, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.VolumeMean_PSD,
            0,
            (0, 10_000_020),
            is_independent=False,
        ),
    }
    COLLECTION_DEFAULTS = [
        VarCollectionContinuous,
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class SimulationDuration(ENTBaseEquipment):
    # #TODO Alen fill in defaults and bounds
    CONTVAR_DEFAULTS = {
        VOContinuousVariable(
            ContVarSpecEnum.SimulationDuration_SD,
            None,
            (0, 10_000_000_000),
            is_independent=True,
        ),
    }
    COLLECTION_DEFAULTS = [
        VarCollectionContinuous,
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class Crystallizer(ENTBaseEquipment):
    # TODO Alen fill in defaults and bounds
    # Warning: Contvars without bounds originally have been replaced with default bounds of [MIN_SAFE_INTEGER, MAX_SAFE_INTEGER],
    # as there is an assert in PresetContvar.hydrate_schema()
    CONTVAR_DEFAULTS = {
        # Equipment and Operation Variables
        VOContinuousVariable(ContVarSpecEnum.EquipmentVolume_CR, 1, (0, 100)),
        VOContinuousVariable(ContVarSpecEnum.EquipmentHeight_CR, 1, (0, 100_000)),
        VOContinuousVariable(
            ContVarSpecEnum.EquipmentCrossSectionalArea_CR, 1, (0, 100_000)
        ),
        VOContinuousVariable(ContVarSpecEnum.EquipmentDiameter_CR, 1, (0, 100_000)),
        VOContinuousVariable(ContVarSpecEnum.FlowRateSetPoint_CR, 1, (0, 2.777777)),
        VOContinuousVariable(ContVarSpecEnum.MeanResidenceTime_CR, 2, (0, 6_000_000)),
        VOContinuousVariable(ContVarSpecEnum.RelativeFill_CR, 1, (1e-5, 1)),
        VOContinuousVariable(
            ContVarSpecEnum.TimeConstantOfFlowResponse_CR,
            None,
            (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
        ),  # TODO TBD
        # VOContinuousVariable(
        #     ContVarSpecEnum.PressureSetPoint_CR, 0, (0, 100_000_000)
        # ),  # TODO TBD
        # VOContinuousVariable(
        #     ContVarSpecEnum.VaporOutflowSetPoint_CR, 0, (0, 100_000_000)
        # ),  # TODO TBD
        # VOContinuousVariable(
        #     ContVarSpecEnum.LiquidEntrainment_CR, 0, (0, 100_000_000)
        # ),  # TODO TBD
        VOContinuousVariable(ContVarSpecEnum.ImpellerDiameter_CR, 0.5, (1e-6, 100)),
        VOContinuousVariable(ContVarSpecEnum.ImpellerFrequency_CR, 40, (1e-6, 300_000)),
        VOContinuousVariable(
            ContVarSpecEnum.ImpellerPowerNumber_CR,
            0.5,
            (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ImpellerPumpingNumber_CR,
            0.8,
            (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
        ),
        VOContinuousVariable(ContVarSpecEnum.SpecificPowerInput_CR, 1, (0.01, 100)),
        # Heat Transfer Variables
        VOContinuousVariable(ContVarSpecEnum.EnergyInput_CR, 0, (-1e6, 1e6)),
        VOContinuousVariable(
            ContVarSpecEnum.HeatingCoolingFluidTemperature_CR, 25, (-50, 200)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.OverallHeatTransferCoefficient_CR, 500, (0, 10_000)
        ),
        VOContinuousVariable(ContVarSpecEnum.HeatTransferArea_CR, 0.1, (0, 100)),
        VOContinuousVariable(
            ContVarSpecEnum.OverallHeatTransferCoefficientXArea_CR, 100, (0, 10_000)
        ),
        VOContinuousVariable(ContVarSpecEnum.AmbientHeatLoss_CR, 0, (-1e6, 1e6)),
        VOContinuousVariable(
            ContVarSpecEnum.AmbientHeatTransferCoefficient_CR, 10, (0, 1_000)
        ),
        VOContinuousVariable(ContVarSpecEnum.AreaForAmbientHeatLoss_CR, 0.01, (0, 100)),
        VOContinuousVariable(ContVarSpecEnum.AmbientTemperature_CR, 25, (-50, 200)),
        # Primary Nucleation Variables
        VOContinuousVariable(
            ContVarSpecEnum.PrimaryNucleationClassicalRateConstant_CR,
            20,
            (-50, 100),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PrimaryNucleationClassicalSurfaceEnergy_CR,
            0.01,
            (1e-4, 10),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PrimaryNucleationClassicalSurfaceEnergyCorrectionFactor_CR,
            1,
            (1e-3, 1e3),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PrimaryNucleationPowerLawRateConstant_CR,
            10,
            (-50, 100),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PrimaryNucleationPowerLawSupersaturationOrder_CR,
            1,
            (0, 10),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PrimaryNucleationPowerLawActivationEnergy_CR,
            0,
            (0, 1e10),
        ),
        # Secondary Nucleation Variables
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedMersmannRateConstant_CR,
            -35,
            (-50, 0),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedMersmannSolubilityFactor_CR,
            0.414,
            (0, 1),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedMersmannNumEntities_CR,
            1,
            (0, 5),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedMersmannSurfaceAreaShapeFactor_CR,
            3.14,
            (0, 100),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawRateConstant_CR,
            20,
            (-50, 100),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSupersaturationOrder_CR,
            1,
            (0, 5),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawActivationEnergy_CR,
            0,
            (0, 1e5),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSpecificPowerInputOrder_CR,
            1,
            (0, 5),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSurfaceAreaOrder_CR,
            0.001,
            (1e-5, 1),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSurfaceAreaShapeFactor_CR,
            3.14,
            (0, 100),
        ),
        # Secondary Nucleation Attrition Variables
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionEvansRateConstantCC_CR,
            15,
            (-50, 100),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSizeAboveCC_CR,
            100,
            (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSupersaturationOrderCC_CR,
            1,
            (0, 5),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionEvansRateConstantCI_CR,
            15,
            (-50, 100),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSizeAboveCI_CR,
            100,
            (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSupersaturationOrderCI_CR,
            1,
            (0, 5),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawRateConstant_CR,
            20,
            (-50, 100),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawSupersaturationOrder_CR,
            1,
            (0, 5),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawActivationEnergy_CR,
            0,
            (0, 1e5),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawSpecificPowerInputOrder_CR,
            1,
            (0, 5),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawSlurryDensityOrder_CR,
            1,
            (0, 5),
        ),
        # Growth and Dissolution Variables
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionClassicalGrowthRateConstant_CR,
            1e-4,
            (0, 1e6),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionClassicalSupersaturationOrder_CR,
            1,
            (0, 10),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionClassicalActivationEnergy_CR,
            1e4,
            (-1e12, 1e12),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionMersmannGrowthRateConstant_CR,
            1e-4,
            (0, 1e6),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionMersmannSupersaturationOrder_CR,
            1,
            (0, 10),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionMersmannActivationEnergy_CR,
            1e4,
            (-1e12, 1e12),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionPowerLawGrowthRateConstant_CR,
            0.1,
            (0, 1e6),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionPowerLawSupersaturationOrder_CR,
            1,
            (0, 10),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionPowerLawActivationEnergy_CR,
            0,
            (-1e12, 1e12),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionEffectiveDiffusitivityCorrectionFactor_CR,
            1,
            (1e-10, 10),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionVesselDiameter_CR, 0.1, (1e-10, 100)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.GrowthAndDissolutionSurfaceEnergyForSizeDependantSolubility_CR,
            0.01,
            (1e-6, 10),
        ),
        # Agglomeration Variables
        VOContinuousVariable(ContVarSpecEnum.AgglomerationParameterA50_CR, 1, (0, 10)),
        VOContinuousVariable(ContVarSpecEnum.ConstantTermA1_CR, 1, (0, 10)),
        VOContinuousVariable(ContVarSpecEnum.GrowthRateOrderA2_CR, 1, (-10, 10)),
        VOContinuousVariable(
            ContVarSpecEnum.SpecificPowerInputOrderA3_CR, 1, (-10, 10)
        ),
        # Evaporation Variables
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationLiquidMassTransferCoefficient_CR,
        #     0.01,
        #     (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationVaporMassTransferCoefficient_CR,
        #     0.01,
        #     (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationUniformLiquidMassTransferCoefficient_CR,
        #     0.01,
        #     (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationUniformVaporMassTransferCoefficient_CR,
        #     0.01,
        #     (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationSpecificContactArea_CR, 1, (-1e-10, 1e10)
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationContactArea_CR, 1, (-1e-10, 1e10)
        # ),
        # Scheduler Discharge Variables
        VOContinuousVariable(
            ContVarSpecEnum.SchedulerDischargeNumberOfDischarges_CR, None, (0, 10)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
            30,
            (0, 166_600_000),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
            1,
            (-0.036, 3.6 * MAX_SAFE_INTEGER),
        ),
        # VOContinuousVariable(ContVarSpecEnum.SchedulerDischargeVolumeFlowRate_CR, 0, (0, 100_000_000)),
        VOContinuousVariable(
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            0.5,
            (0, 1),
        ),
        # VOContinuousVariable(ContVarSpecEnum.SchedulerDischargeFractionOfVolumeHoldupDischarged_CR, 0, (0, 100_000_000)),
        VOContinuousVariable(
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
            1,
            (0, MAX_SAFE_INTEGER),
        ),
        # VOContinuousVariable(ContVarSpecEnum.SchedulerDischargeVolumeHoldupDischarged_CR, 0, (0, 100_000_000)),
        VOContinuousVariable(
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
            1,
            (0, MAX_SAFE_INTEGER),
        ),
        # VOContinuousVariable(ContVarSpecEnum.SchedulerDischargeVolumeHoldupLeftBehind_CR, 0, (0, 100_000_000)),
        VOContinuousVariable(
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
            0.5,
            (0, MAX_SAFE_INTEGER),
        ),
        # Output variables (KPIs)
        # Crystallization Key Measurements
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementCrystalMass_CR,
            None,
            (0, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementLiquidMass_CR,
            None,
            (-0.001, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementMassConcentrationSoluteAtSaturation_CR,
            None,
            (-1e10, 1e10),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementMassConcentrationSoluteInLiquid_CR,
            None,
            (-1e10, 1e10),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementMassFractionSoluteAtSaturation_CR,
            None,
            (0, 1),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementMassFractionSoluteInLiquid_CR,
            None,
            (0, 1),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementMassOfSoluteInLiquid_CR,
            None,
            (-0.001, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementRelativeSaturation_CR,
            None,
            (-1e11, 1e11),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementRelativeSupersaturation_CR,
            None,
            (-1e11, 1e11),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationKeyMeasurementTemperature_CR,
            50,
            (-500, 80_000),
            is_independent=False,
        ),
        # Crystallization Report Variables
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationReportVariableAverageCrystalGrowth_CR,
            None,
            (0, 1e7),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationReportVariableBirthRateOfCrystals_CR,
            None,
            (-100, 100),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationReportVariablePrimaryNucleationRate_CR,
            None,
            (-100, 100),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationReportVariableSecondaryNucleationActivatedRate_CR,
            None,
            (-100, 100),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.CrystallizationReportVariableSecondaryNucleationAttritionRate_CR,
            None,
            (-100, 100),
            is_independent=False,
        ),
        # Evaporation Key Measurements
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationKeyMeasurementLiquidMassConcentration_CR,
        #     None,
        #     (-1e10, 1e10),
        #     is_independent=False,
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationKeyMeasurementLiquidMolarConcentration_CR,
        #     None,
        #     (-1e10, MAX_SAFE_INTEGER),
        #     is_independent=False,
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationKeyMeasurementMassFraction_CR,
        #     None,
        #     (0, 1),
        #     is_independent=False,
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationKeyMeasurementMolarFraction_CR,
        #     None,
        #     (0, 1),
        #     is_independent=False,
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationKeyMeasurementVapourMassConcentration_CR,
        #     None,
        #     (-1e10, 1e10),
        #     is_independent=False,
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationKeyMeasurementVapourMolarConcentration_CR,
        #     None,
        #     (1e-5, MAX_SAFE_INTEGER),
        #     is_independent=False,
        # ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.EvaporationReportVariableEvaporationRate_CR,
        #     None,
        #     (-1e10, MAX_SAFE_INTEGER),
        #     is_independent=False,
        # ),
        # Flow and Holdup Report Variables
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableContinuousPhaseTemperature_CR,
            50,
            (-50, 80_000),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableDistributedPhaseTemperature_CR,
            50,
            (-50, 80_000),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableInletContinuousPhaseMassFlowRate_CR,
            None,
            (-0.001, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableInletContinuousPhaseTemperature_CR,
            50,
            (-50, 80_000),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableInletDistributedPhaseMassFlowRate_CR,
            None,
            (-0.001, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableInletDistributedPhaseTemperature_CR,
            50,
            (-50, 80_000),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableLiquidOutletContinuousPhaseMassFlowRate_CR,
            None,
            (-0.001, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableLiquidOutletContinuousPhaseTemperature_CR,
            50,
            (-50, 80_000),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableLiquidOutletDistributedPhaseMassFlowRate_CR,
            None,
            (-0.001, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableLiquidOutletDistributedPhaseTemperature_CR,
            50,
            (-50, 80_000),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableMassHoldupOfContinuousPhase_CR,
            None,
            (-0.001, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableMassHoldupOfDistributedPhase_CR,
            None,
            (-0.001, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariablePressure_CR,
            None,
            (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FlowAndHoldupReportVariableTotalVolumeHoldup_CR,
            None,
            (MIN_SAFE_INTEGER, MAX_SAFE_INTEGER),
            is_independent=False,
        ),
        # VOContinuousVariable(
        #     ContVarSpecEnum.FlowAndHoldupReportVariableVaporOutletContinuousPhaseMassFlowRate_CR,
        #     0,
        #     (0, 100_000_000),
        #     is_independent=False,
        # ),  # TODO TBD
        # VOContinuousVariable(
        #     ContVarSpecEnum.FlowAndHoldupReportVariableVaporOutletContinuousPhaseTemperature_CR,
        #     0,
        #     (0, 100_000_000),
        #     is_independent=False,
        # ),  # TODO TBD
        # VOContinuousVariable(
        #     ContVarSpecEnum.FlowAndHoldupReportVariableVaporOutletEntrainedLiquidMassFlowRate_CR,
        #     0,
        #     (0, 100_000_000),
        #     is_independent=False,
        # ),  # TODO TBD
        # Impurities Variables
        VOContinuousVariable(
            ContVarSpecEnum.ImpuritiesKeyMeasurementCrystalPhaseComposition_CR,
            None,
            (0, 1),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ImpuritiesReportVariableCrystalGrowthDissolutionRate_CR,
            None,
            (0, 1e7),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ImpuritiesReportVariablePureCrystalGrowthDissolutionRate_CR,
            None,
            (0, 1e7),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ImpuritiesReportVariableUnmodifiedCrystalGrowthDissolutionRate_CR,
            None,
            (0, 1e7),
            is_independent=False,
        ),
    }

    DISCRETESET_DEFAULTS = {
        # Equipment and operation selection
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_VolumeSpecification,
            init_value=DiscreteItemSpecEnum.CR_SpecifiedVolume,
            bounds={
                DiscreteItemSpecEnum.CR_SpecifiedVolume,
                DiscreteItemSpecEnum.CR_SpecifiedHeightAndCrossSectionalArea,
                DiscreteItemSpecEnum.CR_SpecifiedHeightAndDiameter,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_Evaporation,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_EvaporationNotConsidered,
                DiscreteItemSpecEnum.CR_EvaporationConsidered,
            },
        ),
        # NOTE: Do not account for vapour flow since vapour phase is not required now in parameter mapping excel
        # VODiscreteVariable(
        #     variable_enum=DiscreteSetSpecEnum.CR_PressureControl,
        #     init_value=None,
        #     bounds={
        #         DiscreteItemSpecEnum.CR_ControlledByVaporSource,
        #         DiscreteItemSpecEnum.CR_ControlledByVaporOutflow,
        #     },
        #     prerequisites_for_selection=(
        #         {DiscreteItemSpecEnum.CR_EvaporationConsidered},
        #     ),
        # ),
        # NOTE: We only account for Control Type = Perfect Control and no PI Control to limit combinations
        # VODiscreteVariable(
        #     variable_enum=DiscreteSetSpecEnum.CR_ControlType,
        #     init_value=None,
        #     bounds={
        #         DiscreteItemSpecEnum.CR_PerfectControl,
        #         DiscreteItemSpecEnum.CR_PIControl,
        #     },
        #     prerequisites_for_selection=(
        #         {DiscreteItemSpecEnum.CR_EvaporationConsidered},
        #     ),
        # ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_LiquidAndSolidOutflow,
            init_value=DiscreteItemSpecEnum.CR_SpecifiedResidenceTime,
            bounds={
                DiscreteItemSpecEnum.CR_SpecifiedResidenceTime,
                DiscreteItemSpecEnum.CR_BatchOperation,
                DiscreteItemSpecEnum.CR_SpecifiedFlowRate,
                DiscreteItemSpecEnum.CR_FixedVolume,
                DiscreteItemSpecEnum.CR_ControlledByPump,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SpecificPowerInput,
            init_value=DiscreteItemSpecEnum.CR_Calculated,
            bounds={
                DiscreteItemSpecEnum.CR_Calculated,
                DiscreteItemSpecEnum.CR_Specified,
            },
        ),
        # Energy balance
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_EnergyBalance,
            init_value=DiscreteItemSpecEnum.CR_EnergyBalanceActive,
            bounds={
                DiscreteItemSpecEnum.CR_EnergyBalanceActive,
                DiscreteItemSpecEnum.CR_EnergyBalanceInactive,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_HeatTransfer,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_HeatTransferConsidered,
                DiscreteItemSpecEnum.CR_HeatTransferNotConsidered,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_EnergyBalanceActive},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_EnergyInput,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_EnergyInputCalculated,
                DiscreteItemSpecEnum.CR_EnergyInputSpecified,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_HeatTransferConsidered},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_OverallHeatTransferCoefficient,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_SpecifiedU,
                DiscreteItemSpecEnum.CR_SpecifiedUA,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_EnergyInputCalculated},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_AmbientHeatLoss,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_AmbientHeatLossNo,
                DiscreteItemSpecEnum.CR_AmbientHeatLossYes,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_HeatTransferConsidered},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_HeatLossOption,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_HeatLossOptionSpecified,
                DiscreteItemSpecEnum.CR_HeatLossOptionCalculated,
            },
            prerequisites_for_selection=({DiscreteItemSpecEnum.CR_AmbientHeatLossYes},),
        ),
        # Primary nucleation
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_PrimaryNucleationKinetics,
            init_value=DiscreteItemSpecEnum.CR_PrimaryNucleationKineticsInactive,
            bounds={
                DiscreteItemSpecEnum.CR_PrimaryNucleationKineticsInactive,
                DiscreteItemSpecEnum.CR_PrimaryNucleationKineticsClassical,
                DiscreteItemSpecEnum.CR_PrimaryNucleationKineticsPowerLaw,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SurfaceEnergy,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_SurfaceEnergyComputed,
                DiscreteItemSpecEnum.CR_SurfaceEnergySpecified,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_PrimaryNucleationKineticsClassical},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_PrimaryNucleationDrivingForce,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_PrimaryNucleationRelativeSupersaturation,
                DiscreteItemSpecEnum.CR_PrimaryNucleationAbsoluteSupersaturation,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_PrimaryNucleationKineticsPowerLaw},
            ),
        ),
        # Secondary nucleation - activated
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SecondaryNucleationActivatedKinetics,
            init_value=DiscreteItemSpecEnum.CR_SecondaryNucleationActivatedKineticsInactive,
            bounds={
                DiscreteItemSpecEnum.CR_SecondaryNucleationActivatedKineticsInactive,
                DiscreteItemSpecEnum.CR_SecondaryNucleationActivatedKineticsMersmann,
                DiscreteItemSpecEnum.CR_SecondaryNucleationActivatedKineticsPowerLaw,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SecondaryNucleationActivatedDrivingForce,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_SecondaryNucleationActivatedRelativeSupersaturation,
                DiscreteItemSpecEnum.CR_SecondaryNucleationActivatedAbsoluteSupersaturation,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_SecondaryNucleationActivatedKineticsPowerLaw},
            ),
        ),
        # Secondary nucleation - attrition
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SecondaryNucleationAttritionKinetics,
            init_value=DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionKineticsInactive,
            bounds={
                DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionKineticsInactive,
                DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionKineticsEvans,
                DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionKineticsPowerLaw,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SecondaryNucleationAttritionCollisionType,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionCollisionTypeCI,
                DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionCollisionTypeCC,
                DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionCollisionTypeBoth,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionKineticsEvans},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SecondaryNucleationAttritionDrivingForce,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionRelativeSupersaturation,
                DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionAbsoluteSupersaturation,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_SecondaryNucleationAttritionKineticsPowerLaw},
            ),
        ),
        # Growth and dissolution
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_GrowthAndDissolutionKinetics,
            init_value=DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsInactive,
            bounds={
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsInactive,
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsClassical,
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsMersmann,
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsPowerLaw,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_GrowthAndDissolutionMassTransferCorrelation,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionMassTransferCorrelationGarside,
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionMassTransferCorrelationLevinsAndGlastonbury,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsClassical},
                {DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsMersmann},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_GrowthAndDissolutionSizeDependantSolubility,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionSizeDependantSolubilityActive,
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionSizeDependantSolubilityInactive,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsClassical},
                {DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsMersmann},
                {DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsPowerLaw},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_GrowthAndDissolutionDrivingForce,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionDrivingForceRelativeSupersaturation,
                DiscreteItemSpecEnum.CR_GrowthAndDissolutionDrivingForceAbsoluteSupersaturation,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_GrowthAndDissolutionKineticsPowerLaw},
            ),
        ),
        # Agglomeration
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_AgglomerationKernel,
            init_value=DiscreteItemSpecEnum.CR_AgglomerationNone,
            bounds={
                DiscreteItemSpecEnum.CR_AgglomerationNone,
                DiscreteItemSpecEnum.CR_AgglomerationMumtazKinetics,
                DiscreteItemSpecEnum.CR_AgglomerationPowerLawKinetics,
                DiscreteItemSpecEnum.CR_AgglomerationSmoluchowskiKinetics,
            },
        ),
        # Evaporation
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_EvaporationMassTransferOptions,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_EvaporationIncludeDiffusiveMassTransferEffects,
                DiscreteItemSpecEnum.CR_EvaporationFastMassTransfer,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_EvaporationConsidered},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_EvaporationMassTransferCoefficientBasis,
            init_value=None,
            bounds={
                # Only allow mass basis at the moment
                DiscreteItemSpecEnum.CR_EvaporationMassTransferCoefficientBasisMass,
                # DiscreteItemSpecEnum.CR_EvaporationMassTransferCoefficientBasisVolume,
            },
            prerequisites_for_selection=(
                {
                    DiscreteItemSpecEnum.CR_EvaporationIncludeDiffusiveMassTransferEffects
                },
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_EvaporationMassTransferCoefficientInput,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_EvaporationMassTransferCoefficientInputUser,
                DiscreteItemSpecEnum.CR_EvaporationMassTransferCoefficientInputUniform,
            },
            prerequisites_for_selection=(
                {
                    DiscreteItemSpecEnum.CR_EvaporationIncludeDiffusiveMassTransferEffects
                },
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_EvaporationMassTransferArea,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_EvaporationMassTransferAreaSpecificContactArea,
                DiscreteItemSpecEnum.CR_EvaporationMassTransferAreaContactArea,
                DiscreteItemSpecEnum.CR_EvaporationMassTransferAreaCalculateFromGeometry,
            },
            prerequisites_for_selection=(
                {
                    DiscreteItemSpecEnum.CR_EvaporationIncludeDiffusiveMassTransferEffects
                },
            ),
        ),
        # Scheduler: Discharge
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SchedulerDischargeOperations,
            init_value=DiscreteItemSpecEnum.CR_SchedulerDischargeOperationsOff,
            bounds={
                DiscreteItemSpecEnum.CR_SchedulerDischargeOperationsOff,
                DiscreteItemSpecEnum.CR_SchedulerDischargeOperationsOn,
            },
        ),
        # VODiscreteVariable(
        #     variable_enum=DiscreteSetSpecEnum.CR_SchedulerDischargeConditions,
        #     init_value=None,
        #     bounds={
        #         DiscreteItemSpecEnum.CR_SchedulerDischargeConditionsSpecifiedHere,
        #         DiscreteItemSpecEnum.CR_SchedulerDischargeConditionsSpecifiedInProcessSchedule,
        #     },
        #     prerequisites_for_selection=(
        #         {DiscreteItemSpecEnum.CR_SchedulerDischargeOperationsOn},
        #     ),
        # ),
        # VODiscreteVariable(
        #     variable_enum=DiscreteSetSpecEnum.CR_SchedulerDischargeNumConditions,
        #     init_value=None,
        #     bounds={
        #         DiscreteItemSpecEnum.CR_SchedulerDischargeNumConditionsOne,
        #         DiscreteItemSpecEnum.CR_SchedulerDischargeNumConditionsTwo,
        #     },
        #     prerequisites_for_selection=(
        #         {DiscreteItemSpecEnum.CR_SchedulerDischargeConditionsSpecifiedHere},
        #     ),
        # ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SchedulerDischargeSpecs,
            init_value=DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsInstantaneousEmpty,
            bounds={
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsInstantaneousEmpty,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationTillEmpty,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateTillEmpty,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationAndFlowRate,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationAndFractionDischarged,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationAndHoldupDischarged,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationAndHoldupLeftBehind,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationAndRelativeFillLeftBehind,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateAndFractionDischarged,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateAndHoldupDischarged,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateAndHoldupLeftBehind,
                DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateAndRelativeFillLeftBehind,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_SchedulerDischargeOperationsOn},
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SchedulerDischargeMassOrVolumeBasis,
            init_value=None,
            bounds={
                # Note that we only consider mass basis for now.
                # This is because we cannot account in a good way for combinations of discrete selections
                # i.e. DiscreteItemA AND DiscreteItemB => toggle indep ContVarC
                DiscreteItemSpecEnum.CR_SchedulerDischargeMassOrVolumeBasisMass,
                # DiscreteItemSpecEnum.CR_SchedulerDischargeMassOrVolumeBasisVolume,
            },
            # Note: Any option with flow rate, fraction or holdup is a prereq
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateTillEmpty},
                {DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationAndFlowRate},
                {
                    DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationAndFractionDischarged
                },
                {
                    DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationAndHoldupDischarged
                },
                {
                    DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsDurationAndHoldupLeftBehind
                },
                {
                    DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateAndFractionDischarged
                },
                {
                    DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateAndHoldupDischarged
                },
                {
                    DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateAndHoldupLeftBehind
                },
                {
                    DiscreteItemSpecEnum.CR_SchedulerDischargeSpecsFlowRateAndRelativeFillLeftBehind
                },
            ),
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.CR_SchedulerDischargeNumDischarges,
            init_value=None,
            bounds={
                DiscreteItemSpecEnum.CR_SchedulerDischargeNumDischargesOne,
                DiscreteItemSpecEnum.CR_SchedulerDischargeNumDischargesMultipleFixedNumber,
                DiscreteItemSpecEnum.CR_SchedulerDischargeNumDischargesMultipleRecurring,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.CR_SchedulerDischargeOperationsOn},
            ),
        ),
    }

    COLLECTION_DEFAULTS = [
        VarCollectionDiscreteSet,
        VarCollectionContinuous,
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class TemperatureControl(ENTBaseEquipment):
    # TODO Alen fill in defaults and bounds
    CONTVAR_DEFAULTS = {
        # Output variables that are always dependent and cannot be toggled
        VOContinuousVariable(
            ContVarSpecEnum.HeatInput_TC,
            None,
            (-10_000_000_000, 10_000_000_000),
            is_independent=False,
        ),
        VOContinuousVariable(
            ContVarSpecEnum.TemperatureMeasured_TC,
            None,
            (-500, 80_000),
            is_independent=False,
        ),
        # Other variables
        VOContinuousVariable(
            ContVarSpecEnum.PIControllerMinimumOutput_TC,
            None,
            (-10_000_000_000, 10_000_000_000),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PIControllerMaximumOutput_TC,
            None,
            (-10_000_000_000, 10_000_000_000),
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PIControllerBias_TC, 0, (-10_000_000_000, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PIControllerGain_TC, 1, (0, MAX_SAFE_INTEGER)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.PIControllerResetTime_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.TempCSetPointInitial_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.TempCSetPointRamp_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.TemperatureSetPointC_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration01_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration02_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration03_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration04_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration05_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration06_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration07_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration08_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration09_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration10_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration11_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration12_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration13_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration14_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration15_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration16_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration17_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration18_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration19_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration20_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration21_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration22_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration23_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration24_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileDuration25_TC, None, (0, 10_000_000_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature01_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature02_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature03_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature04_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature05_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature06_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature07_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature08_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature09_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature10_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature11_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature12_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature13_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature14_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature15_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature16_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature17_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature18_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature19_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature20_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature21_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature22_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature23_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature24_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.ControlProfileTemperature25_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.InitialTemperatureC_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FinalTemperatureC_TC, None, (-500, 80_000)
        ),
        VOContinuousVariable(
            ContVarSpecEnum.FinalTimeMinutes_TC, None, (0, 10_000_000_000)
        ),
    }

    DISCRETESET_DEFAULTS = {
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.TemperatureControlType,
            init_value=DiscreteItemSpecEnum.TC_PerfectControl,
            bounds={
                DiscreteItemSpecEnum.TC_PerfectControl,
                DiscreteItemSpecEnum.TC_PIControl,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.TemperatureControlOptimizationMode,
            init_value=DiscreteItemSpecEnum.TC_OptimizationModeOff,
            bounds={
                DiscreteItemSpecEnum.TC_OptimizationModeOff,
                DiscreteItemSpecEnum.TC_OptimizationModeOn,
            },
        ),
        VODiscreteVariable(
            variable_enum=DiscreteSetSpecEnum.TemperatureControlProfile,
            init_value=DiscreteItemSpecEnum.TC_ControlledCooling,
            bounds={
                DiscreteItemSpecEnum.TC_ControlledCooling,
                DiscreteItemSpecEnum.TC_TimeInvariant,
                DiscreteItemSpecEnum.TC_PiecewiseLinear,
            },
            prerequisites_for_selection=(
                {DiscreteItemSpecEnum.TC_OptimizationModeOff},
            ),
        ),
    }

    COLLECTION_DEFAULTS = [
        VarCollectionDiscreteSet,
        VarCollectionContinuous,
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


# class AbsorptionColumn(EquipmentEntity):

#     CONTVAR_DEFAULTS = {
#         ContinuousVariableVO(ParameterLabel.CondenserPressureDrop, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.ColumnPressureDrop, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.NumberofStages, 12, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.EstimatedDiameter, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.EstimatedHeight, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.FeedStage_Gas, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.FeedStage_Liquid, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.BottomProduct_Stage, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.OverheadVapor_Stage, 0, (0, 10_000.0)),
#     }

#     DISCRETESET_DEFAULTS = {
#         DiscreteVariableVO(
#             variable_id=DiscreteSetLabel.ColumnType,
#             init_value=DiscreteItem.AbsorptionOperatingMode_Absorber,
#             bounds={
#                 DiscreteItem.AbsorptionOperatingMode_Absorber,
#                 DiscreteItem.AbsorptionOperatingMode_LiqLiqExtractor,
#             },
#         )
#     }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)


# class DistillationColumn(EquipmentEntity):

#     CONTVAR_DEFAULTS = {
#         # General
#         ContinuousVariableVO(ParameterLabel.NumberofStages, 12, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.ColumnPressureDrop, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.EstimatedDiameter, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.EstimatedHeight, 0, (0, 10_000.0)),
#         # Condenser Specs
#         ContinuousVariableVO(ParameterLabel.CondenserDeltaP, 0, (0, 10_000.0)),
#         ContinuousVariableVO(ParameterLabel.VaporProductFlowRate, 0, (0, 100_000_000)),
#         ContinuousVariableVO(
#             ParameterLabel.TotalCondenserSubcoolingTemperatureDrop,
#             0,
#             (0, 100_000_000),
#             is_independent=True,
#         ),
#         ContinuousVariableVO(ParameterLabel.CondenserDuty, 0, (0, 100_000_000)),
#         ContinuousVariableVO(ParameterLabel.CondenserSpecValue, 5, (0, 100_000_000)),
#         # Reboiler Specs
#         ContinuousVariableVO(ParameterLabel.ReboilerDuty, 0, (0, 100_000_000)),
#         ContinuousVariableVO(ParameterLabel.ReboilerSpecValue, 0, (0, 100_000_000)),
#     }

#     DISCRETESET_DEFAULTS = {
#         DiscreteVariableVO(
#             variable_id=DiscreteSetLabel.CondenserType,  # "condenser_type",
#             init_value=DiscreteItem.DC_CondenserType_Total,
#             bounds={
#                 DiscreteItem.DC_CondenserType_Total,
#                 DiscreteItem.DC_CondenserType_Partial,
#                 DiscreteItem.DC_CondenserType_FullReflux,
#             },
#         ),
#         DiscreteVariableVO(
#             variable_id=DiscreteSetLabel.CondenserSpec,  # "condenser_spec",
#             init_value=DiscreteItem.DC_Condenser_Spec_RefluxRatio,
#             bounds={
#                 DiscreteItem.DC_Condenser_Spec_HeatLoad,  # PASS
#                 DiscreteItem.DC_Condenser_Spec_ProductMolarFlowRate,  # PASS
#                 DiscreteItem.DC_Condenser_Spec_CompoundMolarFlowInProductStream,  # PASS
#                 DiscreteItem.DC_Condenser_Spec_ProductMassFlow,  # PASS
#                 DiscreteItem.DC_Condenser_Spec_CompoundMassFlowInProductStream,  # PASS
#                 DiscreteItem.DC_Condenser_Spec_CompoundFractionInProductStream,  # PASS
#                 DiscreteItem.DC_Condenser_Spec_CompoundRecovery,  # PASS
#                 DiscreteItem.DC_Condenser_Spec_RefluxRatio,  # PASS
#                 DiscreteItem.DC_Condenser_Spec_Temperature,  # PASS
#             },
#         ),
#         DiscreteVariableVO(
#             variable_id=DiscreteSetLabel.ReboilerSpec,  # "reboiler_spec",
#             init_value=DiscreteItem.DC_Reboiler_Spec_ProductMolarFlow,
#             bounds={
#                 DiscreteItem.DC_Reboiler_Spec_HeatLoad,
#                 DiscreteItem.DC_Reboiler_Spec_ProductMolarFlow,
#                 DiscreteItem.DC_Reboiler_Spec_CompoundMolarFlowInProductStream,
#                 DiscreteItem.DC_Reboiler_Spec_ProductMassFlow,
#                 DiscreteItem.DC_Reboiler_Spec_CompoundMassFlowInProductStream,
#                 DiscreteItem.DC_Reboiler_Spec_CompoundMassFractionInProductStream,
#                 DiscreteItem.DC_Reboiler_Spec_CompoundRecovery,
#                 DiscreteItem.DC_Reboiler_Spec_BoilUpRatio,  #
#                 DiscreteItem.DC_Reboiler_Spec_Temperature,
#             },
#         ),
#     }

#     CONTVAR_DEFAULTS = {
#         ContinuousVariableVO(ParameterLabel.CondenserDuty, 0, (0, 100_000_000)),
#         ContinuousVariableVO(ParameterLabel.ReboilerDuty, 0, (0, 100_000_000)),
#     }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)


# class RCT_Equilibrium(EquipmentEntity):

#     CONTVAR_DEFAULTS = {
#         ContinuousVariableVO(
#             ParameterLabel.PressureDrop, 0, (0, 10_000.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.TemperatureDifference, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.HeatLoad, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.InitialGibbsEnergy, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.FinalGibbsEnergy, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ReactionExtents, 0, (0, 1.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ComponentConversions, 0, (0, 1.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.OutletTemperature, 0, (0, 100_000_000)
#         ),
#     }

#     DISCRETESET_DEFAULTS = {
#         DiscreteVariableVO(
#             variable_id=DiscreteSetLabel.CalculationType,
#             init_value=DiscreteItem.RCT_Equilibrium_Adiabatic,
#             bounds={
#                 DiscreteItem.RCT_Equilibrium_Adiabatic,
#                 DiscreteItem.RCT_Equilibrium_Isothermic,
#                 DiscreteItem.RCT_Equilibrium_DefineOutletTemp,
#             },
#         )
#     }

#     CONTVAR_DEFAULTS = {
#         ContinuousVariableVO(
#             PropertyLabel.POWER_REQUIRED, 0, (0, 100_000_000)
#         ),
#     }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)


# class RCT_CSTR(EquipmentEntity):

#     CONTVAR_DEFAULTS = {
#         ContinuousVariableVO(
#             ParameterLabel.ReactorVolume, 1, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.HeadSpace, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.PressureDrop, 0, (0, 10_000.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.CatalystAmount, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.TemperatureDifference, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.HeatLoad, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ComponentConversions, 0, (0, 1.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.VaporResidenceTime, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.LiquidResidenceTime, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ReactionCoordinate, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ReactionRate, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ReactionHeat, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.OutletTemperature, 0, (0, 100_000_000)
#         ),
#     }

#     DISCRETESET_DEFAULTS = {
#         DiscreteVariableVO(
#             variable_id=DiscreteSetLabel.CalculationType,
#             init_value=DiscreteItem.RCT_CSTR_Adiabatic,
#             bounds={
#                 DiscreteItem.RCT_CSTR_Adiabatic,
#                 DiscreteItem.RCT_CSTR_Isothermic,
#                 DiscreteItem.RCT_CSTR_DefineOutletTemp,
#             },
#         )
#     }

#     CONTVAR_DEFAULTS = {
#         ContinuousVariableVO(
#             PropertyLabel.POWER_REQUIRED, 0, (0, 100_000_000)
#         ),
#     }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)


# class RCT_PFR(EquipmentEntity):

#     CONTVAR_DEFAULTS = {
#         ContinuousVariableVO(
#             ParameterLabel.ReactorVolume, 1, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.OutletTemperature,
#             298.15,
#             (0, 100_000_000),
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.TemperatureDifference, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.HeatLoad, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ComponentConversions, 0, (0, 1.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.PressureDrop, 0, (0, 10_000.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.LiquidResidenceTime, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.TubeLength, 1, (0, 10_000.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.NumberOfTubes, 1, (0, 10_000.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ReactionCoordinate, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ReactionRate, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.ReactionHeat, 0, (0, 100_000_000)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.CatalystAmount, 0, (0, 10_000.0)
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.CatalystParticleDiameter,
#             0,
#             (0, 10_000.0),
#         ),
#         ContinuousVariableVO(
#             ParameterLabel.CatalystVoidFraction, 0, (0, 1.0)
#         ),
#     }

#     DISCRETESET_DEFAULTS = {
#         DiscreteVariableVO(
#             variable_id=DiscreteSetLabel.CalculationType,
#             init_value=DiscreteItem.RCT_PFR_Adiabatic,
#             bounds={
#                 DiscreteItem.RCT_PFR_Adiabatic,
#                 DiscreteItem.RCT_PFR_Isothermic,
#                 DiscreteItem.RCT_PFR_DefineOutletTemp,
#                 DiscreteItem.RCT_PFR_DefineNonAdiabaticNonIsothermal,
#             },
#         )
#     }

#     CONTVAR_DEFAULTS = {
#         ContinuousVariableVO(
#             PropertyLabel.POWER_REQUIRED, 0, (0, 100_000_000)
#         ),
#     }

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)


# class OT_Recycle(EquipmentEntity):
#     #     CONTVAR_DEFAULTS= {
#     #         ParameterEnum.Max_Iteration,
#     #         ParameterEnum.Convergence_Tolerance_Mass_Flow,
#     #         ParameterEnum.Convergence_Tolerance_Temperature,
#     #         ParameterEnum.Convergence_Tolerance_Pressure,
#     #     }

#     #     PROPERTIES = set()

#     def __init__(self, *args, **kwargs):
#         super().__init__(*args, **kwargs)

# [ ] THIS SHOULDBE MORE DYNAMIC
VALID_EQUIPMENTS: List[Type[ENTBaseEquipment]] = [
    BatteryIn,
    BatteryOut,
    Heater,
    Cooler,
    HeatExchanger,
    AirCooler2,
    OrificePlate,
    Compressor,
    Pump,
    Expander,
    Valve,
    StreamMixer,
    # StreamSplitter,
    Vessel,
    ShortcutColumn,
    ConversionReactor,
    Crystallizer,
    SimulationDuration,
    TemperatureControl,
    PositionSensitiveDetector,
]
VALID_STREAMS: List[Type[ENTBaseStream]] = [MaterialStream, InputStream, OutputStream]

EQP_STRATEGY: Dict[str, Type[ENTBaseEquipment]] = {
    e.get_entity_type(): e for e in VALID_EQUIPMENTS
}
STREAM_STRATEGY: Dict[str, Type[ENTBaseStream]] = {
    e.get_entity_type(): e for e in VALID_STREAMS
}
