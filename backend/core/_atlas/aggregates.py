####################
from __future__ import annotations
from ._imports import *
from backend.core._atlas.entities import *


####################

# COLLECTION LAYER


class _AbstractUIDCollection(AbstractCollection[UUID, V]):
    """Base class for object access via UUIDs.

    1) All keys are UUIDs. this guarantees immutability.
    2) This Collection makes user labels a O[1] best-case

    Additional label cache.
    - O(1) lookup for common case
    - Cache automatically populated during registration
    - Cache refresh method available when labels change
    - Falls back to iteration when cache misses
    - Maintains UUID as source of truth while labels can change

    Thoughts on Caching:
    Decision to point cache to UUID instead of item
    - Single source of truth - The main collection remains the authoritative store of items, while cache just provides fast lookup routing
    - Consistency - Since we're already using UUIDs as keys in the main collection, we maintain the same access pattern
    - Type safety - The cache mapping maintains clear boundaries between string labels -> UUIDs -> items

    """

    def __init__(self):
        super().__init__()
        self._label_cache: Dict[str, uuid.UUID] = {}

    # CONCRETE IMPLEMENTATION

    def _get_key(self, item: V) -> uuid.UUID:
        return item.uid

    # LABEL METHOD SET
    @classmethod
    def get_ui_label(cls, item: V) -> str:
        """Returns the UI label. Implements any string inflection"""
        return cls._get_base_label(item)

    @classmethod
    @abc.abstractmethod
    def _get_base_label(cls, item: V) -> str:
        """Method that gets a base string for ui label from an object"""
        raise NotImplementedError()

    @classmethod
    def _transform_base_label(cls, base: str):
        return base

    def get_ui_labels(self) -> List[str]:
        """Returns all ui labels in cache."""
        # if lengths same,  return cache
        if len(self._label_cache) == len(self._collection):
            return list(self._label_cache.keys())
        else:
            self._refresh_cache()
            return list(self._label_cache.keys())

    # METHOD OVERRIDES FOR CACHE

    def add_item(self, item: V):
        super().add_item(item)
        # update cache with new items label
        label = self.get_ui_label(item)
        self._label_cache[label] = self._get_key(item)

    # UTILITY METHODS

    def get_uid(self, label: str) -> uuid.UUID:
        """Queires the cache firest, then falls back to full lookup"""

        # Case 1 - item in Cache
        if label in self._label_cache:
            return self._label_cache[label]

        # Case 2 - Cache miss, do full lookup using dynamic method calls
        for item in self.items:
            if self.get_ui_label(item) == label:
                uid = self._get_key(item)
                self._label_cache[label] = uid
                return uid

        # Case 3 - no such item based on user label (this might be FE thing)
        raise KeyError(f"No item found with label: {label}")

    # Override so this has override to get from ui label
    def get_item(self, key: Union[uuid.UUID, str]) -> V:
        """Overriden Method that takes in key (uuid) or string ui label. If ui_label will return the first match"""
        # Case 1 - its a uuid
        if isinstance(key, uuid.UUID):
            return super().get_item(key)

        # Case 2 - its a string (i.e. label)
        elif isinstance(key, str):
            # assume its a label
            uid = self.get_uid(key)
            return super().get_item(uid)

        else:
            raise TypeError(f"{type(key)} not accepted. Should be UID or STR")

    def _refresh_cache(self):
        """O(N) process to update cache. Useful to do after FE call where user can update naming, etc"""
        # Purge cache
        self._label_cache = {}

        # Update cache
        for item in self.items:
            label = self.get_ui_label(item)
            self._label_cache[label] = self._get_key(item)

    def sync_collection(self, items: List[V]):
        """
        Update collection to match the input list of items

        """
        existing_items = set(self.items) or set()
        target_state = set(items)

        # Cull items
        for item in list(existing_items - target_state):
            self.remove_item(item)

        for item in list(target_state - existing_items):
            self.add_item(item)


class CompoundUIDCollection(_AbstractUIDCollection[VOCompound]):
    @classmethod
    def _get_base_label(cls, item: VOCompound) -> str:
        return item.label

    def get_item_from_compound_id(self, compound_id: str) -> VOCompound:
        """
        Gets a compound, given a compound_id. Will return the first hit.
        """
        for compound in self.items:
            id_label = compound.id
            if id_label == compound_id:
                return compound
        raise KeyError(
            f"Compound Collection: no compound found from compound id: `{compound_id}`"
        )


class ReactionSetUIDCollection(_AbstractUIDCollection[ConversionReaction]):

    @classmethod
    def _get_base_label(cls, item: ConversionReaction) -> str:
        return item.label

    @staticmethod
    def is_kpi(item: ConversionReaction) -> bool:
        """Determines if a variable is a KPI"""
        return False


class VariableUIDCollection(_AbstractUIDCollection[VOBaseVariable]):

    @classmethod
    def _get_base_label(cls, item: VOBaseVariable[Any, Any, Any]) -> str:

        if isinstance(item, VOCompoundMassRatio):
            # NOTE - this label needs to match the base label in CompoundUUIDCOllection because its used as key reference to VOCompoundMassRatio
            return item.compound.label

        elif isinstance(item, VOBaseVariable):
            var_enum: BaseSpecEnum = item.variable_enum
            label = var_enum.stringify
            return inflection.titleize(label)

        else:
            raise TypeError(f"Variable UID Collection not label type not found: {item}")

    @classmethod
    def _transform_base_label(cls, base: str):
        return inflection.titleize(base)

    @staticmethod
    def is_kpi(item: VOBaseVariable) -> bool:
        """Determines if a variable is a KPI"""

        # if item.is_independent == True:
        #     return False

        if not isinstance(item, (VOContinuousVariable, VOCompoundMassRatio)):
            return False

        return True


class KPI(UUIDBase, ReprMixin):
    """'
    Lightweight class that store strings of KPI expressions. Relies on UUID for reference to other collections
    # NOTE - to consider moving this to `entities.py`
    """

    delimiter = " - "

    def __init__(
        self,
        label: str,
        expression: str,
        collection_reference: VariableUIDCollection,
        *,
        flags: List[str] = ["{{", "}}"],
        **kwargs,
    ):
        super().__init__(**kwargs)
        self.label = label
        self._variables: Set[uuid.UUID] = set()
        self._uuid_expression: str = ""
        self._flags: List[str] = flags
        self.delimiter = self.__class__.delimiter
        self.var_uid_collection: VariableUIDCollection = collection_reference
        self._init_uuid_expression(expression)

    # TODO remove var_uid collection, use it as injected reference when needed via KPIUidCollection

    def _init_uuid_expression(self, expression: str) -> None:
        """
        Assumes we are getting a return from KPI Label.
        Stores `expression` with string UUID swap-ins.
        Populates `variables` with UUID objects of variables
        """
        self._uuid_expression = self._swap_vars(
            expression,
            self._transform_kpi_label_to_uuid_label_and_store_UUID,
            keep_flags=True,
        )

    @classmethod
    def get_kpi_label(cls, item: VOBaseVariable) -> str:
        """Given a variable, return a string for the variable as a KPI"""
        entity_label = item.parent.label  # e.g. HT-01

        return f"{entity_label}{cls.delimiter}{cls.get_var_label(item)}"  # HT-01 - Temperature

    @classmethod
    def get_var_label(cls, item: VOBaseVariable) -> str:
        return VariableUIDCollection.get_ui_label(item)

    @property
    def variable_uids(self):
        return self._variables

    def get_uuid_expression(self) -> str:
        return self._uuid_expression

    def get_ui_expression(self) -> str:
        """Get the UI version of a KPI expression"""
        return self._swap_vars(
            self._uuid_expression, self._transform_uid_str_to_kpi_label, keep_flags=True
        )

    def get_kpi_value(self) -> float:
        # Get exprssion without flags and with numbers
        expression = self._swap_vars(
            self._uuid_expression, self._transform_uid_str_to_val, keep_flags=False
        )
        try:
            kpi = eval(str(expression))
            assert isinstance(kpi, (float, int)), f"Error on evaluation return: `{kpi}`"
            return kpi
        except Exception as e:
            raise ValueError(
                f"Expression evaluation failed: {e}, {expression}, {type(expression)}"
            )

    def _swap_vars(
        self, expression, transform: Callable[[str], str], *, keep_flags=True
    ) -> str:

        # Util Func
        def _extract_targets(expression: str) -> Dict[str, Optional[str]]:
            """Extracts unique variables between flag delimiters from an expression.
            Returns a dictionary with variable names as keys and None as initial values.

            Args:
                expression (str): String containing flagged variables

            Returns:
                Dict[str, None]: Dictionary with variable names as keys, all values initialized to None

            Examples:
                >>> _extract_targets("{{X1}} + {{X2}} + {{X1}}")
                {'X1': None, 'X2': None}

                >>> _extract_targets("{{temp}} * {{flow}} / {{pressure}}")
                {'temp': None, 'flow': None, 'pressure': None}

                >>> _extract_targets("No variables")
                {}

            Note:
                Does not support nested flags like {{outer{{inner}}outer}}
                Only extracts non-nested variables between flag pairs
            """

            # Build regex pattern
            start_flag = re.escape(self._flags[0])
            end_flag = re.escape(self._flags[1])
            pattern = f"{start_flag}(.*?){end_flag}"  # use non-greedy

            matches = re.findall(pattern, expression)
            return dict.fromkeys(matches)

        def _swap_in_variables(
            expression: str, replacements: Dict[str, Optional[str]], keep_flags: bool
        ) -> str:
            """Transform variables in expression using replacement mapping

            Example:
                expression: "{{X1}} + {{X2}}"
                replacements: {"X1": "uid1", "X2": "uid2"}
                returns: "{{uid1}} + {{uid2}}"
            """
            result = expression
            for source, replacement in replacements.items():
                if replacement == None:
                    raise ValueError(f"Are you sure {source} is None?")
                old = f"{self._flags[0]}{source}{self._flags[1]}"

                if keep_flags == True:
                    new = f"{self._flags[0]}{replacement}{self._flags[1]}"
                else:
                    new = f"{replacement}"
                result = result.replace(old, new)  # swaps all
            return result

        # LOGIC

        remaps = _extract_targets(expression)
        for original in remaps.keys():
            remaps[original] = transform(original)

        return _swap_in_variables(expression, remaps, keep_flags)

    # Transform Methods
    # simple methods that take in a str and return another str.

    def _transform_uid_str_to_val(self, uid_label: str) -> str:
        uid = uuid.UUID(uid_label)
        var = self.var_uid_collection.get_item(uid)

        return str(var.value)

    def _transform_uid_str_to_kpi_label(self, uid_label: str) -> str:

        uid = uuid.UUID(uid_label)
        var = self.var_uid_collection.get_item(uid)

        if var.parent is None:
            raise AttributeError(
                f"{var} - Variable has no referenced entity. Check its creation process"
            )

        var_label = self.get_var_label(var)

        return f"{var.parent.label}{self.delimiter}{var_label}"

    def _transform_kpi_label_to_uuid_label_and_store_UUID(
        self,
        kpi_label: str,
    ) -> str:
        """
        Given an input string, a list of transforms and a collection reference,
        run the string through the transforms
        Then use the final output to check the collection reference

        HT-01.temperature -> [HT-01, temperature]
        """

        def split_ui_label(label: str) -> Tuple[str, uuid.UUID]:
            """Extract equipment ID and variable UUID from a KPI label.

            Args:
                label: Format "<equipment_id><delim><variable_label>"
                      Example: "HEX-100 - Heat Loss"

            Returns:
                Tuple[str, uuid.UUID]: (equipment_id, variable_uuid)

            Raises:
                KeyError: If matching variable not found
            """
            # Find matching variable by scanning collection
            for var in self.var_uid_collection.items:
                parent_label = var.parent.label
                var_label = self.var_uid_collection.get_ui_label(var)

                # Build expected full label
                expected_label = f"{parent_label}{self.delimiter}{var_label}"

                # Direct full string comparison
                if label.strip() == expected_label:
                    return parent_label, var.uid

            raise KeyError(f"No variable found matching label: {label}")

        clean_label = kpi_label.strip()
        entity_label, var_uid = split_ui_label(clean_label)
        self._variables.add(var_uid)
        return str(var_uid)


class KPIUIDCollection(_AbstractUIDCollection[KPI]):
    def __init__(self, varuid_collection: VariableUIDCollection):
        super().__init__()
        self._var_ref = varuid_collection

    @classmethod
    def _get_base_label(cls, item: KPI) -> str:
        return item.label

    def sync_collection_items(self, uid_collection: VariableUIDCollection):
        """
        A method that checks for all the KPIs in the collection whether their UIDs are valid/exist.
        If the UID does not exist, the KPI object is removed from collection.
        """
        # Update the Variable Collection
        self._var_ref = uid_collection

        # Get all the KPIs in the collection and run through to check if their variables are present in reference collection.
        reference_var_uids = set(var.uid for var in uid_collection.items)
        internal_var_uids = set(*[kpi.variable_uids for kpi in self.items])

        # Vars to remove
        for uid in internal_var_uids - reference_var_uids:
            for item in self.items:
                if uid in item.variable_uids:
                    self.remove_item(item)

        return


class SensorUIDCollection(_AbstractUIDCollection[VOSensor]):
    @classmethod
    def _get_base_label(cls, item: VOSensor) -> str:
        return item.label


####################


# ATLAS
class AtlasRoot(UUIDBase):

    def __init__(
        self,
        label: str,
        plant_id: str,
        user_config: Optional[UserAtlasConfig] = None,
        *,
        description: str = "",
        compound_registry: Optional[Dict[str, VOCompound]] = None,  # ID: name
        **kwargs,
    ):
        # Metadata
        super().__init__(**kwargs)
        self.user_config = user_config or UserAtlasConfig(
            user_industry=UserIndustryEnum.CHEMICAL,
            matrix_engine=MatrixEngineEnum.DWSIM,
        )
        self.label = label
        self._description = description
        self._version = 0
        self._plant_id = plant_id
        self.sensor_timeset_col: Optional[str] = None
        self.sensor_timestep_col: Optional[str] = None

        # Model
        self.G = nx.DiGraph()

        # COLLECTIONS
        # ALL COLLECTIONS REFERENCED BY UUID
        # FIXME
        # write some general method to find UUID aed on front end strong.(reverse lookup)
        # have some function: to_string , from-string (this is a reverse lookup)
        # subclass AbstractCollection -> UUIDCollection. if not found, create and register to collection
        # also, entities hsould live here and parent points in variables to point to UUID instead of parent obj.

        self.compounds_collection: CompoundUIDCollection = CompoundUIDCollection()
        self.reaction_collection: ReactionSetUIDCollection = ReactionSetUIDCollection()
        self.variables_collection: VariableUIDCollection = VariableUIDCollection()
        self.kpi_collection: KPIUIDCollection = KPIUIDCollection(
            self.variables_collection
        )  # this should be unique
        self.sensor_collection: SensorUIDCollection = SensorUIDCollection()
        # self._initialize_compound_registry() # Do not init all compounds

        # SETS
        self.base_equipment_set = TypedSet(ENTBaseEquipment)
        self.base_stream_set = TypedSet(ENTBaseStream)
        self.base_compound_set = TypedSet(VOCompound)
        self._register_base_sets()

    @property
    def plant_id(self) -> str:
        return self._plant_id

    @plant_id.setter
    def plant_id(self, value: str):
        self._plant_id = value

    @property
    def description(self) -> str:
        return self._description

    @property
    def version(self) -> int:
        return self._version

    @version.setter
    def version(self, value: int):
        self._version = value

    @property
    def equipments(self) -> List[str]:
        return [e for e in self.G.nodes]

    @property
    def streams(self) -> List[str]:
        return [edgeobj["data"].label for _, _, edgeobj in self.G.edges(data=True)]

    @property
    def edgelist(self) -> List[Tuple[str, str, str]]:
        """
        Returns a list of connections in the format: source, target, edge
        """
        connections = []
        for edge in self.G.edges(data=True):
            upstream_id, downstream_id, stream_obj = edge
            connections.append((upstream_id, downstream_id, stream_obj["data"].label))
        return connections

    # DUNDERS

    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.label})>"

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, AtlasRoot):
            return NotImplemented
        return (
            self.label == other.label
            and self.version == other.version
            and self.equipments == other.equipments
            and self.streams == other.streams
            and self.compounds == other.compounds
        )

    def __hash__(self) -> int:
        return hash(
            (
                self.label,
                self.version,
                frozenset(self.equipments),
                frozenset(self.streams),
                frozenset(self.compounds),
            )
        )

    # HELPER FUNCTIONS

    ####################
    # LOOKUPS

    def _register_base_sets(self):
        """Loads the sets based on the industry and engine"""
        user_industry = self.user_config.user_industry
        matrix_engine = self.user_config.matrix_engine

        if (
            user_industry == UserIndustryEnum.CHEMICAL
            and matrix_engine == MatrixEngineEnum.DWSIM
        ):
            EQUIPMENTS: List[Type[ENTBaseEquipment]] = [
                BatteryIn,
                BatteryOut,
                Heater,
                Cooler,
                HeatExchanger,
                AirCooler2,
                OrificePlate,
                Compressor,
                Pump,
                Expander,
                Valve,
                StreamMixer,
                Vessel,
                ShortcutColumn,
                ConversionReactor,
            ]
            STREAMS: List[Type[ENTBaseStream]] = [
                MaterialStream,
                InputStream,
                OutputStream,
            ]
            COMPOUNDS: List[VOCompound] = CompoundRef.get_all_dwsim_compounds()

        elif (
            user_industry == UserIndustryEnum.PHARMA
            and matrix_engine == MatrixEngineEnum.GPROMS
        ):
            EQUIPMENTS: List[Type[ENTBaseEquipment]] = [
                Crystallizer,
                SimulationDuration,
                PositionSensitiveDetector,
                TemperatureControl,
            ]
            # No streams for first Pfizer release
            STREAMS: List[Type[ENTBaseStream]] = []
            # No compounds for first Pfizer release as initial conditions like mass fraction cannot be be changed.
            COMPOUNDS: List[VOCompound] = []

        else:
            # If the combination is not found, raise KeyError
            raise KeyError(
                f"Unsupported configuration: Industry={user_industry}, Engine={matrix_engine}"
            )

        # Add to base sets
        for item in EQUIPMENTS:
            self.base_equipment_set.add(item)
        for item in STREAMS:
            self.base_stream_set.add(item)
        for item in COMPOUNDS:
            self.base_compound_set.add(item)

    # COLLECTION - VARIABLES
    def register_entity_variables(self, entity: ENTBase):
        """Register all variables from an entity into the root UID collection"""
        for var in entity.get_variables():

            # [ ]:  hardskiips for variables that shouldn't be exposed for KPI
            hacky_skipset = {
                DiscreteSetSpecEnum.Conversions,
                DiscreteSetSpecEnum.ComponentConversions,
                DiscreteSetSpecEnum.LightKeyCompound,
                DiscreteSetSpecEnum.HeavyKeyCompound,
            }
            if var.variable_enum in hacky_skipset:
                logging.info(
                    f"{var.variable_enum} skipped and not registered to variable registery. It will not be able to be a KPI"
                )
                continue

            self.variables_collection.add_item(var)

    def deregister_variables(self, entity: ENTBase):
        """Remove all variables from an entity from the root UID collection"""
        for var in entity.get_variables():
            self.variables_collection.remove_item(var)

    ####################

    # COLLECTION - COMPOUNDS
    def _initialize_compound_registry(
        self, compound_maps: Optional[Dict[str, str]] = None
    ):
        """Loads all CAS data, currently stored as an Enum"""

        # Iterate through all enums in CompoundEnum and create key:value of it via a reverse dict of the enum
        for compound in CompoundRef.get_all_dwsim_compounds():
            self.register_compound(compound)

    @property
    def compounds(self) -> List[VOCompound]:
        return self.compounds_collection.items

    def retreive_compound(
        self, reference: Union[uuid.UUID, CASCompoundEnum]
    ) -> VOCompound:
        """
        Given a compoundEnum or compound_id, returns the VOCompound object.
        """
        # If CASCOMpoundEnum, get by user label
        # CASE 1: if CASCompoundEnum
        if isinstance(reference, CASCompoundEnum):
            sample = CompoundRef.get_vocompound_from_enum(reference)
            label = self.compounds_collection.get_ui_label(sample)
            uid = self.compounds_collection.get_uid(label)
            return self.compounds_collection.get_item(uid)

        # CASE 2: if uuid
        if isinstance(reference, uuid.UUID):
            return self.compounds_collection.get_item(reference)

        raise TypeError(f"{type(reference)} is wrong type")

    def register_compound(self, compound: VOCompound):
        # DEFENSIVE
        if compound not in self.base_compound_set:
            raise AttributeError(
                f"Compound {compound} not found in base compound set. Cannot register."
            )

        # LOGIC
        if compound not in self.compounds_collection.items:
            self.compounds_collection.add_item(compound)

    def deregister_compound(self, compound_uid: uuid.UUID):
        self.compounds_collection.remove_item(compound_uid)

    ####################t

    # COLLECTION - KPIs
    def propogate_vars_to_uuid_collection(self):
        """
        Pushes all variables in entities to collection.
        Resets collection in process

        Return
            - dictionary of variable collection
        """
        self.variables_collection._collection = {}

        for label in self.equipments:
            entity = self.get_equipment(by_label=label)
            vars = entity.get_variables()
            _ = [self.variables_collection.add_item(var) for var in vars]

        for label in self.streams:
            entity = self.get_stream(by_label=label)
            vars = entity.get_variables()
            _ = [self.variables_collection.add_item(var) for var in vars]

        # Safety
        for k, var in self.variables_collection.collection.items():
            assert (
                k == var.uid
            ), f"Mismatch between Key and Items' UUID: {k} vs {var}:{var.uid}"

        return self.variables_collection.collection

    ####################

    # COLLECTION - REACTIONS

    def add_reaction_to_reactor(
        self, reactor: ConversionReactor, reaction: ConversionReaction
    ):
        """
        Add new variable to a entity. Used when variables in entities are not fixed sets
        e.g. VOReactionStoich or VOCompoundMassMix

        Updates the variable AND registers it to the VarUUIDCollection
        """
        reactor.add_reaction(reaction)
        self.register_reaction(reaction)
        reaction.add_equipment_reference(reactor)

    def remove_reaction_from_reactor(
        self, reactor: ConversionReactor, reaction: ConversionReaction
    ):
        """
        set Variable on an entity
        """
        reactor.remove_reaction(reaction)
        reaction.remove_equipment_reference(reactor)

    def register_reaction(self, reaction: ConversionReaction):
        """
        Add reaction to reaction collection
        Add reaction variables to variable collection
        """
        self.reaction_collection.add_item(reaction)
        self.register_entity_variables(reaction)

    def deregister_reaction(self, item: ConversionReaction):
        """
        Args
            reference: uuid object or string of label
        """
        self.reaction_collection.remove_item(item)
        self.deregister_variables(item)

    def retreive_reaction(self, reference: Union[uuid.UUID, str]):
        """
        Args
            reference: uuid object or string of label
        """
        return self.reaction_collection.get_item(reference)

    ####################

    # HELPER FUNCTIONS

    def _get_equipment_tuple(
        self, equipment: Union[str, ENTBaseEquipment]
    ) -> Tuple[str, ENTBaseEquipment]:
        """
        Retrieves information about the equipment. Used to manage polymorphic user input types of ID as string or actual instance
        """

        if isinstance(equipment, str):
            e_id = equipment
            e_inst = self.get_equipment(by_label=equipment)
        elif isinstance(equipment, ENTBaseEquipment):
            e_id = equipment.label
            e_inst = equipment
        else:
            raise ValueError(
                f"Invalid type: {type(equipment)} for {equipment}. Expected str or EquipmentEntity."
            )

        if not isinstance(e_id, (str, ENTBaseEquipment)) or not isinstance(
            e_inst, ENTBaseEquipment
        ):
            raise ValueError(
                f"_get_equipment_tuple() - {equipment} did not retrieve ID and/or instance"
            )

        return e_id, e_inst

    def _get_stream_tuple(
        self, stream: Union[str, ENTBaseStream]
    ) -> Tuple[str, ENTBaseStream]:
        """
        Retrieves information about the stream. Used to manage polymorphic user input types of ID as string or actual instance
        """

        if isinstance(stream, str):
            s_id = stream
            s_inst = self.get_stream(by_label=s_id)
        elif isinstance(stream, ENTBaseStream):
            s_id = stream.label
            s_inst = stream
        else:
            raise TypeError(
                f"Invalid stream type: {type(stream)} for {stream}. Expected str or MaterialStream."
            )

        # Check for membership
        if not isinstance(s_id, str) or not isinstance(s_inst, ENTBaseStream):
            raise ValueError(
                f"_get_stream_tuple() - {stream} did not retrieve ID and/or instance"
            )

        return s_id, s_inst

    ####################

    # EQUIPMENTS

    @overload
    def add_equipment(
        self,
        equipment_class: Type[ConversionReactor],
        label: str,
        **kwargs,
    ) -> ConversionReactor: ...

    @overload
    def add_equipment(
        self,
        equipment_class: Type[ENTBaseEquipment],
        label: str,
        **kwargs,
    ) -> ENTBaseEquipment: ...

    # @sharedutils.handle_exceptions
    def add_equipment(
        self,
        equipment_class: Type[ENTBaseEquipment],
        label: str,
        **kwargs,
    ) -> ENTBaseEquipment:

        # Defensive
        # FIXME - validation for ID is spread across init (here) and setter (in equip). Consider refactoring so its one callback
        if equipment_class not in self.base_equipment_set:
            raise AttributeError(
                f"Equipment class {equipment_class} not found in base equipment set."
            )
        if label in self.equipments:
            raise ValueError(f"Equipment with ID {label} already exists in the graph.")

        # Create obj
        new_equipment = equipment_class(label, self, **kwargs)

        # Add to graph
        self.G.add_node(new_equipment.label, data=new_equipment)

        # Add to registry
        self.register_entity_variables(new_equipment)
        new_equipment.parent_obj = self

        logging.info(
            f"{sharedutils.get_function_name()} - {self.label} - {new_equipment.label} added to plant"
        )

        return new_equipment

    def get_equipment(
        self,
        *,
        by_label: Optional[str] = None,
        by_input_stream: Optional[Union[str, ENTBaseStream]] = None,
        by_output_stream: Optional[Union[str, ENTBaseStream]] = None,
    ) -> ENTBaseEquipment:
        """Gets an equipment entity by its ID or connected streams.

        Raises:
            KeyError: When equipment cannot be found
            ValueError: When no search criteria provided
        """

        def get_equip_by_stream(
            stream: Union[str, ENTBaseStream], _by_input_stream: bool
        ) -> ENTBaseEquipment:
            if not isinstance(stream, (str, ENTBaseStream)):
                raise TypeError(f"Expected str or MaterialStream, got {type(stream)}")

            stream_id = stream if isinstance(stream, str) else stream.label
            equip_id = next(
                (
                    v if _by_input_stream else u
                    for u, v, edge_data in self.G.edges(data=True)
                    if edge_data["data"].label == stream_id
                ),
                None,
            )

            if equip_id is None:
                raise KeyError(f"No equipment connected to stream '{stream_id}'")

            _, equipment = self._get_equipment_tuple(equip_id)
            if not equipment:
                raise KeyError(f"Equipment '{equip_id}' not found in graph")

            return equipment

        # Logic
        if by_label:
            if by_label not in self.G.nodes:
                raise KeyError(f"Equipment '{by_label}' not found")
            equipment = self.G.nodes[by_label]["data"]

        elif by_input_stream:
            equipment = get_equip_by_stream(by_input_stream, True)

        elif by_output_stream:  # by_output_stream
            equipment = get_equip_by_stream(by_output_stream, False)

        else:
            raise ValueError("Search criteria not found")

        logging.info(f"Equipment retrieved: {equipment.label}")
        return equipment

    def remove_equipment(self, equipment: Union[str, ENTBaseEquipment]):
        """
        Removes the specified equipment from the graph.

        Args:
            equipment (Union[str, EquipmentEntity]): The equipment to remove, specified either as a string ID or an EquipmentEntity instance.

        Returns:
            PlantAggregate: The updated PlantAggregate instance.
        """

        e_id, e_obj = self._get_equipment_tuple(equipment)

        # Disconnect equipment
        in_streams = self.get_streams(filter_by_downstream_equipment=e_obj)
        for s in in_streams:
            e = self.get_equipment(by_output_stream=s)
            e.disconnect_materialstream(material_stream=s)

        out_streams = self.get_streams(filter_by_upstream_equipment=e_obj)
        for s in out_streams:
            e = self.get_equipment(by_input_stream=s)
            e.disconnect_materialstream(material_stream=s)

        # Remove node
        self.G.remove_node(e_id)
        self.deregister_variables(e_obj)

        logging.info(f"{sharedutils.get_function_name()} - {e_id} removed from graph.")

        return self

    ####################

    def _generate_battery_label(self, other_label: str) -> str:
        return "_" + other_label

    # STREAMS
    def add_stream(
        self,
        upstream_equipment: Union[str, ENTBaseEquipment, None],
        downstream_equipment: Union[str, ENTBaseEquipment, None],
        stream_obj: Optional[ENTBaseStream] = None,
        *args,
        stream_type: Type[ENTBaseStream] = MaterialStream,
        **kwargs,
    ) -> ENTBaseStream:
        """
        Sets a stream in the graph.
        If the stream doesn't exist, creates a new edge and adds it to the graph.
        If stream exists, just retreives it

        For input or output streams, set argument to None
        """

        def _connect_stream_to_equipment(
            up_e_obj: ENTBaseEquipment,
            down_e_obj: ENTBaseEquipment,
            stream_obj: ENTBaseStream,
            upstream_port: Optional[int] = None,
            downstream_port: Optional[int] = None,
        ):
            """
            Creates a edge in graph and manages port data via the equipment entities.
            """
            self.G.add_edge(
                u_of_edge=up_e_obj.label,
                v_of_edge=down_e_obj.label,
                data=stream_obj,
            )
            down_e_obj.connect_materialstream(
                material_stream=stream_obj, port_type="in", port_num=downstream_port
            )
            up_e_obj.connect_materialstream(
                material_stream=stream_obj, port_type="out", port_num=upstream_port
            )

        # Get Streams
        up_e_id, up_e_obj = (
            self._get_equipment_tuple(upstream_equipment)
            if upstream_equipment
            else (None, None)
        )
        down_e_id, down_e_obj = (
            self._get_equipment_tuple(downstream_equipment)
            if downstream_equipment
            else (None, None)
        )

        try:
            # STREAM EXISTS
            stream = self.get_stream(by_equipments=(up_e_id, down_e_id))

            # Replace
            if stream_obj is not None:
                stream = stream_obj

        except KeyError:
            # STREAM DOESN"T EXIST
            # DEFENSIVE
            if stream_type not in self.base_stream_set:
                raise AttributeError(
                    f"Stream type {stream_type} not found in base stream set."
                )

            # Create BatteryNodes
            if stream_type == InputStream:  # create BatteryIn
                assert down_e_id is not None, "Should not be None"
                # Create if doesn't exist
                if up_e_id is None:
                    up_e_id = self._generate_battery_label(down_e_id)
                    up_e_obj = self.add_equipment(BatteryIn, up_e_id)

            elif stream_type == OutputStream:  # Create BatteryOut
                assert up_e_id is not None, "Should not be none"
                # Create node if doesn't exist
                if down_e_id is None:
                    down_e_id = self._generate_battery_label(up_e_id)
                    down_e_obj = self.add_equipment(BatteryOut, down_e_id)

            elif stream_type == MaterialStream:
                pass

            else:
                raise RuntimeError(f"{stream_type} not accounted for")

            # Create stream
            assert down_e_id is not None and up_e_id is not None
            stream_label = up_e_id + "->" + down_e_id
            stream = stream_type(stream_label, **kwargs)

            # Connect Topology
            assert up_e_obj is not None and down_e_obj is not None
            _connect_stream_to_equipment(up_e_obj, down_e_obj, stream)

            # Add parent
            stream.parent_obj = self

            # Add to registry
            self.register_entity_variables(stream)

        except Exception as e:
            # UNKNOWN ERROR
            raise RuntimeError(f"Unknown error: {e}")

        return stream

    def get_stream_connections(self, stream_label: str) -> Tuple[str, str]:
        """Return a tuple of the upstream and downstream labels of the equipment"""

        result = None
        for upstream_label, downstream_label, edge_data in self.G.edges(data=True):
            if edge_data["data"].label == stream_label:
                result = (upstream_label, downstream_label)

        if result == None:
            raise ValueError(f"equipments not found for `{stream_label}`")

        return result

    def get_stream(
        self,
        *,
        by_label: Optional[str] = None,
        by_equipments: Optional[
            Tuple[
                Union[str, ENTBaseEquipment, None], Union[str, ENTBaseEquipment, None]
            ]
        ] = None,
    ) -> ENTBaseStream:
        """Gets stream by label or equipment connection.
        For BatteryLimitStreams, specify them as None

        Raises:
            KeyError: Stream not found
            ValueError: Invalid inputs
        """

        def get_by_label(stream_label: str) -> ENTBaseStream:
            stream = next(
                (
                    edge_data["data"]
                    for _, _, edge_data in self.G.edges(data=True)
                    if edge_data["data"].label == stream_label
                ),
                None,
            )
            if stream is None:
                raise KeyError(f"Stream not found: {stream_label}")
            return stream

        def get_by_equipment(
            upstream: Union[str, ENTBaseEquipment, None],
            downstream: Union[str, ENTBaseEquipment, None],
        ) -> ENTBaseStream:
            up_id, _ = (
                self._get_equipment_tuple(upstream)
                if upstream is not None
                else (None, None)
            )
            down_id, _ = (
                self._get_equipment_tuple(downstream)
                if downstream is not None
                else (None, None)
            )

            # Generate labels for none
            if up_id is None and down_id is not None:
                up_id = self._generate_battery_label(down_id)
            if down_id is None and up_id is not None:
                down_id = self._generate_battery_label(up_id)

            if not self.G.has_edge(up_id, down_id):
                raise KeyError(f"No edge: {up_id}->{down_id}")

            return self.G.edges[up_id, down_id]["data"]

        # Main logic

        if by_label:
            stream = get_by_label(by_label)
        elif by_equipments:
            stream = get_by_equipment(*by_equipments)
        else:
            raise ValueError("Must provide either by_label or by_equipments")

        logging.info(f"Stream retrieved: {stream.label}")

        return stream

    @sharedutils.handle_exceptions(return_as_list=True)
    def get_streams(
        self,
        *,
        # Filters
        filter_by_upstream_equipment: Optional[Union[str, ENTBaseEquipment]] = None,
        filter_by_downstream_equipment: Optional[Union[str, ENTBaseEquipment]] = None,
    ) -> List[ENTBaseStream]:
        """
        Get a list of material streams. Match the provided filters if any given.

        Args:
            filter_by_upstream_equipment (Optional[Union[str, EquipmentEntity]]): Filter streams by the upstream equipment.
            filter_by_downstream_equipment (Optional[Union[str, EquipmentEntity]]): Filter streams by the downstream equipment.

        Returns:
            List[MaterialStream]: A list of material streams that match the provided filters.
        """
        upstream_equipment_id = (
            self._get_equipment_tuple(filter_by_upstream_equipment)[0]
            if filter_by_upstream_equipment
            else None
        )
        downstream_equipment_id = (
            self._get_equipment_tuple(filter_by_downstream_equipment)[0]
            if filter_by_downstream_equipment
            else None
        )

        streams = [
            self.G.edges[u, v]["data"]
            for u, v in self.G.edges
            if (not upstream_equipment_id or u == upstream_equipment_id)
            and (not downstream_equipment_id or v == downstream_equipment_id)
        ]

        logging.info(
            f"{sharedutils.get_function_name()} - {len(streams)} items retrieved from graph: {streams}"
        )
        return streams

    @sharedutils.handle_exceptions(return_as_self=True)
    def remove_stream(
        self,
        *,
        by_stream: Optional[Union[str, ENTBaseStream]] = None,
        by_equipment: Optional[
            Tuple[Union[str, ENTBaseEquipment], Union[str, ENTBaseEquipment]]
        ] = None,
    ):
        """
        Removes a stream from the graph based on the provided arguments.
        """

        # HELPER FUNCTIONS
        # Get Stream
        stream = (
            self.get_stream(by_label=by_stream, by_equipments=by_equipment)
            if by_stream == None or isinstance(by_stream, str)
            else by_stream
        )
        assert stream is not None

        # get connected equipt
        e_up = self.get_equipment(by_output_stream=stream)
        e_down = self.get_equipment(by_input_stream=stream)

        self.G.remove_edge(e_up.label, e_down.label)

        if stream.label in [s for s in self.streams]:
            raise ValueError(
                f"Stream {stream.label} not removed. Check implementation."
            )

        self.deregister_variables(stream)

        logging.info(
            f"{sharedutils.get_function_name()} - `{self.label}` - Stream `{stream.label}` removed from graph. The following equipment were affected: `{e_up.label}` and `{e_down.label}`"
        )

    def propogate_compounds_across_streams(self):
        # For edge with no compoundmassmix set, to add it in
        # Simple Version: add all compoundmassmix to registery
        streams = self.get_streams()
        compounds = self.compounds_collection.items

        for s in streams:
            missing_compounds = set(compounds) - set(s.compounds)

            # add as compoundmass variable
            for compound in missing_compounds:
                s.add_variable(VOCompoundMassRatio(compound, 0.0))

            # register
            self.register_entity_variables(s)

    # ####################

    # SERIALIZATION

    @overload
    def serialize_bounds(
        self, vars: List[VOBaseVariable[Any, Any, Any]]
    ) -> pd.DataFrame: ...

    @overload
    def serialize_bounds(
        self, vars: List[VOBaseVariable[Any, Any, Any]], output_type: Literal["numpy"]
    ) -> Tuple[np.ndarray, np.ndarray]: ...

    @overload
    def serialize_bounds(
        self, vars: List[VOBaseVariable[Any, Any, Any]], output_type: Literal["pandas"]
    ) -> pd.DataFrame: ...

    def serialize_bounds(
        self, vars: List[VOBaseVariable[Any, Any, Any]], output_type: str = "pandas"
    ) -> Union[Tuple[np.ndarray, np.ndarray], pd.DataFrame]:
        """
        Transforms a list of VOBaseVariable objects into a numpy array or pandas DataFrame, depending on the specified output type.

        Args:
            vars (List[VOBaseVariable]): A list of VOBaseVariable objects to be serialized.
            output_type (str): The desired output format, either "numpy" or "pandas". Defaults to "pandas".

        Returns:
            Tuple[np.ndarray, np.ndarray: If output_type is "numpy", returns a tuple of two numpy arrays - one for the variable UIDs and one for the variable values.
            pd.DataFrame:  If output_type is "pandas", returns a pandas DataFrame with the variable values as rows and the variable UIDs as columns.

        Raises:
            ValueError: If the output_type is not "numpy" or "pandas".
        """
        uids = [str(var.uid) for var in vars]
        bounds = [
            (
                var.bounds[0] if var.bounds else np.nan,
                var.bounds[1] if var.bounds else np.nan,
            )
            for var in vars
        ]

        if output_type == "numpy":
            return np.array(uids), np.array(bounds, dtype=np.float64)
        elif output_type == "pandas":
            return pd.DataFrame([bounds], columns=uids)
        else:
            raise ValueError(f"Unsupported output type: {output_type}")

    def deserialize_bounds(
        self, data: Union[Tuple[Union[List[str], np.ndarray], np.ndarray], pd.DataFrame]
    ) -> None:
        if isinstance(data, tuple):
            uids, bounds = data
            for uid, (lower, upper) in zip(uids, bounds):
                var_obj = self.variables_collection.get_item(UUID(str(uid)))
                var_obj.bounds = (
                    None if np.isnan(lower) else lower,
                    None if np.isnan(upper) else upper,
                )
        else:
            for uid in data.columns:
                var_obj = self.variables_collection.get_item(UUID(str(uid)))
                bounds_tuple = data.iloc[0][uid]
                lower, upper = bounds_tuple
                var_obj.bounds = (
                    None if np.isnan(lower) else lower,
                    None if np.isnan(upper) else upper,
                )

    @overload
    def serialize_variables(
        self, vars: List[VOBaseVariable[Any, Any, Any]]
    ) -> pd.DataFrame: ...

    @overload
    def serialize_variables(
        self, vars: List[VOBaseVariable[Any, Any, Any]], output_type: Literal["numpy"]
    ) -> Tuple[np.ndarray, np.ndarray]: ...

    @overload
    def serialize_variables(
        self, vars: List[VOBaseVariable[Any, Any, Any]], output_type: Literal["pandas"]
    ) -> pd.DataFrame: ...

    def serialize_variables(
        self, vars: List[VOBaseVariable[Any, Any, Any]], output_type: str = "pandas"
    ) -> Union[Tuple[np.ndarray, np.ndarray], pd.DataFrame]:
        """
        Transforms a list of VOBaseVariable objects into a numpy array or pandas DataFrame, depending on the specified output type.

        Args:
            vars (List[VOBaseVariable]): A list of VOBaseVariable objects to be serialized.
            output_type (str): The desired output format, either "numpy" or "pandas". Defaults to "pandas".

        Returns:
            Tuple[np.ndarray, np.ndarray: If output_type is "numpy", returns a tuple of two numpy arrays - one for the variable UIDs and one for the variable values.
            pd.DataFrame:  If output_type is "pandas", returns a pandas DataFrame with the variable values as rows and the variable UIDs as columns.

        Raises:
            ValueError: If the output_type is not "numpy" or "pandas".
        """

        uids = [str(var.uid) for var in vars]
        values = [var.value for var in vars]

        if output_type == "numpy":
            return np.array(uids), np.array(values, dtype=np.float64)

        elif output_type == "pandas":
            return pd.DataFrame([values], columns=uids)

        else:
            raise ValueError(f"Unsupported output type: {output_type}")

    # def deserialize_values(
    #     self,
    #     data: Union[Tuple[Union[np.ndarray, List[str]], np.ndarray], pd.DataFrame],
    #     row_index: int = 0,
    # ) -> None:
    #     """Transform numpy arrays or pandas DataFrame values back into variable values.

    #     Args:
    #         - data: tuple pair of numpy arrays. data[0] is uuid array, data[1] is value matrix
    #         - data: dataframe of values with uuids as column headers
    #     """
    #     if isinstance(data, tuple):
    #         uids, values = data
    #     else:
    #         uids = data.columns.values
    #         values = data.iloc[row_index].values

    #     for uid, value in zip(uids, values):
    #         var_obj = self.variables_collection.get_item(uuid.UUID(str(uid)))
    #         var_obj.set_value(value)
    def deserialize_values(
        self,
        data: Union[
            Tuple[Union[np.ndarray, List[str]], np.ndarray], pd.DataFrame, pd.Series
        ],
        row_index: int = 0,
    ) -> None:
        """Transform array data, DataFrame or Series into variable values.

        Args:
            - data: tuple pair of numpy arrays (data[0] is uuid array, data[1] is value matrix)
            - data: dataframe of values with uuids as column headers
            - data: pandas Series where index values are uuids
            - row_index: Which row to use if data is DataFrame (ignored for other types)
        """
        if isinstance(data, tuple):
            uids, values = data
        elif isinstance(data, pd.Series):
            # Extract directly from Series
            uids = data.index.values
            values = data.values
        else:  # DataFrame
            uids = data.columns.values
            values = data.iloc[row_index].values

        for uid, value in zip(uids, values):
            var_obj = self.variables_collection.get_item(uuid.UUID(str(uid)))
            var_obj.set_value(value)

    def get_var_from_sensor(self, sensor: VOSensor) -> VOBaseVariable:
        """Retrieves variable object associated to a sensor object.

        Args:
            sensor: Sensor object that variable is associated with.

        Returns:
            Variable object instance associated with sensor.
        """

        var_uid = sensor.variable_uid
        return self.variables_collection.get_item(var_uid)

    def get_sensor_from_var(self, var: VOContinuousVariable) -> Union[VOSensor, None]:
        # TODO In future raise Exception if sensor is not found.
        """Retrieves sensor object associated to a variable object.

        If the variable has no sensor attached, None is returned

        Args:
            var: Variable object that sensor is associated with.

        Returns:
            Sensor object instance associated with variable or None if not found.
        """

        var_uid = var.uid
        associated_sensor = None

        for sensor in self.sensor_collection.items:
            if sensor.variable_uid == var_uid:
                associated_sensor = sensor

        return associated_sensor


# @dataclass
# class AtlasMetadata:
#     """Simple metadata class for the atlas object"""

#     user_id: str  # this is a UID or random string
#     date_created: datetime.datetime
#     date_modified: datetime.datetime
#     date_deleted: Optional[datetime.datetime] = None
#     is_template: bool = False
#     atlas_obj: Optional[AtlasRoot] = None


class UserAtlasConfig(BaseModel):
    user_industry: UserIndustryEnum = Field(UserIndustryEnum.CHEMICAL)
    matrix_engine: MatrixEngineEnum = Field(MatrixEngineEnum.DWSIM)


class AtlasMetadata(BaseModel):
    user_id: str  # this is a UID or random string
    atlas_label: str
    date_created: datetime.datetime
    date_modified: datetime.datetime
    date_deleted: Optional[datetime.datetime] = None
    is_template: bool = False

    # Define the config
    model_config = ConfigDict(
        frozen=True,
        json_encoders={
            datetime.datetime: lambda v: v.astimezone(timezone.utc)
            .isoformat()
            .replace("+00:00", "Z"),
            UUID: lambda v: str(v),
        },
    )

    @field_validator("date_created", "date_modified", "date_deleted", mode="before")
    @classmethod  # Needed in v2
    def parse_datetime(cls, value):
        """Ensure all datetime values are in UTC timezone"""
        # TODO write tests to validate timeformatting is correct
        if value is None:
            return None

        # Case 1: String conversion (ISO format)
        if isinstance(value, str):
            # Handle 'Z' notation or explicit timezone
            dt = datetime.datetime.fromisoformat(value.replace("Z", "+00:00"))
            return (
                dt.replace(tzinfo=timezone.utc)
                if dt.tzinfo is None
                else dt.astimezone(timezone.utc)
            )

        # Case 2: Already datetime object
        if isinstance(value, datetime.datetime):
            # If no timezone set, assume UTC
            if value.tzinfo is None:
                return value.replace(tzinfo=timezone.utc)
            # If timezone set but not UTC, convert to UTC
            return value.astimezone(timezone.utc)

        # Unknown type, let Pydantic handle the error
        return value
