from __future__ import annotations
from backend.core._atlas._imports import *
from backend.core._atlas._singletons import ContVarSpecEnum, DiscreteSetSpecEnum


if TYPE_CHECKING:
    from .entities import ENTBase, ENTBaseEquipment, InputStream
    from .aggregates import AtlasRoot


####################


@runtime_checkable
class _PolicyFunctionProtocol(Protocol):
    """Function signature for policy chains.

    Arguments:
        variable: VOBaseVariable
        value: Any

    Returns:
        _PolicyResult
    """

    def __call__(self, variable: VOBaseVariable, value: Any) -> _PolicyResult: ...


@dataclass
class _PolicyResult:
    value: Any
    is_success: bool


@dataclass
class _PolicyChain:
    """Chain of policy checks, passing the processed value each step of the way"""

    variable: VOBaseVariable
    context_free: List[_PolicyFunctionProtocol]
    context_aware: List[_PolicyFunctionProtocol]
    others: List[_PolicyFunctionProtocol]

    def apply(self, value: Any) -> _PolicyResult:
        """Applies the policy across groups, passing value from one to another"""
        logging.debug(
            f"PolicyChain.apply() - Starting policy chain for {self.variable.variable_enum}"
        )

        result = _PolicyResult(value, True)
        for policy_group in [self.context_free, self.context_aware, self.others]:
            result = self._apply_group(policy_group, value)
            logging.debug(f"PolicyChain.apply() - Group result: {result.is_success}")

            # Early exit
            if result.is_success == False:
                return result

        if result is None:
            raise ValueError(f"check method. Returned none")

        return result

    def _apply_group(
        self,
        policies: List[_PolicyFunctionProtocol],
        value: Any,
    ) -> _PolicyResult:

        # Chain results forward
        result = _PolicyResult(value, True)
        for policy in policies:
            result = self._apply_policy(value, policy)

            # Shortcircuit
            if result.is_success == False:
                return result

            value = result.value

        return result

    def _apply_policy(
        self, value: Any, policy: _PolicyFunctionProtocol
    ) -> _PolicyResult:
        if isinstance(value, (list, tuple)):
            processed_values = []
            all_successful = True
            # Process each value in the sequence
            for v in value:
                result = policy(self.variable, v)

                processed_values.append(result.value)
                all_successful = all_successful and result.is_success

                # Log warning if policy check fails
                if not result.is_success:
                    logging.warning(
                        f"{sharedutils.get_function_name()} - `{self.variable.variable_enum} - value `{v}` not set (failed policy check `{policy.__name__}`)"  # type: ignore
                    )

            return _PolicyResult(type(value)(processed_values), all_successful)

        # Handle single value
        else:
            result = policy(self.variable, value)

            # Log warning if policy check fails
            if not result.is_success:
                logging.warning(
                    f"{sharedutils.get_function_name()} - `{self.variable.variable_enum} - value `{value}` not set (failed policy check `{policy.__name__}`)"  # type: ignore
                )

            return result


####################

TParent = TypeVar("TParent", bound=Union["ENTBase", "AtlasRoot", None])
TVarEnum = TypeVar("TVarEnum", bound=BaseSpecEnum)
TValue = TypeVar("TValue")
TBounds = TypeVar("TBounds", bound=Union[Tuple, Set[Any], None])

# Enum constraint remains the same, as it is useful fo[Any, Any], Set[Any]]], default=None)


class VOBase(UUIDBase, Generic[TParent, TVarEnum, TValue, TBounds], StrMixin):
    """
    A base class for value objects that provides value-based equality semantics while maintaining unique identity.

    Value objects with identical content are considered equal regardless of their UUID.
    Hashing is based on variable id, value and unit(excluding UUID) for consistent dictionary/set operations.
    Deep copying preserves UUID to maintain reference identity.
    UUID is deterministic UUID5 based on parent and variable_id. If no parent, it is then random UUID4

    Generics:
        TParent
        TVarID
        TValue
        TBounds
    """

    def __init__(
        self,
        *,
        variable_enum: TVarEnum,
        parent_obj: Optional[TParent] = None,
        **kwargs,
    ):
        """
        Initializes a new instance of the `VOBase` class.

        Args:
            variable_enum (TVarEnum): The variable enum associated with this value object.
            parent_obj (Optional[TParent]): The parent object for this value object. If provided, the UUID for this object will be derived from the parent's UUID and the `_uuid_hash_str` property.
            **kwargs: Additional keyword arguments to pass to the `UUIDBase` base class.

        Kwargs:
            'uuid_str' = passes a str to create a specific UUID obj

        """
        self._variable_enum: TVarEnum = variable_enum
        self._parent_obj: Optional[TParent] = parent_obj

        super().__init__()

    ####################

    # ABSTRACT METHODS

    @property
    @abstractmethod
    def value(self) -> TValue:
        raise NotImplementedError

    @property
    @abstractmethod
    def category(self) -> VariableCategoryEnum:
        """If variable is condition, setpoint or None"""
        pass

    @abstractmethod
    def set_value(self, value):
        raise NotImplementedError

    ####################

    # STANDARD AND DUNDERS

    @property
    def variable_enum(self) -> TVarEnum:
        return self._variable_enum

    @property
    def unit(self) -> str:
        return self.variable_enum.unit

    @variable_enum.setter
    def variable_enum(self, value):
        self._variable_enum = value

    @property
    # TODO - refactor our parent so var does not need any awareness. move contextaware policy to entity level
    def parent(self) -> Optional[TParent]:
        return self._parent_obj

    @parent.setter
    def parent(self, parent_obj: TParent):
        """
        Assigns parent and updates VO UUID to be deterministic based on parent UUID
        """
        self._parent_obj = parent_obj

    # DUNDERS

    def __repr__(self) -> str:
        return f"`{self.__class__.__name__}<{self.variable_enum}>`"

    # def __str__(self) -> str:
    #     """Returns a string representation showing class name and all attributes"""
    #     attrs = {k: v for k, v in self.__dict__.items() if k != "_uuid"}
    #     return f"{self.__class__.__name__}({', '.join(f'{k}={v!r}' for k,v in attrs.items())})"

    def __eq__(self, other):
        """As VO, ensure equality is only enum and value"""
        if not isinstance(other, self.__class__):
            return NotImplemented
        return self.variable_enum == other.variable_enum and self._make_hashable(
            self.value
        ) == self._make_hashable(other.value)

    def __hash__(self):
        return hash((self.variable_enum, self._make_hashable(self.value)))

    def __deepcopy__(self, memo):
        """Custom deepcopy that preserves UUID while properly handling special types"""
        if id(self) in memo:
            return memo[id(self)]

        cls = self.__class__
        result = cls.__new__(cls)
        memo[id(self)] = result

        for key, value in self.__dict__.items():
            if isinstance(value, Enum):
                setattr(result, key, value)
            elif is_dataclass(value) and not hasattr(value, "__dict__"):
                setattr(result, key, value)
            else:
                setattr(result, key, copy.deepcopy(value, memo))

        return result

    def _make_hashable(self, value):
        """Convert mutable types to immutable for hashing"""
        if isinstance(value, dict):
            return tuple(sorted((k, self._make_hashable(v)) for k, v in value.items()))
        elif isinstance(value, (list, set)):
            return tuple(sorted(self._make_hashable(x) for x in value))
        elif isinstance(value, (tuple, frozenset)):
            return tuple(self._make_hashable(x) for x in value)
        elif isinstance(value, VOCompound):
            return value.id
        return value


class VOCompound(VOBase["AtlasRoot", BaseSpecEnum, float, None], StrMixin):
    """
    A class representing a compound value object.

    - `compound_id`: The unique identifier for the compound, used to map to various endpoints (e.g. CAS number or a company's unique ID).
    - `label`: The human-readable name for the compound, in lower snake case.
    """

    def __init__(self, compound_id: str, label: Optional[str] = None, **kwargs):
        super().__init__(variable_enum=ContVarSpecEnum.DummyCompound, **kwargs)
        self._compound_id = compound_id
        self._label = self._generate_label(label)
        self._molecular_weight = 0.0
        self._formula = "na"
        self.description = "na"

    # ABSTRACT METHODS

    @property
    def value(self):
        """Set the molecular weight of the compound"""
        return self._molecular_weight

    def set_value(self, value: float):
        self._molecular_weight = value

    @property
    def category(self):
        return VariableCategoryEnum.NONE

    # REST

    @property
    def molecular_weight(self) -> float:
        return self._molecular_weight

    @molecular_weight.setter
    def molecular_weight(self, value: float):
        self._molecular_weight = value

    @property
    def formula(self) -> str:
        return self._formula

    @formula.setter
    def formula(self, value: str):
        self._formula = value

    @property
    def id(self) -> str:
        """Returns the compund ID. can be CAS number or client-specific code"""
        return self._compound_id

    @property
    def label(self) -> str:
        """Returns the semantic label of the compound (e.g. "carbon dioxide")"""
        return self._label

    @label.setter
    def label(self, value: str):
        self._label = value

    def __eq__(self, other):
        if not isinstance(other, VOCompound):
            return NotImplemented
        return self.id == other.id and self.label == other.label

    def __hash__(self):
        return hash((self.id, self.label))

    def __repr__(self):
        return f"{self.__class__.__name__}.{self.id}_{self.label}"

    def _generate_label(self, label: Optional[str] = None) -> str:
        if label == None:
            label = self._compound_id + "_label"
        return label


# class Z_VOReactionSet(
#     VOBase["ENTBaseEquipment", BaseSpecEnum, List["z_VOConversionReaction"], None]
# ):
#     # TODO refactor this to be a reaction_collection at entity level
#     # TODO - unify semantics of properties: id = UUID, label = str(no space, one line), description = str(254char)
#     def __init__(
#         self,
#         reactions: Optional[List["z_VOConversionReaction"]] = None,
#         parent_obj: Optional["ENTBaseEquipment"] = None,  # type: ignore
#         **kwargs,
#     ):
#         super().__init__(
#             variable_enum=DiscreteSetSpecEnum.z_ReactionSet,
#             parent_obj=parent_obj,
#             **kwargs,
#         )
#         self.reactions = reactions or []
#         self.bounds = set()

#     @property
#     def value(self):
#         """Set the molecular weight of the compound"""
#         return self.reactions

#     def set_value(
#         self,
#         value: List[z_VOConversionReaction],
#         *,
#         apply_contextaware_policies: bool = True,
#         override_all_policies: bool = False,
#         **kwargs,
#     ) -> _PolicyResult:
#         # [ ] - thos shouldn't need the kwargs, adding because it should be acollection and VarVO
#         self.reactions = value
#         return _PolicyResult(value, True)

#     @property
#     def category(self):
#         return VariableCategoryEnum.NONE

#     def add_reaction(self, reaction):
#         self.reactions.append(reaction)

#     def remove_reaction(self, reaction):
#         self.reactions.pop(reaction)


# class z_VOConversionReaction(
#     VOBase["ENTBaseEquipment", ContVarSpecEnum, Dict[VOCompound, int], None],
#     StrMixin,
# ):
#     def __init__(
#         self,
#         compound_and_stoich: Dict[VOCompound, int],
#         base: VOCompound,
#         reaction_phase: ReactorPhaseEnum,
#         conversion_expression: Union[float, str],
#         label: str = "Label"
#         + str(round(random.random(), 3)),  # [ ] - In case label is not supplied.
#         *,
#         description: Optional[str] = None,
#         **kwargs,
#     ):
#         super().__init__(variable_enum=ContVarSpecEnum.ConversionReaction, **kwargs)
#         self._compound_and_stoich: Dict[VOCompound, int] = (
#             compound_and_stoich if self._validate_stoich(compound_and_stoich) else {}
#         )
#         self._base_compound = base if self._validate_base(base) else None
#         self._reaction_phase = reaction_phase
#         self._conversion = (
#             conversion_expression
#             if self._validate_conversion(conversion_expression)
#             else None
#         )
#         self.description = description
#         self.label = label

#     # DUNDER FOR EQ AND HASHING
#     def __repr__(self) -> str:
#         return f"`{self.__class__.__name__}<{self._compound_and_stoich}>`"

#     def __eq__(self, other):
#         if not isinstance(other, z_VOConversionReaction):
#             return NotImplemented

#         # Compare all relevant attributes
#         return (
#             self.compound_and_stoich == other.compound_and_stoich
#             and self.base_compound == other.base_compound
#             and self.reaction_phase == other.reaction_phase
#             and self.conversion_expression == other.conversion_expression
#         )

#     def __hash__(self):
#         return hash(
#             (
#                 frozenset(self.compound_and_stoich.items()),
#                 self.base_compound,
#                 self.reaction_phase,
#                 self.conversion_expression,
#             )
#         )

#     def __lt__(self, other):
#         """If sorting req, sort by hash"""
#         if not isinstance(other, z_VOConversionReaction):
#             return NotImplemented
#         return hash(self) < hash(other)

#     # ABSTRACT METHODS
#     @property
#     def z_uid_seed(self) -> str:
#         """
#         Name to hash to generate a unique UUID
#         # Setting uniqueness to the compound and stoich makeup
#         """
#         # '[(Water:1), (CarbonDioxide:3),...])'
#         sorted_items = sorted(self.compound_and_stoich.items(), key=lambda x: x[0].id)
#         items_str = "_".join(f"{item[0].id}:{item[1]}" for item in sorted_items)
#         hash_val = hashlib.sha256(items_str.encode()).hexdigest()[:16]
#         return hash_val

#     @property
#     def value(self) -> Dict[VOCompound, int]:
#         """Get the compound stoich of the reaction"""
#         return self.compound_and_stoich

#     def set_value(self, value: Dict[VOCompound, int]):
#         """Set the compound and stoich of the reacion"""
#         self._compound_and_stoich = value

#     @property
#     def category(self):
#         return VariableCategoryEnum.NONE

#     # REST

#     @property
#     def compound_and_stoich(self) -> Dict[VOCompound, int]:
#         return self._compound_and_stoich

#     @compound_and_stoich.setter
#     def compound_and_stoich(self, value: Dict[VOCompound, int]):
#         if not self._validate_stoich(value):
#             raise ValueError(f"Error in stoich amounts. : {value}")
#         self._compound_and_stoich = value

#     @property
#     def base_compound(self) -> VOCompound:
#         if self._base_compound is None:
#             raise ValueError(f"No Base Compound set.")
#         return self._base_compound

#     @base_compound.setter
#     def base_compound(self, value: VOCompound):
#         self._validate_base(value)
#         self._base_compound = value

#     @property
#     def reaction_phase(self):
#         if self._reaction_phase == None:
#             raise ValueError(f"No reaction phase set.")
#         return self._reaction_phase

#     @reaction_phase.setter
#     def reaction_phase(self, phase: ReactorPhaseEnum):
#         if not isinstance(phase, ReactorPhaseEnum):
#             raise TypeError(f"{phase} is of wrong type. Nothing set")
#         self._reaction_phase = phase

#     @property
#     def conversion_expression(self):
#         if self._conversion == None:
#             raise ValueError(f"No expression set.")
#         return self._conversion

#     @conversion_expression.setter
#     def conversion_expression(self, value):
#         self._conversion = value

#     # Validations

#     def _validate_stoich(self, compound_and_stoichcoeff: Dict[VOCompound, int]) -> bool:
#         """
#         Validates the following:
#         - compound is CompoundVO
#         - coeff is numeric, non-zero number. (negatives are allowed)
#         """
#         if not compound_and_stoichcoeff:
#             raise ValueError("compound_and_stoichcoeff cannot be empty")
#         for compound, coeff in compound_and_stoichcoeff.items():
#             if not isinstance(compound, VOCompound):
#                 raise TypeError(f"Invalid compound type: {type(compound)}")
#             if not isinstance(coeff, (int, float)) or coeff == 0:
#                 raise ValueError(f"Invalid coefficient for {compound}: {coeff}")

#         return True

#     def _validate_base(self, base: VOCompound) -> bool:
#         if base not in self._compound_and_stoich:
#             raise ValueError(
#                 f"Base compound {base} must be present in compound_and_stoichcoeff"
#             )
#         return True

#     def _validate_conversion(self, conversion: Union[int, float, str]):
#         # if float, must be between 0 and 1
#         if isinstance(conversion, (int, float)):
#             if not 0 <= conversion <= 1:
#                 raise ValueError("Conversion must be between 0 and 1")
#         return True


####################

# VARIABLES

# TYPE VARIABLES


class VOBaseVariable(
    Generic[TVarEnum, TValue, TBounds],
    VOBase["ENTBase", TVarEnum, TValue, TBounds],
):

    DEFAULT_POLICIES = []

    def __init__(
        self,
        variable_enum: TVarEnum,
        init_value: Optional[TValue] = None,
        bounds: Optional[TBounds] = None,
        *,
        is_independent: bool = True,
        policies: Optional[List[Callable[[VOBaseVariable, Any], _PolicyResult]]] = None,
        description: str = "",
        **kwargs,
    ):
        """
        Initializes a VOBaseVariable instance with the given parameters.

        Args:
            variable_id (TLabel): The unique identifier for the variable.
            init_value (Optional[TValue]): The initial value for the variable.
            bounds (Optional[TBounds]): The bounds for the variable's value.

        Kwargs:
            - is_independent (bool): Whether the variable is independent or not.
            - policies (Optional[List[Callable[[VOBaseVariable, Any], _PolicyResult]]]): The list of policies to apply to the variable's value.
            - description (str): The description for the variable.

        Returns:
            None
        """
        super().__init__(variable_enum=variable_enum, **kwargs)
        self._value: Optional[TValue] = init_value
        self._bounds: Optional[TBounds] = bounds
        self._is_independent: bool = is_independent
        self._policies = (
            policies if policies is not None else self.__class__.DEFAULT_POLICIES.copy()
        )
        self._description = description

        # Removed and simplified to direct assignment on init
        # if init_value != None:
        #     self.set_value(init_value, apply_contextaware_policies=False)

    def __repr__(self) -> str:
        return f"`{self.__class__.__name__}.{self.variable_enum.stringify} <V:`{self.value}`, B:`{self.bounds}`>"

    # CONCERTE METDHOS

    @property
    def value(self):
        return self._value

    def set_value(
        self,
        value: TValue,
        *,
        override_all_policies: bool = False,
        apply_contextaware_policies: bool = True,
        **kwargs,
    ) -> _PolicyResult:
        # EARLY EXIT IF OVERRIDING ALL POLICIES
        should_override = override_all_policies or kwargs.get(
            "override_all_policies", False
        )

        if override_all_policies == True:
            self._value = value
            return _PolicyResult(value, True)

        # Create policy chain groups
        context_free, context_aware, others = [], [], []
        for p in self.policies:
            if hasattr(_ContextFreePolicies, p.__name__):
                context_free.append(p)
            elif hasattr(_ContextAwarePolicies, p.__name__):
                context_aware.append(p)
            else:
                others.append(p)

        # Init policy chain
        policy_chain = _PolicyChain(
            self,
            context_free=context_free,
            context_aware=context_aware if apply_contextaware_policies else [],
            others=others,
        )

        # Apply policies
        result = policy_chain.apply(
            value=copy.deepcopy(value) if value is not None else value,
        )

        # Set value only on success
        if result.is_success == True:
            self._value = result.value
        else:
            logging.warning(
                f"{self.variable_enum} - policy checks failed. `{value}` not set."
            )

        return result

    @property
    def category(self) -> VariableCategoryEnum:
        if not isinstance(self.variable_enum, (ContVarSpecEnum, CompoundMixSpecEnum)):
            return VariableCategoryEnum.NONE

        return self.variable_enum.category

    # REST

    @property
    def description(self) -> str:
        return self._description

    @description.setter
    def description(self, value: str):
        self._description = value

    @property
    def policies(self) -> List[Callable[[VOBaseVariable, Any], _PolicyResult]]:
        return self._policies

    @policies.setter
    def policies(self, value: List[Callable[[VOBaseVariable, Any], _PolicyResult]]):
        self._policies = value

    @property
    def bounds(self) -> Optional[TBounds]:
        return self._bounds

    @bounds.setter
    def bounds(self, bounds: TBounds):
        # Defensive
        if self.value is not None and not self._validate_inbounds(
            self.value, bounds=bounds
        ):
            logging.warning(
                f"Value {self.value} is out of bounds {bounds}.not setting bounds"
            )
            return

        self._bounds = bounds

    @property
    def is_independent(self) -> bool:
        return self._is_independent

    @is_independent.setter
    def is_independent(self, value: bool):
        self._is_independent = value

    # VALIDATIONS

    def _validate_inbounds(self, value: Any, *, bounds: Optional[Any] = None) -> bool:
        """
        Checks if the given value is within the bounds defined for this variable.
        Returns True if value is within bounds, False otherwise.

        TODO refactor bounds to be a bounds class with __contains__(self, value: Any)

        this is to enable `val in variable.bounds` syntax

        """

        '''Sample Refactor
        class Bounds:
            """Type-safe bounds container supporting discrete sets and numeric ranges"""
            def __init__(self, bounds: Union[Set[DiscreteItemEnum], Tuple[float, float], None]):
                self._bounds = bounds

            def __contains__(self, value: Any) -> bool:
                if self._bounds is None:
                    return True
                    
                if isinstance(self._bounds, set):
                    return value in self._bounds
                    
                if isinstance(self._bounds, tuple):
                    lower, upper = self._bounds
                    return (lower is None or value >= lower) and (upper is None or value <= upper)

            @property
            def value(self):
                return self._bounds

        class VOBaseVariable:
            def __init__(self, bounds: Union[Set[DiscreteItemEnum], Tuple[float, float], None]):
                self._bounds = Bounds(bounds)
        '''

        def validate_numeric_bounds() -> bool:
            if value is None:
                return True

            if isinstance(value, (int, float)):
                lower, upper = effective_bounds or (None, None)
                return (lower is None or value >= lower) and (
                    upper is None or value <= upper
                )

            if isinstance(value, (list, tuple)):
                return all(
                    self._validate_inbounds(v, bounds=effective_bounds) for v in value
                )

            raise ValueError(f"Unsupported type for numeric bounds: {type(value)}")

        def validate_set_bounds() -> bool:
            if value is None:
                return True

            return value in effective_bounds

        # LOGIC

        effective_bounds = bounds or self.bounds
        if effective_bounds == None:
            return True
        elif isinstance(effective_bounds, (tuple, list)):
            return validate_numeric_bounds()
        elif isinstance(effective_bounds, set):
            return validate_set_bounds()
        else:
            raise TypeError(
                f"Unsupported bounds type: {type(effective_bounds)} for value: {value}"
            )


####################

# POLICIES


class _ContextAwarePolicies:
    """
    NOTE - this COULD be moved to a batch operation at aggregate level
    Contextaware policies for Entity.set_value() methods.

    These policies require the context of the variable instance to be evaluated.
    It will fail if evaluated during initialization on a Equipment or Stream class.

    All class methods should return a tuple in one of the following formats:
    1. (value, True) - Indicates successful operation
    2. (value, False) - Indicates failed operation

    Note: If False is returned, no action will be set.

    FIXME: when a policy is applied that voids other policies, some decision needs to be made if there is a rollback to previous state or a warning produced.
    """

    @staticmethod
    def no_setval_on_preqreq_not_met(
        instance: "VODiscreteVariable",
        value: DiscreteItemSpecEnum,
        *,
        action: PolicyActionEnum = PolicyActionEnum.NO_OVERRIDE,
    ) -> _PolicyResult:
        """Validates prerequisites are met before allowing discrete value changes"""
        # Skip checks for non-discrete values
        if not isinstance(value, DiscreteItemSpecEnum):
            return _PolicyResult(value, True)

        # Skip if no parent context
        if instance.parent is None:
            return _PolicyResult(value, True)

        # Skip if no prerequisites defined
        if instance.prerequisites_for_selection is None:
            return _PolicyResult(value, True)

        # Handle override action
        if action == PolicyActionEnum.OVERRIDE:
            return _PolicyResult(value, True)

        # Get current discrete values from collection
        collection = instance.parent._collections.get_collection(
            variable_type=VODiscreteVariable
        )
        current_values = {vo.value for vo in collection.items}

        # Edge case: no discrete values set yet
        if len(current_values) == 0:
            logging.warning(
                f"{instance.variable_enum} - value=`{value}` - SKIPPING POLICY: no discrete values set. \n"
                f"Prerequisites: `{instance.prerequisites_for_selection}`\n"
                f"Actual: `{current_values}`"
            )
            return _PolicyResult(instance.value, False)

        # Check if ANY prerequisite set is satisfied
        if not any(
            prereq_set <= current_values
            for prereq_set in instance.prerequisites_for_selection
        ):
            logging.warning(
                f"{instance.variable_enum} - value=`{value}` - SKIPPING POLICY: prerequisites not met. \n"
                f"Prerequisites: `{instance.prerequisites_for_selection}`\n"
                f"Actual: `{current_values}`"
            )
            return _PolicyResult(instance.value, False)

        return _PolicyResult(value, True)

    # @staticmethod
    # def no_setval_on_preqreq_not_met(
    #     instance: "VODiscreteVariable",
    #     value: DiscreteItemEnum,
    #     *,
    #     action: PolicyActionEnum = PolicyActionEnum.NO_OVERRIDE,
    # ) -> _PolicyResult:
    #     """Validates prerequisites are met before allowing discrete value changes"""
    #     # Skip checks for non-discrete values
    #     if not isinstance(value, DiscreteItemEnum):
    #         return _PolicyResult(value, True)

    #     # Skip if no parent context
    #     if instance.parent is None:
    #         return _PolicyResult(value, True)

    #     # Skip if no prerequisites defined
    #     if instance.prerequisites_for_selection is None:
    #         return _PolicyResult(value, True)

    #     # Handle override action
    #     if action == PolicyActionEnum.OVERRIDE:
    #         return _PolicyResult(value, True)

    #     # Get current discrete values from collection
    #     collection = instance.parent._collections.get_collection(DiscreteSetLabelEnum)
    #     current_values = {vo.value for vo in collection.items}

    #     # # Check if any prerequisite set is satisfied
    #     # prereqs_met = any(
    #     #     prereq_set.issubset(current_values)
    #     #     for prereq_set in instance.prerequisites_for_selection
    #     # )
    #     # Check prerequisites
    #     for prereq_set in instance.prerequisites_for_selection:
    #         if not prereq_set.issubset(current_values):
    #             # Prerequisites not met - return False to prevent value change
    #             return _PolicyResult(
    #                 instance.value, False
    #             )  # Return current value instead of new value

    #     return _PolicyResult(value, True)

    # @staticmethod
    # def no_setval_on_preqreq_not_met(
    #     instance: "VODiscreteVariable",
    #     value: DiscreteItemEnum,
    #     *,
    #     action: PolicyActionEnum = PolicyActionEnum.NO_OVERRIDE,
    # ) -> _PolicyResult:

    #     ####################

    #     # DEFENSIVE

    #     if not isinstance(value, DiscreteItemEnum):
    #         logging.info(
    #             f"{sharedutils.get_function_name()} - id:{instance.variable_enum} - policy not applicable to type:`{type(value)}`. Skipping."
    #         )
    #         return _PolicyResult(value, True)

    #     # Skip policy check if no parent context
    #     if instance.parent is None:
    #         logging.debug(
    #             f"{instance.variable_enum} - No parent context found. Skipping policy check."
    #         )
    #         return _PolicyResult(value, True)

    #     valid_actions = [PolicyActionEnum.NO_OVERRIDE, PolicyActionEnum.OVERRIDE]
    #     if action not in valid_actions:
    #         raise ValueError(f"`{action}` invalid. Must be one of: {valid_actions}")

    #     ####################

    #     if action == PolicyActionEnum.OVERRIDE:
    #         return _PolicyResult(value, True)

    #     ### Gate: POLICYACTION = NO_OVERRIDE ###

    #     if instance.prerequisites_for_selection is None:
    #         return _PolicyResult(value, True)

    #     collection = instance.parent._collections.get_collection(DiscreteSetLabelEnum)
    #     curr_discrete_values = {vo.value for vo in collection.items}

    #     # Case: has preqreuiqites but nothing set
    #     if len(curr_discrete_values) == 0:
    #         logging.warning(
    #             f"{instance.variable_enum} - value=`{value}` - SKIPPING POLICY: no discrete values set. \nPrerequisites: `{instance.prerequisites_for_selection}`\nActual: `{curr_discrete_values}`"
    #         )
    #         return _PolicyResult(value, False)

    #     # Case: doesn't fulfill any of prereqs
    #     if (
    #         any(
    #             prerequisite_selection <= curr_discrete_values
    #             for prerequisite_selection in instance.prerequisites_for_selection
    #         )
    #         == False
    #     ):
    #         logging.warning(
    #             f"{instance.variable_enum} - value=`{value}` - SKIPPING POLICY: no discrete values set. \nPrerequisites: `{instance.prerequisites_for_selection}`\nActual: `{curr_discrete_values}`"
    #         )
    #         return _PolicyResult(value, False)

    #     else:
    #         return _PolicyResult(value, True)

    @staticmethod
    def toggle_indep_on_setval(
        instance: "VODiscreteVariable",
        value: DiscreteItemSpecEnum,
        *,
        action: PolicyActionEnum = PolicyActionEnum.NO_OVERRIDE,
    ) -> _PolicyResult:
        """
        Toggles the independence of variables associated with a DiscreteItemEnum value.

        This function is responsible for updating the independence state of variables based on the specification of the provided DiscreteItemEnum value. It handles the case where the provided value is not a DiscreteItemEnum, and also ensures that all required variables exist before attempting to update their independence.

        The function uses a strategy pattern to toggle the independence of the associated variables, and also provides a rollback mechanism in case of any failures during the update process.
        """
        ####################

        # DEFENSIVE
        if not isinstance(value, DiscreteItemSpecEnum):
            logging.info(
                f"{sharedutils.get_function_name()} - id:{instance.variable_enum} - policy not applicable to type:`{type(value)}`. Skipping."
            )
            return _PolicyResult(value, True)

        # Skip policy check if no parent context
        if instance.parent is None:
            logging.debug(
                f"{instance.variable_enum} - No parent context found. Skipping policy check."
            )
            return _PolicyResult(value, True)

        valid_actions = [PolicyActionEnum.NO_OVERRIDE, PolicyActionEnum.OVERRIDE]
        if action not in valid_actions:
            raise ValueError(f"`{action}` invalid. Must be one of: {valid_actions}")

        if action == PolicyActionEnum.OVERRIDE:
            return _PolicyResult(value, True)

        ####################

        # Get specification and verify all parameters exist
        spec = value.get_specification()
        all_params = set(spec.toggle_dep or []) | set(spec.toggle_indep or [])
        for param in all_params:
            if instance.parent.get_variable(param) is None:  # type: ignore
                logging.warning(
                    f"Variable {param} not found in parent. Skipping policy."
                )
                continue

        # Strategy for parameter toggling
        strategy = [
            (spec.toggle_dep, False),
            (spec.toggle_indep, True),
        ]

        # Context Manager for Rollback on Fail
        original_states = {}
        try:
            for variable_ids, is_indep in strategy:
                if variable_ids is None:
                    continue
                for variable_id in variable_ids:
                    variable = instance.parent.get_variable(variable_id)  # type: ignore
                    original_states[variable_id] = variable.is_independent
                    variable.is_independent = is_indep
            return _PolicyResult(value, True)
        except:
            # Rollback Changes
            for variable_id, original_state in original_states.items():
                instance.parent.get_variable(variable_id).is_independent = (  # type: ignore
                    original_state
                )
            return _PolicyResult(value, False)


class _ContextFreePolicies:
    """
    Context-free policies for Entity.set_value() methods.

    These policies do not require the context of the variable instance to be evaluated.

    All class methods should return a tuple in one of the following formats:
    1. (value, True) - Indicates successful operation
    2. (value, False) - Indicates failed operation

    Note: If False is returned, no value will be set.
    """

    @staticmethod
    def no_setval_on_oob_discrete(
        instance: "VODiscreteVariable",
        value: Any,
        *,
        action: PolicyActionEnum = PolicyActionEnum.DO_NOT_SET,
    ) -> _PolicyResult:

        # DEFENSIVE

        valid_actions = [
            PolicyActionEnum.DO_NOT_SET,
            PolicyActionEnum.OVERRIDE,
        ]
        if action not in valid_actions:
            raise ValueError(f"`{action}` invalid. Must be one of: {valid_actions}")

        # LOGIC

        IS_PASS = instance._validate_inbounds(value)

        if action == PolicyActionEnum.DO_NOT_SET:
            return _PolicyResult(value, True if IS_PASS else False)

        if action == PolicyActionEnum.OVERRIDE:
            return _PolicyResult(value, True)

        else:
            raise ValueError(
                f"{instance.variable_enum} - value=`{value}` - fail to execute policy {__class__.__name__} due to {action}. Investigate."
            )

    @staticmethod
    def no_setval_on_zero(
        instance: "VOReactionStoich",
        value: float,
        *,
        action: PolicyActionEnum = PolicyActionEnum.DO_NOT_SET,
    ) -> _PolicyResult:
        if value == 0:
            if action == PolicyActionEnum.DO_NOT_SET:
                return _PolicyResult(value, False)
            else:
                return _PolicyResult(value, True)

        return _PolicyResult(value, True)

    @staticmethod
    def no_setval_on_oob_continuous(
        instance: "VOContinuousVariable",
        value: NumericOrIterableType,
        *,
        action: PolicyActionEnum = PolicyActionEnum.DO_NOT_SET,
    ) -> _PolicyResult:

        # HELPER

        def clip_value(
            value, bounds: Optional[Tuple[Optional[float], Optional[float]]]
        ) -> float:
            if bounds is None:
                return value
            if bounds[0] is None and bounds[1] is not None:
                return min(value, bounds[1])
            if bounds[1] is None and bounds[0] is not None:
                return max(value, bounds[0])
            if bounds[0] is None and bounds[1] is None:
                return value
            if bounds[0] is not None and bounds[1] is not None:
                return max(bounds[0], min(value, bounds[1]))
            return value

        # DEFENSIVE

        valid_actions = [
            PolicyActionEnum.DO_NOT_SET,
            PolicyActionEnum.OVERRIDE,
            PolicyActionEnum.CLIP,
        ]
        if action not in valid_actions:
            raise ValueError(f"`{action}` invalid. Must be one of: {valid_actions}")

        # LOGIC

        IS_PASS = instance._validate_inbounds(value)

        if action == PolicyActionEnum.DO_NOT_SET:
            return _PolicyResult(value, True if IS_PASS else False)

        elif action == PolicyActionEnum.OVERRIDE:
            return _PolicyResult(value, True)

        elif action == PolicyActionEnum.CLIP:
            value = clip_value(value, instance.bounds) if not IS_PASS else value
            return _PolicyResult(value, True)

        else:
            raise ValueError(
                f"{instance.variable_enum} - value=`{value}` - fail to execute policy {__class__.__name__} due to {action}. Investigate."
            )

    @staticmethod
    def no_setval_on_dependent(
        instance: "VOBaseVariable",
        value,
        *,
        action: PolicyActionEnum = PolicyActionEnum.DO_NOT_SET,
    ) -> _PolicyResult:

        valid_actions = [
            PolicyActionEnum.DO_NOT_SET,
            PolicyActionEnum.OVERRIDE,
        ]
        if action not in valid_actions:
            raise ValueError(f"`{action}` invalid. Must be one of: {valid_actions}")

        IS_INDEPENDENT = instance.is_independent

        if action == PolicyActionEnum.DO_NOT_SET:
            return _PolicyResult(value, True if IS_INDEPENDENT else False)

        if action == PolicyActionEnum.OVERRIDE:
            return _PolicyResult(value, True)

        else:
            raise ValueError(
                f"{instance.variable_enum} - value=`{value}` - fail to execute policy {__class__.__name__} due to {action}. Investigate."
            )

    @staticmethod
    def no_setval_on_none(
        instance: "VOBaseVariable",
        value,
        *,
        action: PolicyActionEnum = PolicyActionEnum.DO_NOT_SET,
    ) -> _PolicyResult:
        valid_actions = [
            PolicyActionEnum.DO_NOT_SET,
            PolicyActionEnum.OVERRIDE,
        ]
        if action not in valid_actions:
            raise ValueError(f"`{action}` invalid. Must be one of: {valid_actions}")

        IS_NONE = value == None

        if action == PolicyActionEnum.DO_NOT_SET:
            return _PolicyResult(value, False if IS_NONE else True)

        if action == PolicyActionEnum.OVERRIDE:
            return _PolicyResult(value, True)

        else:
            raise ValueError(
                f"{instance.variable_enum} - value=`{value}` - fail to execute policy {__class__.__name__} due to {action}. Investigate."
            )


####################


class VOContinuousVariable(
    VOBaseVariable[
        ContVarSpecEnum,  # TLabel
        NumericOrIterableType,  # TValue
        Tuple[Optional[float], Optional[float]],  # TBounds
    ],
):
    """
    A collection class for managing properties and parameters with associated logs.

    This class provides functionality for storing, retrieving, and managing logs for
    different properties and parameters. It includes methods for adding logs, setting
    values, retrieving the most recent value, and purging old logs.

    Methods:
        add_log: Adds a log entry to the collection.
        get_value: Returns the most recent value in the collection
        get_bounds: Returns the bounds of the collection

    """

    DEFAULT_POLICIES: List[_PolicyFunctionProtocol] = [  # type: ignore # TODO - fix type hints for VOBaseVariable
        # _ContextFreePolicies.no_setval_on_dependent,
        _ContextFreePolicies.no_setval_on_oob_continuous,
        _ContextFreePolicies.no_setval_on_none,
    ]

    def __init__(
        self,
        variable_enum,
        init_value=None,
        bounds=None,
        **kwargs,
    ):
        super().__init__(
            variable_enum=variable_enum,
            init_value=init_value,
            bounds=bounds,
            **kwargs,
        )

    @property
    def value(self):
        return super().value

    @property
    def category(self) -> VariableCategoryEnum:
        return self.variable_enum.category

    def set_value(self, *args, **kwargs):
        return super().set_value(*args, **kwargs)


class VOSplitStream(VOContinuousVariable):
    DEFAULT_POLICIES: List[_PolicyFunctionProtocol] = [  # type: ignore # TODO - fix type hints for VOBaseVariable
        # _ContextFreePolicies.no_setval_on_dependent,
        _ContextFreePolicies.no_setval_on_oob_continuous,
        _ContextFreePolicies.no_setval_on_none,
    ]

    def __init__(
        self,
        variable_enum,
        init_value=None,
        bounds=None,
        **kwargs,
    ):
        super().__init__(
            variable_enum=variable_enum,
            init_value=init_value,
            bounds=bounds,
            **kwargs,
        )

    @property
    def value(self):
        return super().value

    @property
    def category(self) -> VariableCategoryEnum:
        return self.variable_enum.category

    def set_value(self, *args, **kwargs):
        return super().set_value(*args, **kwargs)


class VOReactionStoich(VOBaseVariable[ContVarSpecEnum, float, Tuple[float, float]]):

    DEFAULT_POLICIES: List[_PolicyFunctionProtocol] = [  # type: ignore # TODO - fix type hints for VOBaseVariable
        _ContextFreePolicies.no_setval_on_none,
        _ContextFreePolicies.no_setval_on_zero,
    ]

    def __init__(
        self,
        compound: VOCompound,
        init_value: float,
        *,
        bounds=(-10_000.0, 10_000.0),
        **kwargs,
    ):
        self._compound = compound
        super().__init__(
            variable_enum=ContVarSpecEnum.ReactionStoich,
            init_value=init_value,
            bounds=bounds,
            **kwargs,
        )

    @property
    def compound(self) -> VOCompound:
        return self._compound

    @property
    def value(self) -> float:
        if self._value is None:
            self._value = 0.0
        return self._value

    @property
    def category(self):
        return VariableCategoryEnum.NONE


class VOCompoundMassRatio(
    VOBaseVariable[  # TParent, TVarId, TValue, TBounds
        CompoundMixSpecEnum,
        float,
        Tuple[float, float],
    ]
):
    def __init__(
        self,
        compound: VOCompound,
        init_value: float,
        *,
        bounds=(0.0, 1_000_000.0),
        **kwargs,
    ):
        self._compound = compound
        super().__init__(
            variable_enum=CompoundMixSpecEnum.COMPOUNDMIX,
            init_value=init_value,
            bounds=bounds,
            **kwargs,
        )

    @property
    def value(self) -> float:
        if self._value is None:
            self._value = 0.0
        return self._value

    @property
    def category(self):
        # Defensive
        if self.parent is None:
            return VariableCategoryEnum.NONE

        # if isinstance(self.parent, "InputStream"):
        # return VariableCategoryEnum.SETPOINT

        return VariableCategoryEnum.SETPOINT

    @property
    def compound(self) -> VOCompound:
        return self._compound


class VODiscreteVariable(
    VOBaseVariable[
        DiscreteSetSpecEnum,  # TVarId
        Union[DiscreteItemSpecEnum, VOCompound],  # TValue
        Union[Set[DiscreteItemSpecEnum], Set[VOCompound]],
    ]
):
    DEFAULT_POLICIES: List[_PolicyFunctionProtocol] = [  # type: ignore
        _ContextFreePolicies.no_setval_on_oob_discrete,
        _ContextFreePolicies.no_setval_on_none,
        # _ContextFreePolicies.no_setval_on_dependent,
        _ContextAwarePolicies.no_setval_on_preqreq_not_met,
        _ContextAwarePolicies.toggle_indep_on_setval,
    ]

    def __init__(
        self,
        variable_enum,
        init_value=None,
        bounds=None,
        *,
        prerequisites_for_selection: Optional[
            Tuple[Set[DiscreteItemSpecEnum], ...]
        ] = None,
        **kwargs,
    ):
        self._prerequisites_for_selection = prerequisites_for_selection
        super().__init__(
            variable_enum=variable_enum,
            init_value=init_value,
            bounds=bounds,
            **kwargs,
        )

    ####################

    # ABSTRACT METHODS

    @property
    def value(self):
        return super().value

    @property
    def category(self):
        """If variable is condition, setpoint or None"""
        return VariableCategoryEnum.NONE

    def set_value(self, *args, **kwargs):
        return super().set_value(*args, **kwargs)

    ####################

    @property
    def prerequisites_for_selection(
        self,
    ) -> Optional[Tuple[Set[DiscreteItemSpecEnum], ...]]:
        return self._prerequisites_for_selection


class VOSensor(UUIDPydanticBase):
    # uid: UUID = Field(
    #     ...,
    #     description="UUID of sensor object",
    #     default_factory=uuid.uuid4,
    #     examples=["sensor-uuid-1", "sensor-uuid-2"],
    # )
    variable_uid: UUID = Field(
        ...,
        description="UUID of variable object that sensor object points to",
        examples=["var-uuid-1, var-uuid-2"],
    )
    # sensor_type: str = Field(
    #     ..., description="Type of sensor", examples=["temperature", "pressure"]
    # )
    label: str = Field(
        ..., description="Name of sensor/sensor column", examples=["S-001"]
    )

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return NotImplemented
        return self.variable_uid == other.variable_uid and self.label == other.label

    def __hash__(self):
        return hash((self.variable_uid, self.label))


class CompoundRef:
    """
    A simple class to reference compounds.
    """

    ID_TO_LABEL: Dict[str, str] = {
        "811-97-2": "R134a",
        "74-82-8": "Methane",
        "74-84-0": "Ethane",
        "74-98-6": "Propane",
        "106-97-8": "N-butane",
        "109-66-0": "N-pentane",
        "110-54-3": "N-hexane",
        "142-82-5": "N-heptane",
        "111-65-9": "N-octane",
        "111-84-2": "N-nonane",
        "124-18-5": "N-decane",
        "1120-21-4": "N-undecane",
        "112-40-3": "N-dodecane",
        "629-50-5": "N-tridecane",
        "629-59-4": "N-tetradecane",
        "629-62-9": "N-pentadecane",
        "544-76-3": "N-hexadecane",
        "629-78-7": "N-heptadecane",
        "593-45-3": "N-octadecane",
        "629-92-5": "N-nonadecane",
        "629-94-7": "N-heneicosane",
        "629-97-0": "N-docosane",
        "638-67-5": "N-tricosane",
        "646-31-1": "N-tetracosane",
        "629-99-2": "N-pentacosane",
        "630-01-3": "N-hexacosane",
        "593-49-7": "N-heptacosane",
        "630-02-4": "N-octacosane",
        "630-03-5": "N-nonacosane",
        "112-95-8": "N-eicosane",
        "75-28-5": "Isobutane",
        "78-78-4": "Isopentane",
        "107-83-5": "2-methylpentane",
        "96-14-0": "3-methylpentane",
        "591-76-4": "2-methylhexane",
        "589-34-4": "3-methylhexane",
        "592-27-8": "2-methylheptane",
        "589-81-1": "3-methylheptane",
        "589-53-7": "4-methylheptane",
        "3221-61-2": "2-methyloctane",
        "2216-33-3": "3-methyloctane",
        "2216-34-4": "4-methyloctane",
        "5911-04-6": "3-methylnonane",
        "871-83-0": "2-methylnonane",
        "17301-94-9": "4-methylnonane",
        "15869-85-9": "5-methylnonane",
        "463-82-1": "Neopentane",
        "75-83-2": "2,2-dimethylbutane",
        "79-29-8": "2,3-dimethylbutane",
        "15869-87-1": "2,2-dimethyloctane",
        "2532-58-3": "Cis-1,3-dimethylcyclopentane",
        "1759-58-6": "Trans-1,3-dimethylcyclopentane",
        "564-02-3": "2,2,3-trimethylpentane",
        "540-84-1": "2,2,4-trimethylpentane",
        "560-21-4": "2,3,3-trimethylpentane",
        "565-75-3": "2,3,4-trimethylpentane",
        "111-01-3": "Squalane",
        "617-78-7": "3-ethylpentane",
        "590-35-2": "2,2-dimethylpentane",
        "565-59-3": "2,3-dimethylpentane",
        "108-08-7": "2,4-dimethylpentane",
        "562-49-2": "3,3-dimethylpentane",
        "464-06-2": "2,2,3-trimethylbutane",
        "619-99-8": "3-ethylhexane",
        "590-73-8": "2,2-dimethylhexane",
        "584-94-1": "2,3-dimethylhexane",
        "589-43-5": "2,4-dimethylhexane",
        "592-13-2": "2,5-dimethylhexane",
        "563-16-6": "3,3-dimethylhexane",
        "583-48-2": "3,4-dimethylhexane",
        "609-26-7": "2-methyl-3-ethylpentane",
        "1067-08-9": "3-methyl-3-ethylpentane",
        "594-82-1": "2,2,3,3-tetramethylbutane",
        "3522-94-9": "2,2,5-trimethylhexane",
        "16747-30-1": "2,4,4-trimethylhexane",
        "1067-20-5": "3,3-diethylpentane",
        "7154-79-2": "2,2,3,3-tetramethylpentane",
        "1186-53-4": "2,2,3,4-tetramethylpentane",
        "1070-87-7": "2,2,4,4-tetramethylpentane",
        "16747-38-9": "2,3,3,4-tetramethylpentane",
        "15869-80-4": "3-ethylheptane",
        "1071-26-7": "2,2-dimethylheptane",
        "7154-80-5": "3,3,5-trimethylheptane",
        "287-92-3": "Cyclopentane",
        "110-82-7": "Cyclohexane",
        "287-23-0": "Cyclobutane",
        "96-37-7": "Methylcyclopentane",
        "1640-89-7": "Ethylcyclopentane",
        "2040-96-2": "N-propylcyclopentane",
        "1638-26-2": "1,1-dimethylcyclopentane",
        "1192-18-3": "Cis-1,2-dimethylcyclopentane",
        "822-50-4": "Trans-1,2-dimethylcyclopentane",
        "3875-51-2": "Isopropylcyclopentane",
        "16747-50-5": "1-methyl-1-ethylcyclopentane",
        "2040-95-1": "N-butylcyclopentane",
        "108-87-2": "Methylcyclohexane",
        "1678-91-7": "Ethylcyclohexane",
        "1678-92-8": "N-propylcyclohexane",
        "1678-93-9": "N-butylcyclohexane",
        "590-66-9": "1,1-dimethylcyclohexane",
        "2207-01-4": "Cis-1,2-dimethylcyclohexane",
        "6876-23-9": "Trans-1,2-dimethylcyclohexane",
        "638-04-0": "Cis-1,3-dimethylcyclohexane",
        "2207-03-6": "Trans-1,3-dimethylcyclohexane",
        "624-29-3": "Cis-1,4-dimethylcyclohexane",
        "2207-04-7": "Trans-1,4-dimethylcyclohexane",
        "3178-22-1": "Tert-butylcyclohexane",
        "493-01-6": "Cis-decahydronaphthalene",
        "493-02-7": "Trans-decahydronaphthalene",
        "74-85-1": "Ethylene",
        "115-07-1": "Propylene",
        "106-98-9": "1-butene",
        "109-67-1": "1-pentene",
        "592-41-6": "1-hexene",
        "592-76-7": "1-heptene",
        "111-66-0": "1-octene",
        "124-11-8": "1-nonene",
        "821-95-4": "1-undecene",
        "872-05-9": "1-decene",
        "112-41-4": "1-dodecene",
        "1120-36-1": "1-tetradecene",
        "629-73-2": "1-hexadecene",
        "112-88-9": "1-octadecene",
        "3452-07-1": "1-eicosene",
        "590-18-1": "Cis-2-butene",
        "624-64-6": "Trans-2-butene",
        "627-20-3": "Cis-2-pentene",
        "646-04-8": "Trans-2-pentene",
        "7688-21-3": "Cis-2-hexene",
        "4050-45-7": "Trans-2-hexene",
        "622-96-8": "P-ethyltoluene",
        "526-73-8": "1,2,3-trimethylbenzene",
        "95-63-6": "1,2,4-trimethylbenzene",
        "108-67-8": "Mesitylene",
        "538-93-2": "Isobutylbenzene",
        "99-87-6": "P-cymene",
        "105-05-5": "P-diethylbenzene",
        "95-93-2": "1,2,4,5-tetramethylbenzene",
        "115-11-7": "Isobutene",
        "563-46-2": "2-methyl-1-butene",
        "563-45-1": "3-methyl-1-butene",
        "513-35-9": "2-methyl-2-butene",
        "763-29-1": "2-methyl-1-pentene",
        "691-38-3": "4-methyl-cis-2-pentene",
        "674-76-0": "4-methyl-trans-2-pentene",
        "15870-10-7": "2-methyl-1-heptene",
        "2980-71-4": "2-methyl-1-nonene",
        "18516-37-5": "2-methyl-1-undecene",
        "18094-01-4": "2-methyl-1-tridecene",
        "29833-69-0": "2-methyl-1-pentadecene",
        "42764-74-9": "2-methyl-1-heptadecene",
        "52254-50-9": "2-methyl-1-nonadecene",
        "110-83-8": "Cyclohexene",
        "463-49-0": "Propadiene",
        "590-19-2": "1,2-butadiene",
        "106-99-0": "1,3-butadiene",
        "78-79-5": "Isoprene",
        "77-73-6": "Dicyclopentadiene",
        "74-86-2": "Acetylene",
        "74-99-7": "Methylacetylene",
        "689-97-4": "Vinylacetylene",
        "503-17-3": "Dimethylacetylene",
        "107-00-6": "Ethylacetylene",
        "71-43-2": "Benzene",
        "108-88-3": "Toluene",
        "100-41-4": "Ethylbenzene",
        "103-65-1": "N-propylbenzene",
        "104-51-8": "N-butylbenzene",
        "108-38-3": "M-xylene",
        "95-47-6": "O-xylene",
        "106-42-3": "P-xylene",
        "98-82-8": "Cumene",
        "611-14-3": "O-ethyltoluene",
        "620-14-4": "M-ethyltoluene",
        "135-98-8": "Sec-butylbenzene",
        "98-06-6": "Tert-butylbenzene",
        "527-84-4": "O-cymene",
        "535-77-3": "M-cymene",
        "135-01-3": "O-diethylbenzene",
        "141-93-5": "M-diethylbenzene",
        "488-23-3": "1,2,3,4-tetramethylbenzene",
        "527-53-7": "1,2,3,5-tetramethylbenzene",
        "2870-04-4": "2-ethyl-m-xylene",
        "1758-88-9": "2-ethyl-p-xylene",
        "874-41-9": "4-ethyl-m-xylene",
        "934-80-5": "4-ethyl-o-xylene",
        "1074-43-7": "1-methyl-3-n-propylbenzene",
        "1074-55-1": "1-methyl-4-n-propylbenzene",
        "100-18-5": "P-diisopropylbenzene",
        "100-42-5": "Styrene",
        "91-20-3": "Naphthalene",
        "90-12-0": "1-methylnaphthalene",
        "91-57-6": "2-methylnaphthalene",
        "605-02-7": "1-phenylnaphthalene",
        "83-32-9": "Acenaphthene",
        "86-73-7": "Fluorene",
        "85-01-8": "Phenanthrene",
        "206-44-0": "Fluoranthene",
        "129-00-0": "Pyrene",
        "218-01-9": "Chrysene",
        "92-52-4": "Biphenyl",
        "95-13-6": "Indene",
        "496-11-7": "Indane",
        "767-59-9": "1-methylindene",
        "2177-47-1": "2-methylindene",
        "132259-10-0": "Air",
        "630-08-0": "Carbon monoxide",
        "124-38-9": "Carbon dioxide",
        "7783-06-4": "Hydrogen sulfide",
        "10102-43-9": "Nitric oxide",
        "10102-44-0": "Nitrogen dioxide",
        "10024-97-2": "Nitrous oxide",
        "7446-09-5": "Sulfur dioxide",
        "7446-11-9": "Sulfur trioxide",
        "10544-73-7": "Nitrogen trioxide",
        "10544-72-6": "Nitrogen tetroxide",
        "7440-59-7": "Helium-4",
        "7782-41-4": "Fluorine",
        "7439-90-9": "Krypton",
        "7440-63-3": "Xenon",
        "10028-15-6": "Ozone",
        "463-58-1": "Carbonyl sulfide",
        "50-00-0": "Formaldehyde",
        "75-07-0": "Acetaldehyde",
        "78-84-2": "2-methylpropanal",
        "123-38-6": "Propanal",
        "123-72-8": "Butanal",
        "110-62-3": "Pentanal",
        "66-25-1": "Hexanal",
        "111-71-7": "Heptanal",
        "109-87-5": "Methylal",
        "14371-10-9": "t-Cinnamaldehyde",
        "100-52-7": "Benzaldehyde",
        "107-02-8": "Acrolein",
        "124-13-0": "Octanal",
        "124-19-6": "Nonanal",
        "112-31-2": "Decanal",
        "112-44-7": "Undecanal",
        "112-54-9": "Dodecanal",
        "10486-19-8": "Tridecanal",
        "124-25-4": "Tetradecanal",
        "2765-11-9": "Pentadecanal",
        "629-80-1": "Hexadecanal",
        "629-90-3": "Heptadecanal",
        "638-66-4": "Octadecanal",
        "17352-32-8": "Nonadecanal",
        "67-64-1": "Acetone",
        "78-93-3": "Methyl ethyl ketone",
        "96-22-0": "3-pentanone",
        "563-80-4": "Methyl isopropyl ketone",
        "108-94-1": "Cyclohexanone",
        "108-10-1": "Methyl isobutyl ketone",
        "106-35-4": "3-heptanone",
        "123-19-3": "4-heptanone",
        "589-38-8": "3-hexanone",
        "107-87-9": "2-pentanone",
        "591-78-6": "2-hexanone",
        "110-43-0": "2-heptanone",
        "110-12-3": "5-methyl-2-hexanone",
        "75-97-8": "3,3-dimethyl-2-butanone",
        "108-83-8": "Diisobutyl ketone",
        "565-80-0": "Diisopropyl ketone",
        "463-51-4": "Ketene",
        "141-79-7": "Mesityl oxide",
        "67-56-1": "Methanol",
        "64-17-5": "Ethanol",
        "71-23-8": "1-propanol",
        "71-36-3": "1-butanol",
        "71-41-0": "1-pentanol",
        "111-27-3": "1-hexanol",
        "111-70-6": "1-heptanol",
        "111-87-5": "1-octanol",
        "143-08-8": "1-nonanol",
        "112-30-1": "1-decanol",
        "112-42-5": "1-undecanol",
        "112-53-8": "1-dodecanol",
        "112-70-9": "1-tridecanol",
        "112-72-1": "1-tetradecanol",
        "629-76-5": "1-pentadecanol",
        "36653-82-4": "1-hexadecanol",
        "1454-85-9": "1-heptadecanol",
        "112-92-5": "1-octadecanol",
        "1454-84-8": "1-nonadecanol",
        "629-96-9": "1-eicosanol",
        "120-82-1": "1,2,4-trichlorobenzene",
        "541-73-1": "M-dichlorobenzene",
        "95-50-1": "O-dichlorobenzene",
        "106-46-7": "P-dichlorobenzene",
        "108-90-7": "Monochlorobenzene",
        "108-86-1": "Bromobenzene",
        "74-88-4": "Methyl iodide",
        "591-50-4": "Iodobenzene",
        "74-89-5": "Methylamine",
        "75-04-7": "Ethylamine",
        "75-50-3": "Trimethylamine",
        "109-89-7": "Diethylamine",
        "121-44-8": "Triethylamine",
        "108-18-9": "Diisopropylamine",
        "75-31-0": "Isopropylamine",
        "110-86-1": "Pyridine",
        "62-53-3": "Aniline",
        "106-50-3": "P-phenylenediamine",
        "107-15-3": "Ethylenediamine",
        "140-31-8": "N-aminoethyl piperazine",
        "111-40-0": "Diethylenetriamine",
        "110-85-0": "Piperazine",
        "74-90-8": "Hydrogen cyanide",
        "75-05-8": "Acetonitrile",
        "107-13-1": "Acrylonitrile",
        "126-98-7": "Methacrylonitrile",
        "107-12-0": "Propionitrile",
        "98-95-3": "Nitrobenzene",
        "75-52-5": "Nitromethane",
        "79-24-3": "Nitroethane",
        "108-03-2": "1-nitropropane",
        "79-46-9": "2-nitropropane",
        "627-05-4": "1-nitrobutane",
        "88-72-2": "O-nitrotoluene",
        "99-99-0": "P-nitrotoluene",
        "99-08-1": "M-nitrotoluene",
        "121-14-2": "2,4-dinitrotoluene",
        "606-20-2": "2,6-dinitrotoluene",
        "610-39-9": "3,4-dinitrotoluene",
        "619-15-8": "2,5-dinitrotoluene",
        "618-85-9": "3,5-dinitrotoluene",
        "118-96-7": "2,4,6-trinitrotoluene",
        "75-08-1": "Ethyl mercaptan",
        "74-93-1": "Methyl mercaptan",
        "107-03-9": "N-propyl mercaptan",
        "75-66-1": "Tert-butyl mercaptan",
        "513-44-0": "Isobutyl mercaptan",
        "513-53-1": "Sec-butyl mercaptan",
        "111-31-9": "N-hexyl mercaptan",
        "75-33-2": "Isopropyl mercaptan",
        "108-98-5": "Phenyl mercaptan",
        "75-15-0": "Carbon disulfide",
        "75-18-3": "Dimethyl sulfide",
        "110-02-1": "Thiophene",
        "624-89-5": "Methyl ethyl sulfide",
        "3877-15-4": "Methyl n-propyl sulfide",
        "6163-64-0": "Methyl t-butyl sulfide",
        "13286-92-5": "Methyl t-pentyl sulfide",
        "111-47-7": "Di-n-propyl sulfide",
        "352-93-2": "Diethyl sulfide",
        "110-81-6": "Diethyl disulfide",
        "624-92-0": "Dimethyl disulfide",
        "629-19-6": "Di-n-propyl disulfide",
        "110-06-5": "Di-tert-butyl disulfide",
        "20333-39-5": "Ethyl methyl disulfide",
        "30453-31-7": "Ethyl propyl disulfide",
        "882-33-7": "Diphenyl disulfide",
        "95-15-8": "Benzothiophene",
        "69-72-7": "Salicylic acid",
        "119-36-8": "Methyl salicylate",
        "108-65-6": "Propylene glycol monomethyl ether acetate",
        "98-01-1": "Furfural",
        "616-38-6": "Dimethyl carbonate",
        "105-58-8": "DiEthyl Carbonate",
        "623-53-0": "Methyl Ethyl Carbonate",
        "13509-27-8": "Methyl Phenyl Carbonate",
        "3878-46-4": "Ethyl Phenyl Carbonate",
        "102-09-0": "DiPhenyl Carbonate",
        "96-49-1": "Ethylene carbonate",
        "108-32-7": "Propylene carbonate",
        "542-52-9": "DiButyl Carbonate",
        "110-80-5": "2-ethoxyethanol",
        "107-98-2": "Propylene glycol monomethyl ether",
        "53716-82-8": "Cyrene",
        "123-42-2": "Diacetone alcohol",
        "67-63-0": "Isopropanol",
        "78-83-1": "2-methyl-1-propanol",
        "78-92-2": "2-butanol",
        "75-65-0": "2-methyl-2-propanol",
        "75-85-4": "2-methyl-2-butanol",
        "6032-29-7": "2-pentanol",
        "137-32-6": "2-methyl-1-butanol",
        "75-84-3": "2,2-dimethyl-1-propanol",
        "625-25-2": "2-Methyl-2-Heptanol",
        "108-93-0": "Cyclohexanol",
        "108-95-2": "Phenol",
        "108-39-4": "M-cresol",
        "95-48-7": "O-cresol",
        "106-44-5": "P-cresol",
        "4286-23-1": "P-isopropenyl phenol",
        "122-97-4": "3-phenyl-1-propanol",
        "100-51-6": "Benzyl alcohol",
        "526-75-0": "2,3-xylenol",
        "105-67-9": "2,4-xylenol",
        "95-87-4": "2,5-xylenol",
        "576-26-1": "2,6-xylenol",
        "95-65-8": "3,4-xylenol",
        "108-68-9": "3,5-xylenol",
        "527-60-6": "Mesitol",
        "599-64-4": "P-cumylphenol",
        "107-21-1": "Ethylene glycol",
        "111-46-6": "Diethylene glycol",
        "112-27-6": "Triethylene glycol",
        "112-60-7": "Tetraethylene glycol",
        "110-63-4": "1,4-butanediol",
        "56-81-5": "Glycerol",
        "57-55-6": "1,2-propylene glycol",
        "80-05-7": "Bisphenol a",
        "463-57-0": "Methane-diol",
        "4433-56-1": "Ethane-1,1-diol",
        "837-08-1": "O,p-bisphenol a",
        "4792-15-8": "PentaEthylene Glycol",
        "2615-15-8": "HexaEthylene Glycol",
        "110-98-5": "DiPropylene Glycol",
        "1638-16-0": "TriPropylene Glycol",
        "24800-25-7": "TetraPropylene Glycol",
        "21482-12-2": "PentaPropylene Glycol",
        "74388-92-4": "HexaPropylene Glycol",
        "64-19-7": "Acetic acid",
        "79-09-4": "Propionic acid",
        "107-92-6": "N-butyric acid",
        "64-18-6": "Formic acid",
        "142-62-1": "Caproic acid",
        "124-07-2": "Caprylic acid",
        "334-48-5": "Capric acid",
        "143-07-7": "Lauric acid",
        "544-63-8": "Myristic acid",
        "57-10-3": "Palmitic acid",
        "57-11-4": "Stearic acid",
        "79-10-7": "Acrylic acid",
        "79-41-4": "Methacrylic acid",
        "112-80-1": "Oleic acid",
        "60-33-3": "Linoleic acid",
        "463-40-1": "Linolenic acid",
        "144-62-7": "Oxalic acid",
        "124-04-9": "Adipic acid",
        "110-16-7": "Maleic acid",
        "141-82-2": "Malonic acid",
        "65-85-0": "Benzoic acid",
        "118-90-1": "O-toluic acid",
        "99-94-5": "P-toluic acid",
        "88-99-3": "Phthalic acid",
        "100-21-0": "Terephthalic acid",
        "140-10-3": "t-Cinnamic acid",
        "108-24-7": "Acetic anhydride",
        "108-31-6": "Maleic anhydride",
        "107-31-3": "Methyl formate",
        "109-94-4": "Ethyl formate",
        "110-74-7": "N-propyl formate",
        "2551-62-4": "Sulfur hexafluoride",
        "79-20-9": "Methyl acetate",
        "141-78-6": "Ethyl acetate",
        "109-60-4": "N-propyl acetate",
        "108-21-4": "Isopropyl acetate",
        "123-86-4": "N-butyl acetate",
        "110-19-0": "Isobutyl acetate",
        "628-63-7": "N-pentyl acetate",
        "108-05-4": "Vinyl acetate",
        "142-92-7": "N-hexyl acetate",
        "122-79-2": "Phenyl acetate",
        "554-12-1": "Methyl propionate",
        "108-59-8": "Dimethylmalonate",
        "106-70-7": "Methyl caproate",
        "111-11-5": "Methyl caprylate",
        "110-42-9": "Methyl caprate",
        "111-82-0": "Methyl laurate",
        "124-10-7": "Methyl myristate",
        "112-39-0": "Methyl palmitate",
        "112-61-8": "Methyl stearate",
        "538-23-8": "Tricaprylin",
        "621-71-6": "Tricaprin",
        "538-24-9": "Trilaurin",
        "555-45-3": "Trimyristin",
        "555-44-2": "Tripalmitin",
        "555-43-1": "Tristearin",
        "80-62-6": "Methyl methacrylate",
        "112-62-9": "Methyl oleate",
        "112-63-0": "Methyl linoleate",
        "301-00-8": "Methyl linolenate",
        "122-32-7": "Triolein",
        "537-40-6": "Trilinolein",
        "14465-68-0": "Trilenollenin",
        "120-61-6": "Dimethyl terephthalate",
        "93-89-0": "Ethyl benzoate",
        "115-10-6": "Dimethyl ether",
        "60-29-7": "Diethyl ether",
        "1634-04-4": "Methyl tert-butyl ether",
        "994-05-8": "Methyl tert-pentyl ether",
        "108-20-3": "Diisopropyl ether",
        "142-96-1": "Di-n-butyl ether",
        "6863-58-7": "Di-sec-butyl ether",
        "540-67-0": "Methyl ethyl ether",
        "557-17-5": "Methyl n-propyl ether",
        "1860-27-1": "Isopropyl butyl ether",
        "625-44-5": "Methyl isobutyl ether",
        "598-53-8": "Methyl isopropyl ether",
        "637-92-3": "Tert-butyl ethyl ether",
        "919-94-8": "Ethyl tert-pentyl ether",
        "111-34-2": "Butyl vinyl ether",
        "76589-16-7": "2-Methoxy-2-Methyl-Heptane",
        "100-66-3": "Anisole",
        "103-73-1": "Phenetole",
        "112-36-7": "Diethylene glycol diethyl ether",
        "101-84-8": "Diphenyl ether",
        "110-71-4": "1,2-dimethoxyethane",
        "111-96-6": "Diethylene glycol dimethyl ether",
        "112-49-2": "Triethylene glycol dimethyl ether",
        "143-24-8": "Tetraethylene glycol dimethyl ether",
        "1191-87-3": "Pentaethylene glycol dimethyl ether",
        "75-21-8": "Ethylene oxide",
        "109-99-9": "Tetrahydrofuran",
        "123-91-1": "1,4-dioxane",
        "75-56-9": "1,2-propylene oxide",
        "80-15-9": "Cumene hydroperoxide",
        "94-36-0": "Benzoyl peroxide",
        "80-43-3": "Dicumyl peroxide",
        "98-49-7": "P-diisopropylbenzene hydroperoxide",
        "3071-32-7": "Ethylbenzene hydroperoxide",
        "56-23-5": "Carbon tetrachloride",
        "67-66-3": "Chloroform",
        "74-87-3": "Methyl chloride",
        "79-01-6": "Trichloroethylene",
        "75-01-4": "Vinyl chloride",
        "79-00-5": "1,1,2-trichloroethane",
        "75-34-3": "1,1-dichloroethane",
        "107-06-2": "1,2-dichloroethane",
        "75-00-3": "Ethyl chloride",
        "68-12-2": "N,n-dimethylformamide",
        "127-19-5": "N,n-dimethylacetamide",
        "141-43-5": "Monoethanolamine",
        "111-42-2": "Diethanolamine",
        "102-71-6": "Triethanolamine",
        "111-41-1": "N-aminoethyl ethanolamine",
        "109-83-1": "Methylethanolamine",
        "108-01-0": "Dimethylethanolamine",
        "105-59-9": "Methyl DiEthanolAmine",
        "100-37-8": "Diethylethanolamine",
        "110-97-4": "Diisopropanolamine",
        "872-50-4": "N-methyl-2-pyrrolidone",
        "126-33-0": "Sulfolane",
        "67-68-5": "Dimethyl sulfoxide",
        "75-44-5": "Phosgene",
        "76-02-8": "Trichloroacetyl chloride",
        "79-36-7": "Dichloroacetyl chloride",
        "75-87-6": "Trichloroacetaldehyde",
        "79-02-7": "Dichloroacetaldehyde",
        "75-36-5": "Acetyl chloride",
        "107-07-3": "2-chloroethanol",
        "7440-37-1": "Argon",
        "7726-95-6": "Bromine",
        "7782-50-5": "Chlorine",
        "1333-74-0": "Hydrogen",
        "7440-01-9": "Neon",
        "7727-37-9": "Nitrogen",
        "7782-44-7": "Oxygen",
        "7439-97-6": "Mercury",
        "7647-01-0": "Hydrogen chloride",
        "10034-85-2": "Hydrogen iodide",
        "7697-37-2": "Nitric acid",
        "7664-41-7": "Ammonia",
        "7722-84-1": "Hydrogen peroxide",
        "7732-18-5": "Water",
        "4780-79-4": "1-Naphthalenemethanol",
    }
    LABEL_TO_ID = {v: k for k, v in ID_TO_LABEL.items()}
    DWSIM_MASK = []


    @classmethod
    def get_labels(cls):
        return list(cls.ID_TO_LABEL.values())

    @classmethod
    def get_label_from_id(cls, id: str):
        return cls.ID_TO_LABEL[id]

    @classmethod
    def get_id_from_label(cls, label: str):
        return cls.LABEL_TO_ID[label]

    @classmethod
    def get_all_dwsim_compounds(cls):
        compounds: List[VOCompound] = []
        for k, v in cls.ID_TO_LABEL.items():
            if k in cls.DWSIM_MASK:
                continue
            compounds.append(VOCompound(k, v))
        return compounds

    #TODO ZL - create a get all pfizer compounds. Consider 
    @classmethod
    def get_all_gprom_compounds(cls):

        return [
                VOCompound("c1", "pfixer s"),
                VOCompound("c1", "pfixer s"),
                VOCompound("c1", "pfixer s"),
                VOCompound("c1", "pfixer s"),
                VOCompound("c1", "pfixer s"),
                VOCompound("c1", "pfixer s")
        ]
        ...

    @classmethod
    def get_vocompound_from_label(cls, ref: str) -> VOCompound:
        if ref in cls.ID_TO_LABEL:
            label = cls.ID_TO_LABEL[ref]
            return VOCompound(ref, label)
        elif ref in cls.LABEL_TO_ID:
            id = cls.LABEL_TO_ID[ref]
            return VOCompound(id, ref)

        else:
            raise KeyError(f"`{ref}` not found in {cls.__name__}")

    @classmethod
    def get_vocompound_from_enum(cls, compound_enum: CASCompoundEnum):
        compound_id = compound_enum.get_cas()
        return cls.get_vocompound_from_label(compound_id)
