from __future__ import annotations
import uuid
from abc import abstractmethod
from dataclasses import dataclass, field
from enum import Enum, auto, unique
from typing import (
    Any,
    Callable,
    List,
    Optional,
    Tuple,
    Type,
    Union,
    TypeVar,
    Set,
    FrozenSet,
)
from copy import deepcopy
from backend.core._sharedutils.mixins import ReprMixin

####################

# HELPER FUNCTIONS


def get_enum_by_value(cls: Type[Enum], value: Any) -> Enum:
    """
    Given a value (CAS number), return the corresponding enum instance.
    """
    for member in cls:
        if member.value.cas == value:
            return member
    raise ValueError(f"No compound found with CAS number: {value}")


def get_enum_by_name(cls: Type[Enum], name: str) -> Enum:
    """Given a name, return the corresponding enum instance."""
    return cls[name]


####################

# BASE CLASSES


E = TypeVar("E", bound="BaseGenericEnum")


class _BaseSpecificationSingleton:
    """
    A base class for singleton-like objects that have a unique UUID identifier.

    The `VOBase` class provides a base implementation for objects that should behave
    like singletons, where only one instance of the object exists and is reused
    across the application. It provides methods for equality comparison and hashing
    that are based on the object's attributes, as well as a custom `__deepcopy__`
    method that returns the same instance instead of creating a new copy.

    This class is intended to be subclassed by other singleton-like classes in the
    application, providing a consistent foundation for these types of objects.
    """

    def __init__(self, **kwargs) -> None:
        super().__init__(**kwargs)

    def __eq__(self, other):
        if isinstance(other, self.__class__):
            # Equality based on attributes in the __dict__
            return self.__dict__ == other.__dict__
        return NotImplemented

    def __hash__(self):
        # Hash based on immutable attributes in __dict__
        return id(self)
        # return hash(tuple((k, v) for k, v in self.__dict__.items()))

    def __deepcopy__(self, memo):
        # Avoid creating a new instance in deepcopy, return the same singleton instance
        # memo[id(self)] = self
        return self


class BaseGenericEnum(Enum):

    @classmethod
    def from_stringify(cls: Type[E], key) -> E:  # type: ignore
        """Returns a class from the string of an enum"""
        for enum in cls:
            if enum.stringify == key:
                return enum
        raise ValueError(f"No enum found with stringify value: {key}")

    @property
    def stringify(self) -> str:
        """Given an enum, return a string. used for ui labels, db, etc"""
        return self._get_stringify()

    def _get_stringify(self) -> str:
        """Base implementation, returns the enum name. Used for ui labels, etc"""
        return self.name

    def __deepcopy__(self, memo):
        """
        Overrides the default deepcopy behavior for this class, returning the instance itself instead of creating a new copy.
        This is a common pattern for singleton-like classes where you want to ensure only a single instance exists.
        """
        return self


class BaseSpecEnum(BaseGenericEnum):
    """
    Base class for singleton-type classes that represent a collection label.
    e.g. CalculationMode, etc.

    Each class should also return a specification of how to initialize itself.
    """

    @abstractmethod
    def get_specification(self):
        raise NotImplementedError

    @property
    @abstractmethod
    def unit(self) -> str:
        raise NotImplementedError

    @property
    @abstractmethod
    def category(self) -> VariableCategoryEnum:
        """Indicates if ts a setpoint or not"""
        raise NotImplementedError

    @abstractmethod
    def _get_stringify(self) -> str:
        raise NotImplementedError


class BaseSelectionSpecification(BaseGenericEnum):

    @abstractmethod
    def get_specification(self):
        raise NotImplementedError


####################


@unique
class PropertyPackageEnum(BaseGenericEnum):
    PENG_ROBINSON = "Peng-Robinson (PR)"
    COOL_PROP = "CoolProp"
    IAPWS = "IAPWS-08 Seawater"
    MODIFIED_UNIFAC = "Modified UNIFAC (NIST)"
    NRTL = "NRTL"
    PENG_ROBINSON_1978 = "Peng-Robinson 1978 (PR78)"
    PENG_ROBINSON_SV2M = "Peng-Robinson-Stryjek-Vera 2 (PRSV2-M)"
    PENG_ROBINSON_SV2VL = "Peng-Robinson-Stryjek-Vera 2 (PRSV2-VL)"
    RAOULT_LAW = "Raoult's Law"
    SRK = "Soave-Redlich-Kwong (SRK)"
    STEAM_TABLES = "Steam Tables (IAPWS-IF97)"
    UNIQUAC = "UNIQUAC"


@dataclass(frozen=True)
class CompoundSpecs:
    cas: str
    packages: FrozenSet[PropertyPackageEnum] = field(default_factory=frozenset)


@unique
class CASCompoundEnum(BaseGenericEnum):
    """
    Starter kit of Compound and their compound_ids.
    CompoundEnums mapped to their unique CAS numbers
    e.g. Methane = "74-82-8"
    """

    R134a = CompoundSpecs(
        cas="811-97-2",
        packages=frozenset({PropertyPackageEnum.IAPWS, PropertyPackageEnum.NRTL}),
    )
    Methane = CompoundSpecs(cas="74-82-8", packages=frozenset())
    Ethane = CompoundSpecs(cas="74-84-0", packages=frozenset())
    Propane = CompoundSpecs(cas="74-98-6", packages=frozenset())
    N_Butane = CompoundSpecs(cas="106-97-8", packages=frozenset())
    N_Pentane = CompoundSpecs(cas="109-66-0", packages=frozenset())
    N_Hexane = CompoundSpecs(cas="110-54-3", packages=frozenset())
    N_Heptane = CompoundSpecs(cas="142-82-5", packages=frozenset())
    N_Octane = CompoundSpecs(cas="111-65-9", packages=frozenset())
    N_Nonane = CompoundSpecs(cas="111-84-2", packages=frozenset())
    N_Decane = CompoundSpecs(cas="124-18-5", packages=frozenset())
    N_Undecane = CompoundSpecs(cas="1120-21-4", packages=frozenset())
    N_Dodecane = CompoundSpecs(cas="112-40-3", packages=frozenset())
    N_Tridecane = CompoundSpecs(cas="629-50-5", packages=frozenset())
    N_Tetradecane = CompoundSpecs(cas="629-59-4", packages=frozenset())
    N_Pentadecane = CompoundSpecs(cas="629-62-9", packages=frozenset())
    N_Hexadecane = CompoundSpecs(cas="544-76-3", packages=frozenset())
    N_Heptadecane = CompoundSpecs(cas="629-78-7", packages=frozenset())
    N_Octadecane = CompoundSpecs(cas="593-45-3", packages=frozenset())
    N_Nonadecane = CompoundSpecs(cas="629-92-5", packages=frozenset())
    N_Heneicosane = CompoundSpecs(cas="629-94-7", packages=frozenset())
    N_Docosane = CompoundSpecs(cas="629-97-0", packages=frozenset())
    N_Tricosane = CompoundSpecs(cas="638-67-5", packages=frozenset())
    N_Tetracosane = CompoundSpecs(cas="646-31-1", packages=frozenset())
    N_Pentacosane = CompoundSpecs(cas="629-99-2", packages=frozenset())
    N_Hexacosane = CompoundSpecs(cas="630-01-3", packages=frozenset())
    N_Heptacosane = CompoundSpecs(cas="593-49-7", packages=frozenset())
    N_Octacosane = CompoundSpecs(cas="630-02-4", packages=frozenset())
    N_Nonacosane = CompoundSpecs(cas="630-03-5", packages=frozenset())
    N_Eicosane = CompoundSpecs(cas="112-95-8", packages=frozenset())
    Isobutane = CompoundSpecs(cas="75-28-5", packages=frozenset())
    Isopentane = CompoundSpecs(cas="78-78-4", packages=frozenset())
    _2_Methylpentane = CompoundSpecs(cas="107-83-5", packages=frozenset())
    _3_Methylpentane = CompoundSpecs(cas="96-14-0", packages=frozenset())
    _2_Methylhexane = CompoundSpecs(cas="591-76-4", packages=frozenset())
    _3_Methylhexane = CompoundSpecs(cas="589-34-4", packages=frozenset())
    _2_Methylheptane = CompoundSpecs(cas="592-27-8", packages=frozenset())
    _3_Methylheptane = CompoundSpecs(cas="589-81-1", packages=frozenset())
    _4_Methylheptane = CompoundSpecs(cas="589-53-7", packages=frozenset())
    _2_Methyloctane = CompoundSpecs(cas="3221-61-2", packages=frozenset())
    _3_Methyloctane = CompoundSpecs(cas="2216-33-3", packages=frozenset())
    _4_Methyloctane = CompoundSpecs(cas="2216-34-4", packages=frozenset())
    _3_Methylnonane = CompoundSpecs(cas="5911-04-6", packages=frozenset())
    _2_Methylnonane = CompoundSpecs(cas="871-83-0", packages=frozenset())
    _4_Methylnonane = CompoundSpecs(cas="17301-94-9", packages=frozenset())
    _5_Methylnonane = CompoundSpecs(cas="15869-85-9", packages=frozenset())
    Neopentane = CompoundSpecs(cas="463-82-1", packages=frozenset())
    _2__2_Dimethylbutane = CompoundSpecs(cas="75-83-2", packages=frozenset())
    _2__3_Dimethylbutane = CompoundSpecs(cas="79-29-8", packages=frozenset())
    _2__2_Dimethyloctane = CompoundSpecs(cas="15869-87-1", packages=frozenset())
    Cis_1__3_Dimethylcyclopentane = CompoundSpecs(cas="2532-58-3", packages=frozenset())
    Trans_1__3_Dimethylcyclopentane = CompoundSpecs(
        cas="1759-58-6", packages=frozenset()
    )
    _2__2__3_Trimethylpentane = CompoundSpecs(cas="564-02-3", packages=frozenset())
    _2__2__4_Trimethylpentane = CompoundSpecs(cas="540-84-1", packages=frozenset())
    _2__3__3_Trimethylpentane = CompoundSpecs(cas="560-21-4", packages=frozenset())
    _2__3__4_Trimethylpentane = CompoundSpecs(cas="565-75-3", packages=frozenset())
    Squalane = CompoundSpecs(cas="111-01-3", packages=frozenset())
    _3_Ethylpentane = CompoundSpecs(cas="617-78-7", packages=frozenset())
    _2__2_Dimethylpentane = CompoundSpecs(cas="590-35-2", packages=frozenset())
    _2__3_Dimethylpentane = CompoundSpecs(cas="565-59-3", packages=frozenset())
    _2__4_Dimethylpentane = CompoundSpecs(cas="108-08-7", packages=frozenset())
    _3__3_Dimethylpentane = CompoundSpecs(cas="562-49-2", packages=frozenset())
    _2__2__3_Trimethylbutane = CompoundSpecs(cas="464-06-2", packages=frozenset())
    _3_Ethylhexane = CompoundSpecs(cas="619-99-8", packages=frozenset())
    _2__2_Dimethylhexane = CompoundSpecs(cas="590-73-8", packages=frozenset())
    _2__3_Dimethylhexane = CompoundSpecs(cas="584-94-1", packages=frozenset())
    _2__4_Dimethylhexane = CompoundSpecs(cas="589-43-5", packages=frozenset())
    _2__5_Dimethylhexane = CompoundSpecs(cas="592-13-2", packages=frozenset())
    _3__3_Dimethylhexane = CompoundSpecs(cas="563-16-6", packages=frozenset())
    _3__4_Dimethylhexane = CompoundSpecs(cas="583-48-2", packages=frozenset())
    _2_Methyl_3_Ethylpentane = CompoundSpecs(cas="609-26-7", packages=frozenset())
    _3_Methyl_3_Ethylpentane = CompoundSpecs(cas="1067-08-9", packages=frozenset())
    _2__2__3__3_Tetramethylbutane = CompoundSpecs(cas="594-82-1", packages=frozenset())
    _2__2__5_Trimethylhexane = CompoundSpecs(cas="3522-94-9", packages=frozenset())
    _2__4__4_Trimethylhexane = CompoundSpecs(cas="16747-30-1", packages=frozenset())
    _3__3_Diethylpentane = CompoundSpecs(cas="1067-20-5", packages=frozenset())
    _2__2__3__3_Tetramethylpentane = CompoundSpecs(
        cas="7154-79-2", packages=frozenset()
    )
    _2__2__3__4_Tetramethylpentane = CompoundSpecs(
        cas="1186-53-4", packages=frozenset()
    )
    _2__2__4__4_Tetramethylpentane = CompoundSpecs(
        cas="1070-87-7", packages=frozenset()
    )
    _2__3__3__4_Tetramethylpentane = CompoundSpecs(
        cas="16747-38-9", packages=frozenset()
    )
    _3_Ethylheptane = CompoundSpecs(cas="15869-80-4", packages=frozenset())
    _2__2_Dimethylheptane = CompoundSpecs(cas="1071-26-7", packages=frozenset())
    _3__3__5_Trimethylheptane = CompoundSpecs(cas="7154-80-5", packages=frozenset())
    Cyclopentane = CompoundSpecs(cas="287-92-3", packages=frozenset())
    Cyclohexane = CompoundSpecs(cas="110-82-7", packages=frozenset())
    Cyclobutane = CompoundSpecs(cas="287-23-0", packages=frozenset())
    Methylcyclopentane = CompoundSpecs(cas="96-37-7", packages=frozenset())
    Ethylcyclopentane = CompoundSpecs(cas="1640-89-7", packages=frozenset())
    N_Propylcyclopentane = CompoundSpecs(cas="2040-96-2", packages=frozenset())
    _1__1_Dimethylcyclopentane = CompoundSpecs(cas="1638-26-2", packages=frozenset())
    Cis_1__2_Dimethylcyclopentane = CompoundSpecs(cas="1192-18-3", packages=frozenset())
    Trans_1__2_Dimethylcyclopentane = CompoundSpecs(
        cas="822-50-4", packages=frozenset()
    )
    Isopropylcyclopentane = CompoundSpecs(cas="3875-51-2", packages=frozenset())
    _1_Methyl_1_Ethylcyclopentane = CompoundSpecs(
        cas="16747-50-5", packages=frozenset()
    )
    N_Butylcyclopentane = CompoundSpecs(cas="2040-95-1", packages=frozenset())
    Methylcyclohexane = CompoundSpecs(cas="108-87-2", packages=frozenset())
    Ethylcyclohexane = CompoundSpecs(cas="1678-91-7", packages=frozenset())
    N_Propylcyclohexane = CompoundSpecs(cas="1678-92-8", packages=frozenset())
    N_Butylcyclohexane = CompoundSpecs(cas="1678-93-9", packages=frozenset())
    _1__1_Dimethylcyclohexane = CompoundSpecs(cas="590-66-9", packages=frozenset())
    Cis_1__2_Dimethylcyclohexane = CompoundSpecs(cas="2207-01-4", packages=frozenset())
    Trans_1__2_Dimethylcyclohexane = CompoundSpecs(
        cas="6876-23-9", packages=frozenset()
    )
    Cis_1__3_Dimethylcyclohexane = CompoundSpecs(cas="638-04-0", packages=frozenset())
    Trans_1__3_Dimethylcyclohexane = CompoundSpecs(
        cas="2207-03-6", packages=frozenset()
    )
    Cis_1__4_Dimethylcyclohexane = CompoundSpecs(cas="624-29-3", packages=frozenset())
    Trans_1__4_Dimethylcyclohexane = CompoundSpecs(
        cas="2207-04-7", packages=frozenset()
    )
    Tert_Butylcyclohexane = CompoundSpecs(cas="3178-22-1", packages=frozenset())
    Cis_Decahydronaphthalene = CompoundSpecs(cas="493-01-6", packages=frozenset())
    Trans_Decahydronaphthalene = CompoundSpecs(cas="493-02-7", packages=frozenset())
    Ethylene = CompoundSpecs(cas="74-85-1", packages=frozenset())
    Propylene = CompoundSpecs(cas="115-07-1", packages=frozenset())
    _1_Butene = CompoundSpecs(cas="106-98-9", packages=frozenset())
    _1_Pentene = CompoundSpecs(cas="109-67-1", packages=frozenset())
    _1_Hexene = CompoundSpecs(cas="592-41-6", packages=frozenset())
    _1_Heptene = CompoundSpecs(cas="592-76-7", packages=frozenset())
    _1_Octene = CompoundSpecs(cas="111-66-0", packages=frozenset())
    _1_Nonene = CompoundSpecs(cas="124-11-8", packages=frozenset())
    _1_Undecene = CompoundSpecs(cas="821-95-4", packages=frozenset())
    _1_Decene = CompoundSpecs(cas="872-05-9", packages=frozenset())
    _1_Dodecene = CompoundSpecs(cas="112-41-4", packages=frozenset())
    _1_Tetradecene = CompoundSpecs(cas="1120-36-1", packages=frozenset())
    _1_Hexadecene = CompoundSpecs(cas="629-73-2", packages=frozenset())
    _1_Octadecene = CompoundSpecs(cas="112-88-9", packages=frozenset())
    _1_Eicosene = CompoundSpecs(cas="3452-07-1", packages=frozenset())
    Cis_2_Butene = CompoundSpecs(cas="590-18-1", packages=frozenset())
    Trans_2_Butene = CompoundSpecs(cas="624-64-6", packages=frozenset())
    Cis_2_Pentene = CompoundSpecs(cas="627-20-3", packages=frozenset())
    Trans_2_Pentene = CompoundSpecs(cas="646-04-8", packages=frozenset())
    Cis_2_Hexene = CompoundSpecs(cas="7688-21-3", packages=frozenset())
    Trans_2_Hexene = CompoundSpecs(cas="4050-45-7", packages=frozenset())
    P_Ethyltoluene = CompoundSpecs(cas="622-96-8", packages=frozenset())
    _1__2__3_Trimethylbenzene = CompoundSpecs(cas="526-73-8", packages=frozenset())
    _1__2__4_Trimethylbenzene = CompoundSpecs(cas="95-63-6", packages=frozenset())
    Mesitylene = CompoundSpecs(cas="108-67-8", packages=frozenset())
    Isobutylbenzene = CompoundSpecs(cas="538-93-2", packages=frozenset())
    P_Cymene = CompoundSpecs(cas="99-87-6", packages=frozenset())
    P_Diethylbenzene = CompoundSpecs(cas="105-05-5", packages=frozenset())
    _1__2__4__5_Tetramethylbenzene = CompoundSpecs(cas="95-93-2", packages=frozenset())
    Isobutene = CompoundSpecs(cas="115-11-7", packages=frozenset())
    _2_Methyl_1_Butene = CompoundSpecs(cas="563-46-2", packages=frozenset())
    _3_Methyl_1_Butene = CompoundSpecs(cas="563-45-1", packages=frozenset())
    _2_Methyl_2_Butene = CompoundSpecs(cas="513-35-9", packages=frozenset())
    _2_Methyl_1_Pentene = CompoundSpecs(cas="763-29-1", packages=frozenset())
    _4_Methyl_Cis_2_Pentene = CompoundSpecs(cas="691-38-3", packages=frozenset())
    _4_Methyl_Trans_2_Pentene = CompoundSpecs(cas="674-76-0", packages=frozenset())
    _2_Methyl_1_Heptene = CompoundSpecs(cas="15870-10-7", packages=frozenset())
    _2_Methyl_1_Nonene = CompoundSpecs(cas="2980-71-4", packages=frozenset())
    _2_Methyl_1_Undecene = CompoundSpecs(cas="18516-37-5", packages=frozenset())
    _2_Methyl_1_Tridecene = CompoundSpecs(cas="18094-01-4", packages=frozenset())
    _2_Methyl_1_Pentadecene = CompoundSpecs(cas="29833-69-0", packages=frozenset())
    _2_Methyl_1_Heptadecene = CompoundSpecs(cas="42764-74-9", packages=frozenset())
    _2_Methyl_1_Nonadecene = CompoundSpecs(cas="52254-50-9", packages=frozenset())
    Cyclohexene = CompoundSpecs(cas="110-83-8", packages=frozenset())
    Propadiene = CompoundSpecs(cas="463-49-0", packages=frozenset())
    _1__2_Butadiene = CompoundSpecs(cas="590-19-2", packages=frozenset())
    _1__3_Butadiene = CompoundSpecs(cas="106-99-0", packages=frozenset())
    Isoprene = CompoundSpecs(cas="78-79-5", packages=frozenset())
    Dicyclopentadiene = CompoundSpecs(cas="77-73-6", packages=frozenset())
    Acetylene = CompoundSpecs(cas="74-86-2", packages=frozenset())
    Methylacetylene = CompoundSpecs(cas="74-99-7", packages=frozenset())
    Vinylacetylene = CompoundSpecs(cas="689-97-4", packages=frozenset())
    Dimethylacetylene = CompoundSpecs(cas="503-17-3", packages=frozenset())
    Ethylacetylene = CompoundSpecs(cas="107-00-6", packages=frozenset())
    Benzene = CompoundSpecs(cas="71-43-2", packages=frozenset())
    Toluene = CompoundSpecs(cas="108-88-3", packages=frozenset())
    Ethylbenzene = CompoundSpecs(cas="100-41-4", packages=frozenset())
    N_Propylbenzene = CompoundSpecs(cas="103-65-1", packages=frozenset())
    N_Butylbenzene = CompoundSpecs(cas="104-51-8", packages=frozenset())
    M_Xylene = CompoundSpecs(cas="108-38-3", packages=frozenset())
    O_Xylene = CompoundSpecs(cas="95-47-6", packages=frozenset())
    P_Xylene = CompoundSpecs(cas="106-42-3", packages=frozenset())
    Cumene = CompoundSpecs(cas="98-82-8", packages=frozenset())
    O_Ethyltoluene = CompoundSpecs(cas="611-14-3", packages=frozenset())
    M_Ethyltoluene = CompoundSpecs(cas="620-14-4", packages=frozenset())
    Sec_Butylbenzene = CompoundSpecs(cas="135-98-8", packages=frozenset())
    Tert_Butylbenzene = CompoundSpecs(cas="98-06-6", packages=frozenset())
    O_Cymene = CompoundSpecs(cas="527-84-4", packages=frozenset())
    M_Cymene = CompoundSpecs(cas="535-77-3", packages=frozenset())
    O_Diethylbenzene = CompoundSpecs(cas="135-01-3", packages=frozenset())
    M_Diethylbenzene = CompoundSpecs(cas="141-93-5", packages=frozenset())
    _1__2__3__4_Tetramethylbenzene = CompoundSpecs(cas="488-23-3", packages=frozenset())
    _1__2__3__5_Tetramethylbenzene = CompoundSpecs(cas="527-53-7", packages=frozenset())
    _2_Ethyl_M_Xylene = CompoundSpecs(cas="2870-04-4", packages=frozenset())
    _2_Ethyl_P_Xylene = CompoundSpecs(cas="1758-88-9", packages=frozenset())
    _4_Ethyl_M_Xylene = CompoundSpecs(cas="874-41-9", packages=frozenset())
    _4_Ethyl_O_Xylene = CompoundSpecs(cas="934-80-5", packages=frozenset())
    _1_Methyl_3_N_Propylbenzene = CompoundSpecs(cas="1074-43-7", packages=frozenset())
    _1_Methyl_4_N_Propylbenzene = CompoundSpecs(cas="1074-55-1", packages=frozenset())
    P_Diisopropylbenzene = CompoundSpecs(cas="100-18-5", packages=frozenset())
    Styrene = CompoundSpecs(cas="100-42-5", packages=frozenset())
    Naphthalene = CompoundSpecs(cas="91-20-3", packages=frozenset())
    _1_Methylnaphthalene = CompoundSpecs(cas="90-12-0", packages=frozenset())
    _2_Methylnaphthalene = CompoundSpecs(cas="91-57-6", packages=frozenset())
    _1_Phenylnaphthalene = CompoundSpecs(cas="605-02-7", packages=frozenset())
    Acenaphthene = CompoundSpecs(cas="83-32-9", packages=frozenset())
    Fluorene = CompoundSpecs(cas="86-73-7", packages=frozenset())
    Phenanthrene = CompoundSpecs(cas="85-01-8", packages=frozenset())
    Fluoranthene = CompoundSpecs(cas="206-44-0", packages=frozenset())
    Pyrene = CompoundSpecs(cas="129-00-0", packages=frozenset())
    Chrysene = CompoundSpecs(cas="218-01-9", packages=frozenset())
    Biphenyl = CompoundSpecs(cas="92-52-4", packages=frozenset())
    Indene = CompoundSpecs(cas="95-13-6", packages=frozenset())
    Indane = CompoundSpecs(cas="496-11-7", packages=frozenset())
    _1_Methylindene = CompoundSpecs(cas="767-59-9", packages=frozenset())
    _2_Methylindene = CompoundSpecs(cas="2177-47-1", packages=frozenset())
    Air = CompoundSpecs(cas="132259-10-0", packages=frozenset())
    CarbonMonoxide = CompoundSpecs(cas="630-08-0", packages=frozenset())
    CarbonDioxide = CompoundSpecs(cas="124-38-9", packages=frozenset())
    HydrogenSulfide = CompoundSpecs(cas="7783-06-4", packages=frozenset())
    NitricOxide = CompoundSpecs(cas="10102-43-9", packages=frozenset())
    NitrogenDioxide = CompoundSpecs(cas="10102-44-0", packages=frozenset())
    NitrousOxide = CompoundSpecs(cas="10024-97-2", packages=frozenset())
    SulfurDioxide = CompoundSpecs(cas="7446-09-5", packages=frozenset())
    SulfurTrioxide = CompoundSpecs(cas="7446-11-9", packages=frozenset())
    NitrogenTrioxide = CompoundSpecs(cas="10544-73-7", packages=frozenset())
    NitrogenTetroxide = CompoundSpecs(cas="10544-72-6", packages=frozenset())
    Helium_4 = CompoundSpecs(cas="7440-59-7", packages=frozenset())
    Fluorine = CompoundSpecs(cas="7782-41-4", packages=frozenset())
    Krypton = CompoundSpecs(cas="7439-90-9", packages=frozenset())
    Xenon = CompoundSpecs(cas="7440-63-3", packages=frozenset())
    Ozone = CompoundSpecs(cas="10028-15-6", packages=frozenset())
    CarbonylSulfide = CompoundSpecs(cas="463-58-1", packages=frozenset())
    Formaldehyde = CompoundSpecs(cas="50-00-0", packages=frozenset())
    Acetaldehyde = CompoundSpecs(cas="75-07-0", packages=frozenset())
    _2_Methylpropanal = CompoundSpecs(cas="78-84-2", packages=frozenset())
    Propanal = CompoundSpecs(cas="123-38-6", packages=frozenset())
    Butanal = CompoundSpecs(cas="123-72-8", packages=frozenset())
    Pentanal = CompoundSpecs(cas="110-62-3", packages=frozenset())
    Hexanal = CompoundSpecs(cas="66-25-1", packages=frozenset())
    Heptanal = CompoundSpecs(cas="111-71-7", packages=frozenset())
    Methylal = CompoundSpecs(cas="109-87-5", packages=frozenset())
    T_Cinnamaldehyde = CompoundSpecs(cas="14371-10-9", packages=frozenset())
    Benzaldehyde = CompoundSpecs(cas="100-52-7", packages=frozenset())
    Acrolein = CompoundSpecs(cas="107-02-8", packages=frozenset())
    Octanal = CompoundSpecs(cas="124-13-0", packages=frozenset())
    Nonanal = CompoundSpecs(cas="124-19-6", packages=frozenset())
    Decanal = CompoundSpecs(cas="112-31-2", packages=frozenset())
    Undecanal = CompoundSpecs(cas="112-44-7", packages=frozenset())
    Dodecanal = CompoundSpecs(cas="112-54-9", packages=frozenset())
    Tridecanal = CompoundSpecs(cas="10486-19-8", packages=frozenset())
    Tetradecanal = CompoundSpecs(cas="124-25-4", packages=frozenset())
    Pentadecanal = CompoundSpecs(cas="2765-11-9", packages=frozenset())
    Hexadecanal = CompoundSpecs(cas="629-80-1", packages=frozenset())
    Heptadecanal = CompoundSpecs(cas="629-90-3", packages=frozenset())
    Octadecanal = CompoundSpecs(cas="638-66-4", packages=frozenset())
    Nonadecanal = CompoundSpecs(cas="17352-32-8", packages=frozenset())
    Acetone = CompoundSpecs(cas="67-64-1", packages=frozenset())
    MethylEthylKetone = CompoundSpecs(cas="78-93-3", packages=frozenset())
    _3_Pentanone = CompoundSpecs(cas="96-22-0", packages=frozenset())
    MethylIsopropylKetone = CompoundSpecs(cas="563-80-4", packages=frozenset())
    Cyclohexanone = CompoundSpecs(cas="108-94-1", packages=frozenset())
    MethylIsobutylKetone = CompoundSpecs(cas="108-10-1", packages=frozenset())
    _3_Heptanone = CompoundSpecs(cas="106-35-4", packages=frozenset())
    _4_Heptanone = CompoundSpecs(cas="123-19-3", packages=frozenset())
    _3_Hexanone = CompoundSpecs(cas="589-38-8", packages=frozenset())
    _2_Pentanone = CompoundSpecs(cas="107-87-9", packages=frozenset())
    _2_Hexanone = CompoundSpecs(cas="591-78-6", packages=frozenset())
    _2_Heptanone = CompoundSpecs(cas="110-43-0", packages=frozenset())
    _5_Methyl_2_Hexanone = CompoundSpecs(cas="110-12-3", packages=frozenset())
    _3__3_Dimethyl_2_Butanone = CompoundSpecs(cas="75-97-8", packages=frozenset())
    DiisobutylKetone = CompoundSpecs(cas="108-83-8", packages=frozenset())
    DiisopropylKetone = CompoundSpecs(cas="565-80-0", packages=frozenset())
    Ketene = CompoundSpecs(cas="463-51-4", packages=frozenset())
    MesitylOxide = CompoundSpecs(cas="141-79-7", packages=frozenset())
    Methanol = CompoundSpecs(cas="67-56-1", packages=frozenset())
    Ethanol = CompoundSpecs(cas="64-17-5", packages=frozenset())
    _1_Propanol = CompoundSpecs(cas="71-23-8", packages=frozenset())
    _1_Butanol = CompoundSpecs(cas="71-36-3", packages=frozenset())
    _1_Pentanol = CompoundSpecs(cas="71-41-0", packages=frozenset())
    _1_Hexanol = CompoundSpecs(cas="111-27-3", packages=frozenset())
    _1_Heptanol = CompoundSpecs(cas="111-70-6", packages=frozenset())
    _1_Octanol = CompoundSpecs(cas="111-87-5", packages=frozenset())
    _1_Nonanol = CompoundSpecs(cas="143-08-8", packages=frozenset())
    _1_Decanol = CompoundSpecs(cas="112-30-1", packages=frozenset())
    _1_Undecanol = CompoundSpecs(cas="112-42-5", packages=frozenset())
    _1_Dodecanol = CompoundSpecs(cas="112-53-8", packages=frozenset())
    _1_Tridecanol = CompoundSpecs(cas="112-70-9", packages=frozenset())
    _1_Tetradecanol = CompoundSpecs(cas="112-72-1", packages=frozenset())
    _1_Pentadecanol = CompoundSpecs(cas="629-76-5", packages=frozenset())
    _1_Hexadecanol = CompoundSpecs(cas="36653-82-4", packages=frozenset())
    _1_Heptadecanol = CompoundSpecs(cas="1454-85-9", packages=frozenset())
    _1_Octadecanol = CompoundSpecs(cas="112-92-5", packages=frozenset())
    _1_Nonadecanol = CompoundSpecs(cas="1454-84-8", packages=frozenset())
    _1_Eicosanol = CompoundSpecs(cas="629-96-9", packages=frozenset())
    _1__2__4_Trichlorobenzene = CompoundSpecs(cas="120-82-1", packages=frozenset())
    M_Dichlorobenzene = CompoundSpecs(cas="541-73-1", packages=frozenset())
    O_Dichlorobenzene = CompoundSpecs(cas="95-50-1", packages=frozenset())
    P_Dichlorobenzene = CompoundSpecs(cas="106-46-7", packages=frozenset())
    Monochlorobenzene = CompoundSpecs(cas="108-90-7", packages=frozenset())
    Bromobenzene = CompoundSpecs(cas="108-86-1", packages=frozenset())
    MethylIodide = CompoundSpecs(cas="74-88-4", packages=frozenset())
    Iodobenzene = CompoundSpecs(cas="591-50-4", packages=frozenset())
    Methylamine = CompoundSpecs(cas="74-89-5", packages=frozenset())
    Ethylamine = CompoundSpecs(cas="75-04-7", packages=frozenset())
    Trimethylamine = CompoundSpecs(cas="75-50-3", packages=frozenset())
    Diethylamine = CompoundSpecs(cas="109-89-7", packages=frozenset())
    Triethylamine = CompoundSpecs(cas="121-44-8", packages=frozenset())
    Diisopropylamine = CompoundSpecs(cas="108-18-9", packages=frozenset())
    Isopropylamine = CompoundSpecs(cas="75-31-0", packages=frozenset())
    Pyridine = CompoundSpecs(cas="110-86-1", packages=frozenset())
    Aniline = CompoundSpecs(cas="62-53-3", packages=frozenset())
    P_Phenylenediamine = CompoundSpecs(cas="106-50-3", packages=frozenset())
    Ethylenediamine = CompoundSpecs(cas="107-15-3", packages=frozenset())
    N_AminoethylPiperazine = CompoundSpecs(cas="140-31-8", packages=frozenset())
    Diethylenetriamine = CompoundSpecs(cas="111-40-0", packages=frozenset())
    Piperazine = CompoundSpecs(cas="110-85-0", packages=frozenset())
    HydrogenCyanide = CompoundSpecs(cas="74-90-8", packages=frozenset())
    Acetonitrile = CompoundSpecs(cas="75-05-8", packages=frozenset())
    Acrylonitrile = CompoundSpecs(cas="107-13-1", packages=frozenset())
    Methacrylonitrile = CompoundSpecs(cas="126-98-7", packages=frozenset())
    Propionitrile = CompoundSpecs(cas="107-12-0", packages=frozenset())
    Nitrobenzene = CompoundSpecs(cas="98-95-3", packages=frozenset())
    Nitromethane = CompoundSpecs(cas="75-52-5", packages=frozenset())
    Nitroethane = CompoundSpecs(cas="79-24-3", packages=frozenset())
    _1_Nitropropane = CompoundSpecs(cas="108-03-2", packages=frozenset())
    _2_Nitropropane = CompoundSpecs(cas="79-46-9", packages=frozenset())
    _1_Nitrobutane = CompoundSpecs(cas="627-05-4", packages=frozenset())
    O_Nitrotoluene = CompoundSpecs(cas="88-72-2", packages=frozenset())
    P_Nitrotoluene = CompoundSpecs(cas="99-99-0", packages=frozenset())
    M_Nitrotoluene = CompoundSpecs(cas="99-08-1", packages=frozenset())
    _2__4_Dinitrotoluene = CompoundSpecs(cas="121-14-2", packages=frozenset())
    _2__6_Dinitrotoluene = CompoundSpecs(cas="606-20-2", packages=frozenset())
    _3__4_Dinitrotoluene = CompoundSpecs(cas="610-39-9", packages=frozenset())
    _2__5_Dinitrotoluene = CompoundSpecs(cas="619-15-8", packages=frozenset())
    _3__5_Dinitrotoluene = CompoundSpecs(cas="618-85-9", packages=frozenset())
    _2__4__6_Trinitrotoluene = CompoundSpecs(cas="118-96-7", packages=frozenset())
    EthylMercaptan = CompoundSpecs(cas="75-08-1", packages=frozenset())
    MethylMercaptan = CompoundSpecs(cas="74-93-1", packages=frozenset())
    N_PropylMercaptan = CompoundSpecs(cas="107-03-9", packages=frozenset())
    Tert_ButylMercaptan = CompoundSpecs(cas="75-66-1", packages=frozenset())
    IsobutylMercaptan = CompoundSpecs(cas="513-44-0", packages=frozenset())
    Sec_ButylMercaptan = CompoundSpecs(cas="513-53-1", packages=frozenset())
    N_HexylMercaptan = CompoundSpecs(cas="111-31-9", packages=frozenset())
    IsopropylMercaptan = CompoundSpecs(cas="75-33-2", packages=frozenset())
    PhenylMercaptan = CompoundSpecs(cas="108-98-5", packages=frozenset())
    CarbonDisulfide = CompoundSpecs(cas="75-15-0", packages=frozenset())
    DimethylSulfide = CompoundSpecs(cas="75-18-3", packages=frozenset())
    Thiophene = CompoundSpecs(cas="110-02-1", packages=frozenset())
    MethylEthylSulfide = CompoundSpecs(cas="624-89-5", packages=frozenset())
    MethylN_PropylSulfide = CompoundSpecs(cas="3877-15-4", packages=frozenset())
    MethylT_ButylSulfide = CompoundSpecs(cas="6163-64-0", packages=frozenset())
    MethylT_PentylSulfide = CompoundSpecs(cas="13286-92-5", packages=frozenset())
    Di_N_PropylSulfide = CompoundSpecs(cas="111-47-7", packages=frozenset())
    DiethylSulfide = CompoundSpecs(cas="352-93-2", packages=frozenset())
    DiethylDisulfide = CompoundSpecs(cas="110-81-6", packages=frozenset())
    DimethylDisulfide = CompoundSpecs(cas="624-92-0", packages=frozenset())
    Di_N_PropylDisulfide = CompoundSpecs(cas="629-19-6", packages=frozenset())
    Di_Tert_ButylDisulfide = CompoundSpecs(cas="110-06-5", packages=frozenset())
    EthylMethylDisulfide = CompoundSpecs(cas="20333-39-5", packages=frozenset())
    EthylPropylDisulfide = CompoundSpecs(cas="30453-31-7", packages=frozenset())
    DiphenylDisulfide = CompoundSpecs(cas="882-33-7", packages=frozenset())
    Benzothiophene = CompoundSpecs(cas="95-15-8", packages=frozenset())
    SalicylicAcid = CompoundSpecs(cas="69-72-7", packages=frozenset())
    MethylSalicylate = CompoundSpecs(cas="119-36-8", packages=frozenset())
    PropyleneGlycolMonomethylEtherAcetate = CompoundSpecs(
        cas="108-65-6", packages=frozenset()
    )
    Furfural = CompoundSpecs(cas="98-01-1", packages=frozenset())
    DimethylCarbonate = CompoundSpecs(cas="616-38-6", packages=frozenset())
    DiethylCarbonate = CompoundSpecs(cas="105-58-8", packages=frozenset())
    MethylEthylCarbonate = CompoundSpecs(cas="623-53-0", packages=frozenset())
    MethylPhenylCarbonate = CompoundSpecs(cas="13509-27-8", packages=frozenset())
    EthylPhenylCarbonate = CompoundSpecs(cas="3878-46-4", packages=frozenset())
    DiphenylCarbonate = CompoundSpecs(cas="102-09-0", packages=frozenset())
    EthyleneCarbonate = CompoundSpecs(cas="96-49-1", packages=frozenset())
    PropyleneCarbonate = CompoundSpecs(cas="108-32-7", packages=frozenset())
    DibutylCarbonate = CompoundSpecs(cas="542-52-9", packages=frozenset())
    _2_Ethoxyethanol = CompoundSpecs(cas="110-80-5", packages=frozenset())
    PropyleneGlycolMonomethylEther = CompoundSpecs(cas="107-98-2", packages=frozenset())
    Cyrene = CompoundSpecs(cas="53716-82-8", packages=frozenset())
    DiacetoneAlcohol = CompoundSpecs(cas="123-42-2", packages=frozenset())
    Isopropanol = CompoundSpecs(cas="67-63-0", packages=frozenset())
    _2_Methyl_1_Propanol = CompoundSpecs(cas="78-83-1", packages=frozenset())
    _2_Butanol = CompoundSpecs(cas="78-92-2", packages=frozenset())
    _2_Methyl_2_Propanol = CompoundSpecs(cas="75-65-0", packages=frozenset())
    _2_Methyl_2_Butanol = CompoundSpecs(cas="75-85-4", packages=frozenset())
    _2_Pentanol = CompoundSpecs(cas="6032-29-7", packages=frozenset())
    _2_Methyl_1_Butanol = CompoundSpecs(cas="137-32-6", packages=frozenset())
    _2__2_Dimethyl_1_Propanol = CompoundSpecs(cas="75-84-3", packages=frozenset())
    _2_Methyl_2_Heptanol = CompoundSpecs(cas="625-25-2", packages=frozenset())
    Cyclohexanol = CompoundSpecs(cas="108-93-0", packages=frozenset())
    Phenol = CompoundSpecs(cas="108-95-2", packages=frozenset())
    M_Cresol = CompoundSpecs(cas="108-39-4", packages=frozenset())
    O_Cresol = CompoundSpecs(cas="95-48-7", packages=frozenset())
    P_Cresol = CompoundSpecs(cas="106-44-5", packages=frozenset())
    P_IsopropenylPhenol = CompoundSpecs(cas="4286-23-1", packages=frozenset())
    _3_Phenyl_1_Propanol = CompoundSpecs(cas="122-97-4", packages=frozenset())
    BenzylAlcohol = CompoundSpecs(cas="100-51-6", packages=frozenset())
    _2__3_Xylenol = CompoundSpecs(cas="526-75-0", packages=frozenset())
    _2__4_Xylenol = CompoundSpecs(cas="105-67-9", packages=frozenset())
    _2__5_Xylenol = CompoundSpecs(cas="95-87-4", packages=frozenset())
    _2__6_Xylenol = CompoundSpecs(cas="576-26-1", packages=frozenset())
    _3__4_Xylenol = CompoundSpecs(cas="95-65-8", packages=frozenset())
    _3__5_Xylenol = CompoundSpecs(cas="108-68-9", packages=frozenset())
    Mesitol = CompoundSpecs(cas="527-60-6", packages=frozenset())
    P_Cumylphenol = CompoundSpecs(cas="599-64-4", packages=frozenset())
    EthyleneGlycol = CompoundSpecs(cas="107-21-1", packages=frozenset())
    DiethyleneGlycol = CompoundSpecs(cas="111-46-6", packages=frozenset())
    TriethyleneGlycol = CompoundSpecs(cas="112-27-6", packages=frozenset())
    TetraethyleneGlycol = CompoundSpecs(cas="112-60-7", packages=frozenset())
    _1__4_Butanediol = CompoundSpecs(cas="110-63-4", packages=frozenset())
    Glycerol = CompoundSpecs(cas="56-81-5", packages=frozenset())
    _1__2_PropyleneGlycol = CompoundSpecs(cas="57-55-6", packages=frozenset())
    BisphenolA = CompoundSpecs(cas="80-05-7", packages=frozenset())
    Methane_Diol = CompoundSpecs(cas="463-57-0", packages=frozenset())
    Ethane_1__1_Diol = CompoundSpecs(cas="4433-56-1", packages=frozenset())
    O__P_BisphenolA = CompoundSpecs(cas="837-08-1", packages=frozenset())
    PentaethyleneGlycol = CompoundSpecs(cas="4792-15-8", packages=frozenset())
    HexaethyleneGlycol = CompoundSpecs(cas="2615-15-8", packages=frozenset())
    DipropyleneGlycol = CompoundSpecs(cas="110-98-5", packages=frozenset())
    TripropyleneGlycol = CompoundSpecs(cas="1638-16-0", packages=frozenset())
    TetrapropyleneGlycol = CompoundSpecs(cas="24800-25-7", packages=frozenset())
    PentapropyleneGlycol = CompoundSpecs(cas="21482-12-2", packages=frozenset())
    HexapropyleneGlycol = CompoundSpecs(cas="74388-92-4", packages=frozenset())
    AceticAcid = CompoundSpecs(cas="64-19-7", packages=frozenset())
    PropionicAcid = CompoundSpecs(cas="79-09-4", packages=frozenset())
    N_ButyricAcid = CompoundSpecs(cas="107-92-6", packages=frozenset())
    FormicAcid = CompoundSpecs(cas="64-18-6", packages=frozenset())
    CaproicAcid = CompoundSpecs(cas="142-62-1", packages=frozenset())
    CaprylicAcid = CompoundSpecs(cas="124-07-2", packages=frozenset())
    CapricAcid = CompoundSpecs(cas="334-48-5", packages=frozenset())
    LauricAcid = CompoundSpecs(cas="143-07-7", packages=frozenset())
    MyristicAcid = CompoundSpecs(cas="544-63-8", packages=frozenset())
    PalmiticAcid = CompoundSpecs(cas="57-10-3", packages=frozenset())
    StearicAcid = CompoundSpecs(cas="57-11-4", packages=frozenset())
    AcrylicAcid = CompoundSpecs(cas="79-10-7", packages=frozenset())
    MethacrylicAcid = CompoundSpecs(cas="79-41-4", packages=frozenset())
    OleicAcid = CompoundSpecs(cas="112-80-1", packages=frozenset())
    LinoleicAcid = CompoundSpecs(cas="60-33-3", packages=frozenset())
    LinolenicAcid = CompoundSpecs(cas="463-40-1", packages=frozenset())
    OxalicAcid = CompoundSpecs(cas="144-62-7", packages=frozenset())
    AdipicAcid = CompoundSpecs(cas="124-04-9", packages=frozenset())
    MaleicAcid = CompoundSpecs(cas="110-16-7", packages=frozenset())
    MalonicAcid = CompoundSpecs(cas="141-82-2", packages=frozenset())
    BenzoicAcid = CompoundSpecs(cas="65-85-0", packages=frozenset())
    O_ToluicAcid = CompoundSpecs(cas="118-90-1", packages=frozenset())
    P_ToluicAcid = CompoundSpecs(cas="99-94-5", packages=frozenset())
    PhthalicAcid = CompoundSpecs(cas="88-99-3", packages=frozenset())
    TerephthalicAcid = CompoundSpecs(cas="100-21-0", packages=frozenset())
    T_CinnamicAcid = CompoundSpecs(cas="140-10-3", packages=frozenset())
    AceticAnhydride = CompoundSpecs(cas="108-24-7", packages=frozenset())
    MaleicAnhydride = CompoundSpecs(cas="108-31-6", packages=frozenset())
    MethylFormate = CompoundSpecs(cas="107-31-3", packages=frozenset())
    EthylFormate = CompoundSpecs(cas="109-94-4", packages=frozenset())
    N_PropylFormate = CompoundSpecs(cas="110-74-7", packages=frozenset())
    SulfurHexafluoride = CompoundSpecs(cas="2551-62-4", packages=frozenset())
    MethylAcetate = CompoundSpecs(cas="79-20-9", packages=frozenset())
    EthylAcetate = CompoundSpecs(cas="141-78-6", packages=frozenset())
    N_PropylAcetate = CompoundSpecs(cas="109-60-4", packages=frozenset())
    IsopropylAcetate = CompoundSpecs(cas="108-21-4", packages=frozenset())
    N_ButylAcetate = CompoundSpecs(cas="123-86-4", packages=frozenset())
    IsobutylAcetate = CompoundSpecs(cas="110-19-0", packages=frozenset())
    N_PentylAcetate = CompoundSpecs(cas="628-63-7", packages=frozenset())
    VinylAcetate = CompoundSpecs(cas="108-05-4", packages=frozenset())
    N_HexylAcetate = CompoundSpecs(cas="142-92-7", packages=frozenset())
    PhenylAcetate = CompoundSpecs(cas="122-79-2", packages=frozenset())
    MethylPropionate = CompoundSpecs(cas="554-12-1", packages=frozenset())
    Dimethylmalonate = CompoundSpecs(cas="108-59-8", packages=frozenset())
    MethylCaproate = CompoundSpecs(cas="106-70-7", packages=frozenset())
    MethylCaprylate = CompoundSpecs(cas="111-11-5", packages=frozenset())
    MethylCaprate = CompoundSpecs(cas="110-42-9", packages=frozenset())
    MethylLaurate = CompoundSpecs(cas="111-82-0", packages=frozenset())
    MethylMyristate = CompoundSpecs(cas="124-10-7", packages=frozenset())
    MethylPalmitate = CompoundSpecs(cas="112-39-0", packages=frozenset())
    MethylStearate = CompoundSpecs(cas="112-61-8", packages=frozenset())
    Tricaprylin = CompoundSpecs(cas="538-23-8", packages=frozenset())
    Tricaprin = CompoundSpecs(cas="621-71-6", packages=frozenset())
    Trilaurin = CompoundSpecs(cas="538-24-9", packages=frozenset())
    Trimyristin = CompoundSpecs(cas="555-45-3", packages=frozenset())
    Tripalmitin = CompoundSpecs(cas="555-44-2", packages=frozenset())
    Tristearin = CompoundSpecs(cas="555-43-1", packages=frozenset())
    MethylMethacrylate = CompoundSpecs(cas="80-62-6", packages=frozenset())
    MethylOleate = CompoundSpecs(cas="112-62-9", packages=frozenset())
    MethylLinoleate = CompoundSpecs(cas="112-63-0", packages=frozenset())
    MethylLinolenate = CompoundSpecs(cas="301-00-8", packages=frozenset())
    Triolein = CompoundSpecs(cas="122-32-7", packages=frozenset())
    Trilinolein = CompoundSpecs(cas="537-40-6", packages=frozenset())
    Trilenollenin = CompoundSpecs(cas="14465-68-0", packages=frozenset())
    DimethylTerephthalate = CompoundSpecs(cas="120-61-6", packages=frozenset())
    EthylBenzoate = CompoundSpecs(cas="93-89-0", packages=frozenset())
    DimethylEther = CompoundSpecs(cas="115-10-6", packages=frozenset())
    DiethylEther = CompoundSpecs(cas="60-29-7", packages=frozenset())
    MethylTert_ButylEther = CompoundSpecs(cas="1634-04-4", packages=frozenset())
    MethylTert_PentylEther = CompoundSpecs(cas="994-05-8", packages=frozenset())
    DiisopropylEther = CompoundSpecs(cas="108-20-3", packages=frozenset())
    Di_N_ButylEther = CompoundSpecs(cas="142-96-1", packages=frozenset())
    Di_Sec_ButylEther = CompoundSpecs(cas="6863-58-7", packages=frozenset())
    MethylEthylEther = CompoundSpecs(cas="540-67-0", packages=frozenset())
    MethylN_PropylEther = CompoundSpecs(cas="557-17-5", packages=frozenset())
    IsopropylButylEther = CompoundSpecs(cas="1860-27-1", packages=frozenset())
    MethylIsobutylEther = CompoundSpecs(cas="625-44-5", packages=frozenset())
    MethylIsopropylEther = CompoundSpecs(cas="598-53-8", packages=frozenset())
    Tert_ButylEthylEther = CompoundSpecs(cas="637-92-3", packages=frozenset())
    EthylTert_PentylEther = CompoundSpecs(cas="919-94-8", packages=frozenset())
    ButylVinylEther = CompoundSpecs(cas="111-34-2", packages=frozenset())
    _2_Methoxy_2_Methyl_Heptane = CompoundSpecs(cas="76589-16-7", packages=frozenset())
    Anisole = CompoundSpecs(cas="100-66-3", packages=frozenset())
    Phenetole = CompoundSpecs(cas="103-73-1", packages=frozenset())
    DiethyleneGlycolDiethylEther = CompoundSpecs(cas="112-36-7", packages=frozenset())
    DiphenylEther = CompoundSpecs(cas="101-84-8", packages=frozenset())
    _1__2_Dimethoxyethane = CompoundSpecs(cas="110-71-4", packages=frozenset())
    DiethyleneGlycolDimethylEther = CompoundSpecs(cas="111-96-6", packages=frozenset())
    TriethyleneGlycolDimethylEther = CompoundSpecs(cas="112-49-2", packages=frozenset())
    TetraethyleneGlycolDimethylEther = CompoundSpecs(
        cas="143-24-8", packages=frozenset()
    )
    PentaethyleneGlycolDimethylEther = CompoundSpecs(
        cas="1191-87-3", packages=frozenset()
    )
    EthyleneOxide = CompoundSpecs(cas="75-21-8", packages=frozenset())
    Tetrahydrofuran = CompoundSpecs(cas="109-99-9", packages=frozenset())
    _1__4_Dioxane = CompoundSpecs(cas="123-91-1", packages=frozenset())
    _1__2_PropyleneOxide = CompoundSpecs(cas="75-56-9", packages=frozenset())
    CumeneHydroperoxide = CompoundSpecs(cas="80-15-9", packages=frozenset())
    BenzoylPeroxide = CompoundSpecs(cas="94-36-0", packages=frozenset())
    DicumylPeroxide = CompoundSpecs(cas="80-43-3", packages=frozenset())
    P_DiisopropylbenzeneHydroperoxide = CompoundSpecs(
        cas="98-49-7", packages=frozenset()
    )
    EthylbenzeneHydroperoxide = CompoundSpecs(cas="3071-32-7", packages=frozenset())
    CarbonTetrachloride = CompoundSpecs(cas="56-23-5", packages=frozenset())
    Chloroform = CompoundSpecs(cas="67-66-3", packages=frozenset())
    MethylChloride = CompoundSpecs(cas="74-87-3", packages=frozenset())
    Trichloroethylene = CompoundSpecs(cas="79-01-6", packages=frozenset())
    VinylChloride = CompoundSpecs(cas="75-01-4", packages=frozenset())
    _1__1__2_Trichloroethane = CompoundSpecs(cas="79-00-5", packages=frozenset())
    _1__1_Dichloroethane = CompoundSpecs(cas="75-34-3", packages=frozenset())
    _1__2_Dichloroethane = CompoundSpecs(cas="107-06-2", packages=frozenset())
    EthylChloride = CompoundSpecs(cas="75-00-3", packages=frozenset())
    N__N_Dimethylformamide = CompoundSpecs(cas="68-12-2", packages=frozenset())
    N__N_Dimethylacetamide = CompoundSpecs(cas="127-19-5", packages=frozenset())
    Monoethanolamine = CompoundSpecs(cas="141-43-5", packages=frozenset())
    Diethanolamine = CompoundSpecs(cas="111-42-2", packages=frozenset())
    Triethanolamine = CompoundSpecs(cas="102-71-6", packages=frozenset())
    N_AminoethylEthanolamine = CompoundSpecs(cas="111-41-1", packages=frozenset())
    Methylethanolamine = CompoundSpecs(cas="109-83-1", packages=frozenset())
    Dimethylethanolamine = CompoundSpecs(cas="108-01-0", packages=frozenset())
    MethylDiethanolamine = CompoundSpecs(cas="105-59-9", packages=frozenset())
    Diethylethanolamine = CompoundSpecs(cas="100-37-8", packages=frozenset())
    Diisopropanolamine = CompoundSpecs(cas="110-97-4", packages=frozenset())
    N_Methyl_2_Pyrrolidone = CompoundSpecs(cas="872-50-4", packages=frozenset())
    Sulfolane = CompoundSpecs(cas="126-33-0", packages=frozenset())
    DimethylSulfoxide = CompoundSpecs(cas="67-68-5", packages=frozenset())
    Phosgene = CompoundSpecs(cas="75-44-5", packages=frozenset())
    TrichloroacetylChloride = CompoundSpecs(cas="76-02-8", packages=frozenset())
    DichloroacetylChloride = CompoundSpecs(cas="79-36-7", packages=frozenset())
    Trichloroacetaldehyde = CompoundSpecs(cas="75-87-6", packages=frozenset())
    Dichloroacetaldehyde = CompoundSpecs(cas="79-02-7", packages=frozenset())
    AcetylChloride = CompoundSpecs(cas="75-36-5", packages=frozenset())
    _2_Chloroethanol = CompoundSpecs(cas="107-07-3", packages=frozenset())
    Argon = CompoundSpecs(cas="7440-37-1", packages=frozenset())
    Bromine = CompoundSpecs(cas="7726-95-6", packages=frozenset())
    Chlorine = CompoundSpecs(cas="7782-50-5", packages=frozenset())
    Hydrogen = CompoundSpecs(cas="1333-74-0", packages=frozenset())
    Neon = CompoundSpecs(cas="7440-01-9", packages=frozenset())
    Nitrogen = CompoundSpecs(cas="7727-37-9", packages=frozenset())
    Oxygen = CompoundSpecs(cas="7782-44-7", packages=frozenset())
    Mercury = CompoundSpecs(cas="7439-97-6", packages=frozenset())
    HydrogenChloride = CompoundSpecs(cas="7647-01-0", packages=frozenset())
    HydrogenIodide = CompoundSpecs(cas="10034-85-2", packages=frozenset())
    NitricAcid = CompoundSpecs(cas="7697-37-2", packages=frozenset())
    Ammonia = CompoundSpecs(cas="7664-41-7", packages=frozenset())
    HydrogenPeroxide = CompoundSpecs(cas="7722-84-1", packages=frozenset())
    Water = CompoundSpecs(cas="7732-18-5", packages=frozenset())
    _1_Naphthalenemethanol = CompoundSpecs(cas="4780-79-4", packages=frozenset())

    @property
    def stringify(self):
        return self.value.cas

    @classmethod
    def from_stringify(cls, key) -> "CASCompoundEnum":
        for member in cls:
            if member.value.cas == key:
                return member
        raise ValueError(f"No compound found with CAS number: {key}")

    def get_key(self) -> str:
        """
        Get the key (compound name) for this enum instance.

        Returns:
            str: The enum key (compound name).
        """
        return self.name

    def get_cas(self) -> str:
        """
        Get the value (CAS number) for this enum instance.

        Returns:
            str: The enum value (CAS number).
        """
        return self.value.cas

    def get_packages(self) -> Set[PropertyPackageEnum]:
        return set(self.value.packages)


########################################
# UNIT OF MEASUREMENT


@unique
class VariableCategoryEnum(BaseGenericEnum):
    # Something a user can set or measure
    SETPOINT = "Setpoint"
    # Something hidden that needs to be guessed or monitored (e.g. efficiency)
    EQUIPMENT_CONDITION = "Condition"
    NONE = "None"


class _ContVarSpecification(_BaseSpecificationSingleton):
    def __init__(
        self,
        label: str,
        unit_of_measurement: str,
        *,
        parameter_category: VariableCategoryEnum = VariableCategoryEnum.EQUIPMENT_CONDITION,
    ):
        self.label = label
        self.unit_of_measurement = unit_of_measurement
        self.parameter_category = parameter_category


@unique
class ContVarSpecEnum(BaseSpecEnum):
    """
    Policy for defining different parameter types with associated default values and behaviors.

    Each member of the enum holds a `Specification` instance containing relevant information
    for a particular parameter.

    """

    ReactionStoich = _ContVarSpecification(
        label="reaction stoichiometry",
        unit_of_measurement="Ratio",
        parameter_category=VariableCategoryEnum.NONE,
    )
    TemperatureDifference = _ContVarSpecification(
        label="temperature difference",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Temperature = _ContVarSpecification(
        label="temperature",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Pressure = _ContVarSpecification(
        label="pressure",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Mass_flow_rate = _ContVarSpecification(
        label="mass flow rate",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Molar_flow_rate = _ContVarSpecification(
        label="molar flow rate",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Volumetric_flow_rate = _ContVarSpecification(
        label="volumetric flow rate",
        unit_of_measurement="m³/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Molar_Enthalpy = _ContVarSpecification(
        label="specific enthalpy",
        unit_of_measurement="kJ/kg",
        parameter_category=VariableCategoryEnum.NONE,
    )
    Molar_Entropy = _ContVarSpecification(
        label="specific entropy",
        unit_of_measurement="kJ/kg K",
        parameter_category=VariableCategoryEnum.NONE,
    )
    CorrectionFactor = _ContVarSpecification(
        label="correction factor",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OverallPressureDrop = _ContVarSpecification(
        label="overall pressure drop",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OrificePressureDrop = _ContVarSpecification(
        label="orifice pressure drop",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OrificeBeta = _ContVarSpecification(
        label="orifice beta",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OrificeDiameter = _ContVarSpecification(
        label="orifice diameter",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    InternalPipeDiameter = _ContVarSpecification(
        label="internalpipe diameter",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Orifice_Temperature_Change = _ContVarSpecification(
        label="orifice temperature change",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PressureIncrease = _ContVarSpecification(
        label="pressure increase",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    OutletPressure = _ContVarSpecification(
        label="outlet pressure",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    AdiabaticEff = _ContVarSpecification(
        label="adiabatic eff",
        unit_of_measurement="",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PolytropicEff = _ContVarSpecification(
        label="polytropic eff",
        unit_of_measurement="",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PowerRequired = _ContVarSpecification(
        label="power required",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    OutletTemperature = _ContVarSpecification(
        label="outlet temperature",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    TemperatureChange = _ContVarSpecification(
        label="temperature change",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    AdiabaticCoeff = _ContVarSpecification(
        label="adiabatic coeff",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PolytropicCoeff = _ContVarSpecification(
        label="polytropic coeff",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    AdiabaticHead = _ContVarSpecification(
        label="adiabatic head",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    PolytropicHead = _ContVarSpecification(
        label="polytropic head",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    RotationSpeed = _ContVarSpecification(
        label="rotation speed",
        unit_of_measurement="rpm",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    PerformanceCurves_FlowRate = _ContVarSpecification(
        label="performance curves flowRate",
        unit_of_measurement="m³/s",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PerformanceCurves_Head = _ContVarSpecification(
        label="performance curves head",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PerformanceCurves_Power = _ContVarSpecification(
        label="performance curves power",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PerformanceCurves_Eff = _ContVarSpecification(
        label="performance_curves eff",
        unit_of_measurement="%",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PerformanceCurves_NPSH = _ContVarSpecification(
        label="performance curves NPSH",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PerformanceCurves_SystemHead = _ContVarSpecification(
        label="performance curves system head",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Efficiency = _ContVarSpecification(
        label="efficiency",
        unit_of_measurement="",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PressureDecrease = _ContVarSpecification(
        label="pressure decrease",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    PowerGenerated = _ContVarSpecification(
        label="power generated",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    PressureDrop = _ContVarSpecification(
        label="pressure drop",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    PressureDrop_ConversionReactor = _ContVarSpecification(
        label="pressure drop (CR)",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Stream_Split_Ratio = _ContVarSpecification(
        label="stream split ratio",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Stream_Split_FlowSpec = _ContVarSpecification(
        label="stream split flow spec",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    OverrideSaturationTemp = _ContVarSpecification(
        label="override saturation temp",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    OverrideSaturationPressure = _ContVarSpecification(
        label="override saturation pressure",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    LightKeyMolFraction = _ContVarSpecification(
        label="light key mol fraction",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    HeavyKeyMolFraction = _ContVarSpecification(
        label="heavy key mol fraction",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    RefluxRatio = _ContVarSpecification(
        label="reflux ratio",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CondenserPressure = _ContVarSpecification(
        label="condener pressure",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ReboilerPressure = _ContVarSpecification(
        label="reboiler pressure",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    MinimumRefluxRatio = _ContVarSpecification(
        label="minimum reflux ratio",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    MinimumNumberOfTrays = _ContVarSpecification(
        label="minimum number of trays",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ActualNumberOfTrays = _ContVarSpecification(
        label="actual number of trays",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OptimalFeedStage = _ContVarSpecification(
        label="optimal feed stage",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    StrippingLiquid = _ContVarSpecification(
        label="stripping liquid",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    RectifyingLiquid = _ContVarSpecification(
        label="rectifying liquid",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    StrippingVapor = _ContVarSpecification(
        label="stripping vapor",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    RectifyingVapor = _ContVarSpecification(
        label="rectifying vapor",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    CondenserDuty = _ContVarSpecification(
        label="condenser duty",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ReboilerDuty = _ContVarSpecification(
        label="reboiler duty",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    HeatLoad = _ContVarSpecification(
        label="heat load",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OutletVaporFraction = _ContVarSpecification(
        label="outlet vapor fraction",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    HeatAddedOrRemoved = _ContVarSpecification(
        label="heat added or removed",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ColdFluidPressureDrop = _ContVarSpecification(
        label="cold fluid pressure drop",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    HotFluidPressureDrop = _ContVarSpecification(
        label="hot fluid pressure drop",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ColdFluidOutletTemperature = _ContVarSpecification(
        label="cold fluid outlet temperature",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    HotFluidOutletTemperature = _ContVarSpecification(
        label="hot fluid outlet temperature",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    GlobalHeatTransferCoefficient = _ContVarSpecification(
        label="global heat transfer coefficient",
        unit_of_measurement="W/(m².K)",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    HeatExchangeArea = _ContVarSpecification(
        label="heat exchange area",
        unit_of_measurement="m²",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    HeatExchange = _ContVarSpecification(
        label="heat exchange",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    MinimumTemperatureDifference = _ContVarSpecification(
        label="minimum temperature difference",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    HeatLoss = _ContVarSpecification(
        label="heat loss",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    HeatTransferEfficiency = _ContVarSpecification(
        label="heat transfer efficiency",
        unit_of_measurement="",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OutletVaporFractionFluid1 = _ContVarSpecification(
        label="outlet vapor fraction fluid 1",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    OutletVaporFractionFluid2 = _ContVarSpecification(
        label="outlet vapor fraction fluid 2",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ShellInSeries = _ContVarSpecification(
        label="shell in series",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ShellPasses = _ContVarSpecification(
        label="shell passes",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    InternalDiameterOfShell = _ContVarSpecification(
        label="internal diameter of shell",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ShellFouling = _ContVarSpecification(
        label="shell fouling",
        unit_of_measurement="K.m²/W",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    BaffleSpacing = _ContVarSpecification(
        label="baffle spacing",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    BaffleCut = _ContVarSpecification(
        label="baffle cut",
        unit_of_measurement="%",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    InternalDiameterOfTube = _ContVarSpecification(
        label="internal diameter of tube",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ExternalDiameterOfTube = _ContVarSpecification(
        label="external diameter of tube",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    TubeLength = _ContVarSpecification(
        label="tube length",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    TubeFouling = _ContVarSpecification(
        label="tube fouling",
        unit_of_measurement="K.m²/W",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    RoughnessTube = _ContVarSpecification(
        label="roughness tube",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ThermalConductivityOfTube = _ContVarSpecification(
        label="thermal conductivity of tube",
        unit_of_measurement="W/[m.K]",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PassesPerShell = _ContVarSpecification(
        label="passes per shell",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    TubesPerShell = _ContVarSpecification(
        label="tubes per shell",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    TubeSpacing = _ContVarSpecification(
        label="tube spacing",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    FluidPressureDrop = _ContVarSpecification(
        label="fluid pressure drop",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OutletFluidTemperature = _ContVarSpecification(
        label="outlet fluid temperature",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    InletAirTemperature = _ContVarSpecification(
        label="inlet air temperature",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    InletAirPressure = _ContVarSpecification(
        label="inlet air pressure",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    OutletAirTemperature = _ContVarSpecification(
        label="outlet air temperature",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    OverallUA = _ContVarSpecification(
        label="overall UA",
        unit_of_measurement="W/K",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    MaximumHeatExchange = _ContVarSpecification(
        label="maximum heat exchange",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ExchangerEfficiency = _ContVarSpecification(
        label="exchanger efficiency",
        unit_of_measurement="",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    NumberOfPasses = _ContVarSpecification(
        label="number of passes",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    NumberOfTubes = _ContVarSpecification(
        label="number of tubes",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ReferenceRotationOfFan = _ContVarSpecification(
        label="reference rotation of fan",
        unit_of_measurement="rpm",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ReferenceAirFlow = _ContVarSpecification(
        label="reference air flow",
        unit_of_measurement="m³/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ActualRotation = _ContVarSpecification(
        label="actual rotation",
        unit_of_measurement="rpm",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ElectricalPowerConversionFactor = _ContVarSpecification(
        label="electrical power conversion factor",
        unit_of_measurement="kW/rpm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ElectricalPowerLoad = _ContVarSpecification(
        label="electrical power load",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ActualAirFlow = _ContVarSpecification(
        label="actual air flow",
        unit_of_measurement="m³/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    DummyCompound = _ContVarSpecification(
        label="compound",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.NONE,
    )
    ConversionReaction = _ContVarSpecification(
        label="conversion reaction",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.NONE,
    )
    CondenserSpecValue = _ContVarSpecification(
        label="condenser spec value",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ReboilerSpecValue = _ContVarSpecification(
        label="reboiler spec value",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ReactionPhase_Liquid = _ContVarSpecification(
        label="reaction phase liquid",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ReactionPhase_Mixture = _ContVarSpecification(
        label="reaction phase mixture",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ReactionPhase_Vapor = _ContVarSpecification(
        label="reaction phase vapor",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Basis_Activity = _ContVarSpecification(
        label="basis activity",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Basis_Fugacity = _ContVarSpecification(
        label="basis fugacity",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Basis_Molar_Concentration = _ContVarSpecification(
        label="basis molar concentration",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Basis_Mass_Concentration = _ContVarSpecification(
        label="basis mass concentration",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Basis_Molar_Fraction = _ContVarSpecification(
        label="basis molar fraction",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Basis_Mass_Fraction = _ContVarSpecification(
        label="basis mass fraction",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    Basis_Partial_Pressure = _ContVarSpecification(
        label="basis partial pressure",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    CondenserPressureDrop = _ContVarSpecification(
        label="condenser pressure drop",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ColumnPressureDrop = _ContVarSpecification(
        label="column pressure drop",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    NumberofStages = _ContVarSpecification(
        label="number of stages",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    EstimatedDiameter = _ContVarSpecification(
        label="estimated diameter",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    EstimatedHeight = _ContVarSpecification(
        label="estimated height",
        unit_of_measurement="mm",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    FeedStage_Gas = _ContVarSpecification(
        label="feed stage gas",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    FeedStage_Liquid = _ContVarSpecification(
        label="feed stage liquid",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    BottomProduct_Stage = _ContVarSpecification(
        label="bottom product stage",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OverheadVapor_Stage = _ContVarSpecification(
        label="overhead vapor stage",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    TotalCondenserSubcoolingTemperatureDrop = _ContVarSpecification(
        label="total condenser subcooling temperature drop",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    VaporProductFlowRate = _ContVarSpecification(
        label="vapor product flow rate",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    HeatLoad_Condenser = _ContVarSpecification(
        label="heat load condenser",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ProductMolarFlow_Condenser = _ContVarSpecification(
        label="product molar flow condenser",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CompoundMolarFlowInProductStream_Condenser = _ContVarSpecification(
        label="compound molar flow in product stream condenser",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ProductMassFlow_Condenser = _ContVarSpecification(
        label="product mass flow condenser",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CompoundMassFlowInProductStream_Condenser = _ContVarSpecification(
        label="compound mass flow in product stream condenser",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CompoundFractionInProductStream_Condenser = _ContVarSpecification(
        label="compound fraction in product stream condenser",
        unit_of_measurement="Mass",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CompoundRecovery_Condenser = _ContVarSpecification(
        label="compound recovery condenser",
        unit_of_measurement="% M/M",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Temperature_Condenser = _ContVarSpecification(
        label="temperature condenser",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    HeatLoad_Reboiler = _ContVarSpecification(
        label="heat load reboiler",
        unit_of_measurement="kW",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ProductMolarFlow_Reboiler = _ContVarSpecification(
        label="product molar flow reboiler",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CompoundMolarFlowInProductStream_Reboiler = _ContVarSpecification(
        label="compound molar flow in product stream reboiler",
        unit_of_measurement="mol/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ProductMassFlow_Reboiler = _ContVarSpecification(
        label="product mass flow reboiler",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CompoundMassFlowInProductStream_Reboiler = _ContVarSpecification(
        label="compound mass flow in product stream reboiler",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CompoundFractionInProductStream_Reboiler = _ContVarSpecification(
        label="compound fraction in product stream reboiler",
        unit_of_measurement="Mass",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CompoundRecovery_Reboiler = _ContVarSpecification(
        label="compound recovery reboiler",
        unit_of_measurement="% M/M",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Boilup_Ratio = _ContVarSpecification(
        label="boilup ratio",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Temperature_Reboiler = _ContVarSpecification(
        label="temperature reboiler",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Dollar_value_per_kg = _ContVarSpecification(
        label="dollar value per kg",
        unit_of_measurement="K",
        parameter_category=VariableCategoryEnum.NONE,
    )
    EF_CO2 = _ContVarSpecification(
        label="emisson factor of CO2",
        unit_of_measurement="kg CO2/kWh",
        parameter_category=VariableCategoryEnum.NONE,
    )
    EF_CH4 = _ContVarSpecification(
        label="emission factor of CH4",
        unit_of_measurement="kg CH4/kWh",
        parameter_category=VariableCategoryEnum.NONE,
    )
    EF_N2O = _ContVarSpecification(
        label="emission factor of N2O",
        unit_of_measurement="kg N2O/kWh",
        parameter_category=VariableCategoryEnum.NONE,
    )
    GWP_CH4 = _ContVarSpecification(
        label="global warming potential of CH4",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.NONE,
    )
    GWP_N2O = _ContVarSpecification(
        label="global warming potential of N2O",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.NONE,
    )
    Calorific_Value = _ContVarSpecification(
        label="calorific value",
        unit_of_measurement="kJ/kg",
        parameter_category=VariableCategoryEnum.NONE,
    )

    ####################
    # Temperature Control Block
    ####################

    PIControllerBias_TC = _ContVarSpecification(
        label="Bias",
        unit_of_measurement="W",
        parameter_category=VariableCategoryEnum.NONE,
    )
    PIControllerGain_TC = _ContVarSpecification(
        label="Gain",
        unit_of_measurement="W/K",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    TempCSetPointRamp_TC = _ContVarSpecification(
        label="Cooling rate - Optimisation mode",
        unit_of_measurement="C/min",
        parameter_category=VariableCategoryEnum.NONE,
    )
    FinalTimeMinutes_TC = _ContVarSpecification(
        label="Duration - controlled cooling profile",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    TempCSetPointInitial_TC = _ContVarSpecification(
        label="Initial temperature - Optimisation mode",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    PIControllerResetTime_TC = _ContVarSpecification(
        label="Integral time",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    PIControllerMaximumOutput_TC = _ContVarSpecification(
        label="Maximum heat output",
        unit_of_measurement="W",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    InitialTemperatureC_TC = _ContVarSpecification(
        label="Maximum/initial temperature - controlled cooling profile",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    TemperatureMeasured_TC = _ContVarSpecification(
        label="Measured temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )  # Output
    PIControllerMinimumOutput_TC = _ContVarSpecification(
        label="Minimum heat output",
        unit_of_measurement="W",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FinalTemperatureC_TC = _ContVarSpecification(
        label="Minimum/final temperature - controlled cooling profile",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    HeatInput_TC = _ContVarSpecification(
        label="Output - Heat input",
        unit_of_measurement="W",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )  # Output
    TemperatureSetPointC_TC = _ContVarSpecification(
        label="Temperature set point",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    # These are for setting Temperature Profile for up to 25 time intervals. Consider putting into collections for more dynamism
    ControlProfileDuration01_TC = _ContVarSpecification(
        label="control profile duration 01",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration02_TC = _ContVarSpecification(
        label="control profile duration 02",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration03_TC = _ContVarSpecification(
        label="control profile duration 03",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration04_TC = _ContVarSpecification(
        label="control profile duration 04",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration05_TC = _ContVarSpecification(
        label="control profile duration 05",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration06_TC = _ContVarSpecification(
        label="control profile duration 06",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration07_TC = _ContVarSpecification(
        label="control profile duration 07",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration08_TC = _ContVarSpecification(
        label="control profile duration 08",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration09_TC = _ContVarSpecification(
        label="control profile duration 09",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration10_TC = _ContVarSpecification(
        label="control profile duration 10",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration11_TC = _ContVarSpecification(
        label="control profile duration 11",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration12_TC = _ContVarSpecification(
        label="control profile duration 12",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration13_TC = _ContVarSpecification(
        label="control profile duration 13",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration14_TC = _ContVarSpecification(
        label="control profile duration 14",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration15_TC = _ContVarSpecification(
        label="control profile duration 15",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration16_TC = _ContVarSpecification(
        label="control profile duration 16",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration17_TC = _ContVarSpecification(
        label="control profile duration 17",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration18_TC = _ContVarSpecification(
        label="control profile duration 18",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration19_TC = _ContVarSpecification(
        label="control profile duration 19",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration20_TC = _ContVarSpecification(
        label="control profile duration 20",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration21_TC = _ContVarSpecification(
        label="control profile duration 21",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration22_TC = _ContVarSpecification(
        label="control profile duration 22",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration23_TC = _ContVarSpecification(
        label="control profile duration 23",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration24_TC = _ContVarSpecification(
        label="control profile duration 24",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileDuration25_TC = _ContVarSpecification(
        label="control profile duration 25",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature01_TC = _ContVarSpecification(
        label="control profile temperature 01",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature02_TC = _ContVarSpecification(
        label="control profile temperature 02",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature03_TC = _ContVarSpecification(
        label="control profile temperature 03",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature04_TC = _ContVarSpecification(
        label="control profile temperature 04",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature05_TC = _ContVarSpecification(
        label="control profile temperature 05",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature06_TC = _ContVarSpecification(
        label="control profile temperature 06",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature07_TC = _ContVarSpecification(
        label="control profile temperature 07",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature08_TC = _ContVarSpecification(
        label="control profile temperature 08",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature09_TC = _ContVarSpecification(
        label="control profile temperature 09",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature10_TC = _ContVarSpecification(
        label="control profile temperature 10",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature11_TC = _ContVarSpecification(
        label="control profile temperature 11",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature12_TC = _ContVarSpecification(
        label="control profile temperature 12",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature13_TC = _ContVarSpecification(
        label="control profile temperature 13",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature14_TC = _ContVarSpecification(
        label="control profile temperature 14",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature15_TC = _ContVarSpecification(
        label="control profile temperature 15",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature16_TC = _ContVarSpecification(
        label="control profile temperature 16",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature17_TC = _ContVarSpecification(
        label="control profile temperature 17",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature18_TC = _ContVarSpecification(
        label="control profile temperature 18",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature19_TC = _ContVarSpecification(
        label="control profile temperature 19",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature20_TC = _ContVarSpecification(
        label="control profile temperature 20",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature21_TC = _ContVarSpecification(
        label="control profile temperature 21",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature22_TC = _ContVarSpecification(
        label="control profile temperature 22",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature23_TC = _ContVarSpecification(
        label="control profile temperature 23",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature24_TC = _ContVarSpecification(
        label="control profile temperature 24",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ControlProfileTemperature25_TC = _ContVarSpecification(
        label="control profile temperature 25",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )

    ####################
    # Crystallizer
    ####################

    # Equipment and operation
    EquipmentVolume_CR = _ContVarSpecification(
        label="Equipment and operation - Equipment volume",
        unit_of_measurement="m3",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    EquipmentHeight_CR = _ContVarSpecification(
        label="Equipment and operation - Equipment height",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    EquipmentCrossSectionalArea_CR = _ContVarSpecification(
        label="Equipment and operation - Equipment cross sectional area",
        unit_of_measurement="m2",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    EquipmentDiameter_CR = _ContVarSpecification(
        label="Equipment and operation - Equipment diameter",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    FlowRateSetPoint_CR = _ContVarSpecification(
        label="Equipment and operation - Flow rate set point",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    MeanResidenceTime_CR = _ContVarSpecification(
        label="Equipment and operation - Mean residence time",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    RelativeFill_CR = _ContVarSpecification(
        label="Equipment and operation - Relative fill",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    TimeConstantOfFlowResponse_CR = _ContVarSpecification(
        label="Equipment and operation - Time constant of flow response",
        unit_of_measurement="kg/s",  # TODO TBD
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PressureSetPoint_CR = _ContVarSpecification(
        label="Equipment and operation - Pressure set point",
        unit_of_measurement="bar",  # TODO TBD
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    VaporOutflowSetPoint_CR = _ContVarSpecification(
        label="Equipment and operation - Vapor outflow set point",
        unit_of_measurement="kg/s",  # TODO TBD
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    LiquidEntrainment_CR = _ContVarSpecification(
        label="Equipment and operation - Liquid entrainment",
        unit_of_measurement="%",  # TODO TBD
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ImpellerDiameter_CR = _ContVarSpecification(
        label="Equipment and operation - Equipment - Impeller diameter",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ImpellerFrequency_CR = _ContVarSpecification(
        label="Equipment and operation - Operation - Impeller frequency",
        unit_of_measurement="RPM",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ImpellerPowerNumber_CR = _ContVarSpecification(
        label="Equipment and operation - Equipment - Impeller power number",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ImpellerPumpingNumber_CR = _ContVarSpecification(
        label="Equipment and operation - Equipment - Impeller pumping number",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SpecificPowerInput_CR = _ContVarSpecification(
        label="Equipment and operation - Operation - Specific power input",
        unit_of_measurement="W/kg",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    # Energy balance
    EnergyInput_CR = _ContVarSpecification(
        label="Heat transfer - Operation - Energy input",
        unit_of_measurement="W",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    HeatingCoolingFluidTemperature_CR = _ContVarSpecification(
        label="Heat transfer - Operation - Heating/cooling fluid temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OverallHeatTransferCoefficient_CR = _ContVarSpecification(
        label="Heat transfer - Operation - Overall heat transfer coefficient",
        unit_of_measurement="W m-2 C-1",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    HeatTransferArea_CR = _ContVarSpecification(
        label="Heat transfer - Equipment - Heat transfer area",
        unit_of_measurement="m2",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    OverallHeatTransferCoefficientXArea_CR = _ContVarSpecification(
        label="Heat transfer - Operation - Overall heat transfer coefficient times area",
        unit_of_measurement="W/K",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    AmbientHeatLoss_CR = _ContVarSpecification(
        label="Heat transfer - Operation - Ambient heat loss",
        unit_of_measurement="W",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    AmbientHeatTransferCoefficient_CR = _ContVarSpecification(
        label="Heat transfer - Operation - Ambient heat transfer coefficient",
        unit_of_measurement="W m-2 C-1",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    AreaForAmbientHeatLoss_CR = _ContVarSpecification(
        label="Heat transfer - Operation - area for ambient heat loss",
        unit_of_measurement="m2",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    AmbientTemperature_CR = _ContVarSpecification(
        label="Heat transfer - Operation - Ambient temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PrimaryNucleationClassicalRateConstant_CR = _ContVarSpecification(
        label="Primary nucleation - Kinetic parameter - Classical kinetics (Mullin):  Rate constant (ln)",
        unit_of_measurement="ln(#/m3.s)",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PrimaryNucleationClassicalSurfaceEnergy_CR = _ContVarSpecification(
        label="Primary nucleation - Kinetic parameter - Classical kinetics (Mullin):  Surface energy",
        unit_of_measurement="J/m2",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PrimaryNucleationClassicalSurfaceEnergyCorrectionFactor_CR = _ContVarSpecification(
        label="Primary nucleation - Kinetic parameter - Classical kinetics (Mullin):  Surface energy correction factor",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PrimaryNucleationPowerLawRateConstant_CR = _ContVarSpecification(
        label="Primary nucleation - Kinetic parameter - power law kinetics:  Rate constant (ln)",
        unit_of_measurement="ln(#/m3.s)",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PrimaryNucleationPowerLawSupersaturationOrder_CR = _ContVarSpecification(
        label="Primary nucleation - Kinetic parameter - power law kinetics:  Supersaturation order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    PrimaryNucleationPowerLawActivationEnergy_CR = _ContVarSpecification(
        label="Primary nucleation - Kinetic parameter - power law kinetics:  Activation Energy",
        unit_of_measurement="J/mol",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedMersmannRateConstant_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Mersmann kinetics: Rate constant (ln)",
        unit_of_measurement="ln(-)",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedMersmannSolubilityFactor_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Mersmann kinetics: Solubility factor",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedMersmannNumEntities_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Mersmann kinetics: Number of entities",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedMersmannSurfaceAreaShapeFactor_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Mersmann kinetics: Surface area shape factor",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedPowerLawRateConstant_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Power law kinetics: Rate constant (ln)",
        unit_of_measurement="ln(#/m3.s)",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedPowerLawSupersaturationOrder_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Power law kinetics: Supersaturation order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedPowerLawActivationEnergy_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Power law kinetics: Activation energy",
        unit_of_measurement="J/mol",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedPowerLawSpecificPowerInputOrder_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Power law kinetics: Specific power input order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedPowerLawSurfaceAreaOrder_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Power law kinetics: Surface area order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationActivatedPowerLawSurfaceAreaShapeFactor_CR = _ContVarSpecification(
        label="Secondary nucleation (activated) - Kinetic parameter - Power law kinetics: Surface area shape factor",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    # Secondary nucleation - attrition
    SecondaryNucleationAttritionEvansRateConstantCC_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Evans kinetics: Rate constant (CC) (ln)",
        unit_of_measurement="ln(#/m3.s)",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionEvansSizeAboveCC_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Evans kinetics: Size above which crystals undergo attrition (CC)",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionEvansSupersaturationOrderCC_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Evans kinetics: Order with respect to supersaturation (CC)",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionEvansRateConstantCI_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Evans kinetics: Rate constant (CI) (ln)",
        unit_of_measurement="ln(#/m3.s)",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionEvansSizeAboveCI_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Evans kinetics: Size above which crystals undergo attrition (CI)",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionEvansSupersaturationOrderCI_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Evans kinetics: Order with respect to supersaturation (CI)",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionPowerLawRateConstant_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Power law kinetics: Rate constant (ln)",
        unit_of_measurement="ln(#/m3.s)",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionPowerLawSupersaturationOrder_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Power law kinetics: Supersaturation order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionPowerLawActivationEnergy_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Power law kinetics: Activation energy",
        unit_of_measurement="J/mol",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionPowerLawSpecificPowerInputOrder_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Power law kinetics: Specific power input order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    SecondaryNucleationAttritionPowerLawSlurryDensityOrder_CR = _ContVarSpecification(
        label="Secondary nucleation (attrition) - Kinetic parameter - Power law kinetics: Slurry density order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    # Growth and dissolution
    GrowthAndDissolutionClassicalGrowthRateConstant_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Two-step kinetics (classical): Growth rate constant",
        unit_of_measurement="m/s",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionClassicalSupersaturationOrder_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Two-step kinetics (classical): Supersaturation order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionClassicalActivationEnergy_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Two-step kinetics (classical): Activation energy",
        unit_of_measurement="J/mol",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionMersmannGrowthRateConstant_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Two-step kinetics (Mersmann): Growth rate constant",
        unit_of_measurement="m/s",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionMersmannSupersaturationOrder_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Two-step kinetics (Mersmann): Supersaturation order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionMersmannActivationEnergy_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Two-step kinetics (Mersmann): Activation energy",
        unit_of_measurement="J/mol",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionPowerLawGrowthRateConstant_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Power law kinetics: Growth rate constant",
        unit_of_measurement="m/s",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionPowerLawSupersaturationOrder_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Power law kinetics: Supersaturation order",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionPowerLawActivationEnergy_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Power law kinetics: Activation energy",
        unit_of_measurement="J/mol",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionEffectiveDiffusitivityCorrectionFactor_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Two-step kinetics: Effective diffusivity correction factor",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionVesselDiameter_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Two-step kinetics: Levins and Glastonbury correlation vessel diameter",
        unit_of_measurement="m",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthAndDissolutionSurfaceEnergyForSizeDependantSolubility_CR = _ContVarSpecification(
        label="Growth and dissolution - Kinetic parameter - Size-dependent solubility: Surface energy",
        unit_of_measurement="J/m2",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    # Agglomeration
    AgglomerationParameterA50_CR = _ContVarSpecification(
        label="Agglomeration - Kinetic parameter - Mumtaz kinetics: Agglomeration parameter (A50)",
        unit_of_measurement="N/m",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    ConstantTermA1_CR = _ContVarSpecification(
        label="Agglomeration - Kinetic parameter - Power law kinetics: Constant term (a1)",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )
    GrowthRateOrderA2_CR = _ContVarSpecification(
        label="Agglomeration - Kinetic parameter - Power law kinetics: Growth rate order (a2)",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )  # gPROMS UI label shows they are for Mumtaz kinetic but really they are for power law
    SpecificPowerInputOrderA3_CR = _ContVarSpecification(
        label="Agglomeration - Kinetic parameter - Power law kinetics: Specific power input order (a3)",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    )  # gPROMS UI label shows they are for Mumtaz kinetic but really they are for power law
    # Evaporation
    # EvaporationVolumetricLiquidMassTransferCoefficient_CR = _ContVarSpecification(
    #     label="Evaporation - Kinetic parameter - Volumetric liquid mass transfer coefficient (kLa)",
    #     unit_of_measurement="s-1",
    #     parameter_category=VariableCategoryEnum.SETPOINT,
    # )
    # EvaporationVolumetricVaporMassTransferCoefficient_CR = _ContVarSpecification(
    #     label="Evaporation - Kinetic parameter - Volumetric vapor mass transfer coefficient (kga)",
    #     unit_of_measurement="s-1",
    #     parameter_category=VariableCategoryEnum.SETPOINT,
    # )
    # EvaporationUniformVolumetricLiquidMassTransferCoefficient_CR = _ContVarSpecification(
    #     label="Evaporation - Kinetic parameter - Uniform volumetric liquid mass transfer coefficient (kLa) for all species transferred",
    #     unit_of_measurement="s-1",
    #     parameter_category=VariableCategoryEnum.SETPOINT,
    # )
    # EvaporationUniformVolumetricVaporMassTransferCoefficient_CR = _ContVarSpecification(
    #     label="Evaporation - Kinetic parameter - Uniform volumetric vapor mass transfer coefficient (kga) for all species transferred",
    #     unit_of_measurement="s-1",
    #     parameter_category=VariableCategoryEnum.SETPOINT,
    # )
    EvaporationLiquidMassTransferCoefficient_CR = _ContVarSpecification(
        label="Evaporation - Kinetic parameter - Liquid mass transfer coefficient (kL)",
        unit_of_measurement="m/s",
        parameter_category=VariableCategoryEnum.NONE,
    )
    EvaporationVaporMassTransferCoefficient_CR = _ContVarSpecification(
        label="Evaporation - Kinetic parameter - Vapor mass transfer coefficient (kg)",
        unit_of_measurement="m/s",
        parameter_category=VariableCategoryEnum.NONE,
    )
    EvaporationUniformLiquidMassTransferCoefficient_CR = _ContVarSpecification(
        label="Evaporation - Kinetic parameter - Uniform liquid mass transfer coefficient (kL) for all species transferred",
        unit_of_measurement="m/s",
        parameter_category=VariableCategoryEnum.NONE,
    )
    EvaporationUniformVaporMassTransferCoefficient_CR = _ContVarSpecification(
        label="Evaporation - Kinetic parameter - Uniform vapor mass transfer coefficient (kg) for all species transferred",
        unit_of_measurement="m/s",
        parameter_category=VariableCategoryEnum.NONE,
    )
    EvaporationSpecificContactArea_CR = _ContVarSpecification(
        label="Evaporation - Kinetic parameter - Specific contact area",
        unit_of_measurement="m-1",
        parameter_category=VariableCategoryEnum.NONE,
    )
    EvaporationContactArea_CR = _ContVarSpecification(
        label="Evaporation - Kinetic parameter - Contact area",
        unit_of_measurement="m2",
        parameter_category=VariableCategoryEnum.NONE,
    )
    # Scheduler: Discharge
    SchedulerDischargeNumberOfDischarges_CR = _ContVarSpecification(
        label="Discharge: Number of discharges",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeDurationOfDischarge_CR = _ContVarSpecification(
        label="Discharge: Duration of discharge",
        unit_of_measurement="min",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeMassFlowRate_CR = _ContVarSpecification(
        label="Discharge: Discharge mass flow rate",
        unit_of_measurement="kg/h",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeVolumeFlowRate_CR = _ContVarSpecification(
        label="Discharge: Discharge volume flow rate",
        unit_of_measurement="L/h",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeFractionOfMassHoldupDischarged_CR = _ContVarSpecification(
        label="Discharge: Fraction of mass holdup discharged",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeFractionOfVolumeHoldupDischarged_CR = _ContVarSpecification(
        label="Discharge: Fraction of volume holdup discharged",
        unit_of_measurement="m3/m3",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeMassHoldupDischarged_CR = _ContVarSpecification(
        label="Discharge: Mass holdup discharged",
        unit_of_measurement="kg",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeVolumeHoldupDischarged_CR = _ContVarSpecification(
        label="Discharge: Volume holdup discharged",
        unit_of_measurement="L",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeMassHoldupLeftBehind_CR = _ContVarSpecification(
        label="Discharge: Mass holdup left behind",
        unit_of_measurement="kg",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeVolumeHoldupLeftBehind_CR = _ContVarSpecification(
        label="Discharge: Volume holdup left behind",
        unit_of_measurement="L",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SchedulerDischargeTargetRelativeFill_CR = _ContVarSpecification(
        label="Discharge: Target relative fill",
        unit_of_measurement="m3/m3",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )

    # Output variables (KPIs)
    CrystallizationKeyMeasurementCrystalMass_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Crystal mass",
        unit_of_measurement="kg",
        parameter_category=VariableCategoryEnum.NONE,
    )
    CrystallizationKeyMeasurementLiquidMass_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Liquid mass",
        unit_of_measurement="kg",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CrystallizationKeyMeasurementMassConcentrationSoluteAtSaturation_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Mass concentration of solute at saturation",
        unit_of_measurement="kg/m3",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CrystallizationKeyMeasurementMassConcentrationSoluteInLiquid_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Mass concentration of solute in liquid",
        unit_of_measurement="kg/m3",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CrystallizationKeyMeasurementMassFractionSoluteAtSaturation_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Mass fraction of solute at saturation",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CrystallizationKeyMeasurementMassFractionSoluteInLiquid_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Mass fraction of solute in liquid",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CrystallizationKeyMeasurementMassOfSoluteInLiquid_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Mass of solute in liquid",
        unit_of_measurement="kg",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CrystallizationKeyMeasurementRelativeSaturation_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Relative saturation",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CrystallizationKeyMeasurementRelativeSupersaturation_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Relative supersaturation",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CrystallizationKeyMeasurementTemperature_CR = _ContVarSpecification(
        label="Crystallization - Key measurement - Temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CrystallizationReportVariableAverageCrystalGrowth_CR = _ContVarSpecification(
        label="Crystallization - Report variable - Average crystal growth",
        unit_of_measurement="um/s",
        parameter_category=VariableCategoryEnum.NONE,
    )
    CrystallizationReportVariableBirthRateOfCrystals_CR = _ContVarSpecification(
        label="Crystallization - Report variable - Birth rate of crystals at lower bound of size range",
        unit_of_measurement="log(#/m3.s)",
        parameter_category=VariableCategoryEnum.NONE,
    )
    CrystallizationReportVariablePrimaryNucleationRate_CR = _ContVarSpecification(
        label="Crystallization - Report variable - Primary nucleation rate",
        unit_of_measurement="log(#/m3.s)",
        parameter_category=VariableCategoryEnum.NONE,
    )
    CrystallizationReportVariableSecondaryNucleationActivatedRate_CR = _ContVarSpecification(
        label="Crystallization - Report variable - Secondary nucleation (activated) rate",
        unit_of_measurement="log(#/m3.s)",
        parameter_category=VariableCategoryEnum.NONE,
    )
    CrystallizationReportVariableSecondaryNucleationAttritionRate_CR = _ContVarSpecification(
        label="Crystallization - Report variable - Secondary nucleation (attrition) rate",
        unit_of_measurement="log(#/m3.s)",
        parameter_category=VariableCategoryEnum.NONE,
    )
    EvaporationKeyMeasurementLiquidMassConcentration_CR = _ContVarSpecification(
        label="Evaporation - Key measurement - Liquid mass concentration",
        unit_of_measurement="kg/m3",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    EvaporationKeyMeasurementLiquidMolarConcentration_CR = _ContVarSpecification(
        label="Evaporation - Key measurement - Liquid molar concentration",
        unit_of_measurement="mol/m3",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    EvaporationKeyMeasurementMassFraction_CR = _ContVarSpecification(
        label="Evaporation - Key measurement - Mass fraction",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    EvaporationKeyMeasurementMolarFraction_CR = _ContVarSpecification(
        label="Evaporation - Key measurement - Molar fraction",
        unit_of_measurement="na",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    EvaporationKeyMeasurementVapourMassConcentration_CR = _ContVarSpecification(
        label="Evaporation - Key measurement - Vapour mass concentration",
        unit_of_measurement="kg/m3",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    EvaporationKeyMeasurementVapourMolarConcentration_CR = _ContVarSpecification(
        label="Evaporation - Key measurement - Vapour molar concentration",
        unit_of_measurement="mol/m3",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    EvaporationReportVariableEvaporationRate_CR = _ContVarSpecification(
        label="Evaporation - Report variable - Evaporation rate",
        unit_of_measurement="mol m-3 s-1",
        parameter_category=VariableCategoryEnum.NONE,
    )
    FlowAndHoldupReportVariableContinuousPhaseTemperature_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Continuous phase temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableDistributedPhaseTemperature_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Distributed phase temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableInletContinuousPhaseMassFlowRate_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Inlet continuous phase mass flow rate",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableInletContinuousPhaseTemperature_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Inlet continuous phase temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableInletDistributedPhaseMassFlowRate_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Inlet distributed phase mass flow rate",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableInletDistributedPhaseTemperature_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Inlet distributed phase temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableLiquidOutletContinuousPhaseMassFlowRate_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Liquid outlet continuous phase mass flow rate",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableLiquidOutletContinuousPhaseTemperature_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Liquid outlet continuous phase temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableLiquidOutletDistributedPhaseMassFlowRate_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Liquid outlet distributed phase mass flow rate",
        unit_of_measurement="kg/s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableLiquidOutletDistributedPhaseTemperature_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Liquid outlet distributed phase temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableMassHoldupOfContinuousPhase_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Mass holdup of continuous phase",
        unit_of_measurement="kg",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableMassHoldupOfDistributedPhase_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Mass holdup of distributed phase",
        unit_of_measurement="kg",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariablePressure_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Pressure",
        unit_of_measurement="Pa",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableTotalVolumeHoldup_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Total volume holdup",
        unit_of_measurement="m3",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableVaporOutletContinuousPhaseMassFlowRate_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Vapor outlet continuous phase mass flow rate",
        unit_of_measurement="kg/h",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableVaporOutletContinuousPhaseTemperature_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Vapor outlet continuous phase temperature",
        unit_of_measurement="C",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    FlowAndHoldupReportVariableVaporOutletEntrainedLiquidMassFlowRate_CR = _ContVarSpecification(
        label="Flow and holdup - Report variable - Vapor outlet entrained liquid mass flow rate",
        unit_of_measurement="kg/h",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ImpuritiesKeyMeasurementCrystalPhaseComposition_CR = _ContVarSpecification(
        label="Impurities - Key measurement - Crystal phase composition",
        unit_of_measurement="kg/kg",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    ImpuritiesReportVariableCrystalGrowthDissolutionRate_CR = _ContVarSpecification(
        label="Impurities - Report variable - Crystal growth/dissolution rate",
        unit_of_measurement="um/s",
        parameter_category=VariableCategoryEnum.NONE,
    )
    ImpuritiesReportVariablePureCrystalGrowthDissolutionRate_CR = _ContVarSpecification(
        label="Impurities - Report variable - Pure crystal growth/dissolution rate",
        unit_of_measurement="um/s",
        parameter_category=VariableCategoryEnum.NONE,
    )
    ImpuritiesReportVariableUnmodifiedCrystalGrowthDissolutionRate_CR = _ContVarSpecification(
        label="Impurities - Report variable - Unmodified crystal growth/dissolution rate",
        unit_of_measurement="um/s",
        parameter_category=VariableCategoryEnum.NONE,
    )

    ####################
    # PSD Sensor
    ####################

    AverageParticleSize_PSD = _ContVarSpecification(
        label="Average particle size, D[4,3]",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    CoefficientOfVariation_PSD = _ContVarSpecification(
        label="Coefficient of Variation (CV)",
        unit_of_measurement="%",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Quantile05_PSD = _ContVarSpecification(
        label="Quantile('05%'')",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Quantile10_PSD = _ContVarSpecification(
        label="Quantile('10%'')",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Quantile16_PSD = _ContVarSpecification(
        label="Quantile('16%'')",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Quantile25_PSD = _ContVarSpecification(
        label="Quantile('25%'')",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Quantile50_PSD = _ContVarSpecification(
        label="Quantile('50%'')",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Quantile75_PSD = _ContVarSpecification(
        label="Quantile('75%'')",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Quantile84_PSD = _ContVarSpecification(
        label="Quantile('84%'')",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Quantile90_PSD = _ContVarSpecification(
        label="Quantile('90%'')",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Quantile95_PSD = _ContVarSpecification(
        label="Quantile('95%'')",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    SauterMean_PSD = _ContVarSpecification(
        label="Sauter mean, D[3,2]",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    Span_PSD = _ContVarSpecification(
        label="Span",
        unit_of_measurement="%",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )
    VolumeMean_PSD = _ContVarSpecification(
        label="Volume mean, D[4,3]",
        unit_of_measurement="um",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )

    ####################
    # PSD Sensor
    ####################

    SimulationDuration_SD = _ContVarSpecification(
        label="Simulation duration",
        unit_of_measurement="s",
        parameter_category=VariableCategoryEnum.SETPOINT,
    )

    ###########################################################################
    ###########################################################################
    ## These will be used later when other classes are being used
    ## CSTR, PFR, Reactions sets and so on
    ###########################################################################
    ###########################################################################
    # VAPOR_MOLE_FRAC = _ContVarSpecification(
    #     label="vapor_mol_frac",
    #     unit_of_measurement=Z_PropertyLabelEnum.OTHER,
    #     parameter_category=VariableCategoryEnum.SETPOINT,
    # )
    # CompoundMassRatio = _ContVarSpecification(
    #     label="compound_mass_ratio",
    #     unit_of_measurement=Z_PropertyLabelEnum.OTHER,
    #     parameter_category=VariableCategoryEnum.NONE,
    # )
    # DummyReactionSet = _ContVarSpecification(
    #     label="reaction_set",
    #     unit_of_measurement=Z_PropertyLabelEnum.OTHER,
    #     parameter_category=VariableCategoryEnum.NONE,
    # )
    # FinalGibbsEnergy = _ContVarSpecification(
    #     "final_gibbs_energy",
    #     Z_PropertyLabelEnum.POWER_REQUIRED,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # InitialGibbsEnergy = _ContVarSpecification(
    #     "initial_gibbs_energy",
    #     Z_PropertyLabelEnum.POWER_REQUIRED,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # CatalystVoidFraction = _ContVarSpecification(
    #     "catalyst_void_fraction",
    #     Z_PropertyLabelEnum.RATIO,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # CatalystParticleDiameter = _ContVarSpecification(
    #     "catalyst_particle_diameter",
    #     Z_PropertyLabelEnum.PARTICLE_DIAMETER,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # ReactionCoordinate = _ContVarSpecification(
    #     "reaction_coordinate",
    #     Z_PropertyLabelEnum.molar_flow_rate_,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # ReactionRate = _ContVarSpecification(
    #     "reaction_rate",
    #     Z_PropertyLabelEnum.REACTION_RATE,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # ReactionHeat = _ContVarSpecification(
    #     "reaction_heat",
    #     Z_PropertyLabelEnum.REACTION_HEAT,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # CondenserDeltaP = _ContVarSpecification(
    #     "condenser_delta_P",
    #     Z_PropertyLabelEnum.PRESSURE_DIFFERENCE,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # ReactionExtents = _ContVarSpecification(
    #     "reaction_extents",
    #     Z_PropertyLabelEnum.molar_flow_rate_,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # CompoundStoichiometricCoeff = _ContVarSpecification(
    #     "compound_stoichiometric_coeff",
    #     Z_PropertyLabelEnum.COMPOUND_STOICHIOMETRIC_COEFF,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # LiquidResidenceTime = _ContVarSpecification(
    #     "liquid_residence_time,",
    #     Z_PropertyLabelEnum.TIME,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # VaporResidenceTime = _ContVarSpecification(
    #     "vapor_residence_time",
    #     Z_PropertyLabelEnum.TIME,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # ReactorVolume = _ContVarSpecification(
    #     "reactor_volume",
    #     Z_PropertyLabelEnum.VOLUME,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # HeadSpace = _ContVarSpecification(
    #     "head_space",
    #     Z_PropertyLabelEnum.VOLUME,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # CatalystAmount = _ContVarSpecification(
    #     "catalyst_amount",
    #     Z_PropertyLabelEnum.CATALYST_AMOUNT,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # BaseCompound = _ContVarSpecification(
    #     "base_compound",
    #     Z_PropertyLabelEnum.BASE_COMPOUND,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # ReactionTemperatureApproach = _ContVarSpecification(
    #     "reaction_temperature_approach",
    #     Z_PropertyLabelEnum.TEMPERATURE,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # ReactionConstantValue = _ContVarSpecification(
    #     "reaction_constant_value",
    #     Z_PropertyLabelEnum.COUNT,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # ReactionTFunction = _ContVarSpecification(
    #     "reaction_tfunction",
    #     Z_PropertyLabelEnum.REACTION_T_FUNCTION,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # AmountUnit = _ContVarSpecification(
    #     "amount_unit",
    #     Z_PropertyLabelEnum.AMOUNT_UNIT,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # RateUnit = _ContVarSpecification(
    #     "rate_unit",
    #     Z_PropertyLabelEnum.RATE_UNIT,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # A = _ContVarSpecification(
    #     "A",
    #     Z_PropertyLabelEnum.COUNT,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # A_Prime = _ContVarSpecification(
    #     "A_Prime",
    #     Z_PropertyLabelEnum.COUNT,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # E = _ContVarSpecification(
    #     "E",
    #     Z_PropertyLabelEnum.COUNT,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # E_Prime = _ContVarSpecification(
    #     "E_Prime",
    #     Z_PropertyLabelEnum.COUNT,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # DirectOrder = _ContVarSpecification(
    #     "direct_order",
    #     Z_PropertyLabelEnum.DIRECT_ORDER,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # ReverseOrder = _ContVarSpecification(
    #     "reverse_order",
    #     Z_PropertyLabelEnum.REVERSE_ORDER,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )

    # # BaffleType

    # LightKeyCompound = _ContVarSpecification(
    #     "light_key_compound",
    #     Z_PropertyLabelEnum.BASE_COMPOUND,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )
    # HeavyKeyCompound = _ContVarSpecification(
    #     "heavy_key_compound",
    #     Z_PropertyLabelEnum.BASE_COMPOUND,
    #     parameter_category=VariableCategoryEnum.EQUIPMENT_CONDITION,
    # )

    # Max_Iteration = _ContVarSpecification(
    #     "max_iteration",
    #     Z_PropertyLabelEnum.COUNT,
    #     parameter_category=VariableCategoryEnum.SETPOINT,
    # )
    # Convergence_Tolerance_Mass_Flow = _ContVarSpecification(
    #     "convergence_tolerance_mass_flow",
    #     Z_PropertyLabelEnum.COUNT,
    #     parameter_category=VariableCategoryEnum.SETPOINT,
    # )
    # Convergence_Tolerance_Temperature = _ContVarSpecification(
    #     "convergence_tolerance_emperature",
    #     Z_PropertyLabelEnum.COUNT,
    #     parameter_category=VariableCategoryEnum.SETPOINT,
    # )
    # Convergence_Tolerance_Pressure = _ContVarSpecification(
    #     "convergence_tolerance_pressure",
    #     Z_PropertyLabelEnum.COUNT,
    #     parameter_category=VariableCategoryEnum.SETPOINT,
    # )

    ###########################################################################
    ###########################################################################
    ###########################################################################

    def __repr__(self):
        return f"{self.__class__.__name__}.{self.name}"

    def get_specification(self) -> _ContVarSpecification:
        return self.value

    @property
    def unit(self) -> str:
        return self.get_specification().unit_of_measurement

    @property
    def category(self) -> VariableCategoryEnum:
        """Indicates if its a setpoint or condition"""
        return self.value.parameter_category

    def _get_stringify(self) -> str:
        """returns a string of the Variable"""
        return self.value.label


# class _SplitStreamSpecification(_BaseSpecificationSingleton):
#     def __init__(
#         self,
#         label: str,
#         unit_of_measurement: str,
#         *,
#         parameter_category: VariableCategoryEnum = VariableCategoryEnum.EQUIPMENT_CONDITION,
#     ):
#         self.label = label
#         self.unit_of_measurement = unit_of_measurement
#         self.parameter_category = parameter_category


# class SplitStreamSpecEnum(BaseSpecEnum):
#     S1 = _SplitStreamSpecification(
#         label="split 1 ratio",
#         unit_of_measurement="%",
#         parameter_category=VariableCategoryEnum.SETPOINT,
#     )
#     S2 = _SplitStreamSpecification(
#         label="split 2 ratio",
#         unit_of_measurement="%",
#         parameter_category=VariableCategoryEnum.SETPOINT,
#     )
#     S3 = _SplitStreamSpecification(
#         label="split 3 ratio",
#         unit_of_measurement="%",
#         parameter_category=VariableCategoryEnum.SETPOINT,
#     )
#     S4 = _SplitStreamSpecification(
#         label="split 4 ratio",
#         unit_of_measurement="%",
#         parameter_category=VariableCategoryEnum.SETPOINT,
#     )


class _CompoundMixSpecification(_BaseSpecificationSingleton):
    def __init__(
        self,
        parameter_label: str,
        unit_of_measurement: str,
        *,
        parameter_category: VariableCategoryEnum = VariableCategoryEnum.EQUIPMENT_CONDITION,
    ):
        self.parameter_label = parameter_label
        self.unit_of_measure = unit_of_measurement
        self.parameter_category = parameter_category


@unique
class CompoundMixSpecEnum(BaseSpecEnum):
    """
    Policy for defining different parameter types with associated default values and behaviors.

    Each member of the enum holds a `Specification` instance containing relevant information
    for a particular parameter.
    """

    COMPOUNDMIX = _CompoundMixSpecification(
        parameter_label="compound_mix",
        unit_of_measurement="NA",
        parameter_category=VariableCategoryEnum.NONE,
    )

    def get_specification(self) -> _CompoundMixSpecification:
        return self.value

    @property
    def unit(self) -> str:
        return self.get_specification().unit_of_measure

    @property
    def category(self) -> VariableCategoryEnum:
        """Indicates if its a setpoint or condition"""
        return self.value.parameter_category

    def _get_stringify(self) -> str:
        """returns a string of the Variable"""
        return self.value.parameter_label


########################################
# CALC POLICIES
########################################


class _DiscreteSetSpecification(_BaseSpecificationSingleton):
    def __init__(
        self,
        parameter_label: str,
    ):
        self.parameter_label = parameter_label


@unique
class DiscreteSetSpecEnum(BaseSpecEnum):
    CompoundMix = _DiscreteSetSpecification("mass_fraction_selections")
    Z_MolFraction = _DiscreteSetSpecification("mole_fraction_selections")
    ComponentConversions = _DiscreteSetSpecification("component_conversions_selections")
    Conversions = _DiscreteSetSpecification("conversions_selections")
    CalculationType = _DiscreteSetSpecification("calculation_type_selections")
    PressureCalculation = _DiscreteSetSpecification("pressure_calculation_selections")
    ThermodynamicCycles = _DiscreteSetSpecification("thermodynamic_cycles_selections")
    FlowDirection = _DiscreteSetSpecification("flow_direction_selections")
    TubeLayout = _DiscreteSetSpecification("tube_layout_selections")
    FluidInTubes = _DiscreteSetSpecification("fluid_in_tubes_selections")
    CondenserType = _DiscreteSetSpecification("condenser_type_selections")
    LightKeyCompound = _DiscreteSetSpecification("light_key_compound_selections")
    HeavyKeyCompound = _DiscreteSetSpecification("heavy_key_compound_selections")
    OrificeType = _DiscreteSetSpecification("orifice_type_selections")
    OverrideTemperature = _DiscreteSetSpecification("override_temperature_selections")
    OverridePressure = _DiscreteSetSpecification("override_pressure_selections")
    ColumnType = _DiscreteSetSpecification("column_type_selections")
    CondenserSpec = _DiscreteSetSpecification("condenser_spec_selections")
    ReboilerSpec = _DiscreteSetSpecification("reboiler_spec_selections")
    ThermodynamicProcess = _DiscreteSetSpecification(
        "thermodyanamic_process_selections"
    )
    FlashSpec = _DiscreteSetSpecification("flash_spec_selections")
    MaterialStreamType = _DiscreteSetSpecification("material_stream_type_selections")
    MaterialStreamBasis = _DiscreteSetSpecification("material_stream_basis_selections")
    UnitBasis = _DiscreteSetSpecification("unit_basis_selections")
    z_ReactionSet = _DiscreteSetSpecification("reaction_set_selections")
    ReactionPhase = _DiscreteSetSpecification("reaction_phase_selections")
    ConversionExpression = _DiscreteSetSpecification("conversion_expression_selections")
    BaseCompound = _DiscreteSetSpecification("base_compound_selections")
    TemperatureControlType = _DiscreteSetSpecification(
        "temperature_control_type_selections"
    )
    TemperatureControlOptimizationMode = _DiscreteSetSpecification(
        "temperature_control_optimization_mode_selections"
    )
    TemperatureControlProfile = _DiscreteSetSpecification(
        "temperature_control_profile_selections"
    )
    CR_VolumeSpecification = _DiscreteSetSpecification(
        "volume_specification_selections"
    )
    CR_Evaporation = _DiscreteSetSpecification("evaporation_selections")
    CR_LiquidAndSolidOutflow = _DiscreteSetSpecification(
        "liquid_and_solid_outflow_selections"
    )
    CR_PressureControl = _DiscreteSetSpecification("pressure_control_selections")
    CR_SpecificPowerInput = _DiscreteSetSpecification("specific_power_input_selections")
    CR_EnergyBalance = _DiscreteSetSpecification("energy_balance_selections")
    CR_HeatTransfer = _DiscreteSetSpecification("heat_transfer_selections")
    CR_EnergyInput = _DiscreteSetSpecification("energy_input_selections")
    CR_OverallHeatTransferCoefficient = _DiscreteSetSpecification(
        "overall_heat_transfer_coefficient_selections"
    )
    CR_AmbientHeatLoss = _DiscreteSetSpecification("ambient_heat_loss_selections")
    CR_HeatLossOption = _DiscreteSetSpecification("heat_loss_selections")
    CR_PrimaryNucleationKinetics = _DiscreteSetSpecification(
        "primary_nucleation_kinetics_selections"
    )
    CR_SurfaceEnergy = _DiscreteSetSpecification("surface_energy_selections")
    CR_PrimaryNucleationDrivingForce = _DiscreteSetSpecification(
        "primary_nucleation_driving_force_selections"
    )
    CR_SecondaryNucleationActivatedKinetics = _DiscreteSetSpecification(
        "secondary_nucleation_activated_kinetics_selections"
    )
    CR_SecondaryNucleationActivatedDrivingForce = _DiscreteSetSpecification(
        "secondary_nucleation_activated_driving_force_selections"
    )
    CR_SecondaryNucleationAttritionKinetics = _DiscreteSetSpecification(
        "secondary_nucleation_attrition_kinetics_selections"
    )
    CR_SecondaryNucleationAttritionCollisionType = _DiscreteSetSpecification(
        "secondary_nucleation_attrition_collision_type_selections"
    )
    CR_SecondaryNucleationAttritionDrivingForce = _DiscreteSetSpecification(
        "secondary_nucleation_attrition_driving_force_selections"
    )
    CR_GrowthAndDissolutionKinetics = _DiscreteSetSpecification(
        "growth_and_dissolution_kinetics_selections"
    )
    CR_GrowthAndDissolutionMassTransferCorrelation = _DiscreteSetSpecification(
        "growth_and_dissolution_mass_transfer_correlation_selections"
    )
    CR_GrowthAndDissolutionSizeDependantSolubility = _DiscreteSetSpecification(
        "growth_and_dissolution_size_dependant_solubility_selections"
    )
    CR_GrowthAndDissolutionDrivingForce = _DiscreteSetSpecification(
        "growth_and_dissolution_driving_force_selections"
    )
    CR_AgglomerationKernel = _DiscreteSetSpecification(
        "agglomeration_kernel_selections"
    )
    CR_EvaporationMassTransferOptions = _DiscreteSetSpecification(
        "evaporation_mass_transfer_options_selections"
    )
    CR_EvaporationMassTransferCoefficientBasis = _DiscreteSetSpecification(
        "evaporation_mass_transfer_coefficient_basis_selections"
    )
    CR_EvaporationMassTransferCoefficientInput = _DiscreteSetSpecification(
        "evaporation_mass_transfer_coefficient_input_selections"
    )
    CR_EvaporationMassTransferArea = _DiscreteSetSpecification(
        "evaporation_mass_transfer_area_selections"
    )
    CR_SchedulerDischargeOperations = _DiscreteSetSpecification(
        "scheduler_discharge_operations_selections"
    )
    CR_SchedulerDischargeSpecs = _DiscreteSetSpecification(
        "scheduler_discharge_specifications_selections"
    )
    CR_SchedulerDischargeMassOrVolumeBasis = _DiscreteSetSpecification(
        "scheduler_discharge_basis_selections"
    )
    CR_SchedulerDischargeNumDischarges = _DiscreteSetSpecification(
        "scheduler_discharge_number_of_discharges_selections"
    )

    def __repr__(self):
        return f"{self.__class__.__name__}.{self.name}"

    def get_specification(self):
        return self.value

    @property
    def unit(self) -> str:
        return self.value.parameter_label

    @property
    def category(self) -> VariableCategoryEnum:
        """Indicates if ts a setpoint or not"""
        return VariableCategoryEnum.NONE

    def _get_stringify(self) -> str:
        """returns a string of the Variable"""
        return self.value.parameter_label


####################


class _DiscreteItemSpecification(_BaseSpecificationSingleton, ReprMixin):
    def __init__(
        self,
        unique_label: str,
        *,
        toggle_indep: Optional[List[ContVarSpecEnum]] = None,
        toggle_dep: Optional[List[ContVarSpecEnum]] = None,
    ):
        self.unique_label = unique_label
        self.toggle_indep = tuple(toggle_indep or [])
        self.toggle_dep = tuple(toggle_dep or [])


@unique
class DiscreteItemSpecEnum(BaseSpecEnum):
    """
    Enum representing different calculation policies. Each member is associated with a `Toggle`
    instance that determines which parameters or properties are independent or dependent
    when the policy is applied.
    """

    REACTION_MIXTURE = _DiscreteItemSpecification(
        "mixture_reaction",
    )
    REACTION_VAPOR = _DiscreteItemSpecification("vapor_reaction")
    REACTION_LIQUID = _DiscreteItemSpecification("liquid_reaction")
    ComponentConversions = _DiscreteItemSpecification(
        "component_conversion",
    )
    Conversions = _DiscreteItemSpecification(
        "conversion",
    )
    MaterialStreamBasis_Mol = _DiscreteItemSpecification(
        "molar_basis",
        toggle_indep=[
            # ParameterLabelEnum.MOLE_FRACTION,
            # FIXME This is binary: one or the other
            # ContVarSpecEnum.Volumetric_flow_rate,
            ContVarSpecEnum.Mass_flow_rate,
        ],
        toggle_dep=[
            # ParameterLabelEnum.MASS_FRACTION,
            # ContVarSpecEnum.Molar_flow_rate,
        ],
    )
    MaterialStreamBasis_Mass = _DiscreteItemSpecification(
        "mass_basis",
        toggle_indep=[
            # ParameterLabelEnum.MASS_FRACTION,
            # User can only specify ONE
            # ContVarSpecEnum.Volumetric_flow_rate,
            ContVarSpecEnum.Mass_flow_rate,
        ],
        toggle_dep=[
            # ParameterLabelEnum.MOLE_FRACTION,
            # ContVarSpecEnum.Molar_flow_rate,
        ],
    )
    MaterialStream_Input = _DiscreteItemSpecification(
        "input_stream",
        toggle_indep=[
            ContVarSpecEnum.Temperature,
            ContVarSpecEnum.Pressure,
            # PropertyLabelEnum.MOLAR_ENTHALPY,
            # PropertyLabelEnum.MOLAR_ENTROPY,
            # PropertyLabelEnum.VAPOR_MOLE_FRAC,
            ContVarSpecEnum.Mass_flow_rate,
            # ContVarSpecEnum.Molar_flow_rate,
            # ContVarSpecEnum.Volumetric_flow_rate,
            # ParameterLabelEnum.MASS_FRACTION,
            # ParameterLabelEnum.MOLE_FRACTION,
        ],
    )
    MaterialStream_Output = _DiscreteItemSpecification(
        "output_stream",
        toggle_dep=[
            ContVarSpecEnum.Temperature,
            ContVarSpecEnum.Pressure,
            # PropertyLabelEnum.MOLAR_ENTHALPY,
            # PropertyLabelEnum.MOLAR_ENTROPY,
            # PropertyLabelEnum.VAPOR_MOLE_FRAC,
            ContVarSpecEnum.Mass_flow_rate,
            # ContVarSpecEnum.Molar_flow_rate,
            # ContVarSpecEnum.Volumetric_flow_rate,
            # ParameterLabelEnum.MASS_FRACTION,
            # ParameterLabelEnum.MOLE_FRACTION,
        ],
    )
    MaterialStream_InProcess = _DiscreteItemSpecification(
        "in_process_stream",
        toggle_dep=[
            ContVarSpecEnum.Temperature,
            ContVarSpecEnum.Pressure,
            # PropertyLabelEnum.MOLAR_ENTHALPY,
            # PropertyLabelEnum.MOLAR_ENTROPY,
            # PropertyLabelEnum.VAPOR_MOLE_FRAC,
            ContVarSpecEnum.Mass_flow_rate,
            # ContVarSpecEnum.Molar_flow_rate,
            # ContVarSpecEnum.Volumetric_flow_rate,
            # ParameterLabelEnum.MASS_FRACTION,
            # ParameterLabelEnum.MOLE_FRACTION,
        ],
    )
    HeatAddedOrRemoved = _DiscreteItemSpecification(
        "heat_added_or_removed",
        toggle_indep=[
            ContVarSpecEnum.HeatAddedOrRemoved,
            ContVarSpecEnum.Efficiency,
            ContVarSpecEnum.PressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.OutletVaporFraction,
            ContVarSpecEnum.TemperatureChange,
        ],
    )
    TemperatureChange = _DiscreteItemSpecification(
        "temperature_change",
        toggle_indep=[
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.Efficiency,
            ContVarSpecEnum.PressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.OutletVaporFraction,
            ContVarSpecEnum.HeatAddedOrRemoved,
        ],
    )
    OutletTemperature = _DiscreteItemSpecification(
        "outlet_temperature",
        toggle_indep=[
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.Efficiency,
            ContVarSpecEnum.PressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.OutletVaporFraction,
            ContVarSpecEnum.HeatAddedOrRemoved,
        ],
    )
    OutletVaporMoleFraction = _DiscreteItemSpecification(
        "outlet_vapor_mole_fraction",
        toggle_indep=[
            ContVarSpecEnum.OutletVaporFraction,
            ContVarSpecEnum.Efficiency,
            ContVarSpecEnum.PressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.HeatAddedOrRemoved,
        ],
    )
    PressureIncrease = _DiscreteItemSpecification(
        "pressure_increase",
        toggle_indep=[
            ContVarSpecEnum.PressureIncrease,
            ContVarSpecEnum.Efficiency,
        ],
        toggle_dep=[
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.PowerRequired,
        ],
    )
    OutletPressure = _DiscreteItemSpecification(
        "outlet_pressure",
        toggle_indep=[ContVarSpecEnum.OutletPressure, ContVarSpecEnum.Efficiency],
        toggle_dep=[
            ContVarSpecEnum.PressureIncrease,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.PowerRequired,
        ],
    )
    PowerRequired = _DiscreteItemSpecification(
        "power_required",
        toggle_indep=[
            ContVarSpecEnum.Efficiency,
            ContVarSpecEnum.PowerRequired,
        ],
        toggle_dep=[
            ContVarSpecEnum.PressureIncrease,
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
        ],
    )
    PerformanceCurve = _DiscreteItemSpecification(
        "performance_curve",
        toggle_indep=[
            ContVarSpecEnum.PerformanceCurves_FlowRate,
            ContVarSpecEnum.PerformanceCurves_Head,
            ContVarSpecEnum.PerformanceCurves_Eff,
            ContVarSpecEnum.PerformanceCurves_Power,
            ContVarSpecEnum.PerformanceCurves_NPSH,
            ContVarSpecEnum.PerformanceCurves_SystemHead,
        ],
        toggle_dep=[
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.PressureIncrease,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.Efficiency,
            ContVarSpecEnum.PowerRequired,
        ],
    )
    PressureDrop_Valve = _DiscreteItemSpecification(
        "pressure_drop",
        toggle_indep=[ContVarSpecEnum.PressureDrop],
        toggle_dep=[ContVarSpecEnum.OutletPressure],
    )
    OutletPressure_Valve = _DiscreteItemSpecification(
        "outlet_pressure_v",
        toggle_indep=[ContVarSpecEnum.OutletPressure],
        toggle_dep=[ContVarSpecEnum.PressureDrop],
    )
    Inlet_Minimum = _DiscreteItemSpecification(
        "inlet_minimum",
        toggle_indep=[],
        toggle_dep=[],
    )
    Inlet_Average = _DiscreteItemSpecification(
        "inlet_average",
        toggle_indep=[],
        toggle_dep=[],
    )
    Inlet_Maximum = _DiscreteItemSpecification(
        "inlet_maximum",
        toggle_indep=[],
        toggle_dep=[],
    )
    OverrideSepTemperature = _DiscreteItemSpecification(
        "override_temperature",
        toggle_indep=[ContVarSpecEnum.OverrideSaturationTemp],
        toggle_dep=[],
    )
    NoOverrideSepTemperature = _DiscreteItemSpecification(
        "no_override_temperature",
        toggle_indep=[],
        toggle_dep=[ContVarSpecEnum.OverrideSaturationTemp],
    )
    OverrideSepPressure = _DiscreteItemSpecification(
        "override_pressure",
        toggle_indep=[
            ContVarSpecEnum.OverrideSaturationPressure,
        ],
        toggle_dep=[],
    )
    NoOverrideSepPressure = _DiscreteItemSpecification(
        "no_override_pressure",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.OverrideSaturationPressure,
        ],
    )
    Stream_Split_Ratios = _DiscreteItemSpecification(
        "stream_split_ratios",
        toggle_indep=[
            ContVarSpecEnum.Stream_Split_Ratio,
        ],
        toggle_dep=[ContVarSpecEnum.Stream_Split_FlowSpec],
    )
    Stream_Mass_Flow_Specs = _DiscreteItemSpecification(
        "stream_mass_flow",
        toggle_indep=[ContVarSpecEnum.Stream_Split_FlowSpec],
        toggle_dep=[
            ContVarSpecEnum.Stream_Split_Ratio,
        ],
    )
    Stream_Mole_Flow_Specs = _DiscreteItemSpecification(
        "stream_mol_flow",
        toggle_indep=[ContVarSpecEnum.Stream_Split_FlowSpec],
        toggle_dep=[
            ContVarSpecEnum.Stream_Split_Ratio,
        ],
    )
    OutletPressure_Compressor = _DiscreteItemSpecification(
        "outlet_pressure_c",
        toggle_indep=[
            ContVarSpecEnum.OutletPressure,
        ],
        toggle_dep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.PolytropicCoeff,
            ContVarSpecEnum.PressureIncrease,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.PowerRequired,
            ContVarSpecEnum.AdiabaticCoeff,
        ],
    )
    PressureIncrease_Compressor = _DiscreteItemSpecification(
        "pressure_increase_c",
        toggle_indep=[ContVarSpecEnum.PressureIncrease],
        toggle_dep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.AdiabaticCoeff,
            ContVarSpecEnum.PolytropicCoeff,
            ContVarSpecEnum.PowerRequired,
        ],
    )
    PowerRequired_Compressor = _DiscreteItemSpecification(
        "power_required_c",
        toggle_indep=[ContVarSpecEnum.PowerRequired],
        toggle_dep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.PressureIncrease,
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.AdiabaticCoeff,
            ContVarSpecEnum.PolytropicCoeff,
        ],
    )
    PerformanceCurves_Compressor = _DiscreteItemSpecification(
        "performance_curve_c",
        toggle_indep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.PerformanceCurves_FlowRate,
            ContVarSpecEnum.PerformanceCurves_Head,
            ContVarSpecEnum.PerformanceCurves_Power,
            ContVarSpecEnum.PerformanceCurves_Eff,
        ],
        toggle_dep=[
            ContVarSpecEnum.PressureIncrease,
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.AdiabaticCoeff,
            ContVarSpecEnum.PolytropicCoeff,
            ContVarSpecEnum.PowerRequired,
        ],
    )
    Known_Head_Compressor = _DiscreteItemSpecification(
        "head_c",
        toggle_indep=[
            ContVarSpecEnum.PolytropicHead,
        ],
        toggle_dep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.AdiabaticHead,
            ContVarSpecEnum.PressureIncrease,
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.AdiabaticCoeff,
            ContVarSpecEnum.PolytropicCoeff,
            ContVarSpecEnum.PowerRequired,
        ],
    )
    ThermodynamicProcess_Adiabatic = _DiscreteItemSpecification(
        "adiabatic_t",
        toggle_indep=[
            ContVarSpecEnum.AdiabaticEff,
        ],
        toggle_dep=[
            ContVarSpecEnum.PolytropicEff,
            ContVarSpecEnum.PolytropicHead,
            ContVarSpecEnum.AdiabaticHead,
        ],
    )
    ThermodynamicProcess_Polytropic = _DiscreteItemSpecification(
        "polytropic_t",
        toggle_indep=[ContVarSpecEnum.PolytropicEff],
        toggle_dep=[
            ContVarSpecEnum.AdiabaticEff,
            ContVarSpecEnum.AdiabaticHead,
            ContVarSpecEnum.PolytropicHead,
        ],
    )
    OutletPressure_Expander = _DiscreteItemSpecification(
        "outlet_pressure_e",
        toggle_indep=[
            ContVarSpecEnum.OutletPressure,
        ],
        toggle_dep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.PressureDecrease,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.PolytropicCoeff,
            ContVarSpecEnum.PowerGenerated,
        ],
    )
    PressureDecrease_Expander = _DiscreteItemSpecification(
        "pressure_decrease_e",
        toggle_indep=[ContVarSpecEnum.PressureDecrease],
        toggle_dep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.AdiabaticCoeff,
            ContVarSpecEnum.PolytropicCoeff,
            ContVarSpecEnum.PowerRequired,
        ],
    )
    PowerGenerated_Expander = _DiscreteItemSpecification(
        "power_generated_e",
        toggle_indep=[ContVarSpecEnum.PowerGenerated],
        toggle_dep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.PressureDecrease,
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.AdiabaticCoeff,
            ContVarSpecEnum.PolytropicCoeff,
        ],
    )
    PerformanceCurves_Expander = _DiscreteItemSpecification(
        "performance_curve_e",
        toggle_indep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.PerformanceCurves_FlowRate,
            ContVarSpecEnum.PerformanceCurves_Head,
            ContVarSpecEnum.PerformanceCurves_Power,
            ContVarSpecEnum.PerformanceCurves_Eff,
        ],
        toggle_dep=[
            ContVarSpecEnum.PressureDecrease,
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.AdiabaticCoeff,
            ContVarSpecEnum.PolytropicCoeff,
            ContVarSpecEnum.PowerGenerated,
        ],
    )
    Known_Head_Expander = _DiscreteItemSpecification(
        "head",
        toggle_indep=[
            ContVarSpecEnum.AdiabaticHead,
            ContVarSpecEnum.PolytropicHead,
        ],
        toggle_dep=[
            ContVarSpecEnum.RotationSpeed,
            ContVarSpecEnum.PressureDecrease,
            ContVarSpecEnum.OutletPressure,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureChange,
            ContVarSpecEnum.AdiabaticCoeff,
            ContVarSpecEnum.PolytropicCoeff,
            ContVarSpecEnum.PowerGenerated,
        ],
    )

    RCT_Equilibrium_Adiabatic = _DiscreteItemSpecification(
        "adiabatic_rct",
        toggle_indep=[
            ContVarSpecEnum.PressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # All the below parameters will go in equilibrium reactions
            #     ParameterEnum.CompoundStoichiometricCoeff,
            #     ParameterEnum.BaseCompound,
            #     ParameterEnum.ReactionPhase_Liquid,
            #     ParameterEnum.ReactionPhase_Vapor,
            #     ParameterEnum.ReactionPhase_Mixture,
            #     ParameterEnum.ReactionTFunction,
            #     ParameterEnum.ReactionConstantValue,
            #     ParameterEnum.ReactionTemperatureApproach,
            #     ParameterEnum.Basis_Activity,
            #     ParameterEnum.Basis_Fugacity,
            #     ParameterEnum.Basis_Molar_Concentration,
            #     ParameterEnum.Basis_Mass_Concentration,
            #     ParameterEnum.Basis_Molar_Fraction,
            #     ParameterEnum.Basis_Mass_Fraction,
            #     ParameterEnum.Basis_Partial_Pressure,
        ],
    )
    RCT_Equilibrium_Isothermic = _DiscreteItemSpecification(
        "isothermic_rct",
        toggle_indep=[
            ContVarSpecEnum.PressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.ReactionTFunction,
            # ParameterEnum.ReactionConstantValue,
            # ParameterEnum.ReactionTemperatureApproach,
            # ParameterEnum.Basis_Activity,
            # ParameterEnum.Basis_Fugacity,
            # ParameterEnum.Basis_Molar_Concentration,
            # ParameterEnum.Basis_Mass_Concentration,
            # ParameterEnum.Basis_Molar_Fraction,
            # ParameterEnum.Basis_Mass_Fraction,
            # ParameterEnum.Basis_Partial_Pressure,
        ],
    )
    RCT_Equilibrium_DefineOutletTemp = _DiscreteItemSpecification(
        "equilibrium_outlet_temperature_rct",
        toggle_indep=[
            ContVarSpecEnum.PressureDrop,
            ContVarSpecEnum.OutletTemperature,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.ReactionTFunction,
            # ParameterEnum.ReactionConstantValue,
            # ParameterEnum.ReactionTemperatureApproach,
            # ParameterEnum.Basis_Activity,
            # ParameterEnum.Basis_Fugacity,
            # ParameterEnum.Basis_Molar_Concentration,
            # ParameterEnum.Basis_Mass_Concentration,
            # ParameterEnum.Basis_Molar_Fraction,
            # ParameterEnum.Basis_Mass_Fraction,
            # ParameterEnum.Basis_Partial_Pressure,
        ],
    )
    RCT_Conversion_Adiabatic = _DiscreteItemSpecification(
        "conversion_adiabatic_rct",
        toggle_indep=[
            ContVarSpecEnum.PressureDrop_ConversionReactor,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.ComponentConversions,
        ],
    )
    RCT_Conversion_Isothermic = _DiscreteItemSpecification(
        "conversion_isothermic",
        toggle_indep=[
            ContVarSpecEnum.PressureDrop_ConversionReactor,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.ComponentConversions,
        ],
    )
    RCT_Conversion_DefineOutletTemp = _DiscreteItemSpecification(
        "conversion_define_outlet_temperature",
        toggle_indep=[
            ContVarSpecEnum.PressureDrop_ConversionReactor,
            ContVarSpecEnum.OutletTemperature,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.ComponentConversions,
        ],
    )
    RCT_CSTR_Adiabatic = _DiscreteItemSpecification(
        "cstr_adiabatic",
        toggle_indep=[
            ContVarSpecEnum.PressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.DirectOrder,
            # ParameterEnum.ReverseOrder,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.Basis_Activity,
            # ParameterEnum.Basis_Fugacity,
            # ParameterEnum.Basis_Molar_Concentration,
            # ParameterEnum.Basis_Mass_Concentration,
            # ParameterEnum.Basis_Molar_Fraction,
            # ParameterEnum.Basis_Mass_Fraction,
            # ParameterEnum.Basis_Partial_Pressure,
            # ParameterEnum.AmountUnit,
            # ParameterEnum.RateUnit,
            # ParameterEnum.A,
            # ParameterEnum.A_Prime,
            # ParameterEnum.E,
            # ParameterEnum.E_Prime,
        ],
    )
    RCT_CSTR_Isothermic = _DiscreteItemSpecification(
        "cstr_isothermic",
        toggle_indep=[
            ContVarSpecEnum.PressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.DirectOrder,
            # ParameterEnum.ReverseOrder,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.Basis_Activity,
            # ParameterEnum.Basis_Fugacity,
            # ParameterEnum.Basis_Molar_Concentration,
            # ParameterEnum.Basis_Mass_Concentration,
            # ParameterEnum.Basis_Molar_Fraction,
            # ParameterEnum.Basis_Mass_Fraction,
            # ParameterEnum.Basis_Partial_Pressure,
            # ParameterEnum.AmountUnit,
            # ParameterEnum.RateUnit,
            # ParameterEnum.A,
            # ParameterEnum.A_Prime,
            # ParameterEnum.E,
            # ParameterEnum.E_Prime,
        ],
    )
    RCT_CSTR_DefineOutletTemp = _DiscreteItemSpecification(
        "cstr_define_outlet_temp",
        toggle_indep=[
            ContVarSpecEnum.PressureDrop,
            ContVarSpecEnum.OutletTemperature,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.DirectOrder,
            # ParameterEnum.ReverseOrder,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.Basis_Activity,
            # ParameterEnum.Basis_Fugacity,
            # ParameterEnum.Basis_Molar_Concentration,
            # ParameterEnum.Basis_Mass_Concentration,
            # ParameterEnum.Basis_Molar_Fraction,
            # ParameterEnum.Basis_Mass_Fraction,
            # ParameterEnum.Basis_Partial_Pressure,
            # ParameterEnum.AmountUnit,
            # ParameterEnum.RateUnit,
            # ParameterEnum.A,
            # ParameterEnum.A_Prime,
            # ParameterEnum.E,
            # ParameterEnum.E_Prime,
        ],
    )
    RCT_Define_Basis_As_Fugacity = _DiscreteItemSpecification(  # TO VIEW
        "fct_fugacity",
        toggle_indep=[
            ContVarSpecEnum.Basis_Fugacity,
        ],
        toggle_dep=[
            ContVarSpecEnum.Basis_Activity,
            ContVarSpecEnum.Basis_Molar_Concentration,
            ContVarSpecEnum.Basis_Mass_Concentration,
            ContVarSpecEnum.Basis_Molar_Fraction,
            ContVarSpecEnum.Basis_Mass_Fraction,
            ContVarSpecEnum.Basis_Partial_Pressure,
        ],
    )
    RCT_Define_ReactionPhase_As_Liquid = _DiscreteItemSpecification(  # TO VIEW
        "reaction_phase_as_liquid",
        toggle_indep=[ContVarSpecEnum.ReactionPhase_Liquid],
        toggle_dep=[
            ContVarSpecEnum.ReactionPhase_Mixture,
            ContVarSpecEnum.ReactionPhase_Vapor,
            ContVarSpecEnum.ReactionPhase_Liquid,
        ],
    )
    RCT_PFR_Adiabatic = _DiscreteItemSpecification(
        "adiabatic",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.PressureDrop,
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.DirectOrder,
            # ParameterEnum.ReverseOrder,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.Basis_Activity,
            # ParameterEnum.Basis_Fugacity,
            # ParameterEnum.Basis_Molar_Concentration,
            # ParameterEnum.Basis_Mass_Concentration,
            # ParameterEnum.Basis_Molar_Fraction,
            # ParameterEnum.Basis_Mass_Fraction,
            # ParameterEnum.Basis_Partial_Pressure,
            # ParameterEnum.AmountUnit,
            # ParameterEnum.RateUnit,
            # ParameterEnum.A,
            # ParameterEnum.A_Prime,
            # ParameterEnum.E,
            # ParameterEnum.E_Prime,
        ],
    )
    RCT_PFR_Isothermic = _DiscreteItemSpecification(
        "isothermic",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.PressureDrop,
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.DirectOrder,
            # ParameterEnum.ReverseOrder,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.Basis_Activity,
            # ParameterEnum.Basis_Fugacity,
            # ParameterEnum.Basis_Molar_Concentration,
            # ParameterEnum.Basis_Mass_Concentration,
            # ParameterEnum.Basis_Molar_Fraction,
            # ParameterEnum.Basis_Mass_Fraction,
            # ParameterEnum.Basis_Partial_Pressure,
            # ParameterEnum.AmountUnit,
            # ParameterEnum.RateUnit,
            # ParameterEnum.A,
            # ParameterEnum.A_Prime,
            # ParameterEnum.E,
            # ParameterEnum.E_Prime,
        ],
    )
    RCT_PFR_DefineOutletTemp = _DiscreteItemSpecification(
        "define_outlet_temp",
        toggle_indep=[
            ContVarSpecEnum.OutletTemperature,
        ],
        toggle_dep=[
            ContVarSpecEnum.PressureDrop,
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.DirectOrder,
            # ParameterEnum.ReverseOrder,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.Basis_Activity,
            # ParameterEnum.Basis_Fugacity,
            # ParameterEnum.Basis_Molar_Concentration,
            # ParameterEnum.Basis_Mass_Concentration,
            # ParameterEnum.Basis_Molar_Fraction,
            # ParameterEnum.Basis_Mass_Fraction,
            # ParameterEnum.Basis_Partial_Pressure,
            # ParameterEnum.AmountUnit,
            # ParameterEnum.RateUnit,
            # ParameterEnum.A,
            # ParameterEnum.A_Prime,
            # ParameterEnum.E,
            # ParameterEnum.E_Prime,
        ],
    )
    RCT_PFR_DefineNonAdiabaticNonIsothermal = _DiscreteItemSpecification(
        "define_non_adiabatic_non_isothermal",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.PressureDrop,
            ContVarSpecEnum.OutletTemperature,
            ContVarSpecEnum.TemperatureDifference,
            ContVarSpecEnum.HeatLoad,
            # ParameterEnum.CompoundStoichiometricCoeff,
            # ParameterEnum.BaseCompound,
            # ParameterEnum.DirectOrder,
            # ParameterEnum.ReverseOrder,
            # ParameterEnum.ReactionPhase_Liquid,
            # ParameterEnum.ReactionPhase_Vapor,
            # ParameterEnum.ReactionPhase_Mixture,
            # ParameterEnum.Basis_Activity,
            # ParameterEnum.Basis_Fugacity,
            # ParameterEnum.Basis_Molar_Concentration,
            # ParameterEnum.Basis_Mass_Concentration,
            # ParameterEnum.Basis_Molar_Fraction,
            # ParameterEnum.Basis_Mass_Fraction,
            # ParameterEnum.Basis_Partial_Pressure,
            # ParameterEnum.AmountUnit,
            # ParameterEnum.RateUnit,
            # ParameterEnum.A,
            # ParameterEnum.A_Prime,
            # ParameterEnum.E,
            # ParameterEnum.E_Prime,
        ],
    )
    CalculateHotFluidOutletTemperature = _DiscreteItemSpecification(
        "hot_fluid_outlet_temperature",
        toggle_indep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatLoss,
        ],
        toggle_dep=[
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # Tube Vars
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.RoughnessTube,
        ],
    )
    CalculateColdFluidOutletTemperature = _DiscreteItemSpecification(
        "cold_fluid_outlet_temperature",
        toggle_indep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatLoss,
        ],
        toggle_dep=[
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # Tube Vars
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.RoughnessTube,
        ],
    )
    CalculateOutletTemperatures = _DiscreteItemSpecification(
        "outlet_temperatures",
        toggle_indep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.HeatLoss,
        ],
        toggle_dep=[
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # Tube Vars
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.RoughnessTube,
        ],
    )
    CalculateOutletTemperatures_UA = _DiscreteItemSpecification(
        "outlet_temperatures_UA",
        toggle_indep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatLoss,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # Tube Vars
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.RoughnessTube,
        ],
    )
    CalculateArea = _DiscreteItemSpecification(
        "calculate_area",
        toggle_indep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.HeatLoss,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.RoughnessTube,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # Tube Vars
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeSpacing,
        ],
    )
    ShellAndTubesExchangerRating = _DiscreteItemSpecification(
        "shell_and_tubes_exchanger_rating",
        toggle_indep=[
            ContVarSpecEnum.HeatLoss,
            ContVarSpecEnum.RoughnessTube,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.ShellFouling,
        ],
        toggle_dep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # Tube Layout
            # ParamEnum.BaffleType,
            # ParamEnum.BaffleOrientation,
        ],
    )
    TubeLayout_Triangle = _DiscreteItemSpecification(
        "tube_layout_triangle",
    )
    TubeLayout_RotatedTriangle = _DiscreteItemSpecification(
        "tube_layout_rotated_triangle",
    )
    TubeLayout_Square = _DiscreteItemSpecification(
        "tube_layout_square",
    )
    TubeLayout_RotatedSquare = _DiscreteItemSpecification(
        "tube_layout_rotated_square",
    )
    FluidInTubes_Cold = _DiscreteItemSpecification("cold")
    FluidInTubes_Hot = _DiscreteItemSpecification("hot")
    ShellAndTubesExchangerFoulingFactor = _DiscreteItemSpecification(
        "shell_and_tubes_exchanger_fouling_factor",
        toggle_indep=[
            ContVarSpecEnum.HeatLoss,
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.RoughnessTube,
        ],
        toggle_dep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # ParamEnum.BaffleType,
            # ParamEnum.BaffleOrientation,
            ContVarSpecEnum.HotFluidPressureDrop,
        ],
    )
    PinchPoint = _DiscreteItemSpecification(
        "pinch_point",
        toggle_indep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatLoss,
        ],
        toggle_dep=[
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # Tube Vars
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.RoughnessTube,
        ],
    )
    SpecifyHeatTransferEfficiency = _DiscreteItemSpecification(
        "specify_heat_transfer_efficiency",
        toggle_indep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.HeatLoss,
            ContVarSpecEnum.HeatTransferEfficiency,
        ],
        toggle_dep=[
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # Tube Vars
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.RoughnessTube,
        ],
    )
    SpecifyOutletMolarVaporFraction_Stream1 = _DiscreteItemSpecification(
        "specify_outlet_molar_vapor_fraction_stream_1",
        toggle_indep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatLoss,
            ContVarSpecEnum.OutletVaporFractionFluid1,
        ],
        toggle_dep=[
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid2,
            # Tube Vars
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.RoughnessTube,
        ],
    )
    SpecifyOutletMolarVaporFraction_Stream2 = _DiscreteItemSpecification(
        "specify_outlet_molar_vapor_fraction_stream_2",
        toggle_indep=[
            ContVarSpecEnum.ColdFluidPressureDrop,
            ContVarSpecEnum.HotFluidPressureDrop,
            ContVarSpecEnum.HeatExchangeArea,
            ContVarSpecEnum.HeatLoss,
            ContVarSpecEnum.OutletVaporFractionFluid2,
        ],
        toggle_dep=[
            ContVarSpecEnum.ColdFluidOutletTemperature,
            ContVarSpecEnum.HotFluidOutletTemperature,
            ContVarSpecEnum.GlobalHeatTransferCoefficient,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MinimumTemperatureDifference,
            ContVarSpecEnum.HeatTransferEfficiency,
            ContVarSpecEnum.OutletVaporFractionFluid1,
            # Tube Vars
            ContVarSpecEnum.ShellInSeries,
            ContVarSpecEnum.ShellPasses,
            ContVarSpecEnum.InternalDiameterOfShell,
            ContVarSpecEnum.ShellFouling,
            ContVarSpecEnum.BaffleSpacing,
            ContVarSpecEnum.BaffleCut,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.PassesPerShell,
            ContVarSpecEnum.TubesPerShell,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.RoughnessTube,
        ],
    )
    FlowDirection_CounterCurrent = _DiscreteItemSpecification(
        "counter_current", toggle_indep=[], toggle_dep=[]
    )
    FlowDirection_CoCurrent = _DiscreteItemSpecification(
        "co_current", toggle_indep=[], toggle_dep=[]
    )
    CalcMode_SC_Total = _DiscreteItemSpecification(
        "total",
        toggle_indep=[
            ContVarSpecEnum.LightKeyMolFraction,
            ContVarSpecEnum.HeavyKeyMolFraction,
            ContVarSpecEnum.RefluxRatio,
            ContVarSpecEnum.CondenserPressure,
            ContVarSpecEnum.ReboilerPressure,
        ],
        toggle_dep=[
            ContVarSpecEnum.MinimumRefluxRatio,
            ContVarSpecEnum.MinimumNumberOfTrays,
            ContVarSpecEnum.ActualNumberOfTrays,
            ContVarSpecEnum.OptimalFeedStage,
            ContVarSpecEnum.StrippingLiquid,
            ContVarSpecEnum.RectifyingLiquid,
            ContVarSpecEnum.StrippingLiquid,
            ContVarSpecEnum.StrippingVapor,
            ContVarSpecEnum.ReboilerDuty,
            ContVarSpecEnum.CondenserDuty,
            ContVarSpecEnum.RectifyingVapor,
        ],
    )
    Light_Key_Compound = _DiscreteItemSpecification(
        "light_key_compound",
    )

    Heavy_Key_Compound = _DiscreteItemSpecification(
        "heavy_key_compound",
    )

    CalcMode_SC_Partial = _DiscreteItemSpecification(
        "partial",
        toggle_indep=[
            ContVarSpecEnum.LightKeyMolFraction,
            ContVarSpecEnum.HeavyKeyMolFraction,
            ContVarSpecEnum.RefluxRatio,
            ContVarSpecEnum.CondenserPressure,
            ContVarSpecEnum.ReboilerPressure,
        ],
        toggle_dep=[
            ContVarSpecEnum.MinimumRefluxRatio,
            ContVarSpecEnum.MinimumNumberOfTrays,
            ContVarSpecEnum.ActualNumberOfTrays,
            ContVarSpecEnum.OptimalFeedStage,
            ContVarSpecEnum.StrippingLiquid,
            ContVarSpecEnum.RectifyingLiquid,
            ContVarSpecEnum.StrippingLiquid,
            ContVarSpecEnum.StrippingVapor,
            ContVarSpecEnum.ReboilerDuty,
            ContVarSpecEnum.CondenserDuty,
        ],
    )
    Pressure_Tappings_Corner = _DiscreteItemSpecification(
        "corner",
        toggle_indep=[
            ContVarSpecEnum.OrificeDiameter,
            ContVarSpecEnum.InternalPipeDiameter,
            ContVarSpecEnum.CorrectionFactor,
        ],
        toggle_dep=[
            ContVarSpecEnum.OrificePressureDrop,
            ContVarSpecEnum.OverallPressureDrop,
            ContVarSpecEnum.Orifice_Temperature_Change,
            ContVarSpecEnum.OrificeBeta,
        ],
    )
    Pressure_Tappings_Flange = _DiscreteItemSpecification(
        "flange",
        toggle_indep=[
            ContVarSpecEnum.OrificeDiameter,
            ContVarSpecEnum.InternalPipeDiameter,
            ContVarSpecEnum.CorrectionFactor,
        ],
        toggle_dep=[
            ContVarSpecEnum.OrificePressureDrop,
            ContVarSpecEnum.OverallPressureDrop,
            ContVarSpecEnum.Orifice_Temperature_Change,
            ContVarSpecEnum.OrificeBeta,
        ],
    )
    Pressure_Tappings_Radius = _DiscreteItemSpecification(
        "radius",
        toggle_indep=[
            ContVarSpecEnum.OrificeDiameter,
            ContVarSpecEnum.InternalPipeDiameter,
            ContVarSpecEnum.CorrectionFactor,
        ],
        toggle_dep=[
            ContVarSpecEnum.OrificePressureDrop,
            ContVarSpecEnum.OverallPressureDrop,
            ContVarSpecEnum.Orifice_Temperature_Change,
            ContVarSpecEnum.OrificeBeta,
        ],
    )
    AbsorptionOperatingMode_Absorber = _DiscreteItemSpecification(
        "absorber",
        toggle_indep=[
            ContVarSpecEnum.CondenserPressureDrop,
            ContVarSpecEnum.ColumnPressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.NumberofStages,
            ContVarSpecEnum.EstimatedDiameter,
            ContVarSpecEnum.EstimatedHeight,
            ContVarSpecEnum.FeedStage_Gas,
            ContVarSpecEnum.FeedStage_Liquid,
            ContVarSpecEnum.BottomProduct_Stage,
            ContVarSpecEnum.OverheadVapor_Stage,
        ],
    )
    AbsorptionOperatingMode_LiqLiqExtractor = _DiscreteItemSpecification(
        "liq_liq_extractor",
        toggle_indep=[
            ContVarSpecEnum.CondenserPressureDrop,
            ContVarSpecEnum.ColumnPressureDrop,
        ],
        toggle_dep=[
            ContVarSpecEnum.NumberofStages,
            ContVarSpecEnum.EstimatedDiameter,
            ContVarSpecEnum.EstimatedHeight,
            ContVarSpecEnum.FeedStage_Gas,
            ContVarSpecEnum.FeedStage_Liquid,
            ContVarSpecEnum.BottomProduct_Stage,
            ContVarSpecEnum.OverheadVapor_Stage,
        ],
    )
    SpecifyOutletTemp = _DiscreteItemSpecification(
        "specify_outlet_temp",
        toggle_indep=[
            ContVarSpecEnum.ElectricalPowerConversionFactor,
            ContVarSpecEnum.FluidPressureDrop,
            ContVarSpecEnum.OutletFluidTemperature,
            ContVarSpecEnum.InletAirTemperature,
            ContVarSpecEnum.InletAirPressure,
            ContVarSpecEnum.ReferenceRotationOfFan,
            ContVarSpecEnum.ReferenceAirFlow,
            ContVarSpecEnum.ActualRotation,
        ],
        toggle_dep=[
            ContVarSpecEnum.OutletAirTemperature,
            ContVarSpecEnum.OverallUA,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MaximumHeatExchange,
            ContVarSpecEnum.ExchangerEfficiency,
            ContVarSpecEnum.ActualAirFlow,
            ContVarSpecEnum.PowerRequired,
            # Tube Var Specs
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.RoughnessTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.NumberOfPasses,
            ContVarSpecEnum.NumberOfTubes,
            ContVarSpecEnum.TubeSpacing,
        ],
    )
    SpecifyTubeGeometry = _DiscreteItemSpecification(
        "specify_tube_geometry",
        toggle_indep=[
            ContVarSpecEnum.InletAirPressure,
            ContVarSpecEnum.InletAirTemperature,
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.RoughnessTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.NumberOfPasses,
            ContVarSpecEnum.NumberOfTubes,
            ContVarSpecEnum.TubeSpacing,
            ContVarSpecEnum.ReferenceRotationOfFan,
            ContVarSpecEnum.ReferenceAirFlow,
            ContVarSpecEnum.ActualRotation,
            ContVarSpecEnum.ElectricalPowerConversionFactor,
        ],
        toggle_dep=[
            ContVarSpecEnum.FluidPressureDrop,
            ContVarSpecEnum.OutletFluidTemperature,
            ContVarSpecEnum.OutletAirTemperature,
            ContVarSpecEnum.OverallUA,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MaximumHeatExchange,
            ContVarSpecEnum.ExchangerEfficiency,
            ContVarSpecEnum.PowerRequired,
            ContVarSpecEnum.ActualAirFlow,
        ],
    )
    SpecifyOverallUA = _DiscreteItemSpecification(
        "specify_overall_UA",
        toggle_indep=[
            ContVarSpecEnum.FluidPressureDrop,
            ContVarSpecEnum.InletAirPressure,
            ContVarSpecEnum.InletAirTemperature,
            ContVarSpecEnum.OverallUA,
            ContVarSpecEnum.ReferenceRotationOfFan,
            ContVarSpecEnum.ReferenceAirFlow,
            ContVarSpecEnum.ActualRotation,
            ContVarSpecEnum.ElectricalPowerConversionFactor,
        ],
        toggle_dep=[
            ContVarSpecEnum.OutletFluidTemperature,
            ContVarSpecEnum.OutletAirTemperature,
            ContVarSpecEnum.HeatExchange,
            ContVarSpecEnum.MaximumHeatExchange,
            ContVarSpecEnum.ExchangerEfficiency,
            ContVarSpecEnum.ActualAirFlow,
            ContVarSpecEnum.PowerRequired,
            # Tube Var Specs
            ContVarSpecEnum.InternalDiameterOfTube,
            ContVarSpecEnum.ExternalDiameterOfTube,
            ContVarSpecEnum.TubeLength,
            ContVarSpecEnum.TubeFouling,
            ContVarSpecEnum.RoughnessTube,
            ContVarSpecEnum.ThermalConductivityOfTube,
            ContVarSpecEnum.NumberOfPasses,
            ContVarSpecEnum.NumberOfTubes,
            ContVarSpecEnum.TubeSpacing,
        ],
    )

    DC_CondenserType_Total = _DiscreteItemSpecification(
        "dc_condenser_total",
        toggle_indep=[ContVarSpecEnum.TotalCondenserSubcoolingTemperatureDrop],
        toggle_dep=[ContVarSpecEnum.VaporProductFlowRate],
    )
    DC_CondenserType_Partial = _DiscreteItemSpecification(
        "dc_condenser_partial",
        toggle_indep=[ContVarSpecEnum.VaporProductFlowRate],
        toggle_dep=[ContVarSpecEnum.TotalCondenserSubcoolingTemperatureDrop],
    )
    DC_CondenserType_FullReflux = _DiscreteItemSpecification(
        "dc_condenser_full_reflux",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.TotalCondenserSubcoolingTemperatureDrop,
            ContVarSpecEnum.VaporProductFlowRate,
        ],
    )
    DC_Condenser_Spec_HeatLoad = _DiscreteItemSpecification(
        "dc_condenser_heatload",
        toggle_indep=[
            ContVarSpecEnum.HeatLoad_Condenser,
        ],
        toggle_dep=[
            ContVarSpecEnum.ProductMolarFlow_Condenser,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Condenser,
            ContVarSpecEnum.ProductMassFlow_Condenser,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Condenser,
            ContVarSpecEnum.CompoundFractionInProductStream_Condenser,
            ContVarSpecEnum.CompoundRecovery_Condenser,
            ContVarSpecEnum.RefluxRatio,
            ContVarSpecEnum.Temperature_Condenser,
        ],
    )
    DC_Condenser_Spec_ProductMolarFlowRate = _DiscreteItemSpecification(
        "dc_condenser_product_molar_flowrate",
        toggle_indep=[
            ContVarSpecEnum.ProductMolarFlow_Condenser,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Condenser,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Condenser,
            ContVarSpecEnum.ProductMassFlow_Condenser,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Condenser,
            ContVarSpecEnum.CompoundFractionInProductStream_Condenser,
            ContVarSpecEnum.CompoundRecovery_Condenser,
            ContVarSpecEnum.RefluxRatio,
            ContVarSpecEnum.Temperature_Condenser,
        ],
    )
    DC_Condenser_Spec_CompoundMolarFlowInProductStream = _DiscreteItemSpecification(
        "dc_condenser_compound_molar_flow_in_product_stream",
        toggle_indep=[
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Condenser,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Condenser,
            ContVarSpecEnum.ProductMolarFlow_Condenser,
            ContVarSpecEnum.ProductMassFlow_Condenser,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Condenser,
            ContVarSpecEnum.CompoundFractionInProductStream_Condenser,
            ContVarSpecEnum.CompoundRecovery_Condenser,
            ContVarSpecEnum.RefluxRatio,
            ContVarSpecEnum.Temperature_Condenser,
        ],
    )
    DC_Condenser_Spec_ProductMassFlow = _DiscreteItemSpecification(
        "dc_condenser_product_mass_flow",
        toggle_indep=[
            ContVarSpecEnum.ProductMassFlow_Condenser,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Condenser,
            ContVarSpecEnum.ProductMolarFlow_Condenser,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Condenser,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Condenser,
            ContVarSpecEnum.CompoundFractionInProductStream_Condenser,
            ContVarSpecEnum.CompoundRecovery_Condenser,
            ContVarSpecEnum.RefluxRatio,
            ContVarSpecEnum.Temperature_Condenser,
        ],
    )
    DC_Condenser_Spec_CompoundMassFlowInProductStream = _DiscreteItemSpecification(
        "dc_condenser_compound_mass_flow_in_product_stream",
        toggle_indep=[
            ContVarSpecEnum.CompoundMassFlowInProductStream_Condenser,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Condenser,
            ContVarSpecEnum.ProductMolarFlow_Condenser,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Condenser,
            ContVarSpecEnum.CompoundFractionInProductStream_Condenser,
            ContVarSpecEnum.ProductMassFlow_Condenser,
            ContVarSpecEnum.CompoundRecovery_Condenser,
            ContVarSpecEnum.RefluxRatio,
            ContVarSpecEnum.Temperature_Condenser,
        ],
    )
    DC_Condenser_Spec_CompoundFractionInProductStream = _DiscreteItemSpecification(
        "dc_condenser_compound_fraction_in_product_stream",
        toggle_indep=[
            ContVarSpecEnum.CompoundFractionInProductStream_Condenser,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Condenser,
            ContVarSpecEnum.ProductMolarFlow_Condenser,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Condenser,
            ContVarSpecEnum.ProductMassFlow_Condenser,
            ContVarSpecEnum.CompoundRecovery_Condenser,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Condenser,
            ContVarSpecEnum.RefluxRatio,
            ContVarSpecEnum.Temperature_Condenser,
        ],
    )
    DC_Condenser_Spec_CompoundRecovery = _DiscreteItemSpecification(
        "dc_condenser_compound_recovery",
        toggle_indep=[
            ContVarSpecEnum.CompoundRecovery_Condenser,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Condenser,
            ContVarSpecEnum.ProductMolarFlow_Condenser,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Condenser,
            ContVarSpecEnum.ProductMassFlow_Condenser,
            ContVarSpecEnum.CompoundFractionInProductStream_Condenser,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Condenser,
            ContVarSpecEnum.RefluxRatio,
            ContVarSpecEnum.Temperature_Condenser,
        ],
    )
    DC_Condenser_Spec_RefluxRatio = _DiscreteItemSpecification(
        "dc_condenser_spec_reflux_ratio",
        toggle_indep=[
            ContVarSpecEnum.RefluxRatio,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Condenser,
            ContVarSpecEnum.ProductMolarFlow_Condenser,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Condenser,
            ContVarSpecEnum.ProductMassFlow_Condenser,
            ContVarSpecEnum.CompoundFractionInProductStream_Condenser,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Condenser,
            ContVarSpecEnum.CompoundRecovery_Condenser,
            ContVarSpecEnum.Temperature_Condenser,
        ],
    )
    DC_Condenser_Spec_Temperature = _DiscreteItemSpecification(
        "dc_condenser_spec_temperature",
        toggle_indep=[
            ContVarSpecEnum.Temperature_Condenser,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Condenser,
            ContVarSpecEnum.ProductMolarFlow_Condenser,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Condenser,
            ContVarSpecEnum.ProductMassFlow_Condenser,
            ContVarSpecEnum.CompoundFractionInProductStream_Condenser,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Condenser,
            ContVarSpecEnum.CompoundRecovery_Condenser,
            ContVarSpecEnum.RefluxRatio,
        ],
    )
    DC_Reboiler_Spec_HeatLoad = _DiscreteItemSpecification(
        "dc_reboiler_heatload",
        toggle_indep=[
            ContVarSpecEnum.HeatLoad_Reboiler,
        ],
        toggle_dep=[
            ContVarSpecEnum.ProductMolarFlow_Reboiler,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Reboiler,
            ContVarSpecEnum.ProductMassFlow_Reboiler,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundFractionInProductStream_Reboiler,
            ContVarSpecEnum.CompoundRecovery_Reboiler,
            ContVarSpecEnum.Boilup_Ratio,
            ContVarSpecEnum.Temperature_Reboiler,
        ],
    )
    DC_Reboiler_Spec_ProductMolarFlow = _DiscreteItemSpecification(
        "dc_reboiler_product_molar_flow",
        toggle_indep=[
            ContVarSpecEnum.ProductMolarFlow_Reboiler,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Reboiler,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Reboiler,
            ContVarSpecEnum.ProductMassFlow_Reboiler,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundFractionInProductStream_Reboiler,
            ContVarSpecEnum.CompoundRecovery_Reboiler,
            ContVarSpecEnum.Boilup_Ratio,
            ContVarSpecEnum.Temperature_Reboiler,
        ],
    )
    DC_Reboiler_Spec_CompoundMolarFlowInProductStream = _DiscreteItemSpecification(
        "dc_reboiler_compound_molar_flow_in_product_stream",
        toggle_indep=[
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Reboiler,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Reboiler,
            ContVarSpecEnum.ProductMolarFlow_Reboiler,
            ContVarSpecEnum.ProductMassFlow_Reboiler,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundFractionInProductStream_Reboiler,
            ContVarSpecEnum.CompoundRecovery_Reboiler,
            ContVarSpecEnum.Boilup_Ratio,
            ContVarSpecEnum.Temperature_Reboiler,
        ],
    )
    DC_Reboiler_Spec_ProductMassFlow = _DiscreteItemSpecification(
        "dc_reboiler_product_mass_flow",
        toggle_indep=[
            ContVarSpecEnum.ProductMassFlow_Reboiler,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Reboiler,
            ContVarSpecEnum.ProductMolarFlow_Reboiler,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundFractionInProductStream_Reboiler,
            ContVarSpecEnum.CompoundRecovery_Reboiler,
            ContVarSpecEnum.Boilup_Ratio,
            ContVarSpecEnum.Temperature_Reboiler,
        ],
    )
    DC_Reboiler_Spec_CompoundMassFlowInProductStream = _DiscreteItemSpecification(
        "dc_reboiler_compound_mass_flow_in_product_stream",
        toggle_indep=[
            ContVarSpecEnum.CompoundMassFlowInProductStream_Reboiler,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Reboiler,
            ContVarSpecEnum.ProductMolarFlow_Reboiler,
            ContVarSpecEnum.ProductMassFlow_Reboiler,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundFractionInProductStream_Reboiler,
            ContVarSpecEnum.CompoundRecovery_Reboiler,
            ContVarSpecEnum.Boilup_Ratio,
            ContVarSpecEnum.Temperature_Reboiler,
        ],
    )
    DC_Reboiler_Spec_CompoundMassFractionInProductStream = _DiscreteItemSpecification(
        "dc_reboiler_compound_mass_fraction_in_product_stream",
        toggle_indep=[
            ContVarSpecEnum.CompoundFractionInProductStream_Reboiler,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Reboiler,
            ContVarSpecEnum.ProductMolarFlow_Reboiler,
            ContVarSpecEnum.ProductMassFlow_Reboiler,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundRecovery_Reboiler,
            ContVarSpecEnum.Boilup_Ratio,
            ContVarSpecEnum.Temperature_Reboiler,
        ],
    )
    DC_Reboiler_Spec_CompoundRecovery = _DiscreteItemSpecification(
        "dc_reboiler_compound_recovery",
        toggle_indep=[
            ContVarSpecEnum.CompoundRecovery_Reboiler,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Reboiler,
            ContVarSpecEnum.ProductMolarFlow_Reboiler,
            ContVarSpecEnum.ProductMassFlow_Reboiler,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundFractionInProductStream_Reboiler,
            ContVarSpecEnum.Boilup_Ratio,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Reboiler,
            ContVarSpecEnum.Temperature_Reboiler,
        ],
    )
    DC_Reboiler_Spec_BoilUpRatio = _DiscreteItemSpecification(
        "dc_reboiler_boil_up_ratio",
        toggle_indep=[
            ContVarSpecEnum.Boilup_Ratio,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Reboiler,
            ContVarSpecEnum.ProductMolarFlow_Reboiler,
            ContVarSpecEnum.ProductMassFlow_Reboiler,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundRecovery_Reboiler,
            ContVarSpecEnum.CompoundFractionInProductStream_Reboiler,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Reboiler,
            ContVarSpecEnum.Temperature_Reboiler,
        ],
    )
    DC_Reboiler_Spec_Temperature = _DiscreteItemSpecification(
        "dc_reboiler_spec_temperature",
        toggle_indep=[
            ContVarSpecEnum.Temperature_Reboiler,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatLoad_Reboiler,
            ContVarSpecEnum.CompoundRecovery_Reboiler,
            ContVarSpecEnum.ProductMolarFlow_Reboiler,
            ContVarSpecEnum.ProductMassFlow_Reboiler,
            ContVarSpecEnum.CompoundMassFlowInProductStream_Reboiler,
            ContVarSpecEnum.CompoundFractionInProductStream_Reboiler,
            ContVarSpecEnum.Boilup_Ratio,
            ContVarSpecEnum.CompoundMolarFlowInProductStream_Reboiler,
        ],
    )

    ####################
    # Material Streams
    ####################

    TemperatureAndPressure = _DiscreteItemSpecification(
        "temperature_and_pressure",
        toggle_indep=[
            ContVarSpecEnum.Temperature,
            ContVarSpecEnum.Pressure,
            ContVarSpecEnum.Mass_flow_rate,
            # ContVarSpecEnum.Molar_flow_rate,
            # ParameterLabelEnum.MASS_FRACTION,
            # ParameterLabelEnum.MOLE_FRACTION,
            # ContVarSpecEnum.Volumetric_flow_rate,
        ],
        toggle_dep=[
            # NOTE - removed because these could not be accessed in Dwsim
            # PropertyLabelEnum.MOLAR_ENTHALPY,
            # PropertyLabelEnum.MOLAR_ENTROPY,
            # PropertyLabelEnum.VAPOR_MOLE_FRAC,
        ],
    )
    PressureAndEnthalphy = _DiscreteItemSpecification(
        "pressure_and_enthalphy",
        toggle_indep=[
            ContVarSpecEnum.Pressure,
            # PropertyLabelEnum.MOLAR_ENTHALPY,
            ContVarSpecEnum.Mass_flow_rate,
            # ContVarSpecEnum.Molar_flow_rate,
            # ContVarSpecEnum.Volumetric_flow_rate,
            # ParameterLabelEnum.MASS_FRACTION,
            # ParameterLabelEnum.MOLE_FRACTION,
        ],
        toggle_dep=[
            ContVarSpecEnum.Temperature,
            # PropertyLabelEnum.MOLAR_ENTROPY,
            # PropertyLabelEnum.VAPOR_MOLE_FRAC,
        ],
    )
    PressureAndEntropy = _DiscreteItemSpecification(
        "pressure_and_entropy",
        toggle_indep=[
            ContVarSpecEnum.Pressure,
            # PropertyLabelEnum.MOLAR_ENTROPY,
            ContVarSpecEnum.Mass_flow_rate,
            # ContVarSpecEnum.Molar_flow_rate,
            # ContVarSpecEnum.Volumetric_flow_rate,
            # ParameterLabelEnum.MASS_FRACTION,
            # ParameterLabelEnum.MOLE_FRACTION,
        ],
        toggle_dep=[
            ContVarSpecEnum.Temperature,
            # PropertyLabelEnum.MOLAR_ENTHALPY,
            # PropertyLabelEnum.VAPOR_MOLE_FRAC,
        ],
    )
    PressureaAndVaporFraction = _DiscreteItemSpecification(
        "pressure_and_vapor_fraction",
        toggle_indep=[
            ContVarSpecEnum.Pressure,
            # PropertyLabelEnum.VAPOR_MOLE_FRAC,
            ContVarSpecEnum.Mass_flow_rate,
            # ContVarSpecEnum.Molar_flow_rate,
            # ContVarSpecEnum.Volumetric_flow_rate,
            # ParameterLabelEnum.MASS_FRACTION,
            # ParameterLabelEnum.MOLE_FRACTION,
        ],
        toggle_dep=[
            ContVarSpecEnum.Temperature,
            # PropertyLabelEnum.MOLAR_ENTHALPY,
            # PropertyLabelEnum.MOLAR_ENTROPY,
        ],
    )
    TemperatureAndVaporFraction = _DiscreteItemSpecification(
        "temperature_and_vapor_fraction",
        toggle_indep=[
            ContVarSpecEnum.Temperature,
            # PropertyLabelEnum.VAPOR_MOLE_FRAC,
            ContVarSpecEnum.Mass_flow_rate,
            # ContVarSpecEnum.Molar_flow_rate,
            # ContVarSpecEnum.Volumetric_flow_rate,
            # ParameterLabelEnum.MASS_FRACTION,
            # ParameterLabelEnum.MOLE_FRACTION,
        ],
        toggle_dep=[
            ContVarSpecEnum.Pressure,
            # PropertyLabelEnum.MOLAR_ENTHALPY,
            # PropertyLabelEnum.MOLAR_ENTROPY,
        ],
    )

    ####################
    # Temperature Control Block
    ####################

    TC_PerfectControl = _DiscreteItemSpecification(
        "perfect_control",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.PIControllerMinimumOutput_TC,
            ContVarSpecEnum.PIControllerMaximumOutput_TC,
            ContVarSpecEnum.PIControllerBias_TC,
            ContVarSpecEnum.PIControllerGain_TC,
            ContVarSpecEnum.PIControllerResetTime_TC,
        ],
    )
    TC_PIControl = _DiscreteItemSpecification(
        "pi_control",
        toggle_indep=[
            ContVarSpecEnum.PIControllerMinimumOutput_TC,
            ContVarSpecEnum.PIControllerMaximumOutput_TC,
            ContVarSpecEnum.PIControllerBias_TC,
            ContVarSpecEnum.PIControllerGain_TC,
            ContVarSpecEnum.PIControllerResetTime_TC,
        ],
        toggle_dep=[],
    )
    TC_OptimizationModeOff = _DiscreteItemSpecification(
        "off",
        toggle_dep=[
            ContVarSpecEnum.TempCSetPointInitial_TC,
            ContVarSpecEnum.TempCSetPointRamp_TC,
        ],
    )
    TC_OptimizationModeOn = _DiscreteItemSpecification(
        "on",
        toggle_indep=[
            ContVarSpecEnum.TempCSetPointInitial_TC,
            ContVarSpecEnum.TempCSetPointRamp_TC,
        ],
    )
    TC_ControlledCooling = _DiscreteItemSpecification(
        "controlled_cooling",
        toggle_indep=[
            ContVarSpecEnum.InitialTemperatureC_TC,
            ContVarSpecEnum.FinalTemperatureC_TC,
            ContVarSpecEnum.FinalTimeMinutes_TC,
        ],
        toggle_dep=[
            ContVarSpecEnum.TemperatureSetPointC_TC,
            # For DiscreteItemSpecEnum.TC_PiecewiseLinear
            ContVarSpecEnum.ControlProfileDuration01_TC,
            ContVarSpecEnum.ControlProfileDuration02_TC,
            ContVarSpecEnum.ControlProfileDuration03_TC,
            ContVarSpecEnum.ControlProfileDuration04_TC,
            ContVarSpecEnum.ControlProfileDuration05_TC,
            ContVarSpecEnum.ControlProfileDuration06_TC,
            ContVarSpecEnum.ControlProfileDuration07_TC,
            ContVarSpecEnum.ControlProfileDuration08_TC,
            ContVarSpecEnum.ControlProfileDuration09_TC,
            ContVarSpecEnum.ControlProfileDuration10_TC,
            ContVarSpecEnum.ControlProfileDuration11_TC,
            ContVarSpecEnum.ControlProfileDuration12_TC,
            ContVarSpecEnum.ControlProfileDuration13_TC,
            ContVarSpecEnum.ControlProfileDuration14_TC,
            ContVarSpecEnum.ControlProfileDuration15_TC,
            ContVarSpecEnum.ControlProfileDuration16_TC,
            ContVarSpecEnum.ControlProfileDuration17_TC,
            ContVarSpecEnum.ControlProfileDuration18_TC,
            ContVarSpecEnum.ControlProfileDuration19_TC,
            ContVarSpecEnum.ControlProfileDuration20_TC,
            ContVarSpecEnum.ControlProfileDuration21_TC,
            ContVarSpecEnum.ControlProfileDuration22_TC,
            ContVarSpecEnum.ControlProfileDuration23_TC,
            ContVarSpecEnum.ControlProfileDuration24_TC,
            ContVarSpecEnum.ControlProfileDuration25_TC,
            ContVarSpecEnum.ControlProfileTemperature01_TC,
            ContVarSpecEnum.ControlProfileTemperature02_TC,
            ContVarSpecEnum.ControlProfileTemperature03_TC,
            ContVarSpecEnum.ControlProfileTemperature04_TC,
            ContVarSpecEnum.ControlProfileTemperature05_TC,
            ContVarSpecEnum.ControlProfileTemperature06_TC,
            ContVarSpecEnum.ControlProfileTemperature07_TC,
            ContVarSpecEnum.ControlProfileTemperature08_TC,
            ContVarSpecEnum.ControlProfileTemperature09_TC,
            ContVarSpecEnum.ControlProfileTemperature10_TC,
            ContVarSpecEnum.ControlProfileTemperature11_TC,
            ContVarSpecEnum.ControlProfileTemperature12_TC,
            ContVarSpecEnum.ControlProfileTemperature13_TC,
            ContVarSpecEnum.ControlProfileTemperature14_TC,
            ContVarSpecEnum.ControlProfileTemperature15_TC,
            ContVarSpecEnum.ControlProfileTemperature16_TC,
            ContVarSpecEnum.ControlProfileTemperature17_TC,
            ContVarSpecEnum.ControlProfileTemperature18_TC,
            ContVarSpecEnum.ControlProfileTemperature19_TC,
            ContVarSpecEnum.ControlProfileTemperature20_TC,
            ContVarSpecEnum.ControlProfileTemperature21_TC,
            ContVarSpecEnum.ControlProfileTemperature22_TC,
            ContVarSpecEnum.ControlProfileTemperature23_TC,
            ContVarSpecEnum.ControlProfileTemperature24_TC,
            ContVarSpecEnum.ControlProfileTemperature25_TC,
        ],
    )
    TC_TimeInvariant = _DiscreteItemSpecification(
        "time_invariant",
        toggle_indep=[
            ContVarSpecEnum.TemperatureSetPointC_TC,
        ],
        toggle_dep=[
            ContVarSpecEnum.ControlProfileDuration01_TC,
            ContVarSpecEnum.ControlProfileDuration02_TC,
            ContVarSpecEnum.ControlProfileDuration03_TC,
            ContVarSpecEnum.ControlProfileDuration04_TC,
            ContVarSpecEnum.ControlProfileDuration05_TC,
            ContVarSpecEnum.ControlProfileDuration06_TC,
            ContVarSpecEnum.ControlProfileDuration07_TC,
            ContVarSpecEnum.ControlProfileDuration08_TC,
            ContVarSpecEnum.ControlProfileDuration09_TC,
            ContVarSpecEnum.ControlProfileDuration10_TC,
            ContVarSpecEnum.ControlProfileDuration11_TC,
            ContVarSpecEnum.ControlProfileDuration12_TC,
            ContVarSpecEnum.ControlProfileDuration13_TC,
            ContVarSpecEnum.ControlProfileDuration14_TC,
            ContVarSpecEnum.ControlProfileDuration15_TC,
            ContVarSpecEnum.ControlProfileDuration16_TC,
            ContVarSpecEnum.ControlProfileDuration17_TC,
            ContVarSpecEnum.ControlProfileDuration18_TC,
            ContVarSpecEnum.ControlProfileDuration19_TC,
            ContVarSpecEnum.ControlProfileDuration20_TC,
            ContVarSpecEnum.ControlProfileDuration21_TC,
            ContVarSpecEnum.ControlProfileDuration22_TC,
            ContVarSpecEnum.ControlProfileDuration23_TC,
            ContVarSpecEnum.ControlProfileDuration24_TC,
            ContVarSpecEnum.ControlProfileDuration25_TC,
            ContVarSpecEnum.ControlProfileTemperature01_TC,
            ContVarSpecEnum.ControlProfileTemperature02_TC,
            ContVarSpecEnum.ControlProfileTemperature03_TC,
            ContVarSpecEnum.ControlProfileTemperature04_TC,
            ContVarSpecEnum.ControlProfileTemperature05_TC,
            ContVarSpecEnum.ControlProfileTemperature06_TC,
            ContVarSpecEnum.ControlProfileTemperature07_TC,
            ContVarSpecEnum.ControlProfileTemperature08_TC,
            ContVarSpecEnum.ControlProfileTemperature09_TC,
            ContVarSpecEnum.ControlProfileTemperature10_TC,
            ContVarSpecEnum.ControlProfileTemperature11_TC,
            ContVarSpecEnum.ControlProfileTemperature12_TC,
            ContVarSpecEnum.ControlProfileTemperature13_TC,
            ContVarSpecEnum.ControlProfileTemperature14_TC,
            ContVarSpecEnum.ControlProfileTemperature15_TC,
            ContVarSpecEnum.ControlProfileTemperature16_TC,
            ContVarSpecEnum.ControlProfileTemperature17_TC,
            ContVarSpecEnum.ControlProfileTemperature18_TC,
            ContVarSpecEnum.ControlProfileTemperature19_TC,
            ContVarSpecEnum.ControlProfileTemperature20_TC,
            ContVarSpecEnum.ControlProfileTemperature21_TC,
            ContVarSpecEnum.ControlProfileTemperature22_TC,
            ContVarSpecEnum.ControlProfileTemperature23_TC,
            ContVarSpecEnum.ControlProfileTemperature24_TC,
            ContVarSpecEnum.ControlProfileTemperature25_TC,
            # For DiscreteItemSpecEnum.TC_ControlledCooling
            ContVarSpecEnum.InitialTemperatureC_TC,
            ContVarSpecEnum.FinalTemperatureC_TC,
            ContVarSpecEnum.FinalTimeMinutes_TC,
        ],
    )
    TC_PiecewiseLinear = _DiscreteItemSpecification(
        "piecewise_linear",
        toggle_indep=[
            ContVarSpecEnum.TemperatureSetPointC_TC,
            # For DiscreteItemSpecEnum.TC_PiecewiseLinear
            ContVarSpecEnum.ControlProfileDuration01_TC,
            ContVarSpecEnum.ControlProfileDuration02_TC,
            ContVarSpecEnum.ControlProfileDuration03_TC,
            ContVarSpecEnum.ControlProfileDuration04_TC,
            ContVarSpecEnum.ControlProfileDuration05_TC,
            ContVarSpecEnum.ControlProfileDuration06_TC,
            ContVarSpecEnum.ControlProfileDuration07_TC,
            ContVarSpecEnum.ControlProfileDuration08_TC,
            ContVarSpecEnum.ControlProfileDuration09_TC,
            ContVarSpecEnum.ControlProfileDuration10_TC,
            ContVarSpecEnum.ControlProfileDuration11_TC,
            ContVarSpecEnum.ControlProfileDuration12_TC,
            ContVarSpecEnum.ControlProfileDuration13_TC,
            ContVarSpecEnum.ControlProfileDuration14_TC,
            ContVarSpecEnum.ControlProfileDuration15_TC,
            ContVarSpecEnum.ControlProfileDuration16_TC,
            ContVarSpecEnum.ControlProfileDuration17_TC,
            ContVarSpecEnum.ControlProfileDuration18_TC,
            ContVarSpecEnum.ControlProfileDuration19_TC,
            ContVarSpecEnum.ControlProfileDuration20_TC,
            ContVarSpecEnum.ControlProfileDuration21_TC,
            ContVarSpecEnum.ControlProfileDuration22_TC,
            ContVarSpecEnum.ControlProfileDuration23_TC,
            ContVarSpecEnum.ControlProfileDuration24_TC,
            ContVarSpecEnum.ControlProfileDuration25_TC,
            ContVarSpecEnum.ControlProfileTemperature01_TC,
            ContVarSpecEnum.ControlProfileTemperature02_TC,
            ContVarSpecEnum.ControlProfileTemperature03_TC,
            ContVarSpecEnum.ControlProfileTemperature04_TC,
            ContVarSpecEnum.ControlProfileTemperature05_TC,
            ContVarSpecEnum.ControlProfileTemperature06_TC,
            ContVarSpecEnum.ControlProfileTemperature07_TC,
            ContVarSpecEnum.ControlProfileTemperature08_TC,
            ContVarSpecEnum.ControlProfileTemperature09_TC,
            ContVarSpecEnum.ControlProfileTemperature10_TC,
            ContVarSpecEnum.ControlProfileTemperature11_TC,
            ContVarSpecEnum.ControlProfileTemperature12_TC,
            ContVarSpecEnum.ControlProfileTemperature13_TC,
            ContVarSpecEnum.ControlProfileTemperature14_TC,
            ContVarSpecEnum.ControlProfileTemperature15_TC,
            ContVarSpecEnum.ControlProfileTemperature16_TC,
            ContVarSpecEnum.ControlProfileTemperature17_TC,
            ContVarSpecEnum.ControlProfileTemperature18_TC,
            ContVarSpecEnum.ControlProfileTemperature19_TC,
            ContVarSpecEnum.ControlProfileTemperature20_TC,
            ContVarSpecEnum.ControlProfileTemperature21_TC,
            ContVarSpecEnum.ControlProfileTemperature22_TC,
            ContVarSpecEnum.ControlProfileTemperature23_TC,
            ContVarSpecEnum.ControlProfileTemperature24_TC,
            ContVarSpecEnum.ControlProfileTemperature25_TC,
        ],
        toggle_dep=[
            ContVarSpecEnum.InitialTemperatureC_TC,
            ContVarSpecEnum.FinalTemperatureC_TC,
            ContVarSpecEnum.FinalTimeMinutes_TC,
        ],
    )

    ####################
    # Crystallizer
    ####################

    CR_SpecifiedVolume = _DiscreteItemSpecification(
        "specified_volume",
        toggle_indep=[
            ContVarSpecEnum.EquipmentVolume_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.EquipmentCrossSectionalArea_CR,
            ContVarSpecEnum.EquipmentDiameter_CR,
            ContVarSpecEnum.EquipmentHeight_CR,
        ],
    )
    CR_SpecifiedHeightAndCrossSectionalArea = _DiscreteItemSpecification(
        "specified_height_and_cross_sectional_area",
        toggle_indep=[
            ContVarSpecEnum.EquipmentHeight_CR,
            ContVarSpecEnum.EquipmentCrossSectionalArea_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.EquipmentVolume_CR,
            ContVarSpecEnum.EquipmentDiameter_CR,
        ],
    )
    CR_SpecifiedHeightAndDiameter = _DiscreteItemSpecification(
        "specified_height_and_diameter",
        toggle_indep=[
            ContVarSpecEnum.EquipmentHeight_CR,
            ContVarSpecEnum.EquipmentDiameter_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.EquipmentVolume_CR,
            ContVarSpecEnum.EquipmentCrossSectionalArea_CR,
        ],
    )
    CR_EvaporationNotConsidered = _DiscreteItemSpecification(
        "evaporation_not_considered",
        toggle_indep=[],
        toggle_dep=[],
    )
    CR_EvaporationConsidered = _DiscreteItemSpecification(
        "evaporation_considered",
        toggle_indep=[],
        toggle_dep=[],
    )
    CR_BatchOperation = _DiscreteItemSpecification(
        "batch_operation",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.FlowRateSetPoint_CR,
            ContVarSpecEnum.MeanResidenceTime_CR,
            ContVarSpecEnum.RelativeFill_CR,
            ContVarSpecEnum.TimeConstantOfFlowResponse_CR,
        ],
    )
    CR_SpecifiedFlowRate = _DiscreteItemSpecification(
        "specified_flow_rate",
        toggle_indep=[
            ContVarSpecEnum.FlowRateSetPoint_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.MeanResidenceTime_CR,
            ContVarSpecEnum.RelativeFill_CR,
            ContVarSpecEnum.TimeConstantOfFlowResponse_CR,
        ],
    )
    CR_SpecifiedResidenceTime = _DiscreteItemSpecification(
        "specified_residence_time",
        toggle_indep=[
            ContVarSpecEnum.MeanResidenceTime_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.FlowRateSetPoint_CR,
            ContVarSpecEnum.RelativeFill_CR,
            ContVarSpecEnum.TimeConstantOfFlowResponse_CR,
        ],
    )
    CR_FixedVolume = _DiscreteItemSpecification(
        "fixed_volume",
        toggle_indep=[
            ContVarSpecEnum.RelativeFill_CR,
            ContVarSpecEnum.TimeConstantOfFlowResponse_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.FlowRateSetPoint_CR,
            ContVarSpecEnum.MeanResidenceTime_CR,
        ],
    )
    CR_ControlledByPump = _DiscreteItemSpecification(
        "controlled_by_pump",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.FlowRateSetPoint_CR,
            ContVarSpecEnum.MeanResidenceTime_CR,
            ContVarSpecEnum.RelativeFill_CR,
            ContVarSpecEnum.TimeConstantOfFlowResponse_CR,
        ],
    )
    CR_ControlledByVaporSource = _DiscreteItemSpecification(
        "controlled_by_vapor_source",
        toggle_indep=[
            ContVarSpecEnum.PressureSetPoint_CR,
            ContVarSpecEnum.VaporOutflowSetPoint_CR,
            ContVarSpecEnum.LiquidEntrainment_CR,
        ],
        toggle_dep=[],
    )
    CR_ControlledByVaporOutflow = _DiscreteItemSpecification(
        "controlled_by_vapor_outflow",
        toggle_indep=[
            ContVarSpecEnum.PressureSetPoint_CR,
            ContVarSpecEnum.LiquidEntrainment_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.VaporOutflowSetPoint_CR,
        ],
    )
    CR_Calculated = _DiscreteItemSpecification(
        "calculated",
        toggle_indep=[
            ContVarSpecEnum.ImpellerDiameter_CR,
            ContVarSpecEnum.ImpellerFrequency_CR,
            ContVarSpecEnum.ImpellerPowerNumber_CR,
            ContVarSpecEnum.ImpellerPumpingNumber_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SpecificPowerInput_CR,
        ],
    )
    CR_Specified = _DiscreteItemSpecification(
        "specified",
        toggle_indep=[
            ContVarSpecEnum.ImpellerPumpingNumber_CR,
            ContVarSpecEnum.SpecificPowerInput_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.ImpellerDiameter_CR,
            ContVarSpecEnum.ImpellerFrequency_CR,
            ContVarSpecEnum.ImpellerPowerNumber_CR,
        ],
    )
    # Energy balance
    CR_EnergyBalanceActive = _DiscreteItemSpecification(
        "energy_balance_active",
    )
    CR_EnergyBalanceInactive = _DiscreteItemSpecification(
        "energy_balance_inactive",
    )
    CR_HeatTransferConsidered = _DiscreteItemSpecification(
        "heat_transfer_considered",
    )
    CR_HeatTransferNotConsidered = _DiscreteItemSpecification(
        "heat_transfer_not_considered",
    )
    CR_EnergyInputCalculated = _DiscreteItemSpecification(
        "energy_input_calculated",
        toggle_indep=[
            ContVarSpecEnum.HeatingCoolingFluidTemperature_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.EnergyInput_CR,
        ],
    )
    CR_EnergyInputSpecified = _DiscreteItemSpecification(
        "energy_input_specified",
        toggle_indep=[
            ContVarSpecEnum.EnergyInput_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.HeatingCoolingFluidTemperature_CR,
        ],
    )
    CR_SpecifiedU = _DiscreteItemSpecification(
        "specified_u",
        toggle_indep=[
            ContVarSpecEnum.OverallHeatTransferCoefficient_CR,
            ContVarSpecEnum.HeatTransferArea_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.OverallHeatTransferCoefficientXArea_CR,
        ],
    )
    CR_SpecifiedUA = _DiscreteItemSpecification(
        "specified_ua",
        toggle_indep=[
            ContVarSpecEnum.OverallHeatTransferCoefficientXArea_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.OverallHeatTransferCoefficient_CR,
            ContVarSpecEnum.HeatTransferArea_CR,
        ],
    )
    CR_AmbientHeatLossNo = _DiscreteItemSpecification(
        "ambient_heat_loss_no",
    )
    CR_AmbientHeatLossYes = _DiscreteItemSpecification(
        "ambient_heat_loss_yes",
    )
    CR_HeatLossOptionSpecified = _DiscreteItemSpecification(
        "heat_loss_option_specified",
        toggle_indep=[
            ContVarSpecEnum.AmbientHeatLoss_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.AmbientHeatTransferCoefficient_CR,
            ContVarSpecEnum.AreaForAmbientHeatLoss_CR,
            ContVarSpecEnum.AmbientTemperature_CR,
        ],
    )
    CR_HeatLossOptionCalculated = _DiscreteItemSpecification(
        "heat_loss_option_calculated",
        toggle_indep=[
            ContVarSpecEnum.AmbientHeatTransferCoefficient_CR,
            ContVarSpecEnum.AreaForAmbientHeatLoss_CR,
            ContVarSpecEnum.AmbientTemperature_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.AmbientHeatLoss_CR,
        ],
    )
    # Primary nucleation
    CR_PrimaryNucleationKineticsInactive = _DiscreteItemSpecification(
        "primary_nucleation_kinetics_inactive",
    )
    CR_PrimaryNucleationKineticsClassical = _DiscreteItemSpecification(
        "primary_nucleation_kinetics_classical",
    )
    CR_PrimaryNucleationKineticsPowerLaw = _DiscreteItemSpecification(
        "primary_nucleation_kinetics_power_law",
    )
    CR_SurfaceEnergyComputed = _DiscreteItemSpecification(
        "surface_energy_computed",
        toggle_indep=[
            ContVarSpecEnum.PrimaryNucleationClassicalRateConstant_CR,
            ContVarSpecEnum.PrimaryNucleationClassicalSurfaceEnergyCorrectionFactor_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.PrimaryNucleationClassicalSurfaceEnergy_CR,
        ],
    )
    CR_SurfaceEnergySpecified = _DiscreteItemSpecification(
        "surface_energy_specified",
        toggle_indep=[
            ContVarSpecEnum.PrimaryNucleationClassicalRateConstant_CR,
            ContVarSpecEnum.PrimaryNucleationClassicalSurfaceEnergy_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.PrimaryNucleationClassicalSurfaceEnergyCorrectionFactor_CR,
        ],
    )
    CR_PrimaryNucleationRelativeSupersaturation = _DiscreteItemSpecification(
        "primary_nucleation_relative_supersaturation",
        toggle_indep=[
            ContVarSpecEnum.PrimaryNucleationPowerLawRateConstant_CR,
            ContVarSpecEnum.PrimaryNucleationPowerLawSupersaturationOrder_CR,
            ContVarSpecEnum.PrimaryNucleationPowerLawActivationEnergy_CR,
        ],
    )
    CR_PrimaryNucleationAbsoluteSupersaturation = _DiscreteItemSpecification(
        "primary_nucleation_absolute_supersaturation",
        toggle_indep=[
            ContVarSpecEnum.PrimaryNucleationPowerLawRateConstant_CR,
            ContVarSpecEnum.PrimaryNucleationPowerLawSupersaturationOrder_CR,
            ContVarSpecEnum.PrimaryNucleationPowerLawActivationEnergy_CR,
        ],
    )
    # Secondary nucleation - activated
    CR_SecondaryNucleationActivatedKineticsInactive = _DiscreteItemSpecification(
        "secondary_nucleation_activated_kinetics_inactive",
    )
    CR_SecondaryNucleationActivatedKineticsMersmann = _DiscreteItemSpecification(
        "secondary_nucleation_activated_kinetics_mersmann",
        toggle_indep=[
            ContVarSpecEnum.SecondaryNucleationActivatedMersmannRateConstant_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedMersmannSolubilityFactor_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedMersmannNumEntities_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedMersmannSurfaceAreaShapeFactor_CR,
        ],
    )
    CR_SecondaryNucleationActivatedKineticsPowerLaw = _DiscreteItemSpecification(
        "secondary_nucleation_activated_kinetics_power_law",
    )
    CR_SecondaryNucleationActivatedRelativeSupersaturation = _DiscreteItemSpecification(
        "secondary_nucleation_activated_relative_supersaturation",
        toggle_indep=[
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawRateConstant_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSupersaturationOrder_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawActivationEnergy_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSpecificPowerInputOrder_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSurfaceAreaOrder_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSurfaceAreaShapeFactor_CR,
        ],
    )
    CR_SecondaryNucleationActivatedAbsoluteSupersaturation = _DiscreteItemSpecification(
        "secondary_nucleation_activated_absolute_supersaturation",
        toggle_indep=[
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawRateConstant_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSupersaturationOrder_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawActivationEnergy_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSpecificPowerInputOrder_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSurfaceAreaOrder_CR,
            ContVarSpecEnum.SecondaryNucleationActivatedPowerLawSurfaceAreaShapeFactor_CR,
        ],
    )
    # Secondary nucleation - attrition
    CR_SecondaryNucleationAttritionKineticsInactive = _DiscreteItemSpecification(
        "secondary_nucleation_attrition_kinetics_inactive",
    )
    CR_SecondaryNucleationAttritionKineticsEvans = _DiscreteItemSpecification(
        "secondary_nucleation_attrition_kinetics_evans",
    )
    CR_SecondaryNucleationAttritionKineticsPowerLaw = _DiscreteItemSpecification(
        "secondary_nucleation_attrition_kinetics_power_law",
    )
    CR_SecondaryNucleationAttritionCollisionTypeCI = _DiscreteItemSpecification(
        "secondary_nucleation_attrition_collision_type_ci",
        toggle_indep=[
            ContVarSpecEnum.SecondaryNucleationAttritionEvansRateConstantCI_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSizeAboveCI_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSupersaturationOrderCI_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SecondaryNucleationAttritionEvansRateConstantCC_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSizeAboveCC_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSupersaturationOrderCC_CR,
        ],
    )
    CR_SecondaryNucleationAttritionCollisionTypeCC = _DiscreteItemSpecification(
        "secondary_nucleation_attrition_collision_type_cc",
        toggle_indep=[
            ContVarSpecEnum.SecondaryNucleationAttritionEvansRateConstantCC_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSizeAboveCC_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSupersaturationOrderCC_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SecondaryNucleationAttritionEvansRateConstantCI_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSizeAboveCI_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSupersaturationOrderCI_CR,
        ],
    )
    CR_SecondaryNucleationAttritionCollisionTypeBoth = _DiscreteItemSpecification(
        "secondary_nucleation_attrition_collision_type_both",
        toggle_indep=[
            ContVarSpecEnum.SecondaryNucleationAttritionEvansRateConstantCI_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSizeAboveCI_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSupersaturationOrderCI_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansRateConstantCC_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSizeAboveCC_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionEvansSupersaturationOrderCC_CR,
        ],
    )
    CR_SecondaryNucleationAttritionRelativeSupersaturation = _DiscreteItemSpecification(
        "secondary_nucleation_attrition_relative_supersaturation",
        toggle_indep=[
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawRateConstant_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawSupersaturationOrder_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawActivationEnergy_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawSpecificPowerInputOrder_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawSlurryDensityOrder_CR,
        ],
    )
    CR_SecondaryNucleationAttritionAbsoluteSupersaturation = _DiscreteItemSpecification(
        "secondary_nucleation_attrition_absolute_supersaturation",
        toggle_indep=[
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawRateConstant_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawSupersaturationOrder_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawActivationEnergy_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawSpecificPowerInputOrder_CR,
            ContVarSpecEnum.SecondaryNucleationAttritionPowerLawSlurryDensityOrder_CR,
        ],
    )
    # Growth and dissolution
    CR_GrowthAndDissolutionKineticsInactive = _DiscreteItemSpecification(
        "growth_and_dissolution_inactive",
    )
    CR_GrowthAndDissolutionKineticsClassical = _DiscreteItemSpecification(
        "growth_and_dissolution_classical",
        toggle_indep=[
            ContVarSpecEnum.GrowthAndDissolutionClassicalActivationEnergy_CR,
            ContVarSpecEnum.GrowthAndDissolutionClassicalGrowthRateConstant_CR,
            ContVarSpecEnum.GrowthAndDissolutionClassicalSupersaturationOrder_CR,
            ContVarSpecEnum.GrowthAndDissolutionEffectiveDiffusitivityCorrectionFactor_CR,
        ],
    )
    CR_GrowthAndDissolutionKineticsMersmann = _DiscreteItemSpecification(
        "growth_and_dissolution_mersmann",
        toggle_indep=[
            ContVarSpecEnum.GrowthAndDissolutionMersmannActivationEnergy_CR,
            ContVarSpecEnum.GrowthAndDissolutionMersmannGrowthRateConstant_CR,
            ContVarSpecEnum.GrowthAndDissolutionMersmannSupersaturationOrder_CR,
            ContVarSpecEnum.GrowthAndDissolutionEffectiveDiffusitivityCorrectionFactor_CR,
        ],
    )
    CR_GrowthAndDissolutionKineticsPowerLaw = _DiscreteItemSpecification(
        "growth_and_dissolution_power_law",
        toggle_indep=[
            ContVarSpecEnum.GrowthAndDissolutionPowerLawActivationEnergy_CR,
            ContVarSpecEnum.GrowthAndDissolutionPowerLawGrowthRateConstant_CR,
            ContVarSpecEnum.GrowthAndDissolutionPowerLawSupersaturationOrder_CR,
        ],
    )
    CR_GrowthAndDissolutionMassTransferCorrelationGarside = _DiscreteItemSpecification(
        "growth_and_dissolution_mass_transfer_correlation_garside",
        toggle_dep=[
            ContVarSpecEnum.GrowthAndDissolutionVesselDiameter_CR,
        ],
    )
    CR_GrowthAndDissolutionMassTransferCorrelationLevinsAndGlastonbury = (
        _DiscreteItemSpecification(
            "growth_and_dissolution_mass_transfer_correlation_lewis_and_glastonbury",
            toggle_indep=[
                ContVarSpecEnum.GrowthAndDissolutionVesselDiameter_CR,
            ],
        )
    )

    CR_GrowthAndDissolutionSizeDependantSolubilityActive = _DiscreteItemSpecification(
        "growth_and_dissolution_size_dependant_solubility_active",
        toggle_indep=[
            ContVarSpecEnum.GrowthAndDissolutionSurfaceEnergyForSizeDependantSolubility_CR,
        ],
    )
    CR_GrowthAndDissolutionSizeDependantSolubilityInactive = _DiscreteItemSpecification(
        "growth_and_dissolution_size_dependant_solubility_inactive",
        toggle_dep=[
            ContVarSpecEnum.GrowthAndDissolutionSurfaceEnergyForSizeDependantSolubility_CR,
        ],
    )
    CR_GrowthAndDissolutionDrivingForceRelativeSupersaturation = (
        _DiscreteItemSpecification(
            "growth_and_dissolution_driving_force_relative_supersaturation",
        )
    )
    CR_GrowthAndDissolutionDrivingForceAbsoluteSupersaturation = (
        _DiscreteItemSpecification(
            "growth_and_dissolution_driving_force_absolute_supersaturation",
        )
    )
    # Agglomeration
    CR_AgglomerationMumtazKinetics = _DiscreteItemSpecification(
        "agglomeration_mumtaz_kinetics",
        toggle_indep=[
            ContVarSpecEnum.AgglomerationParameterA50_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.ConstantTermA1_CR,
            ContVarSpecEnum.GrowthRateOrderA2_CR,
            ContVarSpecEnum.SpecificPowerInputOrderA3_CR,
        ],
    )
    CR_AgglomerationSmoluchowskiKinetics = _DiscreteItemSpecification(
        "agglomeration_smoluchowski_kinetics",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.AgglomerationParameterA50_CR,
            ContVarSpecEnum.ConstantTermA1_CR,
            ContVarSpecEnum.GrowthRateOrderA2_CR,
            ContVarSpecEnum.SpecificPowerInputOrderA3_CR,
        ],
    )
    CR_AgglomerationPowerLawKinetics = _DiscreteItemSpecification(
        "agglomeration_power_law_kinetics",
        toggle_indep=[
            ContVarSpecEnum.ConstantTermA1_CR,
            ContVarSpecEnum.GrowthRateOrderA2_CR,
            ContVarSpecEnum.SpecificPowerInputOrderA3_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.AgglomerationParameterA50_CR,
        ],
    )
    CR_AgglomerationNone = _DiscreteItemSpecification(
        "agglomeration_none",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.AgglomerationParameterA50_CR,
            ContVarSpecEnum.ConstantTermA1_CR,
            ContVarSpecEnum.GrowthRateOrderA2_CR,
            ContVarSpecEnum.SpecificPowerInputOrderA3_CR,
        ],
    )
    # Evaporation
    CR_EvaporationIncludeDiffusiveMassTransferEffects = _DiscreteItemSpecification(
        "include_diffusive_mass_transfer_effects",
        toggle_indep=[],
        toggle_dep=[],
    )
    CR_EvaporationFastMassTransfer = _DiscreteItemSpecification(
        "fast_mass_transfer_vle_driven",
        toggle_indep=[],
        toggle_dep=[],
    )
    CR_EvaporationMassTransferCoefficientBasisMass = _DiscreteItemSpecification(
        "mass_transfer_coefficient_(k)",
        toggle_indep=[],
        toggle_dep=[],
    )
    # CR_EvaporationMassTransferCoefficientBasisVolume = _DiscreteItemSpecification(
    #     "volumetric_mass_transfer_coefficient_(k*a)_recommended",
    #     toggle_indep=[],
    #     toggle_dep=[],
    # )
    CR_EvaporationMassTransferCoefficientInputUser = _DiscreteItemSpecification(
        "user_defined_for_all_transferred_species",
        toggle_indep=[
            ContVarSpecEnum.EvaporationLiquidMassTransferCoefficient_CR,
            ContVarSpecEnum.EvaporationVaporMassTransferCoefficient_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.EvaporationUniformLiquidMassTransferCoefficient_CR,
            ContVarSpecEnum.EvaporationUniformVaporMassTransferCoefficient_CR,
        ],
    )
    CR_EvaporationMassTransferCoefficientInputUniform = _DiscreteItemSpecification(
        "uniform_for_all_transferred_species",
        toggle_indep=[
            ContVarSpecEnum.EvaporationUniformLiquidMassTransferCoefficient_CR,
            ContVarSpecEnum.EvaporationUniformVaporMassTransferCoefficient_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.EvaporationLiquidMassTransferCoefficient_CR,
            ContVarSpecEnum.EvaporationVaporMassTransferCoefficient_CR,
        ],
    )
    CR_EvaporationMassTransferAreaSpecificContactArea = _DiscreteItemSpecification(
        "specific_contact_area",
        toggle_indep=[
            ContVarSpecEnum.EvaporationSpecificContactArea_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.EvaporationContactArea_CR,
        ],
    )
    CR_EvaporationMassTransferAreaContactArea = _DiscreteItemSpecification(
        "contact_area",
        toggle_indep=[
            ContVarSpecEnum.EvaporationContactArea_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.EvaporationSpecificContactArea_CR,
        ],
    )
    CR_EvaporationMassTransferAreaCalculateFromGeometry = _DiscreteItemSpecification(
        "calculate_from_geometry",
        toggle_indep=[],
        toggle_dep=[
            ContVarSpecEnum.EvaporationSpecificContactArea_CR,
            ContVarSpecEnum.EvaporationContactArea_CR,
        ],
    )
    # Schduler: Discharge
    CR_SchedulerDischargeOperationsOff = _DiscreteItemSpecification(
        "scheduler_discharge_operations_off",
    )
    CR_SchedulerDischargeOperationsOn = _DiscreteItemSpecification(
        "scheduler_discharge_operations_on",
    )
    CR_SchedulerDischargeSpecsInstantaneousEmpty = _DiscreteItemSpecification(
        "scheduler_discharge_specifications_instantaneous_empty",
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
        ],
    )
    CR_SchedulerDischargeSpecsDurationTillEmpty = _DiscreteItemSpecification(
        "scheduler_discharge_specifications_duration_till_empty",
        toggle_indep=[
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
        ],
    )
    CR_SchedulerDischargeSpecsFlowRateTillEmpty = _DiscreteItemSpecification(
        "scheduler_discharge_specifications_flow_rate_till_empty",
        toggle_indep=[
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
        ],
    )
    CR_SchedulerDischargeSpecsDurationAndFlowRate = _DiscreteItemSpecification(
        "scheduler_discharge_specifications_duration_and_flow_rate",
        toggle_indep=[
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
        ],
    )
    CR_SchedulerDischargeSpecsDurationAndFractionDischarged = (
        _DiscreteItemSpecification(
            "scheduler_discharge_specifications_duration_and_fraction_discharged",
            toggle_indep=[
                ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
                ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ],
            toggle_dep=[
                ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
                ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
                ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
                ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
            ],
        )
    )
    CR_SchedulerDischargeSpecsDurationAndHoldupDischarged = _DiscreteItemSpecification(
        "scheduler_discharge_specifications_duration_and_holdup_discharged",
        toggle_indep=[
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
        ],
    )
    CR_SchedulerDischargeSpecsDurationAndHoldupLeftBehind = _DiscreteItemSpecification(
        "scheduler_discharge_specifications_duration_and_holdup_left_behind",
        toggle_indep=[
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
        ],
    )
    CR_SchedulerDischargeSpecsDurationAndRelativeFillLeftBehind = (
        _DiscreteItemSpecification(
            "scheduler_discharge_specifications_duration_and_relative_fill_left_behind",
            toggle_indep=[
                ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
                ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
            ],
            toggle_dep=[
                ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
                ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
                ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
                ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
            ],
        )
    )
    CR_SchedulerDischargeSpecsFlowRateAndFractionDischarged = (
        _DiscreteItemSpecification(
            "scheduler_discharge_specifications_flow_rate_and_fraction_discharged",
            toggle_indep=[
                ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
                ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ],
            toggle_dep=[
                ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
                ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
                ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
                ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
            ],
        )
    )
    CR_SchedulerDischargeSpecsFlowRateAndHoldupDischarged = _DiscreteItemSpecification(
        "scheduler_discharge_specifications_flow_rate_and_holdup_discharged",
        toggle_indep=[
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
        ],
    )
    CR_SchedulerDischargeSpecsFlowRateAndHoldupLeftBehind = _DiscreteItemSpecification(
        "scheduler_discharge_specifications_flow_rate_and_holdup_left_behind",
        toggle_indep=[
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
        ],
    )
    CR_SchedulerDischargeSpecsFlowRateAndRelativeFillLeftBehind = _DiscreteItemSpecification(
        "scheduler_discharge_specifications_flow_rate_and_relative_fill_left_behind",
        toggle_indep=[
            ContVarSpecEnum.SchedulerDischargeMassFlowRate_CR,
            ContVarSpecEnum.SchedulerDischargeTargetRelativeFill_CR,
        ],
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeDurationOfDischarge_CR,
            ContVarSpecEnum.SchedulerDischargeFractionOfMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupDischarged_CR,
            ContVarSpecEnum.SchedulerDischargeMassHoldupLeftBehind_CR,
        ],
    )
    CR_SchedulerDischargeMassOrVolumeBasisMass = _DiscreteItemSpecification(
        "scheduler_discharge_mass_basis",
    )
    # CR_SchedulerDischargeMassOrVolumeBasisVolume = _DiscreteItemSpecification(
    #     "scheduler_discharge_volume_basis",
    # )
    CR_SchedulerDischargeNumDischargesOne = _DiscreteItemSpecification(
        "scheduler_discharge_number_of_discharges_one",
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeNumberOfDischarges_CR,
        ],
    )
    CR_SchedulerDischargeNumDischargesMultipleFixedNumber = _DiscreteItemSpecification(
        "scheduler_discharge_number_of_discharges_multiple_fixed_number",
        toggle_indep=[
            ContVarSpecEnum.SchedulerDischargeNumberOfDischarges_CR,
        ],
    )
    CR_SchedulerDischargeNumDischargesMultipleRecurring = _DiscreteItemSpecification(
        "scheduler_discharge_number_of_discharges_multiple_recurring",
        toggle_dep=[
            ContVarSpecEnum.SchedulerDischargeNumberOfDischarges_CR,
        ],
    )

    def __repr__(self):
        return f"{self.__class__.__name__}.{self.name}"

    def get_specification(self):
        return self.value

    @property
    def unit(self) -> str:
        return "na"

    @property
    def category(self) -> VariableCategoryEnum:
        """Indicates if ts a setpoint or not"""
        return VariableCategoryEnum.NONE

    def _get_stringify(self) -> str:
        """returns a string of the Variable"""
        return self.value.unique_label


####################

# SPLITTERS


####################

# REACTORS


# @unique
# class ReactorPhaseEnum(BaseGenericEnum):
#     MIXTURE = auto()
#     VAPOR = auto()
#     LIQUID = auto()


@unique
class ReactorBasisEnum(BaseGenericEnum):
    ACTIVITY = auto()
    FUGACITY = auto()
    MOLAR_CONCENTRATION = auto()
    MOLAR_FRACTION = auto()
    MASS_CONCENTRATION = auto()
    MASS_FRACTION = auto()
    PARTIAL_PRESSURE = auto()


####################


# POLICIES
@unique
class ParamTypeEnum(BaseGenericEnum):
    PROPERTY = auto()
    PARAMETER = auto()
    DISCRETESET = auto()


@unique
class PolicyActionEnum(BaseGenericEnum):
    DO_NOT_SET = auto()
    OVERRIDE = auto()
    CLIP = auto()
    NO_OVERRIDE = auto()


@unique
class UserIndustryEnum(BaseGenericEnum):
    CHEMICAL = auto()
    PHARMA = auto()


@unique
class MatrixEngineEnum(BaseGenericEnum):
    DWSIM = auto()
    GPROMS = auto()


@unique
class DaedelusEngineEnum(BaseGenericEnum):
    RANDOM_FOREST = "random_forest"
    BOOSTED_TREES = "boosted_trees"
    LSTM = "lstm"


####################

# SENTINEL FOR ARBITRARY USE


class Sentinel:
    def __init__(self):
        pass

    def __repr__(self):
        return "<Sentinel>"
