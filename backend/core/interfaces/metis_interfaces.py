from ._imports import *

#############################

# METIS ERROR HIERARCHY

class MetisError(AlephDomainError):
    """Base class for all Metis-related errors"""

    pass


class MetisSamplingError(MetisError, AlephValidationError):
    """Error during sampling generation"""

    def __init__(self, message: str, original_error: Optional[Exception] = None):
        full_message = f"Error generating samples: {message}"
        if original_error:
            full_message += f"\nOriginal error: {str(original_error)}"
        super().__init__(full_message)


class MetisDFError(MetisError, AlephProcessingError):
    """Error during data transformation"""

    def __init__(
        self,
        transformer_type: str,
        message: str,
        original_error: Optional[Exception] = None,
    ):
        full_message = f"Error in {transformer_type} transformation: {message}"
        if original_error:
            full_message += f"\nOriginal error: {str(original_error)}"
        super().__init__(full_message)


class MetisAnalysisError(MetisError, AlephProcessingError):
    """Error during result analysis"""

    def __init__(
        self,
        analysis_type: str,
        message: str,
        original_error: Optional[Exception] = None,
    ):
        full_message = f"Error in {analysis_type} analysis: {message}"
        if original_error:
            full_message += f"\nOriginal error: {str(original_error)}"
        super().__init__(full_message)

#############################

class IMetis(ABC):
    """Interface for optimization and data analysis"""

    def __init__(
        self,
        sample_generator=None,
        diagnostics_transformer=None,
        importance_analyzer=None,
    ):
        """
        Initialize Metis with configurable components.

        Args:
            sample_generator: Custom SampleGenerator instance
            diagnostics_transformer: Custom DiagnosticDataTransformer instance
            importance_analyzer: Custom FeatureSensitivityAnalysis instance
        """
        # Generators
        self.sample_generator = (
            sample_generator
            if sample_generator
            else me.SampleGenerator.create_dev_config()
        )

        # Transformers
        self.diagnostics_transformer = (
            diagnostics_transformer
            if diagnostics_transformer
            else me.z_DiagnosticDataTransformer.create_dev_config()
        )

        # Analyzers
        self.importance_analyzer = (
            importance_analyzer
            if importance_analyzer
            else me.FeatureSensitivityAnalyzer.create_dev_config()
        )
    
    def generate_samples(
        self,
        bounded_input_vars: Dict[str, Tuple[float, float]],
        fixed_input_vars: Dict[str, float],
        *,
        group_names: Optional[Union[list, None]] = None,
        output_var_names: Optional[List[str]] = None,
    ) -> pd.DataFrame:
        """
        Generate a dataframe of samples for running simulations.

        Args::vsp
            bounded_input_vars: Dictionary mapping variable names to (min, max) bounds
            fixed_input_vars: Dictionary mapping variable names to fixed values
            group_names: Optional list of group names for variables
            output_var_names: Optional list of output variable names

        Returns:
            DataFrame containing generated samples

        Raises:
            SamplingError: If sample generation fails
        """
        try:
            return self.sample_generator.generate_samples(
                bounded_input_variables=bounded_input_vars,
                fixed_input_variables=fixed_input_vars,
                output_names=output_var_names,
                group_names=group_names,
            )
        except Exception as e:
            raise MetisSamplingError(f"Failed to generate samples", e)

    def transform_dataset(
        self,
        transformer_type: str,
        samples_df: pd.DataFrame,
        response_df: pd.DataFrame,  # Changed from Series to DataFrame
        **kwargs,
    ) -> me.DataTransformDTO:  # Changed return type to include DataFrame
        """
        Transform dataset using the specified transformer.

        Args:
            transformer_type: Type of transformer to use (e.g., "diagnostic")
            samples_df: DataFrame of input samples
            samples_baseline: Baseline values for input variables
            response_df: DataFrame of response values with response names as columns
            response_baseline: Dictionary mapping response names to baseline values
            **kwargs: Additional arguments required by the specific transformer

        Returns:
            Tuple of (transformed_samples, transformed_responses)

        Raises:
            TransformationError: If transformation fails or if required kwargs are missing
        """
        # Strategy maps transformer types to (transformer, required_kwargs with defaults)
        STRATEGY = {
            "diagnostic": (
                self.diagnostics_transformer,
                {"kpi_range": None, "samples_baseline": None, "kpi_baseline": None},
            )
        }

        # Check transformer type
        if transformer_type not in STRATEGY:
            raise MetisDFError(
                transformer_type,
                f"Invalid transformer type. Supported types: {', '.join(STRATEGY.keys())}",
            )

        try:
            # Get transformer and expected kwargs with default values
            transformer, expected_kwargs = STRATEGY[transformer_type]

            # Check for missing required kwargs that don't have defaults
            missing_kwargs = [
                kwarg
                for kwarg, default in expected_kwargs.items()
                if kwarg not in kwargs and default is None
            ]
            if missing_kwargs:
                raise MetisDFError(
                    transformer_type,
                    f"Missing required kwargs: {', '.join(missing_kwargs)}",
                )

            # Add default values for optional parameters
            for kwarg, default in expected_kwargs.items():
                if kwarg not in kwargs and default is not None:
                    kwargs[kwarg] = default

            # Run transformation
            result: me.DataTransformDTO = transformer.transform(
                samples_df, response_df, **kwargs
            )

            return result

        except MetisDFError:
            # Re-raise TransformationError without wrapping
            raise
        except Exception as e:
            # Wrap other exceptions
            raise MetisDFError(transformer_type, "Transformation failed", e)

    def analyze_results(
        self, analysis_type: str, x: pd.DataFrame, y: pd.DataFrame, **kwargs
    ) -> pd.DataFrame:
        """
        Analyze data using the specified analysis method.

        The feature importance analysis uses techniques like permutation importance to:
        - Train a model on filtered inputs
        - Randomly shuffle each column to measure prediction degradation
        - Calculate importance based on how much performance drops when each variable is permuted

        Args:
            analysis_type: Type of analysis to perform (e.g., "feature_sensitivity")
            x: Feature dataframe with samples as rows and features as columns
            y: Target dataframe with response values
            **kwargs: Additional arguments required by the specific analysis

        Returns:
            DataFrame containing analysis results

        Raises:
            AnalysisError: If analysis fails or if an unsupported analysis type is requested
        """
        # Strategy maps analysis types to (analyzer, expected_kwargs_with_defaults)
        strategy = {
            "feature_sensitivity": (
                self.importance_analyzer,
                {"include_unselected": False},
            )
        }

        # Check analysis type
        if analysis_type not in strategy:
            raise MetisAnalysisError(
                analysis_type,
                f"Invalid analysis type. Supported types: {', '.join(strategy.keys())}",
            )

        try:
            # Get analyzer and expected kwargs with defaults
            analyzer, expected_kwargs = strategy[analysis_type]

            # Apply default values for any missing kwargs
            for kwarg, default_value in expected_kwargs.items():
                if kwarg not in kwargs:
                    kwargs[kwarg] = default_value

            # Run analysis
            return analyzer.analyse(x, y, **kwargs)

        except Exception as e:
            # Wrap exceptions
            raise MetisAnalysisError(analysis_type, "Analysis failed", e)
