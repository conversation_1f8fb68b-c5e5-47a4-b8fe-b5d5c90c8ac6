# IMPORTS FROM OPTIMIZER
from __future__ import annotations
from ._imports import *
import backend.core._optimizer as op

# =============================
# ERRORS

class OptimizerBaseError(AlephDomainError):
    """
    Base class for all optimizer-related errors.
    """
    def __init__(self, message: str, original_error: T.Optional[Exception] = None):
        full_message = f"Objective function error: {message}"
        if original_error:
            full_message += f" | Original error: {str(original_error)}"
        super().__init__(full_message)

class OptimizerObjectiveFunctionError(OptimizerBaseError):
    """
    Error related to the objective function in the optimizer.
    """
    def __init__(self, message: str, original_error: T.Optional[Exception] = None):
        full_message = f"Objective function error: {message}"
        if original_error:
            full_message += f" | Original error: {str(original_error)}"
        super().__init__(full_message)


class OptimizerOptimizationError(OptimizerBaseError):
    """
    Error related to the objective function in the optimizer.
    """
    def __init__(self, message: str, original_error: T.Optional[Exception] = None):
        full_message = f"Optimization error: {message}"
        if original_error:
            full_message += f" | Original error: {str(original_error)}"
        super().__init__(full_message)


# =============================
# ERRORS
# TODO : IOptimizer with 'calibrate` method and strategy to lookup 1) optimizer types and 2) objective functions

class IOptimizer(ABC):
    """
    Interface for all optimizer classes.
    """
    def __init__(
            self,
            base_optimization_config: op.VOOptimizationConfig
                 ):
        self.optimization_config: op.VOOptimizationConfig = base_optimization_config
    
    def get_optimizer(self, algorithm: op.EnumSearchAlgorithm) -> T.Type[op.BaseOptimizationRunner]:
        """
        Get the optimizer class based on the algorithm name.
        """
        lookup = {
            op.EnumSearchAlgorithm.DIRECT : op.ScipyDirectOptimizationRunner
        }
        
        try:
            return lookup[algorithm]

        except KeyError as e:
            raise OptimizerOptimizationError(
                    f"Optimzer not found. Available options: {list(lookup.keys())}. | Details {e}"
            )
            
        except Exception as e:
            raise OptimizerBaseError(
                f"unexpected error: {e}"
            )

        
    
    def get_objectivefunction(self, objfunc: Literal["surrogate", "first_principle"]) -> T.Type[op.BaseObjectiveFunction]:
        lookup = {
            "surrogate": op.SurrogateObjectiveFunction,
            # "first_principle": op.FirstPrincipleObjectiveFunction
        }

        try: 
            return lookup[objfunc]

        except KeyError as e:
            raise OptimizerObjectiveFunctionError(
                f"Objective function '{objfunc}' not found. Available options: {list(lookup.keys())}. Details: {e}"
            ) from e

        except Exception as e:
            raise OptimizerBaseError(
                f"unexpected error: {e}"
            )
