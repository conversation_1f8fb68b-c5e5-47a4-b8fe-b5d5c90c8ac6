import sklearn.impute
import backend.core._surrogate.valueobjects
from ._imports import *
import backend.core._surrogate as su
import sklearn.preprocessing
import sklearn.impute

#############################

# SURROGATE ERROR HIERARCHY

class SurrogateError(AlephDomainError):
    """Base class for all Surrogate-related errors"""
    pass


class SurrogateTrainingError(SurrogateError, AlephProcessingError):
    """Error during model training"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        full_message = f"Error during surrogate model training: {message}"
        if original_error:
            full_message += f"\nOriginal error: {str(original_error)}"
        super().__init__(full_message)


class SurrogateEvaluationError(SurrogateError, AlephProcessingError):
    """Error during model evaluation"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        full_message = f"Error during surrogate model evaluation: {message}"
        if original_error:
            full_message += f"\nOriginal error: {str(original_error)}"
        super().__init__(full_message)


class SurrogateModelError(SurrogateError, AlephValidationError):
    """Error related to surrogate model handling"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        full_message = f"Error with surrogate model: {message}"
        if original_error:
            full_message += f"\nOriginal error: {str(original_error)}"
        super().__init__(full_message)


class SurrogateConfigError(SurrogateError, AlephValidationError):
    """Error related to surrogate configuration handling"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        full_message = f"Error with surrogate configuration: {message}"
        if original_error:
            full_message += f"\nOriginal error: {str(original_error)}"
        super().__init__(full_message)

class SurrogateInfrastructureError(SurrogateError, AlephValidationError):
    """Error related to surrogate configuration handling"""
    
    def __init__(self, message: str, original_error: Optional[Exception] = None):
        full_message = f"Error with surrogate configuration: {message}"
        if original_error:
            full_message += f"\nOriginal error: {str(original_error)}"
        super().__init__(full_message)

#############################


class SurrogateRegistryPort(ABC):
    """Abstract base class for surrogate model registry adaptors"""
    # TODO this needs to get an adaptor for MLFlow or more robust registry

    @abstractmethod
    def save_datatransformer(
        self,
        metadata: su.VOMetadata_General,
        data: su.transformers.SurrogateDataTransformer,
    )-> Any:
        ...
    
    @abstractmethod
    def get_datatransformer(
        self,
        user_ref: str,
        atlas_ref: str,
    ) ->su.transformers.SurrogateDataTransformer:
        ...

    @abstractmethod
    def save_job(
        self,
        metadata: su.VOMetadata_General,
        data: su.ENTTrainingJob,
    )-> Any:
        ...

    @abstractmethod
    def get_job(
        self,
        user_ref: str,
        atlas_ref: str,
    ) ->su.ENTTrainingJob:
        ...
        

    @abstractmethod
    def save_model(
        self,
        metadata: su.VOMetadata_General,
        model: su.models.BaseSurrogateModel
    ) -> Any:
        """
        Save model to registry
        """
        pass
    
    @abstractmethod
    def get_model(
        self,
        metadata: su.VOMetadata_General
    ) ->su.models.BaseSurrogateModel:
        """
        Retrieve model from registry
        
        Args:
            model_id: Unique model identifier
            label: Model label
            version: Model version
            
        Returns:
            Retrieved model if found, None otherwise
        """
        pass

class ISurrogate():
    """Interface for surrogate model training, evaluation, and management"""
    #NOTE: SUBCLASS FOR DISTRIBUTED TRAINING INFRASTRUCTURE. Creating so it trains locally by default.
    
    def __init__(
        self,
        model_repo: SurrogateRegistryPort,
        training_runner: su.ports.TrainingRunnerPort,
    ):
        """
        Initialize Surrogate with configurable components.
        
        If Jeff Dean named this class, it would probably be 
        'HyperEfficientDistributedSurrogateOrchestrator' and
        initialize in O(1) time regardless of model complexity.
        """
        # Use injected dependencies or defaults will be handled in concrete implementations
        self.registry = model_repo
        self.logger = logging.getLogger(self.__class__.__name__)
        self.training_runner = training_runner
    
    ################################

    # DATA STUFF

    def fit_datatransformer_and_save(
        self,
        metadata: su.VOMetadata_General,
        df_x: pd.DataFrame,
        df_y: pd.DataFrame,
        col2uid: Dict[str, uuid.UUID],
        timeset_col: Optional[str] = None,
        timestep_col: Optional[str] = None,
    ) -> su.transformers.SurrogateDataTransformer:
        """
        Fit and transform data using the appropriate strategy for the given algorithm.
        
        Args:
            metadata: Metadata containing the surrogate algorithm type.
            df_x: Input features as a DataFrame.
            df_y: Target values as a DataFrame.
        
        Returns:
            A fitted SurrogateDataTransformer instance.
        
        Raises:
            SurrogateConfigError: If the algorithm is unsupported or transformation fails.
        """
        self.logger.info(f"Fitting data transformer for algorithm: {metadata.surrogate_algo.value}")
        
        mapper = {
            su.EnumSurrogateAlgorithm.RNN_TS: (
                sklearn.impute.SimpleImputer(strategy = "median"),
                sklearn.preprocessing.StandardScaler(),
                sklearn.impute.SimpleImputer(strategy = "median"),
                None
            )
        }
        try:
            # Select transformation strategy based on the algorithm
            x_imputer , x_scaler, y_imputer, y_scaler = mapper[metadata.surrogate_algo]
            
            # Instantiate and fit the transformer
            transformer = su.transformers.SurrogateDataTransformer(
                uid=uuid.uuid4(),
                df_x=df_x,
                df_y=df_y,
                timeset_col = timeset_col,
                timestep_col= timestep_col,
                col2uid_dict= col2uid,
                x_imputer=x_imputer,
                x_scaler=x_scaler,
                y_imputer=y_imputer,
                y_scaler=y_scaler
            ) 
            
            # SAVE
            self.registry.save_datatransformer(metadata, transformer)
            self.logger.info("Data transformation completed successfully and saved.")

            return transformer
        
        except Exception as e:
            self.logger.error(f"Failed to fit data transformer: {e}", exc_info=True)
            raise SurrogateConfigError("Data transformation failed", original_error=e)
    
    def get_datatransformer(
            self,
            user_ref: str,
            atlas_ref: str
            )-> su.transformers.SurrogateDataTransformer:
        return self.registry.get_datatransformer(user_ref, atlas_ref)

    ################################

    # TRAINING STUFF
    
    def create_job(
            self,
            metadata: su.VOMetadata_General,
            configuration: Literal["rnn_base"],
    ) -> su.ENTTrainingJob:
        # TODO 
        
        if configuration == "rnn_base":
            training_confg : su.VOConfig_Training =  su.trainers.RNNConfigs.get_base_training_config()
            model_config: su.VOConfig_Model =  su.trainers.RNNConfigs.get_base_model_config()
            hpo_config: su.VOConfig_HPO = su.trainers.RNNConfigs.get_base_hpo_config()

            return su.ENTTrainingJob(
                metadata=metadata,
                training_configuration=training_confg,
                model_configuration=model_config,
                hpo_configuration= hpo_config
            )
            
        else:
            raise SurrogateConfigError("Job configuration does not exist")
        
    def execute_training_job(
        self, 
        job: su.ENTTrainingJob,
        train_dataset: su.VODataset,
        test_dataset: su.VODataset,
        datatransformer: su.transformers.SurrogateDataTransformer
    ) -> Tuple[su.models.BaseSurrogateModel, su.ENTTrainingJob]:
        """
        Train a model with provided data and parameters.
        
        Returns:
            Tuple of (trained model, training job entity)
            
        Raises:
            SurrogateTrainingError: If training fails
        """
        metadata = job.metadata
        training_config = job.training_configuration
        model_config = job.model_configuration
        hpo_config = job.hpo_configuration


        try:

            # Start timing
            start_time = time.time()

            # Update job status to training
            job.status = su.EnumTrainingStatus.TRAINING.value
            job.update_status(su.EnumTrainingStatus.TRAINING, "Starting training")
            self.registry.save_job(metadata, job)

            # Execute training using the injected training runner
            self.logger.info(f"Submitting training job for algorithm: {metadata.surrogate_algo.value}")
            native_model, job = self.training_runner.submit_training_job(
                job=job,
                training_data=train_dataset,
                test_data=test_dataset,
                logging_callback=None  # Could be extended in the future
            )

            # Validate that we received a native model
            if native_model is None:
                raise SurrogateTrainingError("Training runner failed to provide a native model")

            # -------------------------------
            # MODEL PERSISTENCE STEP 

            model_wrapper_cls = self._get_model_wrapper_class(metadata.surrogate_algo)
            model = model_wrapper_cls(native_model, datatransformer)
            
            
            # Update job with final timing, ensuring at least 1 second
            elapsed_time = time.time() - start_time
            job.runtime_seconds = max(1, int(elapsed_time))
            
            # Only set complete status if not already set by trainer
            if job.status != su.EnumTrainingStatus.FAILED.value:
                job.status = su.EnumTrainingStatus.COMPLETE.value
                
            self.logger.info(f"Training completed successfully in {job.runtime_seconds} seconds")
            
            return model, job
            
        except Exception as e:
            # Update job with error state
            job.status = su.EnumTrainingStatus.FAILED.value
            job.error_message = str(e)
            
            # Ensure runtime is set even for errors, minimum 1 second
            if 'start_time' in locals():
                elapsed_time = time.time() - start_time  # type: ignore
                job.runtime_seconds = max(1, int(elapsed_time))
            else:
                job.runtime_seconds = 1
            
            # Log the error and re-raise
            self.logger.error(f"Training failed: {e}", exc_info=True)
            
            # Re-raise the original error if it's a SurrogateError, otherwise wrap it
            if isinstance(e, SurrogateError):
                raise
            else:
                raise SurrogateTrainingError("Failed to train surrogate model", original_error=e)

    def _collect_environment_info(self) -> Dict[str, str]:
        """Collect information about the execution environment"""
        import platform
        import socket
        from datetime import datetime, timezone
        import os
        
        info = {
            "hostname": socket.gethostname(),
            "platform": platform.platform(),
            "python": platform.python_version(),
            "timestamp": datetime.now(tz=timezone.utc).isoformat(),
            "process_id": str(os.getpid())
        }
        
        # Add package versions if available
        try:
            import numpy as np
            info["numpy"] = np.__version__
        except ImportError:
            pass
            
        try:
            import pandas as pd
            info["pandas"] = pd.__version__
        except ImportError:
            pass
        
        try:
            import sklearn
            info["sklearn"] = sklearn.__version__
        except ImportError:
            pass
            
        try:
            import torch
            info["pytorch"] = torch.__version__
            if torch.cuda.is_available():
                # info["cuda"] = torch.version.cuda
                info["cuda_device"] = torch.cuda.get_device_name(0)
        except ImportError:
            pass
        
        return info


        
    def _get_model_wrapper_class(self, algorithm: su.EnumSurrogateAlgorithm) -> Type[su.models.BaseSurrogateModel]:
        """Map algorithm enum to appropriate model wrapper class"""
        try:
            MODEL_MAP = {
                    # SKLearnModels
                su.EnumSurrogateAlgorithm.RANDOM_FOREST: su.models.SKLearnModel,

                # PyTorch Models
                su.EnumSurrogateAlgorithm.RNN_TS: su.models.PyTorchSurrogateModel
            }
            
            model_cls = MODEL_MAP.get(algorithm)
            if not model_cls:
                raise SurrogateConfigError(f"Unsupported algorithm for model wrapping: {algorithm}")
                
            return model_cls

        except (AttributeError, ImportError) as e:
            # Handle case where models module might not be fully set up
            self.logger.error(f"Error accessing model classes: {e}")
            raise SurrogateConfigError(f"Failed to access model wrapper for algorithm {algorithm}", original_error=e)
        
    def save_surrogate_job(
            self, 
            metadata: su.VOMetadata_General, 
            job: su.ENTTrainingJob
        ) -> None:
            """
            Save a training job
                
            Raises:
                SurrogateConfigError: If job saving fails
            """
            try:
                self.registry.save_job(metadata, job)
                self.logger.info(f"Successfully saved training job for {metadata.user_reference}/{metadata.atlas_reference}")
            except Exception as e:
                self.logger.error(f"Failed to save training job: {str(e)}", exc_info=True)
                raise SurrogateConfigError(f"Failed to save training job for {metadata.user_reference}/{metadata.atlas_reference}", original_error=e)
            
    def get_surrogate_job(
        self, 
        user_ref: str,
        atlas_ref: str, 
    ) -> su.ENTTrainingJob:
        """
        Get training configuration by atlas label and user ID.
            
        Returns:
            ENTTrainingJob: The retrieved training job
            
        Raises:
            SurrogateConfigError: If job retrieval fails
        """
        try:
            return self.registry.get_job(user_ref, atlas_ref)
        except SurrogateError as e:
            # Re-raise existing domain errors
            self.logger.error(f"Failed to retrieve training job: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Failed to retrieve training job for {user_ref}/{atlas_ref}: {str(e)}", exc_info=True)
            raise SurrogateConfigError(f"Failed to retrieve training job for {user_ref}/{atlas_ref}", original_error=e)

    #############################

    # MODEL REGISTRY 
    def save_model(
        self, 
        metadata: su.VOMetadata_General,
        model: su.models.BaseSurrogateModel, 
    ) -> None:
        """
        Save model to registry.
        
        Args:
            model: Trained surrogate model to save
            metadata: Dictionary of metadata to associate with the model
                
        Raises:
            SurrogateModelError: If model saving fails
        """
        try:
            self.registry.save_model(
                metadata,
                model
            )
            self.logger.info(f"Successfully saved model for {metadata.user_reference}/{metadata.atlas_reference}")
        except Exception as e:
            self.logger.error(f"Failed to save surrogate model: {str(e)}", exc_info=True)
            raise SurrogateModelError(f"Failed to save surrogate model for {metadata.user_reference}/{metadata.atlas_reference}", original_error=e)
        
    def get_model(
        self, 
        user_ref: str,
        atlas_ref: str, 
        *,
        filter_by_label: Optional[str] = None, 
        filter_by_version: Optional[str] = None,
    ) -> su.models.BaseSurrogateModel:
        """
        Retrieve model from registry.
        TODO the filters need implemnting once label and version patterns are solidified
        
        Args:
            user_ref: User reference identifier
            atlas_ref: Atlas reference identifier
            filter_by_label: Optional label filter
            filter_by_version: Optional version filter
        
        Returns:
            BaseSurrogateModel: The retrieved surrogate model
            
        Raises:
            SurrogateModelError: If model retrieval fails
        """
        try:
            surrogate_job = self.get_surrogate_job(user_ref, atlas_ref)
            return self.registry.get_model(surrogate_job.metadata)
        except SurrogateError as e:
            # Re-raise existing domain errors
            self.logger.error(f"Failed to retrieve surrogate model: {str(e)}")
            raise
        except Exception as e:
            self.logger.error(f"Failed to retrieve surrogate model for {user_ref}/{atlas_ref}: {str(e)}", exc_info=True)
            raise SurrogateModelError(f"Failed to retrieve surrogate model for {user_ref}/{atlas_ref}", original_error=e)