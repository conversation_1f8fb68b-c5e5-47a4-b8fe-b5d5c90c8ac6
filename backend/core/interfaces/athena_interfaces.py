from ._imports import *


class AthenaBaseError(AlephDomainError):
    def __init__(self, message: str) -> None:
        self.message = message
        # Include serialization in super() call so it appears in str(error)
        full_message = message

        super().__init__(full_message)

class AthenaAttributeError(AthenaBaseError, AlephValidationError):
    def __init__(self, message: str) -> None:
        self.message = message
        # Include serialization in super() call so it appears in str(error)
        full_message = message

        super().__init__(full_message)
    
VALID_ENTITES = {
    at.BatteryIn,
    at.BatteryOut,
    at.Heater,
    at.Cooler,
    at.HeatExchanger,
    at.AirCooler2,
    at.OrificePlate,
    at.Compressor,
    at.Pump,
    at.Expander,
    at.Valve,
    at.StreamMixer,
    at.Vessel, 
    at.ShortcutColumn,
    at.ConversionReactor,
    at.MaterialStream,
    at.InputStream,
    at.OutputStream
}
EQUIPMENT_TYPE_LOOKUP = {
    entity.__name__: entity
    for entity in VALID_ENTITES
}


class IAthena(ABC):
    """Repository interface for Athena"""
    def __init__(
            self,
            *,
            diagnostic_threshold: float = 5.0
            ) -> None:
        self.diagnostic_threshold = diagnostic_threshold
    
    def generate_entity_diagnosis(
            self,
            entity_label: str,
            variable_contributions: Dict[at.VOBaseVariable, float],
    ) -> str:

        try:
            entity_class = EQUIPMENT_TYPE_LOOKUP[entity_label]
        except KeyError as e:
            raise AthenaAttributeError(f"`{entity_label}` not found in lookup.")
        except Exception as e:
            raise AthenaBaseError(f"{str(e)}")

        diagnosis = th.Diagnostics(
                entity_class=entity_class,
                variable_contributions=variable_contributions,
                diagnostic_threshold= self.diagnostic_threshold
        )
        result = diagnosis.run()
        
        result_str = "\n".join(
            [
                f"{k.variable_enum.stringify}: {v}"
                for k, v in result.items()
            ]
        )
        """
        heat loss: blablabla
        mass flow rate: bllabalblblablaa
        """
        
        return result_str
                                                        
        


        

    