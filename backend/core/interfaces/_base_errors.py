# Cross-cutting error superclasses
class AlephDomainError(Exception):
    """Base class for all domain errors across modules"""

    pass


class AlephResourceNotFoundError(AlephDomainError):
    """Base error for when a resource cannot be found.

    Maps to HTTP 404 NOT_FOUND when caught at API boundaries.
    Use for errors where a requested resource doesn't exist but should.
    """

    pass


class AlephValidationError(AlephDomainError):
    """Base error for when input validation or business rules fail.

    Maps to HTTP 400 BAD_REQUEST when caught at API boundaries.
    Use for all client errors including invalid inputs and conflict situations.
    """

    pass


class AlephProcessingError(AlephDomainError):
    """Base error for when domain processing fails.

    Maps to HTTP 500 INTERNAL_SERVER_ERROR when caught at API boundaries.
    Use for unexpected errors, calculation failures, or system processing issues.
    """

    pass