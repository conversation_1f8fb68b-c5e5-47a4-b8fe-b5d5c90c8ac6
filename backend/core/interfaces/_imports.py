import inflection
import logging
import time
import sympy
import hashlib
import copy
import json
import math
import uuid
import typing as T
from collections import deque, defaultdict
from abc import ABC, abstractmethod
from dataclasses import dataclass, field, is_dataclass
from datetime import datetime, timedelta, timezone
from enum import Enum, auto, unique
from pprint import pformat, pprint
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    Dict,
    Generic,
    Iterable,
    List,
    Optional,
    Set,
    Tuple,
    Type,
    TypeVar,
    Union,
    overload,
    Protocol,
    runtime_checkable,
    get_type_hints,
    Literal,
    get_args,
    Iterator,
)
import matplotlib.pyplot as plt
import networkx as nx
import numpy as np
import pandas as pd
import seaborn as sns

# shared utils
import backend.core._sharedutils.Utilities as sharedutils
from backend.core._sharedutils.mixins import ReprMixin, StrMixin

# Core
from backend.core.interfaces._base_errors import *
import backend.core._atlas._singletons as domain_enums
import backend.core._athena as th
import backend.core._atlas as at
import backend.core._matrix as ma
import backend.core._metis as me