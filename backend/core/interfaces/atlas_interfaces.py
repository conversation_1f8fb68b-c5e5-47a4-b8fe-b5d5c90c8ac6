from ._imports import *

#############################

# ATLAS ERROR HIERARCHY

class AtlasBaseError(AlephDomainError):
    """Base class for all Atlas domain-related errors"""

    def __init__(self, message: str, atlas: Optional[at.AtlasRoot] = None):
        # Store structured data for the error
        self.message = message
        self.serialized_atlas = None

        # Serialize the atlas if provided
        if atlas:
            try:
                self.serialized_atlas = self._serialize_atlas(atlas)
            except Exception as e:
                self.serialized_atlas = f"<Atlas serialization failed: {str(e)}>"

        # Include serialization in super() call so it appears in str(error)
        full_message = message
        if self.serialized_atlas:
            # debug_excerpt = self.serialized_atlas[:200] + "..." if len(self.serialized_atlas) > 200 else self.serialized_atlas
            debug_excerpt = self.serialized_atlas
            full_message = f"{message}\n[DEBUG] Atlas state: {debug_excerpt}"

        super().__init__(full_message)

    @classmethod
    def _serialize_atlas(cls, atlas: at.AtlasRoot) -> str:
        # Previously tried to use serializer but caused circular import issues
        return f"{atlas.label}"


class AtlasGeneralError(AtlasBaseError, AlephProcessingError):
    """General Atlas operational error"""

    def __init__(
        self, atlas_label: str, message: str, atlas: Optional[at.AtlasRoot] = None
    ):
        self.atlas_label = atlas_label
        formatted_message = f"Atlas ID '{atlas_label}', user_id: \nError: {message}"
        super().__init__(formatted_message, atlas)


class AtlasAdaptorError(AtlasBaseError, AlephValidationError):
    """General Atlas operational error"""

    def __init__(
        self,
        user_id: str,
        atlas_label: str,
        message: str,
        atlas: Optional[at.AtlasRoot] = None,
    ):
        self.user_id = user_id
        self.atlas_label = atlas_label
        formatted_message = (
            f"Atlas ID '{atlas_label}', user_id: '{user_id}'\nError: {message}"
        )
        super().__init__(formatted_message, atlas)


class AtlasLookupError(AtlasBaseError, AlephResourceNotFoundError):
    """Raised when a lookup operation fails within an atlas."""

    def __init__(
        self,
        type_name: str,
        lookup_key: str,
        message: str,
        atlas: Optional[at.AtlasRoot] = None,
    ):
        """
        Arguments
            type_name: str. Examples: KPI, Entity, Variable
            lookup_key: str. examples: uuid str, ui labels, etc
        """
        self.type_name = type_name
        self.lookup = lookup_key
        formatted_message = f"Lookup failed: type `{type_name}`, reference :'{lookup_key}'.\nError: {message}"
        super().__init__(formatted_message, atlas)


class AltasMetadataEnums(Enum):
    """Filter types for Atlas repository queries."""

    USER_ID = "user_id"
    NOT_DELETED = "date_deleted_is_none"
    IS_TEMPLATE = "is_template"
    MODIFIED_AFTER = "date_modified_after"
    CREATED_AFTER = "date_created_after"


#############################

class IAtlas(ABC):
    """
    Repository interface for Atlas aggregate persistence operations.

    This interface defines a clean repository pattern for storing and retrieving
    Atlas aggregates with their metadata. It focuses solely on persistence
    operations without domain-specific knowledge.
    """

    def load_templates(self):
        """Check if templates exist in DB, if not add them"""
        # NOTE this is hacky. ZL should refactor load_templates out 
        import backend.application.usecase_templates as eg
        try: 
            templates = self.list(filter_criteria={AltasMetadataEnums.IS_TEMPLATE.value: True})
            labels = [atlas.label for atlas, meta in templates]

            for model_template in eg.USER_TEMPLATES:
                if model_template.label in labels:
                    continue

                meta = at.AtlasMetadata(
                    user_id="system",
                    date_created=datetime.now(timezone.utc),
                    date_modified=datetime.now(timezone.utc),
                    is_template=True,
                    atlas_label=model_template.label
                )

                self.create(model_template, meta)
        except Exception as e: 
            raise AtlasAdaptorError('system', 'multiple', f"Failed to load templates: {e}")
    
    @abstractmethod
    def create(self, atlas: at.AtlasRoot, metadata: at.AtlasMetadata):
        """Create a new Atlas aggregate with metadata"""
        raise NotImplementedError

    @abstractmethod
    def get(
        self, atlas_label: str, user_id: str
    ) -> Tuple[at.AtlasRoot, at.AtlasMetadata]:
        """
        Retrieve an Atlas aggregate by ID with its metadata.

        Args:
            atlas_id: The unique identifier of the atlas to retrieve
            user_id: The user owning the atlas

        Returns:
            A tuple containing (AtlasRoot, AtlasMetadata)

        Raises:
            AtlasNotFoundError: If the specified atlas cannot be found
        """
        ...

    @abstractmethod
    def list(
        self, filter_criteria: Optional[dict] = None
    ) -> List[Tuple[at.AtlasRoot, at.AtlasMetadata]]:
        """
        List Atlas aggregates matching the provided filter criteria.
        Args:
            filter_criteria (Optional[dict]): A dictionary containing filter conditions.
                Supported filters (use AtlasFilterType enum values as keys):
                - AtlasFilterType.USER_ID.value: Match atlases with the specified user ID
                - AtlasFilterType.NOT_DELETED.value: If True, match atlases that have not been deleted
                - AtlasFilterType.IS_TEMPLATE.value: Match atlases with specified template status
                - AtlasFilterType.MODIFIED_AFTER.value: Match atlases modified after datetime
                - AtlasFilterType.CREATED_AFTER.value: Match atlases created after datetime
        Returns:
            List[Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]]: List of tuples containing
                matched atlas objects and their metadata.
        Example:
            ## Get all atlases for a specific user
            atlases = repo.list({AtlasFilterType.USER_ID.value: 'user123'})

            ## Get all templates modified after a certain date
            atlases = repo.list({
                AtlasFilterType.IS_TEMPLATE.value: True,
                AtlasFilterType.MODIFIED_AFTER.value: datetime(2023, 1, 1)
            })

            ## Get all non-deleted atlases
            atlases = repo.list({AtlasFilterType.NOT_DELETED.value: True})
        """
        raise NotImplementedError

    @abstractmethod
    def save(self, atlas: at.AtlasRoot, metadata: at.AtlasMetadata) -> None:
        """Save changes to an existing Atlas aggregate"""
        raise NotImplementedError

    @abstractmethod
    def delete(self, atlas_label: str, user_id: str):
        """Delete an Atlas aggregate by ID"""
        raise NotImplementedError
    
    

    def _matches_criteria(self, metadata: at.AtlasMetadata, criteria: dict) -> bool:
        """Helper method to check if metadata matches filter criteria

        This default implementation supports the standard filter types defined
        in AtlasFilterType. Adaptors can override this method if they need
        custom filtering behavior.
        """
        if (
            AltasMetadataEnums.USER_ID.value in criteria
            and metadata.user_id != criteria[AltasMetadataEnums.USER_ID.value]
        ):
            return False
        # Check date_deleted_is_none filter
        if (
            AltasMetadataEnums.NOT_DELETED.value in criteria
            and criteria[AltasMetadataEnums.NOT_DELETED.value]
        ):
            if metadata.date_deleted is not None:
                return False

        # Check is_template filter
        if (
            AltasMetadataEnums.IS_TEMPLATE.value in criteria
            and metadata.is_template != criteria[AltasMetadataEnums.IS_TEMPLATE.value]
        ):
            return False

        # Check date_modified_after filter
        if (
            AltasMetadataEnums.MODIFIED_AFTER.value in criteria
            and metadata.date_modified < criteria[AltasMetadataEnums.MODIFIED_AFTER.value]
        ):
            return False

        # Check date_created_after filter
        if (
            AltasMetadataEnums.CREATED_AFTER.value in criteria
            and metadata.date_created < criteria[AltasMetadataEnums.CREATED_AFTER.value]
        ):
            return False

        # All filters passed
        return True

    # ATLAS METHODS
    @staticmethod
    def get_valid_entities(
        atlas: at.AtlasRoot
    ) -> Tuple[ List[at.ENTBaseEquipment], List [ at.ENTBaseStream ] ]:
        """
        Get valid list of entities for eqiument and streams give a user industry and chosen engine
        """
        pass
    
    @staticmethod
    def get_valid_compounds(
        atlas: at.AtlasRoot
    ) -> List[at.VOCompound]:
        """
        Get valid list of valid compounds given a user industry and chosen engine
        """
        pass

    @staticmethod
    def get_kpi(
        atlas: at.AtlasRoot,
        *,
        ui_label: Optional[str] = None,
        uid: Optional[uuid.UUID] = None,
    ) -> at.KPI:
        """Retrieve a KPI by either UI label or UUID.

        Args:
            atlas: The Atlas root object
            ui_label: The UI label of the KPI (mutually exclusive with uid)
            uid: The UUID of the KPI (mutually exclusive with ui_label)

        Returns:
            The KPI object

        Raises:
            AtlasLookupError: When the KPI cannot be found
            AttributeError: When both or neither lookup parameters are provided
        """

        # Helpers
        def get_kpi_from_uilabel(atlas: at.AtlasRoot, kpi_ui_label: str) -> at.KPI:
            try:
                return atlas.kpi_collection.get_item(kpi_ui_label)
            except KeyError as e:
                raise AtlasLookupError("KPI", kpi_ui_label, str(e), atlas=atlas)
            except Exception as e:
                raise AtlasGeneralError(atlas.label, str(e), atlas=atlas)

        def get_kpi_from_uid(atlas: at.AtlasRoot, uid: uuid.UUID) -> at.KPI:
            # Defensive
            if isinstance(uid, str):
                raise TypeError(f"Need to pass in UUID obj, got string: {uid}")

            try:
                return atlas.kpi_collection.get_item(uid)
            except KeyError as e:
                raise AtlasLookupError("KPI", str(uid), str(e), atlas=atlas)
            except Exception as e:
                raise AtlasGeneralError(atlas.label, str(e), atlas=atlas)

        # Parameter validation
        if ui_label and uid:
            raise AttributeError("Provide either ui_label OR uid, not both")
        if not ui_label and not uid:
            raise AttributeError("Either ui_label or uid is required")

        # Logic dispatching
        if ui_label:
            return get_kpi_from_uilabel(atlas, ui_label)
        # At this point, uid must be provided (based on validation above)
        return get_kpi_from_uid(atlas, uid)  # type: ignore

    @staticmethod
    def get_variable(
        atlas: at.AtlasRoot,
        *,
        ui_tuple: Optional[Tuple[str, str]] = None,
        uid: Optional[uuid.UUID] = None,
    ) -> at.VOBaseVariable:
        """Retrieve a variable by either UI tuple (entity_label, var_ui_label) or UUID.

        Args:
            atlas: The Atlas root object
            ui_tuple: A tuple of (entity_label, var_ui_label) (mutually exclusive with uid)
            uid: The UUID of the variable (mutually exclusive with ui_tuple)

        Returns:
            The variable object

        Raises:
            AtlasLookupError: When the variable cannot be found
            AttributeError: When both or neither lookup parameters are provided
            TypeError: When parameters have incorrect types
        """

        # HELPER FUNCTION
        def get_variable_from_uilabel(
            atlas: at.AtlasRoot, entity_label: str, var_ui_label: str
        ) -> at.VOBaseVariable:
            # get entity
            try:
                entity = atlas.get_equipment(by_label=entity_label)
            except KeyError:
                # If not equipment, try as stream
                try:
                    entity = atlas.get_stream(by_label=entity_label)
                except KeyError as e:
                    # Convert domain KeyError to AtlasLookupError
                    raise AtlasLookupError("Entity", entity_label, str(e), atlas=atlas)
            except Exception as e:
                raise AtlasLookupError("Entity", entity_label, str(e), atlas=atlas)

            # get var
            try:
                var = [
                    _var
                    for _var in entity.get_variables()
                    if atlas.variables_collection.get_ui_label(_var) == var_ui_label
                ][0]
            except Exception as e:
                raise AtlasLookupError(
                    f"Variable for {entity_label}", var_ui_label, str(e), atlas
                )

            return var

        def get_variable_from_uid(
            atlas: at.AtlasRoot, uid_obj: uuid.UUID
        ) -> at.VOBaseVariable:
            # Defensive
            if not isinstance(uid_obj, uuid.UUID):
                raise TypeError(f"Wrong type for {uid_obj}. Needs to be UUID obj")
            try:
                var = atlas.variables_collection.get_item(uid_obj)
            except KeyError as e:
                raise AtlasLookupError(f"Variable", str(uid_obj), str(e), atlas)
            except Exception as e:
                raise AtlasGeneralError(atlas.label, str(e))

            return var

        # Parameter validation
        if ui_tuple and uid:
            raise AttributeError("Provide either ui_tuple OR uid, not both")
        if not ui_tuple and not uid:
            raise AttributeError("Either ui_tuple or uid is required")

        # Logic dispatching
        if ui_tuple:
            # Validate tuple format
            if not isinstance(ui_tuple, tuple) or len(ui_tuple) != 2:
                raise TypeError(
                    f"ui_tuple must be a tuple of (entity_label, var_ui_label), got: {ui_tuple}"
                )
            entity_label, var_ui_label = ui_tuple
            return get_variable_from_uilabel(atlas, entity_label, var_ui_label)

        # At this point, uid must be provided (based on validation above)
        return get_variable_from_uid(atlas, uid)  # type: ignore

    @staticmethod
    def get_ui_label(
        atlas: at.AtlasRoot, *, obj: Union[at.KPI, at.VOBaseVariable]
    ) -> str:
        """Retrieve the UI label for a KPI or Variable object.

        Args:
            atlas: The Atlas root object
            obj: Either a KPI or Variable object

        Returns:
            The UI label string for the object

        Raises:
            TypeError: When the object type is not supported
            AtlasLookupError: When the label cannot be found
        """

        # Helper functions for type-specific handling
        def get_kpi_label(atlas: at.AtlasRoot, kpi: at.KPI) -> str:
            try:
                return atlas.kpi_collection.get_ui_label(kpi)
            except KeyError as e:
                raise AtlasLookupError("KPI", str(kpi.uid), str(e), atlas=atlas)
            except Exception as e:
                raise AtlasGeneralError(atlas.label, str(e), atlas=atlas)

        def get_variable_label(atlas: at.AtlasRoot, variable: at.VOBaseVariable) -> str:
            try:
                return atlas.variables_collection.get_ui_label(variable)
            except KeyError as e:
                raise AtlasLookupError(
                    "Variable", str(variable.uid), str(e), atlas=atlas
                )
            except Exception as e:
                raise AtlasGeneralError(atlas.label, str(e), atlas=atlas)

        # Type dispatch logic
        if isinstance(obj, at.KPI):
            return get_kpi_label(atlas, obj)
        elif isinstance(obj, at.VOBaseVariable):
            return get_variable_label(atlas, obj)
        else:
            raise TypeError(
                f"Object type {type(obj).__name__} not supported. Must be KPI or Variable."
            )

