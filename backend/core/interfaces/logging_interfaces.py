from ._imports import *
import logging
from abc import ABC

# TODO - ZL to define errors. inhert from _base_errors
class ILogging(ABC):
    """Interface for logging"""

    def setup(self):
        """
        Set up logging service, be it create a DB or file
        This should set the root logger for python's in-built logging module.
        Users can then use logging.<log-level>() to insert logs in our application.
        """

        # Disable uvicorn access logger to prevent duplicate logs
        logging.getLogger("uvicorn.access").handlers = []

        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.handlers = []  # Remove any existing handlers