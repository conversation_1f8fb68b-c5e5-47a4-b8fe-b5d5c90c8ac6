from ._imports import *

#############################

# MATRIX ERROR HIERARCHY

class MatrixError(AlephDomainError):
    """Base class for all Matrix simulation-related errors"""


class MatrixSimultorSetupError(MatrixError, AlephValidationError):
    """Base class for all Atlas domain-related errors"""

    def __init__(self, atlas_label: str, simulator: str, message: str):
        self.message = (
            f"Atlas ID '{atlas_label}', simulator: '{simulator}'/n Error: {message}."
        )
        super().__init__(self.message)


class MatrixSimulationRunError(MatrixError, AlephProcessingError):
    """Error during simulation execution"""

    def __init__(self, simulation_samples: pd.Series, original_error: Exception):
        message = f"Simulation failed with samples: {simulation_samples}: {str(original_error)}"
        super().__init__(message)


class MatrixResponseError(MatrixError, AlephProcessingError):
    """Error during KPI value calculation"""

    def __init__(
        self, simulation_samples: pd.Series, kpi_label: str, original_error: Exception
    ):
        message = f"KPI '{kpi_label}' calculation with samples: {simulation_samples}: {str(original_error)}"
        super().__init__(message)


class IMatrix(ABC):
    """Interface for matrix simulation operations"""

    SIMULATOR_ENGINES: Dict[str, Type[ma.BaseMatrixSimulator]] = {
        "dwsim": ma.MatrixDWSim,
        # Add more engines here as they become available:
        # "aspen": AspenPlusSimulator,
        # "hysys": HysysSimulator,
    }

    @staticmethod
    def setup_simulator(
        simulation_label: str,
        simulator_engine: str,
        atlas_obj: at.AtlasRoot,
        config: Optional[Dict[str, Any]] = None,
    ) -> ma.BaseMatrixSimulator:
        """
        Setup the endpoint simulator and return it as an object

        Args:
            simulation_label: Unique identifier for this simulation
            simulator_engine: Engine identifier (e.g., "dwsim", "aspen", "hysys")
            atlas_obj: Domain model to attach to the simulator
            config: Engine-specific configuration parameters

        Returns:
            Configured simulator instance ready to run

        Raises:
            MatrixGeneralError: If simulator setup fails
        """
        # TODO refactor registry to be at class level var
        # Registry of available simulation engines

        try:
            # Defensive
            if simulator_engine not in IMatrix.SIMULATOR_ENGINES:
                raise ValueError(
                    f"Unsupported simulation engine: {simulator_engine}. Available engines: {', '.join(IMatrix.SIMULATOR_ENGINES.keys())}"
                )

            # Get and use classmethod for instancing
            engine_class = IMatrix.SIMULATOR_ENGINES[simulator_engine]
            engine = engine_class.from_config(simulation_label, config)
            engine.setup_matrix()
            engine.attach_model(atlas_obj, share_model_state=True)

            return engine

        except Exception as e:
            raise MatrixSimultorSetupError(atlas_obj.label, simulator_engine, str(e))

    @abstractmethod
    def run_simulations(
        self,
        simulator_object: ma.BaseMatrixSimulator,
        df_samples: pd.DataFrame,
        kpis: List[at.KPI],
    ) -> pd.DataFrame:
        """
        Run a batch of simulations with varying parameters

        Args:
            simulator_object: A configured simulator instance
            df_samples: DataFrame where each row represents a simulation case
            kpis: List of KPI objects to evaluate after each simulation

        Returns:
            DataFrame containing KPI results for each simulation.
            - Column Headers: KPI UID labels
            - Rows: Simulation results in float
            - row index: matches the input samples index

        Raises:
            MatrixSimulationError: Base class for simulation errors
            SimulationRunError: When a specific simulation fails
            KPICalculationError: When KPI calculation fails
            TypeError: If any computed KPI value cannot be converted to float
        """
        ...
