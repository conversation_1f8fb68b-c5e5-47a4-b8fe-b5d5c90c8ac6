import numpy as np
import pandas as pd

#############################################################
# Returns classified values and the names of classified values
#############################################################
def classify_df(df_data, tag_dict):
    independent_tag_values = []
    dependent_tag_values = []
    independent_tag_list = []
    dependent_tag_list = []
    df_columns = list(df_data)

    for col in df_columns:
        if tag_dict[col] == "independent":
            independent_tag_values.append(df_data[col].to_numpy())
            independent_tag_list.append(col)
        else:
            dependent_tag_values.append(df_data[col].to_numpy())
            dependent_tag_list.append(col)
    return np.array(independent_tag_values), independent_tag_list, np.array(dependent_tag_values), dependent_tag_list

#############################################################
# Returns closeness matrix
#############################################################
def get_closeness_metric(instrument_values, soft_instrument_values):
    n_instruments = instrument_values.shape[0]
    n_soft_instruments = soft_instrument_values.shape[0]
    closeness = np.zeros((n_instruments, n_soft_instruments))
    #############################################################
    # METRIC: `closeness` = `cosine value` / denominator
    # denominator = MAX(`euclidean norm`, 1e-3)
    #############################################################
    for i in range(n_instruments):
        for j in range(n_soft_instruments):
            unit_instrument = instrument_values[i] / np.linalg.norm(instrument_values[i])
            unit_soft_instrument = soft_instrument_values[j] / np.linalg.norm(soft_instrument_values[j])
            cosine = np.dot(unit_instrument, unit_soft_instrument)

            euclidean_norm = np.linalg.norm(instrument_values[i] - soft_instrument_values[j])
            closeness[i][j] = cosine / max(euclidean_norm, 1e-3)
    return closeness

#############################################################
# Returns mapped independent and dependent data
#############################################################
def map_sensor_to_model(instrument_data, soft_instrument_data, instrument_dict, soft_instrument_dict):
    independent_instrument_values, independent_instrument_tags, dependent_instrument_values,\
    dependent_instrument_tags = classify_df(instrument_data, instrument_dict)
    independent_soft_instrument_values, independent_soft_instrument_tags,\
    dependent_soft_instrument_values, dependent_soft_instrument_tags = classify_df(soft_instrument_data, soft_instrument_dict)

    independent_closeness = get_closeness_metric(independent_instrument_values, independent_soft_instrument_values)
    dependent_closeness = get_closeness_metric(dependent_instrument_values, dependent_soft_instrument_values)

    independents = pd.DataFrame(independent_closeness, columns=independent_soft_instrument_tags)
    dependents = pd.DataFrame(dependent_closeness, columns=dependent_soft_instrument_tags)

    independents_mapped = dict(zip(independent_instrument_tags, independents.idxmax(axis=1)))
    dependents_mapped = dict(zip(dependent_instrument_tags, dependents.idxmax(axis=1)))

    return independents_mapped, dependents_mapped

#############################################################
# Returns mapped dict by splitting large dataframes into smaller windows and mapping each window
#############################################################
def generate_instrument_mapping_dict(instrument_data, soft_instrument_data, instrument_dict, soft_instrument_dict, num_windows):
    split_instrument_data = np.array_split(instrument_data, num_windows)
    split_soft_instrument_data = np.array_split(soft_instrument_data, num_windows)

    mapped_dicts = []
    for i in range(num_windows):
        independents, dependents = map_sensor_to_model(split_instrument_data[i], split_soft_instrument_data[i], instrument_dict, soft_instrument_dict)
        combined_dict = independents.copy()
        combined_dict.update(dependents)
        mapped_dicts.append(combined_dict)

    return pd.DataFrame(mapped_dicts).mode().to_dict('records')[0]

###############
# # DEMO
sensor_data = pd.read_csv(r"C:\Users\<USER>\Sushant Garud\Aleph\Aleph_dev-env\src\dummy data\sensor_data.csv")
model_data = pd.read_csv(r"C:\Users\<USER>\Sushant Garud\Aleph\Aleph_dev-env\src\dummy data\model_data.csv")

sensor_dict = {"A": "independent",
               "B": "dependent",
               "C": "independent",
               "D": "dependent",
               "E": "independent",
               "F": "dependent"}

stream_dict = {"1": "independent",
               "2": "dependent",
               "3": "independent",
               "4": "dependent",
               "5": "independent",
               "6": "dependent",
               "7": "independent",
               "8": "dependent",
               "9": "independent",
               "10": "dependent",
               "11": "independent",
               "12": "dependent", }

mapped_dict = get_mapped_dict(sensor_data, model_data, sensor_dict, stream_dict, 100)

print("Sensor Data:")
print(sensor_data.head())
print("Model Data:")
print(model_data.head())
print("Mapped:")
print(mapped_dict)