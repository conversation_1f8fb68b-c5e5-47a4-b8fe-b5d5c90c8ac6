from pprint import pformat


class ReprMixin:
    """A mixin class to provide a detailed `__repr__` method for any class.

    The `__str__` method collects both instance attributes and properties 
    to create a formatted string representation of the object.

    Returns:
        str: A string representation of the object with its attributes and properties.

    Example:
        >>> class Person(StrMixin):
        ...     def __init__(self, name, age):
        ...         self.name = name
        ...         self.age = age
        ...     @property
        ...     def is_adult(self):
        ...         return self.age >= 18
        >>> person = Person(name="<PERSON>", age=25)
        >>> str(person)
        'Person(\n{\n    "name": "<PERSON>",\n    "age": 25,\n    "is_adult": True\n}\n)'
    """
    def __repr__(self) -> str:
        # Collecting instance attributes
        instance_attrs = self.__dict__  # Output: {'name': '<PERSON>', 'age': 25}

        # Collecting @properties
        properties = {
            name: getattr(self, name)
            for name, attr in type(self).__dict__.items()
            if isinstance(attr, property)
        }

        # Combining instance attributes and properties
        attributes = {**instance_attrs, **properties}

        # Using pprint's pformat for a nicely formatted string representation
        formatted_attrs = pformat(attributes, indent=4, width=120)

        return f"{self.__class__.__name__}(\n{formatted_attrs}\n)"


class StrMixin:
    """A mixin class to provide a detailed `__str__` method for any class.

    The `__str__` method collects both instance attributes and properties 
    to create a formatted string representation of the object.

    Returns:
        str: A string representation of the object with its attributes and properties.

    Example:
        >>> class Person(StrMixin):
        ...     def __init__(self, name, age):
        ...         self.name = name
        ...         self.age = age
        ...     @property
        ...     def is_adult(self):
        ...         return self.age >= 18
        >>> person = Person(name="John", age=25)
        >>> str(person)
        'Person(\n{\n    "name": "John",\n    "age": 25,\n    "is_adult": True\n}\n)'
    """
    def __str__(self) -> str:
        # Collecting instance attributes
        instance_attrs = self.__dict__  # Output: {'name': 'John', 'age': 25}

        # Collecting @properties
        properties = {
            name: getattr(self, name)
            for name, attr in type(self).__dict__.items()
            if isinstance(attr, property)
        }

        # Combining instance attributes and properties
        attributes = {**instance_attrs, **properties}

        # Using pprint's pformat for a nicely formatted string representation
        formatted_attrs = pformat(attributes, indent=4, width=120)

        return f"{self.__class__.__name__}(\n{formatted_attrs}\n)"
