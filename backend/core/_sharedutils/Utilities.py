import codecs
import configparser
import ctypes
import functools
import inspect
import logging
import os
import pickle
import shutil
import warnings
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import clr
import importlib
import datetime

System = importlib.import_module("System")

import numpy as np
import pytest
import System
from System.Collections import ArrayList
from System.Collections.Generic import Dictionary, List
from System.Runtime.InteropServices import GCHandle, GCHandleType
from System import Double, String, Boolean, Object, Array, Reflection, Type

# def run_pytest_with_logging(pytest_verbosity_level = logging.WARNING):
#     original_level = logging.root.level
#     original_handlers = logging.root.handlers.copy()

#     # Set up real-time logging
#     logging.basicConfig(level=pytest_verbosity_level, format='%(levelname)-8s %(message)s')


# @contextmanager
# def scoped_file_logging(level: int = logging.INFO, caller_filename: str = None):
#     """Temporary file logging that auto-removes handler when done"""
#     timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
#     prefix = caller_filename.split(".")[0] if caller_filename else "default"
#     filename = f"{prefix}_{timestamp}.log"

#     project_root = get_project_root()
#     log_folder = project_root / "backend/infrastructure/artefacts/logs"
#     log_folder.mkdir(parents=True, exist_ok=True)
#     log_path = log_folder / filename

#     # Setup handler
#     file_handler = logging.FileHandler(log_path)
#     file_handler.setLevel(level)
#     formatter = logging.Formatter(
#         "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
#     )
#     file_handler.setFormatter(formatter)

#     # Add handler temporarily
#     logger = logging.getLogger()
#     logger.addHandler(file_handler)

#     try:
#         yield log_path
#     finally:
#         logger.removeHandler(file_handler)
#         file_handler.close()


def setup_file_logging(level: int = logging.INFO, prefix: Optional[str] = None) -> None:
    """Configure logging with custom level and dynamic filename"""
    # Generate filename with timestamp and caller info
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    prefix = prefix if prefix is not None else "default"
    filename = f"{prefix}_{timestamp}.log"

    project_root = get_project_root()
    log_folder = project_root / Path("backend/infrastructure/artefacts/logs")
    log_folder.mkdir(parents=True, exist_ok=True)
    log_path = log_folder / filename

    # Create file handler with custom level
    file_handler = logging.FileHandler(log_path)
    file_handler.setLevel(level)

    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_handler.setFormatter(formatter)
    logging.getLogger().addHandler(file_handler)


def get_dotnet_private_field(obj, field_name):
    """
    Get the value of a private field on an object using reflection.

    Args:
        obj (object): The object instance containing the private field.
        field_name (str): The name of the private field to retrieve.

    Returns:
        The value of the private field.

    Raises:
        AttributeError: If the specified field does not exist on the object.
    """
    # Get the FieldInfo object for the private field
    # We use BindingFlags to specify we want non-public instance fields
    field_info = obj.GetType().GetField(
        field_name, Reflection.BindingFlags.NonPublic | Reflection.BindingFlags.Instance
    )

    if field_info:
        # If the field exists, get its value for the given object
        return field_info.GetValue(obj)
    else:
        # If the field doesn't exist, raise an AttributeError
        raise AttributeError(
            f"'{obj.GetType().Name}' object has no attribute '{field_name}'"
        )


# def cast_value_py2dotnet(value):
#     """
#     Casts a Python value to its equivalent .NET type.

#     This function recursively casts all nested elements in dictionaries and iterables to their
#     corresponding .NET types.

#     Args:
#         value: The Python value to be cast.

#     Returns:
#         The equivalent .NET type.

#     Raises:
#         ValueError: If an unsupported type is encountered.
#     """

#     # HELPER

#     def cast_iterable_to_arraylist(iterable):
#         result = ArrayList()
#         for item in iterable:
#             if isinstance(item, (list, tuple, set)):
#                 result.Add(cast_iterable_to_arraylist(item))
#             else:
#                 result.Add(cast_value_py2dotnet(item))
#         return result

#     # LOGIC

#     if isinstance(value, (int, float)):
#         return System.Double(value)
#     elif isinstance(value, str):
#         return System.String(value)
#     elif isinstance(value, bool):
#         return System.Boolean(value)
#     elif isinstance(value, dict):
#         dotnet_dict = Dictionary[System.Object, System.Object]()
#         for k, v in value.items():
#             dotnet_dict[cast_value_py2dotnet(k)] = cast_value_py2dotnet(v)
#         return dotnet_dict
#     elif isinstance(value, (list, tuple, set)):
#         return cast_iterable_to_arraylist(value)
#     elif value is None:
#         return None
#     else:
#         raise ValueError(f"Unsupported type: {type(value)}")


def cast_value_py2dotnet(value):
    """
    Casts a Python value to its equivalent .NET type.

    This function recursively casts all nested elements in dictionaries and iterables to their
    corresponding .NET types.

    - does not work if Dictionary requires an explicity type

    Args:
        value: The Python value to be cast.

    Returns:
        The equivalent .NET type.

    """

    # HELPER: Cast iterable to .NET ArrayList
    def cast_iterable_to_arraylist(iterable):
        result = ArrayList()
        for item in iterable:
            if isinstance(item, (list, tuple, set)):
                result.Add(cast_iterable_to_arraylist(item))
            else:
                result.Add(cast_value_py2dotnet(item))
        return result

    # LOGIC: Handle different Python types and cast to .NET types
    if isinstance(value, int):
        # Cast Python int to .NET Int32
        return System.Int32(value)
    elif isinstance(value, float):
        # Cast Python float to .NET Double
        return System.Double(value)
    elif isinstance(value, str):
        # Cast Python str to .NET String
        return System.String(value)
    elif isinstance(value, bool):
        # Cast Python bool to .NET Boolean
        return System.Boolean(value)
    elif isinstance(value, dict):
        # Cast Python dict to .NET Dictionary
        dotnet_dict = Dictionary[Object, Object]()
        for k, v in value.items():
            dotnet_dict[cast_value_py2dotnet(k)] = cast_value_py2dotnet(v)
        return dotnet_dict
    elif isinstance(value, (list, tuple, set)):
        # Cast Python list/tuple/set to .NET ArrayList
        return cast_iterable_to_arraylist(value)
    elif value is None:
        # Handle None as System.Object null equivalent
        return Object()  # Return a .NET null equivalent
    else:
        raise ValueError(f"Unsupported type encountered: {type(value)}, value: {value}")


def cast_value_dotnet2py(value):
    """
    Casts a .NET value to its equivalent Python type, recursively for dictionaries and lists.

    Args:
        value: A .NET value to be converted to Python type

    Returns:
        The equivalent Python type value

    Raises:
        ValueError: If the type cannot be converted
    """
    # Defensive - check if already a Python type
    if isinstance(value, (int, float, str, bool, list, tuple, dict, set, type(None))):
        return value

    # Get the full type name for more precise type checking
    type_name = str(type(value))

    # Handle Generic Dictionary specifically
    if "Dictionary" in type_name:
        try:
            return {
                cast_value_dotnet2py(k): cast_value_dotnet2py(v)
                for k, v in value.GetEnumerator()
            }
        except AttributeError:
            # Fallback to items() if GetEnumerator doesn't work
            return {
                cast_value_dotnet2py(k): cast_value_dotnet2py(v)
                for k, v in value.items()
            }

    # Cast specific .NET types to Python equivalents
    if isinstance(value, (System.Double, System.Single, System.Decimal)):
        return float(value)
    elif isinstance(value, (System.Int32, System.Int64)):
        return float(value)  # Cast to float as requested
    elif isinstance(value, System.String):
        return str(value)
    elif isinstance(value, System.Boolean):
        return bool(value)

    # Handling .NET ArrayList
    if isinstance(value, System.Collections.ArrayList):
        return [cast_value_dotnet2py(item) for item in value]

    # Handling .NET Array separately
    if isinstance(value, System.Array):
        return cast_array_dotnet2np(value)

    if value is None:
        return None

    # If all else fails, attempt to cast unknown types to float
    try:
        return float(value)
    except:
        raise ValueError(f"Unsupported type: {type_name}, {value}")


# def cast_value_dotnet2py(value):
#     """
#     Casts a .NET value to its equivalent Python type, recursively for dictionaries and lists.
#     """
#     # Defensive - check if already a Python type
#     if isinstance(value, (int, float, str, bool, list, tuple, dict, set, type(None))):
#         return value

#     # Cast specific .NET types to Python equivalents
#     if isinstance(value, (System.Double, System.Single, System.Decimal)):
#         return float(value)
#     elif isinstance(value, (System.Int32, System.Int64)):
#         return float(value)  # Cast to float as requested
#     elif isinstance(value, System.String):
#         return str(value)
#     elif isinstance(value, System.Boolean):
#         return bool(value)

#     # Recursive portion for iterable dtypes
#     if isinstance(value, System.Collections.Generic.Dictionary):
#         return {
#             cast_value_dotnet2py(k): cast_value_dotnet2py(v) for k, v in value.items()
#         }
#     # Handling .NET ArrayList
#     elif isinstance(value, System.Collections.ArrayList):
#         return [cast_value_dotnet2py(item) for item in value]
#     # Handling .NET Array separately
#     elif isinstance(value, System.Array):
#         return cast_array_dotnet2np(value)

#     elif value is None:
#         return None

#     # If all else fails, attempt to cast unknown types to float
#     try:
#         return float(value)
#     except:
#         raise ValueError(f"Unsupported type: {type(value)}, {value}")


# def cast_value_dotnet2py(value):
#     """
#     Casts a .NET value to its equivalent Python type.

#     This function recursively casts all nested elements in dictionaries and lists to their
#     corresponding Python types.

#     Args:
#         value: The .NET value to be cast.

#     Returns:
#         The equivalent Python type.

#     Raises:
#         ValueError: If an unsupported type is encountered.
#     """
#     # Defensive - check if already python type
#     if isinstance(value, (int, float, str, bool, list, tuple, dict, set, type(None))):
#         return value

#     # Cast
#     if isinstance(value, (System.Double, System.Single, System.Decimal)):
#         return float(value)
#     elif isinstance(value, (System.Int32, System.Int64)):
#         return float(value)  # Cast to float as requested
#     elif isinstance(value, System.String):
#         return str(value)
#     elif isinstance(value, System.Boolean):
#         return bool(value)
#     elif isinstance(value, System.Collections.Generic.Dictionary):
#         return {
#             cast_value_dotnet2py(k): cast_value_dotnet2py(v) for k, v in value.items()
#         }
#     elif isinstance(value, System.Collections.ArrayList):
#         return [cast_value_dotnet2py(item) for item in value]
#     elif isinstance(value, System.Array):
#         return cast_array_dotnet2np(value)

#     elif value is None:
#         return None
#     else:
#         try:
#             return float(value)  # Attempt to cast unknown types to float
#         except:
#             raise ValueError(f"Unsupported type: {type(value)}, {value}")


def cast_array_dotnet2np(netArray: System.Array) -> np.ndarray:
    """
    Converts a .NET array to a NumPy array.

    Raise:
        ValueError: if data copy fails
        AttributeError: if NumPy array type is not supported
    """
    # Define type mappings
    _MAP_NET_NP = {
        "Single": np.float32,
        "Double": np.float64,
        "SByte": np.int8,
        "Int16": np.int16,
        "Int32": np.int32,
        "Int64": np.int64,
        "Byte": np.uint8,
        "UInt16": np.uint16,
        "UInt32": np.uint32,
        "UInt64": np.uint64,
        "Boolean": np.bool_,
    }

    # Get array dimensions and type
    dims = tuple(netArray.GetLength(i) for i in range(netArray.Rank))
    netType = netArray.GetType().GetElementType().Name

    # Create empty NumPy array with correct type and shape
    try:
        npArray = np.empty(dims, order="C", dtype=_MAP_NET_NP[netType])
    except KeyError:
        raise AttributeError(f"asNumpyArray does not support System type {netType}")

    # Copy data from .NET array to NumPy array
    sourceHandle = GCHandle.Alloc(netArray, GCHandleType.Pinned)
    try:
        sourcePtr = sourceHandle.AddrOfPinnedObject().ToInt64()
        destPtr = npArray.__array_interface__["data"][0]
        ctypes.memmove(destPtr, sourcePtr, npArray.nbytes)
    except Exception as e:
        raise ValueError(f"Could not copy data from.NET array to NumPy array: {e}")

    finally:
        sourceHandle.Free()

    return npArray


def cast_array_np2dotnet(npArray: np.ndarray) -> System.Array:
    """
    Converts a NumPy array to a .NET array.

    Raise:
        ValueError: if data copy fails
        AttributeError: if NumPy array type is not supported
    """
    # Define type mappings
    _MAP_NP_NET = {
        np.dtype(np.float32): System.Single,  # type: ignore
        np.dtype(np.float64): System.Double,  # type: ignore
        np.dtype(np.int8): System.SByte,  # type: ignore
        np.dtype(np.int16): System.Int16,  # type: ignore
        np.dtype(np.int32): System.Int32,  # type: ignore
        np.dtype(np.int64): System.Int64,  # type: ignore
        np.dtype(np.uint8): System.Byte,  # type: ignore
        np.dtype(np.uint16): System.UInt16,  # type: ignore
        np.dtype(np.uint32): System.UInt32,  # type: ignore
        np.dtype(np.uint64): System.UInt64,  # type: ignore
        np.dtype(np.bool_): System.Boolean,  # type: ignore
    }

    dtype = npArray.dtype
    dims = npArray.shape

    # Handle complex number arrays
    if dtype in (np.complex64, np.complex128):
        dtype = np.float32 if dtype == np.complex64 else np.float64
        dims += (2,)
        npArray = npArray.view(dtype).reshape(dims)

    # Ensure array is C-contiguous
    if not npArray.flags.c_contiguous:
        npArray = np.ascontiguousarray(npArray)

    # Create .NET array with correct type and shape
    try:
        netArray = Array.CreateInstance(_MAP_NP_NET[dtype], *dims)  # type: ignore
    except KeyError:
        raise AttributeError(f"asNetArray does not support dtype {dtype}")

    # Copy data from NumPy array to .NET array
    destHandle = GCHandle.Alloc(netArray, GCHandleType.Pinned)
    try:
        sourcePtr = npArray.__array_interface__["data"][0]
        destPtr = destHandle.AddrOfPinnedObject().ToInt64()
        ctypes.memmove(destPtr, sourcePtr, npArray.nbytes)
    except Exception as e:
        raise ValueError(f"Could not copy data from NumPy array to.NET array: {e}")
    finally:
        destHandle.Free()

    return netArray


def handle_exceptions(
    func=None,
    *,
    return_as_self=False,
    return_as_list=False,
    return_as_dict=False,
    return_as_none=True,
):
    """
    Decorator function to handle exceptions in a function or method
    Logs as error

    Handles:
        ValueError, TypeError, KeyError and Exception

    Returns:
        self if return_as_self is True, otherwise returns None.
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):

            # Helper Functions

            def handle_return_type():
                """
                Handles return dtype based on ordered bools from input signature
                """
                if return_as_self:
                    return args[0]
                if return_as_list:
                    return []
                if return_as_dict:
                    return {}
                elif return_as_none:
                    return None

            def get_func_name(args):
                """Generates string of class and function name"""

                class_name = None
                if len(args) > 0 and hasattr(args[0], "__class__"):
                    class_name = args[0].__class__.__name__

                func_name = (
                    f"{class_name}.{func.__name__}()"
                    if class_name
                    else f"{func.__name__}()"
                )

                return func_name

            # func_name = get_func_name(args)
            # Get the filename and line number of the calling module
            caller_frame = inspect.stack()[1]
            caller_filename = caller_frame.filename
            caller_lineno = caller_frame.lineno
            func_name = f"{get_func_name(args)} "
            func_source = f"({caller_filename}:{caller_lineno})"

            try:
                return func(*args, **kwargs)

            except ValueError as e:
                logging.error(f"ValueError for {func_name} - {e} - {func_source}")
                return handle_return_type()

            except TypeError as e:
                logging.error(f"TypeError for {func_name} - {e}- {func_source}")
                return handle_return_type()

            except KeyError as e:
                logging.error(f"KeyError for {func_name} - {e}- {func_source}")
                return handle_return_type()

            except AttributeError as e:
                logging.error(f"AttributeError for {func_name} - {e}- {func_source}")
                return handle_return_type()

            except Exception as e:
                logging.error(f"Exception for {func_name} - {e}- {func_source}")
                return handle_return_type()

        return wrapper

    if func is None:
        return decorator
    else:
        return decorator(func)


def get_function_name():
    """Returns the name of the current function"""
    frame = inspect.currentframe().f_back
    class_name = frame.f_locals.get("self", "").__class__.__name__
    name = f"{class_name}.{frame.f_code.co_name}()"
    return name


def get_project_root():
    # Traverse up until you find the project root marker (e.g., .git directory, or another unique marker)
    current_file_path = Path(__file__).parent
    project_root = current_file_path
    while project_root.parent != project_root and not (project_root / ".git").exists():
        project_root = project_root.parent
    return project_root


# Pickle the object
def encode(obj):
    return codecs.encode(pickle.dumps(obj), "base64").decode()


# Unpickle the object
def decode(obj):
    return pickle.loads(codecs.decode(obj.encode(), "base64"))


class ConfigReader:
    def __init__(self) -> None:
        # Read configuration file
        self._config = configparser.ConfigParser()
        self._config.read("config/aleph.ini")

    @property
    def cfg(self):
        if not self._config.sections():
            self._config.read("config/aleph.ini")
        return self._config


def validate_range(efficiency: float) -> float:
    """Ensures the efficiency is within bounds [0,1].

    Returns
    ---------
    float
        efficiency value if between 0-1 else 0
    """
    # DEFENSIVE
    if efficiency < 0 or efficiency > 1:
        logging.error(
            f"{get_function_name()} - Efficiency must be between 0 and 1. "
            f"Received: {efficiency}. Setting to None"
        )
        efficiency = None

    return efficiency


def validate_float(value) -> Optional[float]:
    """Validate that the input can be converted to a float.

    Parameters:
    value: The input value to be validated and converted to float.

    Returns:
    float: The validated and converted float value.

    Raises:
    ValueError: If the input value cannot be converted to float.
    """
    try:
        # Attempt to convert the input value to a float.
        return float(value)
    except:
        # Log the error and raise a ValueError if conversion fails.
        logging.warning(f"Invalid input: {value}. Must be a float.")
        return None


def format_attributes(obj) -> str:
    # Dynamically build the attributes string from __dict__
    attributes_str = "\n".join(
        [f"\t{key}: {repr(value)}" for key, value in obj.__dict__.items()]
    )
    return f"{obj.__class__.__name__}(\n{attributes_str}\n)"


def create_absolute_path_object(relative_path: str) -> Path:
    """
    Given a relative path from project root, reutrns a path object that is absolute to environment.
    Assumes presence of "PROJECT_ROOT" environment variable

    Arguments:
        relative_path: string of path. It can be to a directory or file. e.g. "backend/data/temp.csv"

    Returns:
        Path object that is absolute to environment
    """
    project_path = Path(os.getenv("PROJECT_ROOT"))
    if not project_path:
        logging.error(
            "create_absolute_path_object() - PROJECT_ROOT env variable not found"
        )
        return Path(relative_path)

    return project_path / relative_path


def cleanup_folder_by_keyword(folder: Path, filename_keyword: str) -> None:
    """
    Cleans up all files in the artefact folder that match the model_id.

    Args:
        artefact_folder (Path): The folder where artefacts are stored.
        model_id (str): The model ID to match for file deletion.
    """
    files_to_delete = [
        file
        for file in folder.iterdir()
        if file.is_file() and filename_keyword in file.name
    ]
    for file in files_to_delete:
        file.unlink()
        logging.info(f"Deleted file: {file}")
