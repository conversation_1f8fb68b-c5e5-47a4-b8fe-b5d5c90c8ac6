import pprint
import pandas as pd
import numpy as np
from skopt import sampler
from skopt.space import Space

#############################################################
# Generate values given the hyperspace
#############################################################
def generate_raw_data(ls_dimensions, n_samples, column_names, **kwargs):
    lhs_type = kwargs.get('lhs_type', 'centered')
    criterion = kwargs.get('criterion', 'maximin')
    iterations = kwargs.get('iterations', 1000)
    random_state = kwargs.get('random_state', 1234)
    # Generate list of dimensions with tuples in the form (lower bound, upper bound)
    current_space = Space(ls_dimensions)

    # Generate samples based on the given updated list of dimensions
    current_lhs = sampler.Lhs(lhs_type=lhs_type, criterion=criterion, iterations=iterations)
    current_samples = current_lhs.generate(dimensions=current_space, n_samples=n_samples, random_state=random_state)
    df_raw_samples = pd.DataFrame(current_samples, columns=column_names)

    return df_raw_samples

# df_samples = generate_raw_data(ls_dimensions=[(900.0, 1100.0), (285.0, 300.0), (800., 900.0),
#                                               (900.0, 1100.0), (285.0, 300.0), (16000.0, 18000.0),
#                                               ],
#                                     n_samples=1000, column_names=['P-001','T-001','F-001',
#                                                                  'P-002', 'T-002', 'F-002',
#                                                                  ])
# df_samples.to_csv('Demo_Data_Before.csv')

#############################################################
# Generate values given the hyperspace
#############################################################
def generate_samples(ls_dimensions, n_samples, **kwargs):
    lhs_type = kwargs.get('lhs_type', 'centered')
    criterion = kwargs.get('criterion', 'maximin')
    iterations = kwargs.get('iterations', 1000)
    random_state = kwargs.get('random_state', 1234)

    # Generate list of dimensions with tuples in the form (lower bound, upper bound)
    split_ls_dimensions = []
    index_ls_dimensions = []
    count_original_index = 0
    count_running_index = 0
    for ele in ls_dimensions:
        # If the given element is already in the required form
        if not isinstance(ele[0], tuple):
            split_ls_dimensions.insert(count_running_index, ele)
            index_ls_dimensions.insert(count_running_index, (0, count_original_index, count_running_index))
            count_original_index = count_original_index + 1
            count_running_index = count_running_index + 1
        # If the given element is not in the required form e.g. curve parameters
        else:
            for num_sub_ele in range(len(ele[0])):
                split_ls_dimensions.insert(count_running_index, (ele[0][num_sub_ele], ele[1][num_sub_ele]))
                index_ls_dimensions.insert(count_running_index, (1, count_original_index, count_running_index))
                count_running_index = count_running_index + 1

            count_original_index = count_original_index + 1

    # Generate samples based on the given updated list of dimensions
    current_lhs = sampler.Lhs(lhs_type=lhs_type, criterion=criterion, iterations=iterations)
    current_samples = current_lhs.generate(dimensions=split_ls_dimensions, n_samples=n_samples, random_state=random_state)

    # Rearrange the samples as per the original sequence in the ls_dimensions
    generated_samples = np.empty(shape=(n_samples, len(ls_dimensions)), dtype=object)
    for num_sample in range(n_samples):
        for ele in index_ls_dimensions:
            if ele[0] == 0:
                generated_samples[num_sample][ele[1]] = current_samples[num_sample][ele[2]]
            elif ele[0] == 1:
                if generated_samples[num_sample][ele[1]]:
                    generated_samples[num_sample][ele[1]].append(current_samples[num_sample][ele[2]])
                else:
                    generated_samples[num_sample][ele[1]] = [current_samples[num_sample][ele[2]]]

    return generated_samples