from sympy import sympify
import pandas as pd
import numpy as np


#############################################################
# Build KPI block for the Analysis
#############################################################
class KPI:
    #############################################################
    # Initialize the class
    #############################################################
    def __init__(self, str_condition_expression: str):
        self.str_condition_expression = str_condition_expression
        self.condition_object = sympify(self.str_condition_expression)
        self.set_free_symbols = self.condition_object.free_symbols
        self.comprised_instruments = set(
            [str(ele) for ele in list(self.set_free_symbols)]
        )
        self.comprised_vars = list(self.comprised_instruments)

    #############################################################
    # Compute KPI value for the given variables
    #############################################################
    def get_kpi_values(self, var_val: np.ndarray) -> np.ndarray:
        # Run through the samples to compute KPI values
        K_samples = var_val.shape[0]
        if var_val.ndim == 1:
            kpi_vals = []
            for num_ele in range(int(K_samples)):
                current_var_val = dict(
                    zip(self.comprised_instruments, [var_val[num_ele]])
                )
                kpi_val = self.condition_object.evalf(subs=current_var_val)
                kpi_vals.insert(num_ele, kpi_val)

        else:
            kpi_vals = []
            for num_ele in range(int(K_samples)):
                current_var_val = dict(
                    zip(self.comprised_instruments, list(var_val[num_ele]))
                )
                kpi_val = self.condition_object.evalf(subs=current_var_val)
                kpi_vals.insert(num_ele, kpi_val)

        kpi_values = np.array(kpi_vals)

        return kpi_values


#############################################################
# Build Condition for Status of the Model
#############################################################
class CalculatedCondition:
    #############################################################
    # Initialize the class
    #############################################################
    def __init__(self, str_condition_expression, lb_condition, ub_condition):
        self.str_condition_expression = str_condition_expression
        self.condition_object = sympify(self.str_condition_expression)
        self.lb_condition = lb_condition
        self.ub_condition = ub_condition
        self.set_free_symbols = self.condition_object.free_symbols
        self.comprised_instruments = set(
            [str(ele) for ele in list(self.set_free_symbols)]
        )

    #############################################################
    # Generate the condition status based on the instrument attributes a given data point
    #############################################################
    def get_condition_status(self, dict_comprised_instruments_val):
        #############################################################
        # Evaluate the expression using the specified input dictionary and return status.
        # Status value 1 means online system and 0 means offline system.
        #############################################################
        condition = self.condition_object.evalf(subs=dict_comprised_instruments_val)
        if condition <= self.ub_condition and condition >= self.lb_condition:
            condition_status = 1
        else:
            condition_status = 0

        return condition_status

    #############################################################
    # Generate the condition status based on the instrument attributes given the dataframe
    #############################################################
    def generate_condition_status(self, df):
        #############################################################
        df["model_running_status"] = 0
        df_headers = set(df.columns)
        symbol_check = self.comprised_instruments.issubset(df_headers)
        print("symbol_check", symbol_check)
        if not symbol_check:
            raise Exception(
                "One or more headers in the specified condition are not in the dataframe!"
            )
            exit()
        #############################################################
        # Run through all the samples
        #############################################################
        df_to_dict = df.to_dict(orient="records")
        for num_sample in range(df.shape[0]):
            #############################################################
            # Get input as dictionary
            #############################################################
            df.loc[num_sample, "model_running_status"] = self.get_condition_status(
                dict_comprised_instruments_val=df_to_dict[num_sample]
            )
        return df


if __name__ == "__main__":
    test_kpi = KPI(str_condition_expression="FI001*TI002")
    test_var_val = np.array([[100.0, 20.0], [200.0, 30.0]])
    kpi_values = test_kpi.get_kpi_values(var_val=test_var_val)
    print(kpi_values)
