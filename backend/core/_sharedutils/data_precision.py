import logging
from typing import List
import numpy as np
import pandas as pd


def set_array_precision(data_array: np.ndarray, precision: int) -> np.ndarray:
    """
    Set the data precision for the given array.

    Args:
        data_array (array): Numpy array that needs to be rounded down.
        precision (int): Number of significant digits for precision.

    Returns:
        rounded_array (array): Numpy array that is rounded down for the specified precision.
    """
    rounded_array = np.round(data_array, precision)
    logging.info(
        f"The given array is successfully rounded down to {precision} significant digits."
    )

    return rounded_array


def set_list_precision(data_list: List[float], precision: int) -> List[float]:
    """
    Set the data precision for the given list.

    Args:
        data_list (list): List that needs to be rounded down.
        precision (int): Number of significant digits for precision.

    Returns:
        rounded_list (list): List that is rounded down for the specified precision.
    """
    rounded_list = [round(ele, precision) for ele in data_list]
    logging.info(
        f"The given list is successfully rounded down to {precision} significant digits."
    )

    return rounded_list


def set_dataframe_precision(dataframe: pd.DataFrame, precision: int) -> pd.DataFrame:
    """
    Set the data precision for the given dataframe.

    Args:
        dataframe (dataframe): Dataframe that needs to be rounded down.
        precision (int): Number of significant digits for precision.

    Returns:
        rounded_dataframe (dataframe): Dataframe that is rounded down for the specified significant digits.
    """
    rounded_dataframe = dataframe.round(precision)
    logging.info(
        f"The given dataframe is successfully rounded down to {precision} significant digits."
    )

    return rounded_dataframe


def set_dataseries_precision(dataseries: pd.Series, precision: int) -> pd.Series:
    """
    Set the data precision for the given series.

    Args:
        dataseries (dataseries): Dataseries that needs to be rounded down.
        precision (int): Number of significant digits for precision.

    Returns:
        rounded_dataseries (dataseries): Dataseries that is rounded down for the specified significant digits.
    """
    rounded_dataseries = dataseries.round(precision)
    logging.info(
        f"The given dataseries is successfully rounded down to {precision} significant digits."
    )

    return rounded_dataseries
