import logging
import os

from python_logging_rabbitmq import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from backend.core.message_broker import enums
from backend.core._sharedutils import Utilities


class AlephLogger:
    def __init__(self) -> None:
        conf = Utilities.ConfigReader()
        level = conf.cfg["LOGGING"]["level"]

        if level not in logging._nameToLevel:
            level = "WARNING"

        self.logger = logging.getLogger("aleph")
        self.logger.setLevel(level)

        self.log_handler = RabbitMQHandler(
            host=os.environ["RABBITMQ_HOSTNAME"],
            username=os.environ["RABBITMQ_DEFAULT_USER"],
            password=os.environ["RABBITMQ_DEFAULT_PASS"],
            exchange=enums.ExchangeName.TOPIC.value,
            heartbeat=0,
        )
        self.logger.addHandler(self.log_handler)

    def log(self):
        return self.logger
