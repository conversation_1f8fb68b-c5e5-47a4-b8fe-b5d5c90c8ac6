import os
from pathlib import Path


def find_project_root(current_path: Path) -> Path:
    """
    Recursively traverse up in the directory structure to find the project root.
    Assumes that the project root contains a marker such as a '.git' directory or a specific file.
    """
    # Define the marker that signifies the root of your project
    project_marker = (
        ".git"  # Common marker if using Git, or use any specific file or directory
    )

    # Traverse up until you find the project marker
    for parent in current_path.parents:
        if any(
            (parent / project_marker).exists()
            for project_marker in [project_marker, "pyproject.toml", "setup.py"]
        ):
            return parent
    return current_path  # Default to current path if no marker is found


def set_project_root():
    # Set the environment variable
    project_root = find_project_root(Path(__file__))
    os.environ["PROJECT_ROOT"] = str(project_root)

    print(f"Set PROJECT_ROOT to {os.environ['PROJECT_ROOT']}")
