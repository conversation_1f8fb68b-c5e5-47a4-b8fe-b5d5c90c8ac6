import numpy as np
#########################################################
# Build the Cubic Polynomial Model for a given data set
#########################################################
def build_quad_poly(X, Y):
    X_Train = np.array(X)
    Y_Train = np.array(Y)
    theta = np.polyfit(X_Train, Y_Train, deg=2)

    return theta

#########################################################
# Evaluate the Cubic Polynomial Model for a given data point
#########################################################
def evaluate_quad_poly(theta, X_Query):
    Y_Query = np.polyval(theta, X_Query)

    return Y_Query

#########################################################
# Build the Cubic Polynomial Model for a given data set
#########################################################
def build_cubic_poly(X, Y):
    X_Train = np.array(X)
    Y_Train = np.array(Y)
    theta = np.polyfit(X_Train, Y_Train, deg=3)

    return theta

#########################################################
# Evaluate the Cubic Polynomial Model for a given data point
#########################################################
def evaluate_cubic_poly(theta, X_Query):
    Y_Query = np.polyval(theta, X_Query)

    return Y_Query

# #############################################################
# # This is a testing block for assessing the script in a stand-alone mode
# #############################################################
# if __name__ == '__main__':
#     X = [0, 1, 2, 3]
#     Y = [0, 1, 8, 27]
#     theta = build_cubic_poly(X, Y)
#     print('Theta: ', theta)
#     X_Query = [2.5, 3.5]
#     Y_Query = evaluate_cubic_poly(theta, X_Query)
#     print('Y_Query: ', Y_Query)
