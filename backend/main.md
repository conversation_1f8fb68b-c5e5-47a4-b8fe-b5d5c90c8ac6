```mermaid
sequenceDiagram
    participant M as main.py
    participant <PERSON><PERSON> as ConfigManager
    participant BC as BaseConfig
    participant Configs as Specific Configs

    M->>CM: ConfigManager.load_profile()
    CM->>BC: Check PROFILE env var
    Note over BC: Determines dev/prod/experimental
    BC->>Configs: Load appropriate config
    Configs-->>CM: Return configs
    CM-->>M: Return complete config

    M->>M: Create FastAPI app with config
    M->>M: Initialize UOW with DB config
    M->>M: Load templates
    M->>M: Register routers```
```