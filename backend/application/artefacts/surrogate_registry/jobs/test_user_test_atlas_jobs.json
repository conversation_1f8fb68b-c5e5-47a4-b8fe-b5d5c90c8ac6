{"uid": "991d0459-3e81-4649-a1a4-31c1505ecf71", "metadata": {"uid": "58753fd0-766f-4921-bd61-69a5d82bd9d1", "label": "test_model_fast", "user_reference": "test_user", "atlas_reference": "test_atlas", "surrogate_algo": "rnn_ts", "description": "Test RNN surrogate model with fast configuration for automated testing", "created_at": "2025-06-23T13:43:53.754374+00:00", "updated_at": null, "tags": {}}, "training_configuration": {"primary_metric": "rmse", "max_iterations": 5, "random_seed": 42, "metric_thresholds": {}, "validation_strategy": "simple_split", "validation_split": 0.2, "cv_folds": null, "enable_early_stopping": true, "early_stopping_rounds": 2, "early_stopping_min_delta": 0.01, "ts_validation_window_size": 5, "ts_validation_stride": 1, "ts_forecast_horizon": 2}, "model_configuration": {"algorithm": "rnn_ts", "parameters": [{"name": "hidden_size", "value": 8, "tunable": false, "bounds": {"type": "integer", "min_value": 8.0, "max_value": 16.0, "choices": []}}, {"name": "seq_num_layers", "value": 1, "tunable": false, "bounds": {"type": "integer", "min_value": 1.0, "max_value": 1.0, "choices": []}}, {"name": "dense_num_layers", "value": 1, "tunable": false, "bounds": {"type": "integer", "min_value": 1.0, "max_value": 1.0, "choices": []}}, {"name": "rnn_type", "value": "LSTM", "tunable": false, "bounds": {"type": "categorical", "min_value": 0.0, "max_value": 0.0, "choices": ["LSTM"]}}, {"name": "dropout", "value": 0.0, "tunable": false, "bounds": {"type": "continuous", "min_value": 0.0, "max_value": 0.1, "choices": []}}, {"name": "batch_size", "value": 16, "tunable": false, "bounds": {"type": "categorical", "min_value": 0.0, "max_value": 0.0, "choices": [16]}}, {"name": "learning_rate", "value": 0.01, "tunable": false, "bounds": {"type": "continuous", "min_value": 0.01, "max_value": 0.01, "choices": []}}, {"name": "loss_function", "value": "mse", "tunable": false, "bounds": {"type": "categorical", "min_value": 0.0, "max_value": 0.0, "choices": ["mse"]}}]}, "hpo_configuration": {"is_enable": true, "n_trials": 3, "direction": null, "sampler": "random", "pruner": "none", "timeout_seconds": 30, "n_parallel_jobs": 1}, "training_data_uid": null, "hpo_results": null, "hpo_best_metrics": null, "epoch_metrics": [], "results": null, "status": "Training: Starting training", "early_stopping_triggered": false, "runtime_seconds": 0, "error_message": null, "environment_info": {}}