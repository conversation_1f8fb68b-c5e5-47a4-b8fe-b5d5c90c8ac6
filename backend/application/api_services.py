import logging
import uuid
import typing as T
from typing import Dict, Tuple, List, Optional
from datetime import timezone, datetime
from collections import defaultdict
import numpy as np
import pandas as pd
from random import sample
import math
import inspect
import base64
from io import StringIO

# Internal
from backend.application.config import AdaptorFactory
import backend.endpoint.v1.schema as v1endpt
import backend.core as core

logger = logging.getLogger(__name__)


class APIService:
    RESPONSE_MODULES = ["metis"]

    def __init__(self):
        adaptor_factory = AdaptorFactory()
        self.logger: core.ILogging = adaptor_factory.get_adaptor(core.ILogging.__name__)
        self.logger.setup()
        self.iatlas: core.IAtlas = adaptor_factory.get_adaptor(core.IAtlas.__name__)
        self.imatrix: core.IMatrix = adaptor_factory.get_adaptor(core.IMatrix.__name__)
        self.imetis: core.IMetis = adaptor_factory.get_adaptor(core.IMetis.__name__)
        self.iathena: core.IAthena= adaptor_factory.get_adaptor(core.IAthena.__name__)
        self.isurrogate: core.ISurrogate = adaptor_factory.get_adaptor(core.ISurrogate.__name__)
        self.ioptimizer: core.IOptimizer = adaptor_factory.get_adaptor(core.IOptimizer.__name__)


    #############################

    # CONFIG PAGE

    def get_atlas_w_metadata(
        self, config_name: str, user_id: str
    ) -> Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]:
        """Returns an atlas object and the metadata associated with it"""

        res = self.iatlas.get(config_name, user_id)

        return res

    def get_plant_configuration(
        self, config_name: str, user_id: str
    ) -> v1endpt.PlantConfiguration:
        """Get existing plant configuration"""
        model, meta = self.get_atlas_w_metadata(config_name, user_id)

        return v1endpt.PlantConfiguration.hydrate_schema(model, meta.is_template)

    def update_plant_configuration(
        self, config_name: str, config: v1endpt.PlantConfiguration, user_id: str
    ) -> None:
        """Update existing plant configuration"""
        atlas_ref, meta = self.get_atlas_w_metadata(config_name, user_id)

        # Distinguish if industry and engine has changed
        updated_user_industry = v1endpt.config_dtos.EnumMap.str2enum[config.user_industry]
        updated_engine = v1endpt.config_dtos.EnumMap.str2enum[config.matrix_engine]
        if (atlas_ref.user_config.user_industry!= updated_user_industry or atlas_ref.user_config.matrix_engine!= updated_engine):
            self.iatlas.delete(config_name, user_id)

            new_user_config = core.at.UserAtlasConfig(
                user_industry=updated_user_industry,
                matrix_engine=updated_engine,
            )
            new_atlas = core.at.AtlasRoot(
                atlas_ref.label,
                atlas_ref.plant_id,
                new_user_config,
            )
            meta_updated = core.at.AtlasMetadata(
                user_id=meta.user_id,
                date_created=meta.date_created,
                date_modified=datetime.now(timezone.utc),
                is_template=meta.is_template,
                atlas_label=meta.atlas_label,
            )

            self.iatlas.save(new_atlas, meta_updated)
            return

        atlas = v1endpt.PlantConfiguration.bootstrap_atlas(config, atlas_ref)

        meta_updated = core.at.AtlasMetadata(
            user_id=meta.user_id,
            date_created=meta.date_created,
            date_modified=datetime.now(timezone.utc),
            is_template=meta.is_template,
            atlas_label=meta.atlas_label,
        )

        self.iatlas.save(atlas, meta_updated)

    def get_plant_configurations(
        self, user_id: str
    ) -> List[v1endpt.PlantConfig_Metadata]:
        """Get metadata for all plant configurations for a user"""

        results = []
        logging.info("hit get_plant_configurations")

        # Get user files
        for model, meta in self.iatlas.list(
            {
                core.AltasMetadataEnums.USER_ID.value: user_id,
                core.AltasMetadataEnums.IS_TEMPLATE.value: False,
                core.AltasMetadataEnums.NOT_DELETED.value: True,
            }
        ):
            results.append(
                v1endpt.PlantConfig_Metadata(
                    name=model.label,
                    createdDateTime=meta.date_created,
                    editedDateTime=meta.date_modified,
                    is_template=meta.is_template,
                )
            )

        # Get global templates
        for model, meta in self.iatlas.list(
            {
                core.AltasMetadataEnums.IS_TEMPLATE.value: True,
                core.AltasMetadataEnums.NOT_DELETED.value: True,
            }
        ):
            results.append(
                v1endpt.PlantConfig_Metadata(
                    name=model.label,
                    createdDateTime=meta.date_created,
                    editedDateTime=meta.date_modified,
                    is_template=meta.is_template,
                )
            )

        return results

    def create_plant_configuration(
        self, metadata: v1endpt.PlantConfig_Metadata, user_id: str
    ) -> None:
        """Create empty plant configuration"""

        # Check if name already exists
        model_names = [model.name for model in self.get_plant_configurations(user_id)]
        if metadata.name in model_names:
            raise core.AtlasLookupError(
                "Model name conflict",
                metadata.name,
                "Model name already exists. Choose Another.",
            )

        atlas = core.at.AtlasRoot(label=metadata.name, plant_id=user_id)
        meta = core.at.AtlasMetadata(
            user_id=user_id,
            date_created=datetime.now(timezone.utc),
            date_modified=datetime.now(timezone.utc),
            is_template=metadata.is_template,
            atlas_label=atlas.label,
        )

        # TODO validate if should use `create` or `save`
        self.iatlas.create(atlas, meta)

    def delete_plant_configuration(self, config_name: str, user_id: str) -> None:
        """Delete a plant configuration"""
        self.iatlas.delete(config_name, user_id)

    def get_kpi_variables(
        self, config_name: str, user_id: str
    ) -> List[v1endpt.KPIVariableItem]:
        """Get KPI variables for a plant configuration"""
        atlas, meta = self.get_atlas_w_metadata(config_name, user_id)

        result = []
        for var in atlas.variables_collection.items:
            if core.at.VariableUIDCollection.is_kpi(var):
                kpi_label = core.at.KPI.get_kpi_label(var)
                result.append(v1endpt.KPIVariableItem.hydrate_schema(var, kpi_label))

        return result

    def get_kpis(self, config_name: str, user_id: str) -> List[v1endpt.KPIItem]:
        """Get KPIs defined for a plant configuration"""
        atlas, meta = self.get_atlas_w_metadata(config_name, user_id)
        return [
            v1endpt.KPIItem.hydrate_schema(kpi, atlas)
            for kpi in atlas.kpi_collection.items
        ]

    def update_kpis(
        self, config_name: str, kpis: List[v1endpt.KPIItem], user_id: str
    ) -> None:
        """Update KPI definitions for a plant configuration"""
        atlas, meta = self.get_atlas_w_metadata(config_name, user_id)
        updated_kpis = [
            kpi_item.bootstrap_obj(atlas.variables_collection) for kpi_item in kpis
        ]
        atlas.kpi_collection.sync_collection(updated_kpis)

        meta_updated = core.at.AtlasMetadata(
            user_id=meta.user_id,
            date_created=meta.date_created,
            date_modified=datetime.now(timezone.utc),
            is_template=meta.is_template,
            atlas_label=meta.atlas_label,
        )
        self.iatlas.save(atlas, meta_updated)

    def get_sensor_mappings(
        self, config_name: str, user_id: str
    ) -> v1endpt.SensorMappingConfig:
        """Get sensor mappings for a plant configuration"""
        atlas, meta = self.get_atlas_w_metadata(config_name, user_id)
        return v1endpt.SensorMappingConfig.hydrate_schema(atlas)

    def update_sensor_mappings(
        self, config_name: str, user_id: str, mapping: v1endpt.SensorMappingConfig
    ):
        """Update sensor mappings for a plant configuration"""
        atlas, meta = self.get_atlas_w_metadata(config_name, user_id)
        updated_sensors = [
            v1endpt.SensorItem.bootstrap_obj(label, var_uid)
            for label, var_uid in mapping.csvCol2varUUID.items()
        ]
        atlas.sensor_collection.sync_collection(updated_sensors)
        atlas.sensor_timestep_col = mapping.timestep_col
        atlas.sensor_timeset_col = mapping.timeset_col

        meta_updated = core.at.AtlasMetadata(
            user_id=meta.user_id,
            date_created=meta.date_created,
            date_modified=datetime.now(timezone.utc),
            is_template=meta.is_template,
            atlas_label=meta.atlas_label,
        )
        self.iatlas.save(atlas, meta_updated)

    #############################

    # PRESETS

    # SERVICES
    def get_equipment_types_and_defaults(
        self, config_name: str, user_id: str
    ) -> List[v1endpt.PresetEquipmentType]:
        """Get equipment types and their defaults"""
        atlas, _ = self.get_atlas_w_metadata(config_name, user_id)
        ITEMS = atlas.base_equipment_set.to_list()

        result: List[v1endpt.PresetEquipmentType] = []
        for item in ITEMS:
            assert inspect.isclass(item)
            result.append(v1endpt.PresetEquipmentType.hydrate_schema(item, atlas))

        return result

    def get_stream_types(
        self, config_name: str, user_id: str
    ) -> List[v1endpt.PresetStreamType]:
        """Get stream types"""
        atlas, _ = self.get_atlas_w_metadata(config_name, user_id)
        ITEMS = atlas.base_stream_set.to_list()

        result: List[v1endpt.PresetStreamType] = []
        for stream in ITEMS:
            assert inspect.isclass(stream)
            result.append(v1endpt.PresetStreamType.hydrate_schema(stream, atlas))

        return result

    def get_material_types(
        self, config_name: str, user_id: str
    ) -> v1endpt.PresetMaterialTypes:
        """Get material types"""

        # Dummy atlas to get labels
        compound_collection = core.at.CompoundUIDCollection
        atlas, _ = self.get_atlas_w_metadata(config_name, user_id)
        ITEMS = atlas.base_compound_set.to_list()

        result = []
        for c in ITEMS:
            assert isinstance(c, core.at.VOCompound)
            name = compound_collection.get_ui_label(c)
            cas_number = c.id
            result.append(v1endpt.PresetMaterialType(name=name, cas_number=cas_number))
        return v1endpt.PresetMaterialTypes(items=result)

    def get_sensor_types(self) -> v1endpt.PresetSensorTypes:
        """Get sensor types"""
        # NOT IMPLEMENTED
        return v1endpt.PresetSensorTypes([])

    ####################################

    # Surrogate stuff
    def train_surrogate_model(
        self,
        userId: str,
        atlas_ref: str,
        training_configuration: v1endpt.SurrogateTrainingConfig,
    ):
        """Initiate async training process with proper metadata tracking"""

        # -----------------------
        # Generate training job ID
        # NOTE = we assume only RNN_TS at the moment. Mapping function needed beteen model_type and surrogate algo

        job_metadata = core.su.VOMetadata_General(
            label=f"{userId}_{atlas_ref}",
            user_reference=userId,
            atlas_reference=atlas_ref,
            surrogate_algo=core.su.EnumSurrogateAlgorithm.RNN_TS,
            description="",
        )

        # ---------------------------------
        # DATA SPLIT N TRANSFORM

        # Load
        atlas, _ = self.get_atlas_w_metadata(atlas_ref, userId)

        # Sort the vars
        col_2_varuid = training_configuration.csvCol2varUUID
        x_vars: Dict[str, uuid.UUID] = dict()
        y_vars: Dict[str, uuid.UUID] = dict()
        for col_name, var_uid in col_2_varuid.items():
            var = atlas.variables_collection.get_item(var_uid)
            if var.is_independent == True:
                x_vars[col_name] = var_uid
            else:
                y_vars[col_name] = var_uid

        # Deserialize the CSV
        # NOTE: we assume its timeseries
        # NOTE: this can become a utility function in the future NIT TODO
        timeset_col = training_configuration.timeset_col
        timestep_col = training_configuration.timestep_col
        assert timeset_col is not None
        assert timestep_col is not None
        df_x=core.su.utils.decode_and_filter_csv(
            training_configuration.training_data, 
            list(x_vars.keys()) + [timeset_col, timestep_col]
            )
        df_y=core.su.utils.decode_and_filter_csv(
            training_configuration.training_data, 
            list(y_vars.keys()) + [timeset_col, timestep_col]
            )

        # Rename columns for both DataFrames to UUIDs for vars
        rename_map = {col: str(var_uuid) for col, var_uuid in col_2_varuid.items()}
        df_x = df_x.rename(columns=rename_map)
        df_y = df_y.rename(columns=rename_map)

        # Split
        unique_timesets = df_x[training_configuration.timeset_col].unique()
        train_timesets = unique_timesets[
            : int(len(unique_timesets) * 0.8)
        ]  # 80% for training
        test_timesets = unique_timesets[
            int(len(unique_timesets) * 0.2) :
        ]  # 20% for testing
        df_x_train = df_x[df_x[training_configuration.timeset_col].isin(train_timesets)]
        df_y_train = df_y[df_y[training_configuration.timeset_col].isin(train_timesets)]
        df_x_test = df_x[df_x[training_configuration.timeset_col].isin(test_timesets)]
        df_y_test = df_y[df_y[training_configuration.timeset_col].isin(test_timesets)]

        # Transformer
        transformer = self.isurrogate.fit_datatransformer_and_save(
            metadata=job_metadata,
            df_x=df_x_train,
            df_y=df_y_train,
            col2uid=training_configuration.csvCol2varUUID,
            timeset_col=training_configuration.timeset_col,
            timestep_col=training_configuration.timestep_col,
        )
        arr_x_train, arr_y_train = transformer.transform(df_x_train, df_y_train)
        arr_x_test, arr_y_test = transformer.transform(df_x_test, df_y_test)

        # Datasets
        train_dataset = core.su.VODataset(
            arr_x=arr_x_train,
            arr_y=arr_y_train,
            transformer_uid=transformer.uid,
            colnames_x=transformer._initial_x_variable_cols,
            colnames_y=transformer._initial_y_variable_cols,
            pattern="sequence",
        )
        test_dataset = core.su.VODataset(
            arr_x=arr_x_test,
            arr_y=arr_y_test,
            transformer_uid=transformer.uid,
            colnames_x=transformer._initial_x_variable_cols,
            colnames_y=transformer._initial_y_variable_cols,
            pattern="sequence",
        )

        # ---------------------------
        # GENERATE JOB CONFIGS

        job: core.su.ENTTrainingJob = self.isurrogate.create_job(
            metadata=job_metadata, configuration="rnn_base"
        )
        self.isurrogate.save_surrogate_job(job_metadata, job)

        # NOTE - nice to do is to add callbacks for logging into the training loop
        model, job = self.isurrogate.execute_training_job(
            job=job,
            train_dataset=train_dataset,
            test_dataset=test_dataset,
            datatransformer=transformer,
        )

        self.isurrogate.save_surrogate_job(job_metadata, job)
        self.isurrogate.save_model(job_metadata, model)

        return True

    def get_surrogate_training_config(
        self,
        atlas_label: str,
        userId: str,
    ) -> v1endpt.SurrogateTrainingConfig:
        """Checks if is a training config associated with the atlas model"""
        # TODO ZL - there is no data persistence for this.
        datatransformer = self.isurrogate.get_datatransformer(userId, atlas_label)

        return v1endpt.SurrogateTrainingConfig(
            model_type="High Accuracy, Long Training",
            training_regime="imported_data",
            training_data="na",
            csvCol2varUUID=datatransformer.col2var,
            timeset_col=datatransformer._timeset_col,
            timestep_col=datatransformer._timestep_col,
        )

    def get_surrogate_training_status(
        self, atlas_label: str, userId: str
    ) -> v1endpt.Status:
        job = self.isurrogate.get_surrogate_job(userId, atlas_label)
        status = job.status

        mapping = {
            core.su.EnumTrainingStatus.PENDING.value: "Setup Required",
            core.su.EnumTrainingStatus.TRAINING.value: "In Process",
            core.su.EnumTrainingStatus.COMPLETE.value: "Ready",
            core.su.EnumTrainingStatus.EARLY_STOPPED.value: "Ready",
            core.su.EnumTrainingStatus.FAILED.value: "Setup Required",
        }

        status_remapped = "Setup Required"
        for k, v in mapping.items():
            if k in status:
                status_remapped = v

        return v1endpt.Status(status=status_remapped)  # type: ignore

    def get_surrogate_training_results(
            self,
            atlas_label: str,
            userId: str
    )-> v1endpt.SurrogateTrainingResults:
        
        # get model
        job = self.isurrogate.get_surrogate_job(userId, atlas_label)
        job_results= job.results
        job_model = job.get_model(userId, atlas_label)
        training_result =job_results.test_metrics
        
        # get testing data
        atlas, _ = self.get_atlas_w_metadata(atlas_label,userId)
        training_config =self.get_surrogate_training_config(atlas_label, userId)
        col_2_varuid = training_config.csvCol2varUUID
        x_vars : Dict[str, uuid.UUID] = dict()
        y_vars : Dict[str, uuid.UUID] = dict()
        for col_name, var_uid in col_2_varuid.items():
            var = atlas.variables_collection.get_item(var_uid)
            if var.is_independent == True:
                x_vars[col_name] = var_uid
            else:
                y_vars[col_name] = var_uid
        
        # Deserialize the CSV
        # NOTE: we assume its timeseries
        timeset_col = training_config.timeset_col
        timestep_col = training_config.timestep_col
        assert timeset_col is not None
        assert timestep_col is not None
        df_x=core.su.utils.decode_and_filter_csv(
            training_config.training_data, 
            list(x_vars.keys()) + [timeset_col, timestep_col]
            )
        df_y=core.su.utils.decode_and_filter_csv(
            training_config.training_data, 
            list(y_vars.keys()) + [timeset_col, timestep_col]
            )

        # Rename columns for both DataFrames to UUIDs for vars
        rename_map = {col: str(var_uuid) for col, var_uuid in col_2_varuid.items()}
        df_x = df_x.rename(columns=rename_map)
        df_y = df_y.rename(columns=rename_map)


        # Split
        unique_timesets = df_x[training_config.timeset_col].unique()
        test_timesets = unique_timesets[int(len(unique_timesets) * 0.8):]   # 20% for testing, copied from
        sampled_timesets = sample(test_timesets, math.ceil(len(test_timesets)*0.3)) # sample 30% of testing set
        df_x_test = df_x[df_x[training_config.timeset_col].isin(sampled_timesets)]
        df_y_test = df_y[df_y[training_config.timeset_col].isin(sampled_timesets)]



        # plot for mape values

        # to do simple chart
        mape_plot = v1endpt.SimpleChart(
            title="MAPE Plots acriss variables",
            pri_data={key: [value] for key, value in zip(training_result.variable_names, training_result.mape_values)},
            axes=("Variable", "Mean Absolute Percentage Error"),
            chart_type="vertical_bar"
        )
        
        
        
        
        # scatter plot for 
        sampled_test_predictions = job_model.predict(df_x_test)
        
        # to do simple chart
        scatter_plot =  v1endpt.SimpleChart(
            title= "Time-Window Prediction vs Ground Truth Scatter Plot",
            pri_data= { y : {"Predicted Values": sampled_test_predictions[y], "Ground Truth": df_y_test[y] }  for y in training_result.variable_names } ,
            axes=("Ground Truth", "Predicted Values"),
            chart_type="scatter"
        )
        
        
        return v1endpt.SurrogateTrainingResults(
            section1_header= "Training Performance",
            section2_body= f"The model achieved an Mean Square Error of {training_result.get_mse()} with max error of {training_result.get_max_error()}",
            section2_header = "Assessment",
            section1_body= f"Signal quality shows a directional accuracy of {training_result.get_directional_accuracy()} with a prediction standard deviation of {training_result.get_prediction_std()}",
            plot1 = mape_plot,
            plot2= scatter_plot
        )

    #############################
    # EXPERIMENT PAGE

    def run_experiment(
        self, experiment_config: v1endpt.ExperimentConfig, user_id: str
    ) -> v1endpt.ExperimentResults:

        #############################
        # GET MODEL (ATLAS)
        #############################
        ATLAS_LABEL = experiment_config.config_name
        USER_ID = user_id

        # replace with get function
        atlas, _ = self.get_atlas_w_metadata(ATLAS_LABEL, USER_ID)

        #############################
        # GENERATE SAMPLES (METIS)
        # df_sample schema: [samples, input_var_identifier]
        #############################

        BOUNDED_INPUT_VARS: Dict[str, Tuple[float, float]] = {}
        FIXED_INPUT_VARS: Dict[str, float] = {}

        for entity_spec_dto in experiment_config.entity_settings.value:
            for entity_variable_dto in entity_spec_dto.spec_variables:
                assert isinstance(entity_variable_dto, v1endpt.EntityVariable)
                entity_label = entity_variable_dto.entity_name
                var_ui_label = entity_variable_dto.name
                lower, upper = entity_variable_dto.ranges

                var = self.iatlas.get_variable(
                    atlas, ui_tuple=(entity_label, var_ui_label)
                )
                var_uid_str = str(var.uid)

                if upper == lower:
                    FIXED_INPUT_VARS[var_uid_str] = upper
                else:
                    BOUNDED_INPUT_VARS[var_uid_str] = (lower, upper)

        df_samples = self.imetis.generate_samples(
            bounded_input_vars=BOUNDED_INPUT_VARS, fixed_input_vars=FIXED_INPUT_VARS
        )

        #############################
        # GENERATE RESPONSE VARIABLES (MATRIX/DAEDELUS)
        # df_response schema: [values, kpi_identifiers] (row aligned w df_sample)
        #############################

        RESPONSE_MODULE: str = "metis"  # futureproofing: metis or daedelus models
        METIS_RUN_LABEL: str = experiment_config.title
        METIS_ENGINE: str = (
            "dwsim"  # futureproofing: to be replaced later when there are options from userside
        )
        METIS_ENGINE_CONFIG: dict = (
            {}
        )  # futureproofing: in case there's custom compounds, etc from userside
        KPI_UILABELS: List[str] = [
            experiment_config.sim_kpi.name
        ]  # Existing schema is not list based

        # Defensive:
        if RESPONSE_MODULE not in self.RESPONSE_MODULES:
            raise KeyError(
                f"Invalid response module '{RESPONSE_MODULE}'. Supported modules are: {self.RESPONSE_MODULES}"
            )

        if RESPONSE_MODULE == "metis":
            kpi_objs = [
                self.iatlas.get_kpi(atlas, ui_label=kpi_label)
                for kpi_label in KPI_UILABELS
            ]
            matrix = self.imatrix.setup_simulator(
                simulation_label=METIS_RUN_LABEL,
                simulator_engine=METIS_ENGINE,
                atlas_obj=atlas,
                config=METIS_ENGINE_CONFIG,
            )
            df_responses = self.imatrix.run_simulations(
                simulator_object=matrix, df_samples=df_samples, kpis=kpi_objs
            )
        else:
            raise AttributeError(f"no module specced")

        #############################
        # ANALYSE SIMULATION RESULTS (METIS)
        # df_analysis schema: [input_vars, analysis_features]
        #############################

        ANALYSIS_TYPE: str = (
            "feature_sensitivity"  # futureproofing: this will be for multi-engine
        )
        BOUNDS: Tuple[Optional[float], Optional[float]] = (0.95, None)

        df_analysis = self.imetis.analyze_results(
            ANALYSIS_TYPE, df_samples, df_responses
        )
        df_analysis = core.me.DataframeFilters.filter_df_by_bounds(
            df_analysis, core.me.AnalyserColEnums_FS.IMPORTANCE_MEAN.value, BOUNDS
        )
        #############################
        # GENERATE RETURN API_DTO (scripted mapping, this is currently a one-off)
        #############################

        TOP_IMPACT: Dict[str, float] = (
            {}
        )  # mapping of top impact to importance impact, as filtered by some metric
        TOP_VARIABLES: List[v1endpt.VariableImpactSummary] = (
            []
        )  # list of filtered variables, as filtered from top impact
        SETPOINT_IMPACT_SUMMARY: List[v1endpt.SetpointImpactSummary] = (
            []
        )  # list of filtered variables, segmented by condition = setpoint
        CONDITION_IMPACT_SUMMARY: List[v1endpt.ConditionImpactSummary] = (
            []
        )  # list of filtered variables, segmented by condition = condition

        # GENERATE RESULTS
        main_summary_text_varstrs: List[str] = []
        for _, row in df_analysis.iterrows():
            # Get values
            var_uid_str: str = row[core.me.AnalyserColEnums_FS.COL_LABEL_X.value]
            var: core.at.VOBaseVariable = self.iatlas.get_variable(
                atlas, uid=uuid.UUID(var_uid_str)
            )
            var_label_pretty: str = self.iatlas.get_ui_label(atlas, obj=var)
            entity_label: str = var.parent.label
            importance_mean: float = row[
                core.me.AnalyserColEnums_FS.IMPORTANCE_MEAN.value
            ]
            importance_percent: float = row[
                core.me.AnalyserColEnums_FS.IMPORTANCE_MEAN_PERCENT.value
            ]

            # Add to top impact dictionary
            main_summary_text_varstrs.append(
                f"`{var_label_pretty}` for Entity `{entity_label}`"
            )
            TOP_IMPACT[f"{entity_label}_{var_label_pretty}"] = importance_mean

            # Add to variable summaries
            TOP_VARIABLES.append(
                v1endpt.VariableImpactSummary(
                    name=var_label_pretty,
                    entity=entity_label,
                    type=var.category.value,
                    value=importance_mean,
                    impact_value=importance_percent,
                    unit=var.unit,
                )
            )

            # Categorize by variable type
            if var.category == core.at.VariableCategoryEnum.SETPOINT:
                SETPOINT_IMPACT_SUMMARY.append(
                    v1endpt.SetpointImpactSummary(
                        entity=entity_label,
                        setpoint=var_label_pretty,
                        weightage=importance_percent,
                        unit=var.unit,
                    )
                )

            if var.category == core.at.VariableCategoryEnum.EQUIPMENT_CONDITION:
                CONDITION_IMPACT_SUMMARY.append(
                    v1endpt.ConditionImpactSummary(
                        entity=entity_label,
                        condition=var_label_pretty,
                        weightage=importance_percent,
                        unit=var.unit,
                    )
                )

        # TEXT FORMATTING
        MAIN_SUMMARY_TEXT = f"Based on the simulations performed, following variables have the highest impact: {', '.join(main_summary_text_varstrs)}."
        TOP_SUMMMARY_TEXT = ""
        IMPACT_SUMMARY_TEXT = "Based on the simulation, following weightages are assigned based on how each variable impacts the KPI. Prioritize analyzing the variables with higher weightages. "

        # SIMULATION SUMMARY '
        SIMULATED_SUMMARY: List[v1endpt.SimulatedScenario] = []

        # Iterate through each row in df_samples using iterrows()
        for idx, row in df_samples.iterrows():  # sample_row is now a Series
            # Defensive
            if not isinstance(idx, int):
                raise AttributeError(f"{idx} is wrong type.")

            # Get KPI info
            kpi_item = kpi_objs[0]
            kpi_name = self.iatlas.get_ui_label(atlas, obj=kpi_item)

            # KPI values
            kpi_value = None
            kpi_uid_str = str(kpi_item.uid)
            if kpi_uid_str in df_responses.columns:
                try:
                    raw_value = df_responses.loc[idx, kpi_uid_str]
                    kpi_value = float(raw_value)  # type: ignore
                except (ValueError, TypeError) as e:
                    raise ValueError(
                        f"KPI value for '{kpi_name}' (UID: {kpi_uid_str}) could not be converted to float. "
                    ) from e
            else:
                available_columns = ", ".join(df_responses.columns)
                raise KeyError(
                    f"Cannot find KPI '{kpi_name}' (UID: {kpi_uid_str}) in response data. "
                    f"Available columns: {available_columns}"
                )

            # Iterate through columns (variables)
            entities_map = defaultdict(list)
            for var_id, value in row.items():
                var = self.iatlas.get_variable(atlas, uid=uuid.UUID(var_id))  # type: ignore
                entity_name = var.parent.label

                # Add variable to its entity
                entities_map[entity_name].append(
                    v1endpt.SimulatedVariable(
                        name=self.iatlas.get_ui_label(atlas, obj=var),
                        type=var.category.value,
                        value=value,
                        unit=var.unit,
                    )
                )

            # Convert to required entity specifications
            entity_specs: List[v1endpt.SimulatedEntity] = [
                v1endpt.SimulatedEntity(entity=entity, variables=variables)
                for entity, variables in entities_map.items()
            ]

            # Add scenario to summary
            SIMULATED_SUMMARY.append(
                v1endpt.SimulatedScenario(
                    scenario=f"Scenario {idx}",
                    entity_specification=entity_specs,
                    kpi=kpi_name,
                    kpi_value=kpi_value,
                )
            )

        return v1endpt.ExperimentResults(
            main_summary_text=MAIN_SUMMARY_TEXT,
            top_summary_text=TOP_SUMMMARY_TEXT,
            top_impact=TOP_IMPACT,
            top_variables=TOP_VARIABLES,
            impact_summary_text=IMPACT_SUMMARY_TEXT,
            setpoint_impact_summary=SETPOINT_IMPACT_SUMMARY,
            condition_impact_summary=CONDITION_IMPACT_SUMMARY,
            simulated_summary=v1endpt.SimulatedData(simulated_data=SIMULATED_SUMMARY),
        )

    #############################

    # DIAGNOSTIC PAGE

    def run_diagnostic(
        self, diagnostic_config: v1endpt.DiagnosticConfig, user_id: str
    ) -> v1endpt.DiagnosticResults:
        #############################
        # GET MODEL (ATLAS)
        #############################
        ATLAS_LABEL = diagnostic_config.config_name
        USER_ID = user_id

        # replace with get function
        atlas, _ = self.get_atlas_w_metadata(ATLAS_LABEL, USER_ID)

        #############################
        # GENERATE SAMPLES & BASELINE REFERENCES (METIS)
        # df_sample schema: [samples, input_var_identifier]
        #############################

        BOUNDED_INPUT_VARS: Dict[str, Tuple[float, float]] = {}
        FIXED_INPUT_VARS: Dict[str, float] = {}
        SAMPLES_BASELINE: Dict[str, float] = {}

        for entity_spec_dto in diagnostic_config.entity_settings.value:
            for entity_variable_dto in entity_spec_dto.spec_variables:
                assert isinstance(entity_variable_dto, v1endpt.EntityVariable)
                entity_label = entity_variable_dto.entity_name
                var_ui_label = entity_variable_dto.name
                lower, upper = entity_variable_dto.ranges

                var = self.iatlas.get_variable(
                    atlas, ui_tuple=(entity_label, var_ui_label)
                )
                var_uid_str = str(var.uid)

                if upper == lower:
                    FIXED_INPUT_VARS[var_uid_str] = upper
                else:
                    BOUNDED_INPUT_VARS[var_uid_str] = (lower, upper)

                # Get BASELINE reference
                SAMPLES_BASELINE[var_uid_str] = entity_variable_dto.value

        DF_SAMPLES = self.imetis.generate_samples(
            bounded_input_vars=BOUNDED_INPUT_VARS, fixed_input_vars=FIXED_INPUT_VARS
        )

        #############################
        # GENERATE RESPONSE VARIABLES (MATRIX/DAEDELUS)
        # df_response schema: [values, kpi_identifiers] (row aligned w df_sample)
        #############################

        SIMULATOR_LABEL: str = diagnostic_config.title
        SIM_ENGINE: str = "dwsim"
        CONFIG: dict = {}
        KPI_UILABELS: List[str] = [diagnostic_config.sim_kpi.name]
        RESPONSE_BASELINE: Dict[str, float] = {}  # TODO : check where this fits in

        kpi_objs = [
            self.iatlas.get_kpi(atlas, ui_label=kpi_label) for kpi_label in KPI_UILABELS
        ]

        RESPONSE_BASELINE = {str(kpi.uid): 0.0 for kpi in kpi_objs}

        matrix = self.imatrix.setup_simulator(
            simulation_label=SIMULATOR_LABEL,
            simulator_engine=SIM_ENGINE,
            atlas_obj=atlas,
            config=CONFIG,
        )

        DF_RESPONSES = self.imatrix.run_simulations(
            simulator_object=matrix, df_samples=DF_SAMPLES, kpis=kpi_objs
        )

        #############################
        # FILTER RESPONSES AND SAMPLES (BASED ON KPI OBJECTIVES)
        # df_response schema: [values, kpi_identifiers] (row aligned w df_sample)
        #############################

        KPI_RANGE: Dict[str, Tuple[float, float]] = {
            str(kpi.uid): diagnostic_config.sim_kpi.range
            for kpi in kpi_objs
            if self.iatlas.get_ui_label(atlas, obj=kpi)
            == diagnostic_config.sim_kpi.name
        }

        # Filter samples in range
        df_responses_inrange, bounds_expanded = (
            core.me.DataframeFilters.filter_df_by_dynamic_bounds(
                df=DF_RESPONSES, column_bounds=KPI_RANGE
            )
        )
        df_samples_inrange = DF_SAMPLES.loc[df_responses_inrange.index]

        # Calculate impact as  difference from baseline
        samples_baseline_df = pd.DataFrame(
            {
                col: [SAMPLES_BASELINE.get(col, 0.0)] * len(df_samples_inrange)
                for col in df_samples_inrange.columns
            },
            index=df_samples_inrange.index,
        )
        df_sample_deltas = df_samples_inrange - samples_baseline_df

        # Calculate response impact for each response column
        response_baseline_df = pd.DataFrame(
            {
                col: [RESPONSE_BASELINE.get(col, 0.0)] * len(df_responses_inrange)
                for col in df_responses_inrange.columns
            },
            index=df_responses_inrange.index,
        )
        df_response_deltas = df_responses_inrange - response_baseline_df

        #############################
        # ANALYSE SIMULATION RESULTS (METIS)
        # df_analysis schema: [input_vars, analysis_features]
        #############################

        ANALYSIS_TYPE: str = (
            "feature_sensitivity"  # futureproofing: this will be for different analysis types
        )
        BOUNDS: Tuple[Optional[float], Optional[float]] = (0.95, None)

        df_analysis_filtered_deltas = self.imetis.analyze_results(
            ANALYSIS_TYPE, df_sample_deltas, df_response_deltas
        )
        df_analysis_filtered_deltas = core.me.DataframeFilters.filter_df_by_bounds(
            df_analysis_filtered_deltas,
            core.me.AnalyserColEnums_FS.IMPORTANCE_MEAN.value,
            BOUNDS,
        )

        #############################
        # GENERATE RETURN API_DTO (scripted mapping, this is currently a one-off)
        #############################

        TOP_IMPACT: Dict[str, float] = (
            {}
        )  # from df_analysis_impactset, shows average importance of selected variables. (top 5%)
        TOP_VARIABLES: List[v1endpt.VariableImpactSummary] = (
            []
        )  # from df_analysis_impactset`
        SETPOINT_IMPACT_SUMMARY: List[v1endpt.SetpointImpactSummary] = (
            []
        )  # from df_analysis_impactset
        CONDITION_IMPACT_SUMMARY: List[v1endpt.ConditionImpactSummary] = (
            []
        )  # from impact variables only
        ANALYSIS: List[v1endpt.VariableDiagnosis] = []  # from impact variables only
        DIAGNOSIS_TEXT = ""

        # Iterate through results
        for _, row in df_analysis_filtered_deltas.iterrows():
            # Get values
            var_uid_str: str = row[core.me.AnalyserColEnums_FS.COL_LABEL_X.value]
            var: core.at.VOBaseVariable = self.iatlas.get_variable(
                atlas, uid=uuid.UUID(var_uid_str)
            )
            var_label_pretty: str = self.iatlas.get_ui_label(atlas, obj=var)
            var_unit = var.unit

            entity_label: str = var.parent.label
            entity_type_ = var.parent.__class__.__name__

            importance_mean: float = row[
                core.me.AnalyserColEnums_FS.IMPORTANCE_MEAN.value
            ]
            importance_percent: float = row[
                core.me.AnalyserColEnums_FS.IMPORTANCE_MEAN_PERCENT.value
            ]

            # Add to top impact dictionary
            TOP_IMPACT[f"{entity_label}_{var_label_pretty}"] = importance_mean

            # Add to variable summaries
            TOP_VARIABLES.append(
                v1endpt.VariableImpactSummary(
                    name=var_label_pretty,
                    entity=entity_label,
                    type=var.category.value,
                    value=importance_mean,
                    impact_value=importance_percent,
                    unit=var_unit,
                )
            )

            # Categorize by variable type
            if var.category == core.at.VariableCategoryEnum.SETPOINT:
                SETPOINT_IMPACT_SUMMARY.append(
                    v1endpt.SetpointImpactSummary(
                        entity=entity_label,
                        setpoint=var_label_pretty,
                        weightage=importance_percent,
                        unit=var_unit,
                    )
                )

            if var.category == core.at.VariableCategoryEnum.EQUIPMENT_CONDITION:
                CONDITION_IMPACT_SUMMARY.append(
                    v1endpt.ConditionImpactSummary(
                        entity=entity_label,
                        condition=var_label_pretty,
                        weightage=importance_percent,
                        unit=var_unit,
                    )
                )

            # Use athena for diagnostics
            DIAGNOSIS_TEXT = self.iathena.generate_entity_diagnosis(
                entity_label=entity_type_,
                variable_contributions={var: importance_percent},
            )

            ANALYSIS.append(
                v1endpt.VariableDiagnosis(
                    entity_type=entity_type_,
                    entity=entity_label,
                    name=var_label_pretty,
                    current_value=importance_mean,
                    recommended_value=importance_mean,
                    diagnosis_text=DIAGNOSIS_TEXT,
                    unit=var_unit,
                )
            )

        # GENERATE ASSESSMENT COMPARISON
        # NOTE - built on assumption only 1 kpi is passed, hence using first index
        KPI_COL_IDX = 0

        expected_kpi_value = df_responses_inrange.mean().iloc[0]
        kpi_min_value = df_responses_inrange.iloc[:, KPI_COL_IDX].min()
        kpi_max_value = df_responses_inrange.iloc[:, KPI_COL_IDX].max()

        # Get KPI from current baseline
        current_kpi_value = 0.0
        if RESPONSE_BASELINE:
            kpi_key = list(RESPONSE_BASELINE.keys())[KPI_COL_IDX]
            current_kpi_value = RESPONSE_BASELINE[kpi_key]
        elif not DF_RESPONSES.empty:
            # Fallback to first value in responses if needed
            current_kpi_value = df_responses_inrange.iloc[0, 0]

        # Create Assessment object with properly computed values
        ASSESSMENT = v1endpt.Assessment(
            lower_bound=float(kpi_min_value),
            upper_bound=float(kpi_max_value),
            current_kpi=float(current_kpi_value),  # type: ignore
            expected_kpi=float(expected_kpi_value),
        )

        # GENERATE ASSESSMENT SUMMARY TEXT

        MAIN_SUMMARY_TEXT: str = (
            "Based on the simulations performed, following variables have the highest impact on the KPI range:"
        )
        TOP_SUMMARY_TEXT: str = ""
        SENSITIVITY_ANALYSIS_TEXT: str = "Here are the top contributors to the KPI:"
        ASSESSMENT_SUMMARY: str = (
            "Note: \n- `Setpoints` are variables you could try changing. \n- `Conditions` are variables that could reflect the need for maintenance"
        )

        return v1endpt.DiagnosticResults(
            main_summary_text=MAIN_SUMMARY_TEXT,
            top_summary_text=TOP_SUMMARY_TEXT,
            top_impact=TOP_IMPACT,
            top_variables=TOP_VARIABLES,
            assessment_summary=ASSESSMENT_SUMMARY,
            assessment=ASSESSMENT,
            sensitivity_analysis_text=SENSITIVITY_ANALYSIS_TEXT,
            setpoint_impact_summary=SETPOINT_IMPACT_SUMMARY,
            condition_impact_summary=CONDITION_IMPACT_SUMMARY,
            analysis=ANALYSIS,
        )

    #############################

    # CALIBRATION PAGE

    def run_calibration(
        self, config: v1endpt.CalibrationConfig, userId: str
    ) -> v1endpt.CalibrationResults:
        
        # ==========================
        # ASSEMBLE SURROGATE MODEL

        # Get atlas
        atlas, _ = self.get_atlas_w_metadata(
            config.atlas_label,
            userId
        )
        
        # Get surrogate model
        surrogate_model = self.isurrogate.get_model(
            userId,
            config.atlas_label
        )
        
        # =========================
        # ASSEMBLE OBJ FUNC

        datatransformer = surrogate_model.datatransformer
        
        # Shape Dataframes
        x_cols = datatransformer._initial_x_variable_cols
        y_cols = datatransformer._initial_y_variable_cols
        time_cols = [ datatransformer.timestep_col , datatransformer.timeset_col ]
        df = core.su.utils.decode_and_filter_csv(
                config.calibration_samples,
                x_cols + y_cols + time_cols
        )
        df_x_partial = df[x_cols + time_cols ]
        df_y= df[y_cols + time_cols]
        
        # Get fixed params
        _fixed_params = {
                str(k): v
                for k, v in config.conditions_to_fix.items()
        }
        df_x_partial = df_x_partial.assign(**_fixed_params)

        objfunc_cls = self.ioptimizer.get_objectivefunction("surrogate")
        assert issubclass(objfunc_cls, core.op.SurrogateObjectiveFunction)

        objfunc = objfunc_cls(
                surrogate_model = surrogate_model,
                df_x_partial = df_x_partial,
                df_y_true = df_y,
        )
        
        # ==========================
        # ASSEMBLE OPTIMIZER
        
        algo= self.ioptimizer.optimization_config.strategy
        opt_cls = self.ioptimizer.get_optimizer(algo) # NOTE - this can have more in future
      
        metadata = core.op.ENTMetadata(
                label = "",
                user_id= config.user_id,
                atlas_id= config.atlas_label,
        )
        opt_config = self.ioptimizer.optimization_config
        
        # ==========================
        # ASSEMBLE OPT PROBLEM

        calib_vars : List[core.at.VOBaseVariable] = [
            atlas.variables_collection.get_item(uid)
            for uid in config.conditions_to_calibrate
        ]

        # Assume bounds are from atlas

        PARAMS: List[core.op.VOOptParam] = []
        for var in calib_vars:
            _label = str(var.uid)
            
            # bounds
            bounds = var.bounds
            if not isinstance(bounds, tuple):
                raise TypeError(f"Variable bounds must be a tuple, got {type(bounds).__name__} for variable: {var}")
                
            if len(bounds) != 2:
                raise ValueError(f"Variable bounds must contain exactly 2 elements, got {len(bounds)} for variable: {var}")
                
            lower, upper = bounds
            if not isinstance(lower, (int, float)) or not isinstance(upper, (int, float)):
                raise TypeError(f"Variable bounds must be numeric values, got {type(lower).__name__} and {type(upper).__name__} for variable: {var}")
            
            # Now extract bounds with confidence
            _min, _max = float(lower), float(upper)

            PARAMS.append(core.op.VOOptParam(
                label = _label,
                type_ = core.op.EnumParameterSpaceType.FLOAT_UNIFORM,
                min_max_inclusive= (_min, _max)
                )
            )
            
        METRICS = [ core.op.VOOptMetric(
            label = core.op.EnumMetrics.REG_MSE,
            direction = core.op.EnumDirection.MINIMIZE),
            core.op.VOOptMetric(
            label=core.op.EnumMetrics.TS_MAPE,
            direction=core.op.EnumDirection.MAXIMIZE),
            core.op.VOOptMetric(
            label=core.op.EnumMetrics.TS_MAPE,
            direction=core.op.EnumDirection.MAXIMIZE),  ]
        PRI_METRIC = METRICS[0].label

        opt_problem = core.op.VOOptimizationProblem(
            parameters=PARAMS,
            metrics = METRICS,
            primary_metric=PRI_METRIC
        )

        # ==========================
        # OPTIMIZE

        result = opt_cls().fit(
            metadata= metadata,
            opt_config= opt_config,
            opt_problem= opt_problem,
            objective_fn= objfunc
        )
        
        # ==========================
        # FORMAT FOR RESULTS

        # FOR CHART 1

        # calbrated data
        calib_data = df_y

        # simulated data
        df_x_copy = df_x_partial.copy()
        for i in result.best_parameters.keys():
            df_x_partial[i] = result.best_parameters[i]
        simulated_data = surrogate_model.predict(df_x_partial)      

        output_vars = [y for y in y_cols if y!=datatransformer.timeset_col]
        
        
        chart1 = v1endpt.SimpleChart(
            title = f"Setpoint versus Simulation Plot", 
            pri_data = {f"{int(t_set)}": {y: list(simulated_data[simulated_data[datatransformer.timeset_col]==t_set][y]) for y in output_vars } 
                            for t_set in list(simulated_data[datatransformer.timeset_col].drop_duplicates())},
            sec_data= { f"{int(t_set)}": {y: list(calib_data[calib_data[datatransformer.timeset_col]==t_set][y]) for y in output_vars } 
                            for t_set in list(calib_data[datatransformer.timeset_col].drop_duplicates()) },
            axes = (datatransformer.timeset_col, "Value") ,
            chart_type = "line") 
        
        # FOR CHART 2
        x_time = simulated_data["Time"].to_numpy()
        # mape transformations
        y_data = {y: np.abs((calib_data[y]-simulated_data[y])/calib_data[y])*100 for y in output_vars}

        chart2= v1endpt.SimpleChart(
                        title = "Set point vs Prediction Errors",
                        pri_data = {f"{y} Error": {"x": list(x_time), "y": list(y_data[y])} for y in output_vars if y!=datatransformer.timestep_col},
                        sec_data= None,
                        axes = ("Time", "Error"),
                        chart_type = "vertical_bar"
                    )

        # For table
        params = [param.label for param in PARAMS]
        old_vals = [df_x_copy[param].iloc[0] for param in params]
        new_vals = [result.best_parameters[param] for param in params]
        table = v1endpt.SimpleTable(
            title = "New VS Calibrated Value",
            data = {"Parameters": params, "Old Values": old_vals, "New Values": new_vals},
            summary_text = "Comparison in new and Old calibration Values."
        )


        # Summary Text 
        abs_mape = np.mean(np.abs((calib_data-simulated_data)/calib_data))*100
        summary_text = f"A model was recalibrated to fit a new dataset and the new models is able to predict with an average error of {abs_mape}%." 
        analysis_text = "" # not sure what to add here
        return v1endpt.CalibrationResults(
                summary_text = summary_text,
                analysis_text = analysis_text,
                chart1 = chart1,
                chart2 = chart2,
                table = table
            )
