
import os
import copy
import json
import logging
import math
import uuid
import pathlib
from pathlib import Path
from abc import ABC, abstractmethod
from dataclasses import dataclass, field, is_dataclass
from datetime import datetime, timedelta, timezone
from enum import Enum, auto, unique
from pprint import pformat, pprint
from collections import defaultdict
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    Dict,
    Generic,
    Iterable,
    List,
    Optional,
    Set,
    Tuple,
    Type,
    TypeVar,
    Union,
    overload,
    Protocol,
    runtime_checkable,
)

from ordered_set import OrderedSet

import backend.core._sharedutils.Utilities as sharedutils
from backend.core._sharedutils.singleton import SingletonMeta

# Layers
import backend.core as core
import backend.infrastructure as infra
import backend.endpoint.v1.schema as v1endpt