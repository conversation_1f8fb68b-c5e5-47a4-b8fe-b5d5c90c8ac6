"""
Callable functions to generate test cases dynamically, 
either from  template loader or hardcoded examples with atlas
"""

from __future__ import annotations
from ._imports import *

from backend.core._atlas._singletons import DiscreteItemSpecEnum, DiscreteSetSpecEnum
import backend.core._atlas.aggregates as at
from backend.core._atlas.aggregates import AtlasRoot



def SWANCOR_model_1_HE_1() -> at.AtlasRoot:
    atlas = at.AtlasRoot(
        label="SWANCOR HE-1 Condenser Use Case",
        plant_id="SWANCOR HE-1 Condenser Use Case",
    )

    # Adding the equipments needed for Heat Exchanger Use Case
    HE_1 = atlas.add_equipment(at.HeatExchanger, "HE-1")
    VALVE_1 = atlas.add_equipment(at.Valve, "VALVE-1")
    VALVE_4 = atlas.add_equipment(at.Valve, "VALVE-4")
    V_1 = atlas.add_equipment(at.Vessel, "V-1")

    # Adding inlet battery limits
    Water_In_BL = atlas.add_equipment(at.BatteryIn, "Water In BL")
    Vapor_In_BL = atlas.add_equipment(at.BatteryIn, "Vapor In BL")

    # Adding outlet battery limits

    Water_Out_BL = atlas.add_equipment(at.BatteryOut, "Water Out BL")
    Vapor_Loss_BL = atlas.add_equipment(at.BatteryOut, "Vapor Loss BL")
    Solvent_Recovery_BL = atlas.add_equipment(at.BatteryOut, "Solvent Recovery BL")

    # Adding streams

    """Setting up water connections"""
    Water_In = atlas.add_stream(Water_In_BL, HE_1, stream_type=at.InputStream)
    Vapor_In = atlas.add_stream(Vapor_In_BL, VALVE_1, stream_type=at.InputStream)
    S2 = atlas.add_stream(VALVE_1, HE_1)
    Water_Out = atlas.add_stream(HE_1, Water_Out_BL, stream_type=at.OutputStream)

    """Setting up vapor connections"""
    S5 = atlas.add_stream(HE_1, VALVE_4)
    Vapor_Out = atlas.add_stream(VALVE_4, V_1)
    Vapor_Loss = atlas.add_stream(V_1, Vapor_Loss_BL, stream_type=at.OutputStream)
    Solvent_Recovery = atlas.add_stream(
        V_1, Solvent_Recovery_BL, stream_type=at.OutputStream
    )

    # Configuring equipments

    # Setting discrete vals

    HE_1.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.CalculateHotFluidOutletTemperature,
    )
    VALVE_1.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.PressureDrop_Valve,
    )
    VALVE_4.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.PressureDrop_Valve,
    )
    V_1.set_value(
        at.DiscreteSetSpecEnum.PressureCalculation,
        at.DiscreteItemSpecEnum.Inlet_Minimum,
    )

    # Setting continous vals

    Water_In.set_value(at.ContVarSpecEnum.Pressure, 200000)
    Water_In.set_bounds(at.ContVarSpecEnum.Pressure, (150000, 250000))
    Water_In.set_value(at.ContVarSpecEnum.Temperature, 299.15)
    Water_In.set_bounds(at.ContVarSpecEnum.Temperature, (299.15, 303.15))
    # TODO Check why volumetric flow rate is not working
    """For some reason volumetric flow rate is not working"""
    Water_In.set_value(
        at.ContVarSpecEnum.Mass_flow_rate, 19.3680
    )  # There is a bug in the codebase
    Water_In.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (19.3680, 58.1041))

    Vapor_In.set_value(at.ContVarSpecEnum.Pressure, 100000)
    Vapor_In.set_bounds(at.ContVarSpecEnum.Pressure, (90000, 110000))
    Vapor_In.set_value(at.ContVarSpecEnum.Temperature, 633.42)
    Vapor_In.set_bounds(at.ContVarSpecEnum.Temperature, (413.15, 637.206))
    Vapor_In.set_value(at.ContVarSpecEnum.Mass_flow_rate, 1.1111)
    Vapor_In.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0.5555, 1.6666))

    HE_1.set_value(at.ContVarSpecEnum.ColdFluidPressureDrop, 0)
    HE_1.set_value(at.ContVarSpecEnum.HotFluidPressureDrop, 0)
    HE_1.set_value(at.ContVarSpecEnum.ColdFluidOutletTemperature, 302.15)
    HE_1.set_bounds(at.ContVarSpecEnum.ColdFluidOutletTemperature, (302.15, 306.15))
    HE_1.set_value(at.ContVarSpecEnum.HeatExchangeArea, 20.0)
    HE_1.set_value(at.ContVarSpecEnum.HeatLoss, 0)

    VALVE_1.set_value(at.ContVarSpecEnum.PressureDrop, 0.0)
    VALVE_4.set_value(at.ContVarSpecEnum.PressureDrop, 0.0)

    # CREATE A C&S
    # Register compounds to model
    compounds = [
        at.CASCompoundEnum.Water,
        at.CASCompoundEnum.BisphenolA,
    ]
    for c in compounds:
        atlas.register_compound(at.CompoundRef.get_vocompound_from_enum(c))

    Water = atlas.retreive_compound(at.CASCompoundEnum.Water)
    BisphenolA = atlas.retreive_compound(at.CASCompoundEnum.BisphenolA)

    # Adding compounds mass ratio objects

    Water_In.add_variable(at.VOCompoundMassRatio(Water, 1))
    Vapor_In.add_variable(at.VOCompoundMassRatio(BisphenolA, 1))

    atlas.propogate_compounds_across_streams()
    atlas.propogate_vars_to_uuid_collection()

    return atlas


##########################################################################################################


def SWANCOR_model_2_HE_2() -> at.AtlasRoot:
    atlas = at.AtlasRoot(
        label="SWANCOR HE-2 Condenser Use Case",
        plant_id="SWANCOR HE-2 Condenser Use Case",
    )

    # Adding the equipments needed for Heat Exchanger Use Case
    HE_2 = atlas.add_equipment(at.HeatExchanger, "HE-2")
    VALVE_1 = atlas.add_equipment(at.Valve, "VALVE-1")
    VALVE_4 = atlas.add_equipment(at.Valve, "VALVE-4")
    V_1 = atlas.add_equipment(at.Vessel, "V-1")

    # Adding inlet battery limits
    Water_In_BL = atlas.add_equipment(at.BatteryIn, "Water In BL")
    Vapor_In_BL = atlas.add_equipment(at.BatteryIn, "Vapor In BL")

    # Adding outlet battery limits

    Water_Out_BL = atlas.add_equipment(at.BatteryOut, "Water Out BL")
    Vapor_Loss_BL = atlas.add_equipment(at.BatteryOut, "Vapor Loss BL")
    Solvent_Recovery_BL = atlas.add_equipment(at.BatteryOut, "Solvent Recovery BL")

    # Adding streams

    """Setting up water connections"""
    Water_In = atlas.add_stream(Water_In_BL, HE_2, stream_type=at.InputStream)
    Vapor_In = atlas.add_stream(Vapor_In_BL, VALVE_1, stream_type=at.InputStream)
    S2 = atlas.add_stream(VALVE_1, HE_2)
    Water_Out = atlas.add_stream(HE_2, Water_Out_BL, stream_type=at.OutputStream)

    """Setting up vapor connections"""
    S5 = atlas.add_stream(HE_2, VALVE_4)
    Vapor_Out = atlas.add_stream(VALVE_4, V_1)
    Vapor_Loss = atlas.add_stream(V_1, Vapor_Loss_BL, stream_type=at.OutputStream)
    Solvent_Recovery = atlas.add_stream(
        V_1, Solvent_Recovery_BL, stream_type=at.OutputStream
    )

    # Configuring equipments

    # Setting discrete vals

    HE_2.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.CalculateHotFluidOutletTemperature,
    )
    VALVE_1.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.PressureDrop_Valve,
    )
    VALVE_4.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.PressureDrop_Valve,
    )
    V_1.set_value(
        at.DiscreteSetSpecEnum.PressureCalculation,
        at.DiscreteItemSpecEnum.Inlet_Minimum,
    )

    # Setting continous vals

    Water_In.set_value(at.ContVarSpecEnum.Pressure, 200000)
    Water_In.set_bounds(at.ContVarSpecEnum.Pressure, (150000, 250000))
    Water_In.set_value(at.ContVarSpecEnum.Temperature, 299.15)
    Water_In.set_bounds(at.ContVarSpecEnum.Temperature, (299.15, 303.15))
    # TODO Check why volumetric flow rate is not working
    """For some reason volumetric flow rate is not working"""
    Water_In.set_value(
        at.ContVarSpecEnum.Mass_flow_rate, 19.3680
    )  # There is a bug in the codebase
    Water_In.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (19.3680, 58.1041))

    Vapor_In.set_value(at.ContVarSpecEnum.Pressure, 100000)
    Vapor_In.set_bounds(at.ContVarSpecEnum.Pressure, (90000, 110000))
    Vapor_In.set_value(at.ContVarSpecEnum.Temperature, 434.94)
    Vapor_In.set_bounds(at.ContVarSpecEnum.Temperature, (393.15, 437.15))
    Vapor_In.set_value(at.ContVarSpecEnum.Mass_flow_rate, 1.31666)
    Vapor_In.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0, 1.6666))

    HE_2.set_value(at.ContVarSpecEnum.ColdFluidPressureDrop, 0)
    HE_2.set_value(at.ContVarSpecEnum.HotFluidPressureDrop, 0)
    HE_2.set_value(at.ContVarSpecEnum.ColdFluidOutletTemperature, 302.15)
    HE_2.set_bounds(at.ContVarSpecEnum.ColdFluidOutletTemperature, (302.15, 306.15))
    HE_2.set_value(at.ContVarSpecEnum.HeatExchangeArea, 20.0)
    HE_2.set_value(at.ContVarSpecEnum.HeatLoss, 0)

    VALVE_1.set_value(at.ContVarSpecEnum.PressureDrop, 0.0)
    VALVE_4.set_value(at.ContVarSpecEnum.PressureDrop, 0.0)

    # CREATE A C&S
    # Register compounds to model
    compounds = [
        at.CASCompoundEnum.Water,
        at.CASCompoundEnum.MethacrylicAcid,
    ]
    for c in compounds:
        atlas.register_compound(at.CompoundRef.get_vocompound_from_enum(c))

    Water = atlas.retreive_compound(at.CASCompoundEnum.Water)
    MethacrylicAcid = atlas.retreive_compound(at.CASCompoundEnum.MethacrylicAcid)

    # Adding compounds mass ratio objects

    Water_In.add_variable(at.VOCompoundMassRatio(Water, 1))
    Vapor_In.add_variable(at.VOCompoundMassRatio(MethacrylicAcid, 1))

    atlas.propogate_compounds_across_streams()
    atlas.propogate_vars_to_uuid_collection()

    return atlas


##################################################################################################


def SWANCOR_model_3_HE_3() -> at.AtlasRoot:
    atlas = at.AtlasRoot(
        label="SWANCOR HE-3 Condenser Use Case",
        plant_id="SWANCOR HE-3 Condenser Use Case",
    )

    # Adding the equipments needed for Heat Exchanger Use Case
    HE_3 = atlas.add_equipment(at.HeatExchanger, "HE-3")
    VALVE_1 = atlas.add_equipment(at.Valve, "VALVE-1")
    VALVE_4 = atlas.add_equipment(at.Valve, "VALVE-4")
    V_1 = atlas.add_equipment(at.Vessel, "V-1")

    # Adding inlet battery limits
    Water_In_BL = atlas.add_equipment(at.BatteryIn, "Water In BL")
    Vapor_In_BL = atlas.add_equipment(at.BatteryIn, "Vapor In BL")

    # Adding outlet battery limits

    Water_Out_BL = atlas.add_equipment(at.BatteryOut, "Water Out BL")
    Vapor_Loss_BL = atlas.add_equipment(at.BatteryOut, "Vapor Loss BL")
    Solvent_Recovery_BL = atlas.add_equipment(at.BatteryOut, "Solvent Recovery BL")

    # Adding streams

    """Setting up water connections"""
    Water_In = atlas.add_stream(Water_In_BL, HE_3, stream_type=at.InputStream)
    Vapor_In = atlas.add_stream(Vapor_In_BL, VALVE_1, stream_type=at.InputStream)
    S2 = atlas.add_stream(VALVE_1, HE_3)
    Water_Out = atlas.add_stream(HE_3, Water_Out_BL, stream_type=at.OutputStream)

    """Setting up vapor connections"""
    S5 = atlas.add_stream(HE_3, VALVE_4)
    Vapor_Out = atlas.add_stream(VALVE_4, V_1)
    Vapor_Loss = atlas.add_stream(V_1, Vapor_Loss_BL, stream_type=at.OutputStream)
    Solvent_Recovery = atlas.add_stream(
        V_1, Solvent_Recovery_BL, stream_type=at.OutputStream
    )

    # Configuring equipments

    # Setting discrete vals

    HE_3.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.CalculateHotFluidOutletTemperature,
    )
    VALVE_1.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.PressureDrop_Valve,
    )
    VALVE_4.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.PressureDrop_Valve,
    )
    V_1.set_value(
        at.DiscreteSetSpecEnum.PressureCalculation,
        at.DiscreteItemSpecEnum.Inlet_Minimum,
    )

    # Setting continous vals

    Water_In.set_value(at.ContVarSpecEnum.Pressure, 200000)
    Water_In.set_bounds(at.ContVarSpecEnum.Pressure, (150000, 250000))
    Water_In.set_value(at.ContVarSpecEnum.Temperature, 299.15)
    Water_In.set_bounds(at.ContVarSpecEnum.Temperature, (299.15, 303.15))
    # TODO Check why volumetric flow rate is not working
    """For some reason volumetric flow rate is not working"""
    Water_In.set_value(
        at.ContVarSpecEnum.Mass_flow_rate, 19.3680
    )  # There is a bug in the codebase
    Water_In.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (19.3680, 58.1041))

    Vapor_In.set_value(at.ContVarSpecEnum.Pressure, 100000)
    Vapor_In.set_bounds(at.ContVarSpecEnum.Pressure, (90000, 110000))
    Vapor_In.set_value(at.ContVarSpecEnum.Temperature, 416.74)
    Vapor_In.set_bounds(at.ContVarSpecEnum.Temperature, (333.15, 419.85))
    Vapor_In.set_value(at.ContVarSpecEnum.Mass_flow_rate, 1.9444)
    Vapor_In.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0, 1.9444))

    HE_3.set_value(at.ContVarSpecEnum.ColdFluidPressureDrop, 0)
    HE_3.set_value(at.ContVarSpecEnum.HotFluidPressureDrop, 0)
    HE_3.set_value(at.ContVarSpecEnum.ColdFluidOutletTemperature, 302.15)
    HE_3.set_bounds(at.ContVarSpecEnum.ColdFluidOutletTemperature, (302.15, 306.15))
    HE_3.set_value(at.ContVarSpecEnum.HeatExchangeArea, 20.0)
    HE_3.set_value(at.ContVarSpecEnum.HeatLoss, 0)

    VALVE_1.set_value(at.ContVarSpecEnum.PressureDrop, 0.0)
    VALVE_4.set_value(at.ContVarSpecEnum.PressureDrop, 0.0)

    # CREATE A C&S
    # Register compounds to model
    compounds = [
        at.CASCompoundEnum.Water,
        at.CASCompoundEnum.Styrene,
    ]
    for c in compounds:
        atlas.register_compound(at.CompoundRef.get_vocompound_from_enum(c))

    Water = atlas.retreive_compound(at.CASCompoundEnum.Water)
    Styrene = atlas.retreive_compound(at.CASCompoundEnum.Styrene)
    # Adding compounds mass ratio objects

    Water_In.add_variable(at.VOCompoundMassRatio(Water, 1))
    Vapor_In.add_variable(at.VOCompoundMassRatio(Styrene, 1))

    atlas.propogate_compounds_across_streams()
    atlas.propogate_vars_to_uuid_collection()

    return atlas
