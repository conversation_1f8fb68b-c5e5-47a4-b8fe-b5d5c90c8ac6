"""
Callable functions to generate test cases dynamically, 
either from  template loader or hardcoded examples with atlas
"""

from __future__ import annotations
from ._imports import *

from backend.core._atlas._singletons import DiscreteItemSpecEnum, DiscreteSetSpecEnum
import backend.core._atlas.aggregates as at
from backend.core._atlas.aggregates import AtlasRoot

####################

# UTILITY FUNCTIONS


def _print_entity_variables(entity: at.ENTBase):
    """Simple Function to show variables of an entity"""
    print(f"\n\n########################################")
    print(f"GETTING COLLECTIONS FOR `{entity.label}`...")
    collections = entity.get_collections()
    for collection in collections:
        print(f"\nCOLLECTION NAME: `{collection.__class__.__name__}`")
        pprint(collection.collection, indent=4)
    print("...DONE\n")


####################

def z_heating_oil() -> at.AtlasRoot:
    atlas = at.AtlasRoot(label="Industrial Oil Heating", plant_id="Oil Heating")

    # Adding the equipments needed for industrial chiller system

    Fuel_Mixer = atlas.add_equipment(at.StreamMixer, "Fuel Mixer")
    HG_Boiler = atlas.add_equipment(at.ConversionReactor, "HG Boiler")
    HEX_100 = atlas.add_equipment(at.HeatExchanger, "HEX-100")
    P_100 = atlas.add_equipment(at.Pump, "P-100")
    V_100 = atlas.add_equipment(at.Vessel, "V-100")

    Oil_BL = atlas.add_equipment(at.BatteryIn, "Oil Feed BL")
    Fuel_BL = atlas.add_equipment(at.BatteryIn, "Fuel BL")
    Air_BL = atlas.add_equipment(at.BatteryIn, "Air BL")

    Residue_BL = atlas.add_equipment(at.BatteryOut, "Residue BL")
    To_HRSG_Unit_BL = atlas.add_equipment(at.BatteryOut, "To HRSG Unit BL")
    Vent_BL = atlas.add_equipment(at.BatteryOut, "Vent BL")
    Oil_to_Heating_BL = atlas.add_equipment(at.BatteryOut, "Oil to Heating BL")

    # Adding streams

    Fuel = atlas.add_stream(Fuel_BL, Fuel_Mixer, stream_type=at.InputStream)
    Air = atlas.add_stream(Air_BL, Fuel_Mixer, stream_type=at.InputStream)
    Feed_to_burner = atlas.add_stream(
        Fuel_Mixer,
        HG_Boiler,
    )
    Hot_Flue_Gas = atlas.add_stream(
        HG_Boiler,
        HEX_100,
    )
    Residue = atlas.add_stream(HG_Boiler, Residue_BL, stream_type=at.OutputStream)
    Oil = atlas.add_stream(Oil_BL, P_100, stream_type=at.InputStream)
    Oil_Out = atlas.add_stream(
        P_100,
        HEX_100,
    )
    To_HRSG_Unit = atlas.add_stream(
        HEX_100, To_HRSG_Unit_BL, stream_type=at.OutputStream
    )
    Heated_Oil = atlas.add_stream(HEX_100, V_100)
    Vent = atlas.add_stream(V_100, Vent_BL, stream_type=at.OutputStream)
    Oil_to_Heating = atlas.add_stream(
        V_100, Oil_to_Heating_BL, stream_type=at.OutputStream
    )

    # Configuring equipments

    # Setting discrete vals
    Fuel_Mixer.set_value(
        at.DiscreteSetSpecEnum.PressureCalculation,
        at.DiscreteItemSpecEnum.Inlet_Minimum,
    )
    HG_Boiler.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.RCT_Conversion_Adiabatic,
    )
    P_100.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.OutletPressure,
    )
    HEX_100.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.CalculateOutletTemperatures_UA,
    )
    HEX_100.set_value(
        at.DiscreteSetSpecEnum.FlowDirection,
        at.DiscreteItemSpecEnum.FlowDirection_CounterCurrent,
    )
    V_100.set_value(
        at.DiscreteSetSpecEnum.PressureCalculation,
        at.DiscreteItemSpecEnum.Inlet_Minimum,
    )

    # Setting continous vals
    Fuel.set_value(at.ContVarSpecEnum.Pressure, 101325)
    Fuel.set_value(at.ContVarSpecEnum.Temperature, 298.15)
    Fuel.set_value(at.ContVarSpecEnum.Mass_flow_rate, 0.00730132)
    Fuel.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0.00730132, 0.0091277))

    Air.set_value(at.ContVarSpecEnum.Pressure, 101325)
    Air.set_value(at.ContVarSpecEnum.Temperature, 316.15)
    Air.set_value(at.ContVarSpecEnum.Mass_flow_rate, 0.127338)
    Air.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0.127338, 0.15916))

    Oil.set_value(at.ContVarSpecEnum.Pressure, 101325)
    Oil.set_value(at.ContVarSpecEnum.Temperature, 323.15)
    Oil.set_value(at.ContVarSpecEnum.Mass_flow_rate, 0.138889)
    Oil.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0.138889, 0.16666))

    HG_Boiler.set_value(at.ContVarSpecEnum.PressureDrop, 0)

    P_100.set_value(at.ContVarSpecEnum.OutletPressure, 250000)
    P_100.set_value(at.ContVarSpecEnum.Efficiency, 0.75)

    HEX_100.set_value(at.ContVarSpecEnum.ColdFluidPressureDrop, 862.928)
    HEX_100.set_value(at.ContVarSpecEnum.HotFluidPressureDrop, 25.7026)
    HEX_100.set_value(at.ContVarSpecEnum.GlobalHeatTransferCoefficient, 10.0)
    HEX_100.set_value(at.ContVarSpecEnum.HeatExchangeArea, 1.0)
    HEX_100.set_value(at.ContVarSpecEnum.HeatLoss, 0)
    HEX_100.set_bounds(at.ContVarSpecEnum.GlobalHeatTransferCoefficient, (10, 15))

    # CREATE A C&S
    compounds = [
        at.CASCompoundEnum.N_Dodecane,
        at.CASCompoundEnum.Methane,
        at.CASCompoundEnum.CarbonDioxide,
        at.CASCompoundEnum.Nitrogen,
        at.CASCompoundEnum.Oxygen,
        at.CASCompoundEnum.N_Tetradecane,
        at.CASCompoundEnum.Cyclohexane,
        at.CASCompoundEnum.Toluene,
        at.CASCompoundEnum.N_Hexadecane,
        at.CASCompoundEnum.N_Octadecane,
        at.CASCompoundEnum.Water,
    ]
    for c in compounds:
        atlas.register_compound(at.CompoundRef.get_vocompound_from_enum(c))
    N_dodecane = atlas.retreive_compound(at.CASCompoundEnum.N_Dodecane)
    Methane = atlas.retreive_compound(at.CASCompoundEnum.Methane)
    Carbon_dioxide = atlas.retreive_compound(at.CASCompoundEnum.CarbonDioxide)
    Nitrogen = atlas.retreive_compound(at.CASCompoundEnum.Nitrogen)
    Oxygen = atlas.retreive_compound(at.CASCompoundEnum.Oxygen)
    N_tetradecane = atlas.retreive_compound(at.CASCompoundEnum.N_Tetradecane)
    Cyclohexane = atlas.retreive_compound(at.CASCompoundEnum.Cyclohexane)
    Toluene = atlas.retreive_compound(at.CASCompoundEnum.Toluene)
    N_hexadecane = atlas.retreive_compound(at.CASCompoundEnum.N_Hexadecane)
    N_octadecane = atlas.retreive_compound(at.CASCompoundEnum.N_Octadecane)
    Water = atlas.retreive_compound(at.CASCompoundEnum.Water)

    # Create a reaction
    r1 = at.ConversionReaction("Combustion Reaction of Methane")

    # Add Reaction Stoich
    c_s: Dict[at.VOCompound, int] = {
        Methane: -1,
        Oxygen: -2,
        Water: 2,
        Carbon_dioxide: 1,
    }
    for k, v in c_s.items():
        r1.add_variable(at.VOReactionStoich(k, v))

    r1.set_value(at.DiscreteSetSpecEnum.BaseCompound, Methane)
    r1.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "1")
    r1.set_value(
        at.DiscreteSetSpecEnum.ReactionPhase, at.DiscreteItemSpecEnum.REACTION_VAPOR
    )

    # Add to atlas
    atlas.add_reaction_to_reactor(HG_Boiler, r1)

    # Adding compounds mass ratio objects
    Fuel.add_variable(at.VOCompoundMassRatio(Methane, 1))
    # Fuel.add_variable(at.VOCompoundMassRatio(Methane, 0.0073013194))

    Air.add_variable(at.VOCompoundMassRatio(Water, 0.011947795))
    Air.add_variable(at.VOCompoundMassRatio(Carbon_dioxide, 0.00046085131))
    Air.add_variable(at.VOCompoundMassRatio(Nitrogen, 0.75761501))
    Air.add_variable(at.VOCompoundMassRatio(Oxygen, 0.22997635))

    Oil.add_variable(at.VOCompoundMassRatio(N_dodecane, 0.25))
    Oil.add_variable(at.VOCompoundMassRatio(N_tetradecane, 0.2))
    Oil.add_variable(at.VOCompoundMassRatio(Cyclohexane, 0.1))
    Oil.add_variable(at.VOCompoundMassRatio(Toluene, 0.1))
    Oil.add_variable(at.VOCompoundMassRatio(N_hexadecane, 0.2))
    Oil.add_variable(at.VOCompoundMassRatio(N_octadecane, 0.15))

    return atlas


######################################################################


def industrial_natural_gas_boiler_with_preheating_trains() -> at.AtlasRoot:
    atlas = at.AtlasRoot(
        label="Industrial Natural Gas Boiler With Preheating Trains",
        plant_id="Industrial Natural Gas Boiler With Preheating Trains",
    )

    # Adding the equipments needed for Industrial Natural Gas Boiler With Preheating Trains

    M_100 = atlas.add_equipment(at.StreamMixer, "M-100")
    B_100 = atlas.add_equipment(at.ConversionReactor, "B-100")
    HEX_100 = atlas.add_equipment(at.HeatExchanger, "HEX-100")
    HEX_101 = atlas.add_equipment(at.HeatExchanger, "HEX-101")

    Fuel_BL = atlas.add_equipment(at.BatteryIn, "Fuel BL")
    Air_BL = atlas.add_equipment(at.BatteryIn, "Air BL")
    Oil_Feed_BL = atlas.add_equipment(at.BatteryIn, "Oil Feed BL")
    Benzen_Toluene_BL = atlas.add_equipment(at.BatteryIn, "Benzene Toluene Feed BL")

    Residue_BL = atlas.add_equipment(at.BatteryOut, "Residue BL")
    Heated_Oil_BL = atlas.add_equipment(at.BatteryOut, "Heated Oil BL")
    Heated_BT_BL = atlas.add_equipment(at.BatteryOut, "Heated BT BL")
    To_Stack_BL = atlas.add_equipment(at.BatteryOut, "To Stack BL")
    temp_valve = atlas.add_equipment(at.Valve, "valve")

    # Adding streams

    Fuel = atlas.add_stream(Fuel_BL, M_100, stream_type=at.InputStream)
    Air = atlas.add_stream(Air_BL, M_100, stream_type=at.InputStream)
    Feed_to_burner = atlas.add_stream(
        M_100,
        B_100,
    )
    Hot_Flue_Gas = atlas.add_stream(
        B_100,
        HEX_100,
    )
    To_HRSG_Unit = atlas.add_stream(HEX_100, HEX_101)
    To_Stack_valve = atlas.add_stream(HEX_101, temp_valve)
    To_Stack = atlas.add_stream(temp_valve, To_Stack_BL, stream_type=at.OutputStream)
    Oil_Feed = atlas.add_stream(Oil_Feed_BL, HEX_100, stream_type=at.InputStream)
    Benzen_Toluene_Feed = atlas.add_stream(
        Benzen_Toluene_BL, HEX_101, stream_type=at.InputStream
    )

    Heated_Oil = atlas.add_stream(HEX_100, Heated_Oil_BL, stream_type=at.OutputStream)
    Residue = atlas.add_stream(B_100, Residue_BL, stream_type=at.OutputStream)
    Heated_BT_Feed = atlas.add_stream(
        HEX_101, Heated_BT_BL, stream_type=at.OutputStream
    )

    # Configuring equipments

    # Setting discrete vals

    M_100.set_value(
        at.DiscreteSetSpecEnum.PressureCalculation,
        at.DiscreteItemSpecEnum.Inlet_Minimum,
    )
    B_100.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.RCT_Conversion_Adiabatic,
    )
    HEX_100.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.CalculateOutletTemperatures_UA,
    )
    HEX_100.set_value(
        at.DiscreteSetSpecEnum.FlowDirection,
        at.DiscreteItemSpecEnum.FlowDirection_CounterCurrent,
    )
    HEX_101.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.CalculateOutletTemperatures_UA,
    )
    HEX_101.set_value(
        at.DiscreteSetSpecEnum.FlowDirection,
        at.DiscreteItemSpecEnum.FlowDirection_CounterCurrent,
    )

    # Setting continous vals
    Fuel.set_value(at.ContVarSpecEnum.Pressure, 101325)
    Fuel.set_value(at.ContVarSpecEnum.Temperature, 298.15)
    Fuel.set_value(at.ContVarSpecEnum.Mass_flow_rate, 0.007)
    Fuel.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0.0055, 0.0083))

    Air.set_value(at.ContVarSpecEnum.Pressure, 101325)
    Air.set_value(at.ContVarSpecEnum.Temperature, 316.15)
    Air.set_value(at.ContVarSpecEnum.Mass_flow_rate, 0.1272)
    Air.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0.1111, 0.2222))

    Oil_Feed.set_value(at.ContVarSpecEnum.Pressure, 101325)
    Oil_Feed.set_value(at.ContVarSpecEnum.Temperature, 323.12)
    Oil_Feed.set_value(at.ContVarSpecEnum.Mass_flow_rate, 1)
    Oil_Feed.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0.8333, 1.111))

    Benzen_Toluene_Feed.set_value(at.ContVarSpecEnum.Pressure, 101325)
    Benzen_Toluene_Feed.set_value(at.ContVarSpecEnum.Temperature, 298.15)
    Benzen_Toluene_Feed.set_value(at.ContVarSpecEnum.Mass_flow_rate, 1)
    Benzen_Toluene_Feed.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0.8333, 1.111))

    B_100.set_value(at.ContVarSpecEnum.PressureDrop_ConversionReactor, 0)

    HEX_100.set_value(at.ContVarSpecEnum.ColdFluidPressureDrop, 862.928)
    HEX_100.set_value(at.ContVarSpecEnum.HotFluidPressureDrop, 25.7026)
    HEX_100.set_value(at.ContVarSpecEnum.GlobalHeatTransferCoefficient, 120.0)
    HEX_100.set_value(at.ContVarSpecEnum.HeatExchangeArea, 2.0)
    HEX_100.set_value(at.ContVarSpecEnum.HeatLoss, 0)
    HEX_100.set_bounds(at.ContVarSpecEnum.GlobalHeatTransferCoefficient, (80, 140))
    HEX_100.set_bounds(at.ContVarSpecEnum.HeatLoss, (0, 10))

    HEX_101.set_value(at.ContVarSpecEnum.ColdFluidPressureDrop, 862.928)
    HEX_101.set_value(at.ContVarSpecEnum.HotFluidPressureDrop, 25.7026)
    HEX_101.set_value(at.ContVarSpecEnum.GlobalHeatTransferCoefficient, 11.1)
    HEX_101.set_value(at.ContVarSpecEnum.HeatExchangeArea, 20.0)
    HEX_101.set_value(at.ContVarSpecEnum.HeatLoss, 0)
    HEX_101.set_bounds(at.ContVarSpecEnum.GlobalHeatTransferCoefficient, (5, 15))
    HEX_101.set_bounds(at.ContVarSpecEnum.HeatLoss, (0, 10))

    temp_valve.set_value(at.ContVarSpecEnum.PressureDrop, 0.0)

    # CREATE A C&S
    # Register compounds to model
    compounds = [
        at.CASCompoundEnum.Methane,
        at.CASCompoundEnum.Ethane,
        at.CASCompoundEnum.Propane,
        at.CASCompoundEnum.CarbonDioxide,
        at.CASCompoundEnum.Nitrogen,
        at.CASCompoundEnum.Oxygen,
        at.CASCompoundEnum.N_Dodecane,
        at.CASCompoundEnum.N_Tetradecane,
        at.CASCompoundEnum.Cyclohexane,
        at.CASCompoundEnum.Toluene,
        at.CASCompoundEnum.N_Hexadecane,
        at.CASCompoundEnum.N_Octadecane,
        at.CASCompoundEnum.Water,
        at.CASCompoundEnum.Benzene,
    ]
    for c in compounds:
        atlas.register_compound(at.CompoundRef.get_vocompound_from_enum(c))

    Methane = atlas.retreive_compound(at.CASCompoundEnum.Methane)
    Ethane = atlas.retreive_compound(at.CASCompoundEnum.Ethane)
    Propane = atlas.retreive_compound(at.CASCompoundEnum.Propane)

    Carbon_dioxide = atlas.retreive_compound(at.CASCompoundEnum.CarbonDioxide)
    Nitrogen = atlas.retreive_compound(at.CASCompoundEnum.Nitrogen)
    Oxygen = atlas.retreive_compound(at.CASCompoundEnum.Oxygen)

    N_dodecane = atlas.retreive_compound(at.CASCompoundEnum.N_Dodecane)
    N_tetradecane = atlas.retreive_compound(at.CASCompoundEnum.N_Tetradecane)
    Cyclohexane = atlas.retreive_compound(at.CASCompoundEnum.Cyclohexane)
    Toluene = atlas.retreive_compound(at.CASCompoundEnum.Toluene)
    N_hexadecane = atlas.retreive_compound(at.CASCompoundEnum.N_Hexadecane)
    N_octadecane = atlas.retreive_compound(at.CASCompoundEnum.N_Octadecane)

    Water = atlas.retreive_compound(at.CASCompoundEnum.Water)

    Benzene = atlas.retreive_compound(at.CASCompoundEnum.Benzene)

    # Create a reaction
    r1 = at.ConversionReaction("Combustion Reaction of Methane")
    r2 = at.ConversionReaction("Combustion Reaction of Ethane")
    r3 = at.ConversionReaction("Combustion Reaction of Propane")

    # Add Reaction Stoich
    c_s_one: Dict[at.VOCompound, int] = {
        Methane: -1,
        Oxygen: -2,
        Water: 2,
        Carbon_dioxide: 1,
    }
    for k, v in c_s_one.items():
        r1.add_variable(at.VOReactionStoich(k, v))

    r1.set_value(at.DiscreteSetSpecEnum.BaseCompound, Methane)
    r1.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "1")
    r1.set_value(
        at.DiscreteSetSpecEnum.ReactionPhase, at.DiscreteItemSpecEnum.REACTION_VAPOR
    )

    c_s_two: Dict[at.VOCompound, int] = {
        Ethane: -2,
        Oxygen: -7,
        Water: 6,
        Carbon_dioxide: 4,
    }
    for k, v in c_s_two.items():
        r2.add_variable(at.VOReactionStoich(k, v))

    r2.set_value(at.DiscreteSetSpecEnum.BaseCompound, Ethane)
    r2.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "1")
    r2.set_value(
        at.DiscreteSetSpecEnum.ReactionPhase, at.DiscreteItemSpecEnum.REACTION_VAPOR
    )
    c_s_three: Dict[at.VOCompound, int] = {
        Propane: -1,
        Oxygen: -5,
        Water: 4,
        Carbon_dioxide: 3,
    }
    for k, v in c_s_three.items():
        r3.add_variable(at.VOReactionStoich(k, v))

    r3.set_value(at.DiscreteSetSpecEnum.BaseCompound, Propane)
    r3.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "1")
    r3.set_value(
        at.DiscreteSetSpecEnum.ReactionPhase, at.DiscreteItemSpecEnum.REACTION_VAPOR
    )

    # Add to atlas
    atlas.add_reaction_to_reactor(B_100, r1)
    atlas.add_reaction_to_reactor(B_100, r2)
    atlas.add_reaction_to_reactor(B_100, r3)

    # Adding compounds mass ratio objects
    Fuel.add_variable(at.VOCompoundMassRatio(Methane, 0.79564964))
    Fuel.add_variable(at.VOCompoundMassRatio(Ethane, 0.08285104))
    Fuel.add_variable(at.VOCompoundMassRatio(Propane, 0.12149932))

    Air.add_variable(at.VOCompoundMassRatio(Carbon_dioxide, 0.00046642405))
    Air.add_variable(at.VOCompoundMassRatio(Nitrogen, 0.76677629))
    Air.add_variable(at.VOCompoundMassRatio(Oxygen, 0.23275728))

    Oil_Feed.add_variable(at.VOCompoundMassRatio(N_dodecane, 0.25))
    Oil_Feed.add_variable(at.VOCompoundMassRatio(N_tetradecane, 0.2))
    Oil_Feed.add_variable(at.VOCompoundMassRatio(Cyclohexane, 0.1))
    Oil_Feed.add_variable(at.VOCompoundMassRatio(Toluene, 0.1))
    Oil_Feed.add_variable(at.VOCompoundMassRatio(N_hexadecane, 0.2))
    Oil_Feed.add_variable(at.VOCompoundMassRatio(N_octadecane, 0.15))

    Benzen_Toluene_Feed.add_variable(at.VOCompoundMassRatio(Benzene, 0.45880597))
    Benzen_Toluene_Feed.add_variable(at.VOCompoundMassRatio(Toluene, 0.54119403))

    atlas.propogate_compounds_across_streams()
    atlas.propogate_vars_to_uuid_collection()

    # Adding test KPI
    var_collection = atlas.variables_collection
    kpi_collection = atlas.kpi_collection
    test_kpi1 = at.KPI(
        label="HEX-100 Heat Exchange",
        expression="{{HEX-100 - Heat Exchange}}",
        collection_reference=var_collection,
    )
    kpi_collection.add_item(test_kpi1)

    return atlas


##########################################################################################################


def pump_use_case() -> at.AtlasRoot:
    atlas = at.AtlasRoot(
        label="Pump Use Case",
        plant_id="Pump Use Case",
    )

    # Adding the equipments needed for Pump Use Case
    P_100 = atlas.add_equipment(at.Pump, "P-100")

    # Adding inlet battery limits
    From_Tank_100_BL = atlas.add_equipment(at.BatteryIn, "From Tank 100 BL")

    # Adding outlet battery limits
    To_HEX_100_BL = atlas.add_equipment(at.BatteryOut, "To HEX 100 BL")

    # Adding streams
    From_Tank_100 = atlas.add_stream(
        From_Tank_100_BL, P_100, stream_type=at.InputStream
    )
    To_HEX_100 = atlas.add_stream(P_100, To_HEX_100_BL, stream_type=at.OutputStream)

    # Configuring equipments

    # Setting discrete vals
    P_100.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.PowerRequired,
    )

    # Setting continous vals

    From_Tank_100.set_value(at.ContVarSpecEnum.Pressure, 101325)
    From_Tank_100.set_bounds(at.ContVarSpecEnum.Pressure, (100000, 150000))
    From_Tank_100.set_value(at.ContVarSpecEnum.Temperature, 298.15)
    From_Tank_100.set_bounds(at.ContVarSpecEnum.Temperature, (293.15, 303.15))
    From_Tank_100.set_value(at.ContVarSpecEnum.Mass_flow_rate, 1)
    From_Tank_100.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (0.75, 1.5))

    P_100.set_value(at.ContVarSpecEnum.PowerRequired, 0.85)
    P_100.set_bounds(at.ContVarSpecEnum.PowerRequired, (0.75, 1.45))
    P_100.set_value(at.ContVarSpecEnum.Efficiency, 0.75)
    P_100.set_bounds(at.ContVarSpecEnum.Efficiency, (0.65, 0.85))

    # CREATE A C&S
    # Register compounds to model
    compounds = [
        at.CASCompoundEnum.Methanol,
        at.CASCompoundEnum.Water,
    ]
    for c in compounds:
        atlas.register_compound(at.CompoundRef.get_vocompound_from_enum(c))

    Methanol = atlas.retreive_compound(at.CASCompoundEnum.Methanol)
    Water = atlas.retreive_compound(at.CASCompoundEnum.Water)

    # Adding compounds mass ratio objects

    From_Tank_100.add_variable(at.VOCompoundMassRatio(Methanol, 0.64010569))
    From_Tank_100.add_variable(at.VOCompoundMassRatio(Water, 0.35989431))
    
    # Add kpi
    var_collection = atlas.variables_collection
    kpi_collection = atlas.kpi_collection
    kpi = at.KPI(
        label= "P-100 - Pressure Increase",
        expression = '{{P-100 - Pressure Increase}}',
        collection_reference=var_collection
    )
    kpi_collection.add_item(kpi)

    # Propogate

    atlas.propogate_compounds_across_streams()
    atlas.propogate_vars_to_uuid_collection()

    return atlas


##########################################################################################################


def steam_turbine_use_case() -> at.AtlasRoot:
    atlas = at.AtlasRoot(
        label="Steam Turbine Use Case",
        plant_id="Steam Turbine Use Case",
    )

    # Adding the equipments needed for Pump Use Case
    ST_134 = atlas.add_equipment(at.Expander, "ST-134")

    # Adding inlet battery limits
    From_Drum_D_134_BL = atlas.add_equipment(at.BatteryIn, "From Drum D-134 BL")

    # Adding outlet battery limits
    To_V_134_BL = atlas.add_equipment(at.BatteryOut, "To V-134 BL")

    # Adding streams
    From_Drum_D_134 = atlas.add_stream(
        From_Drum_D_134_BL, ST_134, stream_type=at.InputStream
    )
    To_V_134 = atlas.add_stream(ST_134, To_V_134_BL, stream_type=at.OutputStream)

    # Configuring equipments

    # Setting discrete vals
    ST_134.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.OutletPressure_Expander,
    )
    ST_134.set_value(
        at.DiscreteSetSpecEnum.ThermodynamicProcess,
        at.DiscreteItemSpecEnum.ThermodynamicProcess_Adiabatic,
    )

    # Setting continous vals

    From_Drum_D_134.set_value(at.ContVarSpecEnum.Pressure, 1000000)
    From_Drum_D_134.set_bounds(at.ContVarSpecEnum.Pressure, (900000, 1200000))
    From_Drum_D_134.set_value(at.ContVarSpecEnum.Temperature, 573.15)
    From_Drum_D_134.set_bounds(at.ContVarSpecEnum.Temperature, (550, 650))
    From_Drum_D_134.set_value(at.ContVarSpecEnum.Mass_flow_rate, 20)
    From_Drum_D_134.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (15, 25))

    ST_134.set_value(at.ContVarSpecEnum.OutletPressure, 200000)
    ST_134.set_bounds(at.ContVarSpecEnum.OutletPressure, (200000, 400000))
    ST_134.set_value(at.ContVarSpecEnum.AdiabaticEff, 0.75)
    ST_134.set_bounds(at.ContVarSpecEnum.AdiabaticEff, (0.70, 0.85))

    # CREATE A C&S
    # Register compounds to model
    compounds = [
        at.CASCompoundEnum.Water,
    ]
    for c in compounds:
        atlas.register_compound(at.CompoundRef.get_vocompound_from_enum(c))

    Water = atlas.retreive_compound(at.CASCompoundEnum.Water)

    # Adding compounds mass ratio objects

    From_Drum_D_134.add_variable(at.VOCompoundMassRatio(Water, 1))

    atlas.propogate_compounds_across_streams()
    atlas.propogate_vars_to_uuid_collection()

    return atlas


##########################################################################################################


def heat_exchanger_use_case() -> at.AtlasRoot:
    atlas = at.AtlasRoot(
        label="Heat Exchanger Use Case",
        plant_id="Heat Exchanger Use Case",
    )

    # Adding the equipments needed for Heat Exchanger Use Case
    HEX_100 = atlas.add_equipment(at.HeatExchanger, "HEX-100")

    # Adding inlet battery limits
    Steam_from_HRSG_BL = atlas.add_equipment(at.BatteryIn, "Steam from HRSG BL")
    Feed_Water_In_BL = atlas.add_equipment(at.BatteryIn, "Feed Water In BL")

    # Adding outlet battery limits
    Condensate_Out_BL = atlas.add_equipment(at.BatteryOut, "Condensate Out BL")
    Feed_Water_Out_BL = atlas.add_equipment(at.BatteryOut, "Feed Water Out BL")

    # Adding streams
    Steam_from_HRSG = atlas.add_stream(
        Steam_from_HRSG_BL, HEX_100, stream_type=at.InputStream
    )
    Feed_Water_In = atlas.add_stream(
        Feed_Water_In_BL, HEX_100, stream_type=at.InputStream
    )
    Condensate_Out = atlas.add_stream(
        HEX_100, Condensate_Out_BL, stream_type=at.OutputStream
    )
    Feed_Water_Out = atlas.add_stream(
        HEX_100, Feed_Water_Out_BL, stream_type=at.OutputStream
    )

    # Configuring equipments

    # Setting discrete vals
    HEX_100.set_value(
        at.DiscreteSetSpecEnum.CalculationType,
        at.DiscreteItemSpecEnum.CalculateOutletTemperatures_UA,
    )
    HEX_100.set_value(
        at.DiscreteSetSpecEnum.FlowDirection,
        at.DiscreteItemSpecEnum.FlowDirection_CounterCurrent,
    )

    # Setting continous vals

    Steam_from_HRSG.set_value(at.ContVarSpecEnum.Pressure, 500000)
    Steam_from_HRSG.set_bounds(at.ContVarSpecEnum.Pressure, (450000, 600000))
    Steam_from_HRSG.set_value(at.ContVarSpecEnum.Temperature, 424.99)
    Steam_from_HRSG.set_bounds(at.ContVarSpecEnum.Temperature, (403.15, 438.15))
    Steam_from_HRSG.set_value(at.ContVarSpecEnum.Mass_flow_rate, 1)
    Steam_from_HRSG.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (1, 3))

    Feed_Water_In.set_value(at.ContVarSpecEnum.Pressure, 300000)
    Feed_Water_In.set_bounds(at.ContVarSpecEnum.Pressure, (300000, 350000))
    Feed_Water_In.set_value(at.ContVarSpecEnum.Temperature, 303.15)
    Feed_Water_In.set_bounds(at.ContVarSpecEnum.Temperature, (298.15, 313.15))
    Feed_Water_In.set_value(at.ContVarSpecEnum.Mass_flow_rate, 1)
    Feed_Water_In.set_bounds(at.ContVarSpecEnum.Mass_flow_rate, (1, 3))

    HEX_100.set_value(at.ContVarSpecEnum.ColdFluidPressureDrop, 0)
    HEX_100.set_bounds(at.ContVarSpecEnum.ColdFluidPressureDrop, (0, 50000))

    HEX_100.set_value(at.ContVarSpecEnum.HotFluidPressureDrop, 0)
    HEX_100.set_bounds(at.ContVarSpecEnum.HotFluidPressureDrop, (0, 10000))

    HEX_100.set_value(at.ContVarSpecEnum.GlobalHeatTransferCoefficient, 900.0)
    HEX_100.set_bounds(at.ContVarSpecEnum.GlobalHeatTransferCoefficient, (300, 1200))

    HEX_100.set_value(at.ContVarSpecEnum.HeatExchangeArea, 5.0)

    HEX_100.set_value(at.ContVarSpecEnum.HeatLoss, 0)
    HEX_100.set_bounds(at.ContVarSpecEnum.HeatLoss, (0, 5.0))

    # CREATE A C&S
    # Register compounds to model
    compounds = [
        at.CASCompoundEnum.Water,
    ]
    for c in compounds:
        atlas.register_compound(at.CompoundRef.get_vocompound_from_enum(c))

    Water = atlas.retreive_compound(at.CASCompoundEnum.Water)

    # Adding compounds mass ratio objects

    Steam_from_HRSG.add_variable(at.VOCompoundMassRatio(Water, 1))
    Feed_Water_In.add_variable(at.VOCompoundMassRatio(Water, 1))

    atlas.propogate_compounds_across_streams()
    atlas.propogate_vars_to_uuid_collection()

    return atlas
