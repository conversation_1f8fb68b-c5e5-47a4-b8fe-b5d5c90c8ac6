import backend.application.usecase_templates.public_templates as pb
import backend.application.usecase_templates.swancor as sw
from .swancor import *
from .public_templates import *

USER_TEMPLATES = [
    pb.pump_use_case(),
    pb.heat_exchanger_use_case(),
    pb.industrial_natural_gas_boiler_with_preheating_trains(),
    pb.steam_turbine_use_case()
]

SWANCOR_TEMPLATES = [
    sw.SWANCOR_model_1_HE_1(),
    sw.SWANCOR_model_2_HE_2(),
    sw.SWANCOR_model_3_HE_3()
]