from fastapi import Depends
from pydantic import BaseModel
from fastapi.security import OAuth2PasswordBearer


class User(BaseModel):
    username: str


class TokenPayload(BaseModel):
    user: User


oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")


def check_credentials(token: str = Depends(oauth2_scheme)) -> TokenPayload:
    # This is a placeholder - replace with actual authentication from ND when done
    # Probably depends on oauth2_scheme and should return User
    return TokenPayload(user=User(username="Test"))
