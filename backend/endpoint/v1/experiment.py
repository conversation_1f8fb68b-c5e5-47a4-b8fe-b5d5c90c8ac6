from fastapi import APIRouter, HTTPException, status, Depends, Query, Header, Request
from backend.endpoint.v1.schema import *

from backend.endpoint.v1._error_handling import handle_domain_errors
from backend.endpoint.v1.schema import ExperimentResults
import backend.main as server
import backend.application.api_services as api_svc


####################

# HELPER

router = APIRouter(prefix="/experiment")

####################

@router.post(
    "/", response_model=ExperimentResponse, status_code=status.HTTP_201_CREATED
)
@handle_domain_errors
async def execute_experiment(
    exp_config: ExperimentConfig,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
) -> ExperimentResponse:
    """
    Run a simulation experiment based on the provided configuration.

    Args:
        exp_config (ExperimentConfig): Experiment configuration parameters
        userId (str): User identifier for authorization and tracking

    Raises:
        Standard HTTP exceptions via @handle_domain_errors decorator:
        - 404: Resource not found
        - 400: Validation error
        - 500: Processing or unexpected errors
    """
    result: ExperimentResults = api_service.run_experiment(
        experiment_config=exp_config,
        user_id=userId
    )

    return ExperimentResponse(
        message= "Success",
        data = result,
        error = None
    )