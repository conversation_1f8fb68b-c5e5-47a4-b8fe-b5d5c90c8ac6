from fastapi import APIRouter, status, Depends, Query, Header, Request
from backend.endpoint.v1.schema import *

from backend.endpoint.v1._error_handling import handle_domain_errors
from backend.endpoint.v1.schema import DiagnosticResults
import backend.main as server
import backend.application.api_services as api_svc


####################

# HELPER

router = APIRouter(prefix="/troubleshoot")

####################

@router.post(
    "/",
    response_model=DiagnosticResponse,
    status_code=status.HTTP_201_CREATED,
)
@handle_domain_errors
async def execute_troubleshoot(
    diagnostic_config: DiagnosticConfig,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
) -> DiagnosticResponse:
    """
    Perform diagnostic analysis on a plant configuration.

    Args:
        diagnostic_config (DiagnosticConfig): Diagnostic configuration parameters
        userId (str): User identifier for authorization and tracking

    Returns:
        DiagnosticResponse: Results of the diagnostic analysis

    Raises:
        Standard HTTP exceptions via @handle_domain_errors decorator:
        - 404: Resource not found
        - 400: Validation error
        - 500: Processing or unexpected errors
    """
    result: DiagnosticResults = api_service.run_diagnostic(
        diagnostic_config=diagnostic_config,
        user_id=userId
    )
    return DiagnosticResponse(
        message="Success",
        data = result,
        error = None
    )
