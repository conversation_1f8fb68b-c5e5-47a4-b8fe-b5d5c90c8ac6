from functools import wraps
from fastapi import HTTPException, status
from backend.core.interfaces import AlephResourceNotFoundError, AlephValidationError, AlephProcessingError, AlephDomainError
import logging

logger = logging.getLogger(__name__)

def handle_domain_errors(func):
    """Decorator to standardize error handling across API endpoints"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except AlephResourceNotFoundError as e:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
        except AlephValidationError as e:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
        except AlephProcessingError as e:
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
        except AlephDomainError as e:
            # Catch any other domain errors
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
                               detail=f"Unexpected domain error: {str(e)}")
        except Exception as e:
            # Log unexpected errors for debugging
            logger.exception(f"Unhandled exception in {func.__name__}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred"
            )
    return wrapper
