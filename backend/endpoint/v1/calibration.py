from fastapi import APIRouter, Depends, Header, status, Request
from backend.endpoint.v1.schema import *
from backend.endpoint.v1._error_handling import handle_domain_errors
import backend.main as server
import backend.application.api_services as api_svc

#########################

# HELPER

router = APIRouter(prefix="/calibration")

#########################



@router.post(
    "/",
    response_model=CalibrationResults,
    status_code=status.HTTP_201_CREATED,
)
@handle_domain_errors
async def run_calibration(
    config: CalibrationConfig,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Update a plant configuration"""
    return api_service.run_calibration(config, userId)
