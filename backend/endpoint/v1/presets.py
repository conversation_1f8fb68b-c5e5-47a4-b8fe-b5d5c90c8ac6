from fastapi import APIRouter, HTTPException, Depends, Query, Header, status
from fastapi.responses import JSONResponse
from backend.endpoint.v1.schema import *

import backend.main as server
from backend.endpoint.v1._error_handling import handle_domain_errors
import backend.application.api_services as api_svc
####################

# HELPER
router = APIRouter(
    prefix="/presets",
)


####################


@router.get("/equipment-details", response_model=List[PresetEquipmentType])
@handle_domain_errors
async def get_equipment_types_and_defaults(
    configName: str,  # Assume this is a UUID
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get equipment types and their defaults"""
    return api_service.get_equipment_types_and_defaults(configName,userId)



@router.get("/stream-types", response_model=List[PresetStreamType])
@handle_domain_errors
async def get_stream_types(
    configName: str,  # Assume this is a UUID
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get stream types"""
    return api_service.get_stream_types(configName, userId)


@router.get("/material-types", response_model=PresetMaterialTypes)
@handle_domain_errors
async def get_material_types(
    configName: str,  # Assume this is a UUID
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get material types"""

    return api_service.get_material_types(configName, userId)


@router.get("/sensor-types", response_model=PresetSensorTypes)
@handle_domain_errors
async def get_sensor_types(
    configName: str,  # Assume this is a UUID
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get sensor types"""
    return api_service.get_sensor_types()