
from __future__ import annotations
from ._imports import *

class SimpleChart(BaseModel):
    title: str = Field(
        ...,
        description="Title of the chart",
        examples=["Temperature vs Time", "Pressure Distribution"]
    )
    pri_data: Dict[str, Dict[str, List[float]]] = Field(
        ...,
        description="Primary data series for the chart. First level keys are legends, second level contains data points.",
        examples=[{"Series 1": {"x": [1, 2, 3], "y": [10, 20, 30]}, "Series 2": {"x": [1, 2, 3], "y": [15, 25, 35]}}]
    )
    sec_data: Optional[Dict[str, Dict[str, List[float]]]] = Field(
        None,
        description="Secondary data series for the chart. Must contain same legend keys as primary data.",
        examples=[{"Series 1": {"x": [1, 2, 3], "y": [5, 15, 25]}, "Series 2": {"x": [1, 2, 3], "y": [10, 20, 30]}}]
    )
    axes: Tuple[str, str] = Field(
        ...,
        description="Labels for the x and y axes",
        examples=[("Time (s)", "Temperature (K)"), ("Position (m)", "Pressure (kPa)")]
    )
    chart_type: Literal["scatter", "line", "vertical_bar"] = Field(
        ...,
        description="Type of chart visualization to use",
        examples=["line", "scatter"]
    )
    
    @model_validator(mode='after')
    def validate_chart_data(self) -> 'SimpleChart':
        """Validate that primary and secondary data series have exactly matching legend keys"""
        if self.sec_data:
            pri_keys = set(self.pri_data.keys())
            sec_keys = set(self.sec_data.keys())
            
            if pri_keys != sec_keys:
                missing_in_sec = pri_keys - sec_keys
                extra_in_sec = sec_keys - pri_keys
                error_msg = []
                if missing_in_sec:
                    error_msg.append(f"Secondary data missing keys: {missing_in_sec}")
                if extra_in_sec:
                    error_msg.append(f"Secondary data has extra keys: {extra_in_sec}")
                raise ValueError(", ".join(error_msg))
        return self

class SimpleTable(BaseModel):
    title: str = Field(
        ...,
        description="Title of the table",
        examples=["Calibration Parameters", "Model Results"]
    )
    data: Dict[str, List[Any]] = Field(
        ...,
        description="Column-oriented data, with column names as keys and values as lists",
        examples=[{"Parameter": ["Heat Coefficient", "Pressure"], "Value": [0.85, 120.5], "Units": ["W/m²K", "kPa"]}]
    )
    summary_text: str = Field(
        ...,
        description="Text summarizing the table content",
        examples=["Table shows the calibrated parameters and their values."]
    )
