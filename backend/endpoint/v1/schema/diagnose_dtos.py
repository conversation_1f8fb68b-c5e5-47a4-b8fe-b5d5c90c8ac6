from __future__ import annotations
from ._imports import *
from .experiment_dtos import (
    EntityVariable,
    ImpactVariables,
    VariableImpactSummary,
    SetpointImpactSummary,
    ConditionImpactSummary
)

# CONFIG

class DiagnosticEntityVariable(EntityVariable):
    value: float = Field(..., examples=[50.0])

class DiagnosticEntitySpecification(BaseModel):
    entity_id: str = Field(..., examples=["HT-001", "MT-001->HT-001"])
    spec_variables: List[DiagnosticEntityVariable]
    entity_type: str = Field(..., examples=["MaterialStream", "HeatExchanger"])


class DiagnosticEntitySettings(BaseModel):
    value: List[DiagnosticEntitySpecification]


class DiagnosticKPISpecification(BaseModel):
    name: str = Field(..., examples=["Energy Usage"])
    value: str = Field(..., examples=["{{HT-001.Duty}} + {{PM-002.Duty}}"])
    range: Tuple[float, float] = Field(..., examples=[0.0, 100.0])


# ROOT
class DiagnosticConfig(BaseModel):
    title: str = Field(..., examples=["Simulate and predict experiment results"])
    config_name: str = Field(..., examples=["Ammoania Plant"])
    entity_settings: DiagnosticEntitySettings
    sim_kpi: DiagnosticKPISpecification


####################


class DiagnoseSummary(BaseModel):
    template: str = Field(
        ...,
        description="Following variables are contributing the most to this difference:",
    )
    impact_variables: ImpactVariables = Field(
        ..., description="HT-001.OutletTemperature on Equipment HT-001."
    )
    kpi_improvment: float = Field(..., examples=[85])


class Assessment(BaseModel):
    lower_bound: float = Field(..., examples=[0])
    upper_bound: float = Field(..., examples=[100])
    current_kpi: float = Field(..., examples=[50])
    expected_kpi: float = Field(..., examples=[75])


class VariableDiagnosis(BaseModel):
    entity_type: str = Field(
        ...,
        examples=[
            "Heat Exchanger",
        ],
    )
    entity: str = Field(..., examples=["HT-001"])
    name: str = Field(..., examples=["HT-001.OutletTemperature"])
    current_value: float = Field(..., examples=[30.0])
    recommended_value: float = Field(..., examples=[50.0])
    diagnosis_text: str = Field(
        ...,
        examples=[
            "Potential causes included: \n\n 1. Flow rate is not operated in a proper operating condition. \n 2. Maintenance on the equipment has passed the recommended period"
        ],
    )
    unit: str = Field(..., examples=["K"])


class DiagnosisSummary(BaseModel):
    diagnosed_variables: List[VariableDiagnosis]

# ROOT
class DiagnosticResults(BaseModel):
    main_summary_text: str = Field(
        ...,
        examples=[
            "Based on the simulations performed, following variables have the highest impact: "
        ],
    )
    top_summary_text: str = Field(
        ..., examples=["Focus on the following varibles to achieve top 5'%' of KPI."]
    )
    top_impact: dict = Field(..., examples=[{"HT-001.OutletTemperature": 100.0}])
    top_variables: List[VariableImpactSummary]
    assessment_summary: str = Field()
    assessment: Assessment
    sensitivity_analysis_text: str = Field(
        ...,
        examples=[
            "Sensitivity analysis graph representing the contribution of each variable on preventing user to achieve the desired energy efficiency: "
        ],
    )
    setpoint_impact_summary: List[SetpointImpactSummary]
    condition_impact_summary: List[ConditionImpactSummary]
    analysis: Union[List[VariableDiagnosis], str]


class DiagnosticResponse(BaseModel):
    message: str
    data: Union[DiagnosticResults, str]
    error: Optional[str]
