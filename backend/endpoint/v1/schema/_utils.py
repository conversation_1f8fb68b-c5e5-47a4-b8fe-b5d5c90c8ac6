import base64
import io
import pandas as pd
from typing import Dict, Any

def decode_base64_to_bytes(base64_data: str) -> bytes:
    """
    Decodes a Base64 encoded string into bytes.
    
    Args:
        base64_data: Base64 encoded string data
        
    Returns:
        bytes: The decoded data as bytes
        
    Raises:
        ValueError: If decoding fails
    """
    try:
        return base64.b64decode(base64_data)
    except Exception as e:
        raise ValueError(f"Failed to decode Base64 data: {str(e)}")

def base64_to_dataframe(base64_data: str, **pandas_options) -> pd.DataFrame:
    """
    Converts a Base64 encoded CSV string directly to a pandas DataFrame.
    
    Args:
        base64_data: Base64 encoded CSV data as a string
        **pandas_options: Additional options to pass to pd.read_csv()
        
    Returns:
        pd.DataFrame: The decoded CSV data as a pandas DataFrame
        
    Raises:
        ValueError: If conversion to DataFrame fails
        
    Example:
        ```python
        df = base64_to_dataframe(base64_string, index_col=0)
        ```
    """
    try:
        # Decode the Base64 string to bytes
        csv_bytes = decode_base64_to_bytes(base64_data)
        
        # Read the bytes with pandas
        return pd.read_csv(io.BytesIO(csv_bytes), **pandas_options)
    except Exception as e:
        raise ValueError(f"Failed to convert Base64 data to DataFrame: {str(e)}")
