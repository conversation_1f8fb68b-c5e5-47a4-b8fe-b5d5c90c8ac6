from __future__ import annotations
from ._imports import *

class BaseEntityVariable(BaseModel):
    name: str = Field(..., examples=["Outlet Temperature"])
    entity_name: str = Field(..., examples=["HT-001", "MAT-01->HT-001"])
    type: str = Field(..., examples=["Setpoint"])
    bounds: Tuple[float, float]
    unit: str = Field(..., examples=["K"])


class EntityVariable(BaseEntityVariable):
    ranges: Tuple[float, float]


class EntitySpecification(BaseModel):
    entity_id: str = Field(..., examples=["HT-001", "Feed Stream"])
    spec_variables: List[EntityVariable]
    entity_type: str = Field(..., examples=["MaterialStream", "HeatExchanger"])


class EntitySettings(BaseModel):
    value: List[EntitySpecification]


class KPISpecification(BaseModel):
    name: str = Field(..., examples=["Energy Usage"])
    value: str = Field(..., examples=["{{HT-001.Duty}} + {{PM-002.Duty}}"])


# ROOT
class ExperimentConfig(BaseModel):
    title: str = Field(..., examples=["Simulate and predict experiment results"])
    config_name: str = Field(..., examples=["Ammonia Plant"])
    entity_settings: EntitySettings
    sim_kpi: KPISpecification

#############################

class VariableImpactSummary(BaseModel):
    name: str = Field(..., examples=["HT-001.OutletTemperature"])
    entity: str = Field(..., examples=["HT-001"])
    type: str = Field(..., examples=["Setpoint"])
    value: float = Field(..., examples=[200.0])
    impact_value: float = Field(..., ge=0.0, le=100.0, examples=[50.0])
    unit: str = Field(..., examples=["K"])



class ImpactVariables(BaseModel):
    summary_text: str = Field(
        ..., description="Focus on the following varibles to achieve top 5'%' of KPI."
    )
    top_variables: List[VariableImpactSummary] = Field(
        ..., description="List of top selected variables with their imapct summary."
    )
    selected_variables: List[VariableImpactSummary] = Field(
        ..., description="List of all selected variables with their impact summary."
    )



class SetpointImpactSummary(BaseModel):
    entity: str = Field(..., examples=["HEX-100"])
    setpoint: str = Field(..., examples=["cold_fluid_temperature"])
    weightage: float = Field(..., examples=[24.244])
    unit: str = Field(..., examples=["K"])


class ConditionImpactSummary(BaseModel):
    entity: str = Field(..., examples=["HEX-100"])
    condition: str = Field(..., examples=["cold_fluid_temperature"])
    weightage: float = Field(..., examples=[24.244])
    unit: str = Field(..., examples=["K"])



class SimulationSummary(BaseModel):
    base_template: str = Field(..., description="The fixed text block to be displayed.")
    template: str = Field(..., description="The template string with placeholder.")
    impact_variables: ImpactVariables = Field(
        ..., description="A list of impact variables along with their information"
    )


class SimulatedVariable(BaseModel):
    name: str = Field(..., examples=["HT-001.OutletTemperature"])
    type: str = Field(..., examples=["Setpoint"])
    value: float = Field(..., examples=[200.0])
    unit: str = Field(..., examples=["K"])



class SimulatedEntity(BaseModel):
    entity: str = Field(..., examples=["HT-001"])
    variables: List[SimulatedVariable]


class SimulatedScenario(BaseModel):
    scenario: str = Field(..., examples=["Scenario 1"])
    entity_specification: List[SimulatedEntity]
    kpi: str = Field(..., examples=["KPI"])
    kpi_value: float = Field(..., examples=[60.0])


class SimulatedData(BaseModel):
    simulated_data: List[SimulatedScenario]


# ROOT
class ExperimentResults(BaseModel):
    main_summary_text: str = Field(
        ...,
        examples=[
            "Based on the simulations performed, following variables have the highest impact:"
        ],
    )
    top_summary_text: str = Field(
        ..., examples=["Focus on the following varibles to achieve top 5'%' of KPI."]
    )
    top_impact: dict = Field(..., examples=[{"HT-001.OutletTemperature": 100.0}])
    top_variables: List[VariableImpactSummary]
    impact_summary_text: str = Field(
        ...,
        examples=[
            "Based on the simulation, following weightages are assigned based on how each variable impacts the KPI. Prioritize analyzing the variables with higher weightages. "
        ],
    )
    setpoint_impact_summary: List[SetpointImpactSummary]
    condition_impact_summary: List[ConditionImpactSummary]
    simulated_summary: SimulatedData

class ExperimentResponse(BaseModel):
    message: str
    data: Union[ExperimentResults, str]
    error: Optional[str]
