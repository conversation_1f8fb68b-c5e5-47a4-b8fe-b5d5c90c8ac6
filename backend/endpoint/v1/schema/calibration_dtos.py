# TODO  - refactor organixatin to be CONFIGs, RESULTS, DOMAIN, 

from __future__ import annotations
from ._imports import *
from .viz_dto import Simple<PERSON>hart, SimpleTable


class CalibrationConfig(BaseModel):
    user_id: str = Field(
        ...,
        description="Identifier for the user performing the calibration",
        examples=["user-123", "admin-456"]
    )
    atlas_label: str = Field(
        ...,
        description="Label identifying the Atlas model to be calibrated",
        examples=["heat_exchanger_model", "reactor_simulation_v2"]
    )
    conditions_to_calibrate: List[uuid.UUID] = Field(
        ...,
        description="List of UUIDs identifying the conditions to be calibrated",
        examples=[["123e4567-e89b-12d3-a456-************", "98765432-e89b-12d3-a456-************"]]
    )
    conditions_to_fix: Dict[uuid.UUID, float] = Field(
        ...,
        description="Dictionary mapping condition UUIDs to fixed values",
        examples=[{"123e4567-e89b-12d3-a456-************": 100.0, "98765432-e89b-12d3-a456-************": 200.0}]
    )
    calibration_samples: str = Field(
        ...,
        description="Base64 encoded CSV data containing calibration measurements",
        examples=["base64_encoded_string_here"]
    )

class CalibrationResults(BaseModel):
    summary_text: str = Field(
        ...,
        description="Overall summary text of the calibration results",
        examples=["Calibration completed successfully with convergence after 12 iterations."]
    )
    analysis_text: str = Field(
        ...,
        description="Detailed analysis text of the calibration process and results",
        examples=["The model shows good agreement with experimental data with RMSE of 2.3%."]
    )
    chart1: SimpleChart = Field(
        ...,
        description="Primary chart for visualizing calibration results (typically line chart)",
    )
    chart2: SimpleChart = Field(
        ...,
        description="Secondary chart for visualizing calibration results (typically bar chart)",
    )
    table: SimpleTable = Field(
        ...,
        description="Table containing calibration parameters and results",
    )