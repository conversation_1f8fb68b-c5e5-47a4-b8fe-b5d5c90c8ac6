from fastapi import APIRouter, Depends, Header, status, Request, BackgroundTasks
from backend.endpoint.v1.schema import *
from backend.endpoint.v1._error_handling import handle_domain_errors
import backend.main as server
import backend.application.api_services as api_svc

#########################

# HELPER

router = APIRouter(prefix="/plant-configurations")

#########################


@router.get(
    "/{configName}",
    response_model=PlantConfiguration,
    status_code=status.HTTP_200_OK,
)
@handle_domain_errors
async def get_plant_configuration(
    configName: str,  # Assume this is a UUID
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
) -> PlantConfiguration:
    """Get existing plant configuration"""
    return api_service.get_plant_configuration(configName, userId)


@router.post("/{configName}", status_code=status.HTTP_201_CREATED)
@handle_domain_errors
async def update_plant_configuration(
    configName: str,
    config: PlantConfiguration,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Update a plant configuration"""
    api_service.update_plant_configuration(configName, config, userId)


@router.get(
    "/",
    response_model=List[PlantConfig_Metadata],
    status_code=status.HTTP_200_OK,
)
@handle_domain_errors
async def get_plant_configurations(
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get metadata for all plant configurations for a user"""
    return api_service.get_plant_configurations(userId)


@router.post("/", status_code=status.HTTP_201_CREATED)
@handle_domain_errors
async def create_plant_configuration(
    metadata: PlantConfig_Metadata,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Create an empty plant configuration"""
    api_service.create_plant_configuration(metadata, userId)


@router.delete("/{configName}", status_code=status.HTTP_204_NO_CONTENT)
@handle_domain_errors
async def delete_plant_configuration(
    configName: str,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Delete a plant configuration"""
    api_service.delete_plant_configuration(configName, userId)


@router.get(
    "/{configName}/sensors",
    response_model=SensorMappingConfig,
    status_code=status.HTTP_200_OK,
)
@handle_domain_errors
async def get_sensor_mappings(
    configName: str,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get sensor mappings for a plant configuration"""
    return api_service.get_sensor_mappings(configName, userId)


@router.post("/{configName}/sensors", status_code=status.HTTP_201_CREATED)
@handle_domain_errors
async def update_sensor_mappings(
    configName: str,
    sensor_mappings: SensorMappingConfig,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Update sensor mappings for a plant configuration"""
    api_service.update_sensor_mappings(configName, userId, sensor_mappings)


@router.get(
    "/{configName}/kpi-variables",
    response_model=List[KPIVariableItem],
    status_code=status.HTTP_200_OK,
)
@handle_domain_errors
async def get_kpi_variables(
    configName: str,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get KPI variables for a plant configuration. These are the building blocks for users to build their custom KPI expressions."""
    return api_service.get_kpi_variables(configName, userId)


@router.get(
    "/{configName}/kpis",
    response_model=List[KPIItem],
    status_code=status.HTTP_200_OK,
)
@handle_domain_errors
async def get_kpis(
    configName: str,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get KPIs defined for a plant configuration"""
    return api_service.get_kpis(configName, userId)


@router.post("/{configName}/kpis", status_code=status.HTTP_201_CREATED)
@handle_domain_errors
async def update_kpis(
    configName: str,
    kpis: List[KPIItem],
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Update KPI definitions for a plant configuration"""
    api_service.update_kpis(configName, kpis, userId)


@router.post(
    "/{configName}/model-setup/training-setup", status_code=status.HTTP_201_CREATED
)
@handle_domain_errors
async def run_surrogate_training(
    configName: str,
    training_configuration: SurrogateTrainingConfig,
    background_tasks: BackgroundTasks,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Configure and start a model training job"""
    background_tasks.add_task(train_surrogate_model_task, userId, configName, training_configuration, api_service)

def train_surrogate_model_task(userId: str, configName: str, training_configuration: SurrogateTrainingConfig, api_service: api_svc.APIService):
    """Configure and start a model training job"""
    api_service.train_surrogate_model(
        userId,
        configName,
        training_configuration,
    )

@router.get(
    "/{configName}/model-setup/training-setup",
    response_model=SurrogateTrainingConfig,
    status_code=status.HTTP_200_OK,
)
@handle_domain_errors
async def get_surrogate_training_configuration(
    configName: str,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get current model training configuration"""
    return api_service.get_surrogate_training_config(configName, userId)


@router.get(
    "/{configName}/model-setup/training-status",
    response_model=Status,
    status_code=status.HTTP_200_OK,
)
@handle_domain_errors
async def get_surrogate_status(
    configName: str,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Check status of model training process"""
    return api_service.get_surrogate_training_status(configName, userId)


@router.get(
    "/{configName}/model-setup/training-results",
    response_model=SurrogateTrainingResults,
    status_code=status.HTTP_200_OK,
)
@handle_domain_errors
async def get_model_training_results(
    configName: str,
    userId: str = Header(..., description="User ID for organization"),
    api_service: api_svc.APIService = Depends(server.get_api_service),
):
    """Get results and metrics from model training process"""
    return api_service.get_surrogate_training_results(configName, userId)
