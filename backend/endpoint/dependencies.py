from fastapi import Request
from typing import Callable
from backend.config.z_loader import MetisConfig
from backend.endpoint.v1.plant_configurations import z_AtlasService
from functools import lru_cache


@lru_cache()
def get_dependencies():
    """Central dependency provider"""
    return {
        "get_metis_config": get_metis_config,
        "get_atlas_service": get_atlas_service,
    }


def get_metis_config(request: Request) -> MetisConfig:
    """Get Metis configuration from app state"""
    return request.app.state.config.metis


def get_atlas_service(request: Request) -> z_AtlasService:
    """Get Atlas service from app state"""
    return z_AtlasService(request.app.state.uow)
