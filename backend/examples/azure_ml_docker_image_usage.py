"""
Azure ML Docker Image Management Usage Example

This example demonstrates how to use the enhanced TrainerRegistry
for Docker image management in Azure ML training scenarios.

Key Features Demonstrated:
- Getting appropriate Docker images for different algorithms
- Integrating with Azure ML training runners
- Custom image registration for specific requirements
- Cross-platform image management (Azure, local, AWS, GCP)
"""

import logging
from typing import Dict, Any

from backend.core._surrogate._enums import EnumSurrogateAlgorithm
from backend.core._surrogate.trainers.trainer_registry import (
    TrainerRegistry, 
    get_default_trainer_registry
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AzureMLImageManager:
    """
    Helper class for managing Docker images in Azure ML training scenarios.
    
    This class demonstrates practical usage of the TrainerRegistry's
    Docker image management capabilities for Azure ML workloads.
    """
    
    def __init__(self, trainer_registry: TrainerRegistry = None):
        """
        Initialize the Azure ML image manager.
        
        Args:
            trainer_registry: Optional custom trainer registry. Uses default if None.
        """
        self.registry = trainer_registry or get_default_trainer_registry()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def get_training_environment_config(self, 
                                      algorithm: EnumSurrogateAlgorithm,
                                      training_service: str = "azure") -> Dict[str, Any]:
        """
        Get complete environment configuration for Azure ML training.
        
        Args:
            algorithm: The surrogate algorithm to train
            training_service: Target training service (default: "azure")
            
        Returns:
            Dictionary containing environment configuration
        """
        try:
            # Get the appropriate base image
            base_image = self.registry.get_base_image(algorithm, training_service)
            
            # Get trainer class for additional metadata
            trainer_class = self.registry.get_trainer_class(algorithm)
            
            # Build environment configuration
            config = {
                "base_image": base_image,
                "algorithm": algorithm.value,
                "trainer_class": trainer_class.__name__,
                "training_service": training_service,
                "supported_services": self.registry.get_supported_training_services(algorithm),
                "environment_metadata": self._get_image_metadata(base_image, training_service)
            }
            
            self.logger.info(f"Generated environment config for {algorithm.value} on {training_service}")
            return config
            
        except Exception as e:
            self.logger.error(f"Failed to generate environment config: {e}")
            raise
    
    def _get_image_metadata(self, image_name: str, training_service: str) -> Dict[str, Any]:
        """
        Extract metadata about the Docker image based on service and name.
        
        Args:
            image_name: Docker image name/URI
            training_service: Training service type
            
        Returns:
            Dictionary containing image metadata
        """
        metadata = {
            "image_name": image_name,
            "service": training_service,
            "framework": "unknown",
            "cuda_version": "unknown",
            "python_version": "unknown"
        }
        
        # Parse Azure ML curated environment names
        if training_service == "azure" and "acpt-pytorch" in image_name:
            parts = image_name.split("-")
            if len(parts) >= 3:
                metadata["framework"] = f"pytorch-{parts[2]}"
                if len(parts) >= 4:
                    metadata["cuda_version"] = parts[3]
        
        # Parse official PyTorch images
        elif "pytorch/pytorch" in image_name:
            # Format: pytorch/pytorch:2.4.1-cuda12.1-cudnn9-devel
            if ":" in image_name:
                tag = image_name.split(":")[-1]
                parts = tag.split("-")
                if len(parts) >= 1:
                    metadata["framework"] = f"pytorch-{parts[0]}"
                if len(parts) >= 2 and "cuda" in parts[1]:
                    metadata["cuda_version"] = parts[1]
        
        return metadata
    
    def setup_custom_pytorch_environment(self, 
                                       pytorch_version: str = "2.4.1",
                                       cuda_version: str = "12.1") -> str:
        """
        Set up a custom PyTorch environment for local development.
        
        Args:
            pytorch_version: PyTorch version to use
            cuda_version: CUDA version to use
            
        Returns:
            Custom image name that was registered
        """
        custom_image = f"pytorch/pytorch:{pytorch_version}-cuda{cuda_version}-cudnn9-devel"
        
        # Register the custom image for local development
        self.registry.register_base_image(
            EnumSurrogateAlgorithm.RNN_TS, 
            "local", 
            custom_image
        )
        
        self.logger.info(f"Registered custom PyTorch environment: {custom_image}")
        return custom_image
    
    def get_cross_platform_images(self, algorithm: EnumSurrogateAlgorithm) -> Dict[str, str]:
        """
        Get Docker images for an algorithm across all supported platforms.
        
        Args:
            algorithm: The algorithm to get images for
            
        Returns:
            Dictionary mapping platform names to image URIs
        """
        images = {}
        supported_services = self.registry.get_supported_training_services(algorithm)
        
        for service in supported_services:
            try:
                image = self.registry.get_base_image(algorithm, service)
                images[service] = image
            except Exception as e:
                self.logger.warning(f"Could not get image for {service}: {e}")
                images[service] = None
        
        return images


def demonstrate_azure_ml_integration():
    """
    Demonstrate practical Azure ML integration with Docker image management.
    """
    print("\n" + "="*70)
    print("Azure ML Docker Image Management Integration Demo")
    print("="*70)
    
    # Initialize the image manager
    image_manager = AzureMLImageManager()
    
    # Example 1: Get Azure ML environment configuration for RNN training
    print("\n1. Azure ML Environment Configuration for RNN Training:")
    config = image_manager.get_training_environment_config(
        EnumSurrogateAlgorithm.RNN_TS, 
        "azure"
    )
    
    print(f"   Base Image: {config['base_image']}")
    print(f"   Algorithm: {config['algorithm']}")
    print(f"   Trainer Class: {config['trainer_class']}")
    print(f"   Framework: {config['environment_metadata']['framework']}")
    print(f"   CUDA Version: {config['environment_metadata']['cuda_version']}")
    
    # Example 2: Set up custom local development environment
    print("\n2. Setting up Custom Local Development Environment:")
    custom_image = image_manager.setup_custom_pytorch_environment("2.4.1", "12.1")
    print(f"   Custom Image: {custom_image}")
    
    # Verify the custom environment
    local_config = image_manager.get_training_environment_config(
        EnumSurrogateAlgorithm.RNN_TS, 
        "local"
    )
    print(f"   Updated Local Image: {local_config['base_image']}")
    
    # Example 3: Cross-platform image comparison
    print("\n3. Cross-Platform Image Comparison for RNN Training:")
    cross_platform = image_manager.get_cross_platform_images(EnumSurrogateAlgorithm.RNN_TS)
    
    for platform, image in cross_platform.items():
        if image:
            print(f"   {platform.upper():8}: {image}")
        else:
            print(f"   {platform.upper():8}: Not available")
    
    # Example 4: Azure ML job configuration template
    print("\n4. Azure ML Job Configuration Template:")
    azure_config = image_manager.get_training_environment_config(
        EnumSurrogateAlgorithm.RNN_TS, 
        "azure"
    )
    
    job_template = {
        "environment": {
            "name": f"surrogate-training-{azure_config['algorithm']}",
            "image": azure_config['base_image'],
            "description": f"Environment for {azure_config['algorithm']} training"
        },
        "compute": {
            "target": "gpu-cluster",
            "instance_type": "Standard_NC16as_T4_v3"
        },
        "code": {
            "local_path": "./training_code"
        },
        "command": f"python train_{azure_config['algorithm']}.py",
        "metadata": azure_config['environment_metadata']
    }
    
    print("   Azure ML Job Template:")
    for key, value in job_template.items():
        if isinstance(value, dict):
            print(f"     {key}:")
            for subkey, subvalue in value.items():
                print(f"       {subkey}: {subvalue}")
        else:
            print(f"     {key}: {value}")
    
    print("\n" + "="*70)
    print("Integration demonstration completed successfully!")
    print("="*70)


def demonstrate_image_discovery_workflow():
    """
    Demonstrate the workflow for discovering and validating Azure ML images.
    """
    print("\n" + "="*70)
    print("Azure ML Image Discovery Workflow")
    print("="*70)
    
    print("\n📋 Step-by-Step Guide to Find Compatible Images:")
    
    print("\n1. Check Current Registry Mappings:")
    registry = get_default_trainer_registry()
    summary = registry.get_algorithm_image_summary()
    
    for algorithm, services in summary.items():
        print(f"   {algorithm.value}:")
        for service, image in services.items():
            print(f"     {service}: {image}")
    
    print("\n2. Azure CLI Commands to Discover More Images:")
    print("   # List all environments in your workspace")
    print("   az ml environment list --resource-group <your-rg> --workspace-name <your-workspace>")
    print("")
    print("   # Filter for PyTorch environments")
    print("   az ml environment list --query \"[?contains(name, 'pytorch')]\" --output table")
    print("")
    print("   # Get specific environment details")
    print("   az ml environment show --name <env-name> --version <version>")
    
    print("\n3. Python SDK Commands for Environment Discovery:")
    print("""
   from azure.ai.ml import MLClient
   from azure.identity import DefaultAzureCredential
   
   ml_client = MLClient(
       credential=DefaultAzureCredential(),
       subscription_id="<your-subscription>",
       resource_group_name="<your-rg>",
       workspace_name="<your-workspace>"
   )
   
   # List all environments
   environments = ml_client.environments.list()
   
   # Filter for PyTorch environments
   pytorch_envs = [env for env in environments if 'pytorch' in env.name.lower()]
   
   for env in pytorch_envs:
       print(f"Name: {env.name}, Version: {env.version}")
       env_details = ml_client.environments.get(env.name, env.version)
       print(f"Image: {env_details.image}")
   """)
    
    print("\n4. Recommended Images for PyTorch 2.4.1+cu121:")
    recommendations = [
        ("Azure ML", "acpt-pytorch-2.2-cuda12.1", "Closest available ACPT version"),
        ("Local Dev", "pytorch/pytorch:2.4.1-cuda12.1-cudnn9-devel", "Official PyTorch image"),
        ("Custom", "mcr.microsoft.com/azureml/pytorch-2.4-cuda12.1", "If available in registry"),
    ]
    
    for platform, image, note in recommendations:
        print(f"   {platform:10}: {image}")
        print(f"   {' '*12}({note})")
    
    print("\n" + "="*70)


if __name__ == "__main__":
    # Run the demonstrations
    demonstrate_azure_ml_integration()
    demonstrate_image_discovery_workflow()
