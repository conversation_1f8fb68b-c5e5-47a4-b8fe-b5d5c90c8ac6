"""
To launch server:
    terminal: `fastapi dev`
    or `fastapi run`
To Test:
 http://localhost:8000/docs
 http://127.0.0.1:8000/redoc
"""

from attr import dataclass
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
import backend.application.api_services as api_svc


@dataclass
class FastAPIConfig:
    title: str = "Aleph API"
    description: str = "Process Engineering API"
    version: str = "1.0.0"
    debug: bool = False
    api_prefix: str = "/api/v1"

fastapi_config = FastAPIConfig()


def create_app(config: FastAPIConfig) -> FastAPI:
    """Application factory with configuration and dependencies"""
    app = FastAPI(
        title=config.title,
        description=config.description,
        version=config.version,
        debug=config.debug,
    )

    # Initialize API service
    api_service: api_svc.APIService = api_svc.APIService()
    app.state.api_service = api_service

    # Load templates
    app.state.api_service.iatlas.load_templates()

    return app


def setup_routes(app: FastAPI, config: FastAPIConfig) -> None:
    """Setup routes after app creation to avoid circular imports"""
    from backend.endpoint.v1.presets import router as preset_page_router
    from backend.endpoint.v1.plant_configurations import router as config_page_router
    from backend.endpoint.v1.experiment import router as exp_page_router
    from backend.endpoint.v1.diagnosis import router as trbl_page_router
    from backend.endpoint.v1.calibration import router as calib_page_router

    for router in [
        config_page_router,
        preset_page_router,
        exp_page_router,
        trbl_page_router,
        calib_page_router
    ]:
        app.include_router(router, prefix=config.api_prefix)


def get_api_service(request: Request) -> api_svc.APIService:
    return request.app.state.api_service


# Create application
app = create_app(fastapi_config)
setup_routes(app, fastapi_config)

if __name__ == "__main__":
    @app.get("/")
    def read_root():
        return {"Hello": "Alephian"}
