[tool.poetry]
name = "sonic"
version = "0.1.0"
description = "base python environment for Aleph codebase"
authors = ["lennardong <<EMAIL>>"]
license = "MIT"
include = []

[tool.poetry.dependencies]
python = "~3.8"

# DS
matplotlib="*"
numpy = "*"
pandas = "*"
scikit-learn = "^1.3.2"
scikit-optimize = "^0.9.0"
scipy = "^1.10.1"
seaborn = "~0.13.2"
networkx = "*"
pyswarms = "~1.3.0"
pydacefit = "~1.0.1"


# DOTNET
pythonnet = "=3.0.3"

# WEB FRAMEWORKS
uvicorn = { version = "~0.27.1", extras = ["standard"] }
fastapi = {extras = ["standard"], version = "^0.115.0"}

# SERIALIZATION
dataclasses_json = "~0.6.4"
jsonpickle = "~3.0.2"
marshmallow = "~3.19.0"

# DATABASE
mysql-connector-python = "~8.0.31"
pyvis = "^0.3.2"
fuzzywuzzy = {extras = ["speedup"], version = "^0.18.0"}
ipython = "^7.31.1"
pipdeptree = "^2.23.1"
salib = "^1.4"
sqlalchemy = "^2.0.35"
psycopg2 = "^2.9.9"
ordered-set = "^4.1.0"


[[tool.poetry.source]]
name = "PyPI"
priority = "supplemental"

[[tool.poetry.source]]
name = "pypi-sg"
url = "https://pypi.sgp1.vultrobjects.com/simple/"
priority = "primary"

[[tool.poetry.source]]
name = "nvidia"
url = "https://pypi.ngc.nvidia.com"
priority = "supplemental"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

# Explanation of Version Specifiers:
# ^: Allows updates that do not modify the leftmost non-zero element. For example, ^1.2.3 permits anything from 1.2.3 to, but not including, 2.0.0.
# =: Pins to an exact version.
# ~: The tilde specifier allows patch-level changes if a minor version is specified or minor-level changes if only a major version is specified. For Python, ~3.8 ensures any version 3.8.x is acceptable, without moving to 3.9.

