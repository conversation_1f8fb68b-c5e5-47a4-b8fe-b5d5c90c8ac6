{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["import os\n", "from datetime import datetime\n", "from pathlib import Path\n", "\n", "# Dynamic .NET interop library\n", "import clr\n", "\n", "# Python's way of using .NET environment and I/O classes\n", "from System import Environment, String  # type: ignore\n", "from System.IO import Directory, File, Path  # type: ignore"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Import internal utility functions\n", "# from source.backend._sharedutils import Logger\n", "from source.backend._sharedutils.Utilities import get_function_name\n", "\n", "# Import domain models\n", "from source.backend.atlas_domainmodel.aggregates._base import *\n", "from source.backend.atlas_domainmodel.entities.equipment import *\n", "from source.backend.atlas_domainmodel.entities.materialstream import *\n", "from source.backend.atlas_domainmodel.valueobjects.all import *"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Path to the DWSIM libraries\n", "dwsimpath = \"/usr/local/lib/dwsim/\""]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["<System.Reflection.RuntimeAssembly object at 0x7f26ab7cb180>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"CapeOpen.dll\")  # type: ignore\n", "clr.AddReference(dwsimpath + \"DWSIM.Automation.dll\")  # type: ignore\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.Interfaces.dll\")  # type: ignore\n", "clr.AddReference(dwsimpath + \"DWSIM.GlobalSettings.dll\")  # type: ignore\n", "clr.AddReference(dwsimpath + \"DWSIM.SharedClasses.dll\")  # type: ignore\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")  # type: ignore\n", "clr.AddReference(dwsimpath + \"DWSIM.UnitOperations.dll\")  # type: ignore\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Inspector.dll\")  # type: ignore\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"System.Buffers.dll\")  # type: ignore\n", "# clr.AddReference(dwsimpath + \"DWSIM.Thermodynamics.ThermoC.dll\")  # type: ignore"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Importing now available CLR namespaces\n", "from DWSIM.Automation import Automation3  # type: ignore\n", "from DWSIM.GlobalSettings import Settings  # type: ignore\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType  # type: ignore\n", "from DWSIM.Thermodynamics import PropertyPackages, Streams  # type: ignore\n", "from DWSIM.UnitOperations import UnitOperations  # type: ignore\n", "from System import Array  # type: ignore"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# Importing specific backend domain models\n", "import source.backend.atlas_domainmodel.entities.equipment as equipment\n", "from source.backend.atlas_domainmodel.policies.equipment_and_stream_policies import CalcPolicy\n", "from source.backend.atlas_domainmodel.valueobjects.all import PropertyVO"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class _dwsim_map():\n", "    object_type: ObjectType\n", "    unit_operation: UnitOperations\n", "\n", "_atlas_equipment_2_dwsim = {\n", "    # Example mapping, extend this as per actual needs\n", "    # TODO - to complete map. \n", "    equipment.Pump: _dwsim_map(ObjectType.Pump, UnitOperations.Pump),\n", "    equipment.Heater: _dwsim_map(ObjectType.Heater, UnitOperations.Heater),\n", "    equipment.Vessel: _dwsim_map(ObjectType.Vessel, UnitOperations.Vessel),\n", "    equipment.Cooler: _dwsim_map(ObjectType.Cooler, UnitOperations.Cooler),\n", "    equipment.OrificePlate: _dwsim_map(ObjectType.OrificePlate, UnitOperations.OrificePlate),\n", "    equipment.HeatExchanger: _dwsim_map(ObjectType.HeatExchanger, UnitOperations.HeatExchanger),\n", "    # equipment.StreamSplitter: _dwsim_map(ObjectType.NodeOut, UnitOperations.NodeOut),\n", "    equipment.Compressor: _dwsim_map(ObjectType.Compressor, UnitOperations.Compressor),\n", "    equipment.Expander: _dwsim_map(ObjectType.Expander, UnitOperations.Expander),\n", "    equipment.Valve: _dwsim_map(ObjectType.Valve, UnitOperations.Valve),\n", "    # equipment.StreamMixer: _dwsim_map(ObjectType.NodeIn, UnitOperations.NodeIn),\n", "}"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "NodeIn", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[29], line 7\u001b[0m\n\u001b[1;32m      1\u001b[0m _atlas_ms_2_dwsim \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m      2\u001b[0m     \u001b[38;5;66;03m# equipment.MaterialStream: _dwsim_map(ObjectType.MaterialStream,UnitOperations.MaterialStream),\u001b[39;00m\n\u001b[1;32m      3\u001b[0m }\n\u001b[1;32m      5\u001b[0m _atlas_equipment_2_dwsim \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m      6\u001b[0m     \u001b[38;5;66;03m# equipment.StreamSplitter: _dwsim_map(ObjectType.NodeOut, UnitOperations.NodeOut),\u001b[39;00m\n\u001b[0;32m----> 7\u001b[0m     equipment\u001b[38;5;241m.\u001b[39mStreamMixer: _dwsim_map(ObjectType\u001b[38;5;241m.\u001b[39mNodeIn, \u001b[43mUnitOperations\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mNodeIn\u001b[49m),\n\u001b[1;32m      8\u001b[0m }\n", "\u001b[0;31mAttributeError\u001b[0m: NodeIn"]}], "source": ["_atlas_ms_2_dwsim = {\n", "    # equipment.MaterialStream: _dwsim_map(ObjectType.MaterialStream,UnitOperations.MaterialStream),\n", "}\n", "\n", "_atlas_equipment_2_dwsim = {\n", "    # equipment.StreamSplitter: _dwsim_map(ObjectType.NodeOut, UnitOperations.NodeOut),\n", "    equipment.StreamMixer: _dwsim_map(ObjectType.NodeIn, UnitOperations.NodeIn),\n", "}"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# Mapping from 'CalcPolicy' enumeration to DWSIM string names for unit operation calculation modes\n", "_atlas_calcpolicy_2_dwsim = {\n", "    # Map the 'OutletTemperature' policy to its corresponding setting in DWSIM\n", "    CalcPolicy.OutletPressure: \"OutletPressure\", # For Pump\n", "    CalcPolicy.OutletTemperature: \"OutletTemperature\", # Heater\n", "    CalcPolicy.Inlet_Minimum: \"Inlet_Minimum\", # <PERSON><PERSON>el\n", "    CalcPolicy.OutletTemperature: \"OutletTemperature\", # Cooler\n", "    CalcPolicy.Pressure_Tappings_Flange: \"Pressure_Tappings_Flange\", # OrificePlate\n", "    CalcPolicy.CalculateOutletTemperatures_UA: \"CalculateOutletTemperatures_UA\", # HEX\n", "    CalcPolicy.Stream_Split_Ratios: \"Stream_Split_Ratios\", # Splitter\n", "    CalcPolicy.OutletPressure_Compressor: \"OutletPressure_Compressor\", # Compressor\n", "    CalcPolicy.OutletPressure_Expander: \"OutletPressure_Expander\", # Expander\n", "    CalcPolicy.ThermodynamicProcess_Adiabatic: \"ThermodynamicProcess_Adiabatic\", # Compressor\n", "    CalcPolicy.ThermodynamicProcess_Adiabatic: \"ThermodynamicProcess_Adiabatic\", # Expander\n", "    CalcPolicy.PressureDrop_Valve: \"PressureDrop_Valve\", # Valve\n", "    CalcPolicy.Inlet_Minimum: \"Inlet_Minimum\" # Mixer\n", "}"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from source.backend.atlas_domainmodel.policies.equipment_and_stream_policies import ParameterPolicyVO\n", "_atlas_param_2_dwsim = {\n", "    # For Pump\n", "    ParameterPolicyVO.OutletPressure: \"OutletPressure\",\n", "    ParameterPolicyVO.Efficiency : \"Effciency\",\n", "\n", "    # For Heater\n", "    ParameterPolicyVO.OutletTemperature: \"OutletTemperature\",\n", "    ParameterPolicyVO.Efficiency: \"Efficiency\",\n", "    ParameterPolicyVO.PressureDrop: \"PressureDrop\",\n", "    \n", "    # For Compressor\n", "    ParameterPolicyVO.OutletPressure: \"OutletPressure\",\n", "    ParameterPolicyVO.AdiabaticEff : \"AdiabaticEff\",\n", "\n", "    # For Expander\n", "    ParameterPolicyVO.OutletPressure: \"OutletPressure\",\n", "    ParameterPolicyVO.AdiabaticEff : \"AdiabaticEff\",\n", "\n", "    # For Valve\n", "    ParameterPolicyVO.PressureDrop: \"PressureDrop\",\n", "    \n", "    # For Cooler\n", "    ParameterPolicyVO.OutletTemperature: \"OutletTemperature\",\n", "    ParameterPolicyVO.Efficiency: \"Efficiency\",\n", "    ParameterPolicyVO.PressureDrop: \"PressureDrop\",\n", "\n", "    # For Orifice Plate \n", "    ParameterPolicyVO.OrificeDiameter: \"OrificeDiameter\",\n", "    ParameterPolicyVO.InternalPipeDiameter: \"InternalPipeDiameter\",\n", "    ParameterPolicyVO.CorrectionFactor : \"CorrectionFactor\",\n", "    \n", "    # For Heat Exchanger\n", "    ParameterPolicyVO.HotFluidPressureDrop: \"HotFluidPressureDrop\",\n", "    ParameterPolicyVO.ColdFluidPressureDrop: \"ColdFluidPressureDrop\",\n", "    ParameterPolicyVO.OverallUA: \"OverallUA\",\n", "    ParameterPolicyVO.HeatExchangeArea: \"HeatExchangeArea\",\n", "    ParameterPolicyVO.HeatLoss: \"HeatLoss\",\n", "\n", "    # For Splitter\n", "    ParameterPolicyVO.Stream_1_Split_Ratio:\"Stream_1_Split_Ratio\",\n", "    ParameterPolicyVO.Stream_2_Split_Ratio:\"Stream_2_Split_Ratio\",\n", "}"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}