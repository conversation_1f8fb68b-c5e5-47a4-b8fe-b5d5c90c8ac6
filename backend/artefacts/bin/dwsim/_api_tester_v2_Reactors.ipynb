{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Setup"]}, {"cell_type": "code", "execution_count": 265, "metadata": {}, "outputs": [], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array, Double\n", "\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary, List\n", "\n", "from typing import Optional\n", "\n", "import source.backend.atlas_domainmodel.aggregates.base_aggregate as atlas\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "import DWSIM\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)"]}, {"cell_type": "code", "execution_count": 266, "metadata": {}, "outputs": [], "source": ["# SETUP INTERFACE AND SIMULATION\n", "\n", "import source.backend._sharedutils.Utilities as sharedutils\n", "\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "filepath = sharedutils.create_absolute_path_object(\"sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\")\n", "sim = interf.LoadFlowsheet(str(filepath))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Helper Function: Save"]}, {"cell_type": "code", "execution_count": 267, "metadata": {}, "outputs": [], "source": ["# SAVE OVER TEMP FILE\n", "import source.backend._sharedutils.Utilities as sharedutil\n", "\n", "\n", "def save_file(filename: Optional[str] = None):\n", "    if filename is None:\n", "        filename = \"api_test.dwxmz\"\n", "    else:\n", "        filename = filename + \".dwxmz\"\n", "\n", "    directory = \"source/backend/matrix_psimulator/dwsim/test\"\n", "\n", "    filepath = directory + \"/\" + filename\n", "    absolute_filepath = sharedutil.create_absolute_path_object(filepath)\n", "    interf.SaveFlowsheet(sim, str(absolute_filepath), True)\n", "    print(f\"\\nFile Saved: {absolute_filepath}\")\n", "# to persist, try save or run flowsheet"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Solve for Compound\n", "\n", "- solve for compound creation via numeric ID"]}, {"cell_type": "code", "execution_count": 268, "metadata": {}, "outputs": [], "source": ["Chemical_Compounds = {\n", "    \"Methane\":\"74-82-8\",\n", "    \"Ethane\":\"74-84-0\",\n", "    \"Propane\":\"74-98-6\",\n", "    \"N-butane\":\"106-97-8\",\n", "    \"N-pentane\":\"109-66-0\",\n", "    \"N-hexane\":\"110-54-3\",\n", "    \"N-heptane\":\"142-82-5\",\n", "    \"N-octane\":\"111-65-9\",\n", "    \"N-nonane\":\"111-84-2\",\n", "    \"N-decane\":\"124-18-5\",\n", "    \"Methanol\":\"67-56-1\",\n", "    \"Acetic acid\":\"64-19-7\",\n", "    \"Carbon monoxide\":\"630-08-0\",\n", "    }\n", "# Create a reverse dictionary mapping CAS numbers to compound names\n", "cas_to_name = {cas: name for name, cas in Chemical_Compounds.items()}\n", "# List of CAS numbers to add\n", "CAS_Num = [\"74-82-8\",\"74-84-0\",\"74-98-6\",\"106-97-8\",\"109-66-0\",\"110-54-3\",\"142-82-5\",\"111-65-9\",\"111-84-2\",\"124-18-5\",\"67-56-1\",\"64-19-7\",\"630-08-0\"]\n", "# Add compounds based on their CAS numbers\n", "for cas in CAS_Num:\n", "    compound_name = cas_to_name.get(cas)\n", "    if compound_name:\n", "        sim.AddCompound(compound_name)\n", "    else:\n", "        print(f\"CAS number {cas} not found in the dictionary\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Adding Thermodynamic Package"]}, {"cell_type": "code", "execution_count": 269, "metadata": {}, "outputs": [{"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7f598df5df40>"]}, "execution_count": 269, "metadata": {}, "output_type": "execute_result"}], "source": ["# sim.CreateAndAddPropertyPackage(\"Steam Tables (IAPWS-IF97)\")\n", "sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Material Streams"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create and Get a MaterialStream\n", "\n", "- create a material stream. Use the more complex and wholistic call"]}, {"cell_type": "code", "execution_count": 270, "metadata": {}, "outputs": [], "source": ["# TODO\n", "MS1 = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"1\")\n", "MS1_unitobj = MS1.GetAsObject()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- setup the compound mix"]}, {"cell_type": "code", "execution_count": 271, "metadata": {}, "outputs": [], "source": ["# Setting Some Composition (<PERSON><PERSON>)\n", "MS1_unitobj.SetOverallComposition(Array[float]([0.4,0,0,0.4,0.2,0,0,0,0,0,0,0,0]))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- get the material stream by id"]}, {"cell_type": "code", "execution_count": 272, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> 1: T = 298.15 K, P = 101325 Pa, W = 1 kg/s, M = 0 mol/s, Q = 0 m3/s, EF = 0 kW\n", "Compound Mole Fractions:\n", "Methane: 0.4\n", "Ethane: 0\n", "Propane: 0\n", "N-butane: 0.4\n", "N-pentane: 0.2\n", "N-hexane: 0\n", "N-heptane: 0\n", "N-octane: 0\n", "N-nonane: 0\n", "N-decane: 0\n", "Methanol: 0\n", "Acetic acid: 0\n", "Carbon monoxide: 0\n", "\n", "<class 'DWSIM.Thermodynamics.Streams.MaterialStream'> 1: T = 298.15 K, P = 101325 Pa, W = 1 kg/s, M = 0 mol/s, Q = 0 m3/s, EF = 0 kW\n", "Compound Mole Fractions:\n", "Methane: 0.4\n", "Ethane: 0\n", "Propane: 0\n", "N-butane: 0.4\n", "N-pentane: 0.2\n", "N-hexane: 0\n", "N-heptane: 0\n", "N-octane: 0\n", "N-nonane: 0\n", "N-decane: 0\n", "Methanol: 0\n", "Acetic acid: 0\n", "Carbon monoxide: 0\n", "\n"]}], "source": ["print(type(MS1), MS1)\n", "print(type(MS1_unitobj), MS1_unitobj)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Edit MaterialStream\n", "\n", "- retreive the materialstream\n", "- edit the policies\n", "- edit the properties\n", "- edit the compondmix compounds\n", "- edit the compoundmix settings (e.g. molar or mass, etc)\n", "\n", "Notes: \n", "- test for behaviour when setting thigns in and out of bounds\n", "- test for behaviour when calling wrong parameters \n", "- test for behaviour when setting dep vs indep params"]}, {"cell_type": "code", "execution_count": 273, "metadata": {}, "outputs": [], "source": ["# Retriving the material stream\n", "MS1_obj = sim.GetObject(\"1\")\n", "MS1_obj = MS1_unitobj.GetAsObject()"]}, {"cell_type": "code", "execution_count": 274, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'str'> PT\n", "PT\n"]}], "source": ["# Editing Policies Feel free to comment any of the flash spec policy\n", "setattr(MS1_unitobj, \"FlashSpec\", \"PH\")\n", "setattr(MS1_unitobj, \"FlashSpec\", \"PS\")\n", "setattr(MS1_unitobj, \"FlashSpec\", \"PVF\")\n", "setattr(MS1_unitobj, \"FlashSpec\", \"TVF\")\n", "setattr(MS1_unitobj, \"FlashSpec\", \"PT\")\n", "print(type(getattr(MS1_unitobj, \"FlashSpec\")), getattr(MS1_unitobj, \"FlashSpec\"))\n", "print(MS1_unitobj.GetFlashSpec())"]}, {"cell_type": "code", "execution_count": 275, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'int'> 5\n", "<class 'int'> 201325\n", "<class 'int'> 300\n"]}], "source": ["# Editing Properties\n", "setattr(MS1_unitobj, \"MolarFlow\", 5)\n", "setattr(MS1_unitobj, \"Pressure\", 201325)\n", "setattr(MS1_unitobj, \"Temperature\", 300)\n", "print(type(getattr(MS1_unitobj, \"Molar<PERSON>low\")), getattr(MS1_unitobj, \"MolarFlow\"))\n", "print(type(getattr(MS1_unitobj, \"Pressure\")), getattr(MS1_unitobj, \"Pressure\"))\n", "print(type(getattr(MS1_unitobj, \"Temperature\")), getattr(MS1_unitobj, \"Temperature\"))"]}, {"cell_type": "code", "execution_count": 276, "metadata": {}, "outputs": [], "source": ["# Editing the compounds and tweaking the mol fracitons\n", "MS1_unitobj.SetOverallComposition(Array[float]([0.4,0,0,0.4,0,0,0,0,0,0.2,0,0,0]))"]}, {"cell_type": "code", "execution_count": 277, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n", "0.0\n", "0.0\n", "0.0\n", "0.0\n", "[0.4, 0.0, 0.0, 0.4, 0.0, 0.0, 0.0, 0.0, 0.0, 0.2, 0.0, 0.0, 0.0]\n", "[0.017595728570164407, 0.03298039491483328, 0.04836506125950215, 0.06374972760417104, 0.07913439394883992, 0.09451906029350879, 0.10990379244751827, 0.12528837104639967, 0.14067305932751542, 0.15605774760863114, 0.03514422797022452, 0.06586633148945799, 0.030722103519233462]\n", "0.0\n"]}], "source": ["# Trying to get some properties of the stream\n", "one = sim.GetObject(\"1\")\n", "one = one.GetAsObject()\n", "print(one.GetMassFlow())\n", "print(one.GetMolarFlow())\n", "print(one.GetVolumetricFlow())\n", "print(one.GetMassEnthalpy())\n", "print(one.GetMassEntropy())\n", "print(list(one.GetOverallComposition()))\n", "print(list(one.GetOverallMassComposition()))\n", "print(one.GetOverallMolecularWeight())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### The below cell is being commented out\n", "Mass Fractions are not being set for some reason"]}, {"cell_type": "code", "execution_count": 278, "metadata": {}, "outputs": [], "source": ["# For Mass Fraction the method remains the same key difference is between Array and Double\n", "# TODO \n", "# MS5 = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"2\")\n", "# MS5_unitobj = MS5.GetAsObject()\n", "# # Setting Some Composition (Mass Basis)\n", "# Mass_Comp = Array[Double]([0.359894312779356, 0.640105687220644, 0.0000000000000000, 0.0000000000000000, 0.0000000000000000, 0.0000000000000000, 0.0000000000000000, 0.0000000000000000, 0.0000000000000000, 0.0000000000000000])\n", "# print(type(Mass_Comp),list(Mass_Comp))\n", "# MS5_unitobj.SetOverallMassComposition(Mass_Comp)\n", "# print(type(MS5), MS5)\n", "# print(type(MS5_unitobj), MS5_unitobj)"]}, {"cell_type": "code", "execution_count": 279, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n", "0.0\n", "inf\n", "0.0\n", "0.0\n", "[0.3204887659478781, 0.17098744117431738, 0.11659725406215393, 0.0884589400980726, 0.07126147120115123, 0.05966239315006281, 0.051310634599790385, 0.04501002996772431, 0.0400875147122274, 0.03613555508662179, 0.0, 0.0, 0.0]\n", "[0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.0, 0.0, 0.0]\n", "0.0\n"]}], "source": ["# Creating a new material stream #3\n", "MS3 = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"3\")\n", "MS3_unitobj = MS3.GetAsObject()\n", "\n", "### Below is another way of setting up the composisiton indirectly\n", "### Instead of specifying the mol or mass fraction one can also specify the individual mass flows of the compounds in the material stream\n", "### The same approach can be used for compounds molar flow as well using the command `.SetOverallCompoundMolarFlow`\n", "# Setting up  the mass flows of the individual compound\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"Methane\", 1) #kg/s\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"Ethane\", 1)\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"Propane\", 1)\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"N-butane\", 1)\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"N-pentane\", 1)\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"N-hexane\", 1)\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"N-heptane\", 1)\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"N-octane\", 1)\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"N-nonane\", 1)\n", "MS3_unitobj.SetOverallCompoundMassFlow(\"N-decane\", 1)\n", "\n", "# Setting up the temperature\n", "MS3_unitobj.SetTemperature(300) # K\n", "\n", "# Setting up the pressure\n", "MS3_unitobj.SetPressure(101300) # Pa\n", "\n", "# Settign up the mass flow\n", "MS3_unitobj.SetMassFlow(1) # kg/s\n", "\n", "# Trying to get some properties of the stream\n", "three = sim.GetObject(\"3\")\n", "three = three.GetAsObject()\n", "print(three.GetMassFlow())\n", "print(three.GetMolarFlow())\n", "print(three.GetVolumetricFlow())\n", "print(three.GetMassEnthalpy())\n", "print(three.GetMassEntropy())\n", "print(list(three.GetOverallComposition()))\n", "print(list(three.GetOverallMassComposition()))\n", "print(three.GetOverallMolecularWeight())"]}, {"cell_type": "code", "execution_count": 280, "metadata": {}, "outputs": [], "source": ["# Setting up 2 more streams these are the outlet streams for the heaters\n", "MS2 = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"2\")\n", "MS2_unitobj = MS2.GetAsObject()\n", "MS4 = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"4\")\n", "MS4_unitobj = MS4.GetAsObject()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### !! FIXME ??\n", "\n", "#### CompoundMixes \n", "- can please dig into compound mix compounds: \n", "- how todo compoundmix add, get and sets for each?\n", "- how todo compoundmix settings?\n", "\n", "#### Streams\n", "- how about setting the stream policy? \n", "- how about editing values that should be dependent - any errors? "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## [ ] CompoundMix\n", "\n", "Assume all the chemical compounds in `chemical_compounds` are added to the simulation.\n", "\n", "For 1, \n", "- can you set to mass \n", "- can you set to 4 compounds, equal (water, ethanol, citric acid, chloroform)\n", "- retrieve values in mass\n", "- then change from mass to molar\n", "- retreive value in molar\n", "\n", "For 2 \n", "- can you set to mass \n", "- can you set to 2 compounds, equal (acetic acid, ammonia)\n", "- then change from mass to molar"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## [ ] Material/Energy Stream Policies\n", "\n", "- please get 1 or 2 streams and change their policies"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Energy Stream \n", "\n", "(do the same as material stream)"]}, {"cell_type": "code", "execution_count": 281, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> E1\n", "<class 'DWSIM.UnitOperations.Streams.EnergyStream'> E1\n", "<class 'DWSIM.Interfaces.ISimulationObject'> E2\n", "<class 'DWSIM.UnitOperations.Streams.EnergyStream'> E2\n"]}], "source": ["ES1 = sim.AddObject(ObjectType.EnergyStream, 50, 50, \"E1\")\n", "ES1_unitobj = ES1.GetAsObject()\n", "print(type(ES1), ES1)\n", "print(type(ES1_unitobj), ES1_unitobj)\n", "\n", "ES2 = sim.AddObject(ObjectType.EnergyStream, 50, 50, \"E2\")\n", "ES2_unitobj = ES2.GetAsObject()\n", "print(type(ES2), ES2)\n", "print(type(ES2_unitobj), ES2_unitobj)"]}, {"cell_type": "code", "execution_count": 282, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'float'> 5.0\n", "<class 'float'> 10.0\n"]}], "source": ["# type checking\n", "setattr(ES1_unitobj, \"EnergyFlow\", 5)\n", "print(type(getattr(ES1_unitobj, \"EnergyFlow\")), getattr(ES1_unitobj, \"EnergyFlow\"))\n", "\n", "\n", "setattr(ES2_unitobj, \"EnergyFlow\", 10)\n", "print(type(getattr(ES2_unitobj, \"EnergyFlow\")), getattr(ES2_unitobj, \"EnergyFlow\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Solve for Add Heater"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create/Get Object\n", "\n", "\n", "DWSIM requires the entity-type to have a numeric character somewhere in the string.\n", "It will auto-increment the number to prevent collisions\n", "\n", "Example:\n", "- use tag HT. it will crash\n", "- use tag HT1. With every run on the same sim flowsheet, it will increment the ID"]}, {"cell_type": "code", "execution_count": 283, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> HT1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Heater'> HT1\n", "<class 'DWSIM.Interfaces.ISimulationObject'> HT2\n", "<class 'DWSIM.UnitOperations.UnitOperations.Heater'> HT2\n"]}], "source": ["# Setting up a heater for the stream number 1\n", "HT1 = sim.AddObject(ObjectType.Heater, 50, 50, \"HT1\")\n", "HT1_unitobj = HT1.GetAsObject()\n", "print(type(HT1), HT1)\n", "print(type(HT1_unitobj), HT1_unitobj)\n", "\n", "# Setting up another heater for the stream number 3\n", "HT2 = sim.AddObject(ObjectType.Heater, 50, 50, \"HT2\")\n", "HT2_unitobj = HT2.GetAsObject()\n", "print(type(HT2), HT2)\n", "print(type(HT2_unitobj), HT2_unitobj)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON> Calcmodes\n", "\n", "- this is run on unit operation\n", "- CalcMode returns a CalcMode Object. From that, getAttr and setAttr can modify it directly\n", "- when setting calcmode that doesn't exist, it returns an AttributeError "]}, {"cell_type": "code", "execution_count": 284, "metadata": {}, "outputs": [], "source": ["HT1_unitobj = sim.GetObject(\"HT1\").GetAsObject()"]}, {"cell_type": "code", "execution_count": 285, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Check for CalcMode types\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> HeatAdded\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> HeatAdded\n", "\n", "Change calcmode to OutletTemp\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> OutletTemperature\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> TemperatureChange\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> HeatAddedRemoved\n", "Method 2: using double getattr\n", "OutletTemperature\n", "HeatAddedRemoved\n", "OutletTemperature\n", "Method 3: using double setattr\n", "setting keyword: HeatAdded\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> HeatAdded HeatAdded\n", "setting keyword: OutletTemperature\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> OutletTemperature OutletTemperature\n", "setting keyword: EnergyStream\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> EnergyStream EnergyStream\n", "setting keyword: OutletVaporFraction\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> OutletVaporFraction OutletVaporFraction\n", "setting keyword: <PERSON>mper<PERSON><PERSON><PERSON><PERSON>\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> TemperatureChange TemperatureChange\n", "setting keyword: HeatAddedRemoved\n", "<class 'DWSIM.UnitOperations.UnitOperations.CalculationMode'> HeatAddedRemoved HeatAddedRemoved\n"]}], "source": ["HT1_unitobj = sim.GetObject(\"HT1\").GetAsObject()\n", "HT2_unitobj = sim.GetObject(\"HT2\").GetAsObject()\n", "\n", "# type checking\n", "print(\"\\nCheck for CalcMode types\")\n", "print(type(HT1_unitobj.CalcMode), HT1_unitobj.CalcMode)\n", "print(type(HT2_unitobj.CalcMode), HT2_unitobj.CalcMode)\n", "\n", "# print(getattr(HT1_unitobj.<PERSON>, \"OutletTemperature\"))\n", "\n", "# set attr\n", "print(\"\\nChange calcmode to OutletTemp\")\n", "HT1_unitobj.CalcMode = getattr(HT1_unitobj.CalcMode, \"OutletTemperature\")\n", "print(type(HT1_unitobj.CalcMode), HT1_unitobj.CalcMode)  # this should be Outlet Temperature\n", "HT2_unitobj.CalcMode = getattr(HT2_unitobj.Calc<PERSON>ode, \"TemperatureChange\")\n", "print(type(HT2_unitobj.CalcMode), HT2_unitobj.CalcMode) # this should be Temp Change\n", "HT2_unitobj.CalcMode = getattr(HT2_unitobj.<PERSON>, \"HeatAddedRemoved\")\n", "print(type(HT2_unitobj.CalcMode), HT2_unitobj.CalcMode) # this should be Temp Change\n", "\n", "\n", "# Method 2 - via variable assignment (does not work!) \n", "print(\"Method 2: using double getattr\")\n", "calcmode_attrib = getattr(HT1_unitobj, \"CalcMode\")\n", "print(calcmode_attrib)\n", "calcmode_attrib = getattr(calcmode_attrib, \"HeatAddedRemoved\")\n", "print(calcmode_attrib)\n", "calcmode_attrib = getattr(HT1_unitobj, \"CalcMode\")\n", "print(calcmode_attrib)\n", "\n", "\n", "# Method 3 - via set attr. This is the one to use. \n", "print(\"Method 3: using double setattr\")\n", "keyword_variables = [ \n", "    \"HeatAdded\",\n", "\"OutletTemperature\",\n", "\"EnergyStream\",\n", "\"OutletVaporFraction\",\n", "\"TemperatureChange\",\n", "\"HeatAddedRemoved\"\n", "]\n", "for keyword in keyword_variables:\n", "    print(f\"setting keyword: {keyword}\")\n", "    setattr(\n", "        HT1_unitobj,\n", "        \"CalcMode\",\n", "        getattr(\n", "            getattr(HT1_unitobj, \"CalcMode\"),\n", "             keyword\n", "             ),\n", "    )\n", "    print(type(HT1_unitobj.CalcMode), HT1_unitobj.CalcMode, str(HT1_unitobj.CalcMode)) \n", "\n", "\n", "\n", "# Supported Calc Modes are as follows\n", "# HeatAdded\n", "# OutletTemperature\t \n", "# EnergyStream \n", "# OutletVaporFraction\t \n", "# TemperatureChange\n", "# HeatAddedRemoved"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Edit Properties\n", "\n", "Notes\n", "- use setattr. assigning the property directly does not work, the state will not persist,\n", "- when setting attributes, there are NO bounds checking! this needs to be worked into the code"]}, {"cell_type": "code", "execution_count": 286, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "# TEST FOR INDEP PROP: EFFICIENCY, change to 90\n", "<class 'float'> 90.0\n", "<class 'float'> 100.0\n", "<class 'float'> 350.0\n", "\n", "# TEST FOR INDEP PROP: EFFICIENCY, change to 90\n", "<class 'float'> 90.0\n", "<class 'float'> 100.0\n", "<class 'NoneType'> None\n"]}], "source": ["HT1_unitobj = sim.GetObject(\"HT1\").GetAsObject()\n", "HT2_unitobj = sim.GetObject(\"HT2\").GetAsObject()\n", "\n", "print(\"\\n# TEST FOR INDEP PROP: EFFICIENCY, change to 90\")\n", "setattr(HT1_unitobj, \"Eficiencia\" , 90)\n", "setattr(HT1_unitobj, \"DeltaP\" , 100)\n", "setattr(HT1_unitobj, \"OutletTemperature\" , 350)\n", "HT1_prop_efficiency = getattr(HT1_unitobj, \"Eficiencia\")\n", "HT1_prop_pressuredrop = getattr(HT1_unitobj, \"DeltaP\")\n", "HT1_prop_outlettemp = getattr(HT1_unitobj, \"OutletTemperature\")\n", "print(type(HT1_prop_efficiency), HT1_prop_efficiency)\n", "print(type(HT1_prop_pressuredrop), HT1_prop_pressuredrop)\n", "print(type(HT1_prop_outlettemp), HT1_prop_outlettemp)\n", "\n", "print(\"\\n# TEST FOR INDEP PROP: EFFICIENCY, change to 90\")\n", "setattr(HT2_unitobj, \"Eficiencia\" , 90)\n", "setattr(HT2_unitobj, \"DeltaP\" , 100)\n", "setattr(HT2_unitobj, \"DeltaT\" , 100)\n", "HT2_prop_efficiency = getattr(HT2_unitobj, \"Eficiencia\")\n", "HT2_prop_pressuredrop = getattr(HT2_unitobj, \"DeltaP\")\n", "HT2_prop_outlettemp = getattr(HT1_unitobj, \"DeltaT\")\n", "print(type(HT2_prop_efficiency), HT2_prop_efficiency)\n", "print(type(HT2_prop_pressuredrop), HT2_prop_pressuredrop)\n", "print(type(HT2_prop_outlettemp), HT2_prop_outlettemp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Connecting Objects"]}, {"cell_type": "code", "execution_count": 287, "metadata": {}, "outputs": [], "source": ["# Connecitons for HT1\n", "sim.ConnectObjects(MS1_unitobj.GraphicObject,HT1_unitobj.GraphicObject,-1,-1)\n", "sim.ConnectObjects(HT1_unitobj.GraphicObject,MS2_unitobj.GraphicObject,-1,-1)\n", "sim.ConnectObjects(ES1_unitobj.GraphicObject,HT1_unitobj.GraphicObject,-1,-1)\n", "\n", "# Connections for HT2\n", "sim.ConnectObjects(MS3_unitobj.GraphicObject,HT2_unitobj.GraphicObject,-1,-1)\n", "sim.ConnectObjects(HT2_unitobj.GraphicObject,MS4_unitobj.GraphicObject,-1,-1)\n", "sim.ConnectObjects(ES2_unitobj.GraphicObject,HT2_unitobj.GraphicObject,-1,-1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Solving"]}, {"cell_type": "code", "execution_count": 288, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["System.Collections.Generic.List`1[System.Exception]\n"]}, {"data": {"text/plain": ["[]"]}, "execution_count": 288, "metadata": {}, "output_type": "execute_result"}], "source": ["sim.AutoLayout()\n", "Settings.SolverMode = 1\n", "errors = interf.CalculateFlowsheet4(sim)\n", "interf.CalculateFlowsheet4(sim)\n", "print(errors)  # if not converging, it will return ????\n", "list(errors)\n", "# to get errors just replace the thermodynamic package with Steam Tables"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Saving the flowsheet"]}, {"cell_type": "code", "execution_count": 289, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "File Saved: /code/source/backend/matrix_psimulator/dwsim/test/thisIsAFileName.dwxmz\n"]}], "source": ["save_file(\"thisIsAFileName\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Getting the properties of the outlet streams"]}, {"cell_type": "code", "execution_count": 290, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.0\n", "17.20512869745958\n", "0.3151867621876155\n", "-188.0298598344928\n", "-0.32043087874391857\n", "[0.4, 0.0, 0.0, 0.4, 0.0, 0.0, 0.0, 0.0, 0.0, 0.2, 0.0, 0.0, 0.0]\n", "[0.11040503556953896, 0.0, 0.0, 0.3999999724717941, 0.0, 0.0, 0.0, 0.0, 0.0, 0.489594991958667, 0.0, 0.0, 0.0]\n", "58.122203999999996\n", "-2236.942012728905\n"]}], "source": ["two = sim.GetObject(\"2\")\n", "two = two.GetAsObject()\n", "print(two.GetMassFlow())\n", "print(two.GetMolarFlow())\n", "print(two.GetVolumetricFlow())\n", "print(two.GetMassEnthalpy())\n", "print(two.GetMassEntropy())\n", "print(list(two.GetOverallComposition()))\n", "print(list(two.GetOverallMassComposition()))\n", "print(two.GetOverallMolecularWeight())\n", "print(two.GetOverallHeatOfFormation())"]}, {"cell_type": "code", "execution_count": 291, "metadata": {}, "outputs": [{"data": {"text/plain": ["['vaporPressure',\n", " 'surfaceTension',\n", " 'compressibilityFactor',\n", " 'heatOfVaporization',\n", " 'heatCapacity',\n", " 'heatCapacityCv',\n", " 'idealGasHeatCapacity',\n", " 'idealGasEnthalpy',\n", " 'excessEnthalpy',\n", " 'excessGibbsFreeEnergy',\n", " 'excessEntropy',\n", " 'viscosity',\n", " 'thermalConductivity',\n", " 'fugacity',\n", " 'fugacityCoefficient',\n", " 'activity',\n", " 'activityCoefficient',\n", " 'dewPointPressure',\n", " 'dewPointTemperature',\n", " 'kvalue',\n", " 'logFugacityCoefficient',\n", " 'logkvalue',\n", " 'volume',\n", " 'density',\n", " 'enthalpy',\n", " 'entropy',\n", " 'enthalpyF',\n", " 'entropyF',\n", " 'enthalpyNF',\n", " 'entropyNF',\n", " 'gibbsFreeEnergy',\n", " 'moles',\n", " 'mass',\n", " 'molecularWeight',\n", " 'boilingPointTemperature',\n", " None]"]}, "execution_count": 291, "metadata": {}, "output_type": "execute_result"}], "source": ["list(two.GetPropList())\n", "# Need to figure out a way to get the property when needed"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Getting the properties of the heater"]}, {"cell_type": "code", "execution_count": 292, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["90.0\n", "100.0\n", "None\n", "-0.008386693921920596\n", "0.7542732237675294\n", "298.14161330607806\n", "298.14161330607806\n"]}], "source": ["HT1_obj = sim.GetObject(\"HT1\")\n", "HT1_obj = HT1_obj.GetAsObject()\n", "print(HT1_obj.get_Eficiencia())\n", "print(HT1_obj.get_DeltaP())\n", "print(HT1_obj.get_DeltaQ())\n", "print(HT1_obj.get_DeltaT())\n", "print(HT1_obj.get_OutletVaporFraction())\n", "print(HT1_obj.get_OutletTemperature())\n", "print(getattr(HT1_obj, \"OutletTemperature\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Delete Object "]}, {"cell_type": "code", "execution_count": 293, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> HT1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Heater'> HT1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> HT1\n", "\n", "\n", "<class 'DWSIM.Interfaces.ISimulationObject'> HT1\n", "['AddDynamicProperty', 'AddExtraProperty', 'AdjustVarType', 'Annotation', 'AppendDebugLine', 'AttachedAdjustId', 'AttachedSpecId', 'AttachedUtilities', 'Calculate', 'Calculated', 'CanUsePreviousResults', 'CheckDirtyStatus', 'CheckSpec', 'ClearExtraProperties', 'ClearPropertyPackageInstance', 'CloneJSON', 'CloneXML', 'CloseDynamicsEditForm', 'CloseEditForm', 'ConnectEnergyStream', 'ConnectFeedEnergyStream', 'ConnectFeedMaterialStream', 'ConnectProductEnergyStream', 'ConnectProductMaterialStream', 'CreateDynamicProperties', 'DeCalculate', 'DebugMode', 'DebugText', 'DisplayDynamicsEditForm', 'DisplayEditForm', 'DisplayExtraPropertiesEditForm', 'DynamicsOnly', 'DynamicsSpec', 'Equals', 'ErrorMessage', 'ExtraProperties', 'ExtraPropertiesDescriptions', 'ExtraPropertiesTypes', 'ExtraPropertiesUnitTypes', 'GetAsObject', 'GetChartModel', 'GetChartModelNames', 'GetConnectionPortsInfo', 'GetConnectionPortsList', 'GetDebugReport', 'GetDefaultProperties', 'GetDisplayDescription', 'GetDisplayName', 'GetDynamicContents', 'GetDynamicProperty', 'GetDynamicPropertyUnitType', 'GetDynamicResidenceTime', 'GetDynamicVolume', 'GetEnergyBalanceResidual', 'GetExtraPropertyValue', 'GetFlowsheet', 'GetHashCode', 'GetIconBitmap', 'GetMassBalanceResidual', 'GetPowerGeneratedOrConsumed', 'GetProperties', 'GetPropertyDescription', 'GetPropertyUnit', 'GetPropertyValue', 'GetReport', 'GetStructuredReport', 'GetType', 'GetVersion', 'GraphicObject', 'HasPropertiesForDynamicMode', 'IsAdjustAttached', 'IsDirty', 'IsDynamicProperty', 'IsFunctional', 'IsSink', 'IsSource', 'IsSpecAttached', 'LastUpdated', 'MobileCompatible', 'Name', 'ObjectClass', 'PerformPostCalcValidation', 'PreferredFlashAlgorithmTag', 'PropertyPackage', 'RemoveDynamicProperty', 'RemoveExtraProperty', 'RunDynamicModel', 'SetCanUsePreviousResults', 'SetDirtyStatus', 'SetExtraPropertyValue', 'SetFlowsheet', 'SetPropertyPackageInstance', 'SetPropertyValue', 'Solve', 'SpecVarType', 'SupportsDynamicMode', 'ToString', 'UpdateDynamicsEditForm', 'UpdateEditForm', 'UpdateExtraPropertiesEditForm', 'Validate', '__class__', '__delattr__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', 'get_AdjustVarType', 'get_Annotation', 'get_AttachedAdjustId', 'get_AttachedSpecId', 'get_AttachedUtilities', 'get_Calculated', 'get_CanUsePreviousResults', 'get_DebugMode', 'get_DebugText', 'get_DynamicsOnly', 'get_DynamicsSpec', 'get_ErrorMessage', 'get_ExtraProperties', 'get_ExtraPropertiesDescriptions', 'get_ExtraPropertiesTypes', 'get_ExtraPropertiesUnitTypes', 'get_GraphicObject', 'get_HasPropertiesForDynamicMode', 'get_IsAdjustAttached', 'get_IsDirty', 'get_IsFunctional', 'get_IsSink', 'get_IsSource', 'get_IsSpecAttached', 'get_LastUpdated', 'get_MobileCompatible', 'get_Name', 'get_ObjectClass', 'get_PreferredFlashAlgorithmTag', 'get_PropertyPackage', 'get_SpecVarType', 'get_SupportsDynamicMode', 'set_AdjustVarType', 'set_Annotation', 'set_AttachedAdjustId', 'set_AttachedSpecId', 'set_AttachedUtilities', 'set_Calculated', 'set_DebugMode', 'set_DebugText', 'set_DynamicsOnly', 'set_DynamicsSpec', 'set_ErrorMessage', 'set_ExtraProperties', 'set_ExtraPropertiesDescriptions', 'set_ExtraPropertiesTypes', 'set_ExtraPropertiesUnitTypes', 'set_GraphicObject', 'set_IsAdjustAttached', 'set_IsSpecAttached', 'set_LastUpdated', 'set_Name', 'set_ObjectClass', 'set_PreferredFlashAlgorithmTag', 'set_PropertyPackage', 'set_SpecVarType']\n", "\n", "\n", "<class 'DWSIM.UnitOperations.UnitOperations.Heater'> HT1\n", "['AccumulationStream', 'AddDynamicProperty', 'AddExtraProperty', 'AdjustVarType', 'Annotation', 'AppendDebugLine', 'AttachedAdjustId', 'AttachedSpecId', 'AttachedUtilities', 'CalcMode', 'Calculate', 'Calculated', 'CalculationMode', 'CalculationRoutineOverride', 'CanUsePreviousResults', 'CheckDirtyStatus', 'CheckSpec', 'ClassId', 'ClearExtraProperties', 'ClearPropertyPackageInstance', 'Clone', 'CloneJSON', 'CloneXML', 'CloseDynamicsEditForm', 'CloseEditForm', 'ComponentDescription', 'ComponentName', 'ConnectEnergyStream', 'ConnectFeedEnergyStream', 'ConnectFeedMaterialStream', 'ConnectProductEnergyStream', 'ConnectProductMaterialStream', 'CopyDataToClipboard', 'CreateChartAction', 'CreateDynamicProperties', 'CreateNew', 'DeCalculate', 'DebugMode', 'DebugText', 'DeltaP', 'DeltaQ', 'DeltaT', 'DetailedDebugReport', 'DisplayDynamicsEditForm', 'DisplayEditForm', 'DisplayExtraPropertiesEditForm', 'Dispose', 'DynamicsOnly', 'DynamicsSpec', 'Eficiencia', 'EnergyFlow', 'Equals', 'ErrorMessage', 'ExternalSolverConfigData', 'ExternalSolverID', 'ExtraProperties', 'ExtraPropertiesDescriptions', 'ExtraPropertiesEditor', 'ExtraPropertiesTypes', 'ExtraPropertiesUnitTypes', 'FT', 'Finalize', 'FixOnHeat', 'FlowSheet', 'GetAsObject', 'GetChartModel', 'GetChartModelNames', 'GetConnectionPortsInfo', 'GetConnectionPortsList', 'GetDebugReport', 'GetDebugWriter', 'GetDefaultProperties', 'GetDisplayDescription', 'GetDisplayName', 'GetDynamicContents', 'GetDynamicProperty', 'GetDynamicPropertyUnitType', 'GetDynamicResidenceTime', 'GetDynamicVolume', 'GetEnergyBalanceResidual', 'GetExtraPropertyValue', 'GetFlowsheet', 'GetHashCode', 'GetIconBitmap', 'GetMassBalanceResidual', 'GetPowerGeneratedOrConsumed', 'GetProperties', 'GetPropertyDescription', 'GetPropertyUnit', 'GetPropertyValue', 'GetReport', 'GetStructuredReport', 'GetType', 'GetVersion', 'GraphicObject', 'HasPropertiesForDynamicMode', 'IsAdjustAttached', 'IsDirty', 'IsDynamicProperty', 'IsFunctional', 'IsSink', 'IsSource', 'IsSpecAttached', 'LastSolutionInputSnapshot', 'LastUpdated', 'LaunchExternalPropertyEditor', 'LoadData', 'MemberwiseClone', 'MobileCompatible', 'Name', 'ObjectClass', 'ObjectCopy', 'OutletTemperature', 'OutletVaporFraction', 'Overloads', 'OverrideCalculationRoutine', 'PerformPostCalcValidation', 'Phases', 'PreferredFlashAlgorithmTag', 'ProductAssembly', 'ProductAuthor', 'ProductContactInfo', 'ProductDescription', 'ProductName', 'ProductPage', 'ProductVersion', 'PropertyPackage', 'ReferenceEquals', 'RemoveDynamicProperty', 'RemoveExtraProperty', 'RunDynamicModel', 'SaveData', 'SetCanUsePreviousResults', 'SetDirtyStatus', 'SetDynamicProperty', 'SetExtraPropertyValue', 'SetFlowsheet', 'SetPropertyPackageInstance', 'SetPropertyValue', 'Solve', 'SpecVarType', 'StoreDebugReport', 'StoreDetailedDebugReport', 'SupportsDynamicMode', 'ToString', 'Unsolve', 'UpdateDynamicsEditForm', 'UpdateEditForm', 'UpdateExtraPropertiesEditForm', 'UserDefinedChartNames', 'Validate', 'Visible', '_CanUsePreviousResults', '_IsDirty', '__class__', '__delattr__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__overloads__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '_capeopenmode', '_pp', '_ppid', 'disposedValue', 'f', 'fd', 'get_AdjustVarType', 'get_Annotation', 'get_AttachedAdjustId', 'get_AttachedSpecId', 'get_AttachedUtilities', 'get_CalcMode', 'get_Calculated', 'get_CanUsePreviousResults', 'get_ComponentDescription', 'get_ComponentName', 'get_CreateChartAction', 'get_DebugMode', 'get_DebugText', 'get_DeltaP', 'get_DeltaQ', 'get_DeltaT', 'get_DetailedDebugReport', 'get_DynamicsOnly', 'get_DynamicsSpec', 'get_Eficiencia', 'get_EnergyFlow', 'get_ErrorMessage', 'get_ExternalSolverConfigData', 'get_ExternalSolverID', 'get_ExtraProperties', 'get_ExtraPropertiesDescriptions', 'get_ExtraPropertiesTypes', 'get_ExtraPropertiesUnitTypes', 'get_FixOnHeat', 'get_FlowSheet', 'get_GraphicObject', 'get_HasPropertiesForDynamicMode', 'get_IsAdjustAttached', 'get_IsDirty', 'get_IsFunctional', 'get_IsSink', 'get_IsSource', 'get_IsSpecAttached', 'get_LastSolutionInputSnapshot', 'get_LastUpdated', 'get_MobileCompatible', 'get_Name', 'get_ObjectClass', 'get_OutletTemperature', 'get_OutletVaporFraction', 'get_OverrideCalculationRoutine', 'get_Phases', 'get_PreferredFlashAlgorithmTag', 'get_ProductAssembly', 'get_ProductAuthor', 'get_ProductContactInfo', 'get_ProductDescription', 'get_ProductName', 'get_ProductPage', 'get_ProductVersion', 'get_PropertyPackage', 'get_SpecVarType', 'get_StoreDetailedDebugReport', 'get_SupportsDynamicMode', 'get_UserDefinedChartNames', 'get_Visible', 'm_DQ', 'm_FixOnHeat', 'm_Tout', 'm_VFout', 'm_cmode', 'm_dp', 'm_dt', 'm_eta', 'm_flowsheet', 'set_AdjustVarType', 'set_Annotation', 'set_AttachedAdjustId', 'set_AttachedSpecId', 'set_AttachedUtilities', 'set_CalcMode', 'set_Calculated', 'set_ComponentDescription', 'set_ComponentName', 'set_CreateChartAction', 'set_DebugMode', 'set_DebugText', 'set_DeltaP', 'set_DeltaQ', 'set_DeltaT', 'set_DetailedDebugReport', 'set_DynamicsOnly', 'set_DynamicsSpec', 'set_Eficiencia', 'set_EnergyFlow', 'set_ErrorMessage', 'set_ExternalSolverConfigData', 'set_ExternalSolverID', 'set_ExtraProperties', 'set_ExtraPropertiesDescriptions', 'set_ExtraPropertiesTypes', 'set_ExtraPropertiesUnitTypes', 'set_FixOnHeat', 'set_GraphicObject', 'set_IsAdjustAttached', 'set_IsSpecAttached', 'set_LastSolutionInputSnapshot', 'set_LastUpdated', 'set_Name', 'set_ObjectClass', 'set_OutletTemperature', 'set_OutletVaporFraction', 'set_OverrideCalculationRoutine', 'set_PreferredFlashAlgorithmTag', 'set_PropertyPackage', 'set_SpecVarType', 'set_StoreDetailedDebugReport', 'set_UserDefinedChartNames', 'set_Visible']\n", "\n", "\n", "<class 'DWSIM.Interfaces.IGraphicObject'> HT1\n", "['Active', 'AdditionalInfo', 'AutoSize', 'Calculated', 'Clone', 'ControlPanelModeEditorDisplayDelegate', 'Description', 'DisplayControlPanelModeEditor', 'DoubleClickAction', 'Draw', 'DrawMode', 'DrawOverride', 'Editor', 'EnergyConnector', 'Equals', 'Extensions', 'FlippedH', 'FlippedV', 'Flowsheet', 'FontStyle', 'GetHashCode', 'GetIconAsBitmap', 'GetIconAsStream', 'GetPointValue', 'GetType', 'Height', 'HitTest', 'InputConnectors', 'IsConnector', 'IsEnergyStream', 'Name', 'ObjectType', 'OutputConnectors', 'Owner', 'Position', 'PositionConnectors', 'Rotation', 'Selected', 'Shape', 'ShapeOverride', 'SpecialConnectors', 'Status', 'Tag', 'ToString', 'Width', 'X', 'Y', '__class__', '__delattr__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', 'get_Active', 'get_AdditionalInfo', 'get_AutoSize', 'get_Calculated', 'get_ControlPanelModeEditorDisplayDelegate', 'get_Description', 'get_DoubleClickAction', 'get_DrawMode', 'get_DrawOverride', 'get_Editor', 'get_EnergyConnector', 'get_Extensions', 'get_FlippedH', 'get_FlippedV', 'get_Flowsheet', 'get_FontStyle', 'get_Height', 'get_InputConnectors', 'get_IsConnector', 'get_IsEnergyStream', 'get_Name', 'get_ObjectType', 'get_OutputConnectors', 'get_Owner', 'get_Position', 'get_Rotation', 'get_Selected', 'get_Shape', 'get_ShapeOverride', 'get_SpecialConnectors', 'get_Status', 'get_Tag', 'get_Width', 'get_X', 'get_Y', 'set_Active', 'set_AdditionalInfo', 'set_AutoSize', 'set_Calculated', 'set_ControlPanelModeEditorDisplayDelegate', 'set_Description', 'set_DoubleClickAction', 'set_DrawMode', 'set_DrawOverride', 'set_Editor', 'set_EnergyConnector', 'set_Extensions', 'set_FlippedH', 'set_FlippedV', 'set_Flowsheet', 'set_FontStyle', 'set_Height', 'set_InputConnectors', 'set_IsConnector', 'set_IsEnergyStream', 'set_Name', 'set_ObjectType', 'set_OutputConnectors', 'set_Owner', 'set_Position', 'set_Rotation', 'set_Selected', 'set_Shape', 'set_ShapeOverride', 'set_SpecialConnectors', 'set_Status', 'set_Tag', 'set_Width', 'set_X', 'set_Y']\n", "(sender, e, gobj, confirmation=True, triggercalc=False)\n"]}], "source": ["HT1_sim_obj = sim.GetObject(\"HT1\")\n", "HT1_unit_obj = HT1_sim_obj.GetAsObject()\n", "HT1_graphic_obj = HT1_unit_obj.GraphicObject\n", "\n", "\n", "print(type(HT1_sim_obj), HT1_sim_obj)\n", "print(type(HT1_unit_obj), HT1_unit_obj)\n", "print(type(HT1_graphic_obj), HT1_graphic_obj)\n", "\n", "objects = [HT1_sim_obj, HT1_unit_obj, HT1_graphic_obj]\n", "for object in objects:\n", "    print(\"\\n\")\n", "    print(type(object), object)\n", "    print(dir(object))\n", "    # sim.DeleteSelectedObject(object)\n", "    # print(object.__dict__) # AttributeError: 'ISimulationObject' object has no attribute '__dict__'\n", "# sim.DeleteSelectedObject(HT1_graphic_obj)\n", "\n", "import inspect\n", "signature = inspect.signature(sim.DeleteSelectedObject)\n", "print(signature)\n", "\n", "sim.DeleteSelectedObject(None, None, HT1_graphic_obj)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Per-Equipment API: setting and amending PARAMETERS and POLICIES\n", "\n", "For the below equipment, please review how to set each parameter based on PARAMETER and POLICY variable. \n", "\n", "\n", "Notes\n", "- Reference of PARAMETER and POLICIES: `source/backend/atlas_domainmodel/entities/equipment.py`\n", "- Reference of mappings done prior: `source/backend/matrix_psimulator/dwsim/equipment_manager.py`\n", "- prefer setattr() / getattr() over methods like get_DeltaP()\n", "- solve each equipment within its own H2\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Pump"]}, {"cell_type": "code", "execution_count": 294, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> PUMP1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Pump'> PUMP1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> PUMP1\n"]}, {"data": {"text/plain": ["<CalculationMode.Delta_P: 0>"]}, "execution_count": 294, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "PUMP1 = sim.AddObject(ObjectType.Pump, 0, 0, \"PUMP1\")\n", "\n", "PUMP1_sim = sim.GetObject(\"PUMP1\")\n", "PUMP1_unit = PUMP1.GetAsObject()\n", "PUMP1_graphic = PUMP1_unit.GraphicObject\n", "\n", "_items = [PUMP1_sim, PUMP1_unit, PUMP1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "PUMP1_unit.get_CalcMode()"]}, {"cell_type": "code", "execution_count": 295, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PUMP1_unit.CalcMode = CalcPolicyEnum.PerformanceCurve vs Curves\n", "PUMP1_unit.CalcMode = CalcPolicyEnum.PressureIncrease vs Delta_P\n", "PUMP1_unit.CalcMode = CalcPolicyEnum.OutletPressure vs OutletPressure\n", "PUMP1_unit.CalcMode = CalcPolicyEnum.PowerRequired vs Power\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "CalcPolicyEnum.PressureIncrease,\n", "CalcPolicyEnum.OutletPressure,\n", "CalcPolicyEnum.PowerRequired,\n", "CalcPolicyEnum.PerformanceCurve,\n", "\"\"\"\n", "\n", "\n", "CALCPOLICY_KEYWORDS= {\n", "    atlas.CalcPolicyEnum.PerformanceCurve:(\"CalcMode\",\"Curves\"),\n", "    atlas.CalcPolicyEnum.PressureIncrease:(\"CalcMode\",\"Delta_P\"),\n", "    atlas.CalcPolicyEnum.OutletPressure:(\"CalcMode\",\"OutletPressure\"),\n", "    atlas.CalcPolicyEnum.PowerRequired:(\"CalcMode\",\"Power\"),\n", "}\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        PUMP1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(PUMP1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(PUMP1_unit, _attribute)\n", "    print(f\"PUMP1_unit.CalcMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 296, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.Efficiency: '666.0' (should be 666)\n", "ParameterEnum.PressureIncrease: '666.0' (should be 666)\n", "ParameterEnum.OutletPressure: '666.0' (should be 666)\n", "ParameterEnum.OutletTemperature: '666.0' (should be 666)\n", "ParameterEnum.TemperatureChange: '666.0' (should be 666)\n", "ParameterEnum.PowerRequired: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_FlowRate: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_Head: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_Power: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_Eff: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_NPSH: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_SystemHead: '666.0' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "\n", "PARAMETER_KEYWORDS = {\n", "atlas.ParameterEnum.Efficiency: \"Eficiencia\",  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Pump.htm\n", "atlas.ParameterEnum.PressureIncrease: \"DeltaP\",\n", "atlas.ParameterEnum.OutletPressure: \"Pout\",\n", "atlas.ParameterEnum.OutletTemperature: \"OutletTemperature\",\n", "atlas.ParameterEnum.TemperatureChange: \"DeltaT\",\n", "atlas.ParameterEnum.PowerRequired: \"DeltaQ\",\n", "atlas.ParameterEnum.PerformanceCurves_FlowRate: \"CurveFlow\",\n", "atlas.ParameterEnum.PerformanceCurves_Head: \"CurveHead\",\n", "atlas.ParameterEnum.PerformanceCurves_Power: \"CurvePower\",\n", "atlas.ParameterEnum.PerformanceCurves_Eff: \"CurveEff\",\n", "atlas.ParameterEnum.PerformanceCurves_NPSH: \"CurveNPSHr\",\n", "atlas.ParameterEnum.PerformanceCurves_SystemHead: \"CurveSysHead\",\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(PUMP1_unit, v, 666)\n", "    new_val = getattr(PUMP1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")\n", "\n", "# Its ok for user to toggle curves 1 by 1 "]}, {"cell_type": "code", "execution_count": 297, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Label0: 'insulin' (should be 'insulin')\n", "Label0: 'sysrup' (should be 'sysrup')\n", "Label0: 'coke' (should be 'coke')\n", "Label2: 'in2sulin' (should be 'in2sulin')\n", "Label2: 'syrup' (should be 'syrup')\n", "Label2: 'coke' (should be 'coke')\n", "Label3: 'ins4ulin' (should be 'ins4ulin')\n", "Label3: 'sydrup' (should be 'sydrup')\n", "Label3: 'coke' (should be 'coke')\n", "Label4: 'insulin' (should be 'insulin')\n", "Label4: 'syrup' (should be 'syrup')\n", "Label4: 'co33ke' (should be 'co33ke')\n"]}], "source": ["# EXAMPLE IF STRING FOR PARAM\n", "\n", "# we won't address bounds in this exercise, but lets address \n", "# 1) type - should it be string or float?\n", "# 2) if Str - what are the valid strings \n", "\n", "\n", "valid_strings = {\n", "    \"Label0\": [\"insulin\", \"sysrup\", \"coke\"],\n", "    \"Label2\": [\"in2sulin\", \"syrup\", \"coke\"],\n", "    \"Label3\": [\"ins4ulin\", \"sydrup\", \"coke\"],\n", "    \"Label4\": [\"insulin\", \"syrup\", \"co33ke\"],\n", "}\n", "\n", "for k, v in valid_strings.items():\n", "    for _v in v: \n", "        setattr(PUMP1_unit, k, _v)\n", "        new_val = getattr(PUMP1_unit, k)\n", "        print(f\"{k}: '{new_val}' (should be '{_v}')\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Orifice Plate \n", "\n", "- assume API for Orifice Plate\n", "- reference links: ....\n", "- Calc Policy needs to be checked and the same applies for the parameters"]}, {"cell_type": "code", "execution_count": 298, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> OP5\n", "<class 'DWSIM.UnitOperations.UnitOperations.OrificePlate'> OP5\n", "<class 'DWSIM.Interfaces.IGraphicObject'> OP5\n"]}, {"data": {"text/plain": ["<OrificeType.FlangeTaps: 1>"]}, "execution_count": 298, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "OrificePlate1 = sim.AddObject(ObjectType.OrificePlate,0,0,\"OP5\")\n", "\n", "OrificePlate1_sim = sim.GetObject(\"OP5\")\n", "OrificePlate1_unit = OrificePlate1.GetAsObject()\n", "OrificePlate1_graphic = OrificePlate1_unit.GraphicObject\n", "\n", "_items = [OrificePlate1_sim, OrificePlate1_unit, OrificePlate1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "OrificePlate1_unit.get_OrifType()"]}, {"cell_type": "code", "execution_count": 299, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OrificePlate1_unit.OrifType = CalcPolicyEnum.Pressure_Tappings_Flange vs FlangeTaps\n", "OrificePlate1_unit.OrifType = CalcPolicyEnum.Pressure_Tappings_Corner vs CornerTaps\n", "OrificePlate1_unit.OrifType = CalcPolicyEnum.Pressure_Tappings_Radius vs RadiusTaps\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "atlas.CalcPolicyEnum.Pressure_Tappings_Flange: \"FlangeTaps\",\n", "atlas.CalcPolicyEnum.Pressure_Tappings_Corner: \"CornerTaps\",\n", "atlas.CalcPolicyEnum.Pressure_Tappings_Radius: \"RadiusTaps\",\n", "\"\"\"\n", "\n", "\n", "from DWSIM.UnitOperations.UnitOperations import OrificePlate\n", "\n", "CALCPOLICY_KEYWORDS_2 = {\n", "    atlas.CalcPolicyEnum.Pressure_Tappings_Flange: OrificePlate.OrificeType.FlangeTaps,\n", "    atlas.CalcPolicyEnum.Pressure_Tappings_Corner: OrificePlate.OrificeType.CornerTaps,\n", "    atlas.CalcPolicyEnum.Pressure_Tappings_Radius: OrificePlate.OrificeType.RadiusTaps,\n", "}\n", "\n", "for k, v in CALCPOLICY_KEYWORDS_2.items():\n", "     setattr(\n", "         OrificePlate1_unit,\n", "         \"OrifType\",\n", "         v\n", "     )\n", "\n", "     policy = getattr(OrificePlate1_unit, \"OrifType\")\n", "     print(f\"OrificePlate1_unit.OrifType = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 300, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.OverallPressureDrop: '666.0' (should be 666)\n", "ParameterEnum.CorrectionFactor: '666.0' (should be 666)\n", "ParameterEnum.OrificePressureDrop: '666.0' (should be 666)\n", "ParameterEnum.OrificeBeta: '666.0' (should be 666)\n", "ParameterEnum.OrificeDiameter: '666.0' (should be 666)\n", "ParameterEnum.InternalPipeDiameter: '666.0' (should be 666)\n", "ParameterEnum.TemperatureChange: '666.0' (should be 666)\n", "ParameterEnum.Tapping: '666' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "    atlas.ParameterEnum.OverallPressureDrop: \"OverallPressureDrop\",\n", "    atlas.ParameterEnum.CorrectionFactor: \"CorrectionFactor\",  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_OrificePlate.htm\n", "    atlas.ParameterEnum.OrificePressureDrop: \"OrificePressureDrop\",\n", "    atlas.ParameterEnum.OrificeBeta: \"Beta\",\n", "    atlas.ParameterEnum.OrificeDiameter: \"OrificeDiameter\",\n", "    atlas.ParameterEnum.InternalPipeDiameter: \"InternalPipeDiameter\",\n", "    atlas.ParameterEnum.TemperatureChange: \"DeltaT\",\n", "    atlas.ParameterEnum.Tapping: \"\",\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    try:\n", "        setattr(OrificePlate1_unit, v, 666)\n", "        new_val = getattr(OrificePlate1_unit, v)\n", "        print(f\"{k}: '{new_val}' (should be 666)\")\n", "    except Exception as process_path:\n", "        print(f\"{k}: {process_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compressor \n", "\n", "- assume API for Expander will  be similar to Compressor\n", "- reference links: ....\n", "- Need to cross check for the thermodynamic path of the compressor"]}, {"cell_type": "code", "execution_count": 301, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> Compressor1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Compressor'> Compressor1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> Compressor1\n", "OutletPressure\n", "Adiabatic\n"]}], "source": ["# Create Entity \n", "Compressor1 = sim.AddObject(ObjectType.Compressor,0,0,\"Compressor1\")\n", "\n", "Compressor1_sim = sim.GetObject(\"Compressor1\")\n", "Compressor1_unit = Compressor1.GetAsObject()\n", "Compressor_graphic = Compressor1_unit.GraphicObject\n", "\n", "_items = [Compressor1_sim,Compressor1_unit,Compressor_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "print(Compressor1_unit.get_CalcMode())\n", "print(Compressor1_unit.get_ProcessPath())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### LO to solve for Adiabatic and Polytropic, both need to be passed as Enums\n", "### Set KnownHead Adiabatic Compressor"]}, {"cell_type": "code", "execution_count": 302, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Compressor1_unit.CalcMode = CalcPolicyEnum.PerformanceCurves_Compressor vs Curves\n", "Compressor1_unit.CalcMode = CalcPolicyEnum.PowerRequired_Compressor vs PowerRequired\n", "Compressor1_unit.CalcMode = CalcPolicyEnum.OutletPressure_Compressor vs OutletPressure\n", "Compressor1_unit.CalcMode = CalcPolicyEnum.Known_Head_Compressor vs Head\n", "Compressor1_unit.CalcMode = CalcPolicyEnum.PressureIncrease_Compressor vs Delta_P\n", "Compressor1_unit.CalcMode = CalcPolicyEnum.ThermodynamicProcess_Adiabatic vs Adiabatic\n", "Compressor1_unit.CalcMode = CalcPolicyEnum.ThermodynamicProcess_Polytropic vs Polytropic\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "CalcPolicyEnum.PerformanceCurves_Compressor: \"Curves\",\n", "CalcPolicyEnum.PowerRequired_Compressor: \"PowerRequired\",\n", "CalcPolicyEnum.OutletPressure_Compressor: \"OutletPressure\",\n", "CalcPolicyEnum.Known_Head_Compressor: \"Head\",\n", "CalcPolicyEnum.PressureIncrease_Compressor: \"Delta_P\",\n", "CalcPolicyEnum.ThermodynamicProcess_Adiabatic: \"Adiabatic\",\n", "CalcPolicyEnum.ThermodynamicProcess_Polytropic: \"Polytropic\",\n", "\"\"\"\n", "\n", "# from DWSIM.UnitOperations.UnitOperations import Compressor\n", "\n", "# (<PERSON><PERSON><PERSON><PERSON>, <PERSON>umName)\n", "\n", "CALCPOLICY_KEYWORDS = {\n", "    atlas.CalcPolicyEnum.PerformanceCurves_Compressor: (\"CalcMode\", \"Curves\"),\n", "    atlas.CalcPolicyEnum.PowerRequired_Compressor: (\"CalcMode\", \"PowerRequired\"),\n", "    atlas.CalcPolicyEnum.OutletPressure_Compressor: (\"CalcMode\", \"OutletPressure\"),\n", "    atlas.CalcPolicyEnum.Known_Head_Compressor: (\"CalcM<PERSON>\", \"Head\"),  # VD to fix calc policy enum\n", "    atlas.CalcPolicyEnum.PressureIncrease_Compressor: (\"CalcMode\", \"Delta_P\"),\n", "    atlas.CalcPolicyEnum.ThermodynamicProcess_Adiabatic: (\"ProcessPathType\", \"Adiabatic\"), # VD to fix calcpolicy enum\n", "    atlas.CalcPolicyEnum.ThermodynamicProcess_Polytropic: (\"ProcessPathType\", \"Polytropic\"), # VD to fix calcpolicy enum\n", "}\n", "\n", "# VD To update EQUIPMENT to reflect valid polices\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        Compressor1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(Compressor1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(Compressor1_unit, _attribute)\n", "    print(f\"Compressor1_unit.CalcMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 303, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.PressureIncrease: '666.0' (should be 666)\n", "ParameterEnum.OutletPressure: '666.0' (should be 666)\n", "ParameterEnum.AdiabaticEff: '666.0' (should be 666)\n", "ParameterEnum.PolytropicEff: '666.0' (should be 666)\n", "ParameterEnum.PowerRequired: '666.0' (should be 666)\n", "ParameterEnum.OutletTemperature: '666.0' (should be 666)\n", "ParameterEnum.TemperatureChange: '666.0' (should be 666)\n", "ParameterEnum.AdiabaticCoeff: '666.0' (should be 666)\n", "ParameterEnum.PolytropicCoeff: '666.0' (should be 666)\n", "ParameterEnum.AdiabaticHead: '666.0' (should be 666)\n", "ParameterEnum.PolytropicHead: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_FlowRate: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_Head: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_Power: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_Eff: '666.0' (should be 666)\n", "ParameterEnum.RotationSpeed: '666' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "    atlas.ParameterEnum.PressureIncrease: \"DeltaP\",  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Compressor.htm\n", "    atlas.ParameterEnum.OutletPressure: \"POut\",\n", "    atlas.ParameterEnum.AdiabaticEff: \"AdiabaticEfficiency\",\n", "    atlas.ParameterEnum.PolytropicEff: \"PolytropicEfficiency\",\n", "    atlas.ParameterEnum.PowerRequired: \"DeltaQ\",\n", "    atlas.ParameterEnum.OutletTemperature: \"OutletTemperature\",\n", "    atlas.ParameterEnum.TemperatureChange: \"DeltaT\",\n", "    atlas.ParameterEnum.AdiabaticCoeff: \"AdiabaticCoefficient\",\n", "    atlas.ParameterEnum.PolytropicCoeff: \"PolytropicEfficiency\",\n", "    atlas.ParameterEnum.AdiabaticHead: \"AdiabaticHead\",\n", "    atlas.ParameterEnum.PolytropicHead: \"PolytropicHead\",\n", "    atlas.ParameterEnum.PerformanceCurves_FlowRate: \"CurveFlow\",\n", "    atlas.ParameterEnum.PerformanceCurves_Head: \"CurveHead\",\n", "    atlas.ParameterEnum.PerformanceCurves_Power: \"CurvePower\",\n", "    atlas.ParameterEnum.PerformanceCurves_Eff: \"CurveEff\",\n", "    atlas.ParameterEnum.RotationSpeed: \"Speed\",\n", "}\n", "\n", "\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(Compressor1_unit, v, 666)\n", "    new_val = getattr(Compressor1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Valve"]}, {"cell_type": "code", "execution_count": 304, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> Valve1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Compressor'> Valve1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> Compressor1\n", "OutletPressure\n"]}], "source": ["# Create Entity \n", "Valve1 = sim.AddObject(ObjectType.Compressor,0,0,\"Valve1\")\n", "\n", "Valve1_sim = sim.GetObject(\"Valve1\")\n", "Valve1_unit = Valve1.GetAsObject()\n", "Valve1_graphic = Compressor1_unit.GraphicObject\n", "\n", "_items = [Valve1_sim,Valve1_unit,Valve1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "print(Valve1_unit.get_CalcMode())"]}, {"cell_type": "code", "execution_count": 305, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Valve1_unit.CalcMode = CalcPolicyEnum.PressureDrop_Valve vs Delta_P\n", "Valve1_unit.CalcMode = CalcPolicyEnum.OutletPressure_Valve vs OutletPressure\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "\"\"\"\n", "atlas.CalcPolicyEnum.PressureDrop_Valve: \"DeltaP\",\n", "atlas.CalcPolicyEnum.OutletPressure_Valve: \"OutletPressure\",\n", "\"\"\"    \n", "CALCPOLICY_KEYWORDS = {\n", "    atlas.CalcPolicyEnum.PressureDrop_Valve: (\"CalcMode\",\"Delta_P\"),\n", "    atlas.CalcPolicyEnum.OutletPressure_Valve: (\"CalcMode\",\"OutletPressure\"),\n", "}\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        Valve1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(Valve1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(Valve1_unit, _attribute)\n", "    print(f\"Valve1_unit.CalcMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 306, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.PressureDrop: '666.0' (should be 666)\n", "ParameterEnum.OutletPressure: '666' (should be 666)\n"]}], "source": ["# Set Policies\n", "PARAMETER_KEYWORDS = {\n", "    atlas.ParameterEnum.PressureDrop:\"DeltaP\",\n", "    atlas.ParameterEnum.OutletPressure:\"OutletPressure\",\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(Valve1_unit, v, 666)\n", "    new_val = getattr(Valve1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Expander \n", "\n", "- assume API for Expander will  be similar to Expander\n", "- reference links: ....\n", "- Need to cross check for the thermodynamic path of the Expander"]}, {"cell_type": "code", "execution_count": 307, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> Expander1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Compressor'> Expander1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> Expander1\n", "OutletPressure\n", "Adiabatic\n"]}], "source": ["# Create Entity \n", "Expander1 = sim.AddObject(ObjectType.Compressor,0,0,\"Expander1\")\n", "\n", "Expander1_sim = sim.GetObject(\"Expander1\")\n", "Expander1_unit = Expander1.GetAsObject()\n", "Expander1_graphic = Expander1_unit.GraphicObject\n", "\n", "_items = [Expander1_sim,Expander1_unit,Expander1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "print(Expander1_unit.get_CalcMode())\n", "print(Expander1_unit.get_ProcessPath())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### LO to solve for Adiabatic and Polytropic, both need to be passed as Enums\n", "### Set KnownHead Adiabatic Compressor"]}, {"cell_type": "code", "execution_count": 308, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Expander1_unit.CalcMode = CalcPolicyEnum.PerformanceCurves_Expander vs Curves\n", "Expander1_unit.CalcMode = CalcPolicyEnum.OutletPressure_Expander vs OutletPressure\n", "Expander1_unit.CalcMode = CalcPolicyEnum.Known_Head_Expander vs Head\n", "Expander1_unit.CalcMode = CalcPolicyEnum.PressureDecrease_Expander vs Delta_P\n", "Expander1_unit.CalcMode = CalcPolicyEnum.ThermodynamicProcess_Adiabatic vs Adiabatic\n", "Expander1_unit.CalcMode = CalcPolicyEnum.ThermodynamicProcess_Polytropic vs Polytropic\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "atlas.CalcPolicyEnum.OutletPressure_Expander: \"OutletPressure\",\n", "atlas.CalcPolicyEnum.PowerGenerated_Expander: \"PowerGenerated\",\n", "atlas.CalcPolicyEnum.PressureDecrease_Expander: \"Delta_P\",\n", "atlas.CalcPolicyEnum.PerformanceCurves_Expander: \"Curves\",\n", "atlas.CalcPolicyEnum.Known_Head_Expander: \"Head\",\n", "atlas.CalcPolicyEnum.ThermodynamicProcess_Adiabatic: \"Adiabatic\",\n", "atlas.CalcPolicyEnum.ThermodynamicProcess_Polytropic: \"Polytropic\",\n", "\"\"\"\n", "\n", "# from DWSIM.UnitOperations.UnitOperations import Expander\n", "\n", "# (<PERSON><PERSON><PERSON><PERSON>, <PERSON>umName)\n", "\n", "CALCPOLICY_KEYWORDS = {\n", "    atlas.CalcPolicyEnum.PerformanceCurves_Expander: (\"CalcMode\", \"Curves\"),\n", "    atlas.CalcPolicyEnum.OutletPressure_Expander: (\"CalcMode\", \"OutletPressure\"),\n", "    atlas.CalcPolicyEnum.Known_Head_Expander: (\"CalcMode\", \"Head\"),  # VD to fix calc policy enum\n", "    atlas.CalcPolicyEnum.PressureDecrease_Expander: (\"CalcMode\", \"Delta_P\"),\n", "    atlas.CalcPolicyEnum.ThermodynamicProcess_Adiabatic: (\"ProcessPathType\", \"Adiabatic\"), # VD to fix calcpolicy enum\n", "    atlas.CalcPolicyEnum.ThermodynamicProcess_Polytropic: (\"ProcessPathType\", \"Polytropic\"), # VD to fix calcpolicy enum\n", "    # atlas.CalcPolicyEnum.PowerGenerated_Expander: (\"CalcMode\", \"PowerGenerated\"), # TODO Fix the power generated calc policy\n", "}\n", "\n", "# VD To update EQUIPMENT to reflect valid polices\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        Expander1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(Expander1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(Expander1_unit, _attribute)\n", "    print(f\"Expander1_unit.CalcMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 309, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.PressureDecrease: '666.0' (should be 666)\n", "ParameterEnum.OutletPressure: '666.0' (should be 666)\n", "ParameterEnum.AdiabaticEff: '666.0' (should be 666)\n", "ParameterEnum.PolytropicEff: '666.0' (should be 666)\n", "ParameterEnum.PowerGenerated: '666.0' (should be 666)\n", "ParameterEnum.OutletTemperature: '666.0' (should be 666)\n", "ParameterEnum.TemperatureChange: '666.0' (should be 666)\n", "ParameterEnum.AdiabaticCoeff: '666.0' (should be 666)\n", "ParameterEnum.PolytropicCoeff: '666.0' (should be 666)\n", "ParameterEnum.AdiabaticHead: '666.0' (should be 666)\n", "ParameterEnum.PolytropicHead: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_FlowRate: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_Head: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_Power: '666.0' (should be 666)\n", "ParameterEnum.PerformanceCurves_Eff: '666.0' (should be 666)\n", "ParameterEnum.RotationSpeed: '666' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "    atlas.ParameterEnum.PressureDecrease: \"DeltaP\",  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Expander.htm\n", "    atlas.ParameterEnum.OutletPressure: \"POut\",\n", "    atlas.ParameterEnum.AdiabaticEff: \"AdiabaticEfficiency\",\n", "    atlas.ParameterEnum.PolytropicEff: \"PolytropicEfficiency\",\n", "    atlas.ParameterEnum.PowerGenerated: \"DeltaQ\",\n", "    atlas.ParameterEnum.OutletTemperature: \"OutletTemperature\",\n", "    atlas.ParameterEnum.TemperatureChange: \"DeltaT\",\n", "    atlas.ParameterEnum.AdiabaticCoeff: \"AdiabaticCoefficient\",\n", "    atlas.ParameterEnum.PolytropicCoeff: \"PolytropicCoefficient\",\n", "    atlas.ParameterEnum.AdiabaticHead: \"AdiabaticHead\",\n", "    atlas.ParameterEnum.PolytropicHead: \"PolytropicHead\",\n", "    atlas.ParameterEnum.PerformanceCurves_FlowRate: \"CurveFlow\",\n", "    atlas.ParameterEnum.PerformanceCurves_Head: \"CurveHead\",\n", "    atlas.ParameterEnum.PerformanceCurves_Power: \"CurvePower\",\n", "    atlas.ParameterEnum.PerformanceCurves_Eff: \"CurveEff\",\n", "    atlas.ParameterEnum.RotationSpeed: \"Speed\",\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(Expander1_unit, v, 666)\n", "    new_val = getattr(Expander1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Heater \n", "\n", "- API for Heater\n", "- reference links: ...."]}, {"cell_type": "code", "execution_count": 310, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> Heater1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Heater'> Heater1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> Heater1\n", "HeatAdded\n"]}], "source": ["# Create Entity \n", "Heater1 = sim.AddObject(ObjectType.Heater,0,0,\"Heater1\")\n", "\n", "Heater1_sim = sim.GetObject(\"Heater1\")\n", "Heater1_unit = Heater1.GetAsObject()\n", "Heater1_graphic = Heater1_unit.GraphicObject\n", "\n", "_items = [Heater1_sim,Heater1_unit,Heater1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "print(Heater1_unit.get_CalcMode())"]}, {"cell_type": "code", "execution_count": 311, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Heater1_unit.CalcMode = CalcPolicyEnum.HeatAddedAndRemoved vs HeatAddedRemoved\n", "Heater1_unit.CalcMode = CalcPolicyEnum.TemperatureChange vs TemperatureChange\n", "Heater1_unit.CalcMode = CalcPolicyEnum.OutletTemperature vs OutletTemperature\n", "Heater1_unit.CalcMode = CalcPolicyEnum.OutletVaporMoleFraction vs OutletVaporFraction\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "atlas.CalcPolicyEnum.HeatAddedAndRemoved: \"HeatAddedRemoved\",\n", "atlas.CalcPolicyEnum.TemperatureChange: \"TemperatureChange\",\n", "atlas.CalcPolicyEnum.OutletTemperature: \"OutletTemperature\",\n", "atlas.CalcPolicyEnum.OutletVaporMoleFraction: \"OutletVaporFraction\",\n", "\"\"\"\n", "\n", "# (<PERSON><PERSON><PERSON><PERSON>, <PERSON>umName)\n", "\n", "CALCPOLICY_KEYWORDS = {\n", "    atlas.CalcPolicyEnum.HeatAddedAndRemoved: (\"CalcMode\", \"HeatAddedRemoved\"),\n", "    atlas.CalcPolicyEnum.TemperatureChange: (\"CalcMode\",\"TemperatureChange\"),\n", "    atlas.CalcPolicyEnum.OutletTemperature: (\"CalcMode\",\"OutletTemperature\"),\n", "    atlas.CalcPolicyEnum.OutletVaporMoleFraction: (\"CalcMode\",\"OutletVaporFraction\"),\n", "}\n", "# VD To update EQUIPMENT to reflect valid polices\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        Heater1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(Heater1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(Heater1_unit, _attribute)\n", "    print(f\"Heater1_unit.CalcMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 312, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.Efficiency: '666.0' (should be 666)\n", "ParameterEnum.PressureDrop: '666.0' (should be 666)\n", "ParameterEnum.OutletTemperature: '666.0' (should be 666)\n", "ParameterEnum.HeatAddedOrRemoved: '666.0' (should be 666)\n", "ParameterEnum.OutletVaporFraction: '666.0' (should be 666)\n", "ParameterEnum.TemperatureChange: '666.0' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "    atlas.ParameterEnum.Efficiency: \"Eficiencia\", #https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Heater.htm\n", "    atlas.ParameterEnum.PressureDrop: \"DeltaP\",\n", "    atlas.ParameterEnum.OutletTemperature: \"OutletTemperature\",\n", "    atlas.ParameterEnum.HeatAddedOrRemoved: \"DeltaQ\",\n", "    atlas.ParameterEnum.OutletVaporFraction: \"OutletVaporFraction\",\n", "    atlas.ParameterEnum.TemperatureChange: \"DeltaT\",\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(Heater1_unit, v, 666)\n", "    new_val = getattr(Heater1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cooler\n", "\n", "- API for Heater\n", "- reference links: ...."]}, {"cell_type": "code", "execution_count": 313, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> Cooler1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Cooler'> Cooler1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> Heater1\n", "HeatRemoved\n"]}], "source": ["# Create Entity \n", "Cooler1 = sim.AddObject(ObjectType.Cooler,0,0,\"Cooler1\")\n", "\n", "Cooler1_sim = sim.GetObject(\"Cooler1\")\n", "Cooler1_unit = Cooler1.GetAsObject()\n", "Cooler1_graphic = Heater1_unit.GraphicObject\n", "\n", "_items = [Cooler1_sim,Cooler1_unit,Cooler1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "print(Cooler1_unit.get_CalcMode())"]}, {"cell_type": "code", "execution_count": 314, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cooler1_unit.CalcMode = CalcPolicyEnum.HeatAddedAndRemoved vs HeatRemoved\n", "Cooler1_unit.CalcMode = CalcPolicyEnum.TemperatureChange vs TemperatureChange\n", "Cooler1_unit.CalcMode = CalcPolicyEnum.OutletTemperature vs OutletTemperature\n", "Cooler1_unit.CalcMode = CalcPolicyEnum.OutletVaporMoleFraction vs OutletVaporFraction\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "atlas.CalcPolicyEnum.HeatAddedAndRemoved: \"HeatRemoved\",\n", "atlas.CalcPolicyEnum.TemperatureChange: \"TemperatureChange\",\n", "atlas.CalcPolicyEnum.OutletTemperature: \"OutletTemperature\",\n", "atlas.CalcPolicyEnum.OutletVaporMoleFraction: \"OutletVaporFraction\",\n", "\"\"\"\n", "\n", "# (<PERSON><PERSON><PERSON><PERSON>, <PERSON>umName)\n", "\n", "CALCPOLICY_KEYWORDS = {\n", "    atlas.CalcPolicyEnum.HeatAddedAndRemoved: (\"CalcMode\", \"HeatRemoved\"),\n", "    atlas.CalcPolicyEnum.TemperatureChange: (\"CalcMode\",\"TemperatureChange\"),\n", "    atlas.CalcPolicyEnum.OutletTemperature: (\"CalcMode\",\"OutletTemperature\"),\n", "    atlas.CalcPolicyEnum.OutletVaporMoleFraction: (\"CalcMode\",\"OutletVaporFraction\"),\n", "}\n", "# VD To update EQUIPMENT to reflect valid polices\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        Cooler1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(Cooler1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(Cooler1_unit, _attribute)\n", "    print(f\"Cooler1_unit.CalcMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 315, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.Efficiency: '666.0' (should be 666)\n", "ParameterEnum.PressureDrop: '666.0' (should be 666)\n", "ParameterEnum.OutletTemperature: '666.0' (should be 666)\n", "ParameterEnum.HeatAddedOrRemoved: '666.0' (should be 666)\n", "ParameterEnum.OutletVaporFraction: '666.0' (should be 666)\n", "ParameterEnum.TemperatureChange: '666.0' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "    atlas.ParameterEnum.Efficiency: \"Eficiencia\", # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Cooler.htm\n", "    atlas.ParameterEnum.PressureDrop: \"DeltaP\",\n", "    atlas.ParameterEnum.OutletTemperature: \"OutletTemperature\",\n", "    atlas.ParameterEnum.HeatAddedOrRemoved: \"DeltaQ\",\n", "    atlas.ParameterEnum.OutletVaporFraction: \"OutletVaporFraction\",\n", "    atlas.ParameterEnum.TemperatureChange: \"DeltaT\",\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(Cooler1_unit, v, 666)\n", "    new_val = getattr(Cooler1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## HeatExchanger\n", "- Need to check for flow direction\n", "- Need to check for calc modes"]}, {"cell_type": "code", "execution_count": 316, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> HEX1\n", "<class 'DWSIM.UnitOperations.UnitOperations.HeatExchanger'> HEX1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> HEX1\n", "CalcBothTemp_UA\n", "CounterCurrent\n"]}], "source": ["# Create Entity \n", "HEX1 = sim.AddObject(ObjectType.HeatExchanger,0,0,\"HEX1\")\n", "\n", "HEX1_sim = sim.GetObject(\"HEX1\")\n", "HEX1_unit = HEX1.GetAsObject()\n", "HEX1_graphic = HEX1_unit.GraphicObject\n", "\n", "_items = [HEX1_sim, HEX1_unit, HEX1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "print(HEX1_unit.get_CalculationMode())\n", "print(HEX1_unit.get_FlowDir())"]}, {"cell_type": "code", "execution_count": 317, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["HEX1_unit.CalcMode = CalcPolicyEnum.CalculateHotFluidOutletTemperature vs CalcTempHotOut\n", "HEX1_unit.CalcMode = CalcPolicyEnum.CalculateColdFluidOutletTemperature vs CalcTempColdOut\n", "HEX1_unit.CalcMode = CalcPolicyEnum.CalculateOutletTemperatures vs CalcBothTemp\n", "HEX1_unit.CalcMode = CalcPolicyEnum.CalculateOutletTemperatures_UA vs CalcBothTemp_UA\n", "HEX1_unit.CalcMode = CalcPolicyEnum.CalcualteArea vs CalcArea\n", "HEX1_unit.CalcMode = CalcPolicyEnum.ShellAndTubesExchangerRating vs ShellandTube_Rating\n", "HEX1_unit.CalcMode = CalcPolicyEnum.ShellAndTubesExchangerFoulingFactor vs ShellandTube_CalcFoulingFactor\n", "HEX1_unit.CalcMode = CalcPolicyEnum.PinchPoint vs PinchPoint\n", "HEX1_unit.CalcMode = CalcPolicyEnum.SpecifyHeatTransferEfficiency vs ThermalEfficiency\n", "HEX1_unit.CalcMode = CalcPolicyEnum.SpecifyOutletMolarVaporFraction_Stream1 vs OutletVaporFraction1\n", "HEX1_unit.CalcMode = CalcPolicyEnum.SpecifyOutletMolarVaporFraction_Stream2 vs OutletVaporFraction2\n", "HEX1_unit.CalcMode = CalcPolicyEnum.FlowDirection_CounterCurrent vs CounterCurrent\n", "HEX1_unit.CalcMode = CalcPolicyEnum.FlowDirection_CoCurrent vs CoCurrent\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "atlas.CalcPolicyEnum.CalculateHotFluidOutletTemperature: \"CalcTempHotOut\",\n", "atlas.CalcPolicyEnum.CalculateColdFluidOutletTemperature: \"CalcTempColdOut\",\n", "atlas.CalcPolicyEnum.CalculateOutletTemperatures: \"CalcBothTemp\",\n", "atlas.CalcPolicyEnum.CalculateOutletTemperatures_UA: \"CalcBothTemp_UA\",\n", "atlas.CalcPolicyEnum.CalcualteArea: \"CalcArea\",\n", "atlas.CalcPolicyEnum.ShellAndTubesExchangerRating: \"ShellandTube_Rating\",\n", "atlas.CalcPolicyEnum.ShellAndTubesExchangerFoulingFactor: \"ShellandTube_CalcFoulingFactor\",\n", "atlas.CalcPolicyEnum.PinchPoint: \"PinchPoint\",\n", "atlas.CalcPolicyEnum.SpecifyHeatTransferEfficiency: \"ThermalEfficiency\",\n", "atlas.CalcPolicyEnum.SpecifyOutletMolarVaporFraction_Stream1: \"OutletVaporFraction1\",\n", "atlas.CalcPolicyEnum.SpecifyOutletMolarVaporFraction_Stream2: \"OutletVaporFraction2\",\n", "\"\"\"\n", "\"\"\"\n", "CalcPolicyEnum.FlowDirection_CounterCurrent,\n", "CalcPolicyEnum.FlowDirection_CoCurrent,\n", "\"\"\"\n", "CALCPOLICY_KEYWORDS= {\n", "    atlas.CalcPolicyEnum.CalculateHotFluidOutletTemperature: (\"CalcMode\",\"CalcTempHotOut\"),\n", "    atlas.CalcPolicyEnum.CalculateColdFluidOutletTemperature: (\"CalcMode\",\"CalcTempColdOut\"),\n", "    atlas.CalcPolicyEnum.CalculateOutletTemperatures: (\"CalcMode\",\"CalcBothTemp\"),\n", "    atlas.CalcPolicyEnum.CalculateOutletTemperatures_UA: (\"CalcMode\",\"CalcBothTemp_UA\"),\n", "    atlas.CalcPolicyEnum.CalcualteArea: (\"CalcMode\",\"CalcArea\"),\n", "    atlas.CalcPolicyEnum.ShellAndTubesExchangerRating: (\"CalcMode\",\"ShellandTube_Rating\"),\n", "    atlas.CalcPolicyEnum.ShellAndTubesExchangerFoulingFactor: (\"CalcMode\",\"ShellandTube_CalcFoulingFactor\"),\n", "    atlas.CalcPolicyEnum.PinchPoint: (\"CalcMode\",\"PinchPoint\"),\n", "    atlas.CalcPolicyEnum.SpecifyHeatTransferEfficiency: (\"CalcMode\",\"ThermalEfficiency\"),\n", "    atlas.CalcPolicyEnum.SpecifyOutletMolarVaporFraction_Stream1: (\"CalcMode\",\"OutletVaporFraction1\"),\n", "    atlas.CalcPolicyEnum.SpecifyOutletMolarVaporFraction_Stream2: (\"CalcMode\",\"OutletVaporFraction2\"),\n", "    atlas.CalcPolicyEnum.FlowDirection_CounterCurrent: (\"FlowDir\", \"CounterCurrent\"),\n", "    atlas.CalcPolicyEnum.FlowDirection_CoCurrent: (\"FlowDir\", \"CoCurrent\"),\n", "}\n", "\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        HEX1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(HEX1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(HEX1_unit, _attribute)\n", "    print(f\"HEX1_unit.CalcMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 318, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.ColdFluidPressureDrop: '666.0' (should be 666)\n", "ParameterEnum.HotFluidPressureDrop: '666.0' (should be 666)\n", "ParameterEnum.ColdFluidOutletTemperature: '666.0' (should be 666)\n", "ParameterEnum.HotFluidOutletTemperature: '666.0' (should be 666)\n", "ParameterEnum.HeatExchangeArea: '666.0' (should be 666)\n", "ParameterEnum.HeatLoss: '666.0' (should be 666)\n", "ParameterEnum.GlobalHeatTransferCoefficient: '666.0' (should be 666)\n", "ParameterEnum.HeatExchange: '666.0' (should be 666)\n", "ParameterEnum.MinimumTemperatureDifference: '666.0' (should be 666)\n", "ParameterEnum.HeatTransferEfficiency: '666.0' (should be 666)\n", "ParameterEnum.OutletVaporFractionFluid1: '666.0' (should be 666)\n", "ParameterEnum.OutletVaporFractionFluid2: '666.0' (should be 666)\n", "ParameterEnum.ShellInSeries: '666' (should be 666)\n", "ParameterEnum.ShellPasses: '666' (should be 666)\n", "ParameterEnum.InternalDiameterOfShell: '666' (should be 666)\n", "ParameterEnum.BaffleCut: '666' (should be 666)\n", "ParameterEnum.BaffleSpacing: '666' (should be 666)\n", "ParameterEnum.InternalDiameterOfTube: '666' (should be 666)\n", "ParameterEnum.ExternalDiameterOfTube: '666' (should be 666)\n", "ParameterEnum.TubeLength: '666' (should be 666)\n", "ParameterEnum.ThermalConductivityOfTube: '666' (should be 666)\n", "ParameterEnum.PassesPerShell: '666' (should be 666)\n", "ParameterEnum.TubesPerShell: '666' (should be 666)\n", "ParameterEnum.TubeLayout_Triangle: '666' (should be 666)\n", "ParameterEnum.TubeLayout_Rotated_Triangle: '666' (should be 666)\n", "ParameterEnum.TubeLayout_Square: '666' (should be 666)\n", "ParameterEnum.TubeLayout_Rotated_Square: '666' (should be 666)\n", "ParameterEnum.FluidInTubes: '666' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "atlas.ParameterEnum.ColdFluidPressureDrop: \"ColdSidePressureDrop\",  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_HeatExchanger.htm\n", "atlas.ParameterEnum.HotFluidPressureDrop: \"HotSidePressureDrop\",\n", "atlas.ParameterEnum.ColdFluidOutletTemperature: \"ColdSideOutletTemperature\",\n", "atlas.ParameterEnum.HotFluidOutletTemperature: \"HotSideOutletTemperature\",\n", "atlas.ParameterEnum.HeatExchangeArea: \"Area\",\n", "atlas.ParameterEnum.HeatLoss: \"HeatLoss\",\n", "atlas.ParameterEnum.GlobalHeatTransferCoefficient: \"OverallCoefficient\",\n", "atlas.ParameterEnum.HeatExchange: \"Q\",\n", "atlas.ParameterEnum.MinimumTemperatureDifference: \"MITA\",\n", "atlas.ParameterEnum.HeatTransferEfficiency: \"ThermalEfficiency\",\n", "atlas.ParameterEnum.OutletVaporFractionFluid1: \"OutletVaporFraction1\",\n", "atlas.ParameterEnum.OutletVaporFractionFluid2: \"OutletVaporFraction2\",\n", "atlas.ParameterEnum.ShellInSeries: \"Shell_NumberOfShellsInSeries\",\n", "atlas.ParameterEnum.ShellPasses: \"Shell_NumberOfPasses\",\n", "atlas.ParameterEnum.InternalDiameterOfShell: \"Shell_Di\",\n", "atlas.ParameterEnum.BaffleCut: \"Shell_BaffleCut\",\n", "# ParamEnum.BaffleOrientation:\"\",\n", "atlas.ParameterEnum.BaffleSpacing: \"Shell_BaffleSpacing\",\n", "# ParamEnum.BaffleType:\"\",\n", "atlas.ParameterEnum.InternalDiameterOfTube: \"Tube_Di\",\n", "atlas.ParameterEnum.ExternalDiameterOfTube: \"Tube_De\",\n", "atlas.ParameterEnum.TubeLength: \"Tube_Length\",\n", "atlas.ParameterEnum.ThermalConductivityOfTube: \"Tube_ThermalConductivity\",\n", "atlas.ParameterEnum.PassesPerShell: \"Tube_PassesPerShell\",\n", "atlas.ParameterEnum.TubesPerShell: \"Tube_NumberPerShell\",\n", "atlas.ParameterEnum.TubeLayout_Triangle: \"\",\n", "atlas.ParameterEnum.TubeLayout_Rotated_Triangle: \"\",\n", "atlas.ParameterEnum.TubeLayout_Square: \"\",\n", "atlas.ParameterEnum.TubeLayout_Rotated_Square: \"\",\n", "atlas.ParameterEnum.FluidInTubes: \"\",\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(HEX1_unit, v, 666)\n", "    new_val = getattr(HEX1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Air Cooler2\n", "- Need to check for flow direction\n", "- Need to check for calc modes"]}, {"cell_type": "code", "execution_count": 319, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> AirCooler\n", "<class 'DWSIM.UnitOperations.UnitOperations.AirCooler2'> AirCooler\n", "<class 'DWSIM.Interfaces.IGraphicObject'> AirCooler\n", "SpecifyOutletTemperature\n"]}], "source": ["# Create Entity \n", "AirCooler = sim.AddObject(ObjectType.AirCooler2,0,0,\"AirCooler\")\n", "\n", "AirCooler_sim = sim.GetObject(\"AirCooler\")\n", "AirCooler_unit = AirCooler.GetAsObject()\n", "AirCooler_graphic = AirCooler_unit.GraphicObject\n", "\n", "_items = [AirCooler_sim, AirCooler_unit, AirCooler_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "print(AirCooler_unit.get_CalculationMode())"]}, {"cell_type": "code", "execution_count": 320, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["AirCooler_unit.CalcMode = CalcPolicyEnum.SpecifyOutletTemp vs SpecifyOutletTemperature\n", "AirCooler_unit.CalcMode = CalcPolicyEnum.SpecifyTubeGeometry vs SpecifyGeometry\n", "AirCooler_unit.CalcMode = CalcPolicyEnum.SpecifyOverallUA vs SpecifyUA\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "atlas.CalcPolicyEnum.SpecifyOutletTemp: \"SpecifyOutletTemperature\",\n", "atlas.CalcPolicyEnum.SpecifyTubeGeometry: \"SpecifyGeometry\",\n", "atlas.CalcPolicyEnum.SpecifyOverallUA: \"SpecifyUA\",\n", "\"\"\"\n", "CALCPOLICY_KEYWORDS= {\n", "    atlas.CalcPolicyEnum.SpecifyOutletTemp: (\"CalcMode\",\"SpecifyOutletTemperature\"),\n", "    atlas.CalcPolicyEnum.SpecifyTubeGeometry: (\"CalcMode\",\"SpecifyGeometry\"),\n", "    atlas.CalcPolicyEnum.SpecifyOverallUA: (\"CalcMode\",\"SpecifyUA\"),\n", "    }\n", "\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        AirCooler_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(AirCooler_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(AirCooler_unit, _attribute)\n", "    print(f\"AirCooler_unit.CalcMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 321, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.FluidPressureDrop: '666.0' (should be 666)\n", "ParameterEnum.OutletFluidTemperature: '666.0' (should be 666)\n", "ParameterEnum.InletAirTemperature: '666.0' (should be 666)\n", "ParameterEnum.InletAirPressure: '666.0' (should be 666)\n", "ParameterEnum.ReferenceRotationOfFan: '666.0' (should be 666)\n", "ParameterEnum.ReferenceAirFlow: '666.0' (should be 666)\n", "ParameterEnum.ActualRotation: '666.0' (should be 666)\n", "ParameterEnum.ElectricalPowerConversionFactor: '666.0' (should be 666)\n", "ParameterEnum.OutletAirTemperature: '666.0' (should be 666)\n", "ParameterEnum.OverallUA: '666.0' (should be 666)\n", "ParameterEnum.HeatExchange: '666.0' (should be 666)\n", "ParameterEnum.MaximumHeatExchange: '666.0' (should be 666)\n", "ParameterEnum.ExchangerEfficiency: '666.0' (should be 666)\n", "ParameterEnum.ElectricalPowerLoad: '666.0' (should be 666)\n", "ParameterEnum.InternalDiameterOfTube: '666.0' (should be 666)\n", "ParameterEnum.ExternalDiameterOfTube: '666.0' (should be 666)\n", "ParameterEnum.TubeLength: '666.0' (should be 666)\n", "ParameterEnum.ThermalConductivityOfTube: '666.0' (should be 666)\n", "ParameterEnum.NumberOfPasses: '666' (should be 666)\n", "ParameterEnum.NumberOfTubes: '666' (should be 666)\n", "ParameterEnum.TubeSpacing: '666' (should be 666)\n", "ParameterEnum.ActualAirFlow: '666.0' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "    atlas.ParameterEnum.FluidPressureDrop: \"PressureDrop\", #https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_AirCooler2.htm\n", "    atlas.ParameterEnum.OutletFluidTemperature: \"OutletTemperature\",\n", "    atlas.ParameterEnum.InletAirTemperature: \"AirInletTemperature\",\n", "    atlas.ParameterEnum.InletAirPressure: \"AirPressure\",\n", "    atlas.ParameterEnum.ReferenceRotationOfFan: \"ReferenceFanSpeed\",\n", "    atlas.ParameterEnum.ReferenceAirFlow: \"ReferenceAirFlow\",\n", "    atlas.ParameterEnum.ActualRotation: \"ActualFanSpeed\",\n", "    atlas.ParameterEnum.ElectricalPowerConversionFactor: \"ElectricalPowerConversionFactor\",\n", "    atlas.ParameterEnum.OutletAirTemperature: \"AirOutletTemperature\",\n", "    atlas.ParameterEnum.OverallUA: \"OverallUA\",\n", "    atlas.ParameterEnum.HeatExchange: \"HeatLoad\",\n", "    atlas.ParameterEnum.MaximumHeatExchange: \"MaxHeatExchange\",\n", "    atlas.ParameterEnum.ExchangerEfficiency: \"ExchangerEfficiency\",\n", "    atlas.ParameterEnum.ElectricalPowerLoad: \"ElectricalPowerLoad\",\n", "    atlas.ParameterEnum.InternalDiameterOfTube: \"Tube_Di\",\n", "    atlas.ParameterEnum.ExternalDiameterOfTube: \"Tube_De\",\n", "    atlas.ParameterEnum.TubeLength: \"Tube_Length\",\n", "    atlas.ParameterEnum.ThermalConductivityOfTube: \"Tube_ThermalConductivity\",\n", "    atlas.ParameterEnum.NumberOfPasses: \"Tube_PassesPerShell\",\n", "    atlas.ParameterEnum.NumberOfTubes: \"Tube_NumberPerShell\",\n", "    atlas.ParameterEnum.TubeSpacing: \"\",  # TBD\n", "    atlas.ParameterEnum.ActualAirFlow: \"ActualAirFlow\",\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(AirCooler_unit, v, 666)\n", "    new_val = getattr(AirCooler_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Mixer"]}, {"cell_type": "code", "execution_count": 322, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> MIX1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Mixer'> MIX1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> MIX1\n"]}, {"data": {"text/plain": ["<PressureBehavior.Minimum: 2>"]}, "execution_count": 322, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "MIX1 = sim.AddObject(ObjectType.Mixer,0,0,\"MIX1\")\n", "\n", "MIX1_sim = sim.GetObject(\"MIX1\")\n", "MIX1_unit = MIX1.GetAsObject()\n", "MIX1_graphic = MIX1_unit.GraphicObject\n", "\n", "_items = [MIX1_sim, MIX1_unit, MIX1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "MIX1_unit.get_PressureCalculation()"]}, {"cell_type": "code", "execution_count": 323, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MIX1_unit.PressureCalculation = CalcPolicyEnum.Inlet_Minimum vs Minimum\n", "MIX1_unit.PressureCalculation = CalcPolicyEnum.Inlet_Average vs Average\n", "MIX1_unit.PressureCalculation = CalcPolicyEnum.Inlet_Maximum vs Maximum\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "atlas.CalcPolicyEnum.Inlet_Minimum: \"Minimum\",\n", "atlas.CalcPolicyEnum.Inlet_Average: \"Average\",\n", "atlas.CalcPolicyEnum.Inlet_Maximum: \"Maximum\",\n", "\"\"\"\n", "CALCPOLICY_KEYWORDS = {\n", "atlas.CalcPolicyEnum.Inlet_Minimum: \"Minimum\",\n", "atlas.CalcPolicyEnum.Inlet_Average: \"Average\",\n", "atlas.CalcPolicyEnum.Inlet_Maximum: \"Maximum\",\n", "}\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    setattr(\n", "        MIX1_unit,\n", "        \"PressureCalculation\",\n", "        getattr(\n", "            getattr(MIX1_unit, \"PressureCalculation\"),\n", "            v\n", "        )\n", "    )\n", "\n", "    policy = getattr(MIX1_unit, \"PressureCalculation\")\n", "    print(f\"MIX1_unit.PressureCalculation = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 324, "metadata": {}, "outputs": [], "source": ["# Set Parameters (1 param, 1 section of code)\n", "# No parameters in this equipment 🕊️"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Splitter"]}, {"cell_type": "code", "execution_count": 325, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> SPL1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Splitter'> SPL1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> SPL1\n"]}, {"data": {"text/plain": ["<OpMode.SplitRatios: 0>"]}, "execution_count": 325, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "SPL1 = sim.AddObject(ObjectType.Splitter,0,0,\"SPL1\")\n", "\n", "SPL1_sim = sim.GetObject(\"SPL1\")\n", "SPL1_unit = SPL1.GetAsObject()\n", "SPL1_graphic = SPL1_unit.GraphicObject\n", "\n", "_items = [SPL1_sim, SPL1_unit, SPL1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "SPL1_unit.get_OperationMode()"]}, {"cell_type": "code", "execution_count": 326, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SPL1_unit.OperationMode = CalcPolicyEnum.Stream_Split_Ratios vs SplitRatios\n", "SPL1_unit.OperationMode = CalcPolicyEnum.Stream_Mass_Flow_Specs vs StreamMassFlowSpec\n", "SPL1_unit.OperationMode = CalcPolicyEnum.Stream_Mole_Flow_Specs vs StreamMoleFlowSpec\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "CalcPolicyEnum.Stream_Split_Ratios,\n", "CalcPolicyEnum.Stream_Mass_Flow_Specs,\n", "CalcPolicyEnum.Stream_Mole_Flow_Specs,\n", "\"\"\"\n", "CALCPOLICY_KEYWORDS = {\n", "atlas.CalcPolicyEnum.Stream_Split_Ratios: \"SplitRatios\",\n", "atlas.CalcPolicyEnum.Stream_Mass_Flow_Specs: \"StreamMassFlowSpec\",\n", "atlas.CalcPolicyEnum.Stream_Mole_Flow_Specs: \"StreamMoleFlowSpec\",\n", "}\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    setattr(\n", "        SPL1_unit,\n", "        \"OperationMode\",\n", "        getattr(\n", "            getattr(SPL1_unit, \"OperationMode\"),\n", "            v\n", "        )\n", "    )\n", "\n", "    policy = getattr(SPL1_unit, \"OperationMode\")\n", "    print(f\"SPL1_unit.OperationMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 327, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.Stream_1_Flow_Spec: '666.0' (should be 666)\n", "ParameterEnum.Stream_2_Flow_Spec: '666.0' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "atlas.ParameterEnum.Stream_1_Flow_Spec: \"StreamFlowSpec\",  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Splitter.htm\n", "# atlas.ParameterEnum.Stream_1_Split_Ratio: \"Ratios\",\n", "atlas.ParameterEnum.Stream_2_Flow_Spec: \"Stream2FlowSpec\",\n", "#atlas.ParameterEnum.Stream_2_Split_Ratio: \"Ratios\", #this one read only need to figure out a way to set this\n", "}\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(SPL1_unit, v, 666)\n", "    new_val = getattr(SPL1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> \n", "- need to check the calc policy in `equipment.py`"]}, {"cell_type": "code", "execution_count": 328, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> Vessel1\n", "<class 'DWSIM.UnitOperations.UnitOperations.Vessel'> Vessel1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> Vessel1\n"]}, {"data": {"text/plain": ["<PressureBehavior.Minimum: 2>"]}, "execution_count": 328, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "Vessel1 = sim.AddObject(ObjectType.Vessel,0,0,\"Vessel1\")\n", "\n", "V1_sim = sim.GetObject(\"Vessel1\")\n", "V1_unit = Vessel1.GetAsObject()\n", "V1_graphic = V1_unit.GraphicObject\n", "\n", "_items = [V1_sim, V1_unit, V1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "V1_unit.get_PressureCalculation()"]}, {"cell_type": "code", "execution_count": 329, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["V1_unit.PressureCalculation = CalcPolicyEnum.Inlet_Minimum vs Minimum\n", "V1_unit.PressureCalculation = CalcPolicyEnum.Inlet_Average vs Average\n", "V1_unit.PressureCalculation = CalcPolicyEnum.Inlet_Maximum vs Maximum\n"]}], "source": ["# Set Policies\n", "\"\"\"\n", "CalcPolicyEnum.Inlet_Minimum,\n", "CalcPolicyEnum.Inlet_Average,\n", "CalcPolicyEnum.Inlet_Maximum,\n", "\"\"\"\n", "CALCPOLICY_KEYWORDS = {\n", "        atlas.CalcPolicyEnum.Inlet_Minimum: \"Minimum\",\n", "        atlas.CalcPolicyEnum.Inlet_Average: \"Average\",\n", "        atlas.CalcPolicyEnum.Inlet_Maximum: \"Maximum\",\n", "    }\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    setattr(\n", "        V1_unit,\n", "        \"PressureCalculation\",\n", "        getattr(\n", "            getattr(V1_unit, \"PressureCalculation\"),\n", "            v\n", "        )\n", "    )\n", "\n", "    policy = getattr(V1_unit, \"PressureCalculation\")\n", "    print(f\"V1_unit.PressureCalculation = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": 330, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.OverrideSaturationTemp: '666.0' (should be 666)\n", "ParameterEnum.OverrideSaturationPressure: '666.0' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "        atlas.ParameterEnum.OverrideSaturationTemp: \"FlashTemperature\",  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_Vessel.htm\n", "        atlas.ParameterEnum.OverrideSaturationPressure: \"FlashPressure\",\n", "    }\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(V1_unit, v, 666)\n", "    new_val = getattr(V1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RCT_Conversion\n", "- Need to cross check the reactor operation mode"]}, {"cell_type": "code", "execution_count": 331, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> CONV1\n", "<class 'DWSIM.UnitOperations.Reactors.Reactor_Conversion'> CONV1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> CONV1\n"]}, {"data": {"text/plain": ["<OperationMode.Adiabatic: 1>"]}, "execution_count": 331, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "CONV1 = sim.AddObject(ObjectType.RCT_Conversion,0,0,\"CONV1\")\n", "\n", "CONV1_sim = sim.GetObject(\"CONV1\")\n", "CONV1_unit = CONV1.GetAsObject()\n", "CONV1_graphic = CONV1_unit.GraphicObject\n", "\n", "_items = [CONV1_sim, CONV1_unit, CONV1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "CONV1_unit.get_ReactorOperationMode()"]}, {"cell_type": "code", "execution_count": 335, "metadata": {}, "outputs": [{"ename": "ArgumentException", "evalue": "An item with the same key has already been added. Key: Dealkylation of toluene\n  at System.Collections.Generic.Dictionary`2[<PERSON><PERSON><PERSON>,TValue].TryInsert (TKey key, TValue value, System.Collections.Generic.InsertionBehavior behavior) [0x0015a] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at System.Collections.Generic.Dictionary`2[<PERSON><PERSON><PERSON>,TValue].Add (T<PERSON><PERSON> key, TValue value) [0x00000] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at DWSIM.FlowsheetBase.FlowsheetBase.AddReaction (DWSIM.Interfaces.IReaction reaction) [0x0000c] in <d95a20f5ada04ad9924f4d682f6d6cc7>:0 \n  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)\n  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <a17fa1457c5d44f2885ac746c1764ea5>:0 ", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mArgumentException\u001b[0m                         Traceback (most recent call last)", "Cell \u001b[0;32mIn[335], line 15\u001b[0m\n\u001b[1;32m      9\u001b[0m \u001b[38;5;66;03m# create conversion reaction object\u001b[39;00m\n\u001b[1;32m     10\u001b[0m \u001b[38;5;66;03m# https://dwsim.org/api_help/html/M_DWSIM_FlowsheetBase_FlowsheetBase_CreateConversionReaction.htm\u001b[39;00m\n\u001b[1;32m     12\u001b[0m kr1 \u001b[38;5;241m=\u001b[39m sim\u001b[38;5;241m.\u001b[***************************(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDealkylation of toluene\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDealkylation\u001b[39m\u001b[38;5;124m\"\u001b[39m, \n\u001b[1;32m     13\u001b[0m         comps, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMethanol\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mVapor\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m75\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 15\u001b[0m \u001b[43msim\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mAddReaction\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkr1\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     16\u001b[0m sim\u001b[38;5;241m.\u001b[39mAddReactionToSet(kr1\u001b[38;5;241m.\u001b[39mID, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDefaultSet\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mTrue\u001b[39;00m, \u001b[38;5;241m0\u001b[39m)\n\u001b[1;32m     17\u001b[0m CONV1_unit\u001b[38;5;241m.\u001b[39mReactionSetID\n", "\u001b[0;31mArgumentException\u001b[0m: An item with the same key has already been added. Key: Dealkylation of toluene\n  at System.Collections.Generic.Dictionary`2[<PERSON><PERSON><PERSON>,TValue].TryInsert (TKey key, TValue value, System.Collections.Generic.InsertionBehavior behavior) [0x0015a] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at System.Collections.Generic.Dictionary`2[<PERSON><PERSON><PERSON>,TValue].Add (T<PERSON><PERSON> key, TValue value) [0x00000] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at DWSIM.FlowsheetBase.FlowsheetBase.AddReaction (DWSIM.Interfaces.IReaction reaction) [0x0000c] in <d95a20f5ada04ad9924f4d682f6d6cc7>:0 \n  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)\n  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <a17fa1457c5d44f2885ac746c1764ea5>:0 "]}], "source": ["# add converison reaction\n", "\n", "# stoichiometric coefficients\n", "comps = Dictionary[str, float]()\n", "comps.Add(\"Methanol\", -1.0);\n", "comps.Add(\"Acetic acid\", 1.0);\n", "comps.Add(\"Carbon monoxide\", -1.0);\n", "\n", "# create conversion reaction object\n", "# https://dwsim.org/api_help/html/M_DWSIM_FlowsheetBase_FlowsheetBase_CreateConversionReaction.htm\n", "\n", "kr1 = sim.CreateConversionReaction(\"Dealkylation of toluene\", \"Dealkylation\", \n", "        comps, \"Methanol\", \"Vapor\", \"75\")\n", "\n", "sim.AddReaction(kr1)\n", "sim.AddReactionToSet(kr1.ID, \"DefaultSet\", True, 0)\n", "sim.addreac"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CONV1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_Conversion_Adiabatic vs Adiabatic\n", "CONV1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_Conversion_DefineOutletTemp vs OutletTemperature\n", "CONV1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_Conversion_Isothermic vs Isothermic\n"]}], "source": ["# Set Policies\n", "\"\"\"\"\n", "CalcPolicyEnum.RCT_Conversion_Adiabatic,\n", "CalcPolicyEnum.RCT_Conversion_Isothermic,\n", "CalcPolicyEnum.RCT_Conversion_DefineOutletTemp,\n", "\"\"\"\n", "CALCPOLICY_KEYWORDS = {\n", "    atlas.CalcPolicyEnum.RCT_Conversion_Adiabatic: (\"ReactorOperationMode\",\"Adiabatic\"),\n", "    atlas.CalcPolicyEnum.RCT_Conversion_DefineOutletTemp: (\"ReactorOperationMode\",\"OutletTemperature\"),\n", "    atlas.CalcPolicyEnum.RCT_Conversion_Isothermic: (\"ReactorOperationMode\",\"Isothermic\"),\n", "}\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        CONV1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(CONV1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(CONV1_unit, _attribute)\n", "    print(f\"CONV1_unit.ReactorOperationMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.PressureDrop: '666.0' (should be 666)\n", "ParameterEnum.OutletTemperature: '666.0' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "\n", "PARAMETER_KEYWORDS = {\n", "atlas.ParameterEnum.PressureDrop: \"DeltaP\",  # https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_Reactor_Conversion.htm\n", "#atlas.ParameterEnum.TemperatureDifference: \"DeltaT\", # This property will be calculated\n", "#atlas.ParameterEnum.HeatLoad: \"DeltaQ\", # This property will be calculated\n", "#atlas.ParameterEnum.Conversion: \"Conversions\", # This property will be calculated\n", "atlas.ParameterEnum.OutletTemperature: \"OutletTemperature\"\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(CONV1_unit, v, 666)\n", "    new_val = getattr(CONV1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RCT_Equilibrium\n", "- Need to check the calc policy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> EQ1\n", "<class 'DWSIM.UnitOperations.Reactors.Reactor_Equilibrium'> EQ1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> EQ1\n"]}, {"data": {"text/plain": ["<OperationMode.Adiabatic: 1>"]}, "execution_count": 179, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "EQ1 = sim.AddObject(ObjectType.RCT_Equilibrium,0,0,\"EQ1\")\n", "\n", "EQ1_sim = sim.GetObject(\"EQ1\")\n", "EQ1_unit = EQ1.GetAsObject()\n", "EQ1_graphic = EQ1_unit.GraphicObject\n", "\n", "_items = [EQ1_sim, EQ1_unit, EQ1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "EQ1_unit.get_ReactorOperationMode()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["EQ1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_Equilibrium_Adiabatic vs Adiabatic\n", "EQ1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_Equilibrium_DefineOutletTemp vs OutletTemperature\n", "EQ1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_Equilibrium_Isothermic vs Isothermic\n"]}], "source": ["# Set Policies\n", "\"\"\"\"\n", "atlas.CalcPolicyEnum.RCT_Equilibrium_Adiabatic: \"Adiabatic\",\n", "atlas.CalcPolicyEnum.RCT_Equilibrium_DefineOutletTemp: \"OutletTemperature\",\n", "atlas.CalcPolicyEnum.RCT_Equilibrium_Isothermic: \"Isothermic\",\n", "\"\"\"\n", "CALCPOLICY_KEYWORDS = {\n", "    atlas.CalcPolicyEnum.RCT_Equilibrium_Adiabatic: (\"ReactorOperationMode\",\"Adiabatic\"),\n", "    atlas.CalcPolicyEnum.RCT_Equilibrium_DefineOutletTemp: (\"ReactorOperationMode\",\"OutletTemperature\"),\n", "    atlas.CalcPolicyEnum.RCT_Equilibrium_Isothermic: (\"ReactorOperationMode\",\"Isothermic\"),\n", "}\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        EQ1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(EQ1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(EQ1_unit, _attribute)\n", "    print(f\"EQ1_unit.ReactorOperationMode = {k} vs {policy}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.PressureDrop: '666.0' (should be 666)\n", "ParameterEnum.OutletTemperature: '666.0' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "# Set Parameters (1 param, 1 section of code)\n", "\n", "PARAMETER_KEYWORDS = {\n", "atlas.ParameterEnum.PressureDrop: \"DeltaP\",  # https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_Reactors_Reactor_Equilibrium.htm\n", "# atlas.ParameterEnum.TemperatureDifference: \"DeltaT\", # This will be calculated\n", "# atlas.ParameterEnum.HeatLoad: \"DeltaQ\", # This will be calculated\n", "# atlas.ParameterEnum.Conversion: \"Conversions\", # This will be calculated\n", "atlas.ParameterEnum.OutletTemperature: \"OutletTemperature\"\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(EQ1_unit, v, 666)\n", "    new_val = getattr(EQ1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RCT_CSTR\n", "\n", "- assume API for RCT_PFR will be similar to RCT_CSTR\n", "- Calc modes check needs to be done\n", "- Parameters check as well from `equipments.py`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> CSTR1\n", "<class 'DWSIM.UnitOperations.Reactors.Reactor_CSTR'> CSTR1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> CSTR1\n"]}, {"data": {"text/plain": ["<OperationMode.Adiabatic: 1>"]}, "execution_count": 182, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "CSTR1 = sim.AddObject(ObjectType.RCT_CSTR,0,0,\"CSTR1\")\n", "\n", "CSTR1_sim = sim.GetObject(\"CSTR1\")\n", "CSTR1_unit = CSTR1.GetAsObject()\n", "CSTR1_graphic = CSTR1_unit.GraphicObject\n", "\n", "_items = [CSTR1_sim, CSTR1_unit, CSTR1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "CSTR1_unit.get_ReactorOperationMode()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Creating a reaction "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CSTR1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_CSTR_Adiabatic vs Adiabatic\n", "CSTR1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_CSTR_DefineOutletTemp vs OutletTemperature\n", "CSTR1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_CSTR_Isothermic vs Isothermic\n"]}], "source": ["# Set Policies\n", "\"\"\"\"\n", "CalcPolicyEnum.RCT_CSTR_Adiabatic,\n", "CalcPolicyEnum.RCT_CSTR_Isothermic,\n", "CalcPolicyEnum.RCT_CSTR_DefineOutletTemp,\n", "\"\"\"\n", "\n", "CALCPOLICY_KEYWORDS = {\n", "    atlas.CalcPolicyEnum.RCT_CSTR_Adiabatic: (\"ReactorOperationMode\",\"Adiabatic\"),\n", "    atlas.CalcPolicyEnum.RCT_CSTR_DefineOutletTemp: (\"ReactorOperationMode\",\"OutletTemperature\"),\n", "    atlas.CalcPolicyEnum.RCT_CSTR_Isothermic: (\"ReactorOperationMode\",\"Isothermic\"),\n", "}\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        CSTR1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(CSTR1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(CSTR1_unit, _attribute)\n", "    print(f\"CSTR1_unit.ReactorOperationMode = {k} vs {policy}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.ReactorVolume: '666.0' (should be 666)\n", "ParameterEnum.HeadSpace: '666.0' (should be 666)\n", "ParameterEnum.PressureDrop: '666.0' (should be 666)\n", "ParameterEnum.CatalystAmount: '666.0' (should be 666)\n"]}, {"data": {"text/plain": ["150.0"]}, "execution_count": 185, "metadata": {}, "output_type": "execute_result"}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "\n", "PARAMETER_KEYWORDS={\n", "    atlas.ParameterEnum.ReactorVolume: \"Volume\",  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_Reactors_Reactor_CSTR.htm\n", "    atlas.ParameterEnum.HeadSpace: \"Headspace\",\n", "    atlas.ParameterEnum.PressureDrop: \"DeltaP\",\n", "    atlas.ParameterEnum.CatalystAmount: \"CatalystAmount\",\n", "    # atlas.ParameterEnum.TemperatureDifference: \"DeltaT\", # This will be calculated\n", "    # atlas.ParameterEnum.HeatLoad: \"DeltaQ\", # This will be calculated\n", "    # atlas.ParameterEnum.Conversion: \"Conversions\", # This will be calculated\n", "    # atlas.ParameterEnum.VaporResidenceTime: \"ResidenceTimeV\", # This will be calculated\n", "    # atlas.ParameterEnum.LiquidResidenceTime: \"ResidenceTimeL\", # This will be calculated\n", "}\n", "\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(CSTR1_unit, v, 666)\n", "    new_val = getattr(CSTR1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")\n", "\n", "CSTR1_unit.set_Headspace(150)\n", "CSTR1_unit.get_Headspace()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RCT_PFR\n", "\n", "- assume API for RCT_PFR will be similar to RCT_CSTR\n", "- Calc modes check needs to be done\n", "- Parameters check as well from `equipments.py`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> PFR1\n", "<class 'DWSIM.UnitOperations.Reactors.Reactor_PFR'> PFR1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> PFR1\n"]}, {"data": {"text/plain": ["<OperationMode.Adiabatic: 1>"]}, "execution_count": 186, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "PFR1 = sim.AddObject(ObjectType.RCT_PFR,0,0,\"PFR1\")\n", "\n", "PFR1_sim = sim.GetObject(\"PFR1\")\n", "PFR1_unit = PFR1.GetAsObject()\n", "PFR1_graphic = PFR1_unit.GraphicObject\n", "\n", "_items = [PFR1_sim, PFR1_unit, PFR1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "PFR1_unit.get_ReactorOperationMode()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PFR1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_PFR_Adiabatic vs Adiabatic\n", "PFR1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_PFR_DefineNonAdiabaticNonIsothermal vs NonIsothermalNonAdiabatic\n", "PFR1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_PFR_DefineOutletTemp vs OutletTemperature\n", "PFR1_unit.ReactorOperationMode = CalcPolicyEnum.RCT_PFR_Isothermic vs Isothermic\n"]}], "source": ["# Set Policies\n", "\"\"\"\"\n", "CalcPolicyEnum.RCT_PFR_Adiabatic,\n", "CalcPolicyEnum.RCT_PFR_Isothermic,\n", "CalcPolicyEnum.RCT_PFR_DefineOutletTemp,\n", "CalcPolicyEnum.RCT_PFR_DefineNonAdiabaticNonIsothermal,\n", "\"\"\"\n", "CALCPOLICY_KEYWORDS = {\n", "atlas.CalcPolicyEnum.RCT_PFR_Adiabatic: (\"ReactorOperationMode\",\"Adiabatic\"),\n", "atlas.CalcPolicyEnum.RCT_PFR_DefineNonAdiabaticNonIsothermal: (\"ReactorOperationMode\",\"NonIsothermalNonAdiabatic\"),\n", "atlas.CalcPolicyEnum.RCT_PFR_DefineOutletTemp: (\"ReactorOperationMode\",\"OutletTemperature\"),\n", "atlas.CalcPolicyEnum.RCT_PFR_Isothermic: (\"ReactorOperationMode\",\"Isothermic\"),\n", "}\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "    _attribute, _enum_label = v\n", "    setattr(\n", "        PFR1_unit,\n", "        _attribute,\n", "        getattr(\n", "            getattr(PFR1_unit, _attribute),\n", "            _enum_label\n", "        )\n", "    )\n", "    policy = getattr(PFR1_unit, _attribute)\n", "    print(f\"PFR1_unit.ReactorOperationMode = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.ReactorVolume: '666.0' (should be 666)\n", "ParameterEnum.OutletTemperature: '666.0' (should be 666)\n", "ParameterEnum.TubeLength: '666.0' (should be 666)\n", "ParameterEnum.NumberOfTubes: '666' (should be 666)\n", "ParameterEnum.CatalystAmount: '666.0' (should be 666)\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "\n", "PARAMETER_KEYWORDS={\n", "        atlas.ParameterEnum.ReactorVolume: \"Volume\",  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_Reactors_Reactor_PFR.htm\n", "        atlas.ParameterEnum.OutletTemperature: \"OutletTemperature\",\n", "        #atlas.ParameterEnum.TemperatureDifference: \"DeltaT\", # This will be calculated\n", "        #atlas.ParameterEnum.HeatLoad: \"DeltaQ\", # This will be calculated\n", "        #atlas.ParameterEnum.Conversion: \"Conversions\", # This will be calculated\n", "        #atlas.ParameterEnum.PressureDrop: \"DeltaP\", # This will be calculated\n", "        #atlas.ParameterEnum.LiquidResidenceTime: \"ResidenceTime\", # This will be calculated\n", "        atlas.ParameterEnum.TubeLength: \"Length\",\n", "        atlas.ParameterEnum.NumberOfTubes: \"NumberOfTubes\",\n", "        atlas.ParameterEnum.CatalystAmount: \"CatalystLoading\",\n", "    }\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    setattr(PFR1_unit, v, 666)\n", "    new_val = getattr(PFR1_unit, v)\n", "    print(f\"{k}: '{new_val}' (should be 666)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## SC"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> SC1\n", "<class 'DWSIM.UnitOperations.UnitOperations.ShortcutColumn'> SC1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> SC1\n"]}, {"data": {"text/plain": ["<CondenserType.TotalCond: 0>"]}, "execution_count": 189, "metadata": {}, "output_type": "execute_result"}], "source": ["# Create Entity \n", "SC1 = sim.AddObject(ObjectType.ShortcutColumn,0,0,\"SC1\")\n", "\n", "SC1_sim = sim.GetObject(\"SC1\")\n", "SC1_unit = SC1.GetAsObject()\n", "SC1_graphic = SC1_unit.GraphicObject\n", "\n", "_items = [SC1_sim, SC1_unit, SC1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "SC1_unit.condtype"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SC1_unit.condtype = CalcPolicyEnum.CalcMode_SC_Total vs TotalCond\n", "SC1_unit.condtype = CalcPolicyEnum.CalcMode_SC_Partial vs PartialCond\n"]}], "source": ["# Set Parameters (1 param, 1 section of code)\n", "\"\"\"\"\n", "atlas.CalcPolicyEnum.CalcMode_SC_Total: \"TotalCond\",\n", "atlas.CalcPolicyEnum.CalcMode_SC_Partial: \"PartialCond\",\n", "\"\"\"\n", "from DWSIM.UnitOperations.UnitOperations import ShortcutColumn\n", "\n", "CALCPOLICY_KEYWORDS = {\n", "    atlas.CalcPolicyEnum.CalcMode_SC_Total: ShortcutColumn.CondenserType.TotalCond,\n", "    atlas.CalcPolicyEnum.CalcMode_SC_Partial: ShortcutColumn.CondenserType.PartialCond,\n", "}\n", "\n", "\n", "for k, v in CALCPOLICY_KEYWORDS.items():\n", "     setattr(\n", "         SC1_unit,\n", "         \"condtype\",\n", "         v\n", "     )\n", "\n", "     policy = getattr(SC1_unit, \"condtype\")\n", "     print(f\"SC1_unit.condtype = {k} vs {policy}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ParameterEnum.LightKeyCompound: 'int' value cannot be converted to System.String\n", "ParameterEnum.HeavyKeyCompound: 'int' value cannot be converted to System.String\n", "ParameterEnum.LightKeyMolFraction: '666.0' (should be 666)\n", "ParameterEnum.HeavyKeyMolFraction: '666.0' (should be 666)\n", "ParameterEnum.RefluxRatio: '666.0' (should be 666)\n", "ParameterEnum.CondenserPressure: '666.0' (should be 666)\n", "ParameterEnum.ReboilerPressure: '666.0' (should be 666)\n", "ParameterEnum.MinimumRefluxRatio: '666.0' (should be 666)\n", "ParameterEnum.MinimumNumberOfTrays: '666.0' (should be 666)\n", "ParameterEnum.ActualNumberOfTrays: '666.0' (should be 666)\n", "ParameterEnum.OptimalFeedStage: '666.0' (should be 666)\n", "ParameterEnum.StrippingLiquid: '666.0' (should be 666)\n", "ParameterEnum.RectifyingLiquid: '666.0' (should be 666)\n", "ParameterEnum.StrippingVapor: '666.0' (should be 666)\n", "ParameterEnum.RectifyingVapor: '666.0' (should be 666)\n", "ParameterEnum.CondenserDuty: '666.0' (should be 666)\n", "ParameterEnum.ReboilerDuty: '666.0' (should be 666)\n"]}], "source": ["# Set Policies\n", "# Set Parameters (1 param, 1 section of code)\n", "PARAMETER_KEYWORDS = {\n", "    atlas.ParameterEnum.LightKeyCompound: \"m_lightkey\",  # https://dwsim.org/api_help/html/Fields_T_DWSIM_UnitOperations_UnitOperations_ShortcutColumn.htm\n", "    atlas.ParameterEnum.HeavyKeyCompound: \"m_heavykey\",\n", "    atlas.ParameterEnum.LightKeyMolFraction: \"m_lightkeymolarfrac\",\n", "    atlas.ParameterEnum.HeavyKeyMolFraction: \"m_heavykeymolarfrac\",\n", "    atlas.ParameterEnum.RefluxRatio: \"m_refluxratio\",\n", "    atlas.ParameterEnum.CondenserPressure: \"m_condenserpressure\",\n", "    atlas.ParameterEnum.ReboilerPressure: \"m_boilerpressure\",\n", "    atlas.ParameterEnum.MinimumRefluxRatio: \"m_Rmin\",\n", "    atlas.ParameterEnum.MinimumNumberOfTrays: \"m_Nmin\",\n", "    atlas.ParameterEnum.ActualNumberOfTrays: \"m_N\",\n", "    atlas.ParameterEnum.OptimalFeedStage: \"ofs\",\n", "    atlas.ParameterEnum.StrippingLiquid: \"L_\",\n", "    atlas.ParameterEnum.RectifyingLiquid: \"L\",\n", "    atlas.ParameterEnum.StrippingVapor: \"V_\",\n", "    atlas.ParameterEnum.RectifyingVapor: \"V\",\n", "    atlas.ParameterEnum.CondenserDuty: \"m_Qc\",\n", "    atlas.ParameterEnum.ReboilerDuty: \"m_Qb\",\n", "}\n", "\n", "for k,v in PARAMETER_KEYWORDS.items():\n", "    try:\n", "        setattr(SC1_unit, v, 666)\n", "        new_val = getattr(SC1_unit, v)\n", "        print(f\"{k}: '{new_val}' (should be 666)\")\n", "    except Exception as process_path:\n", "        print(f\"{k}: {process_path}\")\n", "    \n", "SC1_unit.m_lightkey = \"Methane\"\n", "SC1_unit.m_heavykey = \"Water\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Distillation Column"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.ISimulationObject'> DC1\n", "<class 'DWSIM.UnitOperations.UnitOperations.DistillationColumn'> DC1\n", "<class 'DWSIM.Interfaces.IGraphicObject'> DC1\n", "Total_Condenser\n"]}, {"ename": "AttributeError", "evalue": "'DistillationColumn' object has no attribute 'reboilerspec'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[192], line 15\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28mprint\u001b[39m(DC1_unit\u001b[38;5;241m.\u001b[39mCondenserType)\n\u001b[1;32m     14\u001b[0m DC1_unit\u001b[38;5;241m.\u001b[39mSetCondenserSpec(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mReflux Ratio\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;241m3.0\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 15\u001b[0m \u001b[43mDC1_unit\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mreboilerspec\u001b[49m\n", "\u001b[0;31mAttributeError\u001b[0m: 'DistillationColumn' object has no attribute 'reboilerspec'"]}], "source": ["# Create Entity \n", "DC1 = sim.AddObject(ObjectType.DistillationColumn,0,0,\"DC1\")\n", "\n", "DC1_sim = sim.GetObject(\"DC1\")\n", "DC1_unit = DC1.GetAsObject()\n", "DC1_graphic = DC1_unit.GraphicObject\n", "\n", "_items = [DC1_sim, DC1_unit, DC1_graphic]\n", "\n", "for item in _items:\n", "    print(type(item), item)\n", "\n", "print(DC1_unit.CondenserType)\n", "DC1_unit.SetCondenserSpec(\"Reflux Ratio\", 3.0, \"\")\n", "DC1_unit.reboilerspec"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Set Policies"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n", "\n", "Code below this line here was written by V<PERSON>. \n", "It is an important reference. \n", "==Do not delete=="]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}