"""
Surrogate Model Factory Classes

This module provides factory classes for creating surrogate model configurations
optimized for different use cases, particularly testing scenarios where we need
deterministic, fast-executing model variants.

Design Philosophy (<PERSON> × <PERSON>):
- **Deterministic patterns**: All configurations use fixed seeds and parameters
- **Performance tiers**: Fast variants for unit tests, slow variants for integration
- **Information preservation**: Clear documentation of parameter choices
- **Composability**: Factories can be combined and extended
"""

from typing import Dict, Any, Optional, Literal
from ._enums import EnumSurrogateAlgorithm, EnumMetricName, Hyperparams
from .valueobjects import (
    VOConfig_Training,
    VOConfig_Model,
    VOConfig_HPO,
    VOConfig_Parameter,
    VOConfig_ParamBounds,
    VOMetadata_General
)


class RNNSurrogateTrainerFactory:
    """
    Factory for creating RNN surrogate trainer configurations optimized for testing.
    
    This factory creates deterministic configurations with two performance tiers:
    - **Fast**: Minimal parameters for unit tests (< 10 seconds execution)
    - **Slow**: Realistic parameters for integration tests (< 60 seconds execution)
    
    All configurations use fixed random seeds and simplified architectures to ensure
    reproducible test results while maintaining the essential training patterns.
    """

    @staticmethod
    def create_training_config(variant: Literal["fast", "slow"] = "fast") -> VOConfig_Training:
        """
        Create training configuration optimized for testing.
        
        Args:
            variant: Configuration variant - "fast" for unit tests, "slow" for integration
            
        Returns:
            VOConfig_Training with deterministic, test-optimized parameters
        """
        if variant == "fast":
            # Ultra-fast configuration for unit tests
            return VOConfig_Training(
                primary_metric=EnumMetricName.RMSE,
                max_iterations=5,  # Very few epochs
                random_seed=42,
                validation_strategy="simple_split",
                validation_split=0.2,
                enable_early_stopping=True,
                early_stopping_rounds=2,  # Stop quickly
                early_stopping_min_delta=0.01,  # Loose convergence criteria
                # Time series specific (minimal)
                ts_validation_window_size=5,
                ts_forecast_horizon=2,
                ts_validation_stride=1
            )
        elif variant == "slow":
            # More realistic configuration for integration tests
            return VOConfig_Training(
                primary_metric=EnumMetricName.RMSE,
                max_iterations=20,  # Moderate number of epochs
                random_seed=42,
                validation_strategy="simple_split",
                validation_split=0.2,
                enable_early_stopping=True,
                early_stopping_rounds=5,
                early_stopping_min_delta=0.001,
                # Time series specific
                ts_validation_window_size=10,
                ts_forecast_horizon=5,
                ts_validation_stride=1
            )
        else:
            raise ValueError(f"Unknown variant: {variant}. Must be 'fast' or 'slow'")

    @staticmethod
    def create_model_config(variant: Literal["fast", "slow"] = "fast") -> VOConfig_Model:
        """
        Create model configuration optimized for testing.
        
        Args:
            variant: Configuration variant - "fast" for unit tests, "slow" for integration
            
        Returns:
            VOConfig_Model with deterministic, test-optimized parameters
        """
        if variant == "fast":
            # Minimal architecture for fast execution
            params = [
                VOConfig_Parameter(
                    name=Hyperparams.NN.HIDDEN_SIZE,
                    value=8,  # Very small hidden size
                    tunable=False,  # Fixed for deterministic tests
                    bounds=VOConfig_ParamBounds(type="integer", min_value=8, max_value=16)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.SEQ_NUM_LAYERS,
                    value=1,  # Single layer
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="integer", min_value=1, max_value=1)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.DENSE_NUM_LAYERS,
                    value=1,  # Single dense layer
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="integer", min_value=1, max_value=1)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.RNN_TYPE,
                    value="LSTM",  # Fixed type
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="categorical", choices=["LSTM"])
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.DROPOUT,
                    value=0.0,  # No dropout for speed
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="continuous", min_value=0.0, max_value=0.1)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.BATCH_SIZE,
                    value=16,  # Small batch size
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="categorical", choices=[16])
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.LEARNING_RATE,
                    value=0.01,  # Higher learning rate for faster convergence
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="continuous", min_value=0.01, max_value=0.01)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.LOSS_FUNCTION,
                    value="mse",
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="categorical", choices=["mse"])
                )
            ]
        elif variant == "slow":
            # More realistic architecture for integration tests
            params = [
                VOConfig_Parameter(
                    name=Hyperparams.NN.HIDDEN_SIZE,
                    value=32,  # Moderate hidden size
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="integer", min_value=16, max_value=64)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.SEQ_NUM_LAYERS,
                    value=2,  # Two layers
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="integer", min_value=1, max_value=2)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.DENSE_NUM_LAYERS,
                    value=2,  # Two dense layers
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="integer", min_value=1, max_value=2)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.RNN_TYPE,
                    value="LSTM",
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="categorical", choices=["LSTM", "GRU"])
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.DROPOUT,
                    value=0.1,  # Light dropout
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="continuous", min_value=0.0, max_value=0.2)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.BATCH_SIZE,
                    value=32,
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="categorical", choices=[32])
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.LEARNING_RATE,
                    value=0.001,  # Standard learning rate
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="continuous", min_value=0.001, max_value=0.001)
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.LOSS_FUNCTION,
                    value="mse",
                    tunable=False,
                    bounds=VOConfig_ParamBounds(type="categorical", choices=["mse"])
                )
            ]
        else:
            raise ValueError(f"Unknown variant: {variant}. Must be 'fast' or 'slow'")

        return VOConfig_Model(
            algorithm=EnumSurrogateAlgorithm.RNN_TS,
            parameters=params
        )

    @staticmethod
    def create_hpo_config(variant: Literal["fast", "slow"] = "fast") -> VOConfig_HPO:
        """
        Create HPO configuration optimized for testing.
        
        Args:
            variant: Configuration variant - "fast" for unit tests, "slow" for integration
            
        Returns:
            VOConfig_HPO with deterministic, test-optimized parameters
        """
        if variant == "fast":
            # Minimal HPO for unit tests
            return VOConfig_HPO(
                is_enable=True,
                n_trials=3,  # Very few trials
                sampler="random",  # Fastest sampler
                pruner="none",  # No pruning for simplicity
                timeout_seconds=30,  # Short timeout
                n_parallel_jobs=1  # Single threaded
            )
        elif variant == "slow":
            # More realistic HPO for integration tests
            return VOConfig_HPO(
                is_enable=True,
                n_trials=10,  # Moderate number of trials
                sampler="tpe",  # More sophisticated sampler
                pruner="median",  # Enable pruning
                timeout_seconds=120,  # Longer timeout
                n_parallel_jobs=1  # Still single threaded for determinism
            )
        else:
            raise ValueError(f"Unknown variant: {variant}. Must be 'fast' or 'slow'")

    @classmethod
    def create_complete_config(
        cls,
        variant: Literal["fast", "slow"] = "fast",
        user_ref: str = "test_user",
        atlas_ref: str = "test_atlas"
    ) -> Dict[str, Any]:
        """
        Create a complete configuration bundle for testing.
        
        This method provides a convenient way to get all required configurations
        for testing the training pipeline end-to-end.
        
        Args:
            variant: Configuration variant - "fast" for unit tests, "slow" for integration
            user_ref: User reference for metadata
            atlas_ref: Atlas reference for metadata
            
        Returns:
            Dictionary containing all configuration objects needed for testing
        """
        metadata = VOMetadata_General(
            user_reference=user_ref,
            atlas_reference=atlas_ref,
            surrogate_algo=EnumSurrogateAlgorithm.RNN_TS,
            label=f"test_model_{variant}",
            description=f"Test RNN surrogate model with {variant} configuration for automated testing"
        )

        return {
            "metadata": metadata,
            "training_config": cls.create_training_config(variant),
            "model_config": cls.create_model_config(variant),
            "hpo_config": cls.create_hpo_config(variant)
        }
