"""
Trainer Registry Module

This module provides centralized algorithm-to-trainer mapping functionality,
eliminating code duplication across different training runners and ensuring
consistent trainer resolution throughout the system.

Design Philosophy (<PERSON>):
- **Single Source of Truth**: All algorithm mappings centralized in one location
- **Extensibility**: Registration mechanism supports dynamic algorithm addition
- **Operational Excellence**: Eliminates mapping drift between execution environments
- **Testability**: Isolated registry enables comprehensive validation
"""

import logging
from typing import Type, Dict, Callable, Optional, List, Literal

from backend.core._surrogate._enums import EnumSurrogateAlgorithm
from backend.core._surrogate.trainers.trainer_base import SurrogateTrainerPort

# Type alias for supported training services
TrainingService = Literal["azure", "local", "aws", "gcp"]


class TrainerRegistryError(Exception):
    """Exception raised by TrainerRegistry operations."""

    def __init__(self, message: str, original_error: Optional[Exception] = None):
        super().__init__(message)
        self.original_error = original_error


class TrainerRegistry:
    """
    Central registry for algorithm-to-trainer mappings.
    
    This class provides a single point of maintenance for all algorithm-to-trainer
    mappings, ensuring consistency across different execution environments and
    eliminating code duplication.
    
    Key Features:
    - **Centralized Mapping**: Single source of truth for all trainer mappings
    - **Dynamic Registration**: Support for runtime algorithm registration
    - **Validation**: Comprehensive error handling for unsupported algorithms
    - **Extensibility**: Easy addition of new algorithms without runner modifications
    """

    def __init__(self):
        """Initialize the trainer registry with default mappings."""
        self.logger = logging.getLogger(self.__class__.__name__)
        self._trainer_map: Dict[EnumSurrogateAlgorithm, Callable[[], Type[SurrogateTrainerPort]]] = {}
        self._base_image_map: Dict[EnumSurrogateAlgorithm, Dict[TrainingService, str]] = {}
        self._initialize_default_mappings()

    def _initialize_default_mappings(self) -> None:
        """Initialize the registry with default algorithm-to-trainer mappings."""
        try:
            # Import trainers dynamically to avoid circular imports
            from backend.core._surrogate.trainers.trainer_rnn import Seq2SeqTSTrainer
            
            # Register default mappings
            self._trainer_map = {
                # Deep learning trainers
                EnumSurrogateAlgorithm.RNN_TS: lambda: Seq2SeqTSTrainer,
                
                # Traditional ML trainers (commented out until implemented)
                # EnumSurrogateAlgorithm.RANDOM_FOREST: lambda: RandomForestTrainer,
                # EnumSurrogateAlgorithm.GRADIENT_BOOSTING: lambda: GradientBoostingTrainer,
                
                # Time series traditional ML trainers (commented out until implemented)
                # EnumSurrogateAlgorithm.RANDOM_FOREST_TS: lambda: TimeSeriesRandomForestTrainer,
                # EnumSurrogateAlgorithm.GRADIENT_BOOSTING_TS: lambda: TimeSeriesGradientBoostingTrainer,
            }
            
            # Initialize base image mappings for different training services
            self._base_image_map = {
                EnumSurrogateAlgorithm.RNN_TS: {
                    "azure": "azureml://registries/azureml/environments/acpt-pytorch-2.2-cuda12.1/version/37",  # Closest to PyTorch 2.4.1+cu121
                    "local": "pytorch/pytorch:2.4.1-cuda12.1-cudnn9-devel",
                    "aws": "763104351884.dkr.ecr.us-west-2.amazonaws.com/pytorch-training:2.4.1-gpu-py311-cu121-ubuntu20.04-sagemaker",
                    "gcp": "gcr.io/deeplearning-platform-release/pytorch-gpu.2-4:latest"
                },
                # EnumSurrogateAlgorithm.RANDOM_FOREST: {
                #     "azure": "mcr.microsoft.com/azureml/curated/sklearn-1.5-ubuntu20.04-py38-cpu:latest",
                #     "local": "python:3.10-slim",
                #     "aws": "683313688378.dkr.ecr.us-west-2.amazonaws.com/sagemaker-scikit-learn:1.2-1-cpu-py3",
                #     "gcp": "gcr.io/deeplearning-platform-release/sklearn-cpu:latest"
                # },
            }
            
            self.logger.debug(f"Initialized trainer registry with {len(self._trainer_map)} mappings")
            
        except ImportError as e:
            self.logger.error(f"Failed to import trainer classes during registry initialization: {e}")
            raise TrainerRegistryError(f"Trainer registry initialization failed", original_error=e)

    def get_trainer_class(self, algorithm: EnumSurrogateAlgorithm) -> Type[SurrogateTrainerPort]:
        """
        Get the trainer class for the specified algorithm.
        
        Args:
            algorithm: The surrogate algorithm enum
            
        Returns:
            The appropriate trainer class
            
        Raises:
            TrainerRegistryError: If algorithm is not supported or trainer cannot be accessed
        """
        try:
            trainer_factory = self._trainer_map.get(algorithm)
            if not trainer_factory:
                available_algorithms = list(self._trainer_map.keys())
                raise TrainerRegistryError(
                    f"Unsupported algorithm: {algorithm}. "
                    f"Available algorithms: {[alg.value for alg in available_algorithms]}"
                )
            
            trainer_cls = trainer_factory()
            self.logger.debug(f"Retrieved trainer class {trainer_cls.__name__} for algorithm {algorithm.value}")
            return trainer_cls
            
        except (AttributeError, ImportError) as e:
            self.logger.error(f"Error accessing trainer class for algorithm {algorithm}: {e}")
            raise TrainerRegistryError(f"Failed to access trainer for algorithm {algorithm}", original_error=e)

    def get_base_image(self, algorithm: EnumSurrogateAlgorithm, training_service: TrainingService) -> str:
        """
        Get the appropriate Docker base image for the specified algorithm and training service.

        This method provides algorithm-specific Docker images optimized for different training
        services (Azure ML, local development, AWS SageMaker, GCP AI Platform, etc.).

        Args:
            algorithm: The surrogate algorithm enum
            training_service: The target training service ("azure", "local", "aws", "gcp")

        Returns:
            Docker image name/URI appropriate for the algorithm and service

        Raises:
            TrainerRegistryError: If algorithm or training service is not supported

        Examples:
            >>> registry = TrainerRegistry()
            >>> # Get Azure ML image for RNN training
            >>> image = registry.get_base_image(EnumSurrogateAlgorithm.RNN_TS, "azure")
            >>> print(image)  # "acpt-pytorch-2.2-cuda12.1"

            >>> # Get local development image for RNN training
            >>> image = registry.get_base_image(EnumSurrogateAlgorithm.RNN_TS, "local")
            >>> print(image)  # "pytorch/pytorch:2.4.1-cuda12.1-cudnn9-devel"
        """
        try:
            # Check if algorithm is supported
            if algorithm not in self._base_image_map:
                available_algorithms = list(self._base_image_map.keys())
                raise TrainerRegistryError(
                    f"No base image mapping found for algorithm: {algorithm}. "
                    f"Available algorithms: {[alg.value for alg in available_algorithms]}"
                )

            algorithm_images = self._base_image_map[algorithm]

            # Check if training service is supported for this algorithm
            if training_service not in algorithm_images:
                available_services = list(algorithm_images.keys())
                raise TrainerRegistryError(
                    f"Training service '{training_service}' not supported for algorithm {algorithm}. "
                    f"Available services: {available_services}"
                )

            image_name = algorithm_images[training_service]
            self.logger.debug(f"Retrieved base image '{image_name}' for algorithm {algorithm.value} on {training_service}")
            return image_name

        except Exception as e:
            if isinstance(e, TrainerRegistryError):
                raise
            self.logger.error(f"Error retrieving base image for algorithm {algorithm} on {training_service}: {e}")
            raise TrainerRegistryError(f"Failed to get base image", original_error=e)

    def register_base_image(self,
                           algorithm: EnumSurrogateAlgorithm,
                           training_service: TrainingService,
                           image_name: str) -> None:
        """
        Register a base image for a specific algorithm and training service.

        This method allows dynamic registration of new image mappings, supporting
        extensibility for custom algorithms or updated base images.

        Args:
            algorithm: The algorithm enum to register the image for
            training_service: The target training service
            image_name: The Docker image name/URI

        Raises:
            TrainerRegistryError: If registration fails due to invalid inputs
        """
        try:
            # Initialize algorithm entry if it doesn't exist
            if algorithm not in self._base_image_map:
                self._base_image_map[algorithm] = {}

            # Register the image mapping
            self._base_image_map[algorithm][training_service] = image_name

            self.logger.info(f"Registered base image '{image_name}' for algorithm {algorithm.value} on {training_service}")

        except Exception as e:
            self.logger.error(f"Failed to register base image for {algorithm} on {training_service}: {e}")
            raise TrainerRegistryError(f"Base image registration failed", original_error=e)

    def get_supported_training_services(self, algorithm: EnumSurrogateAlgorithm) -> List[TrainingService]:
        """
        Get the list of supported training services for a specific algorithm.

        Args:
            algorithm: The algorithm enum to check

        Returns:
            List of supported training services for the algorithm

        Raises:
            TrainerRegistryError: If algorithm is not supported
        """
        if algorithm not in self._base_image_map:
            raise TrainerRegistryError(f"Algorithm {algorithm} is not supported")

        return list(self._base_image_map[algorithm].keys())

    def get_algorithm_image_summary(self) -> Dict[EnumSurrogateAlgorithm, Dict[TrainingService, str]]:
        """
        Get a complete summary of all algorithm-to-image mappings.

        Returns:
            Dictionary mapping algorithms to their training service image mappings
        """
        return dict(self._base_image_map)

    def register_trainer(self, 
                        algorithm: EnumSurrogateAlgorithm, 
                        trainer_cls: Type[SurrogateTrainerPort]) -> None:
        """
        Register a new algorithm-trainer mapping.
        
        This method allows dynamic registration of new trainers, supporting
        extensibility without modifying the core registry code.
        
        Args:
            algorithm: The algorithm enum to register
            trainer_cls: The trainer class to associate with the algorithm
            
        Raises:
            TrainerRegistryError: If registration fails due to invalid inputs
        """
        try:
            # Validate that trainer_cls is actually a trainer class
            if not issubclass(trainer_cls, SurrogateTrainerPort):
                raise TrainerRegistryError(
                    f"Trainer class {trainer_cls} must inherit from SurrogateTrainerPort"
                )
            
            # Register the mapping using a lambda to maintain consistency
            self._trainer_map[algorithm] = lambda: trainer_cls
            
            self.logger.info(f"Registered trainer {trainer_cls.__name__} for algorithm {algorithm.value}")
            
        except Exception as e:
            self.logger.error(f"Failed to register trainer {trainer_cls} for algorithm {algorithm}: {e}")
            raise TrainerRegistryError(f"Trainer registration failed", original_error=e)

    def unregister_trainer(self, algorithm: EnumSurrogateAlgorithm) -> None:
        """
        Unregister an algorithm-trainer mapping.
        
        Args:
            algorithm: The algorithm enum to unregister
            
        Raises:
            TrainerRegistryError: If algorithm is not currently registered
        """
        if algorithm not in self._trainer_map:
            raise TrainerRegistryError(f"Algorithm {algorithm} is not registered")
        
        del self._trainer_map[algorithm]
        self.logger.info(f"Unregistered trainer for algorithm {algorithm.value}")

    def list_registered_algorithms(self) -> List[EnumSurrogateAlgorithm]:
        """
        Get a list of all registered algorithms.
        
        Returns:
            List of registered algorithm enums
        """
        return list(self._trainer_map.keys())

    def is_algorithm_supported(self, algorithm: EnumSurrogateAlgorithm) -> bool:
        """
        Check if an algorithm is supported by the registry.
        
        Args:
            algorithm: The algorithm enum to check
            
        Returns:
            True if algorithm is supported, False otherwise
        """
        return algorithm in self._trainer_map

    def validate_registry(self) -> Dict[EnumSurrogateAlgorithm, bool]:
        """
        Validate all registered trainer mappings.
        
        This method attempts to instantiate each registered trainer to ensure
        they are accessible and properly configured.
        
        Returns:
            Dictionary mapping algorithms to validation status (True = valid, False = invalid)
        """
        validation_results = {}
        
        for algorithm in self._trainer_map:
            try:
                trainer_cls = self.get_trainer_class(algorithm)
                # Attempt to instantiate the trainer to validate it's accessible
                _ = trainer_cls()  # Just validate instantiation, don't need the instance
                validation_results[algorithm] = True
                self.logger.debug(f"Validation successful for {algorithm.value}")
                
            except Exception as e:
                validation_results[algorithm] = False
                self.logger.warning(f"Validation failed for {algorithm.value}: {e}")
        
        return validation_results


# Global default instance for backward compatibility
_default_registry: Optional[TrainerRegistry] = None


def get_default_trainer_registry() -> TrainerRegistry:
    """
    Get the default trainer registry instance.
    
    This function provides a singleton pattern for the default registry,
    ensuring consistent behavior across the application while allowing
    for dependency injection when needed.
    
    Returns:
        The default TrainerRegistry instance
    """
    global _default_registry
    if _default_registry is None:
        _default_registry = TrainerRegistry()
    return _default_registry


def reset_default_trainer_registry() -> None:
    """
    Reset the default trainer registry instance.
    
    This function is primarily useful for testing scenarios where
    a fresh registry state is needed.
    """
    global _default_registry
    _default_registry = None
