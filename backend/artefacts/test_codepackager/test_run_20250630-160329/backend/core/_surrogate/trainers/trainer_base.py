from __future__ import annotations

import optuna
import logging
import time
from typing import Dict, List, Any, Optional, Union, Tuple, Callable, Type, TypeVar, Generic
import numpy.typing as npt
import sklearn
from sklearn.inspection import permutation_importance
from sklearn.metrics import (mean_squared_error, mean_absolute_error, 
                                   r2_score, max_error, mean_absolute_percentage_error)

from .._imports import *
from .._enums import *
from .._utilities import *

from ..entities import *
from ..valueobjects import *



# Define TypeVar for model types
ModelT = TypeVar('ModelT')

class SurrogateTrainerPort(ABC, Generic[ModelT]):
    """
    Abstract base class for all surrogate model trainers.
    Defines the common interface and functionality shared by all trainers.
    """
    RANDOM_SEED = 42
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self._random_seed = self.__class__.RANDOM_SEED

    @property
    def random_seed(self) -> int:
        """Random seed for reproducibility."""
        if hasattr(self, "_random_seed"):
            return self._random_seed
        else :
            return self.RANDOM_SEED

    @random_seed.setter
    def random_seed(self, value: int) -> None:
        """Set the random seed for reproducibility."""
        self._random_seed = value
        self._set_random_seed(value)

    def _set_random_seed(self, seed: int):
        """Set random seed for reproducibility"""
        np.random.seed(seed)
        random.seed(seed)

    # ---------------------------------------
    # SURROGATE

    def train(
        self,
        metadata: VOMetadata_General,
        training_data: VODataset,
        training_config: VOConfig_Training,
        model_config: VOConfig_Model,
        hpo_config: Optional[VOConfig_HPO] = None,
        job: Optional[ENTTrainingJob] = None,
        x_test: Optional [ np.ndarray ] = None,
        y_test: Optional [ np.ndarray ]= None,
        logging_callback: Optional[Callable] = None
    ) -> Tuple[ModelT, ENTTrainingJob]:
        """
        Train model with provided data and configuration.
        If hpo_config is provided, performs hyperparameter optimization.
            
        Returns:
            Tuple of (trained model, updated training job with results)
        """
        # Create job if not provided
        if job is None:
            job = ENTTrainingJob(
                    metadata=metadata,
                    training_configuration=training_config,
                    model_configuration=model_config,
                    training_data_uid=training_data.uid,
                    status="TRAINING",
                    environment_info=self._collect_environment_info()
            )
        
        try:
            start_time = time.time()

            # -------------------------
            # HPO (optional) 
            if hpo_config is not None and hpo_config.is_enable == True:
                # Execute HPO
                hpo_results = self.run_hpo(
                    training_data=training_data,
                    training_config=training_config,
                    model_config=model_config,
                    hpo_config=hpo_config
                )
                
                # Store HPO results in job
                job.hpo_results = hpo_results
                model_config = model_config.update_params(hpo_results.best_params)
                job.model_configuration= model_config
                
                job.update_status(EnumTrainingStatus.TRAINING, "Optimization Done")

                if logging_callback is not None:
                    logging_callback()

                    
            # -------------------------
            # TRAIN FULL MODEL

            self.logger.info(f"Performing standard training for {metadata.surrogate_algo}")
            model = self._run_standard_training(
                    training_data=training_data,
                    training_config=training_config,
                    model_config= model_config,
                    job = job,
            )
            self._model = model

            # -------------------------
            # CREATE TRAINING RESULTS
            
            # 1. GET TEST DATA
            self.logger.info("Preparing test data for model evaluation")

            if x_test is not None and y_test is not None:
                # Use provided test data
                self.logger.info("Using provided test data for evaluation")
                test_x, test_y = x_test, y_test
                test_uid = None  # External test data has no stored UID

            else:
                self.logger.info("Using random split for data")
                _, eval_split = split_data_simple(training_data, 0.2, self.random_seed)
                
                # Get arrays from evaluation split
                test_x, test_y = eval_split.arr_x, eval_split.arr_y
                test_uid = eval_split.uid
            
            # -------------------------
            # 2. EVALUATE MODEL

            self.logger.info("Evaluating model performance")
            
            start_eval_time = time.time()
            test_metrics = self._compute_model_metrics(model, test_x, test_y, training_data)
            y_pred = self._predict(model, test_x)
            prediction_summary = self._compute_model_prediction_summary(test_y, y_pred, training_data)
            feature_importance = self._compute_model_feature_importance(model, test_x,test_y, training_data)
            
            job.results = VOResult_ModelTest(
                test_metrics=test_metrics,
                feature_importance=feature_importance,
                x_test_uid=test_uid,
                prediction_summary=prediction_summary,
                evaluation_time_ms=(time.time() - start_eval_time) * 1000
            )
            
            # Update job status
            job.status = "COMPLETE"
            job.runtime_seconds = int(time.time() - start_time)

            return model, job
            
        except Exception as e:
            # Update job with error information
            job.status = "FAILED"
            job.error_message = str(e)
            job.runtime_seconds = int(time.time() - start_time) if 'start_time' in locals() else 0 # type: ignore
            self.logger.exception(f"Training failed: {str(e)}")
            raise
    
    @abstractmethod
    def _initialize_model(
            self,
            model_configuration: VOConfig_Model,
            training_dataset: VODataset
    ) -> ModelT:
        """
        Returns an initialized model for the specific algorithm.
        """
        pass

    def _run_standard_training(
        self,
        training_data: VODataset,
        training_config: VOConfig_Training,
        model_config: VOConfig_Model,
        job: ENTTrainingJob,
    ) -> ModelT:
        """Execute training based on the specified validation strategy"""
        # Choose appropriate training method based on strategy
        if training_config.validation_strategy == "simple_split":
            train_set, val_set = split_data_simple(training_data, training_config.validation_split, random_seed=self.random_seed)
            model = self._train_with_validation(train_set, val_set, training_config, model_config, job)
            return model
            
        elif training_config.validation_strategy == "cross_validation":
            # Use integrated sklearn CV without explicit split functions
            model = self._train_with_cv(training_data, training_config, model_config, job)
            return model
            
        elif training_config.validation_strategy == "ts_cross_validation":
            # Use time series CV
            if training_data.pattern != "sequence":
                raise ValueError("Time series CV requires sequence data pattern")
                
            model = self._train_with_ts_cv(training_data, training_config, model_config, job)
            return model
            
        else:
            raise ValueError(f"Unknown validation strategy: {training_config.validation_strategy}")

    @abstractmethod
    def _train_with_validation(
            self,
            train_set: VODataset,
            val_set: VODataset,
            training_config: VOConfig_Training,
            model_config: VOConfig_Model,
            job: ENTTrainingJob
    ) -> ModelT:
        raise NotImplementedError()
        
    @abstractmethod
    def _train_with_cv(
            self,
            train_set: VODataset,
            training_config: VOConfig_Training,
            model_config: VOConfig_Model,
            job: ENTTrainingJob
    ) -> ModelT:
        raise NotImplementedError()
            
    @abstractmethod
    def _train_with_ts_cv(
            self,
            train_set: VODataset,
            training_config: VOConfig_Training,
            model_config: VOConfig_Model,
            job: ENTTrainingJob
    ) -> ModelT:
        raise NotImplementedError()
    
    # ---------------------------------------
    # HPO

    @abstractmethod
    def _hpo_training(
        self,
        model: ModelT,
        training_data: VODataset,
        validation_data: VODataset,
        model_config: VOConfig_Model,
        training_config: VOConfig_Training,
        trial:optuna.Trial,
    ) -> Tuple[ModelT, VOLog_ModelMetrics]:
        """Train model with specific parameters, with optional pruning support."""
        pass

    def _check_pruning(
        self, 
        trial:optuna.Trial,
        epoch: int, 
        metric_value: float
    ) -> bool:
        """
        Check if the current trial should be pruned based on intermediate metrics. To be used in _hpo_training
        Returns:
            True if training should continue, False if pruned
        """
        # Report metric to Optuna for pruning decisions
        trial.report(metric_value, epoch)
        
        # Check if trial should be pruned
        if trial.should_prune():
            self.logger.info(f"Trial pruned at epoch {epoch} with value: {metric_value:.6f}")
            raise optuna.exceptions.TrialPruned()
            
        return True

    def _create_objective_function( 
        self,
        training_data: VODataset,
        validation_data: VODataset,
        training_config: VOConfig_Training,
        model_config: VOConfig_Model
    ) -> Callable[[optuna.Trial], float]:
        """
        Create an algorithm-specific objective function for HPO.
        Built on optuna
            
        Returns:
            Callable objective function that takes an Optuna trial and returns metric value
        """
        # Get algorithm-specific parameter info
        def _extract_tunable_params_from_model_config(
            model_config: VOConfig_Model
        ) -> List[VOConfig_Parameter]:
            """
            Extract parameters marked as tunable from model configuration.
            """
            return [param for param in model_config.parameters if param.tunable and param.bounds]

        def _suggest_parameters(
            trial: optuna.Trial,
            tunable_params: List[VOConfig_Parameter],
        ) -> Dict[Hyperparams.BaseHPEnum, Any]:
            """
            Suggest parameters for an Optuna trial based on parameter specifications.
            """
            params = {}
            
            for param_spec in tunable_params:
                p_enum = param_spec.name
                p_str = self.enum2str_param(p_enum)
                bounds = param_spec.bounds
                
                if not bounds:
                    self.logger.warning(f"Parameter {p_str} has no bounds defined, skipping")
                    continue
                
                # Suggest parameter based on its bounds type
                if bounds.type == "integer":
                    params[p_enum] = trial.suggest_int(
                        p_str,
                        int(bounds.min_value),
                        int(bounds.max_value)
                    )
                elif bounds.type == "continuous":
                    params[p_enum] = trial.suggest_float(
                        p_str,
                        bounds.min_value,
                        bounds.max_value
                    )
                elif bounds.type == "continuous_logscale":
                    params[p_enum] = trial.suggest_float(
                        p_str,
                        bounds.min_value,
                        bounds.max_value,
                        log=True
                    )
                elif bounds.type == "categorical":
                    params[p_enum] = trial.suggest_categorical(
                        p_str,
                        bounds.choices
                    )
                elif bounds.type == "boolean":
                    params[p_enum] = trial.suggest_categorical(
                        p_str,
                        [True, False]
                    )
                    
            return params

        def _convert_params_to_model_config(
            base_config: VOConfig_Model,
            params_to_update: Dict[Hyperparams.BaseHPEnum, Any]
        ) -> VOConfig_Model:
            """
            Convert a parameter dictionary to a VOConfig_Model object while preserving
            existing parameters not modified by the optimization process.
            """
            # Create copies of all original parameters
            updated_params = [param.model_copy() for param in base_config.parameters]
            
            # Create map for faster lookups
            param_map = {p.name: i for i, p in enumerate(updated_params)}
            
            # Update existing parameters or add new ones
            for param_enum, value in params_to_update.items():
                if param_enum in param_map:
                    # Update existing parameter
                    idx = param_map[param_enum]
                    updated_params[idx] = updated_params[idx].model_copy(update={"value": value})
                else:
                    # Add new parameter
                    updated_params.append(VOConfig_Parameter(
                        name=param_enum,
                        value=value,
                        tunable=True
                    ))
            
            # Create new config with updated parameters but same algorithm
            return base_config.model_copy(update={"parameters": updated_params})

        
        tunable_params = _extract_tunable_params_from_model_config(model_config)

        def objective(trial: optuna.Trial) -> float:
            """Objective function for HPO"""
            # Suggest parameters based on parameter specifications

            params = _suggest_parameters(trial, tunable_params)
            model_config_updated= _convert_params_to_model_config(model_config, params)
            
            # Train model with suggested parameters
            try:
                model = self._initialize_model(model_config_updated, training_data)
                model, metrics = self._hpo_training(
                        model=model,
                        training_data=training_data,
                        validation_data=validation_data,
                        model_config=model_config_updated,
                        training_config = training_config,
                        trial = trial
                )
                
                # Get validation metric value
                metric_name = training_config.primary_metric
                metric_value = getattr(metrics, metric_name.value)
                    
                return metric_value

            except optuna.exceptions.TrialPruned:
                raise

            except Exception as e:
                self.logger.error(f"Trial failed with error: {str(e)}")
                self.logger.debug(f"Parameters: {params}", exc_info=True)
                raise  # Let the training loop handle actual errors
        
        return objective

    def run_hpo(
        self,
        training_data: VODataset,
        training_config: VOConfig_Training,
        model_config: VOConfig_Model,
        hpo_config: VOConfig_HPO
    ) -> VOResult_HPO:
        """Execute hyperparameter optimization using Optuna."""

        # --------------------
        # CONFIGURE
    
        # Determine optimization direction
        direction = (
            hpo_config.direction
            if hpo_config.direction is not None
            else "maximize" if EnumMetricName.is_higher_better(training_config.primary_metric) else "minimize"
        )
    
        # Configure sampler and pruner
        sampler = optuna.samplers.RandomSampler(seed=self.random_seed) if hpo_config.sampler == "random" else optuna.samplers.TPESampler(seed=self.random_seed)
        pruner = (
            optuna.pruners.HyperbandPruner() if hpo_config.pruner == "hyperband"
            else optuna.pruners.NopPruner() if hpo_config.pruner == "none"
            else optuna.pruners.MedianPruner()
        )
    
        # Split data for HPO
        training_set, validation_set = split_data_simple(training_data, 0.2, self.random_seed)
    
        # Create study
        study = optuna.create_study(sampler=sampler, pruner=pruner, direction=direction)
    
        # Create objective function
        objective = self._create_objective_function(
            training_data=training_set,
            validation_data=validation_set,
            training_config=training_config,
            model_config=model_config
        )
    
        # Define progress logging callback
        def log_progress(study: optuna.study.Study, trial: optuna.trial.FrozenTrial):
            completed_trials = len([t for t in study.trials if t.state in {optuna.trial.TrialState.COMPLETE, optuna.trial.TrialState.PRUNED}])
            successful_trials = len([t for t in study.trials if t.state == optuna.trial.TrialState.COMPLETE])
            pruned_trials = len([t for t in study.trials if t.state == optuna.trial.TrialState.PRUNED])
            failed_trials = len([t for t in study.trials if t.state == optuna.trial.TrialState.FAIL])
    
            # Calculate success rate
            success_rate = (successful_trials / completed_trials * 100) if completed_trials > 0 else 0
    
            # Calculate ETA
            durations = [t.duration.total_seconds() for t in study.trials if t.duration]
            avg_duration = sum(durations) / len(durations) if durations else 0
            remaining_trials = hpo_config.n_trials - completed_trials
            eta_seconds = avg_duration * remaining_trials
            eta_str = time.strftime("%H:%M:%S", time.gmtime(eta_seconds)) if avg_duration > 0 else "calculating..."
    
            # Log progress
            self.logger.info(
                f"Trial {trial.number+1}/{hpo_config.n_trials}: "
                f"Value={trial.value:.6f}, Best={study.best_value:.6f}, "
                f"Success rate: {success_rate:.1f}%, ETA: {eta_str}, "
                f"Pruned: {pruned_trials}, Failed: {failed_trials}"
            )
    
        # --------------------
        # EXECUTE OPTIMIZATION

        # Run optimization
        start_time = time.time()
        study.optimize(
            objective,
            n_trials=hpo_config.n_trials,
            timeout=hpo_config.timeout_seconds,
            n_jobs=hpo_config.n_parallel_jobs,
            callbacks=[log_progress]
        )
        runtime_seconds = time.time() - start_time

        # --------------------
        # METRICS
    
        # Collect results
        best_params = self.str2enum_paramdict(study.best_params)
        best_value = study.best_value
        self.logger.info(f"HPO complete. Best value: {best_value:.6f}")
        self.logger.info(f"Best parameters: {best_params}")
    
        # Calculate parameter importance
        try:
            importance = optuna.importance.get_param_importances(study)
            param_importance = {k: float(v) for k, v in importance.items()}
        except Exception as e:
            self.logger.warning(f"Could not calculate parameter importance: {str(e)}")
            param_importance = {}
    
        # Create HPO results
        
        hpo_results = VOResult_HPO(
            best_params=best_params,
            best_value=best_value,
            best_trial_id=study.best_trial.number,
            trials=[
                VOLog_HPOTrial(
                    trial_id=trial.number,
                    value=trial.value if trial.value is not None else 0.0,
                    params=self.str2enum_paramdict(trial.params) if trial.params else {},
                    duration_seconds=trial.duration.total_seconds() if trial.duration else 0,
                    best_value=study.best_value,
                    # Map Optuna trial states to our state literals
                    state="PRUNED" if trial.state == optuna.trial.TrialState.PRUNED else
                          "FAIL" if trial.state == optuna.trial.TrialState.FAIL else 
                          "COMPLETE"
                )
                for trial in study.trials
            ],
            parameter_importance=self.str2enum_paramdict(param_importance),
            runtime_seconds=runtime_seconds,
            direction=direction,
        )
    
        return hpo_results

    # --------------------------------
    # SHARED METHODS

    @abstractmethod
    def _predict(
        self,
        model: ModelT,
        x: npt.NDArray,
    ) -> npt.NDArray:
        """
        Make predictions using the trained model.
        Accepts batched input of arbitrary size for efficient processing.
        
        Args:
            model: The trained model instance
            x: Feature array of shape (n_samples, n_features) or 
               (n_samples, n_timesteps, n_features) for time series data
                
        Returns:
            Predictions array of shape (n_samples, n_outputs), where each row
            corresponds to the prediction for the respective input sample
                
        Raises:
            RuntimeError: If model has not been initialized
        """
        pass

    def _compute_model_prediction_summary(
        self,
        arr_y_true: np.ndarray,
        arr_y_pred: np.ndarray,
        training_data: VODataset
    ) -> Dict[str, Any]:
        """Calculate statistical summary of predictions."""
        
        # Reshape if needed
        arr_y_true = UtilsForArrays.reshape_ts_array(arr_y_true)
        arr_y_pred = UtilsForArrays.reshape_ts_array(arr_y_pred)
        
        summary = {
            "predictions_mean": float(np.mean(arr_y_pred)),
            "predictions_std": float(np.std(arr_y_pred)),
            "predictions_min": float(np.min(arr_y_pred)),
            "predictions_max": float(np.max(arr_y_pred)),
            "residuals_mean": float(np.mean(arr_y_true - arr_y_pred)),
            "residuals_std": float(np.std(arr_y_true - arr_y_pred)),
        }
        
        # Add quantiles
        quantiles = [0.1, 0.25, 0.5, 0.75, 0.9]
        for q in quantiles:
            summary[f"predictions_q{int(q*100)}"] = float(np.quantile(arr_y_pred, q))
            summary[f"residuals_q{int(q*100)}"] = float(np.quantile(arr_y_true - arr_y_pred, q))
        
        return summary
    
    def _compute_model_feature_importance(
        self,
        model: ModelT,
        arr_x: np.ndarray,
        arr_y: np.ndarray,
        training_data: VODataset
    ) -> Dict[str, float]:
        """
        Calculate feature importance using permutation importance.
        Default implementation uses model-agnostic approach.
        """
        
        # Create a scoring function to be used by permutation_importance
        def scorer(estimator, X, y):
            preds = self._predict(estimator, X)
            return -mean_squared_error(y, preds)  # Negative MSE as higher is better
        
        try:
            # Calculate permutation importance
            result = permutation_importance(
                model, arr_x, arr_y, 
                n_repeats=5,  # NOTE - for v2, to make this specific to each concrete implementation
                random_state=self.random_seed, 
                scoring=scorer
            )
            
            # Map to feature names
            feature_names = training_data.colnames_x
            importances = {name: float(imp) for name, imp in zip(feature_names, result.importances_mean)} # type: ignore
            
            # Normalize to sum to 1.0
            total = sum(importances.values())
            if total > 0:
                importances = {k: v/total for k, v in importances.items()}
                
            return importances
            
        except Exception as e:
            raise ValueError(f"Could not compute model feature importance: {e}")

    def _compute_model_metrics(
            self,
            model: ModelT,
            x: npt.NDArray,
            y_true: npt.NDArray,
            training_set: VODataset
     ) -> VOLog_ModelMetrics:
        """
        Compute model metrics directly on provided arrays.
        Prioritizes consistent calculation over complex reshaping
        """
        
        y_pred = self._predict(model, x)
        
        return VOLog_ModelMetrics.create_from_predictions(y_true, y_pred, training_set.colnames_y)
        
        
    # -------------------------
    # LOGGING

    def _collect_environment_info(self) -> Dict[str, str]:
        """Collect information about the execution environment"""
        import platform
        import socket
        import sys
        
        return {
            "hostname": socket.gethostname(),
            "platform": platform.platform(),
            "python": platform.python_version(),
            "numpy": np.__version__,
            "pandas": pd.__version__,
            "optuna": optuna.__version__,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
        }

    # -------------------------
    # STRATEGY MAPS

    @abstractmethod
    def _get_mlframework_param_map(self) -> Dict[Hyperparams.BaseHPEnum, str]:
        """
        Returns mapping from hyperparameter enums to ML framework parameter names.
        This base method raises NotImplementedError and should be overridden by subclasses.
        
        Returns:
            Dictionary mapping hyperparameter enums to framework-specific parameter strings
        
        Raises:
            NotImplementedError: When called on the base class without override
        """
        raise NotImplementedError("Subclasses must implement get_mlframework_param_map")
    
    def str2enum_param(self, _str: str) -> Hyperparams.BaseHPEnum:
        rev = {
            v: k for k, v in self._get_mlframework_param_map().items()
        }
        return rev[_str]
    
    def enum2str_param(self, val: Hyperparams.BaseHPEnum) -> str:
        return self._get_mlframework_param_map()[val]

    def enum2str_paramdict(self, params: Dict[Union[Hyperparams.BaseHPEnum, str], Any]) -> Dict[str, Any]:
        """
        Convert dictionary with enum keys to dictionary with string keys based on framework mapping.
        
        Args:
            params: Dictionary with enum keys
            
        Returns:
            Dictionary with string keys for the ML framework
            
        Raises:
            KeyError: When a parameter enum doesn't have a mapping in the framework
        """
        framework_map = self._get_mlframework_param_map()
        result = {}
        
        for param, value in params.items():
            if isinstance(param, str):
                # Already a string key
                result[param] = value
                continue
                
            if param not in framework_map:
                raise KeyError(f"Parameter {param} not supported by this framework")
            param_name = framework_map[param]
            result[param_name] = value
        
        return result

    def str2enum_paramdict(self, params: Dict[str, Any]) -> Dict[Hyperparams.BaseHPEnum, Any]:
        """
        Convert dictionary with string keys to dictionary with enum keys based on framework mapping.
        
        Args:
            params: Dictionary with string keys
            
        Returns:
            Dictionary with enum keys
            
        Raises:
            KeyError: When a parameter string doesn't map to a known enum
        """
        framework_map = self._get_mlframework_param_map()
        # Create reverse mapping (string -> enum)
        reverse_map = {v: k for k, v in framework_map.items()}
        result = {}
        
        for param_name, value in params.items():
            if param_name not in reverse_map:
                raise KeyError(f"Parameter name {param_name} does not map to a known enum")
            param_enum = reverse_map[param_name]
            result[param_enum] = value
        
        return result

