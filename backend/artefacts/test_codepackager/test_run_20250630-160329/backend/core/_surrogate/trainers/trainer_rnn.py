# SYSTEM IMPORTS
from __future__ import annotations
from typing import Dict, List, Optional, Tuple, Any, Union, cast, Callable
import time
import os
from abc import ABC, abstractmethod

# LOGICAL IMPORTS
import sklearn
import sklearn.preprocessing
import torch
import torch.utils.data
import torch.utils.data as tdata
import optuna
import numpy as np
import numpy.typing as npt
import pandas as pd

# INTERNAL IMPORTS
from ._torchblocks import *
from ..entities import ENTTrainingJob
from ..trainers.trainer_base import SurrogateTrainerPort
from .._enums import EnumTrainingStatus, EnumSurrogateAlgorithm, EnumMetricName, Hyperparams
from .._utilities import *
from ..valueobjects import (
    VOMetadata_General, 
    VODataset, 
    VOConfig_Training, 
    VOConfig_Model,
    VOConfig_HPO,
    VOConfig_Parameter,
    VOConfig_ParamBounds,
    VOResult_ModelTest,
    VOLog_ModelMetrics,
    VOConfig_Parameter,
    VOLog_EpochMetrics
)

class Seq2SeqTSTrainer(SurrogateTrainerPort[torch.nn.Module]):


    # ---------------------------------------
    # ADMIN

    def _get_mlframework_param_map(self) -> Dict[Hyperparams.BaseHPEnum, str]:
        """
        Returns mapping from neural network hyperparameter enums to PyTorch parameter names.
        
        Returns:
            Dictionary mapping hyperparameter enums to PyTorch parameter names
        """
        return {
            # Model architecture parameters
            Hyperparams.NN.INPUT_SIZE: "input_size",
            Hyperparams.NN.OUTPUT_SIZE: "output_size",
            Hyperparams.NN.HIDDEN_SIZE: "hidden_size",
            Hyperparams.NN.DENSE_NUM_LAYERS: "num_layers",
            Hyperparams.NN.SEQ_NUM_LAYERS: "num_layers",
            Hyperparams.NN.RNN_TYPE: "rnn_type",  # Custom handling in model initialization
            Hyperparams.NN.BIDIRECTIONAL: "bidirectional",
            Hyperparams.NN.DROPOUT: "dropout",
            
            # Batch sizing - handled separately in DataLoader
            Hyperparams.NN.BATCH_SIZE: "batch_size",
            
            # Optimization parameters - handled in optimizer creation
            Hyperparams.NN.LEARNING_RATE: "lr",  # PyTorch uses 'lr' not 'learning_rate'
            Hyperparams.NN.WEIGHT_DECAY: "weight_decay",
            Hyperparams.NN.OPTIMIZER_TYPE: "optimizer_type",  # Custom handling
            Hyperparams.NN.OPTIMIZER_BETA1: "betas[0]",  # First beta for Adam
            Hyperparams.NN.OPTIMIZER_BETA2: "betas[1]",  # Second beta for Adam
            Hyperparams.NN.OPTIMIZER_EPS: "eps",  # Epsilon for numerical stability
            
            # Loss function - handled separately
            Hyperparams.NN.LOSS_FUNCTION: "loss_function",  # Custom handling
            
            # Learning rate scheduler parameters
            Hyperparams.NN.LR_SCHEDULER_TYPE: "lr_scheduler_type",  # Custom handling
            Hyperparams.NN.LR_SCHEDULER_STEP_SIZE: "step_size",  # For StepLR
            Hyperparams.NN.LR_SCHEDULER_GAMMA: "gamma",  # For StepLR
            Hyperparams.NN.LR_SCHEDULER_PATIENCE: "patience",  # For ReduceLROnPlateau
            Hyperparams.NN.LR_SCHEDULER_MIN_LR: "min_lr",  # For ReduceLROnPlateau
            
            # Training process control
            Hyperparams.NN.EPOCHS: "epochs",  # Handled in training loop
            Hyperparams.NN.EARLY_STOPPING: "early_stopping",  # Custom handling
            Hyperparams.NN.PATIENCE: "patience",  # For early stopping
            Hyperparams.NN.MIN_DELTA: "min_delta",  # For early stopping
            Hyperparams.NN.GRADIENT_CLIP_VAL: "clip_value",  # For gradient clipping
        }

    # ---------------------------------------
    # TRAINING HOOKS
    
    def _setup_data_loaders(
        self,
        train_data: VODataset,
        val_data: VODataset,
        batch_size: int,
        device: torch.device
    ) -> Tuple[torch.utils.data.DataLoader, torch.utils.data.DataLoader]:
        """
        Set up data loaders for training and validation.
        
        Args:
            train_data: Training dataset
            val_data: Validation dataset
            batch_size: Batch size
            device: Training device
            
        Returns:
            Tuple of (train_loader, val_loader)
        """
        num_workers = 0 if device.type == "cuda" else min(4, os.cpu_count() or 1)
        
        # Convert to PyTorch tensors
        X_train_tensor = torch.from_numpy(train_data.arr_x).float().to(device)
        y_train_tensor = torch.from_numpy(train_data.arr_y).float().to(device)
        X_val_tensor = torch.from_numpy(val_data.arr_x).float().to(device)
        y_val_tensor = torch.from_numpy(val_data.arr_y).float().to(device)
        
        # Create datasets and loaders
        train_dataset = tdata.TensorDataset(X_train_tensor, y_train_tensor)
        val_dataset = tdata.TensorDataset(X_val_tensor, y_val_tensor)
        
        train_loader = tdata.DataLoader(
            train_dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=num_workers,
            pin_memory=(device.type == "cuda")
        )
        
        val_loader = tdata.DataLoader(
            val_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=(device.type == "cuda")
        )
        
        return train_loader, val_loader
    
    def _setup_optimizer(
        self,
        model: torch.nn.Module,
        model_config: VOConfig_Model,
        learning_rate: float
    ) -> torch.optim.Optimizer: # type: ignore
        """
        Set up optimizer based on model configuration.
        
        Args:
            model: The model
            model_config: Model configuration
            learning_rate: Learning rate
            
        Returns:
            Configured optimizer
        """
        optimizer_type = model_config.get_param_value(Hyperparams.NN.OPTIMIZER_TYPE, "adam").lower()
        weight_decay = model_config.get_param_value(Hyperparams.NN.WEIGHT_DECAY, 0.0)
        
        if optimizer_type == "adam":
            beta1 = model_config.get_param_value(Hyperparams.NN.OPTIMIZER_BETA1, 0.9)
            beta2 = model_config.get_param_value(Hyperparams.NN.OPTIMIZER_BETA2, 0.999)
            eps = model_config.get_param_value(Hyperparams.NN.OPTIMIZER_EPS, 1e-8)
            return torch.optim.Adam( # type: ignore
                model.parameters(),
                lr=learning_rate,
                betas=(beta1, beta2),
                eps=eps,
                weight_decay=weight_decay
            )
        elif optimizer_type == "sgd":
            momentum = model_config.get_param_value(Hyperparams.NN.OPTIMIZER_BETA1, 0.9)
            return torch.optim.SGD( # type: ignore
                model.parameters(),
                lr=learning_rate,
                momentum=momentum,
                weight_decay=weight_decay
            )
        else:
            return torch.optim.Adam( # type: ignore
                model.parameters(),
                lr=learning_rate,
                weight_decay=weight_decay
            )
    
    def _setup_loss_function(
        self,
        model_config: VOConfig_Model
    ) -> torch.nn.Module:
        """
        Set up loss function based on model configuration.
        """
        loss_func_name = model_config.get_param_value(Hyperparams.NN.LOSS_FUNCTION, "mse").lower()
        
        _lossfunc_lookup: Dict[str, Type[torch.nn.modules.loss._Loss]] = {
            "mse": torch.nn.MSELoss,
            "mae": torch.nn.L1Loss
        }
        
        return _lossfunc_lookup.get(loss_func_name, torch.nn.MSELoss)()
    
    def _setup_lr_scheduler(
        self,
        optimizer: torch.optim.Optimizer, # type: ignore
        model_config: VOConfig_Model
    ) -> Optional[Union[torch.optim.lr_scheduler._LRScheduler, torch.optim.lr_scheduler.ReduceLROnPlateau]]:
        """
        Set up learning rate scheduler based on model configuration.
        """
        scheduler_type = model_config.get_param_value(Hyperparams.NN.LR_SCHEDULER_TYPE, None)
        
        if scheduler_type == "step":
            step_size = model_config.get_param_value(Hyperparams.NN.LR_SCHEDULER_STEP_SIZE, 10)
            gamma = model_config.get_param_value(Hyperparams.NN.LR_SCHEDULER_GAMMA, 0.1)
            return torch.optim.lr_scheduler.StepLR(optimizer, step_size=step_size, gamma=gamma) #type: ignore

        elif scheduler_type == "plateau":
            patience = model_config.get_param_value(Hyperparams.NN.LR_SCHEDULER_PATIENCE, 5)
            factor = model_config.get_param_value(Hyperparams.NN.LR_SCHEDULER_GAMMA, 0.1)
            return torch.optim.lr_scheduler.ReduceLROnPlateau(
                optimizer=optimizer,
                mode="min",
                factor=factor,
                patience=patience
            )
        
        return None
    
    def _train_epoch(
        self,
        model: torch.nn.Module,
        train_loader: torch.utils.data.DataLoader,
        optimizer: torch.optim.Optimizer, # type: ignore
        criterion: torch.nn.Module,
        device: torch.device,
        model_config: VOConfig_Model,
        job: Optional[ENTTrainingJob] = None,
        epoch: Optional[int] = None,
        max_epochs: Optional[int] = None
    ) -> float:
        """
        Execute one training epoch.

        Returns:
            Average training loss
        """
        model.train()
        train_loss = 0.0
        
        for batch_idx, (batch_X, batch_y) in enumerate(train_loader):
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            # Forward pass
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            
            # Backward pass and optimization
            optimizer.zero_grad()
            loss.backward()
            
            # Apply gradient clipping if specified
            grad_clip_val = model_config.get_param_value(Hyperparams.NN.GRADIENT_CLIP_VAL, None)
            if grad_clip_val is not None:
                torch.nn.utils.clip_grad_norm_(model.parameters(), grad_clip_val)
            
            # Optimization step
            optimizer.step()
            
            # Accumulate batch loss
            train_loss += loss.item() * batch_X.size(0)
            
            # Log batch metrics
            if job and epoch is not None and max_epochs is not None and batch_idx % 100 == 0:
                job.update_status(
                    f"Epoch {epoch+1}/{max_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}"
                )
            print(f"Epoch {epoch+1}/{max_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")
        
        # Calculate average training loss
        train_loss /= len(train_loader.dataset) # type: ignore
        return train_loss
    
    def _validate_epoch(
        self,
        model: torch.nn.Module,
        val_loader: torch.utils.data.DataLoader,
        criterion: torch.nn.Module,
        device: torch.device
    ) -> float:
        """
        Execute one validation epoch.

        Returns:
            Average validatoin loss
        """
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                
                # Forward pass
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                
                # Accumulate batch loss
                val_loss += loss.item() * batch_X.size(0)
        
        # Calculate average validation loss
        val_loss /= len(val_loader.dataset) # type: ignore
        return val_loss
    
    def _update_metrics(
        self,
        train_loss: float,
        val_loss: float,
        epoch: int,
        job: ENTTrainingJob,
        train_loader: torch.utils.data.DataLoader,
        val_loader: torch.utils.data.DataLoader,
        training_config: VOConfig_Training
    ) -> None:
        """
        Update metrics for the current epoch.
        """
        pri_metric = training_config.primary_metric
        
        epoch_metrics = VOLog_EpochMetrics(
            epoch = epoch + 1,
            
            metric_name= pri_metric,
            train_metric=train_loss,
            validation_metric=val_loss,

            train_samples=len(train_loader.dataset), # type: ignore
            validation_samples= len(val_loader.dataset) # type: ignore
        )
        job.log_epoch_metric(
            epoch_metrics
        )
        
    
    def _check_early_stopping(
        self,
        val_loss: float,
        best_val_loss: float,
        patience_counter: int,
        early_stopping_enabled: bool,
        early_stopping_patience: int,
        early_stopping_min_delta: float,
        epoch: int,
        job: ENTTrainingJob
    ) -> Tuple[float, int, bool]:
        """
        Check if early stopping should be triggered.

        Returns:
            Tuple of (best_val_loss, patience_counter, should_stop)
        """
        if not early_stopping_enabled:
            return best_val_loss, patience_counter, False
        
        # Check if there's improvement
        delta = val_loss - best_val_loss

        # Improvement found
        if delta < -early_stopping_min_delta:
            return val_loss, 0, False
        else:
            # No improvement
            patience_counter += 1
            if patience_counter >= early_stopping_patience:
                job.update_status(
                    EnumTrainingStatus.EARLY_STOPPED,
                    f"Early stopping triggered at epoch {epoch+1}"
                )
                job.early_stopping_triggered = True
                return best_val_loss, patience_counter, True
            return best_val_loss, patience_counter, False



    # ---------------------------------------
    # STANDARD TRAINING

    def _initialize_model(
            self,
            model_configuration: VOConfig_Model,
            training_dataset: VODataset
    ) -> torch.nn.Module:
        """
        Returns an initialized model for the specific algorithm.
        """
        
        # Extract target dimensions from transformer
        n_inputs = len(training_dataset.colnames_x)
        n_outputs = len(training_dataset.colnames_y)
        
        # Extract model hyperparameters with defaults
        n_hidden_units = model_configuration.get_param_value(Hyperparams.NN.HIDDEN_SIZE, 64)
        rnn_type = model_configuration.get_param_value(Hyperparams.NN.RNN_TYPE, "LSTM")
        rnn_layers = model_configuration.get_param_value(Hyperparams.NN.SEQ_NUM_LAYERS, 1)
        dense_layers = model_configuration.get_param_value(Hyperparams.NN.DENSE_NUM_LAYERS, 2)
        dropout_probability = model_configuration.get_param_value(Hyperparams.NN.DROPOUT, 0.1)
        
        # Create and return model
        # NOTE - to extend this to a strategy pattern to mix and match seq2seq models
        model = Model_RNN(
            n_inputs=n_inputs,
            n_outputs=n_outputs,
            n_hidden_units=n_hidden_units,
            rnn_type=rnn_type,
            rnn_layers=rnn_layers,
            dense_layers=dense_layers,
            dropout_probability=dropout_probability
        )
        
        return model

    def _predict(
        self,
        model: torch.nn.Module,
        x: npt.NDArray,
    ) -> npt.NDArray:
        """
        Generate predictions from the RNN model.
        
        Args:
            model: The trained RNN model
            x: Input features of shape (n_samples, n_timesteps, n_features)
            
        Returns:
            Predictions of shape (n_samples, n_outputs)
        """
        # Set model to evaluation mode
        model.eval()
        
        # Convert to torch tensor
        device = next(model.parameters()).device  # Get the device model is on
        x_tensor = torch.from_numpy(x).float().to(device)
        
        # Generate predictions
        with torch.no_grad():
            predictions = model(x_tensor)
            
        # Convert back to numpy and return
        return predictions.cpu().numpy()


        
    def _train_with_cv(
            self,
            train_set: VODataset,
            training_config: VOConfig_Training,
            model_config: VOConfig_Model,
            job: ENTTrainingJob
    ) -> torch.nn.Module:
        raise NotImplementedError("This does not exist for this trainer")
            
    def _train_with_ts_cv(
            self,
            train_set: VODataset,
            training_config: VOConfig_Training,
            model_config: VOConfig_Model,
            job: ENTTrainingJob
    ) -> torch.nn.Module:
        raise NotImplementedError("This does not exist for this trainer")

    def _train_with_validation(
            self,
            train_set: VODataset,
            val_set: VODataset,
            training_config: VOConfig_Training,
            model_config: VOConfig_Model,
            job: ENTTrainingJob
    ) -> torch.nn.Module:
        """
        NOTE this can be simplified to just use the JOB
        Execute standard training without HPO.
        
        Args:
            metadata: Model metadata
            training_data: Training data samples
            validation_data: Validation data samples
            training_config: Training configuration
            model_config: Model configuration
            job: Training job to update
            
        Returns:
            Trained native model
        """
        # --------------------------
        # INIT DEVICE 
        DEVICE = torch.device("cuda:0" if torch.cuda.is_available() else "cpu") # number is for multi-gpu devices.

        # --------------------------
        # INIT MODEL
        model = self._initialize_model(model_config,train_set)
        model = model.to(DEVICE)

        # --------------------------
        # SETUP TRAINING COMPONENTS
        BATCH_SIZE = model_config.get_param_value(Hyperparams.NN.BATCH_SIZE)
        LEARNING_RATE= model_config.get_param_value(Hyperparams.NN.LEARNING_RATE)
        EPOCHS = training_config.max_iterations
        
        train_loader, val_loader = self._setup_data_loaders(train_set, val_set,BATCH_SIZE, DEVICE)
        optimizer = self._setup_optimizer(model, model_config, LEARNING_RATE)
        criterion = self._setup_loss_function(model_config)
        scheduler = self._setup_lr_scheduler(optimizer,model_config)
        

        # --------------------------
        # EARLY STOPPING

        early_stopping_enabled = model_config.get_param_value(Hyperparams.NN.EARLY_STOPPING, False)
        early_stopping_patience= model_config.get_param_value(Hyperparams.NN.PATIENCE,10)
        early_stopping_min_delta= model_config.get_param_value(Hyperparams.NN.MIN_DELTA,0.001)
        best_val_loss = float("inf")
        patience_counter = 0
        
        # --------------------------
        # TRAIN

        job.update_status(f"Status: `{EnumTrainingStatus.TRAINING.value}`. Starting training for `{EPOCHS}` Epochs")
        time_start = time.time()
        
        for epoch in range(EPOCHS):
            time_epoch_start = time.time()

            # ---------------------------
            # FORWARD-BACKWARD 

            train_loss = self._train_epoch(
                model = model,
                train_loader = train_loader,
                optimizer = optimizer,
                criterion = criterion,
                device = DEVICE,
                model_config = model_config, 
                job = job,
                epoch = epoch,
                max_epochs = EPOCHS
            )
            
            val_loss = self._validate_epoch(
                    model = model,
                    val_loader=val_loader,
                    criterion=criterion,
                    device=DEVICE
            )

            # ---------------------------
            # PARAM ADJUSTMENTS
            
            # LR
            if scheduler is not None:
                if isinstance(scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    scheduler.step(val_loss)
                else:
                    scheduler.step()
                    
            # EVAL-EARLY STOPPING
            best_val_loss, patience_counter, should_stop = self._check_early_stopping(
                    val_loss=val_loss,
                    best_val_loss=best_val_loss,
                    patience_counter=patience_counter,
                    early_stopping_enabled= early_stopping_enabled,
                    early_stopping_patience= early_stopping_patience,
                    early_stopping_min_delta= early_stopping_min_delta,
                    epoch = epoch,
                    job = job
            )
            
            # ---------------------------
            # UPDATE METRICS

            self._update_metrics(
                    train_loss= train_loss,
                    val_loss = val_loss,
                    epoch = epoch,
                    job=job,
                    train_loader = train_loader,
                    val_loader = val_loader,
                    training_config = training_config
            )
            
            if should_stop:
                break    
        
        # ---------------------------
        # FINAL EVAL
        
        time_end  = time.time()
        total_duration= time_end - time_start
        final_val_loss = best_val_loss if early_stopping_enabled else val_loss # type: ignore

        if job.early_stopping_triggered:
            job.update_status(
                EnumTrainingStatus.EARLY_STOPPED,
                f"Early stopping triggered at epoch {len(job.epoch_metrics)}. Final validation loss: {final_val_loss:.4f}"
            )
        else:
            job.update_status(
                EnumTrainingStatus.COMPLETE,
                f"Training completed in {total_duration:.2f} seconds. Final validation loss: {final_val_loss:.4f}"
            )

        return model

    # ---------------------------------------
    # HPO

    def _hpo_training(
        self,
        model: torch.nn.Module,
        training_data: VODataset,
        validation_data: VODataset,
        model_config: VOConfig_Model,
        training_config: VOConfig_Training,
        trial: optuna.Trial,
    ) -> Tuple[torch.nn.Module, VOLog_ModelMetrics]:
        """
        Train model with specific parameters for HPO and evaluate it.
        
        Args:
            model: The model to train (already initialized with proper architecture)
            training_data: Training dataset
            validation_data: Validation dataset for pruning decisions
            model_config: Model configuration with trial-suggested parameters
            training_config: Training configuration with metric settings
            trial: Optuna trial for pruning support
            
        Returns:
            Tuple of (trained model, validation metrics)
        """
        # Setup device
        device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
        model = model.to(device)
        
        # Extract parameters from model_config
        hpo_epochs= min(10, model_config.get_param_value(Hyperparams.NN.EPOCHS, 10))
        batch_size = model_config.get_param_value(Hyperparams.NN.BATCH_SIZE, 16)
        learning_rate = model_config.get_param_value(Hyperparams.NN.LEARNING_RATE, 0.001)
        
        # Setup training components
        train_loader, val_loader = self._setup_data_loaders(
            training_data, 
            validation_data, 
            batch_size, 
            device
        )
        
        optimizer = self._setup_optimizer(model, model_config, learning_rate)
        criterion = self._setup_loss_function(model_config)
        scheduler = self._setup_lr_scheduler(optimizer, model_config)
        
        # Setup early stopping - simplified for PO
        best_val_loss = float("inf")
        
        # Training loop
        for epoch in range(hpo_epochs):
            # Training phase
            train_loss = self._train_epoch(
                model=model,
                train_loader=train_loader,
                optimizer=optimizer,
                criterion=criterion,
                device=device,
                model_config=model_config,
                job=None,
                epoch=epoch,
                max_epochs=hpo_epochs
            )
            
            # Validation phase
            val_loss = self._validate_epoch(
                model=model,
                val_loader=val_loader,
                criterion=criterion,
                device=device
            )
            
            # Update best validation loss and LR scheduler
            if val_loss < best_val_loss:
                best_val_loss = val_loss
            
            if scheduler is not None:
                if isinstance(scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                    scheduler.step(val_loss)
                else:
                    scheduler.step()
            
            # Calculate metrics for pruning
            metrics = self._compute_model_metrics(
                model, 
                x=validation_data.arr_x,
                y_true=validation_data.arr_y,
                training_set=training_data
            )
            
            # Report the primary metric for pruning
            metric_value = getattr(metrics, training_config.primary_metric.value)
            trial.report(metric_value, epoch)
            
            # Check if trial should be pruned
            if trial.should_prune():
                raise optuna.exceptions.TrialPruned()
        
        # Final evaluation using the same method as in the objective function
        final_metrics = self._compute_model_metrics(
            model, 
            x=validation_data.arr_x,
            y_true=validation_data.arr_y,
            training_set=training_data
        )
        
        return model, final_metrics
    
    # ----------------
    # METHOD OVERRIDES
    def _compute_model_feature_importance(
        self,
        model: torch.nn.Module,
        arr_x: np.ndarray, 
        arr_y: np.ndarray,
        training_data: VODataset
    ) -> Dict[str, float]:
        """Calculate feature importance using a RandomForest surrogate model.
        
        This approach trains a simple RandomForest model to mimic the predictions
        of the complex PyTorch model, then extracts feature importance values
        from this interpretable surrogate.
        
        Returns:
            Dictionary mapping feature names to importance values (sum to 1.0)
        """
        from sklearn.ensemble import RandomForestRegressor
        
        # Get predictions from the PyTorch model using existing method
        y_pred = self._predict(model, arr_x)
        
        # Reshape inputs and outputs to 2D for sklearn
        # For sequence data (3D arrays), reshape to (samples, features)
        n_samples = arr_x.shape[0] * arr_x.shape[1]  # timesets * timesteps
        n_features = arr_x.shape[2]
        n_targets = arr_y.shape[2]
        
        X_2d = arr_x.reshape(n_samples, n_features)
        y_pred_2d = y_pred.reshape(n_samples, n_targets)
        
        # Train surrogate model to mimic the complex model's predictions
        surrogate = RandomForestRegressor(n_estimators=100, random_state=42)
        surrogate.fit(X_2d, y_pred_2d)
        
        # Extract feature importances (already normalized to sum to 1.0)
        importances = surrogate.feature_importances_
        
        # If multi-target, average importance across all targets
        if importances.ndim > 1:
            importances = np.mean(importances, axis=1)
        
        # Map to feature names
        feature_names = training_data.colnames_x
        return {name: float(imp) for name, imp in zip(feature_names, importances)}
    


        

###################################################

# MODEL

class Model_RNN(torch.nn.Module):

    def __init__(
            self,
            n_inputs: int,
            n_outputs: int,
            n_hidden_units: int,
            rnn_type: str,
            rnn_layers: int,
            dense_layers: int,
            dropout_probability: float
    ):
        super().__init__()

        self.sequence_block = SequenceBlock(
            input_size = n_inputs,
            hidden_size = n_hidden_units,
            rnn_type = rnn_type, # type: ignore
            num_layers=rnn_layers
        )
        
        # TODO is this typically called dense block or FullyConnected Block?
        self.dense_block = ResidualDenseBlock(
            hidden_size=n_hidden_units,
            num_layers=dense_layers,
            dropout_prob=dropout_probability
        )
        
        self.output_layer = torch.nn.Linear(
            in_features=n_hidden_units,
            out_features=n_outputs
        )
        
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the model.
        
        Args:
            x: Input tensor of shape [batch_size, time_steps, features]
                
        Returns:
            Output tensor of shape [batch_size, time_steps, output_features]
        """
        # Input shape: [batch_size, time_steps, features]
        # No reshaping needed as DataTransformer provides correct shape
        
        # Process through sequence block (RNN)
        x = self.sequence_block(x)  # [batch_size, time_steps, hidden_size]
        
        # Store for residual connection
        x_seed = x
        
        # Process through dense block with residual connection
        x = self.dense_block(x, x_seed)  # [batch_size, time_steps, hidden_size]
        
        # Final output projection to target dimension
        return self.output_layer(x)  # [batch_size, time_steps, output_features]

###################################################

# Configurations 


class RNNConfigs:
    """
    Simple namespace
    """
    @staticmethod
    def get_base_training_config(max_iterations: int = 300) -> VOConfig_Training:
        """
        Create a standard training configuration.
        """
        return VOConfig_Training(
            primary_metric=EnumMetricName.RMSE,
            max_iterations=max_iterations,
            random_seed=42,
            validation_strategy="simple_split",
            validation_split=0.2,
            enable_early_stopping=True,
            early_stopping_rounds=5,
            early_stopping_min_delta=0.001
        )

    @staticmethod
    def get_base_hpo_config() -> VOConfig_HPO:
        return VOConfig_HPO(
            # Core Settings
            is_enable=True,
            n_trials = 20,
            sampler = "tpe",
            pruner = "median",

            # Resource Allocation
            timeout_seconds = 60*5,
            n_parallel_jobs= 1,
        )

    @staticmethod
    def get_base_model_config(overrides: Optional[Dict[str, Any]] = None) -> VOConfig_Model:
        """
        Create a standard model configuration with optional overrides.
        
        Args:
            overrides: Dictionary of parameter name to value overrides
            
        Returns:
            VOConfig_Model with reasonable defaults
        """
        params = [
            VOConfig_Parameter(
                name=Hyperparams.NN.HIDDEN_SIZE,
                value=32,
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="integer",
                    # TODO convert to categorical
                    min_value=16,
                    max_value=128
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.SEQ_NUM_LAYERS,
                value=1,
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="integer",
                    # TODO convert to categorical
                    min_value=1,
                    max_value=3
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.DENSE_NUM_LAYERS,
                value=1,
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="integer",
                    # TODO convert to categorical
                    min_value=1,
                    max_value=3
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.RNN_TYPE,
                value="LSTM",
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="categorical",
                    choices=["LSTM", "GRU"]
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.DROPOUT,
                value=0.1,
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="continuous",
                    min_value=0.0,
                    max_value=0.5
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.BATCH_SIZE,
                value=16,
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="categorical",
                    choices=[8, 16, 32, 64]
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.LEARNING_RATE,
                value=0.001,
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="continuous_logscale",
                    min_value=1e-5,
                    max_value=1e-2
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.LOSS_FUNCTION,
                value="mse",
                tunable=False,
                bounds=VOConfig_ParamBounds(
                    type="categorical",
                    choices=["mse", "mae"]
                )
            )
        ]
        
        # Override param values if specified
        if overrides:
            for param in params:
                if param.name.name in overrides:
                    # Create a new parameter with the overridden value
                    param = VOConfig_Parameter(
                        name=param.name,
                        value=overrides[param.name.name],
                        tunable=param.tunable,
                        bounds=param.bounds
                    )
        
        return VOConfig_Model(
            algorithm=EnumSurrogateAlgorithm.RNN_TS,
            parameters=params
        )