import warnings
warnings.filterwarnings("ignore")

import os
import pathlib
import sys

import numpy as np
import pandas as pd
import json
from math import floor
import torch
from torch import nn
import torch.nn.functional as F
from bayes_opt import BayesianOptimization
from torch.utils.data import DataLoader

try:
    from torch.utils.tensorboard import SummaryWriter
    print("tensorboard imported!")
    TENSORBOARD = True
except:
    print("Tensorboad not imported.")
    TENSORBOARD = False

import matplotlib.pyplot as plt
import seaborn as sns

cur_path = pathlib.Path(__file__).resolve().parent


sys.path.append(str(cur_path.parent.parent))
import model
sys.path.append(str(cur_path.parent.parent/"surrogateModel"))
from surrogateModel.dataset import dataset_build
from surrogateModel.surrogate_model_deeplearning.model_DNN import Surrogate_DeepLearning_Model
from surrogateModel.surrogate_model_deeplearning.metrics import Surrogate_Model_Metric
from surrogateModel.surrogate_model_deeplearning.deeplearning_dataset import base_dataset, pytorch_dataset
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"Device used: {device}")

class SurrogateModel_Learning:
    """
    This class contains methods that handle the creation and Hyperparameter optimization of Surrogate Models.

    Attributes
    ----------
        1) data: base dataset to be used for building dataloaders
        2) training_dl/ validation_dl/ testing_dl: dataloaders
        3) t_steps: number of time steps
        4) n_inputs/n_outputs: Input and Output size respectively
        5) training_report: report_of_training_progress

    Methods
    -------
        1) __init__: Constructor of the Class.
        2) data_loading: Method written to aid in the creation of datalaoaders.
        3) create_model: Method writtern to create the model based on a config dict.
        4) __training_reports(self, tboard_dicts): Method written to automate training reports.
        5) __training_block: Method written to represent the training portion of the pyTorch training loop.
        6) __validation_block: Method written to automate validation/testing of a pyTorch NN model.
        7) __tensorboard_logging: Method written to represent tensoroard logging and report creation.
        8) __save_best_model: Method written to save the best model.
        9) model_training_main: Method written for representing training loop of the main surrogate model. (Includes logging of training details and saving best models.)
        10) training_ray_tune: Training loop function written to use for applying ray-tune for hyper parameter optimization.
        11) ray_tune_h_opt: Hyper Parameter Optimization using Bayesian Optimization by leveraging Ray Tune Library.
    """
    def __init__(self,dataset_cl: dataset_build.dataset, y_transform=True, y_scaling="Standard"):        
        """
        Constructor of the Class.

        Parameters
        ----------
            dataset_cl: dataset class
                original surrogate model dataset
            y_transform: boolean
                transform y or not
            y_scaling: str
                Either Standard Scaling/MinMax

        
        """
        self.data = base_dataset(dataset_cl, y_transform, test_val_split=0.2, y_scaling=y_scaling)
        self.output_names = dataset_cl.model.output_name_list
        self.input_names = dataset_cl.model.input_name_list

        
    
    def data_loading(self, config):
        """
        Method written to aid in the creation of datalaoaders.

        Parameters
        ----------
            config: dict
                hyperparameter config of a model
        """
        
        self.training_dl = DataLoader(pytorch_dataset(self.data, kind="training"), batch_size=config["batch_size"])
        self.validation_dl = DataLoader(pytorch_dataset(self.data, kind="validation"), batch_size=config["batch_size"])
        self.testing_dl = DataLoader(pytorch_dataset(self.data, kind="testing"), batch_size=config["batch_size"])
        self.t_steps = self.data.n_time_steps
        self.n_inputs = next(iter(self.testing_dl))["inputs"].size(dim=1)
        self.n_outputs = next(iter(self.testing_dl))["outputs"].size(dim=2)


    def create_model(self, model_config):
        """
        Method writtern to create the model based on a config dict.

        Parameters
        ----------
            model_config: dict
                hyperparameter dict
        """
        return Surrogate_DeepLearning_Model(self.n_inputs, self.n_outputs, self.t_steps,
                                            model_config["rnn_type"], model_config["rnn_layers"], model_config["n_hidden_units"],
                                            model_config["DNN_length"], model_config["dropout_prob"])


    
    def training_reports(self, tboard_dicts, save_dir):
        """
        Method written to automate training reports.

        Parameters
        ----------
            t_board_dicts: dict
                data
        
        """
        if self.training_report is None:
            self.training_report = {}
            for i in tboard_dicts.keys():
                for j in tboard_dicts[i].keys():
                    self.training_report[j] = [tboard_dicts[i][j]]
        else:
            
            for i in tboard_dicts.keys():
                if i!="Epoch":
                    plt.figure()

                for j in tboard_dicts[i].keys():
                    self.training_report[j].append(tboard_dicts[i][j])
                    if i!="Epoch":
                        sns.lineplot(x=self.training_report["Epochs"], y=self.training_report[j], label=j)

                if i!="Epoch":
                    plt.yscale("log")
                    plt.ylabel(i)
                    plt.xlabel("Epoch")
                    plt.grid(visible=True)
                    plt.savefig(save_dir/f"{i}.png")


    def __training_block(self, nn_net, optimizer):
        """
        Method written to represent the training portion of the pyTorch training loop.

        Parameters
        ----------
            nn_net: pyTorch NN
            optimizer: pyTorch Optimizer

        Returns
        -------
            training_loss, training_metrics
        """
        # initial loss
        _loss = 0 
        # initial metrics
        traininging_metrics = Surrogate_Model_Metric()
        for metric in traininging_metrics.metrics.keys():
            traininging_metrics.metrics[metric] = np.zeros((1, self.n_outputs)) 
        
        nn_net.train()

        for batch_no, data in enumerate(self.training_dl):
            # Converting data from cpu to GPU if available to improve speed
            inputs, outputs = data["inputs"].to(device), data["outputs"].to(device)
            
            # Sets the gradients of all optimized tensors to zero
            optimizer.zero_grad()
            
            outputs_pred = nn_net(inputs)
            
            # Compute loss 
            loss = F.mse_loss(outputs_pred, outputs)
            _loss +=  loss.item()

            # back prop
            loss.backward()
            optimizer.step()

            # training metrics
            metrics_c = Surrogate_Model_Metric(outputs.detach().cpu().numpy(), outputs_pred.clone().detach().cpu().numpy())
            for metric in traininging_metrics.metrics.keys():
                traininging_metrics.metrics[metric] += metrics_c.metrics[metric]

            
        # Compute the average MSE and MAPE across the epoch
        _loss /=len(self.training_dl)
        for metric in traininging_metrics.metrics.keys():
            traininging_metrics.metrics[metric] /= len(self.training_dl)
        
        return _loss, traininging_metrics


    def __validation_block(self, nn_net, data_loader):
        """
        Method written to automate validation/testing of a pyTorch NN model.

        Parameters
        ----------
            nn_net: pyTorch NN
            data_loader: self.validation_dl/self.testing_dl
        
        Returns
        -------
            loss and metrics
        """
        nn_net.eval()
        with torch.inference_mode():
            _loss = 0 
            metrics_ = Surrogate_Model_Metric()
            for metric in metrics_.metrics.keys():
                metrics_.metrics[metric] = np.zeros((1, self.n_outputs)) 
            
            for batch_no, data in enumerate(data_loader):
                
                inputs, outputs = data["inputs"].to(device), data["outputs"].to(device)
                
                with torch.no_grad():
                    outputs_pred = nn_net(inputs)              
                    # Compute loss and other metrics
                    _loss += F.mse_loss(outputs_pred, outputs).item()
                    metrics_c = Surrogate_Model_Metric(outputs.detach().cpu().numpy(), outputs_pred.clone().detach().cpu().numpy())
                    for metric in metrics_.metrics.keys():
                        metrics_.metrics[metric] += metrics_c.metrics[metric]
            _loss /= len(data_loader)
            for metric in metrics_.metrics.keys():
                metrics_.metrics[metric] /= len(data_loader)
        
        return _loss, metrics_

    def __tensorboard_logging(self, writer, training_loss, training_metrics, val_loss, val_metrics, epoch, optimizer, exp_dir):
        """
        Method written to represent tensoroard logging and report creation.

        Parameters:
        -----------
            writer, training_loss, training_metrics, val_loss, val_metrics, epoch, optimizer
        
        """

         # logging loss function values
        tboard_dicts = {}
        tboard_dicts["Epoch"] = {"Epochs": epoch}
        tboard_dicts["Loss"] = {"Training Loss": training_loss, "Validation Loss": val_loss}
        if TENSORBOARD:
            writer.add_scalars("Loss", tboard_dicts["Loss"], epoch)

        for output_ind in range(len(self.output_names)):
            for metric in training_metrics.metrics.keys():
                tboard_dicts[f"{metric}_{self.output_names[output_ind]}"] = {
                    f"{metric}_{self.output_names[output_ind]}_Training": training_metrics.metrics[metric][0,output_ind] ,
                    f"{metric}_{self.output_names[output_ind]}_Validation": val_metrics.metrics[metric][0,output_ind]}
                if TENSORBOARD:
                    writer.add_scalars(f"{metric}_{self.output_names[output_ind]}", tboard_dicts[f"{metric}_{self.output_names[output_ind]}"] ,epoch)

        tboard_dicts["Learning Rate"] =  {"Learning Rate": optimizer.param_groups[0]['lr']}
        if TENSORBOARD:
            writer.add_scalars(f"Learning Rate",tboard_dicts["Learning Rate"] , epoch)

        # training reports creation
        self.training_reports(tboard_dicts, exp_dir)




    def __save_best_model(self, nn_model, best_loss, training_loss, _loss_val, exp_report_dir):
        """
        Method written to save the best model.

        Parameters
        ----------
            nn_model, best_loss, training_loss, _loss_val, exp_report_dir
        
        Returns
        -------
            best loss (current top validation loss)
        
        """
        if best_loss>=_loss_val:
            best_loss = _loss_val
            torch.save(obj=nn_model.state_dict(), f=exp_report_dir/"best_model.pth")
            print(f"Model saved! | Validation Error: {_loss_val} | Training Error: {training_loss}")
            pd.DataFrame(self.training_report).reset_index().rename(columns={"index": "Epoch"}).to_csv(exp_report_dir/"training_log_for_best_model.csv")
            # testing best model
            _loss_test, test_metrics = self.__validation_block(nn_model, self.testing_dl)
            # arrange res into dict
            testing_res = {}
            testing_res["Loss"] = _loss_test 
            for output_ind in range(len(self.output_names)):
                for metric in test_metrics.metrics.keys():
                    testing_res[f"{metric}_{self.output_names[output_ind]}"] = test_metrics.metrics[metric][0,output_ind] 
            # convert dict to json
            with open(exp_report_dir/"test_report.json", "w") as f:
                json.dump(testing_res, f)
        
        return best_loss


    def model_training(self, config, epochs, exp_report_dir, hyper_param_opt=False):
        """
        Method written for representing training loop of the main surrogate model. (Includes logging of training details and saving best models.)

        Parameters
        ----------
            config: model config dictionary
            epochs: max number of training epochs
            exp_report_dir: directory to store training results: tensorboard logging, training logs, testing results and best model
        """
        torch.manual_seed(53)
        # set-up data
        self.data_loading(config)
         # instantiate NN model
        nn_model = self.create_model(config)

        if hyper_param_opt==False:
            self.training_report = None
            # pyTorch Summary Writer
            os.makedirs(exp_report_dir)
            if TENSORBOARD:
                writer = SummaryWriter(log_dir=exp_report_dir/"tensorboard_logs")
            else: 
                writer=None
            with open(exp_report_dir/"model_config.json", "w") as f:
                json.dump(config, f)
            
        # optimizer
        optimizer = torch.optim.Adam(nn_model.parameters(),lr = config["starting_lr"])
        if hyper_param_opt==False:
            scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)   

        # very high loss for saving the best model
        best_loss = 10E10

        # move to gpu if available
        nn_model.to(device)
        # training loop
        for epoch in range(epochs):

            # trianing
            _loss, training_metrics = self.__training_block(nn_model, optimizer)

            # validation
            _loss_val, val_metrics = self.__validation_block(nn_model, self.validation_dl)

            if hyper_param_opt==False:
                #lr-scheduling
                scheduler.step(_loss_val)

                # tensorboard logging
                self.__tensorboard_logging(writer, _loss, training_metrics, _loss_val, val_metrics, epoch, optimizer, exp_report_dir)

                # saving best model
                self.__save_best_model(nn_model, best_loss, _loss, _loss_val, exp_report_dir)

                print(f"Epoch {epoch}| Training Error: {_loss} | Validation Error: {_loss_val}")

        if hyper_param_opt==False:    
            if TENSORBOARD:
                writer.close()   
            # save full training log
            pd.DataFrame(self.training_report).reset_index().rename(columns={"index": "Epoch"}).to_csv(exp_report_dir/"full_training_log.csv")
        else:
            return _loss_val

        
        
    def hyper_param_opt_bayesian_optimization(self, init_points, iters, epochs=50):
        """
        Training loop function written to use for applying ray-tune for hyper parameter optimization.

        Parameters
        ----------
            config: model config dictionary
            epochs: max number of training epochs
        """
       
        hyper_params_choice_type = {"rnn_type": ["RNN", "GRU", "LSTM"], 
                             "n_hidden_units": [32,64,128,256,512,1024,2048], 
                             "batch_size":[16,32,64,128,256,512],
                             "starting_lr": [1e-6, 1e-5, 1e-4, 1e-3, 1e-2, 1e-1, 1, 10]}

        hyperparam_ranges = {
                            "rnn_type": (0,len(hyper_params_choice_type["rnn_type"])*0.99), 
                            "rnn_layers":(1,4), "n_hidden_units":(0,len(hyper_params_choice_type["n_hidden_units"])*0.99),
                            "DNN_length": (10,30), 
                            "dropout_prob": (0,1), 
                            "starting_lr": (0,len(hyper_params_choice_type["starting_lr"])*0.99), 
                             "batch_size":(0,len(hyper_params_choice_type["batch_size"])*0.99)
                             }
        
        objective_wrapper = lambda rnn_type, rnn_layers, n_hidden_units, DNN_length,dropout_prob, starting_lr, batch_size, : self.objective_function(
            rnn_type, rnn_layers, n_hidden_units, DNN_length,dropout_prob, starting_lr, batch_size, hyper_params_choice_type, epochs)
        
        hyper_param_opt = BayesianOptimization(objective_wrapper, hyperparam_ranges, random_state=111) 
        hyper_param_opt.maximize(init_points=init_points, n_iter=iters)

        print(hyper_param_opt.max)
        hyper_params_dict = hyper_param_opt.max["params"]
        
        hyper_params_dict["rnn_type"] = hyper_params_choice_type["rnn_type"][floor(hyper_params_dict["rnn_type"])]
        hyper_params_dict["rnn_layers"] = floor(hyper_params_dict["rnn_layers"])
        hyper_params_dict["n_hidden_units"] = hyper_params_choice_type["n_hidden_units"][floor(hyper_params_dict["n_hidden_units"])]
        hyper_params_dict["DNN_length"] = floor(hyper_param_opt["DNN_length"])
        hyper_params_dict["dropout_prob"] = hyper_param_opt["dropout_prob"] 
        hyper_params_dict["starting_lr"] = hyper_params_choice_type["starting_lr"][floor(hyper_param_opt["starting_lr"] )]
        hyper_params_dict["batch_size"] = hyper_params_choice_type["batch_size"][floor(hyper_param_opt["batch_size"] )]
        return hyper_params_dict

    def objective_function(self, rnn_type, rnn_layers, n_hidden_units, DNN_length,dropout_prob, starting_lr, batch_size, hyper_params_choice_type, epochs):

        config = {  "rnn_type": hyper_params_choice_type["rnn_type"][floor(rnn_type)],
                    "rnn_layers":floor(rnn_layers),
                    "n_hidden_units": hyper_params_choice_type["n_hidden_units"][floor(n_hidden_units)],
                    "DNN_length": floor(DNN_length), 
                    "dropout_prob": dropout_prob, 
                    "starting_lr": hyper_params_choice_type["starting_lr"][floor(starting_lr)], 
                    "batch_size": hyper_params_choice_type["batch_size"][floor(batch_size)]}
        
        return - self.model_training(config, epochs, None, True) # maximize -mse

    
# if __name__=="__main__":

#         # load batch parameters
#     with open(r"C:\Users\<USER>\Documents\Aleph\Code\New\NUS_HybridApproachesProject\utility_notebooks\saved_objects\batch_params.json", "r") as f:
#         batch_params = json.load(f)
#     # load sqlite database details
#     db_path = r"C:\Users\<USER>\Documents\Aleph\Code\New\NUS_HybridApproachesProject\Batch_Data_Testing\ModelMaintainenceFiles"
#     db_name = "BatchModel.db"
#     #setup gproms model
#     model_path = r"C:\Users\<USER>\Documents\Aleph\Code\New\NUS_HybridApproachesProject\Batch_Data_Testing\BatchScaleModels\SNA_SNAt_GD"
#     gPROMS_file_name = "Crystallization_System"
#     gPROMS_process_entity_name = "Crystallization_System"
#     encryption_password = "Pfizer@123"
#     sim_time = 56460
#     report_time = 1880
#     # inputs and limits for the creation of dataset
#     input_name_ls = [ "SNA_RC", "SNA_SF", "SNAt_RC", "SNAt_SSO", "SNAt_MPS","CGD_AE", "CGD_GR", "CGD_SSO", "CGD_DC"]
#     model_param_limits = [(-50, -10), (0.1, 1), (-50,50), (0.001,3), (0.01,100),
#                         (batch_params["CGD_AE"]*0.95, batch_params["CGD_AE"]*1.05), (batch_params["CGD_GR"]*0.95, batch_params["CGD_GR"]*1.05),
#                             (batch_params["CGD_SSO"]*0.95, batch_params["CGD_SSO"]*1.05), (0.95*batch_params["CGD_DC"], 1.05*batch_params["CGD_DC"])]
#     # name of outputs
#     output_name_ls = ['Temperature', 'CRY_MF_API', 'PSD_D90', 'PSD_VMD']
#     model_name = "batch_plant"
#     # gproms model
#     mod = model.gpromsModel(gPROMS_file_name, gPROMS_process_entity_name, encryption_password, sim_time, report_time, input_name_ls, output_name_ls, model_path, model_name)
#     # datset class
#     dataset_cl = dataset_build.dataset(db_path, db_name, model_param_limits, mod)

#     training = SurrogateModel_Learning(dataset_cl)

#     config = {  "rnn_type": "GRU",
#                     "rnn_layers": 1,
#                     "n_hidden_units": 1024,
#                     "DNN_length": 20, 
#                     "dropout_prob": 0.01, 
#                     "starting_lr": 1e-4, 
#                     "batch_size": 32 
#     }
#     training.model_training(config, 250, cur_path/"temp")