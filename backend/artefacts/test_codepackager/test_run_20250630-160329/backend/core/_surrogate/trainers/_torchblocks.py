import torch
from typing import Literal

class SequenceBlock(torch.nn.Module):
    def __init__(
            self, 
            input_size: int,
            hidden_size: int,
            rnn_type: Literal["RNN", "LSTM", "GRU"],
            num_layers: int = 1
    ):
        super().__init__()
        rnn_classes = {
            "RNN": torch.nn.RNN,
            "GRU": torch.nn.GRU,
            "LSTM": torch.nn.LSTM,
        }
        
        self.rnn = rnn_classes[rnn_type](
            input_size = input_size,
            hidden_size = hidden_size,
            num_layers= num_layers,
            batch_first = True
        )
        self.norm = torch.nn.LayerNorm(hidden_size)
    
    def forward(self, x):
        x, _ = self.rnn(x)
        # ??? what is self.rnn returning
        return self.norm(x)
    
class ResidualDenseBlock(torch.nn.Module):
    def __init__(
            self,
            hidden_size:int, 
            num_layers: int,
            dropout_prob: float = 0.1
    ):
        super().__init__()
        self.layers = torch.nn.ModuleList(
            [
                torch.nn.Linear(hidden_size, hidden_size)
                for _ in range(num_layers)
            ]
        )
        # ??? hidden size is the number of "neurons" i.e nodes in the layer?
        self.norms = torch.nn.ModuleList(
            [
                torch.nn.LayerNorm(hidden_size)
                for _ in range(num_layers)
            ]
        )
        self.alphas = torch.nn.ParameterList(
            [
                torch.nn.Parameter(torch.ones(hidden_size))
                for _ in range(num_layers)
            ]
        )
        self.dropout = torch.nn.Dropout(p=dropout_prob)
        self.activation = torch.nn.GELU()
        self.num_layers = num_layers
    
    def forward(self, x, x_seed):
        # ??? what is X? is it a tensor... if so of what? 
        for i in range(self.num_layers):

            x_0 = self.alphas[i] * (x + x_seed) # weighted by seed and current tensor
            x = self.layers[i](x)
            x = self.norms[i](x+ x_0)
            x = self.activation(x)
            x = self.dropout(x)
        
        return x



