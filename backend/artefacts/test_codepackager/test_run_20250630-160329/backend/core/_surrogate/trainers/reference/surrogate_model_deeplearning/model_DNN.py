import warnings # remove warning messages
warnings.filterwarnings("ignore")

import torch
from torch import nn
# device agnostic code
device = "cuda" if torch.cuda.is_available() else "cpu"

class Surrogate_DeepLearning_Model(nn.Module):
    """
    Deep Learning Surrogate Model built based on gPROMS simulation data.

    Attributes:
    ----------
        1) t_steps: number of time_steps
        2) RNN_layer: RNN layer used (input_size=n_inputs, output_size =n_hidden_units)
        3) norm_RNN: LayerNorm post RNN 
        4) DNN_layer_s: Module List for Linear DNN(input_size=output_size=n_hidden_units)/ size of list defined by user
        5) norm_layers: Module List for each LayerNorm after each DNN/ size of list defined by user
        6) alpha: List of parameters representing weightage to each skip connection/ size of list defined by user 
        size of lists above are the same
        7) output: Linear Layer (input_size=n_hidden_units, output_size=n_outputs)
        8) dropout: Dropout Layer.
        9) GELU: GELU layer
    Methods
    -------
        1) __init__: Constructor of the class. The model's hyper parameters are specified here.
        2) forward: forward pass of the model

    Network Structure
    -----------------
                                            
                                              ____________________________________________                                               
                                              |         |                                 A (used next time step)           
                                              |         V                                 |           
        Input -> RNN_layer -> LayerNorm_RNN ----> [DNNN --> LayerNorm_N -> GELU Activation -> Dropout] X DNN_length --> output_layer --> Output 
    
    """
    def __init__(self, n_inputs,  n_outputs, n_time_steps=68, rnn_type="RNN",rnn_layers=1, n_hidden_units=256, DNN_length=20,  dropout_prob=0.001):
        super().__init__()
        """
        Constructor of the class and the parameters of the methods represent the model's hyperparameters.

        Parameters
        ----------
        n_inputs: int 
            number of input variables
        n_outputs: int
            number of output variables
        n_time_steps: int
            number of time steps
        rnn_type: str
            can be "RNN"/"GRU"/"LSTM"
        rnn_layers: int
            number of RNN layers initially
        n_hidden_units: int
            width of all layers in the network
        DNN_length: int
            number of DNN layers after RNN.
        dropout_prob: float
            dropout probability for dropout layer

        """
        self.t_steps = n_time_steps
        self.DNN_len = DNN_length
        # RNN layers
        if rnn_type == "RNN":
            self.RNN_layer = nn.RNN(input_size=n_inputs, hidden_size=n_hidden_units, batch_first=True, num_layers=rnn_layers)
        elif rnn_type=="GRU":
            self.RNN_layer = nn.GRU(input_size=n_inputs, hidden_size=n_hidden_units, batch_first=True, num_layers=rnn_layers)
        elif rnn_type=="LSTM":
            self.RNN_layer = nn.GRU(input_size=n_inputs, hidden_size=n_hidden_units, batch_first=True, num_layers=rnn_layers)
        else:
            raise ValueError("The rnn_type can be either 'RNN'/'GRU'/'LSTM'")
        self.norm_RNN = nn.LayerNorm(n_hidden_units)
        self.DNN_layer_s = nn.ModuleList([nn.Linear(in_features=n_hidden_units, out_features=n_hidden_units) for _ in range(DNN_length)])
        self.norm_layers =  nn.ModuleList([nn.LayerNorm(n_hidden_units) for _ in range(DNN_length)])
        self.alpha = nn.ParameterList([nn.Parameter(torch.ones(n_hidden_units)) for _ in range(DNN_length)])
        self.output = nn.Linear(in_features=n_hidden_units, out_features=n_outputs)
        self.dropout = nn.Dropout(p=dropout_prob)
        self.GELU = nn.GELU()
           
    
    def forward(self, x):
        """
        Method representing forward propogation of the model.

        Input
        -----
            x: torch.tensor, dtype: torch.float32, shape:(batch_size, n_inputs)
        
        Output
        ======
            torch.tensor
        """

        # Create a sequence from the single input
        x = x.unsqueeze(1).repeat(1, self.t_steps, 1)  # (batch_size, t_steps, n_features)

        x, h_ = self.RNN_layer(x)
        x = self.norm_RNN(x)
        x_RNN = x

        for i in range(self.DNN_len):
            x_0 = self.alpha[i] * ( x + x_RNN )
            x = self.DNN_layer_s[i]( x )   
            x = self.norm_layers[i]( x + x_0 )
            x = self.GELU(x)
            x = self.dropout(x)
            
        return self.output(x)
        
        
        