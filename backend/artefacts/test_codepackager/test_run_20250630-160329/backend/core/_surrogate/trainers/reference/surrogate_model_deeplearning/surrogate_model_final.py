import warnings
warnings.filterwarnings("ignore")

import os
import pathlib
import sys
import json

import numpy as np
import pandas as pd

import torch
from torch import nn
import torch.nn.functional as F


device = "cuda" if torch.cuda.is_available() else "cpu"

cur_path = pathlib.Path(__file__).resolve().parent

sys.path.append(str(cur_path.parent.parent))
import model
sys.path.append(str(cur_path.parent.parent/"surrogateModel"))
from surrogateModel.dataset import dataset_build
from surrogateModel.infeasible_region_detection import model_infeasibilty_det

from surrogateModel.surrogate_model_deeplearning.deeplearning_dataset import base_dataset, pytorch_dataset
from surrogateModel.surrogate_model_deeplearning.model_DNN import Surrogate_DeepLearning_Model
print(f"Device used: {device}")


class Surrogate_Model:
    """
    Class written to make surrogate model predictions which behave closely to gPROMS Model.

    Attributes
    ----------
        1) actual_model: equivalent gproms model class
        2) tsteps: time steps 
        3) y_scaling: if y is scaled
        4) X_scaler, y_scaler: input and output scaler
        5) surrogate_model: surrogate model
        6) create_infeasiblity_detection_model: Method writtern to create the infeasibility model based on a config json and model weights.
    
    Methods
    -------
        1) __init__: Constructor of the class.
        2) create_model: Method writtern to create the model based on a config json and model weights.
        3) prediction: Surrogate model prediction method to match format of gproms model prediction.
    
    """
    def __init__(self, dataset_cl: dataset_build.dataset, surrogate_config_json, weights_pth_file, y_transform=True):
        """
        Constructor of the class.

        Parameters
        ----------
            dataset_cl: dataset class
                dataset class of the surrogate model dataset
            surrogate_config_json: model config json path
            weights_pth_file: model weights path
            y_transform: whether outputs were transformed
        """
        # actual model
        self.actual_model = dataset_cl.model

        # base dataset to get y and X transform
        base_dset = base_dataset(dataset_cl, y_transform)
        self.tsteps = base_dset.n_time_steps
        self.y_scaling = y_transform
        if y_transform==None:
            self.X_scaler = base_dset.scaler_X
        else:
            self.X_scaler, self.y_scaler = base_dset.scaler_X, base_dset.scaler_y
        # surrogate model set-up
        
        self.surrogate_model = self.create_model(surrogate_config_json,weights_pth_file)

    

    def create_model(self, model_config_json, model_weights_pth):
        """
        Method writtern to create the model based on a config json and model weights.

        Parameters
        ----------
            model_config_json: model config json path
            model_weights_pth: model weights path
        
        Returns
        -------
            functional surrogate model
                
        """
        with open(model_config_json, "r") as f:
            model_config = json.load(f)
        model= Surrogate_DeepLearning_Model(len(self.actual_model.input_name_list), len(self.actual_model.output_name_list), self.tsteps,
                                            model_config["rnn_type"], model_config["rnn_layers"], model_config["n_hidden_units"],
                                            model_config["DNN_length"], model_config["dropout_prob"])
        model.load_state_dict(torch.load(model_weights_pth, weights_only=True))
        return model

    def prediction(self, input):
        """
        Surrogate model prediction method to match format of gproms model prediction.

        Parameters
        ----------
            input: list
                input data in same format as gproms model
        Returns
        -------
            output: dataframe of input and output based on time
        """
        input = self.X_scaler.transform(np.array(input).reshape((1, len(input))))
        input_tensor = torch.from_numpy(input).to(torch.float32)

        with torch.inference_mode():
            output_tensor = self.surrogate_model(input_tensor)
        
        output_tensor = output_tensor.numpy()
        output_tensor = self.y_scaler.inverse_transform(output_tensor.reshape((-1, len(self.actual_model.output_name_list))))
        output_df= pd.DataFrame(output_tensor, columns=self.actual_model.output_name_list)
        
        input_tensor= input_tensor.reshape(len(self.actual_model.input_name_list)).repeat(len(output_df),1)
        input_df = pd.DataFrame(self.X_scaler.inverse_transform(input_tensor), columns=self.actual_model.input_name_list)
        merged_df = pd.merge(left=input_df, right=output_df, how="inner", left_index=True, right_index=True).reset_index()
        merged_df["Time"] = self.actual_model.reporting_interval * merged_df["index"]
        merged_df = merged_df.drop(columns="index")

        return merged_df
    
    

    

class Infeasibility_Model:
    """
    Class written to encompass the infeasibility model.

    Attributes
    ----------
        1) threshold: probability threshold for predictions
        2) infeasibility_model: Infeasibility model network contained trained weights.
    
    Methods
    -------
        1) __init__: Constructor of the class.
        2) create_model: Method writtern to create the infeasibility model based on a config json and model weights.
        3) infeasibility_detection: Infeasibility classification method matching format of gproms input.
    
    """
    def __init__(self,dataset_cl, model_config_json, model_weights_pth, selected_threshold):
        """
        Constructor of the class.

        Parameters
        ----------
            dataset_cl: dataset class
                dataset class of the surrogate model dataset
            surrogate_config_json: model config json path
            weights_pth_file: model weights path
            selected_threshold: probability threshold for predictions
        """
        self.actual_model = dataset_cl.model
        self.threshold = selected_threshold
        self.infeasibility_model = self.create_infeasiblity_detection_model(model_config_json, model_weights_pth)
        base_dset = base_dataset(dataset_cl)
        self.X_scaler = base_dset.scaler_X
        

    def create_infeasiblity_detection_model(self, model_config_json, model_weights_pth):
        """
        Method writtern to create the infeasibility model based on a config json and model weights.

        Parameters
        ----------
            model_config_json: model config json path
            model_weights_pth: model weights path
        
        Returns
        -------
            infeasibility_model
                
        """
        with open(model_config_json, "r") as f:
            model_config = json.load(f)
        infeasibility_model =  model_infeasibilty_det.Classification_Network(len(self.actual_model.input_name_list), model_config["n_hidden_layers"], model_config["hidden_units"],
                                       model_config["hidden_activation_function"], model_config["dropout_prob"])
        infeasibility_model.load_state_dict(torch.load(model_weights_pth, weights_only=True))

        return infeasibility_model


    def infeasibility_detection(self, input):
        """
        Infeasibilit classification method matching format of gproms input.

        Parameters
        ----------
            input: list
                input data in same format as gproms model
        Returns
        -------
            output: dataframe of input and output based on time
        """
        input = self.X_scaler.transform(np.array(input).reshape((1, len(input))))
        input_tensor = torch.from_numpy(input).to(torch.float32)

        with torch.inference_mode():
            output_tensor = self.infeasibility_model(input_tensor)
        
        output_tensor = output_tensor.numpy()

        y_predict = output_tensor>self.threshold

        return y_predict.astype(int)[0]
    
class SurrogateModel_w_Infeasibility:
    """
    Class to combine both surrogate model and infeasibility detection model. If infeasibility detection model
    classifies a point as infeasible then we run it via gPROMS otherwise we run the surrogate model.

    Attributes
    ----------
        1) surrogate_model: surrogate_model
        2) infeasibility_model: infeasibility_model
    Methods
    -------
        1) __init__: Constructor of the class.
        2) prediction: Method representing prediction mechanism of Infeasibility Classification followed by model prediction(Surrogate/Actual Model.)
    
    """
    def __init__(self, surrogate_model: Surrogate_Model, infeasibility_model: Infeasibility_Model):
        """
        Constructor of the class.

        Parameters
        ----------
            surrogate_model: surrogate_model
            infeasibility_model: infeasibility_model

        """
        self.surrogate_model = surrogate_model
        self.infeasibility_model = infeasibility_model

    def prediction(self, input):
        """
        Method representing prediction mechanism of Infeasibility Classification followed by model prediction(Surrogate/Actual Model.)
        """
        det = self.infeasibility_model.infeasibility_detection(input)

        if det == 0:
            return self.surrogate_model.prediction(input)
        else:
            print("Infeaibility detected, Running actual model")
            return self.surrogate_model.actual_model.simple_simulation(input)

# testing code

# if __name__=="__main__":

#         # load batch parameters
#     with open(r"C:\Users\<USER>\Documents\Aleph\Code\New\NUS_HybridApproachesProject\utility_notebooks\saved_objects\batch_params.json", "r") as f:
#         batch_params = json.load(f)
#     # load sqlite database details
#     db_path = r"C:\Users\<USER>\Documents\Aleph\Code\New\NUS_HybridApproachesProject\Batch_Data_Testing\ModelMaintainenceFiles"
#     db_name = "BatchModel.db"
#     #setup gproms model
#     model_path = r"C:\Users\<USER>\Documents\Aleph\Code\New\NUS_HybridApproachesProject\Batch_Data_Testing\BatchScaleModels\SNA_SNAt_GD"
#     gPROMS_file_name = "Crystallization_System"
#     gPROMS_process_entity_name = "Crystallization_System"
#     encryption_password = "Pfizer@123"
#     sim_time = 56460
#     report_time = 1880
#     # inputs and limits for the creation of dataset
#     input_name_ls = [ "SNA_RC", "SNA_SF", "SNAt_RC", "SNAt_SSO", "SNAt_MPS","CGD_AE", "CGD_GR", "CGD_SSO", "CGD_DC"]
#     model_param_limits = [(-50, -10), (0.1, 1), (-50,50), (0.001,3), (0.01,100),
#                         (batch_params["CGD_AE"]*0.95, batch_params["CGD_AE"]*1.05), (batch_params["CGD_GR"]*0.95, batch_params["CGD_GR"]*1.05),
#                             (batch_params["CGD_SSO"]*0.95, batch_params["CGD_SSO"]*1.05), (0.95*batch_params["CGD_DC"], 1.05*batch_params["CGD_DC"])]
#     # name of outputs
#     output_name_ls = ['Temperature', 'CRY_MF_API', 'PSD_D90', 'PSD_VMD']
#     model_name = "batch_plant"
#     # gproms model
#     mod = model.gpromsModel(gPROMS_file_name, gPROMS_process_entity_name, encryption_password, sim_time, report_time, input_name_ls, output_name_ls, model_path, model_name)
#     # datset class
#     dataset_cl = dataset_build.dataset(db_path, db_name, model_param_limits, mod)

#     surrogate_model = Surrogate_Model(dataset_cl, cur_path/"temp/model_config.json", cur_path/"temp/best_model.pth")
#     infeasiblity_model = Infeasibility_Model(dataset_cl, cur_path.parent/"infeasible_region_detection/temp/model_config.json", cur_path.parent/"infeasible_region_detection/temp/best_model.pth", 0.9)

#     f = SurrogateModel_w_Infeasibility(surrogate_model,infeasiblity_model)

#     print(f.prediction([-50.0,	0.1000,	-50.0,	0.001000,	0.01000,	7916.666666,	0.000827,	1.821150,	3.4580]))
#     print(f.prediction([-39.0,	0.353125,	-34.0,	2.906281,	21.882812,	8359.375000,	0.000890,	1.922991,	3.765125]))