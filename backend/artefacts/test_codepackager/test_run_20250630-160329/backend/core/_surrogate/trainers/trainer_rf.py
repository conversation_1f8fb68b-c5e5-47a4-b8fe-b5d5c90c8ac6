from __future__ import annotations
from typing import Dict, List, Optional, Tuple, Any, Union, cast, Callable
import numpy as np
import pandas as pd
import time
import os

from sklearn.ensemble import RandomForestRegressor

from ..valueobjects import (
    VOMetadata_General, 
    VODataset, 
    VOConfig_Training, 
    VOConfig_Model,
    VOResult_ModelTest,
    VOLog_ModelMetrics,
    VOConfig_Parameter,
    VOLog_EpochMetrics
)
from ..entities import ENTTrainingJob
from ..trainers.trainer_base import SurrogateTrainerPort
from .._enums import EnumTrainingStatus, EnumSurrogateAlgorithm, EnumMetricName, Hyperparams
from .._utilities import *

class RandomForestTrainer(SurrogateTrainerPort[RandomForestRegressor]):
    """
    Trainer for Random Forest regression models using scikit-learn.
    """
    
    def _get_mlframework_param_map(self) -> Dict[Hyperparams.BaseHPEnum, str]:
        """
        Returns mapping from RF hyperparameter enums to scikit-learn parameter names.
        
        Returns:
            Dictionary mapping hyperparameter enums to scikit-learn parameter names
        """
        return {
            Hyperparams.RF.N_ESTIMATORS: "n_estimators",
            Hyperparams.RF.MAX_DEPTH: "max_depth",
            Hyperparams.RF.MIN_SAMPLES_SPLIT: "min_samples_split",
            Hyperparams.RF.MIN_SAMPLES_LEAF: "min_samples_leaf",
            Hyperparams.RF.MIN_WEIGHT_FRACTION_LEAF: "min_weight_fraction_leaf",
            Hyperparams.RF.MAX_LEAF_NODES: "max_leaf_nodes",
            Hyperparams.RF.MIN_IMPURITY_DECREASE: "min_impurity_decrease",
            Hyperparams.RF.MAX_FEATURES: "max_features",
            Hyperparams.RF.BOOTSTRAP: "bootstrap",
            Hyperparams.RF.OOB_SCORE: "oob_score",
            Hyperparams.RF.CRITERION: "criterion",
            Hyperparams.RF.RANDOM_STATE: "random_state",
        }

    def _initialize_model(self, model_configuration: VOConfig_Model) -> RandomForestRegressor:
        """
        Initialize RandomForest model with configuration.
        
        Args:
            model_configuration: Configuration containing RandomForest parameters
        """
        params_dict = {param.name: param.value for param in model_configuration.parameters}
        sklearn_params = self.enum2str_paramdict(params_dict) # type: ignore
        
        # TODO  should there be defaults, given tha twe have confguration parameters?
        # Set defaults
        sklearn_params.setdefault("n_estimators", 100)
        sklearn_params.setdefault("random_state", self.random_seed)
        sklearn_params.setdefault("n_jobs", -1)
        
        # Create model with all params in one shot
        _model = RandomForestRegressor(**sklearn_params)

        return _model

    def _predict(
        self,
        model: RandomForestRegressor,
        df_x: pd.DataFrame,
        df_y: Optional[pd.DataFrame] = None
    ) -> pd.DataFrame:
        """
        Make predictions with the current model and return as a DataFrame.
        
        Returns:
            DataFrame of predictions with same index as input
        """
        # Get predictions as numpy array
        y_pred = model.predict(df_x)
        
        # Convert to DataFrame with same index as input
        if len(y_pred.shape) > 1 and y_pred.shape[1] > 1:
            # Multi-output case - use column names from df_y if available
            if df_y is not None and len(df_y.columns) == y_pred.shape[1]:
                cols = [f"pred_{col}" for col in df_y.columns]
            else:
                cols = [f"prediction_{i}" for i in range(y_pred.shape[1])]
            df_pred = pd.DataFrame(y_pred, index=df_x.index, columns=cols)
        else:
            # Single-output case - flatten if needed and use target column name if available
            if len(y_pred.shape) > 1:
                y_pred = y_pred.flatten()
                
            if df_y is not None and len(df_y.columns) == 1:
                col_name = f"pred_{df_y.columns[0]}"
            else:
                col_name = "prediction"
                
            df_pred = pd.DataFrame(y_pred, index=df_x.index, columns=[col_name])
        
        return df_pred
    
    def get_feature_importance(self, model: RandomForestRegressor, df_x: pd.DataFrame) -> Dict[str, float]:
        """
        Get feature importance scores from model trained on the provided features.
        
        Returns:
            Dictionary mapping feature names to importance scores (0-1)
        """
        try:
            # Get feature names directly from the DataFrame
            feature_names = list(df_x.columns)
            
            # Validate model has feature importance attribute
            if not hasattr(model, 'feature_importances_'):
                raise RuntimeError("Model does not have feature_importances_ attribute. Ensure it's properly trained.")
            
            # Get feature importance from model
            importance_values = model.feature_importances_
            
            # Validate dimensions match
            if len(feature_names) != len(importance_values):
                raise ValueError(f"Feature count mismatch: {len(feature_names)} names vs {len(importance_values)} importance values")
            
            # Normalize to sum to 1.0 if not already
            if abs(sum(importance_values) - 1.0) > 1e-10:
                importance_values = importance_values / importance_values.sum()
            
            # Create dictionary mapping feature names to importance values
            return {name: float(importance) for name, importance in zip(feature_names, importance_values)}
            
        except Exception as e:
            # Provide informative error
            if isinstance(e, (ValueError, RuntimeError)):
                raise
            raise RuntimeError(f"Failed to calculate feature importance: {str(e)}") from e

    def _run_standard_training(
            self,
            training_data: VODataset,
            training_config: VOConfig_Training,
            model_config: VOConfig_Model,
            job: ENTTrainingJob,
        ) -> RandomForestRegressor:
        """
        Execute standard training with the appropriate strategy based on validation approach.
            
        Returns:
            Trained RandomForestRegressor
        """
        # Initialize model first
        model = self._initialize_model(model_config)
        
        # Strategy dispatcher
        strategies: Dict[str, Callable] = {
            "simple_split": self._train_simple_split,
            "cross_validation": self._train_cv_model,
        }
        
        # Get appropriate strategy
        strategy = strategies[training_config.validation_strategy]
        
        # Execute strategy passing the initialized model
        return strategy(model, training_data, training_config, job)

    def _train_simple_split(
        self,
        model: RandomForestRegressor,
        training_data: VODataset,
        training_config: VOConfig_Training,
        job: ENTTrainingJob,
    ) -> RandomForestRegressor:
        """
        Train model using simple train/validation split.
            
        Returns:
            Trained RandomForestRegressor
        """
        def _fit_model(model: RandomForestRegressor, X: pd.DataFrame, y: pd.DataFrame) -> None:
            """
            Fit RandomForest model to training data.
            """
            # Fit model to training data
            # Handle multi-output regression if y has multiple columns
            if y.shape[1] > 1:
                model.fit(X, y.values)
            else:
                # If single target, convert to 1D array as sklearn prefers
                model.fit(X, y.values.ravel())

        self.logger.info(f"Training Random Forest model with simple split: {len(training_data.df_x)} samples")
        start_time = time.time()
        training_set, validation_set = split_data_simple(training_data, training_config.validation_split, self.random_seed)
        
        # Fit the model using stateless method
        _fit_model(model, training_data.df_x, training_data.df_y)
        
        # Calculate metrics
        train_metrics = self._compute_model_metrics(model, training_set)
        val_metrics = self._compute_model_metrics(model, validation_set)
        
        # Create epoch metrics
        epoch_metrics = VOLog_EpochMetrics(
            epoch=0,
            train_metrics=train_metrics,
            validation_metrics=val_metrics
        )
        
        # Store in job
        if job.epoch_metrics is None:
            job.epoch_metrics = []
        job.epoch_metrics.append(epoch_metrics)
        
        # Log training time
        training_time = time.time() - start_time
        self.logger.info(f"Training completed in {training_time:.2f} seconds")
        
        return model

    def _train_cv_model(
        self,
        model: RandomForestRegressor,  # Accept model but don't use it - we create per-fold models
        training_data: VODataset,
        training_config: VOConfig_Training,
        job: ENTTrainingJob,
    ) -> RandomForestRegressor:
        """
        Train model using K-fold cross-validation.
        
        Returns:
            Trained RandomForestRegressor (best model from CV)
        """
        def _select_best_cv_model_idx(metrics_list: List[VOLog_ModelMetrics], primary_metric: EnumMetricName) -> int:
            """
            Select the index of the best model based on the primary metric.
            
            Args:
                metrics_list: List of metrics for each fold
                primary_metric: Metric name to optimize
                
            Returns:
                Index of the best model
            """
            # Extract metric values
            # TODO: ensure error is raised if there is a None

            metric_values = [
                metric.get_value(primary_metric)
                for metric in metrics_list
            ]
            if None in metric_values:
                raise ValueError("Found `None` values for metric")
            
            # Determine if metric should be maximized or minimized
            maximize_metrics = {EnumMetricName.R2, EnumMetricName.DIRECTIONAL_ACCURACY}
            maximize = primary_metric in maximize_metrics
            
            # Select best model index
            if maximize:
                return int(np.argmax(metric_values)) # type: ignore
            else:
                return int(np.argmin(metric_values)) # type: ignore

        # Basic validation
        assert training_config.cv_folds is not None and training_config.cv_folds > 1, "CV folds must be > 1"
        
        self.logger.info(f"Training Random Forest model with {training_config.cv_folds}-fold CV: {len(training_data.df_x)} samples")
        start_time = time.time()
        
        # Initialize storage for CV results
        cv_models: List[RandomForestRegressor] = []
        cv_train_metrics: List[VOLog_ModelMetrics] = []
        cv_val_metrics: List[VOLog_ModelMetrics] = []
        
        # Initialize job epoch metrics if needed
        if job.epoch_metrics is None:
            job.epoch_metrics = []
        
        # Train model for each fold
        for fold_idx in range(training_config.cv_folds):
            self.logger.info(f"Training fold {fold_idx+1}/{training_config.cv_folds}")
            
            # Get data split for this fold based on validation strategy
            if training_config.validation_strategy == "cross_validation":
                fold_train, fold_val = split_data_cv(
                    training_data, 
                    training_config.cv_folds, 
                    fold_idx, 
                    self.random_seed
                )
            else:
                raise ValueError(f"Unsupported CV strategy: {training_config.validation_strategy}")
            
            # Create a new model instance for this fold
            fold_params = {param.name: param.value for param in model_config.parameters}
            fold_sklearn_params = self.enum2str_paramdict(fold_params) # type: ignore
            fold_sklearn_params.setdefault("n_estimators", 100)
            fold_sklearn_params.setdefault("random_state", self.random_seed)
            fold_sklearn_params.setdefault("n_jobs", -1)
            
            fold_model = RandomForestRegressor(**fold_sklearn_params)
            
            
            # Fit model with appropriate handling of target shape
            y_data = fold_train.df_y.values
            y_train = y_data.ravel() if y_data.shape[1] == 1 else y_data
            fold_model.fit(fold_train.df_x, y_train)
            
            # Evaluate model on this fold
            fold_train_metrics = self._compute_model_metrics(fold_model, fold_train)
            fold_val_metrics = self._compute_model_metrics(fold_model, fold_val)
            
            # Store results for this fold
            cv_models.append(fold_model)
            cv_train_metrics.append(fold_train_metrics)
            cv_val_metrics.append(fold_val_metrics)
            
            # Log fold metrics
            job.epoch_metrics.append(VOLog_EpochMetrics(
                epoch=fold_idx,
                train_metrics=fold_train_metrics,
                validation_metrics=fold_val_metrics
            ))
        
        # Select best model based on validation metric
        best_fold_idx = _select_best_cv_model_idx(cv_val_metrics, training_config.primary_metric)
        best_model = cv_models[best_fold_idx]
        
        # Log overall metrics and timing
        cv_time = time.time() - start_time
        self.logger.info(f"CV training completed in {cv_time:.2f} seconds. Best fold: {best_fold_idx+1}")
        
        return best_model


    def _hpo_training(
        self,
        model: RandomForestRegressor,
        training_data: VODataset,
        params: Dict[Hyperparams.BaseHPEnum, Any],
    ) -> RandomForestRegressor:
        """
        Train model with specific parameters for HPO.
        """
        sklearn_params = self.enum2str_paramdict(params) # type: ignore
        sklearn_params.setdefault("random_state", self.random_seed)
        
        # Determine proper parallelism based on HPO context
        total_cpus = os.cpu_count() or 1
        parallel_trials = getattr(self, 'hpo_parallel_trials', 1)
        # Use proportional allocation to avoid over-subscription
        n_jobs = max(1, int(total_cpus / (parallel_trials * 2)))
        sklearn_params.setdefault("n_jobs", n_jobs)
        
        # Update parameters on existing model to preserve non-tuned parameters
        model.set_params(**sklearn_params)
        
        # Fit model with appropriate target shape handling
        y_data = training_data.df_y.values
        y_train = y_data.ravel() if y_data.shape[1] == 1 else y_data
        model.fit(training_data.df_x, y_train)
        
        return model


