from __future__ import annotations
from .._imports import *
from .._enums import *
from ..valueobjects import VODataset

import typing as T
import joblib
import io
import base64
import uuid
import pandas as pd
import numpy.typing as npt
import sklearn.preprocessing
import sklearn.impute
import sklearn.base
from typing import Callable, List, Tuple, TypeVar, Union, Any, Dict, Optional


# TYPING

class SklearnImputeProtocol(Protocol):
    def fit(self, X: Any, y: Optional[Any] = None) -> "SklearnImputeProtocol":
        ... # The '...' indicates no implementation here

    def transform(self, X: Any) -> Any:
        ...

class SklearnTransformerProtocol(SklearnImputeProtocol, Protocol):
    def inverse_transform(self, X: Any) -> Any:
        ...


# MAIN
# NOTE - private helper methods avoid direct interdependencies. Orchaestration is the responsibility of the public methods. 

class SurrogateDataTransformer():
    
    def __init__(
        self,
        uid: uuid.UUID,
        df_x: pd.<PERSON><PERSON>rame,
        df_y: pd.<PERSON><PERSON>rame,
        timeset_col: Optional[str] = None,
        timestep_col: Optional[str] = None,
        x_imputer: Optional[SklearnImputeProtocol] = None,
        x_scaler: Optional[SklearnTransformerProtocol] = None,
        y_imputer: Optional[SklearnImputeProtocol] = None,
        y_scaler: Optional[SklearnTransformerProtocol] = None,
        col2uid_dict: Optional[Dict[str,uuid.UUID]] = None,
        random_state: int = 42,
    ):  
    
        self.uid = uid or uuid.uuid4()
        
        # Column Identifiers
        self._initial_x_variable_cols: List[str] = []
        self._initial_y_variable_cols: List[str] = []
        self._timeset_col: Optional[str] = None
        self._timestep_col: Optional[str] = None
        self._timestep_values: Optional[Any] = None # for remembering timestep values that are entered by user.
        self.random_state = random_state

        # State: Fitted transformers (will be populated by _init_transformer)
        self._x_imputer_fitted = None
        self._y_imputer_fitted = None
        self._x_scaler_fitted = None
        self._y_scaler_fitted = None
        
        self._col2var = col2uid_dict

        # Orchestrate the initial pipeline run
        self._init_columns(df_x, df_y, timeset_col, timestep_col)
        self._fit_transformers(df_x, df_y, x_imputer, x_scaler, y_imputer, y_scaler)
    @property
    def timeset_col(self):
        if self._timeset_col is None:
            raise AttributeError(f"no timeset col set")
        return self._timeset_col

    @property
    def timestep_col(self):
        if self._timestep_col is None:
            raise AttributeError(f"no timestep col set")
        return self._timestep_col

    @property
    def timestep_values (self):
        if self._timestep_values is None:
            raise AttributeError(f"no timestep values set")
        return self._timestep_values

    @property
    def col2var(self) -> Dict[str, uuid.UUID]:
        if self._col2var is None: 
            raise ValueError("Attribute has not been loaded")
        return self._col2var
    
    @property
    def var2col(self) -> Dict[uuid.UUID, str]:
        if self._col2var is None: 
            raise ValueError("Attribute has not been loaded")
        return {v: k for k, v in self._col2var.items()}
    
    @property
    def x_cols(self)-> T.List[str]:
        if self._initial_x_variable_cols is None:
            raise ValueError(f"x_cols are empty. Have they been initialized correctly?")
        return self._initial_x_variable_cols

    @property
    def y_cols(self)-> T.List[str]:
        if self._initial_y_variable_cols is None:
            raise ValueError(f"y_cols are empty. Have they been initialized correctly?")
        return self._initial_y_variable_cols
    
    def get_col_name(self, uid: uuid.UUID) ->str:
        """
        Given a variable UID, return the corresponding column name.
        """
        return self.var2col[uid]

    def get_col_uid(self, col: str) ->uuid.UUID:
        """
        Given a column name, return the corresponding variable UID.
        """
        return self.col2var[col]
    

    @property
    def is_timeseries(self):
        return bool(self._timeset_col)
    
    def _init_columns(self, df_x: pd.DataFrame, df_y: pd.DataFrame, timeset_col: Optional[str] = None, timestep_col: Optional[str] = None):
        """
        Initializes column header attribtutes.
        """
        df_y_cols = df_y.columns.tolist()
        df_x_cols=df_x.columns.tolist()
         
        # DEFENSIVE
        if bool(timeset_col) != bool(timestep_col):
            raise AttributeError(f"Both timeset and timestep columns need to be specified for timeseries or sequence data")

        # Set Attrs
        if (timestep_col is not None and timeset_col is not None):
            # Validate membershiop
            if timeset_col not in df_y_cols or timeset_col not in df_x_cols:
                raise AttributeError(f"Timeset col not specified in dataframe. Double check")
            if timestep_col not in df_y_cols or timestep_col not in df_x_cols:
                raise AttributeError(f"Timestep col not specified in dataframe. Double check")

            # Set attributes
            self._timeset_col = timeset_col
            self._timestep_col = timestep_col
            self._timestep_values = list(df_x[self._timestep_col].drop_duplicates().sort_values()) # for remembering timestep values that are entered by user.
            self._initial_x_variable_cols = [col for col in df_x_cols if col not in [timeset_col, timestep_col]]
            self._initial_y_variable_cols= [col for col in df_y_cols if col not in [timeset_col, timestep_col]]
        else:
            self._initial_x_variable_cols = df_x_cols
            self._initial_y_variable_cols = df_y_cols

    def _fit_transformers(
        self, 
        x: pd.DataFrame, 
        y: pd.DataFrame, 
        x_imputer: Optional[SklearnImputeProtocol] = None,
        x_scaler: Optional[SklearnTransformerProtocol] = None,
        y_imputer: Optional[SklearnImputeProtocol] = None,
        y_scaler: Optional[SklearnTransformerProtocol] = None
    ) -> None:
        """
        Fit the transformer given the seed dataframes.
        
        This method initializes and fits imputers and scalers on the feature columns,
        excluding any time series columns (timeset and timestep).
        """
        # Extract only feature columns (excluding time columns)
        x_features = x[self._initial_x_variable_cols].copy() if self._initial_x_variable_cols else x.copy()
        y_features = y[self._initial_y_variable_cols].copy() if self._initial_y_variable_cols else y.copy()
        
        # Process X imputer
        if x_imputer is not None:
            # Create a fresh clone to avoid any pre-existing fit
            self._x_imputer_fitted = sklearn.base.clone(x_imputer) # type: ignore
            self._x_imputer_fitted.fit(x_features)
            # Apply imputation for subsequent scaling
            x_features = pd.DataFrame(
                self._x_imputer_fitted.transform(x_features),
                columns=x_features.columns,
                index=x_features.index
            )
        
        # Process X scaler
        if x_scaler is not None:
            # Create a fresh clone
            self._x_scaler_fitted = sklearn.base.clone(x_scaler) # type: ignore
            self._x_scaler_fitted.fit(x_features)
        
        # Process Y imputer - IMPORTANT: completely separate from X imputer
        if y_imputer is not None:
            # Create a fresh clone specifically for Y data
            self._y_imputer_fitted = sklearn.base.clone(y_imputer) # type: ignore
            self._y_imputer_fitted.fit(y_features)  # Fit on Y features only
            # Apply imputation for subsequent scaling
            y_features = pd.DataFrame(
                self._y_imputer_fitted.transform(y_features),
                columns=y_features.columns,
                index=y_features.index
            )
        
        # Process Y scaler
        if y_scaler is not None:
            self._y_scaler_fitted = sklearn.base.clone(y_scaler) # type: ignore
            self._y_scaler_fitted.fit(y_features)

    def transform(self, df_x: Optional[pd.DataFrame] = None, df_y: Optional[pd.DataFrame] = None, *, fill_missing_timesteps: bool = False) -> Tuple[npt.NDArray, npt.NDArray]:
        """
        Given dataframes, transform using the fitted imputers and scalers.
        The column order of arrays will conform to: 
            arr_x: timeset, timeset, var0...varN (as per x_col or y_col )
        
            
        Returns:
            Tuple of (arr_x, arr_y) as numpy arrays. If an input DataFrame is None,
            the corresponding output will be an empty numpy array with shape (0, n_features).
        """
        def process_dataframe(df:Optional[ pd.DataFrame ], is_x: bool) -> npt.NDArray:
            """
            Process a single DataFrame through validation and transformation.
            Returns an appropriately shaped numpy array.
            """
            # IF DATAFRAME IS NONE, RETURN EMPTY ARRAY WITH APPROPRIATE SHAPE
            if df is None:
                feature_cols = self._initial_x_variable_cols if is_x else self._initial_y_variable_cols
                n_features = len(feature_cols)

                if self.is_timeseries:
                    return np.zeros((0, 0, n_features))
                else:
                    return np.zeros((0, n_features))

            
            # DEFENSIVE
            # Validate dataframe columns and timesteps (if timeseries)
            is_valid_cols, cols_message = self._validate_df_cols(df, "x" if is_x else "y")
            is_valid_timesteps, timestep_message = self._validate_df_timesteps(df) if (self.is_timeseries and fill_missing_timesteps == False) else (True, "") 
            is_valid = is_valid_cols and is_valid_timesteps

            if not is_valid:
                error_messages = []
                if not is_valid_cols:
                    error_messages.append(cols_message)
                if not is_valid_timesteps:
                    error_messages.append(timestep_message)
                raise ValueError("\n".join(error_messages))
            
            # ASSEMBLE
            if is_x:
                cols = self.x_cols + ([self.timeset_col, self.timestep_col] if self.is_timeseries else [])
            else:
                cols = self.y_cols + ([self.timeset_col, self.timestep_col] if self.is_timeseries else [])
            
            # Apply transformations
            filtered_df = df[cols].copy()
            transformed_df = self._impute_and_scale(filtered_df, is_x=is_x)
            full_df= self._fill_missing_timesteps(transformed_df, is_x=is_x) if fill_missing_timesteps else transformed_df
            reordered_df = self._reorder_df(full_df, cols)
            array = self._reshape_df2array(reordered_df)
            
            return array
        
        # LOGIC
        arr_x = process_dataframe(df_x, True)
        arr_y= process_dataframe(df_y,False)
        return arr_x, arr_y
    
    def _fill_missing_timesteps(self, df: pd.DataFrame, is_x: bool = True, val = 0.0) -> pd.DataFrame:
        """
        Fill missing timesteps in timeseries data with a specified value. (default is 0.0)
        
        Not built for any impute.
        """
        if not self.is_timeseries:
            return df
        
        feature_cols = self.x_cols if is_x else self.y_cols
        timesets= df[self.timeset_col].unique()
        """
        # Create a complete grid with all timeset-timestep combinations
        
        For example, with 3 timesets (0,1,2) and 3 timesteps (0,1,2):
        
        Timeset Grid:
        0 0 0
        1 1 1
        2 2 2
        
        Timestep Grid:
        0 1 2
        0 1 2
        0 1 2
        
        This creates a complete grid of all combinations:
        Timeset  Timestep
            0        0
            0        1
            0        2
            1        0
            1        1
            1        2
            2        0
            2        1
            2        2
        """
        timeset_grid = np.repeat(timesets, len(self.timestep_values))
        timestep_grid = np.tile(self.timestep_values, len(timesets))
        df_completegrid = pd.DataFrame(
            {
            self.timeset_col: timeset_grid,
            self.timestep_col: timestep_grid,
            }
        )
        
        """
        # MERGE, FILL AND SORT

        EXAMPLE OF HOW DF_MERGED WORKS:
        Original dataframe might be missing some timestep entries
          timeset  timestep  feature1  feature2
               0         0      1.2      3.4
               0         2      2.1      5.6    # Note: timestep 1 is missing
               1         0      3.3      7.8
               1         1      4.5      8.9
        
        df_completegrid creates all possible timeset-timestep combinations
          timeset  timestep
               0         0
               0         1    # This combination will be added
               0         2
               1         0
               1         1
               1         2    # This combination will be added
        
        df_merged result after left join:
          timeset  timestep  feature1  feature2
               0         0      1.2      3.4
               0         1      NaN      NaN    # NaN values for missing timestep
               0         2      2.1      5.6
               1         0      3.3      7.8
               1         1      4.5      8.9
               1         2      NaN      NaN    # NaN values for missing timestep
        """
        df_merged = pd.merge(
            df_completegrid, df, on=[self.timeset_col, self.timestep_col], how="left"
        )
        for col in feature_cols:
            df_merged[col] = df_merged[col].fillna(val)

        df_merged = df_merged.sort_values(by=[self.timeset_col, self.timestep_col], ascending=[True, True])
        
        return df_merged

    
    def _reorder_df(self, df: pd.DataFrame, order: T.List[str]) -> pd.DataFrame:
        """
        Given a DF, ensures the columns follows the order of seed DF. 
        If there are any missing columns, raise an error.
        
            
        Returns:
            DataFrame with columns in the specified order
        """
        # Defensive: ensure col sets match
        if set(df.columns) != set(order):
            missing = set(order) - set(df.columns)
            extra = set(df.columns) - set(order)
            error_msg = f"Column mismatch: "
            if missing:
                error_msg += f"missing {missing}, "
            if extra:
                error_msg += f"extra {extra}, "
            error_msg += f"expected {order}"
            raise ValueError(error_msg)
        
        # Return DataFrame with columns in the specified order
        return df[order]

    def _impute_and_scale(self, df: pd.DataFrame, is_x: bool = True) -> pd.DataFrame:
        """
        Helper method to transform a DataFrame using fitted transformers.
        """
        # Select appropriate columns and transformers based on whether it's X or Y
        if is_x:
            var_cols = self._initial_x_variable_cols
            imputer = self._x_imputer_fitted
            scaler = self._x_scaler_fitted
        else:
            var_cols = self._initial_y_variable_cols
            imputer = self._y_imputer_fitted
            scaler = self._y_scaler_fitted
        
        # Extract feature columns (excluding time columns if present)
        features = df[var_cols].copy() if var_cols else df.copy()
        
        # Apply imputation if imputer was fitted
        if imputer is not None:
            features = pd.DataFrame(
                imputer.transform(features),
                columns=features.columns,
                index=features.index
            )
            
        # Apply scaling if scaler was fitted
        if scaler is not None:
            features = pd.DataFrame(
                scaler.transform(features),
                columns=features.columns,
                index=features.index
            )
        
        # Merge back with time columns if present
        if self._timeset_col is not None and self._timestep_col is not None:
            time_cols = df[[self._timeset_col, self._timestep_col]].copy()
            transformed_df = pd.concat([time_cols, features], axis=1)
        else:
            transformed_df = features
        
        return transformed_df


    def _validate_df_timesteps(self, df: pd.DataFrame) -> Tuple[bool, str]:
        """
        Validate that all timesets have complete timestep values matching the seed data.
        
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not self.is_timeseries:
            return (True, "Not Timeseries")

        
        errors = []
        # Check if required columns exist before proceeding
        if self.timestep_col not in df.columns:
            errors.append(f"Missing required time column: {self.timestep_col}")
            return (False, "\n".join(errors))
            
        if self.timeset_col not in df.columns:
            errors.append(f"Missing required time column: {self.timeset_col}")
            return (False, "\n".join(errors))

        # CHECK FOR UNEXPECTED OR MISSING TIMESTEP VALUES ACROSS THE DATASET
        observed_timesteps = set(df[self.timestep_col].unique())
        expected_timesteps = set(self.timestep_values)
        
        missing_timesteps = expected_timesteps - observed_timesteps
        unexpected_timesteps = observed_timesteps - expected_timesteps
        
        if missing_timesteps:
            errors.append(f"Missing timestep values: {missing_timesteps}")
        if unexpected_timesteps:
            errors.append(f"Unexpected timestep values: {unexpected_timesteps}")
        
        # CHECK EACH TIMESET HAS ALL EXPECTED TIMESTEPS
        # NOTE - this can be vectorized in future. Leaving as is for readability
        if not errors:  # Skip expensive check if we already have errors
            timeset_values = df[self.timeset_col].unique()
            
            for timeset in timeset_values:
                timeset_data = df[df[self.timeset_col] == timeset]
                timeset_timesteps = set(timeset_data[self.timestep_col].unique())
                
                missing_in_timeset = expected_timesteps - timeset_timesteps
                if missing_in_timeset:
                    errors.append(f"Timeset {timeset} is missing timesteps: {missing_in_timeset}")
        
            # CHECK EXPECTED ROW COUNT
            expected_rowcount = len(timeset_values) * len(self.timestep_values)
            actual_rowcount = len(df)
            
            if expected_rowcount != actual_rowcount:
                errors.append(f"Row count mismatch: expected {expected_rowcount}, got {actual_rowcount}")
        
        return (len(errors) == 0, "\n".join(errors) if errors else "Valid timeseries data")
    
    def _validate_df_cols(self, df: pd.DataFrame, x_or_y: Literal["x", "y"] = "x") -> Tuple[bool, str]:
        """
        Validate that all required feature columns are present in the DataFrame.
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Determine expected feature columns based on X or Y
        expected_cols = (self.x_cols if x_or_y == "x" else self.y_cols) + ([self.timeset_col, self.timestep_col] if self.is_timeseries else [])

        # Check for missing columns
        df_cols = set(df.columns)
        missing_cols = set(expected_cols) - df_cols
        
        if missing_cols:
            error_msg = f"Missing required feature columns for {x_or_y.upper()} data: {missing_cols}"
            return (False, error_msg)
        
        # All columns present
        return (True, f"All required {x_or_y.upper()} columns present")


    def _reshape_df2array(self, df: pd.DataFrame) -> npt.NDArray:
        """
        Given a dataframe, convert it to a numpy array.
        
        If there are timeset and timestep columns, reshape to a 3D array with dimensions
        [timeset, timestep, var values], where both timeset and timestep are ordered
        in ascending manner.
            
        Returns:
            NumPy array with appropriate shape (2D or 3D)
        """
        if self._timeset_col is not None and self._timestep_col is not None:
            # Ensure DF is sorted -> this ensures array index semantics
            df = df.sort_values(by=[self._timeset_col, self._timestep_col], ascending=[True, True])
            timesets_n = len(df[self._timeset_col].unique())
            timesteps_n = len(df[self._timestep_col].unique())
            
            # Determine feature columns (exclude time columns)
            time_cols = [self._timeset_col, self._timestep_col]
            feature_cols = [col for col in df.columns if col not in time_cols]
            
            # Create empty 3D array with dimensions [timeset, timestep, features]
            features_only_df = df[feature_cols]
            result = np.reshape(np.array(features_only_df), (timesets_n,timesteps_n,len(feature_cols) ) )

            return result
        else:
            # Standard data - simple 2D array conversion
            return df.values
        
    def inv_transform(self, data: npt.NDArray, is_x: bool = False) -> pd.DataFrame:
        """
        Given an array, run inverse transformations on it and reshape it back to a DataFrame.
        
        Args:
            data: NumPy array to inverse transform (can be 2D or 3D for time series)
            is_x: If True, treats as X data and applies X transformers; otherwise uses Y transformers
            
        Returns:
            DataFrame with inverse-transformed data
        
        Raises:
            ValueError: If the array shape doesn't match expected dimensions or if
                       validation fails
        """
        # First reshape the array to a DataFrame
        df = self._reshape_array2df(data, is_x)
        
        # DEFENSIVE - Use existing validation methods
        is_valid_cols, cols_message = self._validate_df_cols(df, "x" if is_x else "y")
        is_valid_time, time_message = self._validate_df_timesteps(df) if self.is_timeseries else (True, "")
        is_valid = is_valid_cols and is_valid_time

        if not is_valid:
            error_messages = []
            if not is_valid_cols:
                error_messages.append(cols_message)
            if not is_valid_time:
                error_messages.append(time_message)
            raise ValueError("Invalid data in inverse transform: " + "\n".join(error_messages))
        
        # Get appropriate scaler based on data type
        scaler = self._x_scaler_fitted if is_x else self._y_scaler_fitted
        
        # If no scaler is available, return the DataFrame as is
        if scaler is None:
            return df
        
        # Get variable columns for the appropriate data type
        var_cols = self._initial_x_variable_cols if is_x else self._initial_y_variable_cols
        
        try:
            # Handle time series and non-time series data differently
            if self.is_timeseries:
                # Extract time columns
                time_cols = [self._timeset_col, self._timestep_col]
                time_data = df[time_cols]
                
                # Extract feature columns and apply inverse scaling
                feature_data = df[var_cols]
                scaled_values = scaler.inverse_transform(feature_data)
                
                # Create scaled features DataFrame
                feature_data = pd.DataFrame(
                    scaled_values,
                    columns=var_cols,
                    index=feature_data.index
                )
                
                # Reconstruct complete DataFrame in one step
                return pd.concat([time_data, feature_data], axis=1)
            else:
                # For non-time series data, apply inverse scaling to all columns
                scaled_values = scaler.inverse_transform(df[var_cols])
                return pd.DataFrame(
                    scaled_values,
                    columns=var_cols,
                    index=df.index
                )
        except Exception as e:
            raise ValueError(f"Error applying inverse scaling: {e}")
    
    def _reshape_array2df(self, array: npt.NDArray, is_x: bool = False) -> pd.DataFrame:
        """
        Convert array to DataFrame with validation for time series completeness.
        
        For time series data, ensures proper 3D structure and uses original timestep values.
        Validates array dimensions match expected feature counts.
                
        Returns:
            DataFrame with appropriate columns
        
        Raises:
            ValueError: If array dimensions don't match expected structure
        """
        # Determine appropriate feature column names
        feature_cols = self.x_cols if is_x else self.y_cols
        
        if self.is_timeseries:
            # DEFENSIVE

            # Validate array structure
            if len(array.shape) != 3:
                raise ValueError(f"Expected 3D array for time series data, got shape {array.shape}")
            
            n_timesets, n_timesteps, n_features = array.shape
            
            # Validate feature count
            if len(feature_cols) != n_features:
                raise ValueError(
                    f"Feature dimension mismatch: array has {n_features} features but expected {len(feature_cols)}"
                )
            
            # Validate timestep count matches stored values
            if len(self.timestep_values) != n_timesteps:
                raise ValueError(
                    f"Timestep count mismatch: array has {n_timesteps} timesteps but expected {len(self.timestep_values)}"
                )
            
            # LOGIC

            features_2d = array.reshape(-1, n_features)
            feature_df = pd.DataFrame(features_2d, columns=feature_cols)
            
            # Create timestep column using actual stored values
            """
            Sample: for n_timesets=3, n_timesteps=2, timestep_values=[10, 20]:
            timestep_array = np.tile([10, 20], 3) = [10, 20, 10, 20, 10, 20]
            """
            timestep_array = np.tile(self.timestep_values, n_timesets)
            timestep_df = pd.DataFrame(timestep_array, columns=[self._timestep_col])
            
            # Create timeset column
            """
            Sample: for n_timesets=3, n_timesteps=2:
            timeset_array = np.repeat([0, 1, 2], 2) = [0, 0, 1, 1, 2, 2]
            """
            timeset_array = np.repeat(np.arange(n_timesets), n_timesteps)
            timeset_df = pd.DataFrame(timeset_array, columns=[self._timeset_col])
            
            # Combine all columns
            return pd.concat([timeset_df, timestep_df, feature_df], axis=1)
        else:
            # Standard data handling
            if len(array.shape) != 2:
                raise ValueError(f"Expected 2D array for standard data, got shape {array.shape}")
            
            _, n_features = array.shape
            if n_features != len(feature_cols):
                raise ValueError(
                    f"Feature dimension mismatch: array has {n_features} features but expected {len(feature_cols)}"
                )
            
            return pd.DataFrame(array, columns=feature_cols)
    
    def _reshape_array2df_old(self, array: npt.NDArray, is_x: bool = False) -> pd.DataFrame:
        """
        Given an array convert it to DataFrame.
        
        If there is timeset and timestep, shape should be [timeset, timestep, var values], where
        both timeset and timestep are ordered in ascending manner. This converts back to a DataFrame
        with timeset and timestep columns following the attribute column name.
        Column values will be simple monotonically increasing integers.
        
        Args:
            array: NumPy array to convert (2D or 3D)
            is_x: Whether this array represents X data (True) or Y data (False)
            
        Returns:
            DataFrame with appropriate columns
        """
        # Determine appropriate feature column names
        feature_cols = self.x_cols if is_x else self.y_cols
        
        if self.is_timeseries:
            # Defensive
            if len(array.shape) != 3:
                raise ValueError(f"Expected 3D array for time series data, got shape {array.shape}")
            
            n_timesets, n_timesteps, n_features = array.shape

            if not feature_cols or len(feature_cols) != n_features:
                feature_cols =[f"feature_{i}" for i in range(n_features)]

            # number of row+features
            array = array.reshape(-1,n_features)
            feature_array_df = pd.DataFrame(array, columns=feature_cols)
            
            # create array[0,1,....,n_timesteps, 0, 1, ..., n_timesteps(repeated n_timeset times)]
            timestep_array = np.tile(np.array([self.timestep_values[i] for i in range(n_timesteps)], dtype=np.float32), n_timesets)
            timestep_array_df = pd.DataFrame(timestep_array, columns=[self._timestep_col])

            # create array[0,0,....,repeated n_timesteps, 1, 1, ..., repeated n_timesteps(until n_timeset)]
            timeset_array = np.repeat(np.array([i for i in range(n_timesets)], dtype=np.float32), n_timesteps)
            timeset_array_df = pd.DataFrame(timeset_array, columns=[self._timeset_col])
            
            # Concatenated DataFrames along columns axis
            return pd.concat([timeset_array_df, timestep_array_df, feature_array_df], axis=1)
        else:
            if len(array.shape) != 2:
                raise ValueError(f"Expected 2D array for standard data, got shape {array.shape}")
            
            _, n_features = array.shape
            if n_features != len(feature_cols):
                raise ValueError(
                    f"Feature dimension mismatch: array has {n_features} features but expected {len(feature_cols)} "
                    f"columns ({feature_cols})"
                )
            
            # Create DataFrame from 2D array with column names
            return pd.DataFrame(array, columns=feature_cols)


    def serialize(self) -> Dict[str, Any]:
        """
        Serialize the transformer for persistence.
        
        Returns:
            Dictionary containing serialized representation of the transformer
            that can be stored in a database or file system.
        """
        
        # Serialize fitted transformers to base64-encoded binary
        def serialize_transformer(transformer):
            if transformer is None:
                return None
            buffer = io.BytesIO()
            joblib.dump(transformer, buffer)
            return base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return {
            'uid': str(self.uid),
            'timeset_col': self._timeset_col,
            'timestep_col': self._timestep_col,
            'initial_x_variable_cols': self._initial_x_variable_cols,
            'initial_y_variable_cols': self._initial_y_variable_cols,
            'random_state': self.random_state,
            'x_imputer': serialize_transformer(self._x_imputer_fitted),
            'y_imputer': serialize_transformer(self._y_imputer_fitted),
            'x_scaler': serialize_transformer(self._x_scaler_fitted),
            'y_scaler': serialize_transformer(self._y_scaler_fitted),
            'timestep_values': self._timestep_values
        }
    
    @classmethod
    def deserialize(cls, data: Dict[str, Any]) -> 'SurrogateDataTransformer':
        """
        Deserialize the transformer from stored data.
        
        Args:
            data: Dictionary containing the serialized transformer data
            
        Returns:
            Reconstructed SurrogateDataTransformer instance
        """
        
        # Create a minimal instance without initialization
        instance = cls.__new__(cls)
        
        # Deserialize transformer objects from base64
        def deserialize_transformer(serialized_data):
            if serialized_data is None:
                return None
            binary_data = base64.b64decode(serialized_data)
            buffer = io.BytesIO(binary_data)
            return joblib.load(buffer)
        
        # Restore simple attributes
        instance.uid = uuid.UUID(data['uid'])
        instance._timeset_col = data['timeset_col']
        instance._timestep_col = data['timestep_col']
        instance._timestep_values = data['timestep_values']
        instance._initial_x_variable_cols = data['initial_x_variable_cols']
        instance._initial_y_variable_cols = data['initial_y_variable_cols']
        instance.random_state = data['random_state']
        
        # Restore fitted transformers
        instance._x_imputer_fitted = deserialize_transformer(data['x_imputer'])
        instance._y_imputer_fitted = deserialize_transformer(data['y_imputer'])
        instance._x_scaler_fitted = deserialize_transformer(data['x_scaler'])
        instance._y_scaler_fitted = deserialize_transformer(data['y_scaler'])
        
        return instance


def surrogate_datatransformer_factory(
    is_timeseries: bool = False,
    timeset_col: Optional[str] = None,
    timestep_col: Optional[str] = None,
    df_x: Optional[pd.DataFrame] = None,
    df_y: Optional[pd.DataFrame] = None,
    x_cols: Optional[List[str]] = None,  # Only used if df_x is None
    y_cols: Optional[List[str]] = None,  # Only used if df_y is None
    random_seed: int = 42
) -> SurrogateDataTransformer:
    """
    Create a transformer for testing using either provided data or synthetic data.
    """
    # Generate synthetic data if not provided
    if df_x is None or df_y is None:
        if x_cols is None or y_cols is None:
            raise ValueError("Must provide either DataFrames or column specifications")
            
        # Generate synthetic data with specified column names
        n_samples = 50 if is_timeseries else 10
        np.random.seed(random_seed)
        
        # Create synthetic feature data
        synth_x_data = np.random.rand(n_samples, len(x_cols))
        synth_y_data = np.random.rand(n_samples, len(y_cols))
        
        # Create DataFrames
        df_x_synth = pd.DataFrame(synth_x_data, columns=x_cols)
        df_y_synth = pd.DataFrame(synth_y_data, columns=y_cols)
        
        # Add time columns if needed
        if is_timeseries:
            if timeset_col is None or timestep_col is None:
                raise ValueError("Time series columns must be specified")
                
            timesets = np.repeat(np.arange(5), 10)
            timesteps = np.tile(np.arange(10), 5)
            
            df_x_synth[timeset_col] = timesets
            df_x_synth[timestep_col] = timesteps
            df_y_synth[timeset_col] = timesets
            df_y_synth[timestep_col] = timesteps
    else:
        df_x_synth = None
        df_y_synth = None

    # Create the transformer
    return SurrogateDataTransformer(
        uid=uuid.uuid4(),
        df_x=df_x if df_x is not None else df_x_synth,
        df_y=df_y if df_y is not None else df_y_synth,
        timeset_col=timeset_col,
        timestep_col=timestep_col,
        x_imputer=None,
        y_imputer=None,
        x_scaler=None,
        y_scaler=None,
        random_state=random_seed
    )
