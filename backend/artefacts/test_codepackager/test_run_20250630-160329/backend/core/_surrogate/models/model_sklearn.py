from __future__ import annotations
from typing import Dict, List, Optional, Tuple, Any, Union, cast
import numpy as np
import pandas as pd

import sklearn.base
import sklearn.ensemble
import sklearn.linear_model

from ..valueobjects import VOMetadata_General
from ..models.model_base import BaseSurrogateModel


class SKLearnModel(BaseSurrogateModel):
    """
    Universal surrogate model wrapper for scikit-learn models.
    
    This class provides a unified interface for working with any scikit-learn model,
    handling predictions, feature importance, and serialization consistently
    regardless of the specific algorithm used.
    """
    
    def __init__(self, native_model: sklearn.base.BaseEstimator, metadata: Optional[VOMetadata_General] = None):
        """
        Initialize with a trained scikit-learn model and optional metadata.
        
        Args:
            native_model: Trained scikit-learn model
            metadata: Optional model metadata
        """
        super().__init__(native_model, metadata)
        self._feature_names: Optional[List[str]] = None
        self._model_type: str = ""
        
        # Extract feature names from model if available
        self._initialize_feature_names()
        
        # Determine model type
        self._model_type = self._determine_model_type()
    
    # PROPERTIES
    #---------------------------------------------------
    @property
    def model_type(self) -> str:
        """String identifier for model type"""
        return self._model_type
    
    @property
    def feature_names_in_(self) -> List[str]:
        """
        List of feature names expected by model
        
        Returns:
            List of feature names
            
        Raises:
            ValueError: If feature names are not available
        """
        if self._feature_names is None:
            raise ValueError("Feature names not available for this model")
        return list(self._feature_names)
    
    # INITIALIZATION METHODS
    #---------------------------------------------------
    def _initialize_feature_names(self) -> None:
        """Extract feature names from model if available"""
        try:
            # Try to get feature_names_in_ (sklearn 1.0+)
            if hasattr(self.native_model, "feature_names_in_"):
                self._feature_names = list(self.native_model.feature_names_in_)
        except Exception as e:
            # Log warning but continue - this is not critical
            print(f"Warning: Could not extract feature names from model: {str(e)}")
    
    def _determine_model_type(self) -> str:
        """
        Determine the type of scikit-learn model
        
        Returns:
            String identifier for the model type
        """
        model = self.native_model
        
        # Check for specific model types using explicit sklearn class references
        if isinstance(model, sklearn.ensemble.RandomForestRegressor):
            return "RandomForest"
        elif isinstance(model, sklearn.ensemble.GradientBoostingRegressor):
            return "GradientBoosting"
        elif isinstance(model, sklearn.linear_model.LinearRegression):
            return "LinearRegression"
        
        # Fall back to the class name for unknown models
        return model.__class__.__name__
    
    # PREDICTION METHODS
    #---------------------------------------------------
    def predict(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Make predictions with the model.
        
        Args:
            data: DataFrame containing features
            
        Returns:
            DataFrame with predictions
            
        Raises:
            ValueError: If input data doesn't match model requirements
            RuntimeError: If prediction fails for any reason
        """
        # Validate input
        self.validate_input(data)
        
        # Make predictions
        try:
            predictions = self.native_model.predict(data)
            
            # Handle different output shapes
            if predictions.ndim == 1:
                # Single output model - reshape to column vector
                predictions = predictions.reshape(-1, 1)
            
            # Create DataFrame with appropriate column names
            if self.output_names and len(self.output_names) == predictions.shape[1]:
                # Use stored output names if available and matching
                columns = self.output_names
            else:
                # Create generic column names
                columns = [f"target_{i}" for i in range(predictions.shape[1])]
            
            return pd.DataFrame(predictions, index=data.index, columns=columns)
        
        except Exception as e:
            # Add context to the error
            raise RuntimeError(f"Error making predictions with {self.model_type} model: {str(e)}") from e
    
    def predict_with_confidence(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, Optional[pd.DataFrame]]:
        """
        Make predictions with confidence intervals for models that support it.
        
        Args:
            data: DataFrame containing features
            
        Returns:
            Tuple of (predictions, confidence) DataFrames
        """
        # Make basic predictions
        predictions = self.predict(data)
        confidence: Optional[pd.DataFrame] = None
        
        # Get confidence intervals for models that support it
        if self.model_type == "RandomForest":
            try:
                # For RandomForest, we can get prediction std dev
                rf_model = cast(sklearn.ensemble.RandomForestRegressor, self.native_model)
                
                # Get individual predictions from all trees
                all_predictions = np.array(
                    [tree.predict(data) for tree in rf_model.estimators_]
                )
                
                # Calculate standard deviation
                std_dev = np.std(all_predictions, axis=0)
                
                # Create confidence DataFrame with same structure as predictions
                confidence = pd.DataFrame(
                    std_dev,
                    index=data.index,
                    columns=[f"{col}_std" for col in predictions.columns]
                )
            
            except Exception as e:
                # If confidence calculation fails, log warning but still return predictions
                print(f"Warning: Failed to compute confidence intervals: {str(e)}")
                confidence = None
        
        # Future: Add confidence calculation for other model types (e.g., gradient boosting)
                
        return predictions, confidence
    
    # MODEL INSPECTION METHODS
    #---------------------------------------------------
    def get_feature_importance(self) -> Dict[str, float]:
        """
        Get feature importance scores if available.
        
        Returns:
            Dictionary mapping feature names to importance scores
            
        Raises:
            ValueError: If feature importance is not available
        """
        try:
            model = self.native_model
            feature_names = self.feature_names_in_
            
            # Extract importance values based on model type
            if hasattr(model, "feature_importances_"):
                # Most ensemble models (RandomForest, GradientBoosting, etc.)
                importance_values = model.feature_importances_
            elif hasattr(model, "coef_"):
                # Linear models (sklearn.linear_model.LinearRegression, etc.)
                coef = model.coef_
                if coef.ndim > 1:
                    # For multi-output, take the mean importance across outputs
                    importance_values = np.abs(coef).mean(axis=0)
                else:
                    importance_values = np.abs(coef)
            else:
                # No feature importance available
                raise ValueError(f"Feature importance not available for {self.model_type} model")
            
            return self._normalize_importance_values(importance_values, feature_names)
            
        except Exception as e:
            if isinstance(e, ValueError):
                # Re-raise ValueError with same message
                raise
            # Convert other exceptions to ValueError with context
            raise ValueError(f"Failed to extract feature importance: {str(e)}") from e
