{"tid": "pkg-250630-ce82be", "timestamp": "2025-06-30 16:03:29.898336", "source_module": "backend.core._surrogate", "entrypoint_script": "test_train.py", "package_size_mb": 0.2720375061035156, "python_files_count": 30, "packaging_time_ms": 23.63872400019318, "directory_structure": ["backend/__init__.py", "backend/core/__init__.py", "backend/core/_surrogate/__init__.py", "backend/core/_surrogate/_enums.py", "backend/core/_surrogate/_imports.py", "backend/core/_surrogate/_utilities.py", "backend/core/_surrogate/entities.py", "backend/core/_surrogate/entrypoints/__init__.py", "backend/core/_surrogate/entrypoints/environment_seqmodel.yml", "backend/core/_surrogate/entrypoints/seqmodel.py", "backend/core/_surrogate/factories.py", "backend/core/_surrogate/models/__init__.py", "backend/core/_surrogate/models/model_base.py", "backend/core/_surrogate/models/model_sklearn.py", "backend/core/_surrogate/ports.py", "backend/core/_surrogate/trainers/__init__.py", "backend/core/_surrogate/trainers/_torchblocks.py", "backend/core/_surrogate/trainers/reference/surrogate_model_deeplearning/__init__.py", "backend/core/_surrogate/trainers/reference/surrogate_model_deeplearning/deeplearning_dataset.py", "backend/core/_surrogate/trainers/reference/surrogate_model_deeplearning/metrics.py", "backend/core/_surrogate/trainers/reference/surrogate_model_deeplearning/model_DNN.py", "backend/core/_surrogate/trainers/reference/surrogate_model_deeplearning/model_training.py", "backend/core/_surrogate/trainers/reference/surrogate_model_deeplearning/surrogate_model_final.py", "backend/core/_surrogate/trainers/trainer_base.py", "backend/core/_surrogate/trainers/trainer_registry.py", "backend/core/_surrogate/trainers/trainer_rf.py", "backend/core/_surrogate/trainers/trainer_rnn.py", "backend/core/_surrogate/transformers/__init__.py", "backend/core/_surrogate/transformers/base_transformer.py", "backend/core/_surrogate/valueobjects.py", "entrypoint.py", "environment.yml"]}