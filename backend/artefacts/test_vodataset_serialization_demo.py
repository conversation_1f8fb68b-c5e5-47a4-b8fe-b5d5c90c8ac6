#!/usr/bin/env python3
"""
Demonstration of VODataset serialization capabilities.

This script shows:
1. Basic serialization/deserialization
2. Compression for large arrays
3. Serialized data format inspection
4. Performance characteristics
"""

import json
import uuid
import numpy as np
import time
from datetime import datetime

from backend.core._surrogate.valueobjects import VODataset


def demo_basic_serialization():
    """Demonstrate basic serialization functionality."""
    print("=== Basic Serialization Demo ===")
    
    # Create a small dataset
    np.random.seed(42)
    arr_x = np.random.randn(10, 3).astype(np.float64)
    arr_y = np.random.randn(10).astype(np.float64)
    
    dataset = VODataset(
        arr_x=arr_x,
        arr_y=arr_y,
        transformer_uid=uuid.uuid4(),
        colnames_x=["feature_1", "feature_2", "feature_3"],
        colnames_y=["target"],
        pattern="tabular"
    )
    
    # Serialize
    json_data = dataset.model_dump(mode="json")
    json_str = json.dumps(json_data, indent=2)
    
    print(f"Original array shapes: x={arr_x.shape}, y={arr_y.shape}")
    print(f"JSON size: {len(json_str)} characters")
    print(f"Compression used: x={json_data['arr_x']['compressed']}, y={json_data['arr_y']['compressed']}")
    
    # Deserialize
    parsed_json = json.loads(json_str)
    restored_dataset = VODataset.model_validate(parsed_json)
    
    print(f"Restored array shapes: x={restored_dataset.arr_x.shape}, y={restored_dataset.arr_y.shape}")
    print(f"Data integrity: {np.allclose(arr_x, restored_dataset.arr_x) and np.allclose(arr_y, restored_dataset.arr_y)}")
    print()


def demo_compression():
    """Demonstrate compression for large arrays."""
    print("=== Compression Demo ===")
    
    # Create a large dataset that should trigger compression
    np.random.seed(42)
    large_x = np.random.randn(2000, 100).astype(np.float64)  # ~1.6MB
    large_y = np.random.randn(2000, 5).astype(np.float64)    # ~80KB
    
    dataset = VODataset(
        arr_x=large_x,
        arr_y=large_y,
        transformer_uid=uuid.uuid4(),
        colnames_x=[f"feature_{i}" for i in range(100)],
        colnames_y=[f"target_{i}" for i in range(5)],
        pattern="tabular"
    )
    
    # Serialize and measure
    start_time = time.time()
    json_data = dataset.model_dump(mode="json")
    serialization_time = time.time() - start_time
    
    json_str = json.dumps(json_data)
    
    print(f"Large array sizes: x={large_x.nbytes:,} bytes, y={large_y.nbytes:,} bytes")
    print(f"Total raw data: {large_x.nbytes + large_y.nbytes:,} bytes")
    print(f"JSON size: {len(json_str):,} characters")
    print(f"Compression used: x={json_data['arr_x']['compressed']}, y={json_data['arr_y']['compressed']}")
    print(f"Serialization time: {serialization_time:.3f} seconds")
    
    # Deserialize and verify
    start_time = time.time()
    parsed_json = json.loads(json_str)
    restored_dataset = VODataset.model_validate(parsed_json)
    deserialization_time = time.time() - start_time
    
    print(f"Deserialization time: {deserialization_time:.3f} seconds")
    print(f"Data integrity: {np.allclose(large_x, restored_dataset.arr_x) and np.allclose(large_y, restored_dataset.arr_y)}")
    print()


def demo_data_types():
    """Demonstrate different data type preservation."""
    print("=== Data Type Preservation Demo ===")
    
    # Test different data types
    dtypes_to_test = [np.float32, np.float64, np.int32, np.int64]
    
    for dtype in dtypes_to_test:
        np.random.seed(42)
        if dtype in [np.int32, np.int64]:
            arr_x = np.random.randint(0, 100, size=(20, 3)).astype(dtype)
            arr_y = np.random.randint(0, 10, size=(20,)).astype(dtype)
        else:
            arr_x = np.random.randn(20, 3).astype(dtype)
            arr_y = np.random.randn(20).astype(dtype)
        
        dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=uuid.uuid4(),
            colnames_x=["x1", "x2", "x3"],
            colnames_y=["y1"],
            pattern="tabular"
        )
        
        # Round-trip
        json_data = dataset.model_dump(mode="json")
        restored_dataset = VODataset.model_validate(json_data)
        
        print(f"Data type {dtype.__name__}:")
        print(f"  Original: x={arr_x.dtype}, y={arr_y.dtype}")
        print(f"  Restored: x={restored_dataset.arr_x.dtype}, y={restored_dataset.arr_y.dtype}")
        print(f"  Integrity: {np.array_equal(arr_x, restored_dataset.arr_x) and np.array_equal(arr_y, restored_dataset.arr_y)}")
    
    print()


def demo_special_values():
    """Demonstrate handling of special float values."""
    print("=== Special Values Demo ===")
    
    # Create arrays with special values
    arr_x = np.array([
        [1.0, np.nan, 3.0],
        [np.inf, 5.0, -np.inf],
        [7.0, 8.0, 9.0]
    ], dtype=np.float64)
    
    arr_y = np.array([np.nan, np.inf, -np.inf], dtype=np.float64)
    
    dataset = VODataset(
        arr_x=arr_x,
        arr_y=arr_y,
        transformer_uid=uuid.uuid4(),
        colnames_x=["x1", "x2", "x3"],
        colnames_y=["y1"],
        pattern="tabular"
    )
    
    # Round-trip
    json_data = dataset.model_dump(mode="json")
    restored_dataset = VODataset.model_validate(json_data)
    
    print("Original special values:")
    print(f"  x[0,1] = {arr_x[0,1]} (NaN)")
    print(f"  x[1,0] = {arr_x[1,0]} (Inf)")
    print(f"  x[1,2] = {arr_x[1,2]} (-Inf)")
    print(f"  y[0] = {arr_y[0]} (NaN)")
    
    print("Restored special values:")
    print(f"  x[0,1] = {restored_dataset.arr_x[0,1]} (NaN: {np.isnan(restored_dataset.arr_x[0,1])})")
    print(f"  x[1,0] = {restored_dataset.arr_x[1,0]} (Inf: {np.isinf(restored_dataset.arr_x[1,0])})")
    print(f"  x[1,2] = {restored_dataset.arr_x[1,2]} (-Inf: {np.isneginf(restored_dataset.arr_x[1,2])})")
    print(f"  y[0] = {restored_dataset.arr_y[0]} (NaN: {np.isnan(restored_dataset.arr_y[0])})")
    print()


if __name__ == "__main__":
    print(f"VODataset Serialization Demonstration")
    print(f"Generated at: {datetime.now().isoformat()}")
    print("=" * 60)
    print()
    
    demo_basic_serialization()
    demo_compression()
    demo_data_types()
    demo_special_values()
    
    print("=" * 60)
    print("All demonstrations completed successfully!")
    print("The VODataset serialization implementation supports:")
    print("✓ JSON round-trip serialization/deserialization")
    print("✓ Automatic compression for large arrays (>1MB)")
    print("✓ Preservation of array shapes and data types")
    print("✓ Handling of special float values (NaN, Inf, -Inf)")
    print("✓ Both tabular and sequence data patterns")
    print("✓ High performance for typical dataset sizes")
