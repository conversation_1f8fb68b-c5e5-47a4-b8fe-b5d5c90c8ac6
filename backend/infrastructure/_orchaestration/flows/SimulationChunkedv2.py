import os

# from dotenv import load_dotenv
import pandas as pd
from prefect import flow, task
from typing import Dict, Any, List, Optional
from collections import defaultdict


from backend.infrastructure._orchaestration.flows.BasePrefectFlow import BasePrefectFlow
from backend.infrastructure._orchaestration.utils._enums import PrefectBlock
import backend.core._matrix.matrix_dwsim as ma
from backend.infrastructure._orchaestration.utils.load_prefect_blocks import (
    get_azure_blob_container,
)
from backend.infrastructure._db.dto import AtlasJSONSerializer
import backend.core as core

# TODO @ZL Fix hardcoded worker image -> Schema is too heavy and results in circular imports if PrefectConfig is imported here
azure_registry = os.getenv("PREFECT_AZ_CONTAINER_REGISTRY", "")
aci_worker_image = os.getenv("PREFECT_AZ_ACI_WORKER_IMAGE", "")

simulation_block_name = PrefectBlock.SimulationBlobStorage.value

SIMULATION_FLOW_NAME = "simulation-multi-kpis"


@task(
    log_prints=True,
    persist_result=True,
    result_storage_key="{flow_run.id}-{parameters[simulation_id]}",
)
def sim_task(
    simulation_id: str, atlas_json: str, kpi_names: List[str], sample_dict: Dict[str, Any]
) -> Dict[str, List[float]]:
    kpi_results = defaultdict(list)

    atlas = AtlasJSONSerializer.deserialize(data=atlas_json)
    matrix = ma.MatrixDWSim(simulation_label=simulation_id)
    sample_df = pd.DataFrame(sample_dict)

    # Get KPI object
    kpi_objects = []
    for kpi_name in kpi_names: 
        kpi_uid = atlas.kpi_collection.get_uid(label=kpi_name)
        kpi_object = atlas.kpi_collection.get_item(kpi_uid)
        kpi_objects.append(kpi_object)

    # Call Protocol methods
    matrix.attach_model(domain_model=atlas, share_model_state=True)
    matrix.setup_matrix()

    # Loop through samples and run simulation
    print(f"Starting to run {len(sample_df)} simulations...")

    for row in range(len(sample_df)):
        print()
        print(f"Running simulation {row + 1}")
        print(f"Sample = {sample_df.iloc[[row]].to_dict('records')}")

        atlas.deserialize_values(data=sample_df, row_index=row)
        matrix.run_matrix()
        for kpi in kpi_objects: 
            try:
                kpi_results[kpi.label].append(kpi.get_kpi_value())
            except Exception as e:
                raise core.MatrixResponseError(sample_df.iloc[row], kpi.label, e)
        print(f"Simulation {row + 1} finished running, KPI result : {kpi_results}")
        print()

    print(f"KPI results = {kpi_results}")

    return kpi_results


@flow(
    name=SIMULATION_FLOW_NAME,
    log_prints=True,
    result_serializer="json",
    persist_result=True,
    result_storage=get_azure_blob_container(simulation_block_name),
)
def sim_flow(
    simulation_id: str, atlas_json: str, kpi_names: List[str], sample_dict: Dict[str, Any]
) -> Dict[str, List[float]]:
    return sim_task(
        simulation_id=simulation_id,
        atlas_json=atlas_json,
        kpi_names=kpi_names,
        sample_dict=sample_dict,
    )


class SimulationChunked(BasePrefectFlow):
    flow_name = SIMULATION_FLOW_NAME
    deployment_name = "simulation-chunked-deployment"
    work_pool_name = "simulation"
    image = f"{azure_registry}/{aci_worker_image}"
    tags = ["simulation"]
    flow = sim_flow


if __name__ == "__main__":
    SimulationChunked.deploy()
