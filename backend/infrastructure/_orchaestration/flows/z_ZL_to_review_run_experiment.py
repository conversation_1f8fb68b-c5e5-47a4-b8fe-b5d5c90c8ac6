# from backend.infrastructure.orchaestration.flows.TestFlow import run_deployments
# from backend.infrastructure.orchaestration.flows.SimulationLocal import run_deployments
from backend.endpoint.v1.schema import *
import logging
from backend.tests.unit.endpoints.z_test_experiment import experiment_config
from backend.tests.unit.endpoints.z_test_diagnosis import diagnosis_config

# run_deployments()


def test_experiment():
    # Build WorkflowManager object using the Experiment Config
    test_wf_manager = experiment_config.bootstrap_wf_manager()

    # Get SummaryExperiment object.
    smr_exp_results = test_wf_manager.run_experiment()

    # Generate Experiment Results object
    exp_results = ExperimentResults.hydrate_schema(
        ent_settings=experiment_config.entity_settings,
        kpi_spec=experiment_config.sim_kpi,
        results=smr_exp_results,
    )

    # Dump exp_results to JSON
    exp_results_json = exp_results.model_dump_json()
    with open("test_experiment.json", "w") as jdf:
        jdf.write(exp_results_json)
    assert isinstance(exp_results, ExperimentResults)
    logging.info(f"Experiment Results object is hydrated {exp_results_json}")


def test_diagnosis():
    # Build WorkflowManager object using the Experiment Config
    test_wf_manager = diagnosis_config.bootstrap_wf_manager()

    # Get SummaryExperiment object.
    atlas, smr_trbl_results, smr_trbl_impact_results = test_wf_manager.run_diagnosis()

    # Generate Experiment Results object
    dgn_results = DiagnosticResults.hydrate_schema(
        trbl_ent_settings=diagnosis_config.entity_settings,
        trbl_kpi_spec=diagnosis_config.sim_kpi,
        wf_manager=test_wf_manager,
        results=smr_trbl_results,
        impact_results=smr_trbl_impact_results,
        atlas=atlas,
    )

    # Dump exp_results to JSON
    dgn_results_json = dgn_results.model_dump_json()
    with open("test_diagnosis.json", "w") as jdf:
        jdf.write(dgn_results_json)
    assert isinstance(dgn_results, DiagnosticResults)
    logging.info(f"Experiment Results object is hydrated {dgn_results_json}")


if __name__ == "__main__":
    logging.basicConfig(level=logging.DEBUG)
    # test_experiment()
    test_diagnosis()
