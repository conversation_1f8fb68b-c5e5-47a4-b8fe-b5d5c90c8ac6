import logging
import os
from dotenv import load_dotenv
import pandas as pd
from prefect import flow, task

from backend.infrastructure._orchaestration.flows.BasePrefectFlow import BasePrefectFlow

from backend.infrastructure._orchaestration.utils._enums import PrefectBlock
import backend.core._matrix.matrix_dwsim as ma

from backend.infrastructure._orchaestration.utils.load_prefect_blocks import (
    get_azure_blob_container,
)
import backend.core._atlas.aggregates as at
import backend.application.usecase_templates as eg


# from backend.core.metis.WorkflowManager import WorkflowManager
# from backend.tests.unit.endpoints.test_experiment import experiment_config
from typing import Dict, Any, List, Optional


load_dotenv()

azure_registry = os.getenv("AZ_CONTAINER_REGISTRY")
aci_worker_image = os.getenv("AZ_ACI_WORKER_IMAGE")
simulation_block_name = PrefectBlock.SimulationBlobStorage.value

SIMULATION_FLOW_NAME = "simulation-flow-test"


def _convert_in_var_names_to_uidstr(
    in_var_names: List[str], atlas: at.AtlasRoot, delim: str = "_"
) -> List[str]:
    """
    Converts in_var_names to uids of variable, which comes fom EquipmentSpecifiction
    Args:
        in_var_names (list): A list of strings representing variable names. "Fuel Flow_Temperature" "Fuel Flow_MassFraciton Methane" Compound_Mix
        atlas (object): Instance of atlas object.
        delim (str): Deliminator to be used for splitting variable names.

    Returns:
        in_var_name_uid (list): A list of uid strings representing variable names.
    """
    remap = {}
    in_var_name_uids = []

    for name in in_var_names:
        entity_label, var_stringify = [term.strip() for term in name.split(delim)]
        var: Optional[at.VOBaseVariable] = None
        logging.info(
            f"The variable {name} has entity label: {entity_label} and variable ui label: {var_stringify}."
        )
        # Go through all the variables from the collection to get specific item
        for var in atlas.variables_collection.items:
            if (
                var.parent.label == entity_label
                and var_stringify == atlas.variables_collection.get_ui_label(var)
            ):
                var = var
                logging.info(f"Variable object is obtained successfully.")
                break
        # If variable is not present in the collection then it will raise key error.
        if var is None:
            raise KeyError(
                f"{name},eqp:`{entity_label}`,var:`{var_stringify}` not found in variables. Ensure the variable name is correctly specified."
            )

        # Build the list of uids corresponding to the pretty name.
        logging.info(f"The variable {name} has uid: {str(var.uid)}.")
        in_var_name_uids.append(str(var.uid))

    logging.info(
        f"The specified variable pretty names are converted to uids {in_var_name_uids}."
    )
    return in_var_name_uids


@task(
    log_prints=True,
    persist_result=True,
    result_storage_key="{flow_run.id}-{parameters[simulation_id]}",
    retries=5,
    retry_delay_seconds=60,
)
def sim_task(
    simulation_id: str,
    kpi_name: str,
    sample_dict: Dict[str, Any],
):
    # atlas = WorkflowManager._get_atlas("")
    # test_wf_manager = experiment_config.bootstrap_wf_manager()
    atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
    # kpi_name = test_wf_manager.ps.output_var_names[0]
    # simulation_id = test_wf_manager.sim_id

    renamed_samples = pd.DataFrame(sample_dict)
    all_in_var_names = list(renamed_samples.columns.values)
    print("in_var all_in_var_names = ")
    print(all_in_var_names)

    all_in_var_name_uids = _convert_in_var_names_to_uidstr(
        in_var_names=all_in_var_names, atlas=atlas
    )
    # Replace variable names of columns in samples dataframe with uids
    in_var_name_map = dict(zip(all_in_var_names, all_in_var_name_uids))
    print("in_var mapping = ")
    print(in_var_name_map)

    renamed_samples = renamed_samples.rename(mapper=in_var_name_map, axis=1)
    # all_in_var_names = (
    #     test_wf_manager.ps.input_var_names + test_wf_manager.ps.fixed_input_var_names
    # )
    # all_in_var_name_uids = test_wf_manager._convert_in_var_names_to_uidstr(
    #     in_var_names=all_in_var_names, atlas=atlas
    # )
    # in_var_name_map = dict(zip(all_in_var_names, all_in_var_name_uids))
    # print(f"in_var_name_map = {in_var_name_map}")
    # renamed_samples = samples.rename(mapper=sample_mapper, axis=1)

    print(f"Running Simulation for sample = ")
    print(renamed_samples)
    print(f"Sample columns = ")
    print(renamed_samples.columns)
    # Initialize Atlas and Matrix objects
    # atlas = AtlasJSONSerializer.deserialize(data=atlas_json)

    print("Creating Matrix...")
    matrix = ma.MatrixDWSim(simulation_label=simulation_id)
    print("Matrix created!")

    # Get KPI object
    kpi_uid = atlas.kpi_collection.get_uid(label=kpi_name)
    kpi_object = atlas.kpi_collection.get_item(kpi_uid)

    # Call Protocol methods
    matrix.attach_model(domain_model=atlas, share_model_state=True)
    matrix.setup_matrix()
    atlas.deserialize_values(data=renamed_samples)

    print("Running simulation...")
    matrix.run_matrix()
    print("Simulation finished running!")

    kpi_result = kpi_object.get_kpi_value()
    print(f"KPI Result = {kpi_result}")
    return kpi_result


@flow(
    name=SIMULATION_FLOW_NAME,
    log_prints=True,
    result_serializer="json",
    persist_result=True,
    result_storage=get_azure_blob_container(simulation_block_name),
    retries=5,
    retry_delay_seconds=60,
)
def sim_flow(
    simulation_id: str,
    kpi_name: str,
    sample_dict: Dict[str, Any],
):
    return sim_task(
        simulation_id,
        kpi_name,
        sample_dict,
    )


class SimulationLocal(BasePrefectFlow):
    flow_name = SIMULATION_FLOW_NAME
    deployment_name = "test-simulation-deployment"
    work_pool_name = "simulation"
    image = f"{azure_registry}/{aci_worker_image}"
    tags = ["test-simulation"]
    flow = sim_flow


def deploy():
    SimulationLocal.deploy()


# def run_deployments():
#     test_wf_manager = experiment_config.bootstrap_wf_manager()
#     kpi_name = test_wf_manager.ps.output_var_names[0]
#     simulation_id = test_wf_manager.sim_id

#     # Generate samples
#     samples = test_wf_manager.ps.generate_samples
#     num_samples = min(40, len(samples))
#     print(f"Generated samples for {num_samples} simulations...")
#     # samples = samples.iloc[:, :10]
#     print(f"# of variables = {samples.shape}")

#     # Run simulations
#     params_list = [
#         dict(
#             simulation_id=simulation_id,
#             kpi_name=kpi_name,
#             sample_dict=samples.iloc[[i]].reset_index(drop=True).to_dict(),
#         )
#         for i in range(num_samples)
#     ]
#     print("Parameters list = ")
#     print(params_list)

#     # Run deployments
#     results = asyncio.run(SimulationLocal.run_deployments(params_list))
#     print(results)
#     return results


if __name__ == "__main__":
    SimulationLocal.deploy()
