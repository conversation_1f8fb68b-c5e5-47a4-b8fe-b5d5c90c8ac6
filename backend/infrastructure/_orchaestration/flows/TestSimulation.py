import os
from dotenv import load_dotenv
import pandas as pd
from prefect import flow, task

from backend.infrastructure._orchaestration.flows.BasePrefectFlow import BasePrefectFlow
from backend.infrastructure._orchaestration.utils._enums import PrefectBlock
import backend.core._matrix.matrix_dwsim as ma
from backend.infrastructure._orchaestration.utils.load_prefect_blocks import (
    get_azure_blob_container,
)
import backend.infrastructure._db.repo as re

from backend.application.usecase_templates import industrial_chiller


repo = re.FolderRepoForDWSim("backend/artefacts/bin/dwsim", "aggregate_tests")

load_dotenv()

azure_registry = os.getenv("AZ_CONTAINER_REGISTRY")
aci_worker_image = os.getenv("AZ_ACI_WORKER_IMAGE")
simulation_block_name = PrefectBlock.SimulationBlobStorage.value

SIMULATION_FLOW_NAME = "test-simulation-flow"


@task(
    log_prints=True,
    persist_result=True,
    result_storage_key="{flow_run.id}-{parameters[run]}",
)
def sim_task(run: int):
    atlas = industrial_chiller()
    matrix = ma.MatrixDWSim("industrial_chiller_example")

    # Call Protocol methods

    matrix.attach_model(domain_model=atlas, share_model_state=True)
    matrix.setup_matrix()
    new_atlas = matrix.run_matrix()

    # Save to check

    repo.save(matrix)
    print("Ran with no problems")
    print(new_atlas)
    df = pd.DataFrame({"run": [run]})
    return new_atlas


@flow(
    name=SIMULATION_FLOW_NAME,
    log_prints=True,
    result_serializer="pickle",
    persist_result=True,
    result_storage=get_azure_blob_container(simulation_block_name),
)
def sim_flow(run: int):
    return sim_task(run=run)


class TestSimulation(BasePrefectFlow):
    flow_name = SIMULATION_FLOW_NAME
    deployment_name = "test-simulation-deployment"
    work_pool_name = "simulation"
    image = f"{azure_registry}/{aci_worker_image}"
    tags = ["test-simulation"]
    flow = sim_flow


if __name__ == "__main__":
    TestSimulation.deploy()
