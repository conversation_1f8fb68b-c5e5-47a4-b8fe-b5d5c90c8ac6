from .._imports import *

from prefect.deployments.deployments import run_deployment
from prefect.flow_runs import wait_for_flow_run
from prefect.flows import Flow
from prefect.states import Completed


class BasePrefectFlow(ABC):
    flow_name: str
    deployment_name: str
    work_pool_name: str
    image: str
    tags: List[str] = []
    flow: Flow
    push_image_on_deploy: bool = False
    build_image_on_deploy: bool = False

    @classmethod
    async def deploy(cls):
        """Create deployment for flow

        Flow.deploy() is called here.
        See: https://orion-docs.prefect.io/latest/api-ref/prefect/flows/#prefect.flows.Flow.deploy

        To customize with more granularity over Flow.deploy(), you can override this method
        """

        await cls.flow.deploy(
            name=cls.deployment_name,
            work_pool_name=cls.work_pool_name,
            image=cls.image,
            push=cls.push_image_on_deploy,
            build=cls.build_image_on_deploy,
            tags=cls.tags,
        )

    @classmethod
    async def _run(cls, params: Dict[str, Any]):
        """Generates a flow run from a deployment"""

        # Ref: https://docs-2.prefect.io/latest/api-ref/prefect/flow_runs/
        return await run_deployment(
            name=f"{cls.flow_name}/{cls.deployment_name}",
            parameters=params,
            timeout=0,
        )

    @classmethod
    async def run_deployments(cls, params_list: List[Dict[str, Any]]):
        """Runs deployments given a list of parameters"""

        assert len(params_list) > 0

        # Ref: https://docs-2.prefect.io/latest/api-ref/prefect/flow_runs/
        flow_runs = [await cls._run(params) for params in params_list]
        finished_flow_runs = [
            await wait_for_flow_run(flow_run_id=flow_run.id, timeout=1200)
            for flow_run in flow_runs
        ]
        # finished_flow_runs = await asyncio.gather(*coros)
        results = []
        failed_ids = []
        for fr in finished_flow_runs:
            logging.info("------------------------------------------")
            logging.info(f"Flow run: {fr.id}")
            logging.info(fr)
            logging.info(f"State: {fr.state}")
            logging.info(fr.state.is_completed())
            if fr.state.is_completed():
                results.extend(await fr.state.result().get())
                logging.info(results)
                continue
            failed_ids.append(str(fr.id))

        if failed_ids:
            logging.error(f"Flow runs {failed_ids} failed to run. ")
            raise core.MatrixSimulationRunError(pd.Series([]), Exception(f"Flow runs {failed_ids} failed to run."))

        return results
