import os
from dotenv import load_dotenv
import pandas as pd
from prefect import flow, task
import random
from typing import Dict, Any, List, Optional


from backend.infrastructure._orchaestration.flows.BasePrefectFlow import BasePrefectFlow
from backend.infrastructure._orchaestration.utils._enums import PrefectBlock
import backend.core._matrix.matrix_dwsim as ma
from backend.infrastructure._orchaestration.utils.load_prefect_blocks import (
    get_azure_blob_container,
)
import backend.infrastructure._db.repo as re

from backend.application.usecase_templates import (
    industrial_natural_gas_boiler_with_preheating_trains,
)


repo = re.FolderRepoForDWSim("backend/artefacts/bin/dwsim", "aggregate_tests")

load_dotenv()

azure_registry = os.getenv("AZ_CONTAINER_REGISTRY")
aci_worker_image = os.getenv("AZ_ACI_WORKER_IMAGE")
simulation_block_name = PrefectBlock.SimulationBlobStorage.value

FLOW_NAME = "test-complex-flow"


@task(
    log_prints=True,
    persist_result=True,
    result_storage_key="{flow_run.id}-{parameters[simulation_id]}",
)
def sim_task(simulation_id: str, kpi_name: str, sample_dict: Dict[str, Any]):
    atlas = industrial_natural_gas_boiler_with_preheating_trains()
    matrix = ma.MatrixDWSim(
        "industrial_natural_gas_boiler_with_preheating_trains_example"
    )

    # Call Protocol methods

    matrix.attach_model(domain_model=atlas, share_model_state=True)
    matrix.setup_matrix()
    new_atlas = matrix.run_matrix()

    # Save to check

    repo.save(matrix)
    print("Ran with no problems")
    kpi = random.randrange(200, 220, 1)
    print(f"KPI generated = {kpi}")
    return kpi


@flow(
    name=FLOW_NAME,
    log_prints=True,
    result_serializer="json",
    persist_result=True,
    result_storage=get_azure_blob_container(simulation_block_name),
)
def sim_flow(simulation_id: str, kpi_name: str, sample_dict: Dict[str, Any]):
    return sim_task(
        simulation_id=simulation_id,
        kpi_name=kpi_name,
        sample_dict=sample_dict,
    )


class TestComplex(BasePrefectFlow):
    flow_name = FLOW_NAME
    deployment_name = "test-complex-deployment"
    work_pool_name = "simulation"
    image = f"{azure_registry}/{aci_worker_image}"
    tags = ["test-simulation", "complex"]
    flow = sim_flow


if __name__ == "__main__":
    TestComplex.deploy()
