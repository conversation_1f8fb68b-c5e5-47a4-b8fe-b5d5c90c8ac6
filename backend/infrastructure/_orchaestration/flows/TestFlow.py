import os
from dotenv import load_dotenv
import pandas as pd
from prefect import flow, task

from backend.infrastructure._orchaestration.flows.BasePrefectFlow import BasePrefectFlow
from backend.infrastructure._orchaestration.utils._enums import PrefectBlock
from backend.infrastructure._orchaestration.utils.load_prefect_blocks import (
    get_azure_blob_container,
)
import asyncio

# TODO @ZL Fix hardcoded worker image -> Schema is too heavy and results in circular imports if PrefectConfig is imported here
azure_registry = os.getenv("PREFECT_AZ_CONTAINER_REGISTRY", "")
aci_worker_image = os.getenv("PREFECT_AZ_ACI_WORKER_IMAGE", "")

simulation_block_name = PrefectBlock.SimulationBlobStorage.value


FLOW_NAME = "test-flow"


@task(
    log_prints=True,
    persist_result=True,
    result_storage_key="{flow_run.id}-{parameters[run]}",
)
def sim_task(run: int):
    result = f"Test run {run} ran with no issues."
    df = pd.DataFrame({"run": [run]})
    print(result)
    return df


@flow(
    name=FLOW_NAME,
    log_prints=True,
    result_serializer="pickle",
    persist_result=True,
    result_storage=get_azure_blob_container(simulation_block_name),
)
def test_flow(run: int):
    return sim_task(run=run)


class TestFlow(BasePrefectFlow):
    flow_name = FLOW_NAME
    deployment_name = "test-deployment"
    work_pool_name = "simulation"
    image = f"{azure_registry}/{aci_worker_image}"
    tags = ["test"]
    flow = test_flow


def run_deployments():
    num_samples = 2

    # Run simulations
    params_list = [dict(run=i) for i in range(num_samples)]
    print("Parameters list = ")
    print(params_list)

    # Run deployments
    results = asyncio.run(TestFlow.run_deployments(params_list))
    print(results)
    return results


if __name__ == "__main__":
    TestFlow.deploy()
