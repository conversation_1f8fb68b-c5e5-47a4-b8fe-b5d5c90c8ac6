import os

# from dotenv import load_dotenv
import pandas as pd
from prefect import flow, task
from typing import Dict, Any, List, Optional


from backend.infrastructure._orchaestration.flows.BasePrefectFlow import BasePrefectFlow
from backend.infrastructure._orchaestration.utils._enums import PrefectBlock
import backend.core._matrix.matrix_dwsim as ma
from backend.infrastructure._orchaestration.utils.load_prefect_blocks import (
    get_azure_blob_container,
)
from backend.infrastructure._db.dto import AtlasJSONSerializer

# TODO @ZL Fix hardcoded worker image -> Schema is too heavy and results in circular imports if PrefectConfig is imported here
azure_registry = os.getenv("PREFECT_AZ_CONTAINER_REGISTRY", "")
aci_worker_image = os.getenv("PREFECT_AZ_ACI_WORKER_IMAGE", "")

simulation_block_name = PrefectBlock.SimulationBlobStorage.value

SIMULATION_FLOW_NAME = "simulation-flow"

@task(
    log_prints=True,
    persist_result=True,
    result_storage_key="{flow_run.id}-{parameters[simulation_id]}",
)
def sim_task(
    simulation_id: str, atlas_json: str, kpi_name: str, sample_dict: Dict[str, Any]
):
    atlas = AtlasJSONSerializer.deserialize(data=atlas_json)
    matrix = ma.MatrixDWSim(simulation_label=simulation_id)
    sample_df = pd.DataFrame(sample_dict)

    # Get KPI object
    kpi_uid = atlas.kpi_collection.get_uid(label=kpi_name)
    kpi_object = atlas.kpi_collection.get_item(kpi_uid)

    # Call Protocol methods
    matrix.attach_model(domain_model=atlas, share_model_state=True)
    matrix.setup_matrix()
    atlas.deserialize_values(data=sample_df)
    matrix.run_matrix()

    print(f"KPI result = {kpi_object.get_kpi_value()}")

    return kpi_object.get_kpi_value()


@flow(
    name=SIMULATION_FLOW_NAME,
    log_prints=True,
    result_serializer="json",
    persist_result=True,
    result_storage=get_azure_blob_container(simulation_block_name),
)
def sim_flow(
    simulation_id: str, atlas_json: str, kpi_name: str, sample_dict: Dict[str, Any]
):
    return sim_task(
        simulation_id=simulation_id,
        atlas_json=atlas_json,
        kpi_name=kpi_name,
        sample_dict=sample_dict,
    )


class Simulation(BasePrefectFlow):
    flow_name = SIMULATION_FLOW_NAME
    deployment_name = "simulation-deployment"
    work_pool_name = "simulation"
    image = f"{azure_registry}/{aci_worker_image}"
    tags = ["simulation"]
    flow = sim_flow


if __name__ == "__main__":
    Simulation.deploy()
