#!/bin/bash
set -e

# Set timezone to Singapore
export TZ="Asia/Singapore"

# Create backups directory if it doesn't exist
mkdir -p /backups

while true; do
    BACKUP_FILE="/backups/prefect_$(date +%Y%m%d_%H%M%S).sql"
    echo "Creating backup: $BACKUP_FILE"
    
    # Wait for postgres to be ready
    until PGPASSWORD=$POSTGRES_PASSWORD pg_isready -h $POSTGRES_HOST -U $POSTGRES_USER; do
        echo "Waiting for postgres..."
        sleep 5
    done
    
    # Create the backup
    PGPASSWORD=$POSTGRES_PASSWORD pg_dump -h $POSTGRES_HOST -U $POSTGRES_USER $POSTGRES_DB > $BACKUP_FILE
    
    # Keep only last 7 days of backups
    echo "Cleaning up old backups..."
    find /backups -type f -name '*.sql' -mtime +7 -exec rm -v {} \;   

    # List remaining files
    echo "Files after cleanup:"
    ls -lh /backups
    
    # Wait 1 hour before next backup
    sleep 3600
done