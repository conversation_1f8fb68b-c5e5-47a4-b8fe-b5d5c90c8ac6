from .._imports import *

# import asyncio
# from dotenv import load_dotenv
# import os
from prefect_azure import AzureBlobStorageCredentials
from prefect_azure.blob_storage import AzureBlobStorageContainer
from backend.infrastructure._orchaestration.utils._enums import PrefectBlock


load_dotenv()


async def create_azure_blob_credentials_block(connection_string: str):
    cred_block_name: str = "azure-blob-credentials"
    if connection_string is None or connection_string == "":
        raise ValueError(
            f"Failed to retrieve blob storage connection string, received: {connection_string}"
        )
    credentials = AzureBlobStorageCredentials(connection_string=connection_string)
    await credentials.save(cred_block_name, overwrite=True)
    print("Azure Blob Storage Credentials Block created!", flush=True)
    print(credentials, flush=True)
    return credentials


async def create_azure_blob_storage_block():
    simluation_block_name = PrefectBlock.SimulationBlobStorage.value
    try:
        credentials = await create_azure_blob_credentials_block(
            os.getenv("PREFECT_AZ_BLOB_STORAGE_CONN", "")
        )
        azure_container_name: str = "simulation-container"

        # Create simulation container in storage account if it does not exist
        async with credentials.get_client() as blob_service_client:
            container_client = blob_service_client.get_container_client(
                azure_container_name
            )

            exists = await container_client.exists()

            if exists:
                print(f"Container {azure_container_name} already exists...", flush=True)
                return

            await container_client.create_container()
            print(f"Container {azure_container_name} created successfully!", flush=True)

        azure_container = AzureBlobStorageContainer(
            container_name=azure_container_name,
            credentials=credentials,
        )

        await azure_container.save(simluation_block_name, overwrite=True)
        print("Simulation Blob Storage Block created!", flush=True)

    except Exception as e:
        print(f"Failed to create {simluation_block_name}: {e}")
        raise Exception(f"Failed to create {simluation_block_name}: {e}")


if __name__ == "__main__":
    asyncio.run(create_azure_blob_storage_block())
