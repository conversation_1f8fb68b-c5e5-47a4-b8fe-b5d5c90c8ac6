import asyncio
from .._imports import *

from backend.infrastructure._orchaestration.flows.BasePrefectFlow import BasePrefectFlow
from backend.infrastructure._orchaestration.flows.SimulationChunked import (
    SimulationChunked,
)
from backend.infrastructure._orchaestration.flows.TestFlow import TestFlow


class PrefectDeploymentManager:
    # All deployable flows are defined here
    flows_set: ClassVar[Set[Type[BasePrefectFlow]]] = {TestFlow, SimulationChunked}

    @classmethod
    def _validate_flows(cls, flows: List[str]) -> List[Type[BasePrefectFlow]]:
        return [flow for flow in cls.flows_set if flow.__name__ in flows]

    @classmethod
    async def deploy_flows(cls, flows: Optional[List[str]] = None):
        validated_flows = cls._validate_flows(flows or cls.get_flow_list())
        for flow in validated_flows:
            await flow.deploy()

    @classmethod
    def get_flow_list(cls):
        return [flow.__name__ for flow in cls.flows_set]


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-n",
        "--names",
        nargs="+",
        help="Provide the class names of Prefect flows to deploy separated by space.",
        required=False,
        type=str,
        choices=[flow.__name__ for flow in PrefectDeploymentManager.flows_set],
    )
    args = parser.parse_args()

    requested_flows = list(
        set(args.names) if args.names else PrefectDeploymentManager.get_flow_list()
    )
    asyncio.run(PrefectDeploymentManager.deploy_flows(requested_flows))
