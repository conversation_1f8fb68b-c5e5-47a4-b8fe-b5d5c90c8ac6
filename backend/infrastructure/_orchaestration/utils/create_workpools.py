from prefect.client.orchestration import get_client
from prefect.client.schemas.actions import WorkPoolCreate
import os


def validate_azure_environment():
    """Validates that all required Azure environment variables are set."""
    required_vars = {
        "PREFECT_AZ_ACI_IDENTITY": "Azure Container Instance Managed Identity",
        "PREFECT_AZ_CONTAINER_REGISTRY": "Azure Container Registry URL",
        "PREFECT_AZ_SUBNET_ID": "Azure Subnet ID",
        "AZ_SUBSCRIPTION_ID": "Azure Subscription ID",
        "AZ_RESOURCE_GROUP": "Azure Resource Group",
    }

    missing_vars = []

    for var, description in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(f"{var} ({description})")

    if missing_vars:
        raise EnvironmentError(
            "Missing required environment variables:\n"
            + "\n".join(f"- {var}" for var in missing_vars)
        )


async def create_aci_workpool():
    """Create Prefect work pool for simulations"""

    validate_azure_environment()

    base_job_template = {
        "variables": {
            "type": "object",
            "required": ["resource_group_name", "subscription_id"],
            "properties": {
                "cpu": {
                    "type": "number",
                    "title": "CPU",
                    "default": 2,
                    "description": "The number of virtual CPUs to assign to the task container. If not provided, a default value of 1.0 will be used.",
                },
                "env": {
                    "type": "object",
                    "title": "Environment Variables",
                    "description": "Environment variables to set when starting a flow run.",
                    "additionalProperties": {"type": "string"},
                },
                "name": {
                    "type": "string",
                    "title": "Name",
                    "description": "Name given to infrastructure created by a worker.",
                },
                "image": {
                    "type": "string",
                    "title": "Image",
                    "description": "The image to use for the Prefect container in the task. This value defaults to a Prefect base image matching your local versions.",
                },
                "labels": {
                    "type": "object",
                    "title": "Labels",
                    "description": "Labels applied to infrastructure created by a worker.",
                    "additionalProperties": {"type": "string"},
                },
                "memory": {
                    "type": "number",
                    "title": "Memory",
                    "default": 2,
                    "description": "The amount of memory in gigabytes to provide to the ACI task. Valid amounts are specified in the Azure documentation. If not provided, a default value of  1.0 will be used unless present on the task definition.",
                },
                "command": {
                    "type": "string",
                    "title": "Command",
                    "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker.",
                },
                "gpu_sku": {
                    "type": "string",
                    "title": "GPU SKU",
                    "description": "The Azure GPU SKU to use. See the ACI documentation for a list of GPU SKUs available in each Azure region.",
                },
                "gpu_count": {
                    "type": "integer",
                    "title": "GPU Count",
                    "description": "The number of GPUs to assign to the task container. If not provided, no GPU will be used.",
                },
                "entrypoint": {
                    "type": "string",
                    "title": "Entrypoint",
                    "default": "/opt/prefect/entrypoint.sh",
                    "description": "The entrypoint of the container you wish you run. This value defaults to the entrypoint used by Prefect images and should only be changed when using a custom image that is not based on an official Prefect image. Any commands set on deployments will be passed to the entrypoint as parameters.",
                },
                "identities": {
                    "type": "array",
                    "items": {"type": "string"},
                    "title": "Identities",
                    "default": [os.getenv("PREFECT_AZ_ACI_IDENTITY", "")],
                    "description": "A list of user-assigned identities to associate with the container group. The identities should be an ARM resource IDs in the form: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}'.",
                },
                "subnet_ids": {
                    "type": "array",
                    "items": {"type": "string"},
                    "title": "Subnet IDs",
                    "default": [os.getenv("PREFECT_AZ_SUBNET_ID", "")],
                    "description": "A list of subnet IDs to associate with the container group. ",
                },
                "dns_servers": {
                    "type": "array",
                    "items": {"type": "string"},
                    "title": "DNS Servers",
                    "description": "A list of DNS servers to associate with the container group.",
                },
                "stream_output": {
                    "type": "boolean",
                    "title": "Stream Output",
                    "default": True,
                    "description": "If `True`, logs will be streamed from the Prefect container to the local console.",
                },
                "image_registry": {
                    "anyOf": [{"$ref": "#/definitions/ACRManagedIdentity"}],
                    "title": "Image Registry (Optional)",
                    "default": {
                        "identity": os.getenv("PREFECT_AZ_ACI_IDENTITY", ""),
                        "registry_url": os.getenv("PREFECT_AZ_CONTAINER_REGISTRY", ""),
                    },
                    "description": "To use any private container registry with a username and password, choose DockerRegistry. To use a private Azure Container Registry with a managed identity, choose ACRManagedIdentity.",
                },
                "aci_credentials": {
                    "allOf": [
                        {"$ref": "#/definitions/AzureContainerInstanceCredentials"}
                    ],
                    "title": "Aci Credentials",
                    "description": "The credentials to use to authenticate with Azure.",
                },
                "subscription_id": {
                    "type": "string",
                    "title": "Azure Subscription ID",
                    "format": "password",
                    "default": os.getenv("AZ_SUBSCRIPTION_ID", ""),
                    "writeOnly": True,
                    "description": "The ID of the Azure subscription to create containers under.",
                },
                "resource_group_name": {
                    "type": "string",
                    "title": "Azure Resource Group Name",
                    "default": os.getenv("AZ_RESOURCE_GROUP", ""),
                    "description": "The name of the Azure Resource Group in which to run Prefect ACI tasks.",
                },
                "keep_container_group": {
                    "type": "boolean",
                    "title": "Keep Container Group After Completion",
                    "default": False,
                    "description": "Keep the completed container group on Azure.",
                },
                "task_watch_poll_interval": {
                    "type": "number",
                    "title": "Task Watch Poll Interval",
                    "default": 5,
                    "description": "The number of seconds to wait between Azure API calls while monitoring the state of an Azure Container Instances task.",
                },
                "task_start_timeout_seconds": {
                    "type": "integer",
                    "title": "Task Start Timeout Seconds",
                    "default": 480,
                    "description": "The amount of time to watch for the start of the ACI container. before marking it as failed.",
                },
            },
            "definitions": {
                "DockerRegistry": {
                    "type": "object",
                    "title": "DockerRegistry",
                    "required": ["username", "password", "registry_url"],
                    "properties": {
                        "reauth": {
                            "type": "boolean",
                            "title": "Reauth",
                            "default": True,
                            "description": "Whether or not to reauthenticate on each interaction.",
                        },
                        "password": {
                            "type": "string",
                            "title": "Password",
                            "format": "password",
                            "writeOnly": True,
                            "description": "The password to log into the registry with.",
                        },
                        "username": {
                            "type": "string",
                            "title": "Username",
                            "description": "The username to log into the registry with.",
                        },
                        "registry_url": {
                            "type": "string",
                            "title": "Registry Url",
                            "description": 'The URL to the registry. Generally, "http" or "https" can be omitted.',
                        },
                    },
                    "description": "DEPRECATION WARNING:\n\nThis class is deprecated as of March 2024 and will not be available after September 2024.\nIt has been replaced by `DockerRegistryCredentials` from the `prefect-docker` package, which\noffers enhanced functionality and better a better user experience.\n\nConnects to a Docker registry.\n\nRequires a Docker Engine to be connectable.",
                    "secret_fields": ["password"],
                    "block_type_slug": "docker-registry",
                    "block_schema_references": {},
                },
                "ACRManagedIdentity": {
                    "type": "object",
                    "title": "ACRManagedIdentity",
                    "properties": {
                        "identity": {
                            "type": "string",
                            "title": "Identity",
                            "description": "The user-assigned Azure managed identity for the private registry.",
                        },
                        "registry_url": {
                            "type": "string",
                            "title": "Registry URL",
                            "description": "The URL to the registry, such as myregistry.azurecr.io. Generally, 'http' or 'https' can be omitted.",
                        },
                    },
                    "description": "Use a Managed Identity to access Azure Container registry. Requires the\nuser-assigned managed identity be available to the ACI container group.",
                },
                "AzureContainerInstanceCredentials": {
                    "type": "object",
                    "title": "AzureContainerInstanceCredentials",
                    "properties": {
                        "client_id": {
                            "type": "string",
                            "title": "Client ID",
                            "description": "The service principal client ID. If none of client_id, tenant_id, and client_secret are provided, will use DefaultAzureCredential; else will need to provide all three to use ClientSecretCredential.",
                        },
                        "tenant_id": {
                            "type": "string",
                            "title": "Tenant ID",
                            "description": "The service principal tenant ID.If none of client_id, tenant_id, and client_secret are provided, will use DefaultAzureCredential; else will need to provide all three to use ClientSecretCredential.",
                        },
                        "client_secret": {
                            "type": "string",
                            "title": "Client Secret",
                            "format": "password",
                            "writeOnly": True,
                            "description": "The service principal client secret.If none of client_id, tenant_id, and client_secret are provided, will use DefaultAzureCredential; else will need to provide all three to use ClientSecretCredential.",
                        },
                        "credential_kwargs": {
                            "type": "object",
                            "title": "Additional Credential Keyword Arguments",
                            "description": "Additional keyword arguments to pass to `ClientSecretCredential` or `DefaultAzureCredential`.",
                        },
                    },
                    "description": "Block used to manage Azure Container Instances authentication. Stores Azure Service\nPrincipal authentication data.",
                    "secret_fields": ["client_secret"],
                    "block_type_slug": "azure-container-instance-credentials",
                    "block_schema_references": {},
                },
            },
            "description": "Variables for an Azure Container Instance flow run.",
        },
        "job_configuration": {
            "cpu": "{{ cpu }}",
            "env": "{{ env }}",
            "name": "{{ name }}",
            "image": "{{ image }}",
            "labels": "{{ labels }}",
            "memory": "{{ memory }}",
            "command": "{{ command }}",
            "gpu_sku": "{{ gpu_sku }}",
            "gpu_count": "{{ gpu_count }}",
            "entrypoint": "{{ entrypoint }}",
            "identities": "{{ identities }}",
            "subnet_ids": "{{ subnet_ids }}",
            "dns_servers": "{{ dns_servers }}",
            "arm_template": {
                "$schema": "https://schema.management.azure.com/schemas/2019-08-01/deploymentTemplate.json#",
                "resources": [
                    {
                        "name": "[parameters('container_group_name')]",
                        "type": "Microsoft.ContainerInstance/containerGroups",
                        "location": "[parameters('location')]",
                        "apiVersion": "2022-09-01",
                        "properties": {
                            "osType": "Linux",
                            "containers": [
                                {
                                    "name": "[parameters('container_name')]",
                                    "properties": {
                                        "image": "{{ image }}",
                                        "command": "{{ command }}",
                                        "resources": {
                                            "requests": {
                                                "cpu": "{{ cpu }}",
                                                "memoryInGB": "{{ memory }}",
                                            }
                                        },
                                        "environmentVariables": [],
                                    },
                                }
                            ],
                            "restartPolicy": "Never",
                        },
                    }
                ],
                "parameters": {
                    "location": {
                        "type": "string",
                        "metadata": {"description": "Location for all resources."},
                        "defaultValue": "[resourceGroup().location]",
                    },
                    "container_name": {
                        "type": "string",
                        "metadata": {
                            "description": "The name of the container to create."
                        },
                        "defaultValue": "[uniqueString(resourceGroup().id)]",
                    },
                    "container_group_name": {
                        "type": "string",
                        "metadata": {
                            "description": "The name of the container group to create."
                        },
                        "defaultValue": "[uniqueString(resourceGroup().id)]",
                    },
                },
                "contentVersion": "*******",
            },
            "stream_output": "{{ stream_output }}",
            "image_registry": "{{ image_registry }}",
            "aci_credentials": "{{ aci_credentials }}",
            "subscription_id": "{{ subscription_id }}",
            "resource_group_name": "{{ resource_group_name }}",
            "keep_container_group": "{{ keep_container_group }}",
            "task_watch_poll_interval": "{{ task_watch_poll_interval }}",
            "task_start_timeout_seconds": "{{ task_start_timeout_seconds }}",
        },
    }

    async with get_client() as client:
        try:
            work_pool = WorkPoolCreate(
                name="simulation",
                type="azure-container-instance",  # This field exists in WorkPoolCreate
                base_job_template=base_job_template,
                is_paused=False,
            )

            result = await client.create_work_pool(work_pool=work_pool, overwrite=True)
            print(f"Created/Updated work pool: {result.name}", flush=True)

        except Exception as e:
            print(f"Error creating work pool: {str(e)}", flush=True)
            raise e
