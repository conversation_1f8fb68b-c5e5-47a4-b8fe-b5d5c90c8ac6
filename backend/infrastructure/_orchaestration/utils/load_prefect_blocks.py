from .._imports import *
from prefect_azure.blob_storage import AzureBlobStorageContainer


def get_azure_blob_container(block_name: str) -> AzureBlobStorageContainer:
    simulation_block_name = block_name
    try:
        azure_container = AzureBlobStorageContainer.load(simulation_block_name)
        return azure_container
    except ValueError as e:
        error_msg = f"{simulation_block_name} does not exist, check that this block has been created. {e}"
        logging.error(error_msg)
        raise ValueError(error_msg)
