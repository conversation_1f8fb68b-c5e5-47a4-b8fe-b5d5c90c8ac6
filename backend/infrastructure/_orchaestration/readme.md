## TLDR: Our Two-Level Scaling Problem
IMHO, we have two distinct scaling challenges to address:

1. Single Runtime Optimization: How to efficiently run multiple DWSIM simulations in parallel within one runtime
2. Distributed Infrastructure Scaling: How to scale our simulation capacity across multiple machines

The distributed architecture provides flexibility - it works with both parallel and sequential execution on each runtime. However, single runtime optimization alone cannot provide the scalability we need.

Therefore, I feel priority is to first implement distributed workload management first, then optimize single runtime performance. This approach gives us immediate scaling capabilities while leaving room for runtime-level optimization.

## Single Runtime Optimization: Parallel Process Executor

For a parallized process executor, the specs we need are:
- for each user, the ability to run min 100 simulations in parallel
- to be able to manage up to 30 users

### Deepdive: DWSim Tech Stack

DWSim's architecture is built on .NET with COM-based interop, which means it uses Microsoft's Component Object Model (COM) to enable communication between our Python code and the .NET-based DWSIM simulation engine.

Here is a stub to illustrate: 
```python
# Bridge setup
import pythonnet  # Enables Python-.NET interop
import clr       # Core component for .NET integration

# DWSIM Core DLLs
clr.AddReference(dwsimpath + "DWSIM.Automation.dll")
clr.AddReference(dwsimpath + "DWSIM.Interfaces.dll")
```
The simulator uses a component-based architecture where each unit operation (heaters, reactors, etc.) is a COM object that maintains its own state and performs calculations. These components communicate through a shared simulation environment, which creates specific challenges for parallel execution.

Key architectural points relevant to parallelization:
1. COM Object Isolation: Each DWSIM simulation instance must run in its own process space due
2. Memory Management: .NET/COM interop requires process isolation, else we will get memory leaks and object overrides
3. Resource Handling: Each instance needs its own .NET runtime and DWSIM component initialization

This architecture means that:
- Multi-threading within a single simulation is not feasible
- Parallel simulations must use process-level isolation
- Resource overhead for each parallel instance is potentially significant
- Process pool size should be carefully managed based on available system resources


DWSim Core Technologies:
```
- DWSIM: Open-source chemical process simulator (.NET-based)
- Python: Host language
- .NET Core: Runtime for DWSIM
- pythonnet: Python-CLR bridge (allows Python to interact with .NET)
```

Stack Hierarchy (top to bottom):
```scss
Python Application
    ↓
pythonnet (Python/.NET bridge)
    ↓
.NET Core Runtime
    ↓
DWSIM Libraries (.dll files)
```

Key Components you will see in `matrix_dwsim.py`
```python
# Bridge setup
import pythonnet  # Enables Python-.NET interop
import sys        # Used for path management
import clr       # Core component for .NET integration

# DWSIM Core DLLs (in priority order)
- CapeOpen.dll           # Chemical engineering standards
- DWSIM.Automation.dll   # Core automation interface
- DWSIM.Interfaces.dll   # Core interfaces
- DWSIM.GlobalSettings.dll  # Configuration
- DWSIM.SharedClasses.dll   # Common utilities
- DWSIM.Thermodynamics.dll  # Thermodynamic calculations
- DWSIM.UnitOperations.dll  # Process operations
```

For implementing parallel simulations, you'll need to focus on:
- Process-based parallelization strategies
- Resource allocation and cleanup
- State management between processes
- Performance monitoring and optimization

Documentation references:
- pythonnet: [https://pythonnet.github.io/](command:_cody.vscode.open?%22https%3A%2F%2Fpythonnet.github.io%2F%22)
- DWSIM API: [https://dwsim.org/wiki/index.php?title=Automation_Interface](command:_cody.vscode.open?%22https%3A%2F%2Fdwsim.org%2Fwiki%2Findex.php%3Ftitle%3DAutomation_Interface%22)

### FAQ
#### Can DWSIM do paralllel processing via concurreny or threads? 
**Threading (concurrent.futures.ThreadPoolExecutor)**
- Shares memory space
- Good for I/O-bound tasks
- Limited by Python's GIL (Global Interpreter Lock)
- Lower overhead to create

**Multiprocessing (concurrent.futures.ProcessPoolExecutor)**
- Separate memory spaces
- Good for CPU-bound tasks
- Bypasses GIL limitations
- Higher overhead to create
	
#### Assuming concurrency, how to make it optimal?
Given DWSIM's .NET/COM architecture, here's the potential approach:

**Use ProcessPoolExecutor (current implementation tested in)**
`test_dwsim_concurrency.py`
```python
with concurrent.futures.ProcessPoolExecutor() as executor:
    futures = [executor.submit(run_single_simulation, sim_id) for sim_id in sim_ids]
```

Reasons:
- DWSIM COM objects aren't thread-safe
- Each simulation needs its own .NET runtime instance
- Process isolation prevents memory leaks from COM interop
- CPU-intensive calculations benefit from true parallelism

**Avoid ThreadPoolExecutor because:**
- GIL limitations with pythonnet
- COM objects sharing same .NET runtime can cause conflicts
- Risk of memory leaks due to shared memory space

**For optimal performance:**
```python
# Add process count limit based on CPU cores
import multiprocessing

MAX_WORKERS = min(multiprocessing.cpu_count() - 1, 4)  # Leave 1 core free
with concurrent.futures.ProcessPoolExecutor(max_workers=MAX_WORKERS) as executor:
    futures = [executor.submit(run_single_simulation, sim_id) for sim_id in sim_ids]
```
... Keep the current ProcessPoolExecutor implementation, just tune the worker count for your hardware.


### Resources
- For matrix api and examples, see the unit tests: `backend/tests/unit/core/matrix`
- aggregate tests should be sufficient for your needs to understand and write tests for parallel execution

## Distributed Workloads

For simple parallel process executors, we don't technically need distributed workload management. In simple terms, we can treat the main runtime as the parallel process executor. 

### Problem Statement: Monolithic Workloads

Our implementation runs (potentiall) multiple DWSIM simulations on a single machine, requiring it to be sized for peak capacity. This creates a fundamental resource management challenge: how do we efficiently handle COM object lifecycles, memory allocation, and CPU scheduling when all processes compete for the same resources?

How can we move from a monolithic workload (single machine, all processes) to a distributed architecture that enables dynamic resource allocation and natural load balancing across multiple machines? This would allow us to scale our simulation capacity based on actual demand while maintaining the process isolation requirements that our DWSIM COM objects need.

### Solution Approaches: Distributed Workloads

I see two ways to manage distributed workloads:

#### Message Queue Based (RabbitMQ/Redis):
- Uses pub/sub pattern for work distribution
- Requires custom implementation of retry logic, monitoring
- Good for simple workflows but requires maintenance

#### Workflow Orchestrators (Prefect/Temporal):
- Uses persistent event sourcing
- Provides workflow primitives out of the box
- Handles complex failure modes automatically
- Has management pane for cron, etc

From my understanding, a message queue would just distribute simulation tasks, while a workflow orchestrator would manage the entire simulation lifecycle including retries, state tracking, and resource coordination.

The key difference is that message queues are building blocks for distributed systems, while workflow orchestrators provide higher-level abstractions specifically designed for complex task management. For our DWSIM workloads, a workflow orchestrator better matches our needs for reliable parallel processing.

### LO Vote: Workflow Orchaestrators

I advocate for Workflow Orchaestrators, so will go into more detail there:

### The primary abstractions of orchestrators like Prefect/Temporal are:
- Tasks: Atomic units of work (our DWSIM simulations)
- Flows: Directed graphs of task dependencies
- Workers: Distributed compute resources
- Schedulers: Intelligent work distribution
- State Backends: Persistent workflow state storage
- Observability Layer: Metrics, logging, tracing

### Benefits of using an open-source solution:
- Built-in retry mechanisms with backoff strategies
- Comprehensive logging and metrics
- Resource quotas and scheduling policies
- Workflow versioning and deployment management
- Development to production workflow promotion
- Integration with cloud infrastructure
- Active community support and best practices

For our DWSIM parallel processing needs, an orchestrator would help manage the COM object lifecycle and resource utilization across multiple simulations efficiently.

For example of implementation, see: https://github.com/lennardong/yoshi_optionstrading (LO to do live walkthrough)


### Resources:
- Temporal: https://temporal.io
- Prefect: https://www.prefect.io

## Profiling to Rightsize

Resource profiling is IMHO a key metric we need for optimizing our DWSIM parallel processing infrastructure. We need to measure memory footprint, CPU utilization, and COM object lifecycle patterns using tools like memory_profiler to understand the behavior of each simulation instance. This data will reveal our performance boundaries and scaling capabilities.

The key metrics and patterns will determine if we can achieve our target of 100 concurrent processes per runtime instance, which directly impacts our container strategy. By analyzing peak memory usage, CPU core saturation, and process isolation overhead, we can make informed decisions about worker pool sizes, resource allocation, and optimal container configurations for production deployment.

Following  that, decisions need to be made on our scaling infrastructre. Here are some braindumps:

2. Prefect/Temporal with Dedicated Workers
- Leverage existing ProcessPoolExecutor code
- Add workflow management layer
- Scale via worker nodes
- Built-in monitoring

3. RabbitMQ + Worker Nodes
- Message queue driven
- Stateless workers
- Simple horizontal scaling
- Direct resource control

4. Docker Swarm
- Native container orchestration
- Service discovery included
- Rolling updates
- Network isolation per service

5. Azure Container instances (ACI) or DigitalOcean App Platform
- Managed container platform
- Automatic scaling
- Pay-per-use
- AWS service integrations

6. Kubernetes
- Full container orchestration
- Resource quotas per namespace
- Advanced scheduling
- Comprehensive monitoring

## LO's IMHO

IMHO, I advocate for using Temporal as our workflow orchestrator paired with Docker Swarm for container orchestration. Resource scaling for the containers will be on a managed service. This architecture provides a balance of battle-testedworkflow management and operational simplicity for our DWSIM parallel processing needs. 

```
Primary VM
+------------------------+
|  Backend Server        |
|  +------------------+ |
|  | Temporal Server  | |
|  +------------------+ |
+------------------------+
           |
           v
+------------------------+
|        Caddy           |
+------------------------+
           |
           v
Docker Swarm Cluster
+------------------------+
| Node1    Node2   Node3 |
| +----+  +----+  +----+|
| |DWSIM| |DWSIM| |DWSIM||
| +----+  +----+  +----+|
|                       |
| Auto-scaling via      |
| VMSS/DO Node Pool    |
+------------------------+
```
Proposed architecture:
- Temporal and our main backend server run on a primary VM, handling workflow orchestration, retries and state management for our DWSIM simulations
- Docker Swarm runs on separate VM(s), providing lightweight container orchestration for our compute-intensive workloads. The main backend communicates with Swarm through a load balancer, enabling independent scaling of orchestration and compute resources
- For the Docker Swarm compute layer, we can leverage Azure Virtual Machine Scale Sets (VMSS) or DigitalOcean Droplet node pools. Both provide native auto-scaling, load balancing, and monitoring - giving us enterprise-grade infrastructure with low operational overhead