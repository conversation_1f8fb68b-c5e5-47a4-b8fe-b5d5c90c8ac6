FROM lennardong/sonic:dwsim84-postgre

WORKDIR /code

COPY pyproject.toml poetry.toml ./
COPY ./.devcontainer/dwsim_patch.sh ./

# Check files 
RUN ls -ah

# Install dependencies
ENV PATH="$HOME/.local/bin:$PATH"
RUN poetry install

# Install DWSIM patch
RUN chmod +x dwsim_patch.sh
RUN ./dwsim_patch.sh

# Define env vars
ENV PROJECT_ROOT="/code"
ENV PATH="/code/.venv/bin:$PATH"

# Set locale for DWSIM
ENV LANG=en_SG.UTF-8 
ENV LC_ALL=en_SG.UTF-8 

# Copy over backend and frontend code
COPY ./frontend ./frontend
COPY ./backend ./backend

# Copy entrypoint.sh into /opt/prefect/entrypoint.sh
RUN mkdir -p /opt/prefect && \
    curl -o /opt/prefect/entrypoint.sh https://raw.githubusercontent.com/PrefectHQ/prefect/refs/tags/2.20.16/scripts/entrypoint.sh && \
    chmod a+x /opt/prefect/entrypoint.sh

ENTRYPOINT ["/opt/prefect/entrypoint.sh"]

CMD ["tail", "-f", "/dev/null"]