# Internal
from __future__ import annotations
from ._imports import *
import backend.infrastructure._db.dto as dto

# SQLAlchemy
from sqlalchemy import (
    DateTime,
    Float,
    JSON,
    Index,
    Integer,
    String,
    UniqueConstraint,
    func,
    text,
    JSON,
    Boolean,
    ForeignKey,
    MetaData,
    Table,
    ForeignKeyConstraint,
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import declarative_base, Mapped, mapped_column



####################

# HELPERS

def get_subclass(parent_class: Type, subclass_name: str) -> Callable:
    """
    Retrieves a subclass of a parent class.

    Args:
        parent_class (Type): The parent class.
        subclass_name (str): The name of the subclass to retrieve.
    Returns:
        Callable: The subclass.
    """
    # BFS search
    classes_to_check = [parent_class]
    while classes_to_check:
        current_class = classes_to_check.pop()
        for subclass in current_class.__subclasses__():
            if subclass.__name__ == subclass_name:
                return subclass
            classes_to_check.append(subclass)
    raise ValueError(f"Subclass {subclass_name} not found in {parent_class}")


#############################

# ATLAS SCOPE

Base = declarative_base()


class AtlasSchema(Base):
    __tablename__ = "atlas_schema"

    # SCHEMA

    # METADATA
    user_id: Mapped[str] = mapped_column(
        String(length=50),
        nullable=False,
    )  # e.g. 2d3w4df-22343fdsd-3432df

    t_created: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
    )
    t_updated: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
    )
    t_deleted: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)

    model_is_template: Mapped[bool] = mapped_column(
        Boolean, nullable=False, server_default="false"
    )

    # MODEL DATA
    model_id_pk: Mapped[str] = mapped_column(
        UUID(as_uuid=True), nullable=False, primary_key=True, default=uuid.uuid4
    )  # e.g. Hydorzene-Fermentation_process 1
    model_label: Mapped[str] = mapped_column(
        String(55),
        nullable=True,
    )
    model_version: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
    )
    model_description: Mapped[str] = mapped_column(
        String(255),
        nullable=True,
    )
    model_json: Mapped[dict] = mapped_column(JSONB, nullable=False)

    # METHODS

    @property
    def id(self):
        return f"{self.model_id_pk}_{self.model_version}"

    def bootstrap_atlas(self) -> Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]:
        """
        Bootstraps an Atlas object from the model data.

        This method constructs an Atlas object and its associated metadata from the model's stored JSON data
        and other model attributes.

        Returns:
            Tuple[AtlasRoot, AtlasMetadata]: A tuple containing:
                - AtlasRoot: The deserialized Atlas object
                - AtlasMetadata: Metadata object containing user ID, timestamps and template status
        """

        data_as_dict = self.model_json
        atlas = dto.AtlasJSONSerializer.deserialize(data_as_dict)

        # Ensure all timestamps are explicitly in UTC timezone
        t_created = self.t_created
        if t_created and t_created.tzinfo is None:
            t_created = t_created.replace(tzinfo=timezone.utc)
        elif t_created and t_created.tzinfo != timezone.utc:
            t_created = t_created.astimezone(timezone.utc)

        t_updated = self.t_updated
        if t_updated and t_updated.tzinfo is None:
            t_updated = t_updated.replace(tzinfo=timezone.utc)
        elif t_updated and t_updated.tzinfo != timezone.utc:
            t_updated = t_updated.astimezone(timezone.utc)

        t_deleted = self.t_deleted
        if t_deleted and t_deleted.tzinfo is None:
            t_deleted = t_deleted.replace(tzinfo=timezone.utc)
        elif t_deleted and t_deleted.tzinfo != timezone.utc:
            t_deleted = t_deleted.astimezone(timezone.utc)

        metadata = core.at.AtlasMetadata(
            user_id=self.user_id,
            date_created=t_created,
            date_modified=t_updated,
            date_deleted=t_deleted,
            is_template=self.model_is_template,
            atlas_label=atlas.label
        )

        return atlas, metadata

    @classmethod
    def hydrate_schema(
        cls, obj: core.at.AtlasRoot, metadata: Optional[core.at.AtlasMetadata] = None
    ):
        """Convert AtlasRoot object and optional metadata into an ORM model instance.

        This method creates a database model instance by hydrating it with data from an AtlasRoot
        object and associated metadata. It serializes the object to JSON and combines it with
        metadata fields to create a complete database record.

        Args:
            obj (core.at.AtlasRoot): The Atlas object to hydrate into the database model
            metadata (Optional[core.at.AtlasMetadata]): Optional metadata for the object. If not provided,
                default metadata will be created with public access and current timestamps

        Returns:
            cls: A new instance of the database model populated with the provided data

        Example:
            >>> root = AtlasRoot(uid="123", label="Test Model")
            >>> db_model = ModelClass.hydrate_schema(root)
        """

        json = dto.AtlasJSONSerializer.serialize(obj)

        if metadata is None:
            metadata = core.at.AtlasMetadata(
                user_id="public",
                atlas_label=obj.label,
                date_created=datetime.now(timezone.utc),
                date_modified=datetime.now(timezone.utc),
                is_template=True,
            )

        return cls(
            # User
            user_id=metadata.user_id,
            t_created=metadata.date_created,
            t_updated=metadata.date_modified,
            t_deleted=metadata.date_deleted,
            model_is_template=metadata.is_template,
            # Model
            model_id_pk=obj.uid,
            model_label=obj.label,
            model_version=obj.version,
            model_description=obj.description,
            model_json=json,
        )
