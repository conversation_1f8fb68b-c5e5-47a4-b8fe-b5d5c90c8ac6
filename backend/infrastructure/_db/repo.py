from __future__ import annotations
from pathlib import Path
from ._imports import *
from .orm import *

from sqlalchemy.orm import Session
from sqlalchemy import select, or_, text

from backend.core._sharedutils.singleton import SingletonMeta


####################

"""
PROTOCOL

This protocol is the interface between this class and any other classes or services.
All concrete classes to follow this protocol. 
"""

T = TypeVar("T")


@runtime_checkable
class RepoProtocol(Protocol[T]):
    """Repository Protocol defining standard CRUD operations"""

    def save(self, data: T, **kwargs) -> bool:
        """
        Save data to repository
        kwargs may include:
            - user_id: UUID (for SQL repos)
            - is_template: bool (for SQL repos)
        """
        ...

    def load(self, key: Union[uuid.UUID, str], **kwargs) -> Optional[Tuple[T, Any]]:
        """
        Load data from repository by key
        - SQL repos expect UUID
        - File repos expect string
        """
        ...

    def list(self, filter_by: Optional[Any] = None) -> List[Tuple[T, Any]]:
        """
        List items in repository, optionally filtered
        - SQL repos use user_id as filter
        - File repos might use different criteria
        """
        ...

    def remove(self, key: Union[uuid.UUID, str]) -> bool:
        """Remove item from repository"""
        ...


####################

# FOLDER REPO


class FolderRepoForDWSim(RepoProtocol[core.ma.MatrixDWSim]):
    def __init__(
        self,
        project_storage_path: str,
        subfolder: str,
    ):
        """Simple saving of DWSIM into a repo"""
        self.storage_path = project_storage_path
        self.subfolder = subfolder  # potentially the plantid or something

    @property
    def abs_folderpath(self) -> Path:
        """Checks relative storage path and converts its to a Path object.
        Adds in subfolder and converts it to ABSOLUTE FILEPATH"""

        path = (
            sharedutils.create_absolute_path_object(self.storage_path) / self.subfolder
        )
        path.mkdir(parents=True, exist_ok=True)
        return path

    def save(self, data: core.ma.MatrixDWSim, **kwargs):
        """Given a Matrix-DWSIM model, save it into server filestorage"""
        filename = f"{data.label}.dwxmz"
        abs_filepath = self.abs_folderpath / filename

        try:
            data.dwsim_interface.SaveFlowsheet(
                data.dwsim_simulator, str(abs_filepath), True
            )
        except Exception as e:
            raise ValueError(f"Issue with saving {filename} - {e}")

        logging.info(
            f"{sharedutils.get_function_name()}() - filesaved: {str(abs_filepath)}"
        )
        return self._verify_save(str(abs_filepath))

    def _verify_save(self, filename: str) -> bool:
        return any(
            file.is_file() and filename in file.name
            for file in self.abs_folderpath.iterdir()
        )

    def load(self, key, **kwargs) -> Optional[Tuple[core.ma.MatrixDWSim, Any]]:
        """Given a atlas model ID, finds the latest dwsim model built from it"""
        #  Get self.abs_path for filenames with key in filename
        raise NotImplementedError()

    def list(self, filter_by: Optional[str] = None) -> List[Tuple[core.ma.MatrixDWSim, Any]]:
        raise NotImplementedError()

    def remove(self, key):
        raise NotImplementedError()


####################

# POSTGRESQL REPO.


class SQLRepoForAtlas(RepoProtocol[core.at.AtlasRoot]):
    def __init__(self, session: Session):
        self.session = session

    def save(self, data: core.at.AtlasRoot, **kwargs) -> bool:
        """Save AtlasRoot object to database.

        Args:
            data: AtlasRoot object to persist
            **kwargs: Must contain metadata with AtlasMetadata object

        Returns:
            bool: True if save successful

        Raises:
            ValueError: If metadata not provided in kwargs
        """
        # Extract required metadata
        metadata = kwargs.get("metadata")
        if not metadata or not isinstance(metadata, core.at.AtlasMetadata):
            raise ValueError("AtlasMetadata object required in kwargs['metadata']")

        # Fetch existing record first
        existing = self.session.query(AtlasSchema).filter_by(model_id_pk=data.uid).first()

        if existing:
            # Update specific fields while preserving creation timestamp
            existing.t_updated = metadata.date_modified
            existing.model_is_template = metadata.is_template
            existing.user_id = metadata.user_id
            existing.model_version += 1
            existing.model_label = data.label
            existing.model_description = data.description
            existing.model_json = dto.AtlasJSONSerializer.serialize(data) # type:ignore

            if metadata.date_deleted:
                existing.t_deleted = metadata.date_deleted
            # Don't modify existing.t_create or model_id_pk

        else:
            # New record case
            schema = AtlasSchema.hydrate_schema(data, metadata=metadata)
            self.session.add(schema)

        return True

    def load(
        self, key: Union[uuid.UUID, str], **kwargs
    ) -> Optional[Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]]:
        """
        Load by either label or UUID.
        - If loading by label: first try exact user match, then template
        - If loading by UUID: no user_id needed (allows template access)

        Args:
            key: Label (str) or UUID of the model
            **kwargs: Optional user_id for access control
        """
        if isinstance(key, str):
            user_id = kwargs.get("user_id")

            # First try to find exact match on label and user_id
            stmt = select(AtlasSchema).where(
                AtlasSchema.model_label == key,
                AtlasSchema.user_id == user_id,
                AtlasSchema.t_deleted.is_(None),
            )
            row = self.session.scalar(stmt)

            # If not found, check if there's a template with the same label
            if not row:
                stmt = select(AtlasSchema).where(
                    AtlasSchema.model_label == key,
                    AtlasSchema.model_is_template.is_(True)
                )
                row = self.session.scalar(stmt)
        else:
            # Loading by UUID - only check deletion status
            stmt = select(AtlasSchema).where(
                AtlasSchema.model_id_pk == key, 
                AtlasSchema.t_deleted.is_(None)
            )
            row = self.session.scalar(stmt)

        return row.bootstrap_atlas() if row else None

    def list(
        self, filter_by: Optional[Any] = None
    ) -> List[Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]]:
        """
        List items in repository, optionally filtered by user_id.

        Args:
            filter_by: Optional user_id to filter results. If provided, returns
                      user's models AND all templates. If None, returns only templates.

        Returns:
            Tuple[AtlasRoot, AtlasMetadata]: List of non-deleted models
        """
        if filter_by:
            # If user_id provided, get their models (including deleted) OR templates
            # Don't filter by deletion status by default - this allows returning deleted items
            stmt = select(AtlasSchema).where(
                or_(
                    AtlasSchema.model_is_template.is_(True),
                    AtlasSchema.user_id == filter_by,
                ),
            )
        else:
            # If no user_id, only get templates (still without deletion filter)
            stmt = select(AtlasSchema).where(
                AtlasSchema.model_is_template.is_(True)
            )

        rows = self.session.scalars(stmt).all()

        return [row.bootstrap_atlas() for row in rows]

    def remove(self, key: Union[uuid.UUID, str], **kwargs) -> bool:
        """
        Soft-delete an item from repository.

        Args:
            key: Label (str) or UUID of the model
            **kwargs: Optional user_id for label-based access control

        Returns:
            bool: True if found and deleted, False otherwise
        """
        if isinstance(key, str):
            # Loading by label - just check deletion status
            stmt = select(AtlasSchema).where(
                AtlasSchema.model_label == key,
                AtlasSchema.t_deleted.is_(None),
                AtlasSchema.user_id == kwargs.get("user_id"),
            )
        else:
            # Loading by UUID - just check deletion status
            stmt = select(AtlasSchema).where(
                AtlasSchema.model_id_pk == key, AtlasSchema.t_deleted.is_(None)
            )

        row = self.session.scalar(stmt)
        if not row:
            return False

        row.t_deleted = datetime.now(timezone.utc)
        self.session.commit()
        return True

    def clear_all(self) -> None:
        """Delete all rows from the database. Use with caution!"""
        stmt = text("DELETE FROM atlas_schema")
        self.session.execute(stmt)
        self.session.commit()


####################

# IN MEM REPO

K = TypeVar("K")
V = TypeVar("V")


class z_AbstractInProcessRepo(Generic[K, V], metaclass=SingletonMeta):

    _repo: Dict

    def __init__(self, repo: Dict[K, V] = {}) -> None:
        self._repo = repo

    @abstractmethod
    def _get_key(self, data: V) -> K:
        pass

    @abstractmethod
    def _get_label(self, data: V) -> str:
        pass

    def save(self, data: V) -> bool:
        """Save data to repository"""
        key = self._get_key(data)
        self._repo[key] = data
        return True

    def load(self, key: K) -> Optional[V]:
        """Load data from repository"""
        return self._repo.get(key, None)

    def remove(self, key: K) -> bool:
        try:
            self._repo.pop(key)
            return True
        except KeyError:
            return False

    def list(self, key: Optional[K] = None) -> List[V]:
        """List all keys in repository. Note that key is not necesary for this repo"""
        return list(self._repo.values())

    def get_labels(self) -> List[str]:
        return list(self.reverse_lookup.keys())

    def get_key_from_label(self, label: str) -> Optional[K]:
        """
        Returns the key that matches the first label.
        # TODO fix this implemetnation so labels are unique
        """
        return self.reverse_lookup.get(label, None)

    @property
    def reverse_lookup(self) -> Dict[str, K]:
        return {self._get_label(v): k for k, v in self._repo.items()}

#############################
# Folder repo for surrogate
import pickle

class ArtifactFolderRepo():
    """
    Saves items to artefact repo as simple files. 
    uses Path objects
    """

    def __init__(self, parent_folder: str):
        self._rootpath = None
        self._init_folder(parent_folder)
    
    @property
    def rootpath(self):
        if self._rootpath is None:
            raise AttributeError("root path needs init")
        return self._rootpath
    
    def _init_folder(self, parent_folder: str):
        """
        Finds root folder /code/backend/artefacts via getting the .gitignore then traversing up
        then, creates a folder in there as name space. 
        stores it as rootpath
        """
        try:
            current_file_path = Path(__file__).parent
            project_root = current_file_path
            while project_root.parent != project_root and not (project_root / ".git").exists():
                project_root = project_root.parent
            
            # Move to artefacts
            base_path = project_root / 'backend' / 'application' / 'artefacts'

            # create directory
            base_path.mkdir(parents=True, exist_ok=True)
            self._rootpath = base_path / parent_folder
            self._rootpath.mkdir(parents=True, exist_ok = True)

        except Exception as e:
            raise RuntimeError(f"Failed to initialize repository: {str(e)}")
        

    def save(self, subfolder: str, filename: str, data: Any) -> Path:
        """
        Save data to folder
        """
        try:
            path = self.rootpath / subfolder
            path.mkdir(parents = True, exist_ok= True)

            filepath = path / filename
            
            if isinstance(data, str):
                with open (filepath, "w", encoding = "utf-8" ) as f:
                    f.write(data)
            else:
                with open(filepath, "wb") as f:
                    pickle.dump(data, f, protocol =pickle.HIGHEST_PROTOCOL)
            
            logging.info(f"Saved file: f{str(filepath)}")
            return filepath
        except Exception as e:
            raise IOError(f"Error saving `{str(filename)}` to `{subfolder}`: {str(e)}")

    def load(self, subfolder: str, filename: str) -> Any:
        """
        Load data from the specified subfolder and filename.
        
        Args:
            subfolder: Subfolder within repository
            filename: Name of the file
            
        Returns:
            The loaded data
            
        Raises:
            FileNotFoundError: If file does not exist
            IOError: If file cannot be read
        """
        try:
            filepath = self.rootpath / subfolder / filename
            
            if not filepath.exists():
                raise FileNotFoundError(f"File not found: {str(filepath)}")
                
            try:
                # Try loading as pickle first
                with open(filepath, 'rb') as f:
                    return pickle.load(f)
            except (pickle.UnpicklingError, UnicodeDecodeError):
                # If not pickle, try as text
                with open(filepath, 'r', encoding='utf-8') as f:
                    return f.read()
                    
        except Exception as e:
            logging.error(f"Error loading file {filename} from {subfolder}: {str(e)}")
            raise

    def remove(self, subfolder: str, filename: str) -> bool:
        """
        Delete file from folder.
        
        Args:
            subfolder: Subfolder within repository
            filename: Name of the file to delete
            
        Returns:
            bool: True if deleted, False otherwise
        """
        try:
            filepath = self.rootpath / subfolder / filename
            
            if filepath.exists():
                filepath.unlink()
                logging.debug(f"Removed file: {str(filepath)}")
                return True
            else:
                logging.warning(f"File not found for removal: {str(filepath)}")
                return False
        except Exception as e:
            logging.error(f"Error removing file {filename} from {subfolder}: {str(e)}")
            return False


class SurrogateFolderRepo(core.SurrogateRegistryPort):
    # NOTE - repo should have engine that is folder. this is a mixing of concerns
    TRANSFORMER_KEYWORD = "transformers"
    JOB_KEYWORD = "jobs"
    MODEL_KEYWORD = "models"
    
    def __init__(self):
        self.repo = ArtifactFolderRepo(parent_folder="surrogate_registry")
    
    def _generate_filename(self, user_ref: str, atlas_ref: str, suffix: str, type: str = "json"):
        filename = f"{user_ref}_{atlas_ref}_{suffix}.{type}"
        return filename

    def save_datatransformer(
        self,
        metadata: core.su.VOMetadata_General,
        data: core.su.transformers.SurrogateDataTransformer,
    ):
        filename = self._generate_filename(metadata.user_reference, metadata.atlas_reference, self.TRANSFORMER_KEYWORD)
        serialized_dict = data.serialize()

        json_data = json.dumps(serialized_dict, indent = 2)
        return self.repo.save(self.TRANSFORMER_KEYWORD, filename, json_data)
    
    def get_datatransformer(
        self,
        user_ref: str,
        atlas_ref: str,
    ) ->core.su.transformers.SurrogateDataTransformer:
        filename = self._generate_filename(user_ref, atlas_ref, self.TRANSFORMER_KEYWORD)
        json_data = self.repo.load(self.TRANSFORMER_KEYWORD, filename)
        
        serialized_dict = json.loads(json_data)
        return core.su.transformers.SurrogateDataTransformer.deserialize(serialized_dict)

    def save_job(
        self,
        metadata: core.su.VOMetadata_General,
        data: core.su.ENTTrainingJob,
    ):
        filename = self._generate_filename(metadata.user_reference, metadata.atlas_reference, self.JOB_KEYWORD)
        json_data = data.model_dump_json(indent = 2)
        
        return self.repo.save(self.JOB_KEYWORD, filename, json_data)

    def get_job(
        self,
        user_ref: str,
        atlas_ref: str,
    ) ->core.su.ENTTrainingJob:

        filename = self._generate_filename(user_ref, atlas_ref, self.JOB_KEYWORD)
        json_data = self.repo.load(self.JOB_KEYWORD, filename)
        dict_data = json.loads(json_data)
        
        return core.su.ENTTrainingJob.model_validate(dict_data)

    def save_model(
        self,
        metadata: core.su.VOMetadata_General,
        model: core.su.models.BaseSurrogateModel,
    ):
        """
        Save model to registry. uses Pickle. Not for production at scale.
        """
        # Save Native Model
        filename = self._generate_filename(metadata.user_reference, metadata.atlas_reference,  self.MODEL_KEYWORD, "pt")
        str_data = model.serialize_native_model()
        
        # Save Datatransformer
        self.save_datatransformer(metadata, model.datatransformer)

        return self.repo.save(
            self.MODEL_KEYWORD,
            filename,
            str_data
        )

    
    def get_model(
        self,
        metadata: core.su.VOMetadata_General
    ) ->core.su.models.BaseSurrogateModel:
        """
        Retrieve model from registry
        """

        # LOAD SERIALIZED MODEL
        user_ref = metadata.user_reference
        atlas_ref = metadata.atlas_reference
        filename = self._generate_filename(metadata.user_reference, metadata.atlas_reference,  self.MODEL_KEYWORD, "pt")
        model_serialized= self.repo.load(
            self.MODEL_KEYWORD,
            filename
        )
        
        # CONVERT TO NATIVE MODEL
        lookup : Dict[core.su.EnumSurrogateAlgorithm, Type[core.su.models.BaseSurrogateModel]]= {
                core.su.EnumSurrogateAlgorithm.RNN_TS: core.su.models.PyTorchSurrogateModel
        }
        model_cls = lookup[metadata.surrogate_algo]
        raw_model = model_cls.deserialize_native_model(model_serialized)
        
        # RETURN MODEL
        datatransformer = self.get_datatransformer(user_ref, atlas_ref)

        return model_cls(
            raw_model,
            datatransformer
        )
