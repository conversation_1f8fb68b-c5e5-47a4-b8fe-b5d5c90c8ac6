from __future__ import annotations

from backend.core._atlas.valueobjects import VOSensor
from ._imports import *

from pydantic import BaseModel, Field

# MAPPERS


def get_subclasses(parent_class: Type) -> List[Type[core.at.BaseGenericEnum]]:
    """
    Given a parent class, return a list of all its subclasses, including nested ones"""
    classes_to_check = [parent_class]
    subclasses = []
    while classes_to_check:
        node = classes_to_check.pop()
        subclasses.extend(node.__subclasses__())
        classes_to_check.extend(node.__subclasses__())

    return list(set(subclasses))


class AbstractHandler(ABC):
    @classmethod
    @abstractmethod
    def serialize(cls, ref, atlas: core.at.AtlasRoot, **kwargs) -> str:
        """Convert domain object to string"""
        pass

    @classmethod
    @abstractmethod
    def deserialize(cls, ref: str, atlas: core.at.AtlasRoot, **kwargs) -> Any:
        """Convert string to domain object"""
        pass


class HandleEnum(AbstractHandler):
    @classmethod
    def serialize(
        cls, ref: core.at.BaseGenericEnum, atlas: core.at.AtlasRoot, **kwargs
    ) -> str:
        return ref.stringify

    @classmethod
    def deserialize(
        cls, ref: str, atlas: core.at.AtlasRoot, **kwargs
    ) -> core.at.BaseGenericEnum:

        enum_classes = get_subclasses(core.at.BaseSpecEnum)

        _enum = None
        enums_checked: List[Type[core.at.BaseGenericEnum]] = []
        while enum_classes:

            # Defensive
            if _enum != None:
                break

            enum_cls: Type[core.at.BaseGenericEnum] = enum_classes.pop()
            try:
                # Found
                _enum = enum_cls.from_stringify(ref)
            except Exception as e:
                # Unknown Error
                enums_checked.append(enum_cls)

        if _enum == None:
            raise ValueError(
                f"Error - Can't find {ref}. Enums checked: {enums_checked}"
            )

        return _enum


class HandleVOCompound(AbstractHandler):
    @classmethod
    def serialize(
        cls, ref: core.at.VOCompound, atlas: core.at.AtlasRoot, **kwargs
    ) -> str:
        return ref.id

    @classmethod
    def deserialize(
        cls, ref: str, atlas: core.at.AtlasRoot, **kwargs
    ) -> core.at.VOCompound:
        return core.at.CompoundRef.get_vocompound_from_label(ref)


class HandleCollection(AbstractHandler):
    MAPPER: Dict[Type[core.at.AbstractVariableCollection], str] = {
        core.at.VarCollectionCompoundMassRatio: "varcollection_compoundmassmix",
        core.at.VarCollectionContinuous: "varcollection_contvar",
        core.at.VarCollectionDiscreteSet: "varcollection_discrete",
        core.at.VarCollectionReactionStoich: "varcollection_reactionstoich",
        # core.at.VarCollectionSplitter: "varcollection_splitter",
    }

    @classmethod
    def serialize(
        cls,
        ref: Type[core.at.AbstractVariableCollection],
        atlas: core.at.AtlasRoot,
        **kwargs,
    ) -> str:
        return cls.MAPPER[ref]

    @classmethod
    def deserialize(
        cls, ref: str, atlas: core.at.AtlasRoot, **kwargs
    ) -> Type[core.at.AbstractVariableCollection]:
        reverse_map = {v: k for k, v in cls.MAPPER.items()}
        return reverse_map[ref]


class HandleCollectionKey(AbstractHandler):

    # Mapper is a dict of all possible key types
    MAPPER: Dict[Type, Type] = {
        core.at.BaseGenericEnum: HandleEnum,
        core.at.VOCompound: HandleVOCompound,
    }

    @classmethod
    def serialize(
        cls, ref: core.at.VOBaseVariable, atlas: core.at.AtlasRoot, **kwargs
    ) -> str:
        collection: core.at.AbstractVariableCollection = kwargs.pop("collection")
        key = collection._get_key(ref)
        if isinstance(key, core.at.BaseGenericEnum):
            key_type = core.at.BaseGenericEnum
        else:
            key_type = core.at.VOCompound

        return cls.MAPPER[key_type].serialize(key, atlas)

    @classmethod
    def deserialize(cls, ref: str, atlas: core.at.AtlasRoot, **kwargs):
        """
        Brute force search for match of string to VOCompound or Enum
        """

        key = None
        for handler in cls.MAPPER.values():
            if key != None:
                break
            try:
                key = handler.deserialize(ref, atlas, **kwargs)
            except:
                pass

        if key == None:
            raise KeyError(f"{ref} not found. Check deserlization")

        return key


########################################


class DTO_Compound(BaseModel):
    uid: UUID
    id: str
    label: str
    description: str

    @classmethod
    def hydrate(
        cls, compound: core.at.VOCompound, atlas: core.at.AtlasRoot, **kwargs
    ) -> DTO_Compound:
        return cls(
            uid=compound.uid,
            id=compound.id,
            label=compound.label,
            description=compound.description,
        )

    def bootstrap(self, atlas: core.at.AtlasRoot, **kwargs) -> core.at.VOCompound:
        vo = core.at.VOCompound(compound_id=self.id, label=self.label)
        vo._uid = self.uid
        return vo


class DTO_KPI(BaseModel):
    label: str
    uuid_expression: str
    label_expression: str

    @classmethod
    def hydrate(cls, ref: core.at.KPI, atlas: core.at.AtlasRoot, **kwargs) -> DTO_KPI:
        return cls(
            label=ref.label,
            uuid_expression=ref.get_uuid_expression(),
            label_expression=ref.get_ui_expression(),
        )

    def bootstrap(self, atlas: core.at.AtlasRoot, **kwargs):
        kpi = core.at.KPI(
            label=self.label,
            expression=self.label_expression,
            collection_reference=atlas.variables_collection,
        )
        atlas.kpi_collection.add_item(kpi)


class DTO_Sensor(BaseModel):
    uid: UUID
    label: str
    variable_uid: UUID

    @classmethod
    def hydrate(cls, ref: core.at.VOSensor):
        return cls(
            uid=ref.uid,
            label=ref.label,
            variable_uid=ref.variable_uid,
        )

    def bootstrap(self, atlas: core.at.AtlasRoot) -> core.at.VOSensor:
        try:
            variable = atlas.variables_collection.get_item(self.variable_uid)
        except Exception as e:
            raise ValueError(
                f"Sensor bootstrap failed, could not find associated variable. Check func: {e}"
            )

        # Convert to VOSensor and add to sensor_collection
        sensor = VOSensor(
            uid=self.uid,
            label=self.label,
            variable_uid=self.variable_uid,
        )
        atlas.sensor_collection.add_item(sensor)

        return sensor


class DTO_Variable(BaseModel):

    uid: uuid.UUID
    collection: str
    var_key: str  # 75-23-11 or HeatLoad
    val: Union[
        str, float, None
    ]  # float (54.2), cas = str(52-23-34), discreteitem = "TempLoad"
    bounds: Optional[Tuple[Optional[float], Optional[float]]]

    @classmethod
    def hydrate(
        cls, ref: core.at.VOBaseVariable, atlas: core.at.AtlasRoot, **kwargs
    ) -> DTO_Variable:
        collection: core.at.AbstractVariableCollection = kwargs.pop("collection")

        collection_label = HandleCollection.serialize(type(collection), atlas)
        var_key = HandleCollectionKey.serialize(ref, atlas, collection=collection)

        if isinstance(collection, core.at.VarCollectionContinuous):
            val = ref.value
            bounds = ref.bounds

        elif isinstance(collection, core.at.VarCollectionDiscreteSet):
            _val = ref.value
            if isinstance(_val, core.at.BaseSpecEnum):
                val = HandleEnum.serialize(_val, atlas)
            elif isinstance(_val, str):
                val = _val
            elif isinstance(_val, core.at.VOCompound):
                val = HandleVOCompound.serialize(_val, atlas)
            elif _val == None:
                val = None
            else:
                raise TypeError(f"`{type(_val)}` is not accounted for. Please check.")
            bounds = None

        elif isinstance(collection, core.at.VarCollectionCompoundMassRatio):
            val = ref.value
            bounds = None

        elif isinstance(collection, core.at.VarCollectionReactionStoich):
            val = ref.value
            bounds = None
        else:
            raise TypeError(f"{ref}, {type(ref)}, is not accounted for. Check hydrate")

        return cls(
            uid=ref.uid,
            collection=collection_label,
            var_key=var_key,
            val=val,
            bounds=bounds,
        )

    def bootstrap(self, atlas: core.at.AtlasRoot, **kwargs):
        entity = kwargs.pop("entity")
        assert isinstance(entity, (core.at.ENTBase))

        _collection = HandleCollection.deserialize(self.collection, atlas, **kwargs)
        var_key_deserialized = HandleCollectionKey.deserialize(
            self.var_key, atlas, collection=_collection, **kwargs
        )

        # GET THE VARIABLE
        # For variable collections, we need to add it in
        if issubclass(_collection, core.at.VarCollectionCompoundMassRatio):
            assert isinstance(self.val, float)
            var = core.at.VOCompoundMassRatio(var_key_deserialized, self.val)
            entity.add_variable(
                var
            )  # NOTE - adding here so that uuid is NOT reset downstream.

        elif issubclass(_collection, core.at.VarCollectionReactionStoich):
            assert isinstance(self.val, float)
            var = core.at.VOReactionStoich(var_key_deserialized, self.val)
            entity.add_variable(
                var
            )  # NOTE - adding here so uuid is not reset downstream

        # This is static collections that are fixed.
        elif issubclass(
            _collection,
            (core.at.VarCollectionContinuous, core.at.VarCollectionDiscreteSet),
        ):
            var = entity.get_variable(var_key_deserialized)

        # Safety in case atlas updated
        else:
            raise TypeError(f"Collection not accounted for: {_collection}")

        # Set variable UID
        var._uid = self.uid

        # SET VARIABLE VALUE
        # Collections with numeric val
        if issubclass(_collection, core.at.VarCollectionContinuous):
            val = self.val
            bounds = self.bounds
            entity.set_value(var_key_deserialized, val, override_all_policies=True)
            entity.set_bounds(var_key_deserialized, bounds)
            return

        # Collections with polymorphic vals (note: they don't need bounds to be set)
        elif issubclass(_collection, core.at.VarCollectionDiscreteSet):

            # Fence - None is valid
            if self.val == None:
                entity.set_value(
                    var_key_deserialized, self.val, override_all_policies=True
                )
                return

            if isinstance(self.val, (float, int)):
                entity.set_value(
                    var_key_deserialized, self.val, override_all_policies=True
                )
                return

            # Trial if compound or enum
            _val = None
            funcs = [HandleEnum.deserialize, HandleVOCompound.deserialize]
            for func in funcs:

                # Exist on success
                if _val != None:
                    break

                try:
                    _val = func(self.val, atlas)
                except Exception as e:
                    pass

            # Set Val - if none found, try as string. if not, set
            if _val is None:
                try:
                    # Edgecase: conversion expressions
                    entity.set_value(
                        var_key_deserialized, self.val, override_all_policies=True
                    )
                except Exception as e:
                    raise ValueError(f"bootstrap failed. Check func: {e}")

            else:
                entity.set_value(var_key_deserialized, _val, override_all_policies=True)
            return

        # Manage variables which are Dynamic ( compound driven )
        elif issubclass(_collection, core.at.VarCollectionCompoundMassRatio):
            return

        elif issubclass(_collection, core.at.VarCollectionReactionStoich):
            return

        else:
            raise TypeError(
                f"{self.collection}.{self.val}, is not accounted for. Check bootstrap"
            )


class DTO_Equipment(BaseModel):
    entity_type: str
    label: str
    uid: UUID
    states: List[DTO_Variable]

    @classmethod
    def hydrate(
        cls, ref: core.at.ENTBaseEquipment, atlas: core.at.AtlasRoot, *kwargs
    ) -> DTO_Equipment:
        states: List[DTO_Variable] = []
        for collection in ref.get_collections():
            for var in collection.items:
                states.append(DTO_Variable.hydrate(var, atlas, collection=collection))

        return cls(
            entity_type=ref.entity_type, label=ref.label, uid=ref.uid, states=states
        )

    def bootstrap(self, atlas: core.at.AtlasRoot, **kwargs) -> core.at.ENTBaseEquipment:
        _cls = core.at.EQP_STRATEGY[self.entity_type]

        entity = atlas.add_equipment(_cls, label=self.label, uuid_str=str(self.uid))
        for state in self.states:
            var = state.bootstrap(atlas, entity=entity)
        return entity


class DTO_Reaction(BaseModel):
    entity_type: str
    label: str
    uid: UUID
    states: List[DTO_Variable]
    equipment_uuids: List[UUID]
    equipment_labels: List[str]

    @classmethod
    def hydrate(
        cls, ref: core.at.ConversionReaction, atlas: core.at.AtlasRoot, **kwargs
    ):
        states: List[DTO_Variable] = []
        for collection in ref.get_collections():
            for var in collection.items:
                states.append(DTO_Variable.hydrate(var, atlas, collection=collection))

        equip_uuids = [equipment.uid for equipment in list(ref._associated_equipment)]
        equip_labels = [
            equipment.label for equipment in list(ref._associated_equipment)
        ]

        return cls(
            entity_type=ref.entity_type,
            label=ref.label,
            uid=ref.uid,
            states=states,
            equipment_uuids=equip_uuids,
            equipment_labels=equip_labels,
        )

    def bootstrap(self, atlas: core.at.AtlasRoot, **kwargs):
        entity = core.at.ConversionReaction(self.label, uuid_str=str(self.uid))

        for state in self.states:
            state.bootstrap(atlas, entity=entity)

        for label in self.equipment_labels:
            reactor = atlas.get_equipment(by_label=label)
            assert isinstance(reactor, core.at.ConversionReactor)
            atlas.add_reaction_to_reactor(reactor, entity)
        return entity


class DTO_Stream(BaseModel):
    entity_type: str
    uid: UUID
    connection_uuid: Tuple[UUID, UUID]
    connection_label: Tuple[str, str]
    stream_label: Optional[str]
    states: List[DTO_Variable]

    @staticmethod
    def get_stream_type(ref: str) -> Type:
        MAPPER = {
            "InputStream": core.at.InputStream,
            "MaterialStream": core.at.MaterialStream,
            "OutputStream": core.at.OutputStream,
        }
        return MAPPER[ref]

    @classmethod
    def hydrate(
        cls, ref: core.at.ENTBaseStream, atlas: core.at.AtlasRoot, **kwargs
    ) -> DTO_Stream:
        up, down = atlas.get_stream_connections(ref.label)
        connection_label = (up, down)
        connection_uuid = (
            atlas.get_equipment(by_label=up).uid,
            atlas.get_equipment(by_label=down).uid,
        )

        states: List[DTO_Variable] = []
        for collection in ref.get_collections():
            for var in collection.items:
                states.append(DTO_Variable.hydrate(var, atlas, collection=collection))
        return cls(
            entity_type=ref.entity_type,
            uid=ref.uid,
            connection_uuid=connection_uuid,
            connection_label=connection_label,
            stream_label=ref.label,
            states=states,
        )

    def bootstrap(self, atlas: core.at.AtlasRoot, **kwargs) -> core.at.ENTBaseStream:
        entity = atlas.add_stream(
            self.connection_label[0],
            self.connection_label[1],
            stream_type=self.get_stream_type(self.entity_type),
            uuid_str=str(self.uid),
        )
        states = self.states

        for state in states:
            var = state.bootstrap(atlas, entity=entity)
        return entity


class DTO_Atlas(BaseModel):

    # Metadata
    label: str
    description: str
    version: int
    uid: UUID
    plant_id: str
    user_config: Optional[Dict[str, Any]] = Field(
        default_factory=lambda: {
            "user_industry": core.at.UserIndustryEnum.CHEMICAL,
            "matrix_engine": core.at.MatrixEngineEnum.DWSIM,
        }
    )
    sensor_timeset_col: Optional[str] = Field(None)
    sensor_timestep_col: Optional[str] = Field(None)
    # NOTE - default values are used for backward compatibility. in future, this should be refactored to include schema versioning and explicit handling.

    # Layer 1
    compounds: List[DTO_Compound]

    # Layer 2
    equipments: List[DTO_Equipment]
    streams: List[DTO_Stream]

    # Layer 3
    reactions: List[DTO_Reaction]
    kpis: List[DTO_KPI]
    sensors: Optional[List[DTO_Sensor]] = Field(None)

    @classmethod
    def hydrate(cls, atlas: core.at.AtlasRoot, **kwargs) -> "DTO_Atlas":
        # Handle atlas_config/user_config naming difference and convert to dict
        user_config_dict = (
            atlas.user_config.model_dump()
            if atlas.user_config
            else {"user_industry": "CHEMICAL", "matrix_engine": "DWSIM"}
        )
        sensor_timeset_col = atlas.sensor_timeset_col
        sensor_timestep_col = atlas.sensor_timestep_col

        # Initialize Reactions and KPIs as empty lists
        constructor_kwargs = defaultdict(list)
        constructor_kwargs["compounds"] = []
        constructor_kwargs["equipments"] = []
        constructor_kwargs["streams"] = []
        constructor_kwargs["reactions"] = []
        constructor_kwargs["kpis"] = []
        constructor_kwargs["sensors"] = []

        # Create Compounds
        compounds = atlas.compounds_collection.items
        for compound in compounds:
            constructor_kwargs["compounds"].append(
                DTO_Compound.hydrate(compound, atlas)
            )

        # Create Equipments
        for label in atlas.equipments:
            entity = atlas.get_equipment(by_label=label)
            constructor_kwargs["equipments"].append(
                DTO_Equipment.hydrate(entity, atlas)
            )

        # Create Streams
        for label in atlas.streams:
            entity = atlas.get_stream(by_label=label)
            constructor_kwargs["streams"].append(DTO_Stream.hydrate(entity, atlas))

        # Create Reactions
        for reaction in atlas.reaction_collection.items:
            assert isinstance(reaction, core.at.ConversionReaction)
            constructor_kwargs["reactions"].append(
                DTO_Reaction.hydrate(reaction, atlas)
            )

        # Create KPIs
        for kpi in atlas.kpi_collection.items:
            constructor_kwargs["kpis"].append(DTO_KPI.hydrate(kpi, atlas))

        # Create Sensors
        for sensor in atlas.sensor_collection.items:
            constructor_kwargs["sensors"].append(DTO_Sensor.hydrate(sensor))

        return cls(
            # Metadata
            label=atlas.label,
            description=atlas.description,
            uid=atlas.uid,
            version=atlas.version,
            plant_id=atlas.plant_id,
            user_config=user_config_dict,
            sensor_timeset_col=sensor_timeset_col,
            sensor_timestep_col=sensor_timestep_col,
            **constructor_kwargs,
        )

    def bootstrap(self) -> core.at.AtlasRoot:
        user_config = core.at.UserAtlasConfig.model_validate(self.user_config)

        # Create atlas
        atlas = core.at.AtlasRoot(
            label=self.label,
            plant_id=self.plant_id,
            description=self.description,
            user_config=user_config,
            uuid_str=str(self.uid),
        )
        atlas._uid = self.uid

        # Create chems
        for item in self.compounds:
            atlas.register_compound(item.bootstrap(atlas))

        # Create Equipments
        for item in self.equipments:
            item.bootstrap(atlas)

        # Create Streams & Connections
        for item in self.streams:
            item.bootstrap(atlas)

        # Create Reactions
        for item in self.reactions:
            item.bootstrap(atlas)

        # Setup Variable Collection
        # NOTE - this is needed before KPIS are added
        atlas.propogate_compounds_across_streams()
        atlas.propogate_vars_to_uuid_collection()

        # Create KPIs
        for item in self.kpis:
            item.bootstrap(atlas)

        # Create Sensors and set time-related columns
        atlas.sensor_timeset_col = self.sensor_timeset_col
        atlas.sensor_timestep_col = self.sensor_timestep_col

        if self.sensors:
            for item in self.sensors:
                item.bootstrap(atlas)

        return atlas


######################3###########3###########3###########3###########3

# SERIALIZATION CLASS


class AtlasJSONSerializer:
    """Serializes Atlas objects to/from JSON"""

    @staticmethod
    def serialize(obj: core.at.AtlasRoot) -> str:
        """Serialize Atlas object to JSON string"""
        dto = DTO_Atlas.hydrate(obj)
        return dto.model_dump_json()

    @staticmethod
    def serialize_to_dict(obj: core.at.AtlasRoot) -> Dict[str, Any]:
        """Serialize to dictionary"""
        dto = DTO_Atlas.hydrate(obj)
        return dto.model_dump()

    @staticmethod
    def deserialize(data: Union[str, dict]) -> core.at.AtlasRoot:
        """Deserialize JSON data to Atlas object

        Args:
            data: Either a JSON string or a dict
        """
        if isinstance(data, str):
            # Direct JSON string validation
            dto = DTO_Atlas.model_validate_json(data)
        else:
            # Dict validation
            dto = DTO_Atlas.model_validate(data)

        return dto.bootstrap()
