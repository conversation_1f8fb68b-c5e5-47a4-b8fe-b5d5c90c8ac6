from ._imports import *

from pathlib import Path
import sqlite3


class ConsoleLogger(core.ILogging):
    """Logger that outputs to console/stdout"""

    def __init__(self) -> None:
        self._format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        self._level = logging.INFO

    def setup(self) -> None:
        super().setup()

        # Configure root logger with console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self._level)
        console_handler.setFormatter(logging.Formatter(self._format))

        logging.basicConfig(
            level=self._level,
            format=self._format,
            handlers=[console_handler],
        )


class FileLogger(core.ILogging):
    def __init__(self, path: str = "backend/logs/app.log") -> None:
        self._file_path = Path(path)
        self._format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        self._level = logging.INFO

    def setup(self) -> None:
        super().setup()

        # Create log file directory if needed
        self._file_path.parent.mkdir(parents=True, exist_ok=True)

        # Configure root logger to log to file.
        logging.basicConfig(
            level=self._level,
            format=self._format,
            handlers=[logging.FileHandler(self._file_path)],
        )


class SQLiteHandler(logging.Handler):
    """Custom logging handler that writes to SQLite database"""

    def __init__(self, db_path: str, table_name: str) -> None:
        super().__init__()
        self.db_path = db_path
        self.table_name = table_name
        self._init_db()

    def _init_db(self) -> None:
        """Initialise SQLite database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                f"""
                CREATE TABLE IF NOT EXISTS {self.table_name} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    level TEXT,
                    function_name TEXT,
                    message TEXT
                )
            """
            )
        print("Logging DB Created!", flush=True)

    def emit(self, record: logging.LogRecord):
        """Overriden method to emit log records"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute(
                f"INSERT INTO {self.table_name} (timestamp, level, function_name, message) VALUES (?, ?, ?, ?)",
                (
                    datetime.fromtimestamp(record.created).isoformat(),
                    record.levelname,
                    record.funcName,
                    self.format(record),
                ),
            )


class SQLiteLogger(core.ILogging):
    def __init__(self, path: str = "backend/logs/app_logs.db") -> None:
        self._db_path = Path(path)
        self._table_name = "logs"
        self._format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        self._level = logging.INFO

        # Create log file directory if needed
        self._db_path.parent.mkdir(parents=True, exist_ok=True)

    def setup(self) -> None:
        super().setup()

        # Define SQLite log handler
        handler = SQLiteHandler(str(self._db_path), self._table_name)
        handler.setLevel(self._level)
        handler.setFormatter(logging.Formatter(self._format))

        # Configure root logger to use SQLite handler
        logging.basicConfig(level=self._level, format=self._format, handlers=[handler])
