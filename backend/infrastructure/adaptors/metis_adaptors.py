from ._imports import *


class LocalMetisDev(core.IMetis):
    """
    Local implementation of Metis interface that uses in-memory data processing.

    This adapter simply passes through to the core IMetis implementation without
    any remote service calls or additional transformations.
    """

    def __init__(
        self,
    ):
        """
        Initialize the local Metis implementation with configurable components.
        """
        # Pass component instances directly to parent constructor
        super().__init__(
            sample_generator=core.me.SampleGenerator.create_dev_config(),
            diagnostics_transformer=core.me.z_DiagnosticDataTransformer.create_dev_config(),
            importance_analyzer=core.me.FeatureSensitivityAnalyzer.create_dev_config(),
        )


class LocalMetisProduction(core.IMetis):
    """
    Local implementation of Metis interface that uses in-memory data processing.

    This adapter simply passes through to the core IMetis implementation without
    any remote service calls or additional transformations.
    """

    def __init__(
        self,
    ):
        """
        Initialize the local Metis implementation with configurable components.
        """
        # Pass component instances directly to parent constructor
        super().__init__(
            sample_generator=core.me.SampleGenerator.create_production_config(),
            diagnostics_transformer=core.me.z_DiagnosticDataTransformer.create_production_config(),
            importance_analyzer=core.me.FeatureSensitivityAnalyzer.create_production_config(),
        )
