from ._imports import *

from httpx import HTTPError
from concurrent.futures import ThreadPoolExecutor
import asyncio

#############################


class MatrixLocalSerialRunner(core.IMatrix):
    def run_simulations(
        self,
        simulator_object: core.ma.BaseMatrixSimulator,
        df_samples: pd.DataFrame,
        kpis: List[core.at.KPI],
    ) -> pd.DataFrame:

        # Assert it has an attached atlas model
        if simulator_object._atlas is None:
            raise AttributeError(f"Missing Atlas Model: matrix needs an atlas model")

        # Iterate through dataframe
        responses = defaultdict(list)

        for idx, row in df_samples.iterrows():
            try:
                # Deserialize values
                simulator_object._atlas.deserialize_values(row)

                # Run simulation
                try:
                    simulator_object.run_matrix()
                except Exception as e:
                    raise core.MatrixSimulationRunError(row, e)

                # Calculate KPIs
                for kpi in kpis:
                    try:
                        responses[str(kpi.uid)].append(kpi.get_kpi_value())
                    except Exception as e:
                        raise core.MatrixResponseError(row, kpi.label, e)

            except (core.MatrixSimulationRunError, core.MatrixResponseError):
                # Re-raise our custom errors without wrapping
                raise

            except Exception as e:
                # Wrap unexpected errors with context
                raise core.MatrixError(
                    f"Unexpected error at simulation index {idx}: {str(e)}"
                ) from e

        # Create response DataFrame
        try:
            df_responses = pd.DataFrame(responses)
            return df_responses

        except Exception as e:
            raise core.MatrixError(
                f"Failed to create results DataFrame: {str(e)}"
            ) from e


class MatrixPrefectRunner(core.IMatrix):

    def __init__(self):
        # Run initializations for Prefect
        try:
            print("Create Prefect blocks and dependencies...", flush=True)
            self._run_async_in_thread(oc.create_azure_blob_storage_block)
            self._run_async_in_thread(oc.create_aci_workpool)
            self._run_async_in_thread(oc.PrefectDeploymentManager.deploy_flows)
            print("Created Prefect dependencies!", flush=True)
        except Exception as e:
            raise core.MatrixError(
                f"Failed to initialize Prefect in MatrixPrefectRunner: {str(e)}"
            )

    def _run_async_in_thread(self, coroutine_func):
        with ThreadPoolExecutor() as executor:
            future = executor.submit(lambda: asyncio.run(coroutine_func()))
            return future.result()

    # TODO This only accounts for single KPI
    def run_simulations(
        self,
        simulator_object: core.ma.BaseMatrixSimulator,
        df_samples: pd.DataFrame,
        kpis: List[core.at.KPI],
    ) -> pd.DataFrame:

        # Assert it has an attached atlas model
        if simulator_object._atlas is None:
            raise AttributeError(f"Missing Atlas Model: matrix needs an atlas model")

        if df_samples.empty:
            raise AttributeError(f"Missing samples: matrix needs at least one sample")

        try:
            chunk_size: int = 10
            chunks: List[Tuple[int, int]] = [
                (n * chunk_size, n * chunk_size + chunk_size)
                for n in range(len(df_samples) // chunk_size + 1)
            ]
            params_list = [
                dict(
                    simulation_id=simulator_object.label,
                    atlas_json=db.AtlasJSONSerializer.serialize(
                        simulator_object._atlas
                    ),
                    kpi_name=kpis[0].label,  # TODO Change to multiple KPIs
                    sample_dict=df_samples.iloc[left_index:right_index]
                    .reset_index(drop=True)
                    .to_dict(),
                )
                for left_index, right_index in chunks
            ]
            with ThreadPoolExecutor() as executor:
                future = executor.submit(
                    lambda: asyncio.run(
                        oc.SimulationChunked.run_deployments(params_list=params_list)
                    )
                )
                responses_list = future.result()
                print(f"KPI results = {responses_list}", flush=True)

        except (core.MatrixSimulationRunError, core.MatrixResponseError, HTTPError):
            # Re-raise our custom errors without wrapping
            raise

        # Create response DataFrame
        try:
            responses = {str(kpis[0].uid): responses_list}
            df_responses = pd.DataFrame(responses)
            return df_responses

        except Exception as e:
            raise core.MatrixError(
                f"Failed to create results DataFrame: {str(e)}"
            ) from e
