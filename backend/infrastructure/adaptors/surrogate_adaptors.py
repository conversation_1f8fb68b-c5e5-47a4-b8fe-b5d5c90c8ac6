from ._imports import *
import backend.infrastructure._db.repo as repo
from backend.infrastructure.runners.surrogate_trainers.runner_azureml import AzureMLRunnerFactory
from backend.infrastructure.runners.surrogate_trainers.runner_local import SurrogateTrainingLocalRunner

class LocalSurrogate(core.ISurrogate):
    """
    Local implementation of ISurrogate interface that uses local training execution.

    This adapter provides a complete local implementation of the surrogate training
    system, using filesystem-based model registry and local training execution.
    It follows hexagonal architecture principles by injecting concrete implementations
    of the required ports.
    """

    def __init__(
        self,
    ):
        """
        Initialize the local surrogate implementation with local components.

        This constructor sets up the complete dependency injection for local execution:
        - SurrogateFilesystemRegistry for model/job persistence
        - LocalRunner for training execution
        """
        # Create local runner instance
        training_runner = SurrogateTrainingLocalRunner()

        # Pass component instances directly to parent constructor
        super().__init__(
            model_repo=repo.SurrogateFolderRepo(),
            training_runner=training_runner
        )


class AzureSurrogate(core.ISurrogate):
    """
    Azure implementation of ISurrogate interface that uses Azure ML for training execution.

    This adapter provides a complete cloud-based implementation of the surrogate training
    system, using Azure ML for training execution and maintaining compatibility with
    the existing ISurrogate interface.
    
    It still uses the local filesystem for model/job persistence. In the future, we can
    replace this with an Azure Blob Storage-based repository.
    
    NOTE the provision is currently done at server initialization and is not dynamic. Update trainer_registry for dyanmic behaviour 
    """

    def __init__(
        self,
    ):
        """
        Initialize the Azure surrogate implementation with cloud components.

        This constructor sets up the complete dependency injection for Azure ML execution:
        - SurrogateFilesystemRegistry for model/job persistence (or custom repo)
        - AzureMLRunner for cloud training execution
        - Azure ML specific configuration
        - TrainerRegistry for consistent algorithm mapping
        """
        training_runner = AzureMLRunnerFactory().create()

        # Pass component instances directly to parent constructor
        super().__init__(
            model_repo=repo.SurrogateFolderRepo(),
            training_runner=training_runner
        )
