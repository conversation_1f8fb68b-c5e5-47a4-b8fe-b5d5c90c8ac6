from ._imports import *


class LocalAthenaDev(core.IAthena):
    """
    Light implementation of the Athena diagnosticss
    """

    def __init__(
        self,
    ):
        """
        Initialize the local Metis implementation with configurable components.
        """
        # Pass component instances directly to parent constructor
        super().__init__(
                diagnostic_threshold=5.0
        )

class LocalAthenaProduction(core.IAthena):
    """
    Light implementation of the Athena diagnosticss
    """

    def __init__(
        self,
    ):
        """
        Initialize the local Metis implementation with configurable components.
        """
        # Pass component instances directly to parent constructor
        super().__init__(
                diagnostic_threshold=5.0
        )