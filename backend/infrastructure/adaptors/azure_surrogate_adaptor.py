"""
Azure Surrogate Adapter Implementation

This module provides the AzureSurrogate adapter that integrates Azure ML
training execution with the ISurrogate interface. It follows hexagonal
architecture principles by injecting the AzureMLRunner into the core domain.

Design Philosophy (<PERSON> × <PERSON>):
- **Dependency Injection**: Clean separation between core domain and infrastructure
- **Configuration Management**: Centralized Azure ML configuration
- **Fault Tolerance**: Robust error handling for cloud operations
- **Operational Excellence**: Comprehensive logging and monitoring
"""

import logging
from typing import Optional

import backend.core.interfaces.surrogate_interfaces as core
import backend.infrastructure._db.repo as repo
from backend.infrastructure.runners.surrogate_trainers.runner_azureml import AzureMLTrainingRunner, VOAzureMLComputeConfig


class AzureSurrogate(core.ISurrogate):
    """
    Azure implementation of ISurrogate interface that uses Azure ML for training execution.

    This adapter provides a complete cloud-based implementation of the surrogate training
    system, using Azure ML for training execution and maintaining compatibility with
    the existing ISurrogate interface.
    
    Key Features:
    - **Cloud Execution**: Leverages Azure ML compute clusters for training
    - **Scalability**: Automatic scaling based on workload demands
    - **Cost Optimization**: Efficient resource utilization and management
    - **Monitoring**: Comprehensive tracking of cloud training jobs
    """

    def __init__(self,
                 azure_config: Optional[VOAzureMLComputeConfig] = None,
                 model_repo: Optional[core.SurrogateRegistryPort] = None,
                 trainer_registry: Optional[core.trainers.TrainerRegistry] = None):
        """
        Initialize the Azure surrogate implementation with cloud components.

        This constructor sets up the complete dependency injection for Azure ML execution:
        - SurrogateFilesystemRegistry for model/job persistence (or custom repo)
        - AzureMLRunner for cloud training execution
        - Azure ML specific configuration
        - TrainerRegistry for consistent algorithm mapping

        Args:
            azure_config: Azure ML specific configuration. If None, uses defaults.
            model_repo: Custom model repository. If None, uses SurrogateFilesystemRegistry.
            trainer_registry: Optional TrainerRegistry instance. If None, uses default registry.
        """
        self.logger = logging.getLogger(self.__class__.__name__)

        # Create Azure ML configuration with defaults if not provided
        self.azure_config = azure_config or VOAzureMLComputeConfig()
        self.logger.info(f"Initialized Azure configuration for experiment: {self.azure_config.experiment_name}")

        # Create Azure ML training runner instance with trainer registry
        training_runner = AzureMLTrainingRunner(
            compute_configuration=self.azure_config,
            trainer_registry=trainer_registry
        )

        # Use provided model repository or default to filesystem
        model_repository = model_repo or repo.SurrogateFolderRepo()

        # Initialize parent with injected dependencies
        super().__init__(
            model_repo=model_repository,
            training_runner=training_runner
        )

        self.logger.info("AzureSurrogate initialized successfully with Azure ML runner")

    def setup_cloud_infrastructure(self) -> None:
        """
        Set up Azure ML infrastructure for training execution.
        
        This method provides a convenient way to ensure all Azure ML
        infrastructure is properly configured before training execution.
        It delegates to the underlying AzureMLRunner's setup_infrastructure method.
        
        Raises:
            SurrogateInfrastructureError: If Azure ML infrastructure setup fails
        """
        self.logger.info("Setting up Azure ML infrastructure")
        self.training_runner.setup_infrastructure()
        self.logger.info("Azure ML infrastructure setup completed")

    def teardown_cloud_infrastructure(self) -> None:
        """
        Clean up Azure ML infrastructure and resources.
        
        This method provides a convenient way to clean up all Azure ML
        resources after training completion. It delegates to the underlying
        AzureMLRunner's teardown_infrastructure method.
        """
        self.logger.info("Tearing down Azure ML infrastructure")
        self.training_runner.teardown_infrastructure()
        self.logger.info("Azure ML infrastructure teardown completed")

    @classmethod
    def create_with_preset_config(cls,
                                preset: str = "default",
                                experiment_name: Optional[str] = None,
                                trainer_registry: Optional[core.trainers.TrainerRegistry] = None) -> "AzureSurrogate":
        """
        Factory method to create AzureSurrogate with preset configurations.

        This method provides convenient preset configurations for common use cases,
        following the factory pattern established in the codebase.

        Args:
            preset: Configuration preset name ("default", "high_performance", "cost_optimized")
            experiment_name: Optional custom experiment name
            trainer_registry: Optional TrainerRegistry instance. If None, uses default registry.

        Returns:
            AzureSurrogate instance with preset configuration

        Raises:
            ValueError: If preset name is not recognized
        """
        if preset == "default":
            config = VOAzureMLComputeConfig(
                compute_label="gpu-cluster",
                instance_type="Standard_NC16as_T4_v3",
                instance_count=1,
                experiment_name=experiment_name or "surrogate-training-default",
                job_timeout_minutes=120,
                max_concurrent_jobs=5
            )
        elif preset == "high_performance":
            config = VOAzureMLComputeConfig(
                compute_label="gpu-cluster-high-perf",
                instance_type="Standard_NC24ads_A100_v4",
                instance_count=2,
                experiment_name=experiment_name or "surrogate-training-high-perf",
                job_timeout_minutes=240,
                max_concurrent_jobs=10,
                auto_scale_enabled=True
            )
        elif preset == "cost_optimized":
            config = VOAzureMLComputeConfig(
                compute_label="cpu-cluster",
                instance_type="Standard_D4s_v3",
                instance_count=1,
                experiment_name=experiment_name or "surrogate-training-cost-opt",
                job_timeout_minutes=60,
                max_concurrent_jobs=3,
                auto_scale_enabled=True
            )
        else:
            raise ValueError(f"Unknown preset: {preset}. Available presets: 'default', 'high_performance', 'cost_optimized'")

        return cls(azure_config=config, trainer_registry=trainer_registry)

    def get_azure_job_status(self, job_name: str) -> dict:
        """
        Get the status of a specific Azure ML training job.
        
        This method provides access to Azure ML job monitoring capabilities,
        allowing users to check the status of cloud training jobs.
        
        Args:
            job_name: Name of the Azure ML job to check
            
        Returns:
            Dictionary containing job status information
            
        Raises:
            SurrogateTrainingError: If job status cannot be retrieved
        """
        try:
            # Access the underlying Azure ML client through the runner
            ml_client = self.training_runner._ml_client
            if ml_client is None:
                self.training_runner.setup_infrastructure()
                ml_client = self.training_runner._ml_client
            
            job = ml_client.jobs.get(job_name)
            
            return {
                "name": job.name,
                "status": job.status,
                "creation_time": job.creation_context.created_at if job.creation_context else None,
                "compute_target": job.compute if hasattr(job, 'compute') else None,
                "experiment_name": job.experiment_name if hasattr(job, 'experiment_name') else None
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get Azure ML job status for {job_name}: {e}")
            from backend.core.interfaces.surrogate_interfaces import SurrogateTrainingError
            raise SurrogateTrainingError(f"Failed to retrieve Azure ML job status", original_error=e)

    def list_recent_azure_jobs(self, limit: int = 10) -> list:
        """
        List recent Azure ML training jobs for this experiment.
        
        This method provides visibility into recent cloud training activity,
        useful for monitoring and debugging purposes.
        
        Args:
            limit: Maximum number of jobs to return
            
        Returns:
            List of dictionaries containing job information
            
        Raises:
            SurrogateTrainingError: If job list cannot be retrieved
        """
        try:
            # Access the underlying Azure ML client through the runner
            ml_client = self.training_runner._ml_client
            if ml_client is None:
                self.training_runner.setup_infrastructure()
                ml_client = self.training_runner._ml_client
            
            jobs = ml_client.jobs.list(
                experiment_name=self.azure_config.experiment_name,
                max_results=limit
            )
            
            job_list = []
            for job in jobs:
                job_info = {
                    "name": job.name,
                    "status": job.status,
                    "creation_time": job.creation_context.created_at if job.creation_context else None,
                    "display_name": job.display_name if hasattr(job, 'display_name') else None
                }
                job_list.append(job_info)
            
            return job_list
            
        except Exception as e:
            self.logger.error(f"Failed to list Azure ML jobs: {e}")
            from backend.core.interfaces.surrogate_interfaces import SurrogateTrainingError
            raise SurrogateTrainingError(f"Failed to list Azure ML jobs", original_error=e)

    def estimate_training_cost(self, 
                             training_duration_minutes: int,
                             instance_count: Optional[int] = None) -> dict:
        """
        Estimate the cost of training on Azure ML.
        
        This method provides cost estimation capabilities to help with
        budget planning and cost optimization decisions.
        
        Args:
            training_duration_minutes: Expected training duration in minutes
            instance_count: Number of instances (uses config default if None)
            
        Returns:
            Dictionary containing cost estimation details
        """
        # Simplified cost estimation (would use actual Azure pricing in full implementation)
        instances = instance_count or self.azure_config.instance_count
        
        # Rough cost estimates per hour for different instance types (USD)
        cost_per_hour = {
            "Standard_NC16as_T4_v3": 1.20,
            "Standard_NC24ads_A100_v4": 3.60,
            "Standard_D4s_v3": 0.20
        }
        
        hourly_rate = cost_per_hour.get(self.azure_config.instance_type, 1.00)
        total_cost = (training_duration_minutes / 60) * hourly_rate * instances
        
        return {
            "instance_type": self.azure_config.instance_type,
            "instance_count": instances,
            "duration_minutes": training_duration_minutes,
            "hourly_rate_usd": hourly_rate,
            "estimated_cost_usd": round(total_cost, 2),
            "note": "Estimate only - actual costs may vary"
        }


# Factory function for easy instantiation
def create_azure_surrogate(preset: str = "default",
                          experiment_name: Optional[str] = None,
                          trainer_registry: Optional[core.trainers.TrainerRegistry] = None) -> AzureSurrogate:
    """
    Convenience factory function for creating AzureSurrogate instances.

    Args:
        preset: Configuration preset name
        experiment_name: Optional custom experiment name
        trainer_registry: Optional TrainerRegistry instance. If None, uses default registry.

    Returns:
        AzureSurrogate instance
    """
    return AzureSurrogate.create_with_preset_config(
        preset=preset,
        experiment_name=experiment_name,
        trainer_registry=trainer_registry
    )
