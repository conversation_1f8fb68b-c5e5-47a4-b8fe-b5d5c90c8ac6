from ._imports import *
import backend.infrastructure._db.repo as repo



direct_optim_fast = core.op.VOOptimizationConfig(
                max_trials= 20,
                timeout_seconds = 60*5,
                strategy = core.op.EnumSearchAlgorithm.DIRECT,
                max_concurrent_trials= 1,
        )

direct_optim_comprehensive = core.op.VOOptimizationConfig(
                max_trials= 200,
                timeout_seconds = 60*5,
                strategy = core.op.EnumSearchAlgorithm.DIRECT,
                max_concurrent_trials= 1,
        )

class BasicOptimizer(core.IOptimizer):
    def __init__(self):
        super().__init__(
                base_optimization_config = direct_optim_fast
        )

class ComprehensiveOptimizer(core.IOptimizer):
    def __init__(self):
        super().__init__(
                base_optimization_config =direct_optim_comprehensive
        )
