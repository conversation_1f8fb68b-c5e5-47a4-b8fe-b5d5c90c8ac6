"""
CRUD adaptors for Atlas
"""

from __future__ import annotations
from ._imports import *

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, sessionmaker


#############################


class AtlasInMemAdaptor(core.IAtlas):

    def __init__(self):
        self._repo: Dict[core.at.AtlasMetadata, core.at.AtlasRoot] = {}
        self.add_atlas_models()

    def add_atlas_models(self):
        from backend.application.usecase_templates import (
            USER_TEMPLATES,
            SWANCOR_TEMPLATES,
        )

        for model in USER_TEMPLATES:
            meta = core.at.AtlasMetadata(
                user_id="user",
                atlas_label=model.label,
                date_created=datetime.now(),
                date_modified=datetime.now(),
            )
            self.create(model, meta)

    def create(self, atlas: core.at.AtlasRoot, metadata: core.at.AtlasMetadata):
        try:
            self._repo[metadata] = atlas
        except Exception as e:
            raise core.AtlasGeneralError(atlas.label, str(e))

    def get(
        self, atlas_label: str, user_id: str
    ) -> Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]:
        matches = [
            (m, a)
            for m, a in self._repo.items()
            if m.user_id == user_id and m.atlas_label == atlas_label
        ]

        if not matches:
            raise core.AtlasAdaptorError(user_id, atlas_label, "Atlas not found")

        # Return the first match (should be only one)
        metadata, atlas = matches[0]
        return atlas, metadata

    def list(
        self, filter_criteria: Optional[dict] = None
    ) -> List[Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]]:
        results = []
        filter_criteria = filter_criteria or {}

        for metadata, atlas in self._repo.items():
            # Use the parent class's _matches_criteria method
            if self._matches_criteria(metadata, filter_criteria):
                results.append((atlas, metadata))

        return results

    def save(self, atlas: core.at.AtlasRoot, metadata: core.at.AtlasMetadata) -> None:
        """Save changes to an existing Atlas aggregate and updates the date_modified timestamp"""
        try:
            for stored_metadata in self._repo:
                # Defensive
                if not (
                    stored_metadata.atlas_label == metadata.atlas_label
                    and stored_metadata.user_id == metadata.user_id
                ):
                    continue

                # Logic
                new_metadata = core.at.AtlasMetadata(
                    **{**stored_metadata.model_dump(), "date_modified": datetime.now()}
                )
                # Remove the old metadata key, add with the new metadata
                self._repo.pop(stored_metadata)
                self._repo[new_metadata] = atlas

                return

            # If we get here, the atlas wasn't found
            raise core.AtlasAdaptorError(
                metadata.user_id, metadata.atlas_label, "Atlas not found"
            )

        except Exception as e:
            raise core.AtlasAdaptorError(metadata.user_id, metadata.atlas_label, str(e))

    def delete(self, atlas_label: str, user_id: str):
        """Delete an Atlas aggregate by ID by marking it as deleted"""
        try:
            to_delete = []
            to_add = []
            for metadata, atlas in self._repo.items():
                # defensive
                if not (
                    metadata.atlas_label == atlas_label and metadata.user_id == user_id
                ):
                    continue

                # Mark as deleted by updating the metadata
                new_metadata = core.at.AtlasMetadata(
                    **{
                        **metadata.model_dump(),
                        "date_deleted": datetime.now(timezone.utc),
                    }
                )
                to_delete.append(metadata)
                to_add.append((new_metadata, atlas))

                break

            if not to_delete:
                raise core.AtlasAdaptorError(user_id, atlas_label, "Atlas not found")

            # Pulled out because pop not possible in loop
            for metadata in to_delete:
                self._repo.pop(metadata)
            for new_metadata, atlas in to_add:
                self._repo[new_metadata] = atlas

        except Exception as e:
            raise core.AtlasAdaptorError(atlas_label, user_id, str(e))


######################


class _AtlasUnitOfWork:
    """Unified unit of work pattern implementation for Atlas persistence.

    This class combines database configuration and session management into a single
    cohesive component, following the unit of work pattern.
    """

    # Environment variable names as class constants
    ENV_USER: ClassVar[str] = "POSTGRES_USER"
    ENV_PASSWORD: ClassVar[str] = "POSTGRES_PASSWORD"
    ENV_HOST: ClassVar[str] = "POSTGRES_HOST"
    ENV_PORT: ClassVar[str] = "POSTGRES_PORT"
    ENV_DB: ClassVar[str] = "POSTGRES_DB"
    ENV_POOL_SIZE: ClassVar[str] = "POSTGRES_POOL_SIZE"
    ENV_MAX_OVERFLOW: ClassVar[str] = "POSTGRES_MAX_OVERFLOW"

    def __init__(
        self,
        user: Optional[str] = None,
        password: Optional[str] = None,
        host: Optional[str] = None,
        port: Optional[int] = None,
        database: Optional[str] = None,
        pool_size: Optional[int] = None,
        max_overflow: Optional[int] = None,
    ):
        """Initialize the UOW with direct parameters or environment variables.

        All parameters are optional - if not provided, they'll be loaded from
        environment variables with sensible defaults.
        """
        # Load configuration (prioritizing passed parameters over env vars)
        self.user = user or os.getenv(self.ENV_USER)
        self.password = password or os.getenv(self.ENV_PASSWORD)
        self.host = host or os.getenv(self.ENV_HOST)
        self.port = port or int(os.getenv(self.ENV_PORT))  # type: ignore
        self.database = database or os.getenv(self.ENV_DB)
        self.pool_size = pool_size or int(os.getenv(self.ENV_POOL_SIZE))  # type: ignore
        self.max_overflow = max_overflow or int(os.getenv(self.ENV_MAX_OVERFLOW))  # type: ignore

        # Setup database connection with explicit timezone setting
        self._engine = create_engine(
            self._get_db_uri(), connect_args={"options": "-c timezone=UTC"}
        )
        self._session_factory = sessionmaker(bind=self._engine)
        self.repo = None
        self._session = None

        # Always ensure schema exists
        self._ensure_schema()

    # HELPERS

    def _get_db_uri(self) -> str:
        """Generate the PostgreSQL connection URI from configuration parameters."""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"

    def _ensure_schema(self):
        """Safely create tables if they don't exist."""
        db.Base.metadata.create_all(self._engine)

    # LOGIC

    def __enter__(self):
        """Context manager entry - create session and repository."""
        self._session = self._session_factory()
        self.repo = db.SQLRepoForAtlas(self._session)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - handle transaction completion."""
        if exc_type is not None:
            self.rollback()
        self._session.close()

    def commit(self):
        """Commit the current transaction."""
        self._session.commit()

    def rollback(self):
        """Roll back the current transaction."""
        self._session.rollback()


class AtlasPostgreAdaptor(core.IAtlas):
    """
    Postgres implementation of Atlas interface using the repository pattern.

    This adaptor translates between domain operations and persistence operations,
    implementing the domain interfaces while delegating actual data access to
    the SQLRepoForAtlas through a unit of work.
    """

    def __init__(self, uow: Optional[_AtlasUnitOfWork] = None):
        self.uow = uow or _AtlasUnitOfWork()

    def create(self, atlas: core.at.AtlasRoot, metadata: core.at.AtlasMetadata):
        """
        Create a new Atlas aggregate with metadata.

        Args:
            atlas: The Atlas root object to persist
            metadata: Associated metadata for the Atlas object

        Raises:
            AtlasGeneralError: If the create operation fails
            AtlasAdaptorError: If validation fails (e.g., duplicate atlas)
        """
        try:
            with self.uow as uow:
                # Verify atlas doesn't already exist with same user_id and label
                existing_atlases = uow.repo.list(filter_by=metadata.user_id)
                for existing_atlas, _ in existing_atlases:
                    if existing_atlas.label == atlas.label:
                        raise core.AtlasAdaptorError(
                            metadata.user_id,
                            atlas.label,
                            "Atlas with this label already exists",
                        )

                # Save new atlas with metadata
                updated_metadata = core.at.AtlasMetadata(
                    **{
                        **metadata.model_dump(),
                        "date_modified": datetime.now(),
                        "date_created": datetime.now(),
                    }
                )

                uow.repo.save(atlas, metadata=updated_metadata)
                uow.commit()
        except core.AtlasAdaptorError:
            raise
        except Exception as e:
            raise core.AtlasGeneralError(
                atlas.label, f"Failed to create atlas: {str(e)}"
            )

    def get(
        self, atlas_label: str, user_id: str
    ) -> Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]:
        """
        Retrieve an Atlas aggregate by label and user ID with its metadata.

        Args:
            atlas_label: The label of the atlas to retrieve
            user_id: The user owning the atlas

        Returns:
            A tuple containing (AtlasRoot, AtlasMetadata)

        Raises:
            AtlasAdaptorError: If the atlas cannot be found
        """
        try:
            with self.uow as uow:
                result = uow.repo.load(atlas_label, user_id=user_id)
                if result is None:
                    raise core.AtlasAdaptorError(
                        user_id, atlas_label, "Atlas not found"
                    )
                return result
        except core.AtlasAdaptorError:
            raise
        except Exception as e:
            raise core.AtlasGeneralError(
                atlas_label, f"Failed to retrieve atlas: {str(e)}"
            )

    def list(
        self, filter_criteria: Optional[dict] = None
    ) -> List[Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]]:
        """
        List Atlas aggregates matching the provided filter criteria.

        Args:
            filter_criteria: Dictionary containing filter conditions using AtlasFilterType values.
                Supported filters:
                - AtlasFilterType.USER_ID.value: Match atlases with the specified user ID
                - AtlasFilterType.NOT_DELETED.value: If True, match atlases that have not been deleted
                - AtlasFilterType.IS_TEMPLATE.value: Match atlases with specified template status
                - AtlasFilterType.MODIFIED_AFTER.value: Match atlases modified after the specified date
                - AtlasFilterType.CREATED_AFTER.value: Match atlases created after the specified date

        Returns:
            List of tuples containing matched atlas objects and their metadata
        """
        filter_criteria = filter_criteria or {}
        try:
            with self.uow as uow:
                user_id = filter_criteria.get(
                    core.AltasMetadataEnums.USER_ID.value, None
                )

                # if user_id is provided, return valid entries + templates
                # if user_id is NOT provided, return templates only
                results = uow.repo.list(filter_by=user_id)

                # Then apply any additional filters if needed
                filtered_results = []
                for atlas, metadata in results:
                    if self._matches_criteria(metadata, filter_criteria):
                        filtered_results.append((atlas, metadata))

            return filtered_results
        except Exception as e:
            # Use a generic error label since we're not operating on a specific atlas
            raise core.AtlasGeneralError(
                "multiple", f"Failed to list atlases: {str(e)}"
            )

    def save(self, atlas: core.at.AtlasRoot, metadata: core.at.AtlasMetadata) -> None:
        """
        Save changes to an existing Atlas aggregate.

        Updates the date_modified timestamp and persists changes to the repository.

        Args:
            atlas: The updated Atlas root object
            metadata: Associated metadata for the Atlas object

        Raises:
            AtlasAdaptorError: If the atlas cannot be found or updated
        """
        try:
            with self.uow as uow:
                # Update modified timestamp
                updated_metadata = core.at.AtlasMetadata(
                    **{
                        **metadata.model_dump(),
                        "date_modified": datetime.now(timezone.utc),
                    }
                )

                # Save to repository
                uow.repo.save(atlas, metadata=updated_metadata)
                uow.commit()

        except Exception as e:
            raise core.AtlasAdaptorError(
                metadata.user_id,
                metadata.atlas_label,
                f"Failed to save atlas: {str(e)}",
            )

    def delete(self, atlas_label: str, user_id: str):
        """
        Delete an Atlas aggregate by marking it as deleted.

        Sets the date_deleted timestamp rather than physically removing the record.

        Args:
            atlas_label: The label of the atlas to delete
            user_id: The user owning the atlas

        Raises:
            AtlasAdaptorError: If the atlas cannot be found or deleted
        """
        try:
            with self.uow as uow:
                # First retrieve the atlas to verify it exists and get the metadata
                result = uow.repo.load(atlas_label, user_id=user_id)
                if result is None:
                    raise core.AtlasAdaptorError(
                        user_id, atlas_label, "Atlas not found"
                    )

                atlas, metadata = result

                # Update metadata with deletion timestamp
                updated_metadata = core.at.AtlasMetadata(
                    **{
                        **metadata.model_dump(),
                        "date_deleted": datetime.now(timezone.utc),
                    }
                )

                # Save updated metadata
                uow.repo.save(atlas, metadata=updated_metadata)
                uow.commit()
        except core.AtlasAdaptorError:
            raise
        except Exception as e:
            raise core.AtlasGeneralError(
                atlas_label, f"Failed to delete atlas: {str(e)}"
            )
