"""
Azure ML Training Runner Implementation

This module implements the AzureMLRunner adapter that executes training jobs
on Azure Machine Learning compute clusters. It follows hexagonal architecture
principles by implementing the TrainingRunnerPort interface.

Design Philosophy (<PERSON> × Thorp):
- **Stateless execution**: Each training job is independent and reproducible
- **Fault tolerance**: Robust error handling with retry mechanisms
- **Performance isolation**: Cloud execution doesn't block core operations
- **Information preservation**: Complete tracking of cloud training execution
"""

from __future__ import annotations
import datetime
import json
import torch
import time
import hashlib
import logging
import pickle
import os
import shlex
import shutil
import tempfile
import time
import uuid
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple
import typing as T
import pydantic
import dataclasses
from dotenv import load_dotenv

# Azure ML imports
import azure.ai.ml
import azure.ai.ml.entities

from azure.ai.ml.entities import AmlCompute, Environment, ResourceConfiguration
from azure.ai.ml import MLClient, command
from azure.identity import DefaultAzureCredential
from azure.ai.ml.entities import Environment
from azure.core.exceptions import ResourceNotFoundError

# Domain Module Imports
import backend.core._surrogate as su
from backend.core.interfaces.surrogate_interfaces import (
    SurrogateTrainingError,
    SurrogateConfigError,
    SurrogateInfrastructureError
)

# Internal
from ..codepackager import CodePackager, VOCodepackagerConfig


class VOAzureMLComputeConfig(su.VOPydanticBase):
    """Azure ML specific configuration following established VO patterns"""
    
    # Compute Configuration
    compute_label: str = pydantic.Field(
        default="gpu-cluster", # gpu-cluster or cpu-cluster will use serverless compute
        description="Azure ML compute target name. Use 'gpu-cluster' or 'cpu-cluster' for serverless."
    )
    instance_type: str = pydantic.Field(
        default="Standard_NC16as_T4_v3",
        description="Azure VM instance type for training"
    )

    # Resource Management
    # NOTE - logical design here can be improved.
    instance_count: int = pydantic.Field(
        default=1,
        description="Number of compute instances"
    )
    max_concurrent_jobs: int = pydantic.Field(
        default=5,
        description="Maximum concurrent training jobs (for dedicated compute)"
    )
    auto_scale_enabled: bool = pydantic.Field(
        default=True,
        description="Enable automatic scaling of compute resources (for dedicated compute)"
    )
    
    # Environment Configuration
    environment_name: str = pydantic.Field(
        default="pydanticrrogate-training-env",
        description="Azure ML environment name"
    )
    environment_version: Optional[str] = pydantic.Field(
        default=None,
        description="Specific environment version (latest if None)"
    )
    
    # Job Configuration
    experiment_name: str = pydantic.Field(
        default="experiment",
        description="Azure ML experiment name"
    )
    job_timeout_minutes: int = pydantic.Field(
        default=120,
        description="Maximum job execution time in minutes"
    )
    idle_min_before_scale_down: int = pydantic.Field(
        default=10,
        description="Idle time before scaling down compute resources (for dedicated compute)"
    )
    
    @property
    def is_serverless(self) -> bool:
        """Check if this configuration uses serverless compute"""
        return self.compute_label in ["gpu-cluster", "cpu-cluster"]
    
    
    @classmethod
    def create(
        cls, 
        compute_type: T.Literal[
            "gpu_performant",
            "gpu_budget",
            "cpu_performant",
            "cpu_budget"
            ]
        ) -> "VOAzureMLComputeConfig":
        
        if compute_type == "gpu_performant":
            return cls(
                compute_label="gpu-cluster",
                instance_type="Standard_NC16as_T4_v3",
                instance_count=1,
                environment_name="pytorch-1.13-gpu",
                environment_version="1",
                experiment_name="gpu-performant-experiment",
                job_timeout_minutes=60*5,
                idle_min_before_scale_down=1,
                max_concurrent_jobs=4,
                auto_scale_enabled=True
            )
        elif compute_type == "gpu_budget":
            return cls(
                compute_label="gpu-cluster-budget",
                instance_type="Standard_NC8as_T4_v3",  # Smaller GPU instance for cost savings
                instance_count=1,
                environment_name="pytorch-1.13-gpu",
                environment_version="1",
                experiment_name="gpu-budget-experiment",
                job_timeout_minutes=60*2,  # Shorter timeout for budget workloads
                idle_min_before_scale_down=5,  # Scale down faster to save costs
                max_concurrent_jobs=2,  # Lower concurrency for budget
                auto_scale_enabled=True
            )
        elif compute_type == "cpu_performant":
            return cls(
                compute_label="cpu-cluster",
                instance_type="Standard_D8s_v3",  # CPU-optimized for traditional ML
                instance_count=2,
                environment_name="sklearn-1.5-cpu",
                environment_version="1",
                experiment_name="cpu-performant-experiment",
                job_timeout_minutes=60*3,  # Moderate timeout for CPU workloads
                idle_min_before_scale_down=10,  # Longer idle time for CPU efficiency
                max_concurrent_jobs=8,  # Higher concurrency for CPU workloads
                auto_scale_enabled=True
            )
        elif compute_type == "cpu_budget":
            return cls(
                compute_label="cpu-cluster-budget",
                instance_type="Standard_D8s_v3",  # Smaller CPU instance for cost savings
                instance_count=1,
                environment_name="sklearn-1.5-cpu",
                environment_version="1",
                experiment_name="cpu-budget-experiment",
                job_timeout_minutes=60*1,  # Shorter timeout for budget workloads
                idle_min_before_scale_down=15,  # Scale down faster to save costs
                max_concurrent_jobs=4,  # Lower concurrency for budget
                auto_scale_enabled=True
            )
        else:
            raise ValueError(f"{compute_type} is not valid. Available types: gpu_performant, gpu_budget, cpu_performant, cpu_budget")
    
    @classmethod
    def create_serverless(
        cls, 
        compute_type: T.Literal[
            "gpu_performant",
            "gpu_budget",
            "cpu_performant",
            "cpu_budget"
            ]
        ) -> "VOAzureMLComputeConfig":
        """
        Create a serverless compute configuration.
        Uses the configuration in create
        
        Args:
            compute_type: Type of compute resources to use
        
        Returns:
            VOAzureMLComputeConfig configured for serverless execution
        """
        if compute_type.startswith("gpu"):
            compute_label = "gpu-cluster"
        else:
            compute_label = "cpu-cluster"
        
        # Start with regular configuration
        config = cls.create(compute_type)
        config = config.model_copy(
            update={"compute_label": compute_label}, 
            deep=True
            )
        
        return config


class AzureMLClientFactory:
    """
    Credential management for creating authenticated azure ml clients. 
    
    Opted for factory instad of dataclass to mitigate risk of passing around credentials.
    """
    def __init__(
            self,
            subscription_id: Optional[str] = None,
            resource_group: Optional[str] = None,
            workspace_name: Optional[str] = None
            ):
        self.subscription_id = subscription_id or os.getenv("AZ_SUBSCRIPTION_ID")
        self.resource_group = resource_group or os.getenv("AZ_RESOURCE_GROUP")
        self.workspace_name = workspace_name or os.getenv("AZ_ML_WORKSPACE")
        self.validate()

    def validate(self):
        if not all([self.subscription_id, self.resource_group, self.workspace_name]):
            raise SurrogateConfigError(
                "Missing required Azure environment variables: "
                "AZ_SUBSCRIPTION_ID, AZ_RESOURCE_GROUP, AZ_ML_WORKSPACE"
            )

    def create_ml_client(self) -> MLClient:
        credential = DefaultAzureCredential()
        return MLClient(
            credential=credential,
            subscription_id=self.subscription_id,
            resource_group_name=self.resource_group,
            workspace_name=self.workspace_name
        )

class AzureMLTrainingRunner(su.ports.TrainingRunnerPort):

    def __init__(
            self,
            client: MLClient,
            compute_configuration: VOAzureMLComputeConfig,
            code_packager: CodePackager,
            trainer_registry: Optional[su.trainers.TrainerRegistry] = None
    ):
        super().__init__(trainer_registry)
        self.compute_configuration = compute_configuration
        self.code_packager = code_packager
        self.ml_client: MLClient = client
        self.logger = logging.getLogger()

    def setup_infrastructure(self) -> None:
        pass

    def teardown_infrastructure(self) -> None:
        pass

    def submit_training_job(
        self,
        job: su.ENTTrainingJob,
        training_data: su.VODataset,
        test_data: Optional[su.VODataset] = None,
        logging_callback: Optional[Callable] = None
    ) -> Tuple[Any, su.ENTTrainingJob]:

        job_id = str(job.uid)
        job_start_time = datetime.datetime.now(datetime.timezone.utc)

        # 1. PROVISION RESOURCES
        docker_baseimage = self.trainer_registry.get_base_image(job.metadata.surrogate_algo, "azure")
        # Compute is provisioned once and can be reused across jobs. It depends only on compute label. THere are no "addons"
        azure_compute_label = self._provision_compute(job_id, self.compute_configuration, self.ml_client)
        # Environment depends on the algorithm. It uses the trainer registry.
        # TODO NOTE - the provision of the environment yml in Codepackager should ALSO be part of the environment provisioning. Currently it is hardcoded within the AzureMLRunnerFactory for RNN ONLY
        azure_environment_label = self._provision_environment(job_id, job, self.ml_client, docker_baseimage)

        # 2. PACKAGE JOB
        # Create temporary local directories
        job_base_dir, job_data_dir, job_output_dir = self._create_job_context(job)

        try:
            # Package code to temporary local directory
            job_base_dir = self.code_packager.package_code(job_base_dir)

            # Package files for the entry point script
            job_configs_file, training_data_file, test_data_file = self._package_context_for_job(
                job, training_data, test_data
            )

            # Get entrypoint script. Script is renamed and moved to root directory with standard name of "entrypoint.py"
            entrypoint_script = self.code_packager.entrypoint_path

            # Build command
            cmd = self._build_training_command(
                entrypoint_script,
                {
                    "job_configs_path": job_configs_file,
                    "training_data_path": training_data_file,
                    "test_data_path": test_data_file,
                }
            )

            # Submit job
            azure_job_label = self._create_and_submit_azure_job(
                job_id=job_id,
                ml_client=self.ml_client,
                compute_label=azure_compute_label,
                environment_label=azure_environment_label,
                command=cmd, 
                code_dir=job_base_dir, 
                surrogate_job=job
            )

            # 3. DOWNLOAD JOB
            job_artefact_dir = self._poll_and_download_rawmodel_and_jobentity(
                self.ml_client, azure_job_label, job_output_dir, 
                job_output_dir, polling_interval_s=5
            )

            # 4. DEPICKLE AND RETURN
            # NOTE keywords are manually algined between the runner script and this depickle.
            # TODO this can be aligned again using a RunnerConfig. This can be in AMLRUnner or be generalized to be RunnerFileConfig?
            raw_model, job_entity = self._depickle_rawmodel_and_jobentity(
                directory=job_artefact_dir, 
                raw_model_keywords=["raw_model", "native_model"], 
                job_entity_keywords=["training_job", "job"]
            )

            return raw_model, job_entity

        finally:
            # 5. CLEANUP - This will always execute, even if an exception occurs
            self.logger.info(f"Cleaning up temporary directory: {job_base_dir}")
            if job_base_dir.exists():
                shutil.rmtree(job_base_dir)

    def _depickle_rawmodel_and_jobentity(self, directory: Path, raw_model_keywords: List[str], job_entity_keywords: List[str]) -> Tuple[Any, su.ENTTrainingJob]:
        """
        Locate and deserialize model and job artifacts from the output directory.
        
        Args:
            directory: Directory containing downloaded artifacts
            raw_model_keywords: List of keywords to identify model files
            job_entity_keywords: List of keywords to identify job entity files
            
        Returns:
            Tuple of (raw_model, job_entity)
            
        Raises:
            SurrogateInfrastructureError: If required artifacts cannot be found
        """
        # Ensure directory exists
        if not directory.exists() or not directory.is_dir():
            raise SurrogateInfrastructureError(f"Directory {directory} does not exist or is not a directory")

        # Find files
        model_file = next(
            (f for f in directory.glob("**/*") 
             if f.is_file() and any(k.lower() in f.name.lower() for k in raw_model_keywords)),
            None
        )
        if model_file is None:
            raise SurrogateInfrastructureError(
                f"Could not find raw model in {directory}. "
                f"Searched for files containing: {', '.join(raw_model_keywords)}"
            )

        job_file = next(
            (f for f in directory.glob("**/*") 
             if f.is_file() and any(k.lower() in f.name.lower() for k in job_entity_keywords)),
            None
        )
        if job_file is None:
            raise SurrogateInfrastructureError(
                f"Could not find job entity in {directory}. "
                f"Searched for files containing: {', '.join(job_entity_keywords)}"
            )

        # Load objects from file
        if model_file.suffix.lower() == ".pth":
            raw_model = torch.load(model_file, map_location=torch.device("cpu"))
        elif model_file.suffix.lower() == ".pt":
            raw_model = torch.load(model_file, map_location=torch.device("cpu"))
        elif model_file.suffix.lower() == ".pkl":
            with open(model_file, "rb") as f:
                raw_model = pickle.load(f, encoding="bytes")
        else:
            raise SurrogateInfrastructureError(f"Unsupported model file type: {model_file.suffix}")

        # Load job entity
        if job_file.suffix.lower() == ".pkl":
            with open(job_file, "rb") as f:
                job_entity = pickle.load(f, encoding="bytes")
        else:
            raise SurrogateInfrastructureError(f"Unsupported job entity file type: {job_file.suffix}")

        return raw_model, job_entity

    def _poll_and_download_rawmodel_and_jobentity(
        self,
        ml_client: MLClient,
        azure_job_name: str,
        local_output_dir: Path,
        runner_output_dir: Path = Path("."),
        polling_interval_s: int = 5
    ) -> Path:
        """
        Poll Azure ML job for completion and download artifacts when complete.
        
        Args:
            ml_client: Authenticated Azure ML client
            azure_job_name: Name of the submitted Azure ML job
            runner_output_dir: Relative path ON THE COMPUTE where training artifacts were saved. For azure, set it as default ("."). It is already seperate from the code directory, etc.
            local_output_dir: Local path where artifacts should be downloaded to
            
        Returns:
            Path: Local directory containing downloaded artifacts
                
        Raises:
            SurrogateInfrastructureError: For job failures or transfer errors
        """
        # State machine for job status
        terminal_success_states = ['completed']
        terminal_failure_states = ['failed', 'canceled', 'canceling', 'not_responding']
        polling_states = ['queued', 'starting', 'preparing', 'running', 'provisioning', 'finalizing']

        def move_subdirectory_contents(
                root: Path, 
                rel_src: Path = Path("artifacts/outputs"),
                rel_dest: Path = Path(".")
                ) -> None:
            """Copy Azure ML artifacts while preserving directory structure."""
            source_dir = root / rel_src
            destination_dir = root / rel_dest

            if not source_dir.exists():
                self.logger.debug(f"Source directory {source_dir} does not exist")
                return

            try:
                shutil.copytree(source_dir, destination_dir, dirs_exist_ok=True)
                self.logger.info(f"Copied contents from {source_dir} to {destination_dir}")
            except OSError as e:
                raise SurrogateInfrastructureError(f"Failed to copy directory contents: {e}")

        while True:
            # Get job status - let parent template method handle general exceptions
            job = ml_client.jobs.get(azure_job_name)
            status = job.status.lower()
            self.logger.debug(f"Job {azure_job_name} status: {status}")

            # Handle terminal success state
            if status in terminal_success_states:
                self.logger.info(f"Job {azure_job_name} completed successfully, downloading artifacts")
                local_output_dir.mkdir(parents=True, exist_ok=True)

                # Download artifacts - let parent template method handle general exceptions
                # Use None for output_name to download all outputs (Azure ML default behavior)
                output_name_param = None if str(runner_output_dir) == "." else str(runner_output_dir)
                self.logger.info(f"Downloading with output_name={output_name_param} to {local_output_dir}")

                ml_client.jobs.download(
                    name=azure_job_name,
                    download_path=str(local_output_dir),
                    output_name=output_name_param
                )
                self.logger.info(f"Job artifacts transferred: {runner_output_dir} → {local_output_dir}")
                move_subdirectory_contents(local_output_dir)

                # List downloaded files for debugging
                downloaded_files = list(local_output_dir.glob("**/*"))
                self.logger.info(f"Downloaded files: {[str(f) for f in downloaded_files if f.is_file()]}")

                # Verify download success
                if not any(local_output_dir.glob("*")):
                    raise SurrogateInfrastructureError(f"No files downloaded to {local_output_dir}")

                break

            # Handle terminal failure state
            elif status in terminal_failure_states:
                error_msg = f"Job {azure_job_name} ended with status: {status}"
                self.logger.error(error_msg)
                raise SurrogateInfrastructureError(error_msg)

            # Handle polling states
            elif status in polling_states:
                time.sleep(polling_interval_s)

            # Handle unknown states
            else:
                self.logger.warning(f"Unknown job status '{status}' for job {azure_job_name}")
                time.sleep(polling_interval_s)

        return local_output_dir

    # ==============================
    # HOOKS
    def _build_training_command(
            self,
            entrypoint_script: Path,
            args: T.Dict[str, Path]
    ):
        cmd_parts = ["python", str(entrypoint_script)]

        # Add arguments in order
        if args:
            for arg_name in sorted(args.keys()):
                arg_value = args[arg_name]
                cmd_parts.extend([f"--{arg_name}", str(arg_value)])

        command = shlex.join(cmd_parts)
        return command

    def _create_and_submit_azure_job(
        self,
        job_id: str,
        ml_client: MLClient,
        compute_label: str,
        environment_label: str,
        command: str,
        code_dir: Path,
        surrogate_job: su.ENTTrainingJob
    ) -> str:
        """
        Create Azure ML job definition and submit it to start training.
        
        Args:
            job_id: Unique identifier for the job
            ml_client: AzureML client instance
            compute_label: Compute resource identifier. If serverless, it should be "gpu-cluster:Standard_NC16as_T4_v3" or similar, where `:` is the delimiter.
            environment_label: Environment identifier
            command: Shell command to execute
            code_dir: Local path containing code assets
            surrogate_job: Training job metadata container

        Samples:
            environment_label="azureml://registries/azureml/environments/acpt-pytorch-2.2-cuda12.1/versions/37",
            
        Returns:
            str: Azure ML job name for tracking
        
        # NOTE - this needs futher refactoring in future .
            use components and pipeline in modular way via azure. this already exists in Surrogate, they just need mapping
            use datastores for input and outputs
        """
        def _parse_serverless_compute_label(compute_label: str) -> Tuple[bool, Optional[str], Optional[str]]:
            """
            Parse a compute label to determine if it's serverless and extract components.
            
            Args:
                compute_label: The compute label to parse
                
            Returns:
                Tuple of (is_serverless, cluster_type, instance_type)
            """
            if not compute_label or ":" not in compute_label:
                return False, None, None

            cluster_type, instance_type = compute_label.split(":", 1)
            is_serverless = cluster_type in ["gpu-cluster", "cpu-cluster"]

            if is_serverless:
                return True, cluster_type, instance_type
            else:
                return False, None, None

        # ------------------------------
        # LOGIC

        # Step 1: Define the command component (WHAT to run)
        command_component = azure.ai.ml.command(
            name=f"surrogate-training-component-{job_id}",
            display_name=f"surrogate-training-{job_id}",
            code=str(code_dir),
            command=command,
            environment=environment_label
        )

        # Common Tags
        tags={
            "user_reference": surrogate_job.metadata.user_reference,
            "atlas_reference": surrogate_job.metadata.atlas_reference,
            "algorithm": surrogate_job.metadata.surrogate_algo.value,
            "label": surrogate_job.metadata.label,
            "serverless": "false"
        }

        # Step 2: Submit component as a job with execution parameters (HOW to run)
        self.logger.info(f"Submitting Azure ML job: {job_id}")

        is_serverless, cluster_type, instance_type = _parse_serverless_compute_label(compute_label)

        if is_serverless:
            # SERVERLESS
            resources = ResourceConfiguration(
                    instance_type=instance_type,
            )
            tags["compute_type"] = instance_type if instance_type else "serverless"
            tags["serverless"] = "true"
            self.logger.info(f"Using serverless compute target: {instance_type}")
            submitted_job = ml_client.jobs.create_or_update(
                command_component, 
                resources=resources,
                experiment_name=surrogate_job.metadata.experiment_name,
                tags = tags,
                timeout=14400  # 4 hours
            )
        else:
            # Managed
            submitted_job = ml_client.jobs.create_or_update(
                command_component, 
                compute=compute_label,
                experiment_name=surrogate_job.metadata.experiment_name,
                tags = tags,
                timeout=14400  # 4 hours
            )

        job_name = submitted_job.name
        if job_name is None:
            raise ValueError(f"Failed to submit Azure ML job: {job_id}")

        return job_name

    def _package_context_for_job(
        self,
        job: su.ENTTrainingJob,
        training_data: su.VODataset,
        test_data: Optional[su.VODataset] = None,
        destination_dir: Optional[Path] = None
    ) -> T.Tuple[Path, Path, Path]:
        """
        Serialize job configuration and data for distributed execution.

        Writes configuration and data as files rather than command-line arguments to:
        - Handle large datasets and complex configurations efficiently
        - Create recoverable checkpoints for fault tolerance
        - Avoid command-line length limitations
        - Enable optimal data transfer in distributed environments
        
        Args:
            job: Training job entity containing configuration
            training_data: Dataset for model training
            test_data: Optional test dataset for evaluation
            destination_dir: Directory to store files (tempdir if None)

        Returns:
            Tuple of (job_file, training_data_file, test_data_file) paths
        """
        # Initialize return variables - single return pattern
        job_file = None
        training_data_file = None
        test_data_file = None

        try:
            # Determine serialization directory
            if destination_dir is None:
                destination_dir = Path(tempfile.mkdtemp(prefix="azure_ml_job_"))
            else:
                destination_dir = Path(destination_dir)
                destination_dir.mkdir(parents=True, exist_ok=True)

            # 1. Serialize job entity
            job_file = destination_dir / "job.json"
            with open(job_file, 'w') as f:
                f.write(job.model_dump_json(indent=2))

            # 2. Serialize training data
            training_data_file = destination_dir / "training_data.json"
            with open(training_data_file, 'w') as f:
                f.write(training_data.model_dump_json(indent=2))

            # 3. Serialize test data (or empty placeholder)
            test_data_file = destination_dir / "test_data.json"
            with open(test_data_file, 'w') as f:
                f.write(test_data.model_dump_json(indent=2) if test_data else '{}')

            # 4. Create manifest file
            manifest = {
                "job_file": "job.json",
                "training_data_file": "training_data.json",
                "test_data_file": "test_data.json" if test_data else None,
                "serialization_timestamp": datetime.datetime.now(datetime.timezone.utc).isoformat(),
                "job_id": str(job.uid),
                "algorithm": job.metadata.surrogate_algo.value
            }

            manifest_file = destination_dir / "manifest.json"
            with open(manifest_file, 'w') as f:
                json.dump(manifest, f, indent=2)

            self.logger.info(f"Serialized job data to {destination_dir}")

        except Exception as e:
            self.logger.error(f"Failed to serialize job data: {e}")
            raise SurrogateConfigError(f"Job serialization failed: {str(e)}", original_error=e)

        # Single return point - mathematical function mapping from inputs to output tuple
        return job_file, training_data_file, test_data_file

    def _create_job_context(self, job: su.ENTTrainingJob) -> T.Tuple[Path, Path, Path]:
        """
        Create directory structure for job. 
        Code lives in base_dir
        """
        base_dir = Path(tempfile.mkdtemp(prefix=f"azure_ml_{job.metadata.user_reference}_{job.metadata.atlas_reference}_"))
        data_dir = base_dir / "_data"
        output_dir = base_dir / "_outputs"

        # Create directories
        data_dir.mkdir(exist_ok=True)
        output_dir.mkdir(exist_ok=True)

        return base_dir, data_dir, output_dir

    def _provision_compute(
        self,
        job_id: str,
        compute_configuration: VOAzureMLComputeConfig,
        ml_client: MLClient
    ) -> str:
        """
        Provision compute resources for Azure ML training job.

        **Resource Provisioning Strategy:**
        - Check existing compute targets before creating new ones
        - Validate quota availability before allocation
        - Handle Azure throttling and transient failures with exponential backoff
        - Provide clear error messages for common failure scenarios
        TODO serverless implemntation needs rework. It should use Amlcompute etc insted of ResourceConfiguration. Check for SDK versions and fix

        Args:
            job_id: Unique identifier for this job
            compute_configuration: Azure ML compute configuration
            ml_client: Authenticated Azure ML client

        Returns:
            Name of the provisioned compute target

        Raises:
            SurrogateInfrastructureError: If resource provisioning fails
        """
        target_name = compute_configuration.compute_label
        is_serverless = False

        # Defensive: check if its serverless
        if target_name in ["gpu-cluster", "cpu-cluster"]:
            self.logger.info(f"Using serverless compute target: {target_name}")
            is_serverless = True
            # Target name should have : and the compute type after that
            target_name = f"{target_name}:{compute_configuration.instance_type}"

        if is_serverless == False:
            compute_provisioned = False

            try:
                # NOTE ugly nested but it works. Change at your own risk
                # ===============================
                # STEP 1: CHECK IF COMPUTE TARGET ALREADY EXISTS (IDEMPOTENCY)
                try:
                    existing_compute = ml_client.compute.get(target_name)
                    self.logger.info(f"Using existing compute target: {target_name}")
                    self.logger.debug(f"Compute target details: {existing_compute}")

                    # Basic validation - ensure compute target is accessible
                    if hasattr(existing_compute, 'provisioning_state'):
                        if existing_compute.provisioning_state.lower() == "failed":
                            raise SurrogateInfrastructureError(
                                f"Compute target {target_name} is in failed state. "
                                f"Please delete and recreate the compute target."
                            )

                    # Existing compute is valid
                    compute_provisioned = True

                except Exception as e:
                    if "not found" not in str(e).lower():
                        # Unexpected error - propagate it
                        raise SurrogateInfrastructureError(
                            f"Failed to check existing compute target {target_name}: {str(e)}"
                        )

                    # Compute target doesn't exist - we'll create it
                    self.logger.info(f"Compute target {target_name} not found, will create new one")

                # ===============================
                # STEP 2: CREATE NEW COMPUTE TARGET IF NEEDED
                # Note: In production, compute targets are typically pre-created by infrastructure teams
                # This is a fallback for development/testing scenarios

                if not compute_provisioned:
                    # Implement compute target creation for development scenarios
                    try:
                        self.logger.info(f"Attempting to create compute target: {target_name}")
                        self._create_compute_target(
                            target_name,
                            compute_configuration,
                            ml_client
                        )

                        self.logger.info(f"Successfully created compute target: {target_name}")
                        compute_provisioned = True

                    except Exception as create_error:
                        self.logger.error(f"Failed to create compute target {target_name}: {create_error}")

                        # Provide comprehensive error guidance
                        error_message = (
                            f"Compute target '{target_name}' not found and creation failed: {str(create_error)}. "
                            f"\n\nOptions to resolve:\n"
                            f"1. Create compute target manually in Azure ML Studio\n"
                            f"2. Use existing compute target names: 'cpu-cluster', 'gpu-cluster'\n"
                            f"3. Check Azure subscription quotas and permissions\n"
                            f"4. Verify compute configuration parameters"
                        )

                        raise SurrogateInfrastructureError(error_message)

            except SurrogateInfrastructureError:
                # Re-raise our custom errors
                raise
            except Exception as e:
                self.logger.error(f"Unexpected error during resource provisioning for job {job_id}: {e}")
                raise SurrogateInfrastructureError(
                    f"Resource provisioning failed for job {job_id}: {str(e)}",
                    original_error=e
                )

        return target_name

    def _provision_environment(
        self,
        job_id: str,
        job: su.ENTTrainingJob,
        ml_client: MLClient,
        environment_name: str,
        conda_yml: T.Optional[Path] = None
    ) -> str:
        """
        Setup the execution environment for Azure ML training job.
        
        Flexible strategy:
        - If curated environment + no conda: use directly
        - If curated environment + conda: create custom environment
        - If container registry image + conda: create custom environment
        - If container registry image + no conda: create simple environment
        
        Args:
            environment_name: Either curated environment URI or container registry image
            conda_yml: Optional path to conda environment file
        
        Returns:
            Environment reference string
        """

        # HELPERS
        def _generate_environment_version(base_image: str, conda_file: T.Optional[Path] = None) -> str:
            """Generate deterministic version from base image + optional conda contents"""
            if conda_file and conda_file.exists():
                hash_input = f"{base_image}:{conda_file.read_text()}".encode('utf-8')
            else:
                hash_input = base_image.encode('utf-8')
            return hashlib.sha256(hash_input).hexdigest()[:12]

        def _is_curated_environment(env_name: str) -> bool:
            """Check if environment is a curated environment"""
            return env_name.startswith("azureml://registries/")

        # LOGIC FLOW - State transitions that modify environment_reference
        environment_reference = None

        has_conda = conda_yml and conda_yml.exists()
        if _is_curated_environment(environment_name) and not has_conda:
            # Direct usage case - simple mapping function
            self.logger.info(f"Using curated environment directly: {environment_name}")
            environment_reference = environment_name
        else:
            # Custom environment case - composition of transformations
            algorithm = job.metadata.surrogate_algo.value
            env_name = f"surrogate-{algorithm}-{'conda' if has_conda else 'simple'}"
            env_version = _generate_environment_version(environment_name, conda_yml if has_conda else None)

            self.logger.info(f"Setting up environment {env_name}:{env_version} for job {job_id}")

            # Attempt to retrieve existing environment (idempotent operation)
            try:
                ml_client.environments.get(env_name, version=env_version)
                self.logger.info(f"Using existing environment: {env_name}:{env_version}")
                environment_reference = f"{env_name}:{env_version}"
            except Exception:
                # Create environment (conditional branch based on dependencies)
                tags = {
                    "algorithm": algorithm,
                    "base_image": environment_name,
                    "has_conda": str(has_conda).lower(),
                    f"{'conda' if has_conda else 'image'}_hash": env_version,
                    "job_id": job_id,
                    "created_by": "surrogate_training_runner"
                }

                environment_config = azure.ai.ml.entities.Environment(
                    name=env_name,
                    version=env_version,
                    image=environment_name,
                    conda_file=str(conda_yml) if has_conda else None,
                    description=f"Surrogate {algorithm} {'conda' if has_conda else 'simple'} environment",
                    tags=tags
                )

                self.logger.info(f"Creating {'conda' if has_conda else 'simple'} environment with {'conda: ' + str(conda_yml) if has_conda else 'image: ' + environment_name}")

                created_env = ml_client.environments.create_or_update(environment_config)
                environment_reference = f"{env_name}:{env_version}"

        return environment_reference

    def _create_compute_target(
        self,
        compute_target_name: str,
        compute_configuration: VOAzureMLComputeConfig,
        ml_client: MLClient
    ):
        """
        Create a new Azure ML compute target for training jobs.

        Args:
            compute_target_name: Name for the new compute target
            compute_configuration: Configuration object with compute specifications
            ml_client: Authenticated Azure ML client

        Returns:
            Created compute target object

        Raises:
            SurrogateInfrastructureError: If compute target creation fails
        """
        created_compute = None

        try:
            # Transform compute config into Azure ML entities
            vm_size = compute_configuration.instance_type

            # Apply conditional logic to instance parameters
            min_instances, max_instances = (0, compute_configuration.max_concurrent_jobs) if compute_configuration.auto_scale_enabled else (compute_configuration.instance_count, compute_configuration.instance_count)

            # Dimensional conversion: minutes → seconds
            idle_time_seconds = compute_configuration.idle_min_before_scale_down * 60

            # Create compute configuration
            compute_config = azure.ai.ml.entities.AmlCompute(
                name=compute_target_name,
                type="amlcompute",
                size=vm_size,
                min_instances=min_instances,
                max_instances=max_instances,
                idle_time_before_scale_down=idle_time_seconds,
                tier="Dedicated",
                tags={
                    "purpose": "surrogate-model-training",
                    "created_by": "azure_ml_training_runner",
                    "auto_created": "true",
                    "experiment_name": compute_configuration.experiment_name,
                    "auto_scale_enabled": str(compute_configuration.auto_scale_enabled)
                }
            )

            self.logger.info(f"Creating compute '{compute_target_name}': VM={vm_size}, instances={min_instances}-{max_instances}, auto_scale={compute_configuration.auto_scale_enabled}, idle={idle_time_seconds}s")

            # Create and await compute target (blocking operation)
            compute_target = ml_client.compute.begin_create_or_update(compute_config)
            created_compute = compute_target.result()

            self.logger.info(f"Successfully created compute target: {compute_target_name}")

        except Exception as e:
            self.logger.error(f"Failed to create compute target {compute_target_name}: {e}")
            # Error classification pattern - map exception space to domain space
            error_details = str(e).lower()

            if "quota" in error_details or "limit" in error_details:
                raise SurrogateInfrastructureError(f"Compute creation failed: quota limits - check Azure subscription VM core quotas")
            elif "permission" in error_details or "authorization" in error_details:
                raise SurrogateInfrastructureError(f"Compute creation failed: permissions - ensure Contributor access to workspace")
            elif "network" in error_details or "subnet" in error_details:
                raise SurrogateInfrastructureError(f"Compute creation failed: network configuration - verify VNet/subnet setup")
            else:
                raise SurrogateInfrastructureError(f"Compute creation failed: {str(e)}")

        return created_compute


class AzureMLRunnerFactory:
    """
    Factory for creating AzureMLRunner instances with necessary dependencies.
    
    This factory encapsulates the instantiation logic for AzureMLRunner, 
    including the creation of MLClient and other dependencies.
    
    TODO this needs code packager to be dynamic  to the algithm. Probably a callback?
    TODO this needs compute config to be dynamic  to the algithm. Probably a callback?
    TODO we need AzureMLRunner to be more dynamic. This means callbacks OR moving the dynamic parts OUT of class constructor and into a method that submit_training_job might call. e.g "init_compute", "init_environment", "init_code_packager", "init_trainer_registry", etc.
    """
    @classmethod
    def create(cls):
        
        # Create compute configuration
        compute_configuration = VOAzureMLComputeConfig.create("gpu_performant")
        
        # Create client
        ml_client = AzureMLClientFactory().create_ml_client()
        
        # Create code packager
        code_packager_config = VOCodepackagerConfig.create_for_surrogate("seq2seq")
        code_packager = CodePackager(code_packager_config)
        
        # Create trainer registry
        
        # Create runner instance
        return AzureMLTrainingRunner(
            client=ml_client,
            compute_configuration=compute_configuration,
            code_packager=code_packager,
            trainer_registry= None # use default
        )
