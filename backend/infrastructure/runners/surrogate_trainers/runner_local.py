"""
Local Training Runner Implementation

This module implements the LocalRunner adapter that executes training jobs
locally by wrapping existing trainer implementations. It follows hexagonal
architecture principles by implementing the TrainingRunnerPort interface.

Design Philosophy:
- **Stateless execution**: Each training job is independent and reproducible
- **Fault tolerance**: Robust error handling with detailed logging
- **Performance**: Direct execution without overhead of distributed systems
- **Compatibility**: Maintains exact same behavior as direct trainer calls
"""

import logging
import time
from typing import Any, Callable, Optional, Tuple

import backend.core._surrogate as su
from backend.core.interfaces.surrogate_interfaces import (
    SurrogateTrainingError,
    SurrogateConfigError,
)


class SurrogateTrainingLocalRunner(su.ports.TrainingRunnerPort):
    """
    Local implementation of TrainingRunnerPort that executes training jobs
    directly on the current machine using existing trainer implementations.

    This adapter wraps the existing trainer.train() calls while maintaining
    identical behavior and providing the abstraction needed for future
    cloud-based execution strategies.

    Key Features:
    - **Zero-overhead abstraction**: Direct delegation to existing trainers
    - **Identical behavior**: Maintains exact same input/output contracts
    - **Robust error handling**: Comprehensive exception handling and logging
    - **Resource management**: Proper setup/teardown lifecycle management
    - **Centralized Trainer Registry**: Uses injected TrainerRegistry for consistent algorithm mapping
    """

    def __init__(self, trainer_registry: Optional[su.trainers.TrainerRegistry] = None):
        """
        Initialize the local training runner.

        Args:
            trainer_registry: Optional TrainerRegistry instance. If None, uses default registry.
        """
        super().__init__(trainer_registry)
        self.logger = logging.getLogger(self.__class__.__name__)
        self._is_setup = False

    def submit_training_job(
        self,
        job: su.ENTTrainingJob,
        training_data: su.VODataset,
        test_data: Optional[su.VODataset] = None,
        logging_callback: Optional[Callable] = None
    ) -> Tuple[Any, su.ENTTrainingJob]:
        """
        Execute training job locally using the appropriate trainer.

        This method replicates the exact behavior of the original trainer.train()
        calls while providing the abstraction layer needed for the runner pattern.

        Args:
            metadata: Training metadata including algorithm and identifiers
            training_data: Dataset for model training
            training_config: Training configuration (validation strategy, metrics, etc.)
            model_config: Model-specific configuration parameters
            job: Training job entity to track execution state
            test_data: Optional test dataset for evaluation
            hpo_config: Optional hyperparameter optimization configuration
            logging_callback: Optional callback for training progress logging

        Returns:
            Tuple of (native_model, updated_job) where:
            - native_model: The trained model in its native format
            - updated_job: ENTTrainingJob with execution results and status

        Raises:
            SurrogateTrainingError: If training execution fails
            SurrogateConfigError: If trainer cannot be instantiated
        """
        metadata = job.metadata
        training_config = job.training_configuration
        model_config = job.model_configuration
        hpo_config = job.hpo_configuration

        self.logger.info(f"Starting local training job for algorithm: {metadata.surrogate_algo.value}")

        try:
            # Get the appropriate trainer class for the algorithm using the registry
            try:
                trainer_cls = self.trainer_registry.get_trainer_class(metadata.surrogate_algo)
            except Exception as registry_error:
                # Convert TrainerRegistryError to SurrogateConfigError for consistency
                raise SurrogateConfigError(f"Failed to get trainer for algorithm {metadata.surrogate_algo}", original_error=registry_error)

            # Instantiate the trainer
            trainer = trainer_cls()

            # Prepare test data arrays if test_data is provided
            x_test = test_data.arr_x if test_data is not None else None
            y_test = test_data.arr_y if test_data is not None else None

            # Execute training using the trainer's train method
            # This maintains exact compatibility with existing trainer.train() calls
            # ON other adaptors, this is where we would submit a training job to the cloud
            native_model, updated_job = trainer.train(
                metadata=metadata,
                training_data=training_data,
                training_config=training_config,
                model_config=model_config,
                hpo_config=hpo_config,
                job=job,
                x_test=x_test,
                y_test=y_test,
                logging_callback=logging_callback
            )

            self.logger.info(f"Local training completed successfully for {metadata.user_reference}/{metadata.atlas_reference}")
            return native_model, updated_job

        except Exception as e:
            self.logger.error(f"Local training failed: {str(e)}", exc_info=True)

            # Update job with error information if not already set
            if job.status != su.EnumTrainingStatus.FAILED.value:
                job.status = su.EnumTrainingStatus.FAILED.value
                job.error_message = str(e)

            # Re-raise as SurrogateTrainingError if not already a surrogate error
            if isinstance(e, (SurrogateTrainingError, SurrogateConfigError)):
                raise
            else:
                raise SurrogateTrainingError(f"Local training execution failed", original_error=e)

    def setup_infrastructure(self) -> None:
        """
        Set up local infrastructure for training execution.

        For local execution, this is primarily a validation step to ensure
        the environment is ready for training. No actual infrastructure
        setup is required.
        """
        self.logger.debug("Setting up local training infrastructure")

        try:
            # Validate that we can import required modules
            import torch
            import sklearn
            import numpy as np
            import pandas as pd

            self._is_setup = True
            self.logger.info("Local training infrastructure setup completed")

        except ImportError as e:
            self.logger.error(f"Failed to setup local infrastructure: missing dependencies - {e}")
            raise SurrogateConfigError(f"Local training infrastructure setup failed: missing dependencies", original_error=e)

    def teardown_infrastructure(self) -> None:
        """
        Clean up local infrastructure resources.

        For local execution, this is primarily a cleanup step to ensure
        any temporary resources are properly released.
        """
        self.logger.debug("Tearing down local training infrastructure")

        # For local execution, there's typically no infrastructure to tear down
        # This method exists for interface compliance and future extensibility
        self._is_setup = False

        self.logger.info("Local training infrastructure teardown completed")

