from __future__ import annotations
from pathlib import Path
import typing as T
import pydantic
from pydantic import field_validator, model_validator
import shutil
import importlib
import time
import uuid
import json
import os
from datetime import datetime


# ==============================
# DATA MODELS

class VOCodepackagerConfig(pydantic.BaseModel):
    """
    Specifications for code packaging

    Attributes:
        source_module: String representation of the module to package (e.g. 'backend.core._surrogate')
        entrypoint_script: Path to the script that will be used as the entry point
        requirements: Optional list of pip-compatible requirement strings
        ignore_patterns: List of glob-style patterns for files/directories to exclude from packaging
    """
    source_module: str
    entrypoint_script: Path
    conda_yml: T.Optional[Path] = None
    ignore_patterns: T.List[str] = [
        "__pycache__",
        "*.pyc",
        "*.pyo",
        "*.pyd",
        "*.md",
        "*.txt",
        "*.log",
        "*.drawio",
        "*.ipynb",
        ".git",
        ".pytest_cache",
        ".mypy_cache",
        "*.egg-info",
        ".DS_Store",
        "Thumbs.db"
    ]

    @field_validator('entrypoint_script')
    @classmethod
    def validate_entrypoint_exists(cls, v):
        if not v.exists():
            raise ValueError(f"Entrypoint script does not exist: {v}")
        if not v.is_file():
            raise ValueError(f"Entrypoint script is not a file: {v}")
        return v

    @field_validator('ignore_patterns')
    @classmethod
    def validate_ignore_patterns(cls, v):
        # Validate that ignore patterns are non-empty strings
        if v is not None:
            for pattern in v:
                if not isinstance(pattern, str) or not pattern.strip():
                    raise ValueError(f"Ignore pattern must be a non-empty string: {pattern}")
        return v

    @model_validator(mode='after')
    def validate_module_and_script_compatibility(self):
        """
        Validate that the source module and entrypoint script are compatible.
        """
        # Check if the module can be imported
        try:
            importlib.import_module(self.source_module)
        except ImportError:
            raise ValueError(f"Source module '{self.source_module}' cannot be imported")

        # Check if entrypoint script exists and is readable
        if not self.entrypoint_script.exists():
            raise ValueError(f"Entrypoint script does not exist: {self.entrypoint_script}")

        # Additional validation: check if script has proper shebang or extension
        if self.entrypoint_script.suffix not in ['.py', '.sh']:
            raise ValueError(f"Entrypoint script should have .py or .sh extension: {self.entrypoint_script}")

        return self

    @classmethod
    def create_for_surrogate(cls, algorithm: T.Literal["seq2seq"] = "seq2seq"):
        """
        Hard coded references for now. Should be moved to trainer registry once we have more than one model.
        """

        config = None

        if algorithm == "seq2seq":
            source_module = "backend.core._surrogate"
            entrypoint_script = Path("core/_surrogate/entrypoints/seqmodel.py")
            conda_yml= Path("core/_surrogate/entrypoints/environment_seqmodel.yml")
            config = cls(
                source_module=source_module,
                entrypoint_script=entrypoint_script,
                    conda_yml=conda_yml
            )
        else:
            raise ValueError(f"Algorithm not supported: {algorithm}")

        return config

class VOPackagingSummary(pydantic.BaseModel):
    tid: str = pydantic.Field(
        default_factory=lambda: f"pkg-{datetime.now().strftime('%y%m%d')}-{uuid.uuid4().hex[:6]}"
    )
    timestamp: datetime
    source_module: str
    entrypoint_script: str
    package_size_mb: float
    python_files_count: int
    packaging_time_ms: float
    directory_structure: T.List[str]

# ==============================
# LOGIC

class CodePackager:
    """
    Packages modules for distributed work by:
    - Copying specified module code
    - Creating a requirements file
    - Copying an entrypoint script
    
    All methods are stateless with explicit parameter passing for easy testing.
    This implementation prioritizes simplicity, reliability, and testability.
    """
    PYTHON_ENTRYPOINT = "entrypoint.py"
    CONDA_ENV = "environment.yml"
    
    def __init__(self, config: VOCodepackagerConfig):
        """
        Initialize the code packager with configuration.
        
        Args:
            config: Configuration specifying what to package
        """
        self.config = config
        self.destination_dir: T.Optional[Path] = None
    
    @property
    def entrypoint_path(self) -> Path:
        """
        Returns the entrypoint path 
        If the entrypoint script is not found, raises a ValueError
        """
        if not isinstance(self.destination_dir, Path):
            raise ValueError("Destination directory not set. Have you run 'package_code' method?")

        _path =  self.destination_dir/ CodePackager.PYTHON_ENTRYPOINT

        if not _path.exists():
            raise ValueError(f"Entrypoint script not found: {_path}")

        return _path
    
    @property
    def conda_env_path(self) -> Path:
        """
        Returns the entrypoint path 
        If the entrypoint script is not found, raises a ValueError
        """
        if not isinstance(self.destination_dir, Path):
            raise ValueError("Destination directory not set. Have you run 'package_code' method?")

        _path =  self.destination_dir/ CodePackager.CONDA_ENV

        if not _path.exists():
            raise ValueError(f"Entrypoint script not found: {_path}")

        return _path

    
    def package_code(self, destination_dir: Path, generate_summary: bool = True) -> Path:
        """
        Package code for distributed execution.

        Args:
            destination_dir: Root directory where packaged code will be placed
            generate_summary: Whether to generate a packaging summary file

        Returns:
            Path to the packaged code directory (same as destination_dir)
        """
        # Start timing
        start_time = time.perf_counter()

        # Use destination_dir directly as the code directory
        code_dir = destination_dir
        code_dir.mkdir(parents=True, exist_ok=True)
        self.destination_dir = code_dir

        # Execute packaging steps with explicit parameter passing
        source_module = self.config.source_module
        module_parts = source_module.split('.')

        # Get module path
        source_path = self._get_module_path(source_module)

        # Copy module code with filtering
        CodePackager._copy_module_code(source_path, code_dir, module_parts, self.config.ignore_patterns)

        # Create requirements file if needed
        if self.config.conda_yml:
            # copy to code_dir
            shutil.copy(self.config.conda_yml, code_dir / CodePackager.CONDA_ENV)

        # Copy entrypoint script
        self._copy_entrypoint_script(self.config.entrypoint_script, code_dir)

        # End timing and generate summary if requested
        end_time = time.perf_counter()
        packaging_time_ms = (end_time - start_time) * 1000  # Convert to milliseconds

        if generate_summary:
            summary = self._create_packaging_summary(code_dir, packaging_time_ms)
            # Serialize to JSON and save to logs.json
            logs_file = code_dir / "logs.json"
            with open(logs_file, "w") as f:
                json.dump(summary.model_dump(), f, indent=2, default=str)

        return code_dir

    def _create_packaging_summary(self, code_dir: Path, packaging_time_ms: float) -> VOPackagingSummary:
        """
        Create a structured packaging summary with detailed information about the packaging operation.

        Args:
            code_dir: The directory where the code was packaged
            packaging_time_ms: Time taken for packaging in milliseconds

        Returns:
            VOPackagingSummary object with all packaging metadata
        """
        try:
            # Calculate package size
            package_size_mb = self._get_dir_size(code_dir)

            # Count Python files
            python_files = list(code_dir.rglob("*.py"))
            python_files_count = len(python_files)

            # Collect directory structure
            directory_structure = []
            for item in sorted(code_dir.rglob("*")):
                if item.is_file():
                    rel_path = item.relative_to(code_dir)
                    directory_structure.append(str(rel_path))

            # Create structured summary object
            summary = VOPackagingSummary(
                timestamp=datetime.now(),
                source_module=self.config.source_module,
                entrypoint_script=self.config.entrypoint_script.name,
                package_size_mb=package_size_mb,
                python_files_count=python_files_count,
                packaging_time_ms=packaging_time_ms,
                directory_structure=directory_structure
            )

            return summary

        except Exception as e:
            # Don't fail the packaging operation if summary generation fails
            import logging
            logging.warning(f"Failed to create packaging summary: {e}")
            # Return a minimal summary object in case of error
            return VOPackagingSummary(
                timestamp=datetime.now(),
                source_module=self.config.source_module,
                entrypoint_script=self.config.entrypoint_script.name,
                package_size_mb=0.0,
                python_files_count=0,
                packaging_time_ms=packaging_time_ms,
                directory_structure=[]
            )
    

    @staticmethod
    def _get_module_path(module_name: str) -> Path:
        """
        Get the filesystem path for a module.
        
        Args:
            module_name: Fully qualified module name
            
        Returns:
            Path to the module directory
            
        Raises:
            ValueError: If the module cannot be imported or has no __file__ attribute
        """
        try:
            module = importlib.import_module(module_name)

            if hasattr(module, '__file__') and module.__file__ is not None:
                return Path(module.__file__).parent
            else:
                raise ValueError(f"Module {module_name} has no __file__ attribute or __file__ is None")
                
        except ImportError as e:
            raise ValueError(f"Could not import module {module_name}: {e}")

        except Exception as e:
            raise ValueError(f"Unknown Error: {e}")
    
    @staticmethod
    def _copy_module_code(source_dir: Path, code_dir: Path, module_parts: T.List[str], ignore_patterns: T.Optional[T.List[str]] = None) -> None:
        """
        Copy module code to the destination directory with filtering.

        Args:
            source_dir: Path to the source module directory
            code_dir: Root destination directory
            module_parts: List of module path components
            ignore_patterns: List of glob-style patterns to ignore

        Raises:
            ValueError: If there's an error copying the module
        """
        ignore_patterns = ignore_patterns or []
        try:
            # Create the module path in the destination
            dest_path = code_dir
            for part in module_parts:
                dest_path = dest_path / part
                dest_path.parent.mkdir(parents=True, exist_ok=True)

            CodePackager._copy_tree_filtered(source_dir, dest_path, ignore_patterns)
            CodePackager._create_init_files(code_dir, module_parts)

        except (shutil.Error, OSError) as e:
            raise ValueError(f"Error copying module: {e}")

        except Exception as e:
            raise ValueError(f"Unknown Error: {e}")

    @staticmethod
    def _copy_tree_filtered(source: Path, destination: Path, ignore_patterns: T.List[str]) -> None:
        try:
            destination.mkdir(parents=True, exist_ok=True)
            shutil.copytree(source, destination, ignore=shutil.ignore_patterns(*ignore_patterns), copy_function=shutil.copy, dirs_exist_ok=True)
        except (shutil.Error, OSError) as e:
            raise ValueError(f"Error copying module: {e}")

    @staticmethod
    def _create_init_files(code_dir: Path, module_parts: T.List[str]) -> None:
        """
        Create __init__.py files in all parent directories.
        
        Args:
            code_dir: Root code directory
            module_parts: List of module path components
        """
        current_path = code_dir
        for part in module_parts:
            current_path = current_path / part
            init_file = current_path / "__init__.py"
            init_file.parent.mkdir(parents=True, exist_ok=True)
            init_file.touch(exist_ok=True)
    
    @staticmethod
    def _copy_entrypoint_script(source_script: Path, code_dir: Path) -> Path:
        """
        Copy the entrypoint script to the code directory.
        Ensure it is executable
        
        Args:
            source_script: Path to the source script
            code_dir: Destination directory
            
        Returns:
            Path to the copied script
            
        Raises:
            ValueError: If there's an error copying the script
        """
        try:
            dest_path = code_dir / CodePackager.PYTHON_ENTRYPOINT 
            shutil.copy(source_script, dest_path)
            dest_path.chmod(dest_path.stat().st_mode | 0o111)  # Add executable bit
            
            return dest_path
            
        except (shutil.Error, OSError) as e:
            raise ValueError(f"Error copying entrypoint script: {e}")

        except Exception as e:
            raise ValueError(f"Unknown Error: {e}")
    

    @staticmethod
    def _get_dir_size(directory: Path) -> float:
        """
        Calculate the total size of a directory in MB.

        Args:
            directory: Path to the directory to measure

        Returns:
            Total size in MB
        """
        total_size = 0

        # Use os.walk for compatibility across Python versions
        for dirpath, _, filenames in os.walk(directory):
            for filename in filenames:
                filepath = Path(dirpath) / filename
                if filepath.exists():
                    total_size += filepath.stat().st_size

        # Convert bytes to MB
        return total_size / (1024 * 1024)

# ==================================================
# FACTORY

# Example requirements for surrogate models
# TODO update so versions are pinned to similar ones in toml
