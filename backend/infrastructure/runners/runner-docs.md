The goal is to create runners for Surrogate, specifically, trainer_rnn.py.


The two runners to build are:
local runner
aml runner

for runners requiring infrastructure provisioning, it should be a funtion within the runner and idempotent. 
It should look for credentials within the .env. 

All runners should use the configuration value objects for experiment names, hpo, etc.
Note that the traininer classes all come built in with HPO methods as well

====

# Surrogate Architecture Overview and Runner Specification

### Core Components:

1. **Value Objects (valueobjects.py)**:
   - Configuration objects like `VOConfig_Training`, `VOConfig_Model`, and `VOConfig_HPO`
   - Immutable data structures for training parameters, metrics, and results
   - All are serializable

2. **Trainers**:
   - `Port_SurrogateTrainer`: Abstract base class defining the training interface
   - `Seq2SeqTSTrainer`: Specialized trainer for RNN-based time series models
   - Built-in HPO capabilities using Optuna

3. **Models**:
   - `BaseSurrogateModel`: Abstract wrapper for different model frameworks
   - `PyTorchSurrogateModel`: Wrapper for PyTorch models

4. **Transformers**:
   - `SurrogateDataTransformer`: Handles data preprocessing, scaling, and transformation

5. **Entities**:
   - `ENTTrainingJob`: Tracks the complete training process with configurations and results

# Hexagonal Architecture Implementation (Lightweight Approach)

Following a lightweight hexagonal architecture approach, we can implement an ISurrogateTrainer with runners as infra adapaters. 

## Folder Structure

```
/backend/
  /core/
    /_surrogate/
        valueobjects.py
        entities.py
        _enums.py
      /ports/           # Refactor with ports here
        runner_port.py  # Runner interface
        model_port.py   # Model interface
        transformer_port.py
      / ... remaining foldiers as is
    /interfaces
        /surrogate_interfaces # To add ISurrogateTrainer
    
  /infrastructure/
    /runners/           # New Infra adapters for running training
      base.py           # Base runner implementation
      local.py          # Local runner implementation
      aml.py            # Azure ML runner implementation
```

## Mock Implementations

```python
class ITrainer(ABC):
    """Primary port defining the training process."""
    
    def __init__(self, runner: RunnerPort):
        """Initialize with a runner implementation."""
        self.runner = runner
        
    @abstractmethod
    # TODO revise - train should also take in a trainer class so I can swap between trainer types. If yrainer has config, etc in it then use that and simplify input siganture
    def train(
        self,
        metadata: VOMetadata_General,
        dataset: VODataset,
        training_config: VOConfig_Training,
        model_config: VOConfig_Model,
        transformer: Optional[TransformerPort] = None,
        hpo_config: Optional[VOConfig_HPO] = None
    ) -> Tuple[ModelPort, ENTTrainingJob]:
        """Orchestrate the training process."""
        pass

# RunnerPort

class RunnerPort(ABC):
    """Port defining how to run training processes."""

    @abstractmethod
    def run(
        self,
        metadata: VOMetadata_General,
        dataset: VODataset,
        training_config: VOConfig_Training,
        model_config: VOConfig_Model,
        transformer: Optional[TransformerPort] = None,
        hpo_config: Optional[VOConfig_HPO] = None
    ) -> Tuple[ModelPort, ENTTrainingJob]:
        """Run a training process."""
        pass

    @abstractmethod
    def setup_infrastructure(self) -> None:
        """Set up required infrastructure."""
        pass
```

## Usage Example with Hexagonal Architecture

```python
# Example usage with the hexagonal architecture
# NOTE - this will later be defined in our adaptor factory

# Create runners (adapters)
local_runner = LocalRunner(output_dir=Path("./outputs"))

# Create trainers with injected runners
local_trainer = RNNTrainer(runner=local_runner)

# Use the same interface for both
model, job = local_trainer.train(
    metadata=metadata,
    dataset=dataset,
    training_config=training_config,
    model_config=model_config,
    transformer=transformer,
    hpo_config=hpo_config
)

# The same code works with a different runner
model, job = docker_trainer.train(
    metadata=metadata,
    dataset=dataset,
    training_config=training_config,
    model_config=model_config,
    transformer=transformer,
    hpo_config=hpo_config
)
```

Notes
- The `ITrainer` acts as the primary port, orchestrating the process by using various secondary ports including the `RunnerPort`. 
- This makes it easy to swap implementations (like switching between local, or AzureML runners) without changing the core training logic.
- This does NOT include infrastructure yet for logging, etc. That is MLFLow and will be implemented in Qatar. Sound out if needed. Est budget: 5 days to implement.

## Runner Specification

Notes
- setup infrastructure should be idempotent. If its setup, no furhter work will be


```python
class LocalRunner(SurrogateRunner):
    """Runner for executing surrogate training locally."""
    
    def __init__(self, output_dir: Optional[Path] = None, use_gpu: bool = True):
        """
        Initialize the local runner.
        
        Args:
            output_dir: Directory to save outputs
            use_gpu: Whether to use GPU if available
        """
        load_dotenv()  # Load environment variables from .env file
        self.output_dir = output_dir or Path("./outputs")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.use_gpu = use_gpu
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def setup_infrastructure(self) -> None:
        """
        Ensure output directories exist.
        No other infrastructure needed for local execution.
        """
        self.logger.info(f"Setting up local infrastructure, output dir: {self.output_dir}")
        
        models_dir = self.output_dir / "models"
        logs_dir = self.output_dir / "logs"
        
        models_dir.mkdir(parents=True, exist_ok=True)
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Check CUDA availability if use_gpu is True
        if self.use_gpu:
            import torch
            self.use_gpu = torch.cuda.is_available()
            if not self.use_gpu:
                self.logger.warning("GPU requested but not available, falling back to CPU")
    
    def run(
        self,
        metadata: VOMetadata_General,
        dataset: VODataset,
        training_config: VOConfig_Training,
        model_config: VOConfig_Model,
        transformer: Optional[SurrogateDataTransformer] = None,
        hpo_config: Optional[VOConfig_HPO] = None
    ) -> tuple[BaseSurrogateModel, ENTTrainingJob]:
        """
        Run the surrogate training process locally.
        """
        self.logger.info(f"Starting local training run for: {metadata.label}")
        self.setup_infrastructure()
        
        # Initialize trainer
        trainer = Seq2SeqTSTrainer()
        
        # Execute training
        native_model, job = trainer.train(
            metadata=metadata,
            training_data=dataset,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Wrap model
        model = PyTorchSurrogateModel(
            native_model=native_model,
            datatransformer=transformer
        )
        
        # Save results
        model_path = self.output_dir / "models" / f"{metadata.uid}.pt"
        job_path = self.output_dir / "jobs" / f"{metadata.uid}.json"
        
        # Save model and job
        self._save_model(model, model_path)
        self._save_job(job, job_path)
        
        self.logger.info(f"Completed local training run for: {metadata.label}")
        return model, job
    
    def _save_model(self, model: BaseSurrogateModel, path: Path) -> None:
        """Save model to disk."""
        path.parent.mkdir(parents=True, exist_ok=True)
        serialized_model = model.serialize_native_model()
        with open(path, 'w') as f:
            f.write(serialized_model)
    
    def _save_job(self, job: ENTTrainingJob, path: Path) -> None:
        """Save training job to disk."""
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, 'w') as f:
            f.write(job.model_dump_json(indent=2))
```

### 3. Azure ML Runner Implementation


necessary env vars: 
AZ_SUBSCRIPTION_ID
AZ_RESOURCE_GROUP
AZ_ML_WORKSPACE
AZURE_TENANT_ID
AZURE_CLIENT_ID
AZURE_CLIENT_SECRET

Questions to answer:
- how will we o


Template 1:
```python
from azure.ai.ml import command
from azure.ai.ml import MLClient
from azure.identity import DefaultAzureCredential
from azure.ai.ml.entities import Environment
from azure.ai.ml import Run

credential = DefaultAzureCredential()
ml_client = MLClient(
    credential=credential,
    subscription_id=os.getenv("AZ_SUBSCRIPTION_ID", ""),
    resource_group_name=os.getenv("AZ_RESOURCE_GROUP", ""),
    workspace_name=os.getenv("AZ_ML_WORKSPACE", ""),
)

# Define environment
env = Environment(
    name="custom-env",
    image="your-acr-name.azurecr.io/your-image:tag",
    description="Custom environment using ACR image"
)

# Define the RNN training job
job = command(
    # code="./azure_ml",
    command="python /<path-to-script-in-container>/train_rnn.py",  # You can think of this as CMD in .dockerfile
    environment=env,
    resources={"instance_type": "Standard_NC16as_T4_v3", "instance_count": 1},  # This is the GPU compute series we are using
    display_name="rnn-training",
)

# Submit the job
ml_client.create_or_update(job)

run = Run.get_context()
# Register model
run.log_model(
    name="rnn_model",
    model_path="outputs/model.pth",
    description="PyTorch RNN model"
)

# ==============================
# GET MODEL AFTER TRAINING
# ==============================
# Get the completed job
completed_job = ml_client.jobs.get(job.name)

# Get the model from the job output
model = ml_client.models.get(
    name="rnn_model",
    version=completed_job.version
)

# Download model locally
ml_client.models.download(
    name=model.name,
    version=model.version,
    download_path="./downloaded_models"
)
```
Template 2:
```python
from azure.ai.ml import command
from azure.ai.ml import MLClient
from azure.identity import DefaultAzureCredential
from azure.ai.ml.entities import ResourceConfiguration
from dotenv import load_dotenv
from os.path import join, dirname
import logging
import os

logging.info("Loading ENV...")
try:
    dotenv_path = join(dirname(__file__), '.env')
    load_success = load_dotenv(dotenv_path=dotenv_path, override=True)
    if not load_success: 
        raise FileNotFoundError(f"Please ensure there is a .env file here: {dotenv_path}")
    logging.info("Loaded ENV ✅")
except Exception as e:
    logging.error(f"Failed to load ENV ❌ | {e}")


# Handle to the workspace
credential = DefaultAzureCredential()
ml_client = MLClient(
    credential=credential,
    subscription_id=os.getenv("AZ_SUBSCRIPTION_ID", ""),
    resource_group_name=os.getenv("AZ_RESOURCE_GROUP", ""),
    workspace_name=os.getenv("AZ_ML_WORKSPACE", ""),
)

env = ml_client.environments.get(name="SurrogateTrainingTest", version="2")
print(env)
# raise Exception("Stop here!")

# Define the RNN training job
job = command(
    code="./azure_ml",
    command="python train_rnn.py",
    environment=env,
    resources={"instance_type": "Standard_NC16as_T4_v3", "instance_count": 1},
    display_name="rnn-training",
)

# Submit the job
ml_client.create_or_update(job)
```
Template 0
```python
class AzureMLRunner(SurrogateRunner):
    """Runner for executing surrogate training on Azure ML."""
    
    def __init__(
        self,
        experiment_name: str = "surrogate-training",
        compute_target: str = "gpu-cluster",
        output_dir: Optional[Path] = None
    ):
        """
        Initialize the Azure ML runner.
        
        Args:
            experiment_name: AML experiment name
            compute_target: AML compute target name
            output_dir: Local directory to save outputs
        """
        load_dotenv()  # Load environment variables from .env file
        self.experiment_name = experiment_name
        self.compute_target = compute_target
        self.output_dir = output_dir or Path("./outputs")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # AML settings from environment variables
        self.subscription_id = os.environ.get("AZURE_SUBSCRIPTION_ID")
        self.resource_group = os.environ.get("AZURE_RESOURCE_GROUP")
        self.workspace_name = os.environ.get("AZURE_ML_WORKSPACE")
        
        # AML client
        self.ml_client = None
    
    def setup_infrastructure(self) -> None:
        """
        Set up Azure ML infrastructure.
        Ensures compute cluster and other resources exist.
        """
        self.logger.info(f"Setting up Azure ML infrastructure for: {self.experiment_name}")
        
        # Check required environment variables
        if not all([self.subscription_id, self.resource_group, self.workspace_name]):
            raise ValueError("Missing required Azure environment variables. Check your .env file.")
        
        # Initialize ML client if needed
        if self.ml_client is None:
            try:
                credential = DefaultAzureCredential()
                self.ml_client = MLClient(
                    credential=credential,
                    subscription_id=self.subscription_id,
                    resource_group_name=self.resource_group,
                    workspace_name=self.workspace_name
                )
                self.logger.info("Successfully authenticated with Azure ML")
            except Exception as e:
                self.logger.error(f"Failed to initialize Azure ML client: {str(e)}")
                raise
        
        # Check if compute target exists, create if needed
        try:
            compute = self.ml_client.compute.get(self.compute_target)
            self.logger.info(f"Using existing compute target: {self.compute_target}")
        except Exception:
            self.logger.info(f"Compute target {self.compute_target} not found. Creating...")
            
            # Create compute cluster
            from azure.ai.ml.entities import AmlCompute
            compute = AmlCompute(
                name=self.compute_target,
                size="Standard_DS3_v2",
                min_instances=0,
                max_instances=4,
                idle_time_before_scale_down=1800
            )
            self.ml_client.compute.begin_create_or_update(compute)
            self.logger.info(f"Created compute target: {self.compute_target}")
    
    def run(
        self,
        metadata: VOMetadata_General,
        dataset: VODataset,
        training_config: VOConfig_Training,
        model_config: VOConfig_Model,
        transformer: Optional[SurrogateDataTransformer] = None,
        hpo_config: Optional[VOConfig_HPO] = None
    ) -> tuple[BaseSurrogateModel, ENTTrainingJob]:
        """
        Run the surrogate training process on Azure ML.
        """
        self.logger.info(f"Starting Azure ML training run for: {metadata.label}")
        self.setup_infrastructure()
        
        # Create temporary directories for inputs/outputs
        temp_input_dir = self.output_dir / "temp_inputs" / f"{metadata.uid}"
        temp_output_dir = self.output_dir / "temp_outputs" / f"{metadata.uid}"
        temp_input_dir.mkdir(parents=True, exist_ok=True)
        temp_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save training inputs to disk
        input_dataset_path = temp_input_dir / "dataset.json"
        input_training_config_path = temp_input_dir / "training_config.json"
        input_model_config_path = temp_input_dir / "model_config.json"
        input_metadata_path = temp_input_dir / "metadata.json"
        
        with open(input_dataset_path, 'w') as f:
            f.write(dataset.model_dump_json(indent=2))
        
        with open(input_training_config_path, 'w') as f:
            f.write(training_config.model_dump_json(indent=2))
        
        with open(input_model_config_path, 'w') as f:
            f.write(model_config.model_dump_json(indent=2))
        
        with open(input_metadata_path, 'w') as f:
            f.write(metadata.model_dump_json(indent=2))
        
        if transformer:
            transformer_path = temp_input_dir / "transformer.json"
            with open(transformer_path, 'w') as f:
                f.write(json.dumps(transformer.serialize(), indent=2))
        
        if hpo_config:
            hpo_config_path = temp_input_dir / "hpo_config.json"
            with open(hpo_config_path, 'w') as f:
                f.write(hpo_config.model_dump_json(indent=2))
        
        # Define AML job
        # NOTE - this can be refactored out to be a proper script
        # NOTE - ZL, we are creating a template training script built on the VOs and ports. Please spend a bit of mindspace: are there any other patterns to consider?
        training_script = """
import json
import os
import sys
from pathlib import Path

import torch
from backend.core._surrogate.trainers.trainer_rnn import Seq2SeqTSTrainer
from backend.core._surrogate.models.model_base import PyTorchSurrogateModel
from backend.core._surrogate.transformers.base_transformer import SurrogateDataTransformer
from backend.core._surrogate.valueobjects import (
    VOConfig_HPO, 
    VOConfig_Model, 
    VOConfig_Training, 
    VODataset, 
    VOMetadata_General
)

# Parse input arguments
input_dir = Path(sys.argv[1])
output_dir = Path(sys.argv[2])
output_dir.mkdir(parents=True, exist_ok=True)

# Load inputs
with open(input_dir / "dataset.json", 'r') as f:
    dataset = VODataset.model_validate_json(f.read())

with open(input_dir / "training_config.json", 'r') as f:
    training_config = VOConfig_Training.model_validate_json(f.read())

with open(input_dir / "model_config.json", 'r') as f:
    model_config = VOConfig_Model.model_validate_json(f.read())

with open(input_dir / "metadata.json", 'r') as f:
    metadata = VOMetadata_General.model_validate_json(f.read())

# Load transformer if exists
transformer = None
transformer_path = input_dir / "transformer.json"
if transformer_path.exists():
    with open(transformer_path, 'r') as f:
        transformer_data = json.loads(f.read())
        transformer = SurrogateDataTransformer.deserialize(transformer_data)

# Load HPO config if exists
hpo_config = None
hpo_path = input_dir / "hpo_config.json"
if hpo_path.exists():
    with open(hpo_path, 'r') as f:
        hpo_config = VOConfig_HPO.model_validate_json(f.read())

# Initialize trainer
trainer = Seq2SeqTSTrainer()

# Execute training
print("Starting training...")
native_model, job = trainer.train(
    metadata=metadata,
    training_data=dataset,
    training_config=training_config,
    model_config=model_config,
    hpo_config=hpo_config
)

# Create final model
model = PyTorchSurrogateModel(
    native_model=native_model,
    datatransformer=transformer
)

# Save outputs
serialized_model = model.serialize_native_model()
with open(output_dir / "model.pt", 'w') as f:
    f.write(serialized_model)

with open(output_dir / "job.json", 'w') as f:
    f.write(job.model_dump_json(indent=2))

print("Training completed successfully.")
"""
        
        training_script_path = temp_input_dir / "run_training.py" 
        with open(training_script_path, 'w') as f:
            f.write(training_script)
        
        # NOTE - this should take from our pytorch base. we do NOT use conda-forge
        # NOTE - ZL - can we use poetry for AML environment? what is the codestub liek?
        # Define AML Environment
        env = Environment(
            name=f"surrogate-env-{metadata.uid}",
            description="Environment for surrogate model training",
            conda_file={
                "name": "surrogate-env",
                "channels": ["conda-forge", "defaults"],
                "dependencies": [
                    "python=3.8",
                    "pip",
                    {"pip": [
                        "torch",
                        "numpy",
                        "pandas",
                        "scikit-learn",
                        "optuna",
                        "pydantic"
                    ]}
                ]
            },
            image="mcr.microsoft.com/azureml/openmpi4.1.0-ubuntu20.04"
        )
        
        # Define and submit job
        job_name = f"surrogate-{metadata.uid}"
        
        command_job = CommandJob(
            code=str(temp_input_dir),
            command=f"python run_training.py {temp_input_dir} ${{output_dir}}",
            environment=env,
            compute=self.compute_target,
            display_name=job_name,
            experiment_name=self.experiment_name
        )
        
        # Submit job
        submitted_job = self.ml_client.jobs.create_or_update(command_job)
        self.logger.info(f"Submitted job: {submitted_job.name}")
        
        # Wait for job completion
        self.ml_client.jobs.stream(submitted_job.name)
        
        # Download results
        self.ml_client.jobs.download(
            name=submitted_job.name,
            output_name="output_dir",
            download_path=str(temp_output_dir)
        )
        
        # Load results
        with open(temp_output_dir / "model.pt", 'r') as f:
            serialized_model = f.read()
        
        with open(temp_output_dir / "job.json", 'r') as f:
            job_json = f.read()
        
        # Create model and job objects
        native_model = PyTorchSurrogateModel.deserialize_native_model(serialized_model)
        job = ENTTrainingJob.model_validate_json(job_json)
        
        # Create final model
        model = PyTorchSurrogateModel(
            native_model=native_model,
            datatransformer=transformer
        )
        
        self.logger.info(f"Completed Azure ML training run for: {metadata.label}")
        return model, job
```

# AzureML Runner Implementation Plan

## Executive Summary

This document provides a comprehensive implementation plan for the AzureMLRunner that extends our existing TrainingRunnerPort abstraction to enable cloud-based training execution on Azure Machine Learning. The design maintains the established hexagonal architecture patterns while providing seamless integration with Azure ML compute clusters.

**Key Design Principles:**
- **Stateless Execution**: Each training job is independent and reproducible
- **Fault Tolerance**: Robust error handling and recovery mechanisms
- **Performance Isolation**: Training execution doesn't block core operations
- **Information Preservation**: All training metadata and results are captured
- **Template Method Pattern**: Extensible design with private hooks for testability

## Architecture Overview

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                    ISurrogate (Core Domain)                 │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐    │
│  │            TrainingRunnerPort (Interface)          │    │
│  │                                                     │    │
│  │  + submit_training_job()                           │    │
│  │  + setup_infrastructure()                          │    │
│  │  + teardown_infrastructure()                       │    │
│  └─────────────────────────────────────────────────────┘    │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ implements
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                Infrastructure Layer                         │
│                                                             │
│  ┌─────────────────┐              ┌─────────────────────┐   │
│  │   LocalRunner   │              │   AzureMLRunner     │   │
│  │                 │              │                     │   │
│  │  Direct trainer │              │  Cloud execution    │   │
│  │  execution      │              │  via Azure ML       │   │
│  └─────────────────┘              └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### Design Philosophy (Dean × Thorp)

**Systems Scalability (Jeff Dean):**
- Horizontal scaling through Azure ML compute clusters
- Efficient resource utilization with auto-scaling
- Fault-tolerant design with retry mechanisms
- Performance monitoring and optimization

**Quantitative Rigor (Jane Street):**
- Deterministic execution with reproducible results
- Risk management through comprehensive error handling
- Operational excellence with detailed logging and monitoring
- Clear success criteria and acceptance tests

## Technical Approach

### 1. Code Upload Strategy Deep Dive Analysis

## Dynamic Code Packaging Strategy

### Complexity Analysis: Three Approaches

**Approach 1: Minimal Packaging (Low Complexity)**
```python
class MinimalCodePackager:
    """Simple code packaging with basic dependency copying"""

    def package_training_code(self, algorithm: EnumSurrogateAlgorithm, temp_dir: Path) -> Path:
        """
        Minimal packaging: Copy core modules + requirements.txt

        Complexity: LOW
        - Copy backend.core._surrogate module tree
        - Generate static requirements.txt
        - Create simple training script wrapper
        """
        code_dir = temp_dir / "code"
        code_dir.mkdir(parents=True, exist_ok=True)
        
        # Log what we're doing
        logging.info(f"Packaging training code for {algorithm.name} to {code_dir}")
        
        # Copy core surrogate module
        self._copy_surrogate_module(code_dir)
        
        # Generate static requirements
        self._create_requirements_file(code_dir)
        
        # Create algorithm-specific training script
        self._create_training_script(code_dir, algorithm)
        
        logging.info(f"Code packaging complete. Output size: {self._get_dir_size(code_dir)} MB")
        return code_dir

    def _copy_surrogate_module(self, code_dir: Path) -> None:
        """Copy entire backend.core._surrogate module tree"""
        import shutil
        from importlib import import_module
        
        # Get the surrogate module path dynamically
        surrogate_module = import_module('backend.core._surrogate')
        source_path = Path(surrogate_module.__file__).parent
        
        # Create destination path
        dest_path = code_dir / "backend" / "core" / "_surrogate"
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy the entire module tree
        logging.info(f"Copying surrogate module from {source_path} to {dest_path}")
        shutil.copytree(source_path, dest_path)
        
        # Create empty __init__.py files for proper importing
        for parent_dir in [code_dir / "backend", code_dir / "backend" / "core"]:
            init_file = parent_dir / "__init__.py"
            init_file.touch()

    def _create_requirements_file(self, code_dir: Path) -> None:
        """Generate static requirements.txt with all potential dependencies"""
        requirements = [
            "torch>=1.9.0",
            "numpy>=1.21.0",
            "pandas>=1.3.0",
            "scikit-learn>=1.0.0",
            "optuna>=2.10.0",
            "pydantic>=1.8.0",
            "matplotlib>=3.4.0",
            "joblib>=1.0.0",
            "scipy>=1.7.0"
        ]
        
        with open(code_dir / "requirements.txt", "w") as f:
            f.write("\n".join(requirements))
        
        logging.info(f"Created requirements.txt with {len(requirements)} packages")

    def _create_training_script(self, code_dir: Path, algorithm: EnumSurrogateAlgorithm) -> None:
        """Create algorithm-specific training script wrapper"""
        algorithm_name = algorithm.name
        
        # Create a simple training script that loads data and runs training
        training_script = f"""
#!/usr/bin/env python3
# Training script for {algorithm_name}
# Generated by MinimalCodePackager

import argparse
import json
import logging
import os
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import surrogate modules
from backend.core._surrogate.valueobjects import (
    VOConfig_HPO, 
    VOConfig_Model, 
    VOConfig_Training, 
    VODataset, 
    VOMetadata_General
)
from backend.core._surrogate._enums import EnumSurrogateAlgorithm
from backend.core._surrogate.trainers import TrainerRegistry

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run surrogate training job')
    parser.add_argument('--input-dir', type=str, required=True, help='Input directory with data files')
    parser.add_argument('--output-dir', type=str, required=True, help='Output directory for results')
    args = parser.parse_args()
    
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Starting training job for algorithm: {algorithm_name}")
    logger.info(f"Input directory: {{input_dir}}")
    logger.info(f"Output directory: {{output_dir}}")
    
    try:
        # Load input files
        with open(input_dir / "metadata.json", 'r') as f:
            metadata = VOMetadata_General.model_validate_json(f.read())
            
        with open(input_dir / "dataset.json", 'r') as f:
            dataset = VODataset.model_validate_json(f.read())
            
        with open(input_dir / "training_config.json", 'r') as f:
            training_config = VOConfig_Training.model_validate_json(f.read())
            
        with open(input_dir / "model_config.json", 'r') as f:
            model_config = VOConfig_Model.model_validate_json(f.read())
        
        # Load HPO config if exists
        hpo_config = None
        hpo_path = input_dir / "hpo_config.json"
        if hpo_path.exists():
            logger.info("HPO config found, loading...")
            with open(hpo_path, 'r') as f:
                hpo_config = VOConfig_HPO.model_validate_json(f.read())
        
        # Initialize appropriate trainer
        logger.info(f"Initializing trainer for {{algorithm_name}}")
        trainer_registry = TrainerRegistry()
        trainer = trainer_registry.get_trainer(EnumSurrogateAlgorithm.{algorithm_name})
        
        # Execute training
        logger.info("Starting training process")
        native_model, job = trainer.train(
            metadata=metadata,
            training_data=dataset,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Save results
        logger.info("Training complete, saving results")
        with open(output_dir / "job.json", 'w') as f:
            f.write(job.model_dump_json(indent=2))
            
        # Save model using pickle for simplicity
        import pickle
        with open(output_dir / "model.pkl", 'wb') as f:
            pickle.dump(native_model, f)
        
        logger.info("Training job completed successfully")
        
    except Exception as e:
        logger.exception(f"Error during training: {{str(e)}}")
        sys.exit(1)

if __name__ == "__main__":
    main()
"""
        
        # Write the training script to disk
        script_path = code_dir / "train.py"
        with open(script_path, 'w') as f:
            f.write(training_script)
        
        # Make script executable
        script_path.chmod(script_path.stat().st_mode | 0o755)
        
        logging.info(f"Created training script at {script_path}")

    def _get_dir_size(self, path: Path) -> float:
        """Calculate directory size in MB"""
        total_size = 0
        for dirpath, _, filenames in os.walk(path):
            for f in filenames:
                fp = Path(dirpath) / f
                total_size += fp.stat().st_size
        
        return total_size / (1024 * 1024)  # Convert bytes to MB
```

**Approach 2: Smart Dependency Analysis (Medium Complexity)**
```python
class SmartCodePackager:
    """Intelligent packaging with dependency analysis"""

    def package_training_code(self, algorithm: EnumSurrogateAlgorithm, temp_dir: Path) -> Path:
        """
        Smart packaging: Analyze dependencies and package only what's needed

        Complexity: MEDIUM
        - AST analysis to find actual dependencies
        - Dynamic requirements generation
        - Selective module copying
        """
        code_dir = temp_dir / "code"

        # Analyze trainer dependencies
        dependencies = self._analyze_trainer_dependencies(algorithm)

        # Copy only required modules
        self._copy_required_modules(code_dir, dependencies)

        # Generate dynamic requirements
        self._generate_dynamic_requirements(code_dir, dependencies)

        # Create optimized training script
        self._create_optimized_training_script(code_dir, algorithm, dependencies)

        return code_dir

    def _analyze_trainer_dependencies(self, algorithm: EnumSurrogateAlgorithm) -> Dict[str, Set[str]]:
        """Use AST analysis to find actual dependencies"""
        import ast
        from backend.core._surrogate.trainers import TrainerRegistry

        registry = TrainerRegistry()
        trainer_cls = registry.get_trainer_class(algorithm)

        # Analyze trainer source code for imports
        dependencies = {
            "modules": set(),
            "packages": set(),
            "files": set()
        }

        # AST analysis implementation would go here
        # This is a simplified version
        dependencies["modules"].update([
            "backend.core._surrogate.trainers",
            "backend.core._surrogate.valueobjects",
            "backend.core._surrogate._utilities"
        ])

        return dependencies

    def _copy_required_modules(self, code_dir: Path, dependencies: Dict[str, Set[str]]):
        """Copy only the modules that are actually used"""
        for module_path in dependencies["modules"]:
            # Implementation would selectively copy modules
            pass
```

**Approach 3: Containerized Packaging (High Complexity)**
```python
class ContainerizedCodePackager:
    """Full containerization with Docker build"""

    def package_training_code(self, algorithm: EnumSurrogateAlgorithm, temp_dir: Path) -> Path:
        """
        Containerized packaging: Build custom Docker image per job

        Complexity: HIGH
        - Dynamic Dockerfile generation
        - Docker build process
        - Image registry management
        - Layer optimization
        """
        code_dir = temp_dir / "code"

        # Generate algorithm-specific Dockerfile
        dockerfile_content = self._generate_dockerfile(algorithm)

        # Create build context
        self._create_build_context(code_dir, algorithm)

        # Build and push image
        image_tag = self._build_and_push_image(code_dir, dockerfile_content)

        # Return image reference instead of code directory
        return image_tag

    def _generate_dockerfile(self, algorithm: EnumSurrogateAlgorithm) -> str:
        """Generate algorithm-specific Dockerfile"""
        base_image = self._get_base_image(algorithm)

        dockerfile = f"""
FROM {base_image}

# Copy application code
COPY backend/ /app/backend/
COPY requirements.txt /app/

# Install dependencies
RUN pip install -r /app/requirements.txt

# Set working directory
WORKDIR /app

# Set entrypoint
ENTRYPOINT ["python", "train.py"]
"""
        return dockerfile

    def _get_base_image(self, algorithm: EnumSurrogateAlgorithm) -> str:
        """Select appropriate base image based on algorithm requirements"""
        if algorithm == EnumSurrogateAlgorithm.RNN_TS:
            return "pytorch/pytorch:1.9.0-cuda11.1-cudnn8-runtime"
        else:
            return "python:3.8-slim"
```

## Minimal Code Packager: Simple and Reliable Approach

## Implementation Overview

The Minimal Code Packager follows these straightforward steps:
1. Create a temporary directory structure
2. Copy the entire surrogate module tree without analysis
3. Generate a static requirements.txt file
4. Create a simple training script for the specific algorithm
5. Return the directory for Azure ML upload

### Detailed Implementation

```python
class MinimalCodePackager:
    """Simple code packaging with basic dependency copying"""

    def package_training_code(self, algorithm: EnumSurrogateAlgorithm, temp_dir: Path) -> Path:
        """
        Minimal packaging: Copy core modules + requirements.txt

        Complexity: LOW
        - Copy backend.core._surrogate module tree
        - Generate static requirements.txt
        - Create simple training script wrapper
        """
        code_dir = temp_dir / "code"
        code_dir.mkdir(parents=True, exist_ok=True)
        
        # Log what we're doing
        logging.info(f"Packaging training code for {algorithm.name} to {code_dir}")
        
        # Copy core surrogate module
        self._copy_surrogate_module(code_dir)
        
        # Generate static requirements
        self._create_requirements_file(code_dir)
        
        # Create algorithm-specific training script
        self._create_training_script(code_dir, algorithm)
        
        logging.info(f"Code packaging complete. Output size: {self._get_dir_size(code_dir)} MB")
        return code_dir

    def _copy_surrogate_module(self, code_dir: Path) -> None:
        """Copy entire backend.core._surrogate module tree"""
        import shutil
        from importlib import import_module
        
        # Get the surrogate module path dynamically
        surrogate_module = import_module('backend.core._surrogate')
        source_path = Path(surrogate_module.__file__).parent
        
        # Create destination path
        dest_path = code_dir / "backend" / "core" / "_surrogate"
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy the entire module tree
        logging.info(f"Copying surrogate module from {source_path} to {dest_path}")
        shutil.copytree(source_path, dest_path)
        
        # Create empty __init__.py files for proper importing
        for parent_dir in [code_dir / "backend", code_dir / "backend" / "core"]:
            init_file = parent_dir / "__init__.py"
            init_file.touch()

    def _create_requirements_file(self, code_dir: Path) -> None:
        """Generate static requirements.txt with all potential dependencies"""
        requirements = [
            "torch>=1.9.0",
            "numpy>=1.21.0",
            "pandas>=1.3.0",
            "scikit-learn>=1.0.0",
            "optuna>=2.10.0",
            "pydantic>=1.8.0",
            "matplotlib>=3.4.0",
            "joblib>=1.0.0",
            "scipy>=1.7.0"
        ]
        
        with open(code_dir / "requirements.txt", "w") as f:
            f.write("\n".join(requirements))
        
        logging.info(f"Created requirements.txt with {len(requirements)} packages")

    def _create_training_script(self, code_dir: Path, algorithm: EnumSurrogateAlgorithm) -> None:
        """Create algorithm-specific training script wrapper"""
        algorithm_name = algorithm.name
        
        # Create a simple training script that loads data and runs training
        training_script = f"""
#!/usr/bin/env python3
# Training script for {algorithm_name}
# Generated by MinimalCodePackager

import argparse
import json
import logging
import os
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import surrogate modules
from backend.core._surrogate.valueobjects import (
    VOConfig_HPO, 
    VOConfig_Model, 
    VOConfig_Training, 
    VODataset, 
    VOMetadata_General
)
from backend.core._surrogate._enums import EnumSurrogateAlgorithm
from backend.core._surrogate.trainers import TrainerRegistry

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run surrogate training job')
    parser.add_argument('--input-dir', type=str, required=True, help='Input directory with data files')
    parser.add_argument('--output-dir', type=str, required=True, help='Output directory for results')
    args = parser.parse_args()
    
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Starting training job for algorithm: {algorithm_name}")
    logger.info(f"Input directory: {{input_dir}}")
    logger.info(f"Output directory: {{output_dir}}")
    
    try:
        # Load input files
        with open(input_dir / "metadata.json", 'r') as f:
            metadata = VOMetadata_General.model_validate_json(f.read())
            
        with open(input_dir / "dataset.json", 'r') as f:
            dataset = VODataset.model_validate_json(f.read())
            
        with open(input_dir / "training_config.json", 'r') as f:
            training_config = VOConfig_Training.model_validate_json(f.read())
            
        with open(input_dir / "model_config.json", 'r') as f:
            model_config = VOConfig_Model.model_validate_json(f.read())
        
        # Load HPO config if exists
        hpo_config = None
        hpo_path = input_dir / "hpo_config.json"
        if hpo_path.exists():
            logger.info("HPO config found, loading...")
            with open(hpo_path, 'r') as f:
                hpo_config = VOConfig_HPO.model_validate_json(f.read())
        
        # Initialize appropriate trainer
        logger.info(f"Initializing trainer for {{algorithm_name}}")
        trainer_registry = TrainerRegistry()
        trainer = trainer_registry.get_trainer(EnumSurrogateAlgorithm.{algorithm_name})
        
        # Execute training
        logger.info("Starting training process")
        native_model, job = trainer.train(
            metadata=metadata,
            training_data=dataset,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Save results
        logger.info("Training complete, saving results")
        with open(output_dir / "job.json", 'w') as f:
            f.write(job.model_dump_json(indent=2))
            
        # Save model using pickle for simplicity
        import pickle
        with open(output_dir / "model.pkl", 'wb') as f:
            pickle.dump(native_model, f)
        
        logger.info("Training job completed successfully")
        
    except Exception as e:
        logger.exception(f"Error during training: {{str(e)}}")
        sys.exit(1)

if __name__ == "__main__":
    main()
"""
        
        # Write the training script to disk
        script_path = code_dir / "train.py"
        with open(script_path, 'w') as f:
            f.write(training_script)
        
        # Make script executable
        script_path.chmod(script_path.stat().st_mode | 0o755)
        
        logging.info(f"Created training script at {script_path}")

    def _get_dir_size(self, path: Path) -> float:
        """Calculate directory size in MB"""
        total_size = 0
        for dirpath, _, filenames in os.walk(path):
            for f in filenames:
                fp = Path(dirpath) / f
                total_size += fp.stat().st_size
        
        return total_size / (1024 * 1024)  # Convert bytes to MB
```

## Minimal Code Packager: Simple and Reliable Approach

## Implementation Overview

The Minimal Code Packager follows these straightforward steps:
1. Create a temporary directory structure
2. Copy the entire surrogate module tree without analysis
3. Generate a static requirements.txt file
4. Create a simple training script for the specific algorithm
5. Return the directory for Azure ML upload

### Detailed Implementation

```python
class MinimalCodePackager:
    """Simple code packaging with basic dependency copying"""

    def package_training_code(self, algorithm: EnumSurrogateAlgorithm, temp_dir: Path) -> Path:
        """
        Minimal packaging: Copy core modules + requirements.txt

        Complexity: LOW
        - Copy backend.core._surrogate module tree
        - Generate static requirements.txt
        - Create simple training script wrapper
        """
        code_dir = temp_dir / "code"
        code_dir.mkdir(parents=True, exist_ok=True)
        
        # Log what we're doing
        logging.info(f"Packaging training code for {algorithm.name} to {code_dir}")
        
        # Copy core surrogate module
        self._copy_surrogate_module(code_dir)
        
        # Generate static requirements
        self._create_requirements_file(code_dir)
        
        # Create algorithm-specific training script
        self._create_training_script(code_dir, algorithm)
        
        logging.info(f"Code packaging complete. Output size: {self._get_dir_size(code_dir)} MB")
        return code_dir

    def _copy_surrogate_module(self, code_dir: Path) -> None:
        """Copy entire backend.core._surrogate module tree"""
        import shutil
        from importlib import import_module
        
        # Get the surrogate module path dynamically
        surrogate_module = import_module('backend.core._surrogate')
        source_path = Path(surrogate_module.__file__).parent
        
        # Create destination path
        dest_path = code_dir / "backend" / "core" / "_surrogate"
        dest_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Copy the entire module tree
        logging.info(f"Copying surrogate module from {source_path} to {dest_path}")
        shutil.copytree(source_path, dest_path)
        
        # Create empty __init__.py files for proper importing
        for parent_dir in [code_dir / "backend", code_dir / "backend" / "core"]:
            init_file = parent_dir / "__init__.py"
            init_file.touch()

    def _create_requirements_file(self, code_dir: Path) -> None:
        """Generate static requirements.txt with all potential dependencies"""
        requirements = [
            "torch>=1.9.0",
            "numpy>=1.21.0",
            "pandas>=1.3.0",
            "scikit-learn>=1.0.0",
            "optuna>=2.10.0",
            "pydantic>=1.8.0",
            "matplotlib>=3.4.0",
            "joblib>=1.0.0",
            "scipy>=1.7.0"
        ]
        
        with open(code_dir / "requirements.txt", "w") as f:
            f.write("\n".join(requirements))
        
        logging.info(f"Created requirements.txt with {len(requirements)} packages")

    def _create_training_script(self, code_dir: Path, algorithm: EnumSurrogateAlgorithm) -> None:
        """Create algorithm-specific training script wrapper"""
        algorithm_name = algorithm.name
        
        # Create a simple training script that loads data and runs training
        training_script = f"""
#!/usr/bin/env python3
# Training script for {algorithm_name}
# Generated by MinimalCodePackager

import argparse
import json
import logging
import os
import sys
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import surrogate modules
from backend.core._surrogate.valueobjects import (
    VOConfig_HPO, 
    VOConfig_Model, 
    VOConfig_Training, 
    VODataset, 
    VOMetadata_General
)
from backend.core._surrogate._enums import EnumSurrogateAlgorithm
from backend.core._surrogate.trainers import TrainerRegistry

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run surrogate training job')
    parser.add_argument('--input-dir', type=str, required=True, help='Input directory with data files')
    parser.add_argument('--output-dir', type=str, required=True, help='Output directory for results')
    args = parser.parse_args()
    
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Starting training job for algorithm: {algorithm_name}")
    logger.info(f"Input directory: {{input_dir}}")
    logger.info(f"Output directory: {{output_dir}}")
    
    try:
        # Load input files
        with open(input_dir / "metadata.json", 'r') as f:
            metadata = VOMetadata_General.model_validate_json(f.read())
            
        with open(input_dir / "dataset.json", 'r') as f:
            dataset = VODataset.model_validate_json(f.read())
            
        with open(input_dir / "training_config.json", 'r') as f:
            training_config = VOConfig_Training.model_validate_json(f.read())
            
        with open(input_dir / "model_config.json", 'r') as f:
            model_config = VOConfig_Model.model_validate_json(f.read())
        
        # Load HPO config if exists
        hpo_config = None
        hpo_path = input_dir / "hpo_config.json"
        if hpo_path.exists():
            logger.info("HPO config found, loading...")
            with open(hpo_path, 'r') as f:
                hpo_config = VOConfig_HPO.model_validate_json(f.read())
        
        # Initialize appropriate trainer
        logger.info(f"Initializing trainer for {{algorithm_name}}")
        trainer_registry = TrainerRegistry()
        trainer = trainer_registry.get_trainer(EnumSurrogateAlgorithm.{algorithm_name})
        
        # Execute training
        logger.info("Starting training process")
        native_model, job = trainer.train(
            metadata=metadata,
            training_data=dataset,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Save results
        logger.info("Training complete, saving results")
        with open(output_dir / "job.json", 'w') as f:
            f.write(job.model_dump_json(indent=2))
            
        # Save model using pickle for simplicity
        import pickle
        with open(output_dir / "model.pkl", 'wb') as f:
            pickle.dump(native_model, f)
        
        logger.info("Training job completed successfully")
        
    except Exception as e:
        logger.exception(f"Error during training: {{str(e)}}")
        sys.exit(1)

if __name__ == "__main__":
    main()
"""
        
        # Write the training script to disk
        script_path = code_dir / "train.py"
        with open(script_path, 'w') as f:
            f.write(training_script)
        
        # Make script executable
        script_path.chmod(script_path.stat().st_mode | 0o755)
        
        logging.info(f"Created training script at {script_path}")

    def _get_dir_size(self, path: Path) -> float:
        """Calculate directory size in MB"""
        total_size = 0
        for dirpath, _, filenames in os.walk(path):
            for f in filenames:
                fp = Path(dirpath) / f
                total_size += fp.stat().st_size
        
        return total_size / (1024 * 1024)  # Convert bytes to MB
```

## Advantages and Trade-offs

### Key Benefits

1. **Operational Simplicity**: The minimal approach has the lowest implementation complexity, making it easy to maintain and debug.

2. **Maximum Flexibility**: By including the entire surrogate module, the approach accommodates any algorithm or model type without special handling.

3. **Development Velocity**: Enables rapid experimentation with new algorithms without packaging changes.

4. **Reduced Development Risk**: Minimizes chances of packaging-related training failures by including all possible dependencies.

5. **Minimal Maintenance**: Simple code structure with few failure points means less maintenance overhead.

### Trade-offs

1. **Larger Package Size**: Uploads the entire module tree (50-100MB) even when only a subset is needed.

2. **Longer Startup Times**: AzureML needs to process larger code packages, leading to longer job initialization times (2-3 minutes).

3. **Environment Inefficiency**: Installs all possible dependencies rather than just those needed for the specific algorithm.

4. **Higher Network Usage**: Consumes more bandwidth for code uploads, which could be a consideration in bandwidth-constrained environments.

5. **Security Scope**: Uploads the entire module, potentially exposing more code than strictly necessary.

## Performance Metrics & Resource Utilization

```python
MINIMAL_PACKAGING_METRICS = {
    "packaging_time": "15-30 seconds",      # Time to create and prepare code package
    "upload_size": "50-100 MB",             # Size of code package uploaded to Azure ML
    "job_startup_time": "2-3 minutes",      # Time from submission to training start
    "maintenance_complexity": "LOW",        # Effort required for maintenance
    "flexibility": "HIGH",                  # Ability to handle code changes
    "error_rate": "LOW",                    # Likelihood of packaging failures
    "development_overhead": "MINIMAL"       # Additional work for developers
}
```

## Integration with AzureMLRunner

The Minimal Code Packager integrates seamlessly with the AzureMLRunner using the following pattern:

```python
class AzureMLRunner(su.ports.TrainingRunnerPort):
    """Azure ML implementation of TrainingRunnerPort"""
    
    def __init__(self, azure_config: Optional[VOConfig_AzureML] = None):
        # ...initialization code...
        self.code_packager = MinimalCodePackager()
    
    def submit_training_job(self, metadata, training_data, training_config, model_config, job, test_data=None, hpo_config=None, logging_callback=None):
        """Submit a training job for execution on Azure ML"""
        # Setup infrastructure
        self.setup_infrastructure()
        
        # Prepare temp directory
        temp_dir = self._prepare_training_environment(metadata)
        
        # Package code using minimal packager
        algorithm = training_config.algorithm
        code_dir = self.code_packager.package_training_code(algorithm, temp_dir)
        
        # Serialize training data
        data_files = self._serialize_training_data(temp_dir, metadata=metadata, 
                                               training_data=training_data, 
                                               training_config=training_config,
                                               model_config=model_config,
                                               hpo_config=hpo_config)
        
        # Create and submit Azure ML job
        azure_job = self._create_azure_job(code_dir, data_files, metadata)
        completed_job = self._submit_and_monitor_job(azure_job)
        
        # Get results and clean up
        result_model, updated_job = self._retrieve_results(completed_job, temp_dir)
        self._cleanup_resources(temp_dir)
        
        return result_model, updated_job
```

## When to Use Minimal Code Packager

The Minimal Code Packager is most appropriate in the following scenarios:

1. **Early Development Phase**: When the codebase is young and rapidly evolving
2. **Rapid Prototyping**: When quick experimentation with different algorithms is needed
3. **Small Development Teams**: When simplicity and reliability are prioritized over optimization
4. **Limited DevOps Resources**: When there's limited capacity for maintaining complex packaging strategies
5. **Training Jobs with Long Runtimes**: When the added startup time is negligible compared to overall job duration

## Implementation Considerations

### Security and Access Control

While simpler, the minimal approach requires careful consideration of security:

1. **Code Access**: Since the entire module is uploaded, ensure proper access control on Azure ML workspace
2. **Secrets Management**: Avoid hardcoding secrets in any part of the module tree
3. **Dependency Scanning**: Regularly scan all dependencies for security vulnerabilities

### Monitoring and Debugging

The minimal approach offers advantages for monitoring and debugging:

1. **Comprehensive Code Access**: Easier to debug since the full module is available
2. **Predictable Environment**: Consistent packaging means fewer environment-related failures
3. **Simple Logging**: Straightforward logging pattern with clear training script

### Migration Path

As projects mature, there's a natural evolution path from Minimal Code Packager:

1. **Start with Minimal**: Begin with the minimal approach for maximum simplicity
2. **Monitor Metrics**: Track job startup time, package size, and failure rates
3. **Evaluate Trade-offs**: Assess when packaging overhead becomes significant
4. **Gradual Evolution**: Move to Smart Dependency Analysis as the codebase stabilizes
5. **Consider Containerization**: For production-scale systems, eventually consider pre-built containers

## Conclusion: The Right Tool for Early Development

The Minimal Code Packager offers a perfect balance of simplicity, reliability, and flexibility for early-stage machine learning projects. While it makes some trade-offs in terms of package size and startup time, these are often acceptable during development phases when predictability and ease of use are paramount.

As projects mature and optimization becomes more important, teams can evolve toward more sophisticated packaging strategies while leveraging the lessons learned from the minimal approach.