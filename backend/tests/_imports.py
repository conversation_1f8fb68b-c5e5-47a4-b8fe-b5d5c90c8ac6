"""
boiler plate imports for most test cases


"""
import json
import pandas as pd
from pydantic import ValidationError
import pytest
import os
import math
import abc
import copy
import datetime
import random
import uuid
import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import (
    TYPE_CHECKING,
    Any,
    Dict,
    Generic,
    List,
    Optional,
    Tuple,
    Type,
    TypeVar,
    Union,
    Callable,
    Protocol,
    Mapping,
    Iterable,
    runtime_checkable,
)
from datetime import datetime, timedelta
import hashlib
import collections
from pprint import pprint, pformat
import pytest
import time
import uuid


# Examples
import backend.application.usecase_templates as eg