"""
Unit Tests for TrainerRegistry

This module tests the TrainerRegistry implementation to ensure it properly
manages algorithm-to-trainer mappings and provides consistent trainer
resolution across the system.

Test Coverage:
- TrainerRegistry initialization and default mappings
- Trainer class resolution for supported algorithms
- Error handling for unsupported algorithms
- Dynamic trainer registration and unregistration
- Registry validation functionality
"""

import pytest
from unittest.mock import Mock

import backend.core._surrogate as su
from backend.core._surrogate.trainers.trainer_registry import <PERSON>er<PERSON><PERSON><PERSON><PERSON>, TrainerRegistryError, get_default_trainer_registry, reset_default_trainer_registry


class TestTrainerRegistryInitialization:
    """Tests for TrainerRegistry initialization and basic functionality."""

    def test_initialization(self):
        """Test TrainerRegistry initializes correctly with default mappings."""
        registry = TrainerRegistry()
        
        assert registry is not None
        assert len(registry._trainer_map) > 0
        print("TrainerRegistry initialization test passed")

    def test_default_mappings(self):
        """Test that default algorithm mappings are properly initialized."""
        registry = TrainerRegistry()
        
        # Test that RNN_TS is mapped
        assert su.EnumSurrogateAlgorithm.RNN_TS in registry._trainer_map
        
        # Test that we can get the trainer class
        trainer_cls = registry.get_trainer_class(su.EnumSurrogateAlgorithm.RNN_TS)
        assert trainer_cls == su.trainers.Seq2SeqTSTrainer
        
        print("Default mappings test passed")

    def test_list_registered_algorithms(self):
        """Test listing of registered algorithms."""
        registry = TrainerRegistry()
        
        algorithms = registry.list_registered_algorithms()
        assert isinstance(algorithms, list)
        assert su.EnumSurrogateAlgorithm.RNN_TS in algorithms
        
        print("List registered algorithms test passed")


class TestTrainerRegistryResolution:
    """Tests for trainer class resolution functionality."""

    def test_get_trainer_class_success(self):
        """Test successful trainer class resolution."""
        registry = TrainerRegistry()
        
        trainer_cls = registry.get_trainer_class(su.EnumSurrogateAlgorithm.RNN_TS)
        
        assert trainer_cls == su.trainers.Seq2SeqTSTrainer
        assert issubclass(trainer_cls, su.trainers.SurrogateTrainerPort)
        
        print("Get trainer class success test passed")

    def test_get_trainer_class_unsupported(self):
        """Test error handling for unsupported algorithms."""
        registry = TrainerRegistry()
        
        with pytest.raises(TrainerRegistryError) as exc_info:
            registry.get_trainer_class(su.EnumSurrogateAlgorithm.RANDOM_FOREST)
        
        assert "Unsupported algorithm" in str(exc_info.value)
        assert "Available algorithms" in str(exc_info.value)
        
        print("Get trainer class unsupported test passed")

    def test_is_algorithm_supported(self):
        """Test algorithm support checking."""
        registry = TrainerRegistry()
        
        # Test supported algorithm
        assert registry.is_algorithm_supported(su.EnumSurrogateAlgorithm.RNN_TS)
        
        # Test unsupported algorithm
        assert not registry.is_algorithm_supported(su.EnumSurrogateAlgorithm.RANDOM_FOREST)
        
        print("Is algorithm supported test passed")


class TestTrainerRegistryDynamicRegistration:
    """Tests for dynamic trainer registration functionality."""

    def test_register_trainer_success(self):
        """Test successful trainer registration."""
        registry = TrainerRegistry()
        
        # Create a mock trainer class that inherits from SurrogateTrainerPort
        class MockTrainer(su.trainers.SurrogateTrainerPort):
            def train(self, *args, **kwargs):
                pass
        
        # Register the mock trainer
        registry.register_trainer(su.EnumSurrogateAlgorithm.RANDOM_FOREST, MockTrainer)
        
        # Verify registration
        assert registry.is_algorithm_supported(su.EnumSurrogateAlgorithm.RANDOM_FOREST)
        trainer_cls = registry.get_trainer_class(su.EnumSurrogateAlgorithm.RANDOM_FOREST)
        assert trainer_cls == MockTrainer
        
        print("Register trainer success test passed")

    def test_register_trainer_invalid_class(self):
        """Test error handling for invalid trainer class registration."""
        registry = TrainerRegistry()
        
        # Try to register a class that doesn't inherit from SurrogateTrainerPort
        class InvalidTrainer:
            pass
        
        with pytest.raises(TrainerRegistryError) as exc_info:
            registry.register_trainer(su.EnumSurrogateAlgorithm.RANDOM_FOREST, InvalidTrainer)

        # Check that the error message contains the expected text
        error_message = str(exc_info.value)
        assert "Trainer registration failed" in error_message or "must inherit from SurrogateTrainerPort" in error_message
        
        print("Register trainer invalid class test passed")

    def test_unregister_trainer_success(self):
        """Test successful trainer unregistration."""
        registry = TrainerRegistry()
        
        # First register a trainer
        class MockTrainer(su.trainers.SurrogateTrainerPort):
            def train(self, *args, **kwargs):
                pass
        
        registry.register_trainer(su.EnumSurrogateAlgorithm.RANDOM_FOREST, MockTrainer)
        assert registry.is_algorithm_supported(su.EnumSurrogateAlgorithm.RANDOM_FOREST)
        
        # Then unregister it
        registry.unregister_trainer(su.EnumSurrogateAlgorithm.RANDOM_FOREST)
        assert not registry.is_algorithm_supported(su.EnumSurrogateAlgorithm.RANDOM_FOREST)
        
        print("Unregister trainer success test passed")

    def test_unregister_trainer_not_registered(self):
        """Test error handling for unregistering non-existent trainer."""
        registry = TrainerRegistry()
        
        with pytest.raises(TrainerRegistryError) as exc_info:
            registry.unregister_trainer(su.EnumSurrogateAlgorithm.RANDOM_FOREST)
        
        assert "is not registered" in str(exc_info.value)
        
        print("Unregister trainer not registered test passed")


class TestTrainerRegistryValidation:
    """Tests for registry validation functionality."""

    def test_validate_registry(self):
        """Test registry validation functionality."""
        registry = TrainerRegistry()
        
        validation_results = registry.validate_registry()
        
        assert isinstance(validation_results, dict)
        assert su.EnumSurrogateAlgorithm.RNN_TS in validation_results
        assert validation_results[su.EnumSurrogateAlgorithm.RNN_TS] is True
        
        print("Validate registry test passed")


class TestTrainerRegistryGlobalInstance:
    """Tests for global registry instance management."""

    def test_get_default_trainer_registry(self):
        """Test default trainer registry singleton."""
        # Reset to ensure clean state
        reset_default_trainer_registry()
        
        registry1 = get_default_trainer_registry()
        registry2 = get_default_trainer_registry()
        
        # Should return the same instance
        assert registry1 is registry2
        assert isinstance(registry1, TrainerRegistry)
        
        print("Get default trainer registry test passed")

    def test_reset_default_trainer_registry(self):
        """Test resetting the default trainer registry."""
        # Get initial registry
        registry1 = get_default_trainer_registry()
        
        # Reset and get new registry
        reset_default_trainer_registry()
        registry2 = get_default_trainer_registry()
        
        # Should be different instances
        assert registry1 is not registry2
        assert isinstance(registry2, TrainerRegistry)
        
        print("Reset default trainer registry test passed")


# Simple runner for direct execution
if __name__ == "__main__":
    print("Running TrainerRegistry tests...")
    
    # Initialization tests
    init_tests = TestTrainerRegistryInitialization()
    init_tests.test_initialization()
    init_tests.test_default_mappings()
    init_tests.test_list_registered_algorithms()
    
    # Resolution tests
    resolution_tests = TestTrainerRegistryResolution()
    resolution_tests.test_get_trainer_class_success()
    resolution_tests.test_get_trainer_class_unsupported()
    resolution_tests.test_is_algorithm_supported()
    
    # Dynamic registration tests
    registration_tests = TestTrainerRegistryDynamicRegistration()
    registration_tests.test_register_trainer_success()
    registration_tests.test_register_trainer_invalid_class()
    registration_tests.test_unregister_trainer_success()
    registration_tests.test_unregister_trainer_not_registered()
    
    # Validation tests
    validation_tests = TestTrainerRegistryValidation()
    validation_tests.test_validate_registry()
    
    # Global instance tests
    global_tests = TestTrainerRegistryGlobalInstance()
    global_tests.test_get_default_trainer_registry()
    global_tests.test_reset_default_trainer_registry()
    
    print("All TrainerRegistry tests completed successfully!")
