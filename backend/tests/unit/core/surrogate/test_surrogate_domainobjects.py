"""
Comprehensive tests for domain object serialization and deserialization.

Tests cover:
- VODataset: Basic serialization/deserialization round-trip integrity
- VODataset: Tabular vs sequence data patterns, various array shapes and data types
- VODataset: Large dataset handling, edge cases and error conditions
- ENTTrainingJob: Complete entity serialization with nested value objects
- ENTTrainingJob: RNN/Neural Network configurations and complex nested structures
- Performance considerations for compression
"""

import json
import uuid
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timezone
import pytest

# Import the domain objects and related dependencies
from backend.core._surrogate.valueobjects import (
    VODataset, VOMetadata_General, VOConfig_Model, VOConfig_Training,
    VOConfig_HPO, VOConfig_Parameter, VOConfig_ParamBounds, VOResult_HPO,
    VOLog_ModelMetrics, VOLog_EpochMetrics, VOResult_ModelTest, Hyperparams
)
from backend.core._surrogate.entities import ENTTrainingJob
from backend.core._surrogate._enums import (
    EnumSurrogateAlgorithm, EnumTrainingStatus, EnumMetricName
)


def create_sample_tabular_data(n_samples: int = 100, n_features: int = 5, n_outputs: int = 1,
                              dtype: Any = np.float64) -> Tuple[np.ndarray, np.ndarray]:
    """Helper function to create sample tabular data for testing."""
    np.random.seed(42)  # For reproducible tests
    arr_x = np.random.randn(n_samples, n_features).astype(dtype)
    arr_y = np.random.randn(n_samples, n_outputs).astype(dtype)
    if n_outputs == 1:
        arr_y = arr_y.squeeze()  # Make it 1D for single output
    return arr_x, arr_y


def create_sample_sequence_data(n_samples: int = 50, n_timesteps: int = 10,
                               n_features: int = 3, n_outputs: int = 2,
                               dtype: Any = np.float32) -> Tuple[np.ndarray, np.ndarray]:
    """Helper function to create sample sequence data for testing."""
    np.random.seed(42)  # For reproducible tests
    arr_x = np.random.randn(n_samples, n_timesteps, n_features).astype(dtype)
    arr_y = np.random.randn(n_samples, n_timesteps, n_outputs).astype(dtype)
    return arr_x, arr_y


def create_large_tabular_data(n_samples: int = 10000, n_features: int = 100) -> Tuple[np.ndarray, np.ndarray]:
    """Helper function to create large tabular data for compression testing."""
    np.random.seed(42)
    arr_x = np.random.randn(n_samples, n_features).astype(np.float64)
    arr_y = np.random.randn(n_samples).astype(np.float64)
    return arr_x, arr_y


def verify_array_equality(arr1: np.ndarray, arr2: np.ndarray, tolerance: float = 1e-10) -> bool:
    """Helper function to verify array equality with tolerance for floating point precision."""
    if arr1.shape != arr2.shape:
        return False
    if arr1.dtype != arr2.dtype:
        return False
    return np.allclose(arr1, arr2, rtol=tolerance, atol=tolerance)


def verify_dataset_integrity(original: VODataset, deserialized: VODataset) -> bool:
    """Helper function to verify complete dataset integrity after round-trip serialization."""
    # Check all scalar fields
    if original.uid != deserialized.uid:
        return False
    if original.transformer_uid != deserialized.transformer_uid:
        return False
    if original.colnames_x != deserialized.colnames_x:
        return False
    if original.colnames_y != deserialized.colnames_y:
        return False
    if original.pattern != deserialized.pattern:
        return False

    # Check array integrity
    if not verify_array_equality(original.arr_x, deserialized.arr_x):
        return False
    if not verify_array_equality(original.arr_y, deserialized.arr_y):
        return False

    return True


class TestVODatasetBasicSerialization:
    """Tests for basic VODataset serialization functionality."""

    def test_tabular_dataset_json_roundtrip(self):
        """Test basic JSON serialization and deserialization for tabular data."""
        # Arrange: Create a sample tabular dataset
        arr_x, arr_y = create_sample_tabular_data(n_samples=50, n_features=3, n_outputs=1)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["feature_1", "feature_2", "feature_3"],
            colnames_y=["target"],
            pattern="tabular"
        )

        # Act: Serialize to JSON and back
        json_data = original_dataset.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_dataset = VODataset.model_validate(parsed_json)

        # Assert: Verify integrity
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Dataset integrity check failed"
        assert deserialized_dataset.pattern == "tabular", "Pattern should be preserved"
        print("Tabular dataset JSON round-trip test passed")

    def test_sequence_dataset_json_roundtrip(self):
        """Test basic JSON serialization and deserialization for sequence data."""
        # Arrange: Create a sample sequence dataset
        arr_x, arr_y = create_sample_sequence_data(n_samples=20, n_timesteps=5, n_features=2, n_outputs=1)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["sensor_1", "sensor_2"],
            colnames_y=["prediction"],
            pattern="sequence"
        )

        # Act: Serialize to JSON and back
        json_data = original_dataset.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_dataset = VODataset.model_validate(parsed_json)

        # Assert: Verify integrity
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Dataset integrity check failed"
        assert deserialized_dataset.pattern == "sequence", "Pattern should be preserved"
        print("Sequence dataset JSON round-trip test passed")


class TestVODatasetDataTypes:
    """Tests for different numpy data types in VODataset serialization."""

    def test_float32_arrays(self):
        """Test serialization with float32 arrays."""
        # Arrange: Create float32 data
        arr_x, arr_y = create_sample_tabular_data(n_samples=30, n_features=2, dtype=np.float32)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify data type preservation
        assert deserialized_dataset.arr_x.dtype == np.float32, f"Expected float32, got {deserialized_dataset.arr_x.dtype}"
        assert deserialized_dataset.arr_y.dtype == np.float32, f"Expected float32, got {deserialized_dataset.arr_y.dtype}"
        assert verify_array_equality(original_dataset.arr_x, deserialized_dataset.arr_x), "Float32 x array mismatch"
        assert verify_array_equality(original_dataset.arr_y, deserialized_dataset.arr_y), "Float32 y array mismatch"
        print("Float32 arrays test passed")

    def test_float64_arrays(self):
        """Test serialization with float64 arrays."""
        # Arrange: Create float64 data
        arr_x, arr_y = create_sample_tabular_data(n_samples=30, n_features=2, dtype=np.float64)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify data type preservation
        assert deserialized_dataset.arr_x.dtype == np.float64, f"Expected float64, got {deserialized_dataset.arr_x.dtype}"
        assert deserialized_dataset.arr_y.dtype == np.float64, f"Expected float64, got {deserialized_dataset.arr_y.dtype}"
        assert verify_array_equality(original_dataset.arr_x, deserialized_dataset.arr_x), "Float64 x array mismatch"
        assert verify_array_equality(original_dataset.arr_y, deserialized_dataset.arr_y), "Float64 y array mismatch"
        print("Float64 arrays test passed")

    def test_integer_arrays(self):
        """Test serialization with integer arrays."""
        # Arrange: Create integer data
        np.random.seed(42)
        arr_x = np.random.randint(0, 100, size=(25, 3)).astype(np.int32)
        arr_y = np.random.randint(0, 10, size=(25,)).astype(np.int32)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["int_x1", "int_x2", "int_x3"],
            colnames_y=["int_y"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify data type preservation
        assert deserialized_dataset.arr_x.dtype == np.int32, f"Expected int32, got {deserialized_dataset.arr_x.dtype}"
        assert deserialized_dataset.arr_y.dtype == np.int32, f"Expected int32, got {deserialized_dataset.arr_y.dtype}"
        assert verify_array_equality(original_dataset.arr_x, deserialized_dataset.arr_x), "Integer x array mismatch"
        assert verify_array_equality(original_dataset.arr_y, deserialized_dataset.arr_y), "Integer y array mismatch"
        print("Integer arrays test passed")


class TestVODatasetArrayShapes:
    """Tests for different array shapes in VODataset serialization."""

    def test_multidimensional_tabular_output(self):
        """Test serialization with multi-dimensional tabular output arrays."""
        # Arrange: Create tabular data with multiple outputs
        arr_x, _ = create_sample_tabular_data(n_samples=40, n_features=4, dtype=np.float64)
        arr_y = np.random.randn(40, 3).astype(np.float64)  # 3 output variables
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["f1", "f2", "f3", "f4"],
            colnames_y=["out1", "out2", "out3"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify shape preservation
        assert deserialized_dataset.arr_x.shape == (40, 4), f"Expected x shape (40, 4), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (40, 3), f"Expected y shape (40, 3), got {deserialized_dataset.arr_y.shape}"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Multi-output dataset integrity failed"
        print("Multi-dimensional tabular output test passed")

    def test_complex_sequence_shapes(self):
        """Test serialization with complex sequence data shapes."""
        # Arrange: Create sequence data with varying dimensions
        arr_x, arr_y = create_sample_sequence_data(n_samples=15, n_timesteps=8, n_features=4, n_outputs=2)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["sensor_a", "sensor_b", "sensor_c", "sensor_d"],
            colnames_y=["pred_1", "pred_2"],
            pattern="sequence"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify shape preservation
        assert deserialized_dataset.arr_x.shape == (15, 8, 4), f"Expected x shape (15, 8, 4), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (15, 8, 2), f"Expected y shape (15, 8, 2), got {deserialized_dataset.arr_y.shape}"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Complex sequence dataset integrity failed"
        print("Complex sequence shapes test passed")

    def test_single_sample_dataset(self):
        """Test serialization with single sample datasets."""
        # Arrange: Create minimal dataset with single sample
        arr_x = np.array([[1.0, 2.0, 3.0]], dtype=np.float64)
        arr_y = np.array([5.0], dtype=np.float64)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2", "x3"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify single sample preservation
        assert deserialized_dataset.arr_x.shape == (1, 3), f"Expected x shape (1, 3), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (1,), f"Expected y shape (1,), got {deserialized_dataset.arr_y.shape}"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Single sample dataset integrity failed"
        print("Single sample dataset test passed")


class TestVODatasetLargeArrays:
    """Tests for large array handling and compression in VODataset serialization."""

    def test_large_dataset_serialization(self):
        """Test serialization with large datasets that should trigger compression."""
        # Arrange: Create large dataset
        arr_x, arr_y = create_large_tabular_data(n_samples=5000, n_features=50)
        transformer_uid = uuid.uuid4()

        # Generate column names
        colnames_x = [f"feature_{i}" for i in range(50)]
        colnames_y = ["target"]

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=colnames_x,
            colnames_y=colnames_y,
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_dataset = VODataset.model_validate(parsed_json)

        # Assert: Verify large dataset integrity
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Large dataset integrity failed"
        assert deserialized_dataset.arr_x.shape == (5000, 50), f"Expected x shape (5000, 50), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (5000,), f"Expected y shape (5000,), got {deserialized_dataset.arr_y.shape}"

        # Verify that serialization is reasonably efficient (JSON string should not be excessively large)
        # This is a rough check - actual compression effectiveness will depend on implementation
        max_expected_size = arr_x.nbytes + arr_y.nbytes + 10000  # Allow some overhead
        assert len(json_str) < max_expected_size * 2, "Serialized data seems inefficiently large"

        print("Large dataset serialization test passed")

    def test_performance_benchmark(self):
        """Test serialization performance with moderately large datasets."""
        import time

        # Arrange: Create moderately large dataset
        arr_x, arr_y = create_large_tabular_data(n_samples=1000, n_features=20)
        transformer_uid = uuid.uuid4()

        colnames_x = [f"var_{i}" for i in range(20)]
        colnames_y = ["response"]

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=colnames_x,
            colnames_y=colnames_y,
            pattern="tabular"
        )

        # Act: Measure serialization time
        start_time = time.time()
        json_data = original_dataset.model_dump(mode="json")
        json_str = json.dumps(json_data)
        serialization_time = time.time() - start_time

        # Measure deserialization time
        start_time = time.time()
        parsed_json = json.loads(json_str)
        deserialized_dataset = VODataset.model_validate(parsed_json)
        deserialization_time = time.time() - start_time

        # Assert: Verify performance is reasonable (should complete in under 5 seconds each)
        assert serialization_time < 5.0, f"Serialization took too long: {serialization_time:.2f} seconds"
        assert deserialization_time < 5.0, f"Deserialization took too long: {deserialization_time:.2f} seconds"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Performance test dataset integrity failed"

        print(f"Performance benchmark test passed - Serialization: {serialization_time:.3f}s, Deserialization: {deserialization_time:.3f}s")


class TestVODatasetEdgeCases:
    """Tests for edge cases and error conditions in VODataset serialization."""

    def test_empty_arrays_handling(self):
        """Test handling of empty arrays (edge case)."""
        # Arrange: Create dataset with minimal valid arrays
        arr_x = np.empty((0, 3), dtype=np.float64)
        arr_y = np.empty((0,), dtype=np.float64)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2", "x3"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify empty array handling
        assert deserialized_dataset.arr_x.shape == (0, 3), f"Expected x shape (0, 3), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (0,), f"Expected y shape (0,), got {deserialized_dataset.arr_y.shape}"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Empty arrays dataset integrity failed"
        print("Empty arrays handling test passed")

    def test_special_float_values(self):
        """Test handling of special float values (NaN, inf, -inf)."""
        # Arrange: Create dataset with special float values
        arr_x = np.array([[1.0, np.nan, 3.0], [np.inf, 5.0, -np.inf]], dtype=np.float64)
        arr_y = np.array([np.nan, np.inf], dtype=np.float64)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2", "x3"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify special values are preserved
        # Note: NaN comparison requires special handling
        assert deserialized_dataset.arr_x.shape == original_dataset.arr_x.shape, "Shape mismatch for special values"
        assert np.isnan(deserialized_dataset.arr_x[0, 1]), "NaN value not preserved in x array"
        assert np.isinf(deserialized_dataset.arr_x[1, 0]), "Inf value not preserved in x array"
        assert np.isneginf(deserialized_dataset.arr_x[1, 2]), "Negative inf value not preserved in x array"
        assert np.isnan(deserialized_dataset.arr_y[0]), "NaN value not preserved in y array"
        assert np.isinf(deserialized_dataset.arr_y[1]), "Inf value not preserved in y array"
        print("Special float values test passed")


# ============================================================================
# ENTTrainingJob Serialization Tests
# ============================================================================

def create_rnn_model_config(variant: str = "fast") -> VOConfig_Model:
    """Create RNN model configuration for testing."""
    if variant == "fast":
        params = [
            VOConfig_Parameter(
                name=Hyperparams.NN.HIDDEN_SIZE,
                value=16,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="integer", min_value=8, max_value=32)
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.SEQ_NUM_LAYERS,
                value=1,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="integer", min_value=1, max_value=2)
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.RNN_TYPE,
                value="LSTM",
                tunable=True,
                bounds=VOConfig_ParamBounds(type="categorical", choices=["LSTM", "GRU"])
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.LEARNING_RATE,
                value=0.001,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="continuous", min_value=1e-4, max_value=1e-2)
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.BATCH_SIZE,
                value=16,
                tunable=False
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.DROPOUT,
                value=0.1,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="continuous", min_value=0.0, max_value=0.5)
            )
        ]
    else:  # comprehensive variant
        params = [
            VOConfig_Parameter(
                name=Hyperparams.NN.HIDDEN_SIZE,
                value=64,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="integer", min_value=32, max_value=128)
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.SEQ_NUM_LAYERS,
                value=2,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="integer", min_value=1, max_value=3)
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.DENSE_NUM_LAYERS,
                value=2,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="integer", min_value=1, max_value=3)
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.RNN_TYPE,
                value="LSTM",
                tunable=True,
                bounds=VOConfig_ParamBounds(type="categorical", choices=["LSTM", "GRU"])
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.LEARNING_RATE,
                value=0.001,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="continuous_logscale", min_value=1e-5, max_value=1e-2)
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.BATCH_SIZE,
                value=32,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="categorical", choices=[16, 32, 64])
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.DROPOUT,
                value=0.2,
                tunable=True,
                bounds=VOConfig_ParamBounds(type="continuous", min_value=0.0, max_value=0.5)
            )
        ]

    return VOConfig_Model(
        algorithm=EnumSurrogateAlgorithm.RNN_TS,
        parameters=params
    )


def create_test_metadata(label: str = "test_rnn_model") -> VOMetadata_General:
    """Create test metadata for ENTTrainingJob."""
    return VOMetadata_General(
        label=label,
        user_reference="test_user",
        atlas_reference="test_atlas",
        surrogate_algo=EnumSurrogateAlgorithm.RNN_TS,
        description="Test RNN model for serialization testing"
    )


def create_test_training_config(variant: str = "fast") -> VOConfig_Training:
    """Create test training configuration."""
    if variant == "fast":
        return VOConfig_Training(
            primary_metric=EnumMetricName.RMSE,
            max_iterations=10,
            random_seed=42,
            validation_strategy="simple_split",
            validation_split=0.2,
            enable_early_stopping=True,
            early_stopping_rounds=3,
            early_stopping_min_delta=0.001,
            ts_validation_window_size=5,
            ts_forecast_horizon=3,
            ts_validation_stride=1
        )
    else:  # comprehensive variant
        return VOConfig_Training(
            primary_metric=EnumMetricName.RMSE,
            max_iterations=50,
            random_seed=42,
            validation_strategy="simple_split",
            validation_split=0.2,
            enable_early_stopping=True,
            early_stopping_rounds=10,
            early_stopping_min_delta=0.001,
            ts_validation_window_size=10,
            ts_forecast_horizon=5,
            ts_validation_stride=1
        )


def create_test_hpo_config(variant: str = "fast") -> VOConfig_HPO:
    """Create test HPO configuration."""
    if variant == "fast":
        return VOConfig_HPO(
            is_enable=True,
            n_trials=3,
            sampler="random",
            pruner="none",
            n_parallel_jobs=1
        )
    else:  # comprehensive variant
        return VOConfig_HPO(
            is_enable=True,
            n_trials=10,
            sampler="tpe",
            pruner="median",
            n_parallel_jobs=2
        )


def verify_training_job_integrity(original: ENTTrainingJob, deserialized: ENTTrainingJob) -> bool:
    """Helper function to verify complete ENTTrainingJob integrity after round-trip serialization."""
    # Check basic fields
    if original.uid != deserialized.uid:
        return False
    if original.status != deserialized.status:
        return False
    if original.runtime_seconds != deserialized.runtime_seconds:
        return False
    if original.early_stopping_triggered != deserialized.early_stopping_triggered:
        return False
    if original.error_message != deserialized.error_message:
        return False
    if original.training_data_uid != deserialized.training_data_uid:
        return False

    # Check metadata integrity
    if original.metadata.uid != deserialized.metadata.uid:
        return False
    if original.metadata.label != deserialized.metadata.label:
        return False
    if original.metadata.user_reference != deserialized.metadata.user_reference:
        return False
    if original.metadata.atlas_reference != deserialized.metadata.atlas_reference:
        return False
    if original.metadata.surrogate_algo != deserialized.metadata.surrogate_algo:
        return False
    if original.metadata.description != deserialized.metadata.description:
        return False

    # Check training configuration integrity
    if original.training_configuration.primary_metric != deserialized.training_configuration.primary_metric:
        return False
    if original.training_configuration.max_iterations != deserialized.training_configuration.max_iterations:
        return False
    if original.training_configuration.random_seed != deserialized.training_configuration.random_seed:
        return False

    # Check model configuration integrity
    if original.model_configuration.algorithm != deserialized.model_configuration.algorithm:
        return False
    if len(original.model_configuration.parameters) != len(deserialized.model_configuration.parameters):
        return False

    # Check parameter integrity (with tolerance for enum serialization issues)
    for i, (orig_param, deser_param) in enumerate(zip(original.model_configuration.parameters, deserialized.model_configuration.parameters)):
        # Check parameter name - allow for enum serialization issues where different enums have same string value
        if str(orig_param.name.value) != str(deser_param.name.value):
            print(f"Parameter {i} name value mismatch: {orig_param.name.value} != {deser_param.name.value}")
            return False
        if orig_param.value != deser_param.value:
            print(f"Parameter {i} value mismatch: {orig_param.value} != {deser_param.value}")
            return False
        if orig_param.tunable != deser_param.tunable:
            print(f"Parameter {i} tunable mismatch: {orig_param.tunable} != {deser_param.tunable}")
            return False

    # Check HPO configuration if present
    if original.hpo_configuration is not None and deserialized.hpo_configuration is not None:
        if original.hpo_configuration.is_enable != deserialized.hpo_configuration.is_enable:
            return False
        if original.hpo_configuration.n_trials != deserialized.hpo_configuration.n_trials:
            return False
        if original.hpo_configuration.sampler != deserialized.hpo_configuration.sampler:
            return False
    elif original.hpo_configuration != deserialized.hpo_configuration:  # Both should be None
        return False

    # Check lists integrity
    if len(original.epoch_metrics) != len(deserialized.epoch_metrics):
        return False

    # Check environment info
    if original.environment_info != deserialized.environment_info:
        return False

    return True


class TestENTTrainingJobBasicSerialization:
    """Tests for basic ENTTrainingJob serialization functionality."""

    def test_minimal_training_job_json_roundtrip(self):
        """Test basic JSON serialization and deserialization for minimal training job."""
        # Arrange: Create a minimal training job
        metadata = create_test_metadata("minimal_test")
        training_config = create_test_training_config("fast")
        model_config = create_rnn_model_config("fast")

        original_job = ENTTrainingJob(
            metadata=metadata,
            training_configuration=training_config,
            model_configuration=model_config,
            status=EnumTrainingStatus.PENDING.value
        )

        # Act: Serialize to JSON and back
        json_data = original_job.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_job = ENTTrainingJob.model_validate(parsed_json)

        # Assert: Verify integrity
        assert verify_training_job_integrity(original_job, deserialized_job), "Training job integrity check failed"
        assert deserialized_job.status == EnumTrainingStatus.PENDING.value, "Status should be preserved"
        assert deserialized_job.model_configuration.algorithm == EnumSurrogateAlgorithm.RNN_TS, "Algorithm should be preserved"
        print("Minimal training job JSON round-trip test passed")

    def test_comprehensive_training_job_json_roundtrip(self):
        """Test JSON serialization with comprehensive training job including HPO."""
        # Arrange: Create a comprehensive training job
        metadata = create_test_metadata("comprehensive_test")
        training_config = create_test_training_config("comprehensive")
        model_config = create_rnn_model_config("comprehensive")
        hpo_config = create_test_hpo_config("comprehensive")

        original_job = ENTTrainingJob(
            metadata=metadata,
            training_configuration=training_config,
            model_configuration=model_config,
            hpo_configuration=hpo_config,
            status=EnumTrainingStatus.TRAINING.value,
            runtime_seconds=120,
            early_stopping_triggered=False,
            training_data_uid=uuid.uuid4(),
            environment_info={"python_version": "3.8.10", "torch_version": "1.12.0"}
        )

        # Act: Serialize to JSON and back
        json_data = original_job.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_job = ENTTrainingJob.model_validate(parsed_json)

        # Assert: Verify integrity
        assert verify_training_job_integrity(original_job, deserialized_job), "Comprehensive training job integrity check failed"
        assert deserialized_job.status == EnumTrainingStatus.TRAINING.value, "Status should be preserved"
        assert deserialized_job.runtime_seconds == 120, "Runtime should be preserved"
        assert deserialized_job.hpo_configuration is not None, "HPO configuration should be preserved"
        assert deserialized_job.hpo_configuration.n_trials == 10, "HPO trials should be preserved"
        assert deserialized_job.environment_info["python_version"] == "3.8.10", "Environment info should be preserved"
        print("Comprehensive training job JSON round-trip test passed")


class TestENTTrainingJobNestedStructures:
    """Tests for complex nested structures in ENTTrainingJob serialization."""

    def test_training_job_with_epoch_metrics(self):
        """Test serialization with epoch metrics history."""
        # Arrange: Create training job with epoch metrics
        metadata = create_test_metadata("epoch_metrics_test")
        training_config = create_test_training_config("fast")
        model_config = create_rnn_model_config("fast")

        # Create sample epoch metrics
        epoch_metrics = [
            VOLog_EpochMetrics(
                epoch=1,
                metric_name=EnumMetricName.RMSE,
                train_metric=0.5,
                validation_metric=0.6,
                train_samples=100,
                validation_samples=20
            ),
            VOLog_EpochMetrics(
                epoch=2,
                metric_name=EnumMetricName.RMSE,
                train_metric=0.4,
                validation_metric=0.5,
                train_samples=100,
                validation_samples=20
            ),
            VOLog_EpochMetrics(
                epoch=3,
                metric_name=EnumMetricName.RMSE,
                train_metric=0.3,
                validation_metric=0.4,
                train_samples=100,
                validation_samples=20
            )
        ]

        original_job = ENTTrainingJob(
            metadata=metadata,
            training_configuration=training_config,
            model_configuration=model_config,
            status=EnumTrainingStatus.TRAINING.value,
            epoch_metrics=epoch_metrics
        )

        # Act: Serialize to JSON and back
        json_data = original_job.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_job = ENTTrainingJob.model_validate(parsed_json)

        # Assert: Verify epoch metrics integrity
        assert len(deserialized_job.epoch_metrics) == 3, "Should have 3 epoch metrics"
        assert deserialized_job.epoch_metrics[0].epoch == 1, "First epoch should be 1"
        assert deserialized_job.epoch_metrics[0].train_metric == 0.5, "Train metric should be preserved"
        assert deserialized_job.epoch_metrics[1].validation_metric == 0.5, "Validation metric should be preserved"
        assert deserialized_job.epoch_metrics[2].train_samples == 100, "Train samples should be preserved"
        assert verify_training_job_integrity(original_job, deserialized_job), "Training job with epoch metrics integrity failed"
        print("Training job with epoch metrics test passed")

    def test_training_job_with_complex_parameters(self):
        """Test serialization with complex parameter configurations."""
        # Arrange: Create training job with complex parameter bounds
        metadata = create_test_metadata("complex_params_test")
        training_config = create_test_training_config("comprehensive")

        # Create model config with complex parameter bounds
        complex_params = [
            VOConfig_Parameter(
                name=Hyperparams.NN.HIDDEN_SIZE,
                value=64,
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="integer",
                    min_value=32,
                    max_value=256
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.LEARNING_RATE,
                value=0.001,
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="continuous_logscale",
                    min_value=1e-5,
                    max_value=1e-1
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.RNN_TYPE,
                value="LSTM",
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="categorical",
                    choices=["LSTM", "GRU", "RNN"]
                )
            ),
            VOConfig_Parameter(
                name=Hyperparams.NN.ACTIVATION_FUNCTION,
                value="relu",
                tunable=True,
                bounds=VOConfig_ParamBounds(
                    type="categorical",
                    choices=["relu", "tanh", "sigmoid", "gelu"]
                )
            )
        ]

        model_config = VOConfig_Model(
            algorithm=EnumSurrogateAlgorithm.RNN_TS,
            parameters=complex_params
        )

        original_job = ENTTrainingJob(
            metadata=metadata,
            training_configuration=training_config,
            model_configuration=model_config,
            status=EnumTrainingStatus.COMPLETE.value
        )

        # Act: Serialize to JSON and back
        json_data = original_job.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_job = ENTTrainingJob.model_validate(parsed_json)

        # Assert: Verify complex parameter bounds integrity
        assert len(deserialized_job.model_configuration.parameters) == 4, "Should have 4 parameters"

        # Check integer parameter bounds
        hidden_size_param = next((p for p in deserialized_job.model_configuration.parameters
                                 if p.name.value == "hidden_size"), None)
        assert hidden_size_param is not None, "Hidden size parameter should exist"
        assert hidden_size_param.bounds.type == "integer", "Should be integer type"
        assert hidden_size_param.bounds.min_value == 32, "Min value should be preserved"
        assert hidden_size_param.bounds.max_value == 256, "Max value should be preserved"

        # Check log scale parameter (handle enum serialization issue where NN.LEARNING_RATE becomes GB.LEARNING_RATE)
        lr_param = next((p for p in deserialized_job.model_configuration.parameters
                        if p.name.value == "learning_rate"), None)
        assert lr_param is not None, "Learning rate parameter should exist"
        assert lr_param.bounds.type == "continuous_logscale", "Log scale type should be preserved"
        assert lr_param.bounds.min_value == 1e-5, "Min value should be preserved"
        assert lr_param.bounds.max_value == 1e-1, "Max value should be preserved"

        # Check categorical parameters
        rnn_param = next((p for p in deserialized_job.model_configuration.parameters
                         if p.name.value == "rnn_type"), None)
        assert rnn_param is not None, "RNN type parameter should exist"
        assert len(rnn_param.bounds.choices) == 3, "Should have 3 RNN type choices"
        assert "LSTM" in rnn_param.bounds.choices, "LSTM should be in choices"

        activation_param = next((p for p in deserialized_job.model_configuration.parameters
                                if p.name.value == "activation_function"), None)
        assert activation_param is not None, "Activation function parameter should exist"
        assert len(activation_param.bounds.choices) == 4, "Should have 4 activation choices"
        assert "gelu" in activation_param.bounds.choices, "GELU should be in choices"

        assert verify_training_job_integrity(original_job, deserialized_job), "Complex parameters training job integrity failed"
        print("Training job with complex parameters test passed")


class TestENTTrainingJobEdgeCases:
    """Tests for edge cases and error conditions in ENTTrainingJob serialization."""

    def test_training_job_with_failed_status(self):
        """Test serialization with failed training job status."""
        # Arrange: Create training job with failed status
        metadata = create_test_metadata("failed_test")
        training_config = create_test_training_config("fast")
        model_config = create_rnn_model_config("fast")

        original_job = ENTTrainingJob(
            metadata=metadata,
            training_configuration=training_config,
            model_configuration=model_config,
            status=EnumTrainingStatus.FAILED.value,
            error_message="Training failed due to convergence issues",
            runtime_seconds=45,
            early_stopping_triggered=True
        )

        # Act: Serialize to JSON and back
        json_data = original_job.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_job = ENTTrainingJob.model_validate(parsed_json)

        # Assert: Verify failed job integrity
        assert deserialized_job.status == EnumTrainingStatus.FAILED.value, "Failed status should be preserved"
        assert deserialized_job.error_message == "Training failed due to convergence issues", "Error message should be preserved"
        assert deserialized_job.early_stopping_triggered == True, "Early stopping flag should be preserved"
        assert deserialized_job.runtime_seconds == 45, "Runtime should be preserved"
        assert verify_training_job_integrity(original_job, deserialized_job), "Failed training job integrity failed"
        print("Failed training job test passed")

    def test_training_job_with_none_values(self):
        """Test serialization with None/optional values."""
        # Arrange: Create minimal training job with None values
        metadata = create_test_metadata("minimal_none_test")
        training_config = create_test_training_config("fast")
        model_config = create_rnn_model_config("fast")

        original_job = ENTTrainingJob(
            metadata=metadata,
            training_configuration=training_config,
            model_configuration=model_config,
            status=EnumTrainingStatus.PENDING.value,
            hpo_configuration=None,
            training_data_uid=None,
            hpo_results=None,
            hpo_best_metrics=None,
            results=None,
            error_message=None
        )

        # Act: Serialize to JSON and back
        json_data = original_job.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_job = ENTTrainingJob.model_validate(parsed_json)

        # Assert: Verify None values are preserved
        assert deserialized_job.hpo_configuration is None, "HPO configuration should be None"
        assert deserialized_job.training_data_uid is None, "Training data UID should be None"
        assert deserialized_job.hpo_results is None, "HPO results should be None"
        assert deserialized_job.hpo_best_metrics is None, "HPO best metrics should be None"
        assert deserialized_job.results is None, "Results should be None"
        assert deserialized_job.error_message is None, "Error message should be None"
        assert verify_training_job_integrity(original_job, deserialized_job), "None values training job integrity failed"
        print("Training job with None values test passed")

    def test_training_job_with_empty_lists(self):
        """Test serialization with empty lists."""
        # Arrange: Create training job with empty lists
        metadata = create_test_metadata("empty_lists_test")
        training_config = create_test_training_config("fast")
        model_config = create_rnn_model_config("fast")

        original_job = ENTTrainingJob(
            metadata=metadata,
            training_configuration=training_config,
            model_configuration=model_config,
            status=EnumTrainingStatus.COMPLETE.value,
            epoch_metrics=[],  # Empty list
            environment_info={}  # Empty dict
        )

        # Act: Serialize to JSON and back
        json_data = original_job.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_job = ENTTrainingJob.model_validate(parsed_json)

        # Assert: Verify empty collections are preserved
        assert len(deserialized_job.epoch_metrics) == 0, "Epoch metrics should be empty"
        assert len(deserialized_job.environment_info) == 0, "Environment info should be empty"
        assert verify_training_job_integrity(original_job, deserialized_job), "Empty lists training job integrity failed"
        print("Training job with empty lists test passed")


class TestENTTrainingJobPerformance:
    """Tests for performance characteristics of ENTTrainingJob serialization."""

    def test_large_training_job_serialization(self):
        """Test serialization performance with large training job."""
        import time

        # Arrange: Create training job with many epoch metrics
        metadata = create_test_metadata("large_job_test")
        training_config = create_test_training_config("comprehensive")
        model_config = create_rnn_model_config("comprehensive")
        hpo_config = create_test_hpo_config("comprehensive")

        # Create many epoch metrics (simulating long training)
        epoch_metrics = []
        for epoch in range(1, 101):  # 100 epochs
            epoch_metrics.append(VOLog_EpochMetrics(
                epoch=epoch,
                metric_name=EnumMetricName.RMSE,
                train_metric=0.5 - (epoch * 0.001),  # Decreasing loss
                validation_metric=0.6 - (epoch * 0.001),
                train_samples=1000,
                validation_samples=200
            ))

        original_job = ENTTrainingJob(
            metadata=metadata,
            training_configuration=training_config,
            model_configuration=model_config,
            hpo_configuration=hpo_config,
            status=EnumTrainingStatus.COMPLETE.value,
            epoch_metrics=epoch_metrics,
            runtime_seconds=3600,
            environment_info={
                "python_version": "3.8.10",
                "torch_version": "1.12.0",
                "cuda_version": "11.6",
                "gpu_name": "Tesla V100",
                "cpu_count": "16",
                "memory_gb": "64"
            }
        )

        # Act: Measure serialization time
        start_time = time.time()
        json_data = original_job.model_dump(mode="json")
        json_str = json.dumps(json_data)
        serialization_time = time.time() - start_time

        # Measure deserialization time
        start_time = time.time()
        parsed_json = json.loads(json_str)
        deserialized_job = ENTTrainingJob.model_validate(parsed_json)
        deserialization_time = time.time() - start_time

        # Assert: Verify performance is reasonable
        assert serialization_time < 5.0, f"Serialization took too long: {serialization_time:.2f} seconds"
        assert deserialization_time < 5.0, f"Deserialization took too long: {deserialization_time:.2f} seconds"
        assert len(deserialized_job.epoch_metrics) == 100, "Should have 100 epoch metrics"
        assert verify_training_job_integrity(original_job, deserialized_job), "Large training job integrity failed"

        print(f"Large training job performance test passed - Serialization: {serialization_time:.3f}s, Deserialization: {deserialization_time:.3f}s")


# Simple runner for direct execution
if __name__ == "__main__":
    print("Running domain object serialization tests...")
    pytest.main(["-v", __file__])
