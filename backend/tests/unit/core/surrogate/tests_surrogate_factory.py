import pytest
from typing import Any, Tu<PERSON>, Optional

import backend.core._surrogate as su
from backend.core._surrogate.factories import RNNSurrogateTrainerFactory

class TestFactoryIntegration:
    """Tests for factory integration with the training system."""

    def test_factory_creates_valid_configurations(self):
        """Test that factory creates valid configurations for training."""
        # Test fast variant
        fast_config = RNNSurrogateTrainerFactory.create_complete_config("fast")
        
        assert "metadata" in fast_config
        assert "training_config" in fast_config
        assert "model_config" in fast_config
        assert "hpo_config" in fast_config
        
        # Verify configurations are valid
        assert fast_config["metadata"].surrogate_algo == su.EnumSurrogateAlgorithm.RNN_TS
        assert fast_config["training_config"].max_iterations == 5
        assert fast_config["model_config"].algorithm == su.EnumSurrogateAlgorithm.RNN_TS
        assert fast_config["hpo_config"].n_trials == 3
        
        # Test slow variant
        slow_config = RNNSurrogateTrainerFactory.create_complete_config("slow")
        assert slow_config["training_config"].max_iterations == 20
        assert slow_config["hpo_config"].n_trials == 10
        
        print("Factory creates valid configurations test passed")




# Simple runner for direct execution
if __name__ == "__main__":
    print("Ready for Testing")
    