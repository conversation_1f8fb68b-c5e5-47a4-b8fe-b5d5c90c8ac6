"""
Unit tests for SurrogateDataTransformer in base_transformer.py

This file contains tests for the SurrogateDataTransformer class, which handles
data preprocessing, transformation, and inverse transformation for surrogate models.

The class handles both regular and time-series data with support for:

- Data transformation and inverse transformation
- Imputation of missing values
- Feature scaling
- Serialization and deserialization
- Special handling for time-series data with timeset and timestep columns

Tests are organized into three main classes:

1. TestFlatData: Tests for standard (non-time series) data transformations
2. TestSequenceData: Tests for time series data handling
3. TestFailCases: Tests for error handling and edge cases

The tests follow the recommended test structure from the README:
- Class-based organization
- Descriptive method names
- Arrange-Act-Assert pattern
- Print statements indicating successful test completion
- Helper functions to facilitate testing
"""
import sys
import uuid
import unittest
from typing import Dict, List, Tuple, Any, Optional

import numpy as np
import pandas as pd
import pytest
import sklearn.preprocessing
import sklearn.impute

from backend.core._surrogate.transformers.base_transformer import (
    SurrogateDataTransformer,
)


def create_sample_data(n_samples=100, n_features=3, random_seed=42) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Create sample data for testing standard transformations.
    
    Args:
        n_samples: Number of samples to generate
        n_features: Number of feature columns
        random_seed: Random seed for reproducibility
        
    Returns:
        Tuple of (df_x, df_y) DataFrames
    """
    np.random.seed(random_seed)
    
    # Create feature data with positive values for testing various transformations
    feature_data = np.abs(np.random.randn(n_samples, n_features)) + 1  # All positive
    feature_cols = [f"feature_{i}" for i in range(n_features)]
    
    # Create target data with similar structure
    target_data = np.random.randn(n_samples, 1)
    target_cols = ["target"]
    
    df_x = pd.DataFrame(feature_data, columns=feature_cols)
    df_y = pd.DataFrame(target_data, columns=target_cols)
    
    return df_x, df_y


def create_time_series_data(n_timesets=5, n_timesteps=10, n_features=3, random_seed=42) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Create sample time series data for testing.
    
    Args:
        n_timesets: Number of distinct time series
        n_timesteps: Number of steps per time series
        n_features: Number of feature columns
        random_seed: Random seed for reproducibility
        
    Returns:
        Tuple of (df_x, df_y) DataFrames with timeset and timestep columns
    """
    np.random.seed(random_seed)
    
    # Initialize empty lists for data points
    data_points = []
    
    # Generate time series data
    for ts in range(n_timesets):
        for step in range(n_timesteps):
            # Create row with features based on timeset and timestep
            features = {}
            for i in range(n_features):
                # Add some pattern based on timeset and timestep
                features[f"feature_{i}"] = (ts + 1) * np.sin(step * 0.1 * (i + 1)) + np.random.randn() * 0.2
            
            # Add timeset and timestep columns
            features["timeset"] = ts
            features["timestep"] = step
            
            data_points.append(features)
    
    # Create DataFrame with all features
    df_x = pd.DataFrame(data_points)
    
    # Create target with lagged relationship to features
    df_y = pd.DataFrame(index=df_x.index)
    df_y["target"] = df_x["feature_0"].shift(1).fillna(0) + df_x["feature_1"].shift(2).fillna(0)
    df_y["timeset"] = df_x["timeset"]
    df_y["timestep"] = df_x["timestep"]
    
    return df_x, df_y


def verify_transform_inverse_cycle(
    df_original: pd.DataFrame, 
    transformed_array: np.ndarray, 
    df_inverse: pd.DataFrame
) -> bool:
    """
    Verify that inverse transformation recovers the original data within a tolerance.
    
    Args:
        df_original: Original DataFrame before transformation
        transformed_array: NumPy array after transformation
        df_inverse: DataFrame after inverse transformation
        
    Returns:
        True if validation passes, raises AssertionError otherwise
    """
    # Check shapes match
    assert df_original.shape == df_inverse.shape, \
        f"Shape mismatch: original {df_original.shape}, inverse {df_inverse.shape}"
    
    # Check column names match
    assert set(df_original.columns) == set(df_inverse.columns), \
        f"Column mismatch: original {df_original.columns}, inverse {df_inverse.columns}"
    
    # Check values are approximately recovered (allowing for numerical precision issues)
    numerical_cols = df_original.select_dtypes(include=['number']).columns
    for col in numerical_cols:
        if col in df_inverse.columns and col not in ["timeset", "timestep"]:
            assert np.allclose(df_original[col], df_inverse[col], rtol=1e-5, atol=1e-5), \
                f"Values not recovered for column {col}"
    
    return True


class TestFlatData:
    """Tests for the SurrogateDataTransformer with standard (non-time series) data."""
    
    def test_basic_initialization(self):
        """Test basic initialization without transformers."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=100)
        
        # Act
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y
        )
        
        # Assert
        # Check that basic attributes are initialized
        assert transformer.uid is not None, "UID should be initialized"
        assert transformer._initial_x_variable_cols, "X variable columns should be initialized"
        assert transformer._initial_y_variable_cols, "Y variable columns should be initialized"
        assert transformer._timeset_col is None, "Timeset column should be None"
        assert transformer._timestep_col is None, "Timestep column should be None"
        
        print("Basic initialization test passed")
    
    def test_z_score_transformation(self):
        """Test z-score transformation and inverse transform."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=100)
        
        # Act
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            x_scaler=sklearn.preprocessing.StandardScaler(),
            y_scaler=sklearn.preprocessing.StandardScaler()
        )
        
        # Transform the full dataset when checking statistics
        x_full_transformed, _ = transformer.transform(df_x, None)
        assert np.allclose(np.mean(x_full_transformed, axis=0), 0, atol=1e-5)
        assert np.allclose(np.std(x_full_transformed, axis=0), 1, atol=1e-5)

        # Continue testing inverse transform with the subset
        x_test_original = df_x.iloc[:10].copy()
        y_test_original = df_y.iloc[:10].copy()
        
        x_transformed, y_transformed = transformer.transform(x_test_original, y_test_original)
        
        # Inverse transform
        x_inverse = transformer.inv_transform(x_transformed, is_x=True)
        y_inverse = transformer.inv_transform(y_transformed, is_x=False)
        
        # Assert
        # Check standardized data has mean ~ 0, std ~ 1
        assert np.allclose(np.mean(x_transformed, axis=0), 0, atol=0.25), \
            f"Z-score transform should result in mean ~ 0, got {np.mean(x_transformed, axis=0)}"
        assert np.allclose(np.std(x_transformed, axis=0), 1, atol=0.25), \
            f"Z-score transform should result in std ~ 1, got {np.std(x_transformed, axis=0)}"
        
        # Verify data is recovered through inverse transform
        verify_transform_inverse_cycle(x_test_original, x_transformed, x_inverse)
        verify_transform_inverse_cycle(y_test_original, y_transformed, y_inverse)
        
        print("Z-score transformation test passed")
    
    def test_min_max_transformation(self):
        """Test min-max scaling and inverse transform."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=100)
        
        # Act
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            x_scaler=sklearn.preprocessing.MinMaxScaler(),
            y_scaler=sklearn.preprocessing.MinMaxScaler()
        )
        
        # Transform test data
        x_test_original = df_x.iloc[:10].copy()
        y_test_original = df_y.iloc[:10].copy()
        
        x_transformed, y_transformed = transformer.transform(x_test_original, y_test_original)
        
        # Inverse transform
        x_inverse = transformer.inv_transform(x_transformed, is_x=True)
        y_inverse = transformer.inv_transform(y_transformed, is_x=False)
        
        # Assert
        # Check min-max scaled data has values between 0 and 1
        assert np.all(x_transformed >= 0), "Min-max transform should result in values >= 0"
        assert np.all(x_transformed <= 1), "Min-max transform should result in values <= 1"
        
        # Verify data is recovered through inverse transform
        verify_transform_inverse_cycle(x_test_original, x_transformed, x_inverse)
        verify_transform_inverse_cycle(y_test_original, y_transformed, y_inverse)
        
        print("Min-max transformation test passed")
        
    def test_log_transformation_with_pipeline(self):
        """Test log transformation using a custom preprocessing pipeline."""
        # Arrange - Use only positive data for log transform
        df_x, df_y = create_sample_data(n_samples=100)
        
        # Create a log transformation using scikit-learn's FunctionTransformer
        log_transformer = sklearn.preprocessing.FunctionTransformer(
            np.log, inverse_func=np.exp, validate=True
        )
        
        # Act
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            x_scaler=log_transformer
            # y is untransformed
        )
        
        # Transform test data
        x_test_original = df_x.iloc[:10].copy()
        y_test_original = df_y.iloc[:10].copy()
        
        x_transformed, y_transformed = transformer.transform(x_test_original, y_test_original)
        
        # Inverse transform
        x_inverse = transformer.inv_transform(x_transformed, is_x=True)
        
        # Assert
        # Log transform should make values smaller for values > 1
        # Compare a manual log transform with the result
        manual_log = np.log(x_test_original.values)
        assert np.allclose(x_transformed, manual_log), "Log transform should match manual log calculation"
        
        # Verify data is recovered through inverse transform
        verify_transform_inverse_cycle(x_test_original, x_transformed, x_inverse)
        
        print("Log transformation test passed")
        
    # def test_complex_transformation(self):
    #     """Test multiple transformation steps using scikit-learn pipeline."""
    #     # Arrange
    #     df_x, df_y = create_sample_data(n_samples=100)
        
    #     # Create a pipeline with log transform followed by standard scaling
    #     from sklearn.pipeline import make_pipeline
        
    #     x_pipeline = make_pipeline(
    #         sklearn.preprocessing.FunctionTransformer(np.log, inverse_func=np.exp),
    #         sklearn.preprocessing.StandardScaler()
    #     )
        
    #     y_pipeline = sklearn.preprocessing.StandardScaler()
        
    #     # Act
    #     transformer = SurrogateDataTransformer(
    #         uid=uuid.uuid4(),
    #         df_x=df_x,
    #         df_y=df_y,
    #         x_scaler=x_pipeline,
    #         y_scaler=y_pipeline
    #     )
        
    #     # Transform test data
    #     x_test_original = df_x.iloc[:10].copy()
    #     y_test_original = df_y.iloc[:10].copy()
        
    #     x_transformed, y_transformed = transformer.transform(x_test_original, y_test_original)
        
    #     # Inverse transform
    #     x_inverse = transformer.inv_transform(x_transformed, is_x=True)
    #     y_inverse = transformer.inv_transform(y_transformed, is_x=False)
        
    #     # Assert
    #     # Transformed data should be standardized, so mean ~ 0, std ~ 1
    #     assert np.allclose(np.mean(x_transformed, axis=0), 0, atol=1e-5), \
    #         f"Complex transform should result in mean ~ 0, got {np.mean(x_transformed, axis=0)}"
    #     assert np.allclose(np.std(x_transformed, axis=0), 1, atol=1e-5), \
    #         f"Complex transform should result in std ~ 1, got {np.std(x_transformed, axis=0)}"
        
    #     # Verify data is recovered through inverse transform within tolerance
    #     verify_transform_inverse_cycle(x_test_original, x_transformed, x_inverse)
    #     verify_transform_inverse_cycle(y_test_original, y_transformed, y_inverse)
        
    #     print("Complex transformation test passed")
        
    def test_different_imputers(self):
        """Test transformer with different imputers from sklearn."""
        # Arrange - Create data with missing values
        df_x, df_y = create_sample_data(n_samples=100)
        
        # Add missing values
        df_x.iloc[10:15, 0] = np.nan
        df_x.iloc[20:25, 1] = np.nan
        df_y.iloc[5:10, 0] = np.nan
        
        # Test different imputers
        imputers = {
            "SimpleImputer (mean)": sklearn.impute.SimpleImputer(strategy="mean"),
            "SimpleImputer (median)": sklearn.impute.SimpleImputer(strategy="median"),
            "SimpleImputer (most_frequent)": sklearn.impute.SimpleImputer(strategy="most_frequent"),
            "KNNImputer": sklearn.impute.KNNImputer(n_neighbors=3)
        }
        
        for imputer_name, imputer in imputers.items():
            # Act
            transformer = SurrogateDataTransformer(
                uid=uuid.uuid4(),
                df_x=df_x,
                df_y=df_y,
                x_imputer=imputer,
                y_imputer=imputer
            )
            
            # Transform test data
            x_test = df_x.iloc[:10].copy()
            y_test = df_y.iloc[:10].copy()
            
            # Add some NaNs to test data too
            x_test.iloc[0, 0] = np.nan
            y_test.iloc[0, 0] = np.nan
            
            x_transformed, y_transformed = transformer.transform(x_test, y_test)
            
            # Assert
            # Check no NaNs in transformed data
            assert not np.isnan(x_transformed).any(), f"{imputer_name}: X should have no NaNs after imputation"
            assert not np.isnan(y_transformed).any(), f"{imputer_name}: Y should have no NaNs after imputation"
            
            # Make sure we can transform back
            x_inverse = transformer.inv_transform(x_transformed, is_x=True)
            assert x_inverse.shape == x_test.shape, f"{imputer_name}: Shape should be preserved in inverse transform"
            
            print(f"{imputer_name} test passed")
            
    def test_different_scalers(self):
        """Test transformer with different scalers from sklearn."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=100)
        
        # Test different scalers
        scalers = {
            "StandardScaler": sklearn.preprocessing.StandardScaler(),
            "MinMaxScaler": sklearn.preprocessing.MinMaxScaler(),
            "RobustScaler": sklearn.preprocessing.RobustScaler(),
            "MaxAbsScaler": sklearn.preprocessing.MaxAbsScaler(),
        }
        
        for scaler_name, scaler in scalers.items():
            # Act
            transformer = SurrogateDataTransformer(
                uid=uuid.uuid4(),
                df_x=df_x,
                df_y=df_y,
                x_scaler=scaler,
                y_scaler=scaler
            )
            
            # Transform test data
            x_test = df_x.iloc[:10].copy()
            y_test = df_y.iloc[:10].copy()
            
            x_transformed, y_transformed = transformer.transform(x_test, y_test)
            
            # Inverse transform
            x_inverse = transformer.inv_transform(x_transformed, is_x=True)
            y_inverse = transformer.inv_transform(y_transformed, is_x=False)
            
            # Assert
            # Verify data is recovered through inverse transform within tolerance
            verify_transform_inverse_cycle(x_test, x_transformed, x_inverse)
            verify_transform_inverse_cycle(y_test, y_transformed, y_inverse)
            
            print(f"{scaler_name} test passed")
    
    def test_serialization_deserialization(self):
        """Test serialization and deserialization of transformer."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=100)
        
        # Create transformer with both imputer and scaler
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            x_imputer=sklearn.impute.SimpleImputer(),
            y_imputer=sklearn.impute.SimpleImputer(),
            x_scaler=sklearn.preprocessing.StandardScaler(),
            y_scaler=sklearn.preprocessing.MinMaxScaler()
        )
        
        # Act - Serialize
        serialized_data = transformer.serialize()
        
        # Deserialize
        deserialized_transformer = SurrogateDataTransformer.deserialize(serialized_data)
        
        # Test both transformers with the same data
        x_test = df_x.iloc[:5].copy()
        y_test = df_y.iloc[:5].copy()
        
        # Add some NaNs to test imputation
        x_test.iloc[0, 0] = np.nan
        
        # Transform with both transformers
        x1_transformed, y1_transformed = transformer.transform(x_test, y_test)
        x2_transformed, y2_transformed = deserialized_transformer.transform(x_test, y_test)
        
        # Assert
        # Transformers should produce identical results
        assert np.allclose(x1_transformed, x2_transformed, equal_nan=True), \
            "Deserialized transformer should produce same X transforms"
        assert np.allclose(y1_transformed, y2_transformed, equal_nan=True), \
            "Deserialized transformer should produce same Y transforms"
        
        # Inverse transforms should also match
        x1_inverse = transformer.inv_transform(x1_transformed, is_x=True)
        x2_inverse = deserialized_transformer.inv_transform(x2_transformed, is_x=True)
        
        assert np.allclose(x1_inverse, x2_inverse, equal_nan=True), \
            "Deserialized transformer should produce same X inverse transforms"
        
        # Check serialized data includes key fields
        assert 'uid' in serialized_data, "Serialized data should include uid"
        assert 'x_imputer' in serialized_data, "Serialized data should include x_imputer"
        assert 'x_scaler' in serialized_data, "Serialized data should include x_scaler"
        
        print("Serialization/deserialization test passed")


class TestSequenceData:
    """Tests for the SurrogateDataTransformer with time series data."""
    
    def test_time_series_initialization(self):
        """Test initialization with time series data."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=5, n_timesteps=10)
        
        # Act
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )
        
        # Assert
        assert transformer._timeset_col == "timeset", "Timeset column should be set"
        assert transformer._timestep_col == "timestep", "Timestep column should be set"
        
        # Verify feature columns exclude time columns
        x_features = transformer._initial_x_variable_cols
        assert "timeset" not in x_features, "Timeset should not be in feature columns"
        assert "timestep" not in x_features, "Timestep should not be in feature columns"
        assert len(x_features) == len(df_x.columns) - 2, "Should have all columns except timeset and timestep"
        
        print("Time series initialization test passed")
        
    def test_time_series_transformation(self):
        """Test transformation of time series data."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=5, n_timesteps=10)
        
        # Act
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            x_scaler=sklearn.preprocessing.StandardScaler(),
            y_scaler=sklearn.preprocessing.StandardScaler(),
            timeset_col="timeset",
            timestep_col="timestep"
        )
        
        # Get a single timeset for testing
        test_timeset = df_x[df_x["timeset"] == 0].copy()
        test_y_timeset = df_y[df_y["timeset"] == 0].copy()
        
        # First verify scaling works correctly on the full dataset
        full_x_transformed, _ = transformer.transform(df_x, None)
        full_feature_means = np.mean(full_x_transformed, axis=(0, 1))
        assert np.allclose(full_feature_means, 0, atol=1e-5), \
            f"Z-score transform should result in mean ~ 0 on full dataset, got {full_feature_means}"

        # Then continue with the test on a subset
        x_transformed, y_transformed = transformer.transform(test_timeset, test_y_timeset)
        
        # Assert
        # Should be 3D array with shape [1, n_timesteps, n_features]
        assert len(x_transformed.shape) == 3, "Transformed time series should be 3D array"
        assert x_transformed.shape[0] == 1, "Should have 1 timeset"
        assert x_transformed.shape[1] == len(test_timeset["timestep"].unique()), \
            "Should have correct number of timesteps"
        
        # For z-score transformed data, mean should be near 0 and std near 1 along feature dimension
        feature_means = np.mean(x_transformed, axis=(0, 1))  # Mean across timeset and timestep
        feature_stds = np.std(x_transformed, axis=(0, 1))    # Std across timeset and timestep
        
        assert np.allclose(feature_means, 0, atol=1), \
            f"Z-score transform should result in mean ~ 0, got {feature_means}"
        assert np.allclose(feature_stds, 1, atol=1), \
            f"Z-score transform should result in std ~ 1, got {feature_stds}"
        
        print("Time series transformation test passed")
        
    def test_time_series_inverse_transform(self):
        """Test inverse transformation of time series data."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=5, n_timesteps=8)
        
        # Act
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            x_scaler=sklearn.preprocessing.StandardScaler(),
            y_scaler=sklearn.preprocessing.StandardScaler(),
            timeset_col="timeset",
            timestep_col="timestep"
        )
        
        # Get a single timeset for testing
        test_timeset = df_x[df_x["timeset"].isin([0,1,2])].copy()
        test_y_timeset = df_y[df_y["timeset"].isin([0,1,2])].copy()
        
        # Transform
        x_transformed, y_transformed = transformer.transform(test_timeset, test_y_timeset)
        
        # Inverse transform
        x_inverse = transformer.inv_transform(x_transformed, is_x=True)
        
        # Assert
        # Check shape of inverse transformed data
        assert x_inverse.shape[0] == test_timeset.shape[0], \
            f"Inverse transform should recover original row count, got {x_inverse.shape[0]} vs {test_timeset.shape[0]}"
        
        # Should include timeset and timestep columns
        assert "timeset" in x_inverse.columns, "Inverse transform should preserve timeset column"
        assert "timestep" in x_inverse.columns, "Inverse transform should preserve timestep column"
        
        # Feature values should be approximately recovered
        for col in test_timeset.columns:
            if col not in ["timeset", "timestep"]:
                assert np.allclose(test_timeset[col].to_numpy(), x_inverse[col].to_numpy(), rtol=1e-5, atol=1e-5), \
                    f"Values not recovered for column {col}"
        
        print("Time series inverse transform test passed")
        
    def test_time_series_serialization(self):
        """Test serialization and deserialization of transformer with time series data."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=3, n_timesteps=8)

        # Create a col2uid_dict mapping for the test
        col2uid_dict = {col: uuid.uuid4() for col in df_x.columns}

        # Create transformer with both imputer and scaler, and col2uid_dict
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            x_scaler=sklearn.preprocessing.StandardScaler(),
            y_scaler=sklearn.preprocessing.StandardScaler(),
            timeset_col="timeset",
            timestep_col="timestep",
            col2uid_dict=col2uid_dict
        )

        # Act - Serialize
        serialized_data = transformer.serialize()

        # Deserialize
        deserialized_transformer = SurrogateDataTransformer.deserialize(serialized_data)

        # Assert col2uid_dict is preserved (if present in serialized data)
        if hasattr(transformer, '_col2var'):
            assert transformer._col2var == col2uid_dict, "Original transformer col2uid_dict should match input"
        if hasattr(deserialized_transformer, '_col2var'):
            assert deserialized_transformer._col2var == col2uid_dict, "Deserialized transformer should preserve col2uid_dict"

        # Test both transformers with the same data
        test_timeset = df_x[df_x["timeset"] == 1].copy()  # Use a different timeset than training

        # Transform with both transformers
        x1_transformed, _ = transformer.transform(test_timeset, None)
        x2_transformed, _ = deserialized_transformer.transform(test_timeset, None)

        # Assert
        # Transformers should produce identical results
        assert np.allclose(x1_transformed, x2_transformed, equal_nan=True), \
            "Deserialized transformer should produce same transforms for time series data"

        # Inverse transforms should match
        x1_inverse = transformer.inv_transform(x1_transformed, is_x=True)
        x2_inverse = deserialized_transformer.inv_transform(x2_transformed, is_x=True)

        # Columns should match
        assert set(x1_inverse.columns) == set(x2_inverse.columns), \
            "Deserialized transformer should produce same columns"

        # Feature values should match
        for col in x1_inverse.columns:
            if col not in ["timeset", "timestep"]:
                assert np.allclose(x1_inverse[col].to_numpy(), x2_inverse[col].to_numpy(), rtol=1e-5, atol=1e-5), \
                    f"Values don't match for column {col} after deserialization"

        print("Time series serialization test passed")


class TestFailCases:
    """Tests for error handling and edge cases in SurrogateDataTransformer."""
    
    def test_invalid_time_columns(self):
        """Test error handling for invalid time series column specifications."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=100)
        
        # Act & Assert - One time column is None, the other is provided
        try:
            transformer = SurrogateDataTransformer(
                uid=uuid.uuid4(),
                df_x=df_x,
                df_y=df_y,
                timeset_col="timeset",  # Specified
                timestep_col=None       # Missing
            )
            assert False, "Should have raised AttributeError for incomplete time columns"
        except AttributeError as e:
            assert "both timeset and timestep" in str(e).lower(), \
                f"Expected error about both columns required, got: {str(e)}"
        
        # Act & Assert - Both time columns provided but don't exist in dataframe
        try:
            transformer = SurrogateDataTransformer(
                uid=uuid.uuid4(),
                df_x=df_x,
                df_y=df_y,
                timeset_col="non_existent_column",
                timestep_col="another_missing_column"
            )
            assert False, "Should have raised AttributeError for non-existent columns"
        except AttributeError as e:
            assert "not specified" in str(e).lower(), f"Expected error about column not found, got: {str(e)}"
        
        print("Invalid time columns test passed")
        
    def test_invalid_transformer_types(self):
        """Test error handling for incompatible transformer types."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=100)
        
        # Create an object that's not a valid transformer
        invalid_transformer = "Not a transformer"
        
        # Act & Assert
        try:
            transformer = SurrogateDataTransformer(
                uid=uuid.uuid4(),
                df_x=df_x,
                df_y=df_y,
                x_scaler=invalid_transformer # type: ignore
            )
            x_test = df_x.iloc[:10].copy()
            transformer.transform(x_test, None)
            assert False, "Should have raised an error for invalid transformer"
        except Exception as e:
            # Different errors might be raised depending on how the code validates transformers
            # Just check that something went wrong
            assert True, f"Expected error for invalid transformer, got: {str(e)}"
        
        print("Invalid transformer test passed")
        
    def test_wrong_array_shape(self):
        """Test error handling for wrong array shapes in inverse transform."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=3, n_timesteps=5)
        
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )
        
        # Act & Assert - Wrong shape for time series (2D instead of 3D)
        try:
            wrong_shape_array = np.zeros((10, 3))  # 2D array
            transformer.inv_transform(wrong_shape_array, is_x=True)
            assert False, "Should have raised ValueError for wrong array shape"
        except ValueError as e:
            assert "expected 3d array" in str(e).lower() or "shape" in str(e).lower(), \
                f"Expected error about wrong shape, got: {str(e)}"
        
        # Create another transformer for flat data
        df_x2, df_y2 = create_sample_data(n_samples=50)
        flat_transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x2,
            df_y=df_y2
        )
        
        # Act & Assert - Wrong shape for flat data (3D instead of 2D)
        try:
            wrong_shape_array = np.zeros((2, 3, 4))  # 3D array for flat data
            flat_transformer.inv_transform(wrong_shape_array, is_x=True)
            assert False, "Should have raised ValueError for wrong array shape"
        except ValueError as e:
            assert "expected 2d array" in str(e).lower() or "shape" in str(e).lower(), \
                f"Expected error about wrong shape, got: {str(e)}"
        
        print("Wrong array shape test passed")
        

class TestColumnOrder:
    """Tests for column ordering functionality in SurrogateDataTransformer."""
    
    def test_standard_data_column_reordering(self):
        """Test that standard data columns are properly reordered."""
        # Arrange - Create standard data and a transformer
        df_x_ordered, df_y = create_sample_data(n_samples=20, n_features=3)
        
        # Create transformer with original ordered data
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x_ordered,
            df_y=df_y,
            x_scaler=sklearn.preprocessing.StandardScaler()
        )
        
        # Create test data with columns shuffled
        shuffled_cols = list(df_x_ordered.columns)
        np.random.shuffle(shuffled_cols)
        df_x_shuffled = df_x_ordered.copy()[shuffled_cols]
        
        # Act - Transform with shuffled columns
        x_transformed, _ = transformer.transform(df_x_shuffled, None)
        
        # Transform with correct column order for comparison
        x_ordered_transformed, _ = transformer.transform(df_x_ordered, None)
        
        # Assert - Transformed arrays should be identical regardless of input column order
        assert np.allclose(x_transformed, x_ordered_transformed), \
            "Transformations should be identical regardless of input column order"
            
        # Inverse transform and check result
        x_inverse = transformer.inv_transform(x_transformed, is_x=True)
        
        # Verify column order in inverse transform matches original
        assert list(x_inverse.columns) == list(df_x_ordered.columns), \
            f"Inverse transform column order incorrect. Expected {list(df_x_ordered.columns)}, got {list(x_inverse.columns)}"
        
        print("Standard data column reordering test passed")
    
    def test_timeseries_data_column_reordering(self):
        """Test that time series data columns are properly reordered."""
        # Arrange - Create time series data
        df_x_ordered, df_y = create_time_series_data(n_timesets=3, n_timesteps=5, n_features=3)
        
        # Create transformer with original ordered data
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x_ordered,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep",
            x_scaler=sklearn.preprocessing.StandardScaler()
        )
        
        # Create shuffled columns version, ensuring timeset/timestep are included
        feature_cols = [col for col in df_x_ordered.columns if col not in ["timeset", "timestep"]]
        np.random.shuffle(feature_cols)
        shuffled_cols = ["timeset", "timestep"] + feature_cols
        df_x_shuffled = df_x_ordered.copy()[shuffled_cols]
        
        # Create another version with timeset/timestep not at the beginning
        mixed_cols = feature_cols[0:1] + ["timeset", "timestep"] + feature_cols[1:]
        df_x_mixed = df_x_ordered.copy()[mixed_cols]
        
        # Act - Transform with different column arrangements
        x_shuffled_transformed, _ = transformer.transform(df_x_shuffled, None)
        x_mixed_transformed, _ = transformer.transform(df_x_mixed, None)
        x_ordered_transformed, _ = transformer.transform(df_x_ordered, None)
        
        # Assert - All transformed arrays should be identical
        assert np.allclose(x_shuffled_transformed, x_ordered_transformed), \
            "Transformation with shuffled columns should match original"
        assert np.allclose(x_mixed_transformed, x_ordered_transformed), \
            "Transformation with mixed columns should match original"
        
        # Inverse transform and check results
        x_shuffled_inverse = transformer.inv_transform(x_shuffled_transformed, is_x=True)
        
        # Verify time columns are first, followed by feature columns in original order
        expected_cols = ["timeset", "timestep"] + sorted([col for col in df_x_ordered.columns 
                                                        if col not in ["timeset", "timestep"]])
        
        # Column order should match the canonical expected order from the transformer
        expected_order = ["timeset", "timestep"] + transformer.x_cols
        assert list(x_shuffled_inverse.columns) == expected_order, \
            f"Inverse transform column order incorrect. Expected {expected_order}, got {list(x_shuffled_inverse.columns)}"
        
        print("Time series data column reordering test passed")
    
    def test_missing_columns_error(self):
        """Test that appropriate errors are raised when columns are missing."""
        # Arrange - Create standard data
        df_x, df_y = create_sample_data(n_samples=20, n_features=4)
        
        # Create transformer
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y
        )
        
        # Create test data with a column missing
        df_x_missing = df_x.drop(columns=[df_x.columns[0]])
        
        # Act & Assert - Should raise error about missing column
        with pytest.raises(ValueError) as excinfo:
            x_transformed, _ = transformer.transform(df_x_missing, None)
            
            
        print("Missing columns error test passed")
    
    def test_timeseries_missing_time_column(self):
        """Test error when a time series specific column is missing."""
        # Arrange - Create time series data
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=3)
        
        # Create transformer
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )
        
        # Create test data with missing timestep column
        df_x_missing_time = df_x.drop(columns=["timestep"])
        
        # Act & Assert - Should raise error
        with pytest.raises(ValueError) as excinfo:
            x_transformed, _ = transformer.transform(df_x_missing_time, None)
            
            
        print("Time series missing column test passed")
    
    
    def test_column_order_preservation_through_serialization(self):
        """Test that column order requirements are preserved through serialization/deserialization."""
        # Arrange - Create data with specific column order
        df_x_cols = ["feature_2", "feature_0", "feature_1"]  # Intentionally out of order
        df_x = pd.DataFrame(np.random.rand(20, 3), columns=df_x_cols)
        df_y = pd.DataFrame(np.random.rand(20, 1), columns=["target"])
        
        # Create transformer
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            x_scaler=sklearn.preprocessing.StandardScaler()
        )
        
        # Check initial column order is preserved in the transformer
        original_x_cols = transformer.x_cols
        assert set(original_x_cols) == set(df_x_cols), "Transformer should preserve all column names"
        
        # Act - Serialize and deserialize
        serialized_data = transformer.serialize()
        deserialized_transformer = SurrogateDataTransformer.deserialize(serialized_data)
        
        # Create test data with shuffled columns
        np.random.shuffle(df_x_cols)
        df_x_shuffled = df_x.copy()[df_x_cols]
        
        # Transform with both transformers
        x1_transformed, _ = transformer.transform(df_x_shuffled, None)
        x2_transformed, _ = deserialized_transformer.transform(df_x_shuffled, None)
        
        # Assert - Transformations should be identical
        assert np.allclose(x1_transformed, x2_transformed), \
            "Original and deserialized transformers should produce identical results"
            
        # Check column order after inverse transform
        x1_inverse = transformer.inv_transform(x1_transformed, is_x=True)
        x2_inverse = deserialized_transformer.inv_transform(x2_transformed, is_x=True)
        
        assert list(x1_inverse.columns) == list(x2_inverse.columns), \
            f"Column order should be preserved after serialization. Original: {list(x1_inverse.columns)}, Deserialized: {list(x2_inverse.columns)}"
            
        print("Column order preservation through serialization test passed")
    
    def test_internal_reorder_df_function(self):
        """Test the reorder_df function directly to verify its behavior."""
        # This test directly accesses the internal reorder_df function by creating a transformer instance
        # and accessing its private method through the transform method
        
        # Arrange - Create simple data and transformer
        df_x, df_y = create_sample_data(n_samples=10, n_features=3)
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y
        )
        
        # Get access to the reorder_df function
        
        # Create test cases 
        # 1. Columns in different order
        df_reordered = df_x.copy()[df_x.columns[::-1]]  # Reverse column order
        
        # Use the function through the transform method which calls reorder_df internally
        x_transformed, _ = transformer.transform(df_reordered, None)
        
        # Verify transformer correctly handles the reordered columns by producing valid output
        assert x_transformed.shape[1] == len(df_x.columns), \
            f"Transformed data should have the right number of columns: {len(df_x.columns)}"
        
        # 2. Create DataFrame with missing column - wrap in a separate function for pytest
        def transform_missing_col():
            df_missing = df_x.copy().drop(columns=[df_x.columns[0]])
            return transformer.transform(df_missing, None)
        
        # Verify error handling
        with pytest.raises(ValueError) as excinfo:
            transform_missing_col()
        assert "missing" in str(excinfo.value).lower(), \
            f"Error should mention missing column, got: {str(excinfo.value)}"
        
        print("Internal reorder_df function test passed")


class TestTransformEdgeCases:
    """Tests for the SurrogateDataTransformer's missing data handling functionality."""

    def test_fill_missing_timesteps_default_value(self):
        """Test filling missing timesteps with default value (0.0)."""
        # Arrange - Create timeseries data with missing timesteps
        df_x, df_y = create_time_series_data(n_timesets=3, n_timesteps=5, n_features=2)

        # Create transformer
        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Create test data with missing timesteps (remove timestep 2 from timeset 0 and timestep 4 from timeset 1)
        df_incomplete = df_x.copy()
        df_incomplete = df_incomplete[~((df_incomplete["timeset"] == 0) & (df_incomplete["timestep"] == 2))]
        df_incomplete = df_incomplete[~((df_incomplete["timeset"] == 1) & (df_incomplete["timestep"] == 4))]

        # Act - Fill missing timesteps
        df_filled = transformer._fill_missing_timesteps(df_incomplete, is_x=True)

        # Assert
        # Check that all timesteps are now present
        expected_rows = 3 * 5  # 3 timesets × 5 timesteps
        assert len(df_filled) == expected_rows, f"Expected {expected_rows} rows, got {len(df_filled)}"

        # Check that missing timesteps were filled with 0.0
        filled_row_ts0_step2 = df_filled[(df_filled["timeset"] == 0) & (df_filled["timestep"] == 2)]
        filled_row_ts1_step4 = df_filled[(df_filled["timeset"] == 1) & (df_filled["timestep"] == 4)]

        assert len(filled_row_ts0_step2) == 1, "Should have exactly one row for timeset 0, timestep 2"
        assert len(filled_row_ts1_step4) == 1, "Should have exactly one row for timeset 1, timestep 4"

        # Check that feature values are filled with 0.0
        for feature_col in ["feature_0", "feature_1"]:
            assert filled_row_ts0_step2[feature_col].iloc[0] == 0.0, f"Feature {feature_col} should be filled with 0.0"
            assert filled_row_ts1_step4[feature_col].iloc[0] == 0.0, f"Feature {feature_col} should be filled with 0.0"

        # Check that data is properly sorted
        assert df_filled["timeset"].is_monotonic_increasing or df_filled.groupby("timeset")["timestep"].apply(lambda x: x.is_monotonic_increasing).all(), \
            "Data should be sorted by timeset and timestep"


    def test_fill_missing_timesteps_custom_value(self):
        """Test filling missing timesteps with custom fill value."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=4, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Create incomplete data
        df_incomplete = df_x.copy()
        df_incomplete = df_incomplete[~((df_incomplete["timeset"] == 0) & (df_incomplete["timestep"] == 1))]

        # Act - Fill with custom value
        custom_fill_value = -999.0
        df_filled = transformer._fill_missing_timesteps(df_incomplete, is_x=True, val=custom_fill_value)

        # Assert
        filled_row = df_filled[(df_filled["timeset"] == 0) & (df_filled["timestep"] == 1)]
        assert len(filled_row) == 1, "Should have exactly one filled row"

        for feature_col in ["feature_0", "feature_1"]:
            assert filled_row[feature_col].iloc[0] == custom_fill_value, \
                f"Feature {feature_col} should be filled with custom value {custom_fill_value}"


    def test_fill_missing_timesteps_no_missing_data(self):
        """Test behavior when no timesteps are missing (should return unchanged data)."""
        # Arrange - Create complete timeseries data
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=3, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Act - Fill missing timesteps on complete data
        df_filled = transformer._fill_missing_timesteps(df_x, is_x=True)

        # Assert - Data should have same content but may have different column order
        assert len(df_filled) == len(df_x), "Length should be unchanged"

        # Check that all original columns are present
        assert set(df_filled.columns) == set(df_x.columns), \
            f"Columns should be the same: original {set(df_x.columns)}, filled {set(df_filled.columns)}"

        # Sort both dataframes for comparison and reorder columns to match
        df_x_sorted = df_x.sort_values(by=["timeset", "timestep"]).reset_index(drop=True)
        df_filled_sorted = df_filled.sort_values(by=["timeset", "timestep"]).reset_index(drop=True)

        # Reorder columns to match for comparison
        df_filled_reordered = df_filled_sorted[df_x.columns]

        try:
            pd.testing.assert_frame_equal(df_x_sorted, df_filled_reordered, check_dtype=False)
        except AssertionError as e:
            raise AssertionError("Data values should be unchanged when no timesteps are missing") from e

        # Verify that time columns are first in the filled dataframe (expected behavior)
        expected_first_cols = ["timeset", "timestep"]
        actual_first_cols = list(df_filled.columns[:2])
        assert actual_first_cols == expected_first_cols, \
            f"Time columns should be first: expected {expected_first_cols}, got {actual_first_cols}"


    def test_fill_missing_timesteps_multiple_timesets_different_patterns(self):
        """Test with multiple timesets having different missing timestep patterns."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=4, n_timesteps=5, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Create complex missing pattern:
        # Timeset 0: missing timestep 1 and 3
        # Timeset 1: missing timestep 0 and 4
        # Timeset 2: missing timestep 2
        # Timeset 3: complete (no missing)
        df_incomplete = df_x.copy()
        df_incomplete = df_incomplete[~((df_incomplete["timeset"] == 0) & (df_incomplete["timestep"].isin([1, 3])))]
        df_incomplete = df_incomplete[~((df_incomplete["timeset"] == 1) & (df_incomplete["timestep"].isin([0, 4])))]
        df_incomplete = df_incomplete[~((df_incomplete["timeset"] == 2) & (df_incomplete["timestep"] == 2))]

        # Act
        df_filled = transformer._fill_missing_timesteps(df_incomplete, is_x=True)

        # Assert
        expected_rows = 4 * 5  # 4 timesets × 5 timesteps
        assert len(df_filled) == expected_rows, f"Expected {expected_rows} rows, got {len(df_filled)}"

        # Check specific filled entries
        filled_entries = [
            (0, 1), (0, 3),  # Timeset 0
            (1, 0), (1, 4),  # Timeset 1
            (2, 2)           # Timeset 2
        ]

        for timeset, timestep in filled_entries:
            filled_row = df_filled[(df_filled["timeset"] == timeset) & (df_filled["timestep"] == timestep)]
            assert len(filled_row) == 1, f"Should have exactly one row for timeset {timeset}, timestep {timestep}"

            for feature_col in ["feature_0", "feature_1"]:
                assert filled_row[feature_col].iloc[0] == 0.0, \
                    f"Feature {feature_col} should be 0.0 for timeset {timeset}, timestep {timestep}"

        # Verify each timeset has all timesteps
        for timeset in range(4):
            timeset_data = df_filled[df_filled["timeset"] == timeset]
            assert len(timeset_data) == 5, f"Timeset {timeset} should have 5 timesteps"
            assert set(timeset_data["timestep"]) == set(range(5)), \
                f"Timeset {timeset} should have timesteps 0-4"


    def test_fill_missing_timesteps_ordering(self):
        """Verify that filled data maintains proper timeset/timestep ordering."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=3, n_timesteps=4, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Create data with missing timesteps and shuffle the order
        df_incomplete = df_x.copy()
        df_incomplete = df_incomplete[~((df_incomplete["timeset"] == 1) & (df_incomplete["timestep"] == 2))]
        df_incomplete = df_incomplete.sample(frac=1).reset_index(drop=True)  # Shuffle

        # Act
        df_filled = transformer._fill_missing_timesteps(df_incomplete, is_x=True)

        # Assert - Check ordering
        # Data should be sorted by timeset, then by timestep
        for i in range(len(df_filled) - 1):
            current_timeset = df_filled.iloc[i]["timeset"]
            current_timestep = df_filled.iloc[i]["timestep"]
            next_timeset = df_filled.iloc[i + 1]["timeset"]
            next_timestep = df_filled.iloc[i + 1]["timestep"]

            # Either next timeset is greater, or same timeset with greater timestep
            assert (next_timeset > current_timeset) or \
                   (next_timeset == current_timeset and next_timestep > current_timestep), \
                   f"Data not properly ordered at index {i}: ({current_timeset}, {current_timestep}) -> ({next_timeset}, {next_timestep})"


    def test_transform_with_fill_missing_timesteps_flag(self):
        """Test transformation of timeseries data with missing timesteps gets automatically filled."""
        # This is the current behavior - validation happens first, then filling.

        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=4, n_features=3)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep",
            x_scaler=sklearn.preprocessing.StandardScaler()
        )

        # Create test data with missing timesteps
        df_x_incomplete = df_x.copy()
        df_x_incomplete = df_x_incomplete[~((df_x_incomplete["timeset"] == 0) & (df_x_incomplete["timestep"] == 2))]
        df_x_incomplete = df_x_incomplete[~((df_x_incomplete["timeset"] == 1) & (df_x_incomplete["timestep"] == 1))]

        x_transformed_complete, _ = transformer.transform(df_x, None, fill_missing_timesteps=True)
        expected_shape = (2, 4, 3)  # 2 timesets, 4 timesteps, 3 features
        assert x_transformed_complete.shape == expected_shape, \
            f"Expected shape {expected_shape}, got {x_transformed_complete.shape}"


    def test_transform_with_fill_missing_timesteps_noflag(self):
        """
        Asserts that error is raised with incomplete timesteps and no fill
        """
        """Test transformation of timeseries data with missing timesteps gets automatically filled."""
        # This is the current behavior - validation happens first, then filling.

        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=4, n_features=3)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep",
            x_scaler=sklearn.preprocessing.StandardScaler()
        )

        # Create test data with missing timesteps
        df_x_incomplete = df_x.copy()
        df_x_incomplete = df_x_incomplete[~((df_x_incomplete["timeset"] == 0) & (df_x_incomplete["timestep"] == 2))]
        df_x_incomplete = df_x_incomplete[~((df_x_incomplete["timeset"] == 1) & (df_x_incomplete["timestep"] == 1))]

        with pytest.raises(ValueError):
            transformer.transform(df_x_incomplete, None, fill_missing_timesteps=False)

    def test_transform_fill_missing_timesteps_3d_shape(self):
        """Test that complete timeseries data produces correct 3D shape with fill_missing_timesteps flag."""

        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=3, n_timesteps=5, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Act - Use complete data with fill_missing_timesteps=True
        x_transformed, _ = transformer.transform(df_x, None, fill_missing_timesteps=True)

        # Assert
        expected_shape = (3, 5, 2)  # 3 timesets, 5 timesteps, 2 features
        assert x_transformed.shape == expected_shape, \
            f"Expected shape {expected_shape}, got {x_transformed.shape}"

        # Verify it's a proper 3D array
        assert len(x_transformed.shape) == 3, f"Should be 3D array, got {len(x_transformed.shape)}D"


        print("Transform fill missing timesteps 3D shape test passed")

    def test_transform_compare_with_without_fill_flag(self):
        """Compare results with and without fill_missing_timesteps flag on complete data."""
        # NOTE: Since validation happens before filling, both flags behave the same on complete data TODO - this is fixed. update all tests. 
        # and both fail on incomplete data. This test verifies the flag doesn't break complete data processing.

        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=3, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep",
            x_scaler=sklearn.preprocessing.StandardScaler()
        )

        # Act - Test with complete data
        x_with_fill, _ = transformer.transform(df_x, None, fill_missing_timesteps=True)
        x_without_fill, _ = transformer.transform(df_x, None, fill_missing_timesteps=False)

        # Assert - Both should produce identical results for complete data
        assert x_with_fill.shape == x_without_fill.shape, \
            f"Shapes should match for complete data: with_fill {x_with_fill.shape} vs without_fill {x_without_fill.shape}"

        expected_shape = (2, 3, 2)  # 2 timesets, 3 timesteps, 2 features
        assert x_with_fill.shape == expected_shape, \
            f"Expected shape {expected_shape}, got {x_with_fill.shape}"

        # Values should be very close (allowing for floating point precision)
        assert np.allclose(x_with_fill, x_without_fill), \
            "Transformed values should be identical for complete data regardless of fill flag"

        # Test that incomplete data fails with both flags
        df_x_incomplete = df_x.copy()
        df_x_incomplete = df_x_incomplete[~((df_x_incomplete["timeset"] == 0) & (df_x_incomplete["timestep"] == 1))]

        with pytest.raises(ValueError):
            transformer.transform(df_x_incomplete, None, fill_missing_timesteps=False)


    def test_validate_df_timesteps_complete_data(self):
        """Test validation passes for complete timeseries data."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=3, n_timesteps=4, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Act
        is_valid, message = transformer._validate_df_timesteps(df_x)

        # Assert
        assert is_valid, f"Validation should pass for complete data, got: {message}"
        assert "Valid timeseries data" in message, f"Expected success message, got: {message}"

        print("Validate df timesteps complete data test passed")

    def test_validate_df_timesteps_wrong_values(self):
        """Test validation fails when timestep values don't match expected values."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=4, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Create data with wrong timestep values
        df_wrong_timesteps = df_x.copy()
        df_wrong_timesteps.loc[df_wrong_timesteps["timestep"] == 2, "timestep"] = 99  # Change timestep 2 to 99

        # Act
        is_valid, message = transformer._validate_df_timesteps(df_wrong_timesteps)

        # Assert
        assert not is_valid, "Validation should fail when timestep values are wrong"
        assert "Missing timestep values:" in message and "Unexpected timestep values:" in message, \
            f"Expected timestep value mismatch error, got: {message}"

        print("Validate df timesteps wrong values test passed")

    def test_validate_df_timesteps_missing_timesteps_in_timeset(self):
        """Test validation fails when individual timesets are missing specific timesteps."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=3, n_timesteps=4, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Remove specific timesteps from specific timesets
        df_incomplete = df_x.copy()
        df_incomplete = df_incomplete[~((df_incomplete["timeset"] == 1) & (df_incomplete["timestep"] == 2))]
        df_incomplete = df_incomplete[~((df_incomplete["timeset"] == 2) & (df_incomplete["timestep"] == 0))]

        # Act
        is_valid, message = transformer._validate_df_timesteps(df_incomplete)

        # Assert
        assert not is_valid, "Validation should fail when timesets have missing timesteps"
        assert "Timeset 1 is missing timesteps:" in message, f"Expected timeset 1 missing timesteps error, got: {message}"
        assert "Timeset 2 is missing timesteps:" in message, f"Expected timeset 2 missing timesteps error, got: {message}"


    def test_validate_df_timesteps_wrong_row_count(self):
        """Test validation fails when row count doesn't match expected (timesets × timesteps)."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=3, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Remove some rows to create wrong count
        df_wrong_count = df_x.iloc[:-2].copy()  # Remove last 2 rows

        # Act
        is_valid, message = transformer._validate_df_timesteps(df_wrong_count)

        # Assert
        assert not is_valid, "Validation should fail when row count is wrong"
        assert "Row count mismatch:" in message, f"Expected row count mismatch error, got: {message}"


    def test_validate_df_cols_x_complete(self):
        """Test validation passes when all required X feature columns are present."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=20, n_features=4)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y
        )

        # Act
        is_valid, message = transformer._validate_df_cols(df_x, "x")

        # Assert
        assert is_valid, f"Validation should pass for complete X data, got: {message}"
        assert "All required X columns present" in message, f"Expected success message, got: {message}"

        print("Validate df cols X complete test passed")

    def test_validate_df_cols_y_complete(self):
        """Test validation passes when all required Y feature columns are present."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=20, n_features=3)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y
        )

        # Act
        is_valid, message = transformer._validate_df_cols(df_y, "y")

        # Assert
        assert is_valid, f"Validation should pass for complete Y data, got: {message}"
        assert "All required Y columns present" in message, f"Expected success message, got: {message}"

        print("Validate df cols Y complete test passed")

    def test_validate_df_cols_x_missing(self):
        """Test validation fails when X feature columns are missing."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=20, n_features=4)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y
        )

        # Create data missing some X columns
        df_x_missing = df_x.drop(columns=[df_x.columns[0], df_x.columns[2]])

        # Act
        is_valid, message = transformer._validate_df_cols(df_x_missing, "x")

        # Assert
        assert not is_valid, "Validation should fail when X columns are missing"
        assert "Missing required feature columns for X data:" in message, \
            f"Expected missing X columns error, got: {message}"


    def test_validate_df_cols_y_missing(self):
        """Test validation fails when Y feature columns are missing."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=20, n_features=3)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y
        )

        # Create data missing Y columns
        df_y_missing = df_y.drop(columns=[df_y.columns[0]])

        # Act
        is_valid, message = transformer._validate_df_cols(df_y_missing, "y")

        # Assert
        assert not is_valid, "Validation should fail when Y columns are missing"
        assert "Missing required feature columns for Y data:" in message, \
            f"Expected missing Y columns error, got: {message}"

        print("Validate df cols Y missing test passed")

    def test_validate_df_cols_extra_columns(self):
        """Test validation with extra columns (should pass)."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=20, n_features=3)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y
        )

        # Add extra columns
        df_x_extra = df_x.copy()
        df_x_extra["extra_col1"] = np.random.rand(len(df_x))
        df_x_extra["extra_col2"] = np.random.rand(len(df_x))

        # Act
        is_valid, message = transformer._validate_df_cols(df_x_extra, "x")

        # Assert
        assert is_valid, f"Validation should pass with extra columns, got: {message}"
        assert "All required X columns present" in message, f"Expected success message, got: {message}"

        print("Validate df cols extra columns test passed")

class TestInverseTransformEdgeCases:

    def test_inv_transform_wrong_feature_dimensions(self):
        """Test inverse transform fails gracefully when input array has wrong feature dimensions."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=3, n_features=3)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep",
            x_scaler=sklearn.preprocessing.StandardScaler()
        )

        # Create array with wrong feature dimension (2 instead of 3)
        wrong_array = np.random.rand(2, 3, 2)  # 2 timesets, 3 timesteps, 2 features (should be 3)

        # Act & Assert
        with pytest.raises(ValueError) as excinfo:
            transformer.inv_transform(wrong_array, is_x=True)

        assert "Feature dimension mismatch" in str(excinfo.value), \
            f"Expected feature dimension error, got: {str(excinfo.value)}"

        print("Inverse transform wrong feature dimensions test passed")

    def test_inv_transform_timeseries_validation_failure(self):
        """Test inverse transform fails gracefully when timeseries validation fails."""
        # Arrange
        df_x, df_y = create_time_series_data(n_timesets=2, n_timesteps=4, n_features=2)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            timeset_col="timeset",
            timestep_col="timestep"
        )

        # Create array with wrong timestep dimension (3 instead of 4)
        wrong_array = np.random.rand(2, 3, 2)  # 2 timesets, 3 timesteps (should be 4), 2 features

        # Act & Assert
        with pytest.raises(ValueError) as excinfo:
            transformer.inv_transform(wrong_array, is_x=True)

        assert "Timestep count mismatch" in str(excinfo.value), \
            f"Expected timestep count error, got: {str(excinfo.value)}"

        print("Inverse transform timeseries validation failure test passed")

    def test_inv_transform_column_validation_failure(self):
        """Test inverse transform fails gracefully when column validation fails."""
        # Arrange - Create transformer with specific columns
        df_x, df_y = create_sample_data(n_samples=20, n_features=3)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y,
            x_scaler=sklearn.preprocessing.StandardScaler()
        )

        # Create array with wrong number of features (2 instead of 3)
        wrong_array = np.random.rand(10, 2)  # 10 samples, 2 features (should be 3)

        # Act & Assert
        with pytest.raises(ValueError) as excinfo:
            transformer.inv_transform(wrong_array, is_x=True)

        assert "Feature dimension mismatch" in str(excinfo.value), \
            f"Expected feature dimension error, got: {str(excinfo.value)}"

        print("Inverse transform column validation failure test passed")

    def test_inv_transform_non_timeseries_wrong_dimensions(self):
        """Test inverse transform error handling for non-timeseries data with wrong dimensions."""
        # Arrange
        df_x, df_y = create_sample_data(n_samples=20, n_features=3)

        transformer = SurrogateDataTransformer(
            uid=uuid.uuid4(),
            df_x=df_x,
            df_y=df_y
        )

        # Test case 1: 3D array for non-timeseries (should be 2D)
        wrong_3d_array = np.random.rand(2, 10, 3)

        with pytest.raises(ValueError) as excinfo:
            transformer.inv_transform(wrong_3d_array, is_x=True)

        assert "Expected 2D array for standard data" in str(excinfo.value), \
            f"Expected 2D array error, got: {str(excinfo.value)}"

        # Test case 2: Wrong feature count
        wrong_features_array = np.random.rand(10, 5)  # 5 features instead of 3

        with pytest.raises(ValueError) as excinfo:
            transformer.inv_transform(wrong_features_array, is_x=True)

        assert "Feature dimension mismatch" in str(excinfo.value), \
            f"Expected feature mismatch error, got: {str(excinfo.value)}"

        print("Inverse transform non-timeseries wrong dimensions test passed")


# Simple runner for direct execution
if __name__ == "__main__":
    print("Ready for testing")
    pytest.main(["-v", __file__])
    print(f"Tests passed for {__file__}")