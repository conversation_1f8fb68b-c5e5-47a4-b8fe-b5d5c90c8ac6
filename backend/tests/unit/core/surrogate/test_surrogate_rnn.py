
import uuid
import os
import pandas as pd
import json
import numpy as np
import sklearn.preprocessing
import pytest
import torch
import subprocess
import sys
from pathlib import Path
from typing import Dict, Any, List, Tuple
import typing as T

from backend.core._surrogate.models.model_base import PyTorchSurrogateModel
from backend.core._surrogate.transformers.base_transformer import SurrogateDataTransformer
from backend.core._surrogate._enums import EnumSurrogateAlgorithm, EnumTrainingStatus
from backend.core._surrogate.valueobjects import *
from backend.core._surrogate.entities import ENTTrainingJob
from backend.infrastructure._db.repo import SurrogateFolderRepo
from backend.core._surrogate.trainers.trainer_rnn import Seq2SeqTSTrainer
# Act: Execute the main training function
from backend.core._surrogate.entrypoints.seqmodel import main

##############

def create_basic_job() -> ENTTrainingJob:
    """Create a mock ENTTrainingJob for testing."""
    training_config = _create_training_config()
    model_config = create_time_series_model_config()
    
    return ENTTrainingJob(
            metadata= create_basic_metadata(),
            training_configuration=training_config,
            model_configuration=model_config,
            status=EnumTrainingStatus.PENDING.value
    )


def create_time_series_model_config(overrides: Optional[Dict[str, Any]] = None) -> VOConfig_Model:
    """
    Create a standard model configuration with optional overrides.
    
    Args:
        overrides: Dictionary of parameter name to value overrides
        
    Returns:
        VOConfig_Model with reasonable defaults
    """
    params = [
        VOConfig_Parameter(
            name=Hyperparams.NN.HIDDEN_SIZE,
            value=32,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="integer",
                min_value=16,
                max_value=128
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.SEQ_NUM_LAYERS,
            value=1,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="integer",
                min_value=1,
                max_value=3
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.DENSE_NUM_LAYERS,
            value=1,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="integer",
                min_value=1,
                max_value=3
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.RNN_TYPE,
            value="LSTM",
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="categorical",
                choices=["LSTM", "GRU"]
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.DROPOUT,
            value=0.1,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="continuous",
                min_value=0.0,
                max_value=0.5
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.BATCH_SIZE,
            value=16,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="categorical",
                choices=[8, 16, 32, 64]
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.LEARNING_RATE,
            value=0.001,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="continuous_logscale",
                min_value=1e-5,
                max_value=1e-2
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.LOSS_FUNCTION,
            value="mse",
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="categorical",
                choices=["mse", "mae"]
            )
        )
    ]
    
    # Override param values if specified
    if overrides:
        for param in params:
            if param.name.name in overrides:
                # Create a new parameter with the overridden value
                param = VOConfig_Parameter(
                    name=param.name,
                    value=overrides[param.name.name],
                    tunable=param.tunable,
                    bounds=param.bounds
                )

    return VOConfig_Model(
        algorithm=EnumSurrogateAlgorithm.RNN_TS,
        parameters=params
    )

def _create_training_config(max_iterations: int = 30) -> VOConfig_Training:
    """
    Create a standard training configuration.
    
    Args:
        max_iterations: Number of training epochs
        
    Returns:
        VOConfig_Training instance
    """
    return VOConfig_Training(
        primary_metric=EnumMetricName.RMSE,
        max_iterations=max_iterations,
        random_seed=42,
        validation_strategy="simple_split",
        validation_split=0.2,
        enable_early_stopping=True,
        early_stopping_rounds=5,
        early_stopping_min_delta=0.001
    )


def create_basic_metadata() -> VOMetadata_General:
    """Generate a basic metadata object for testing."""
    return VOMetadata_General(
        label="Test Surrogate Model",
        user_reference="test_user",
        atlas_reference="test_atlas",
        surrogate_algo=EnumSurrogateAlgorithm.RNN_TS,
        description="Test metadata for surrogate model"
    )


def create_basic_datatransformer() -> SurrogateDataTransformer:
    """Generate a basic data transformer for testing."""
    # Create sample data
    df_x = pd.DataFrame({
        'feature1': [1.0, 2.0, 3.0, 4.0, 5.0],
        'feature2': [0.5, 1.0, 1.5, 2.0, 2.5],
        'feature3': [0.1, 0.2, 0.3, 0.4, 0.5]
    })
    
    df_y = pd.DataFrame({
        'target': [10.0, 20.0, 30.0, 40.0, 50.0]
    })
    
    # Create transformer with standard scalers
    transformer = SurrogateDataTransformer(
        uid=uuid.uuid4(),
        df_x=df_x,
        df_y=df_y,
        x_scaler=sklearn.preprocessing.StandardScaler(),
        y_scaler=sklearn.preprocessing.StandardScaler()
    )
    return transformer


##############
# ENTRY SCRIPT VALIDATION TESTS
##############

class TestSeqModelEntryScript:
    """
    Test suite for validating the seqmodel.py entry script functionality.

    This validates the critical path for distributed training:
    1. JSON deserialization of job configs and training data
    2. Training execution without runtime failures
    3. Model serialization and job entity persistence
    4. Round-trip consistency (save/load model equivalence)
    """

    def test_deserialize_job_from_json(self, tmp_path):
        """
        Test JSON deserialization of ENTTrainingJob.

        Validates that job configurations can be reliably deserialized
        from JSON format as expected by the entry script.
        """
        # Arrange: Create test job and serialize to JSON
        original_job = create_basic_job()
        job_json_path = tmp_path / "job_config.json"

        with open(job_json_path, 'w') as f:
            f.write(original_job.model_dump_json(indent=2))

        # Act: Import and test the deserialization function
        from backend.core._surrogate.entrypoints.seqmodel import deserialize_job
        deserialized_job = deserialize_job(str(job_json_path))

        # Assert: Verify critical fields are preserved
        assert deserialized_job.metadata.label == original_job.metadata.label
        assert deserialized_job.metadata.surrogate_algo == original_job.metadata.surrogate_algo
        assert deserialized_job.training_configuration.max_iterations == original_job.training_configuration.max_iterations
        assert deserialized_job.model_configuration.parameters[0].name == original_job.model_configuration.parameters[0].name

    def test_deserialize_training_data_from_json(self, tmp_path):
        """
        Test JSON deserialization of VODataset.

        Validates that training data can be reliably deserialized
        from JSON format as expected by the entry script.
        """
        # Arrange: Create test dataset
        transformer, df_x, df_y = create_basic_datatransformer_timeseries()

        # Transform data to get arrays
        arr_x, arr_y = transformer.transform(df_x, df_y)

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer.uid,
            colnames_x=['feature1', 'feature2', 'feature3'],
            colnames_y=['target'],
            pattern="sequence"
        )

        data_json_path = tmp_path / "training_data.json"
        with open(data_json_path, 'w') as f:
            f.write(original_dataset.model_dump_json(indent=2))

        # Act: Test deserialization
        from backend.core._surrogate.entrypoints.seqmodel import deserialize_vodata
        deserialized_dataset = deserialize_vodata(str(data_json_path))

        # Assert: Verify data integrity
        assert deserialized_dataset.arr_x.shape == original_dataset.arr_x.shape
        assert deserialized_dataset.arr_y.shape == original_dataset.arr_y.shape
        assert deserialized_dataset.transformer_uid == original_dataset.transformer_uid
        assert deserialized_dataset.colnames_x == original_dataset.colnames_x
        assert deserialized_dataset.colnames_y == original_dataset.colnames_y
        assert deserialized_dataset.pattern == original_dataset.pattern
        np.testing.assert_array_almost_equal(deserialized_dataset.arr_x, original_dataset.arr_x)

    def test_entry_script_training_execution(self, tmp_path):
        """
        Test the complete training execution path of the entry script.

        Validates that the entry script can:
        1. Load job configs and training data from JSON
        2. Execute training without runtime failures
        3. Produce a trained model and updated job entity

        This is the core integration test for distributed training reliability.
        """
        # Arrange: Create test files
        job = create_basic_job()
        transformer, df_x, df_y = create_basic_datatransformer_timeseries()
        arr_x, arr_y = transformer.transform(df_x, df_y)

        training_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer.uid,
            colnames_x=['feature1', 'feature2', 'feature3'],
            colnames_y=['target'],
            pattern="sequence"
        )

        # Create input files
        job_config_path = tmp_path / "job_config.json"
        training_data_path = tmp_path / "training_data.json"
        output_dir = tmp_path / "outputs"
        output_dir.mkdir()

        with open(job_config_path, 'w') as f:
            f.write(job.model_dump_json(indent=2))
        with open(training_data_path, 'w') as f:
            f.write(training_dataset.model_dump_json(indent=2))


        # Change to output directory for model saving
        import os
        original_cwd = os.getcwd()
        try:
            os.chdir(output_dir)
            success = main(
                job_configs_path=str(job_config_path),
                training_data_path=str(training_data_path),
                test_data_path="",  # Empty string handled as None in the function
                output_dir=str(output_dir)
            )
        finally:
            os.chdir(original_cwd)

        # Assert: Verify training completed successfully
        assert success is True, "Training should complete successfully"

        # Verify output files were created
        model_file = output_dir / "model.pt"
        job_file = output_dir / "training_job_entity.json"

        assert model_file.exists(), "Model file should be created"
        assert job_file.exists(), "Job entity file should be created"
        assert model_file.stat().st_size > 0, "Model file should not be empty"
        assert job_file.stat().st_size > 0, "Job entity file should not be empty"

    def test_model_serialization_round_trip_consistency(self, tmp_path):
        """
        Test round-trip consistency of model serialization/deserialization.

        This validates the critical property that a model saved and loaded
        produces mathematically equivalent predictions. This is essential
        for distributed training where models must be transferred between
        compute environments.

        principle: "Data integrity is non-negotiable in distributed systems"
        """
        # Arrange: Train a model using the entry script
        job = create_basic_job()
        transformer, df_x, df_y = create_basic_datatransformer_timeseries()
        arr_x, arr_y = transformer.transform(df_x, df_y)

        training_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer.uid,
            colnames_x=['feature1', 'feature2', 'feature3'],
            colnames_y=['target'],
            pattern="sequence"
        )

        # Create input files and train model
        job_config_path = tmp_path / "job_config.json"
        training_data_path = tmp_path / "training_data.json"
        output_dir = tmp_path / "outputs"
        output_dir.mkdir()

        with open(job_config_path, 'w') as f:
            f.write(job.model_dump_json(indent=2))
        with open(training_data_path, 'w') as f:
            f.write(training_dataset.model_dump_json(indent=2))

        # Train model
        from backend.core._surrogate.entrypoints.seqmodel import main
        import os
        original_cwd = os.getcwd()
        try:
            os.chdir(output_dir)
            success = main(
                job_configs_path=str(job_config_path),
                training_data_path=str(training_data_path),
                test_data_path="",
                output_dir=str(output_dir)
            )
        finally:
            os.chdir(original_cwd)

        assert success, "Training must succeed for round-trip test"

        # Act: Load the saved model and compare with original
        model_file = output_dir / "model.pt"
        assert model_file.exists(), "Model file must exist"

        # Load the saved model
        import torch
        loaded_model = torch.load(model_file, map_location='cpu')

        # Assert: Verify model can be loaded and is in expected state
        assert loaded_model is not None, "Loaded model should not be None"
        assert hasattr(loaded_model, 'eval'), "Loaded model should be a PyTorch model"

        # Verify model is in evaluation mode and can process data
        loaded_model.eval()

        # Test with a small batch of the training data
        test_input = torch.tensor(arr_x[:2], dtype=torch.float32)  # First 2 timesets

        with torch.no_grad():
            try:
                output = loaded_model(test_input)
                assert output is not None, "Model should produce output"
                assert output.shape[0] == 2, "Output should match batch size"
                print(f"✅ Model round-trip test passed - output shape: {output.shape}")
            except Exception as e:
                pytest.fail(f"Model inference failed after round-trip: {e}")

        # Verify job entity was also saved correctly
        job_file = output_dir / "training_job_entity.json"
        with open(job_file, 'r') as f:
            job_data = json.load(f)

        assert 'uid' in job_data, "Job entity should contain UID"
        assert 'metadata' in job_data, "Job entity should contain metadata"
        print("✅ Entry script validation tests completed successfully")

    def test_seqmodel_script_end_to_end_execution(self, tmp_path):
        """
        Test the complete seqmodel.py script execution as a subprocess.

        This validates the actual command-line interface that distributed
        training systems (Azure ML, Kubernetes, etc.) will use in production.

        Tests the full contract:
        1. Command-line argument parsing
        2. File I/O from specified paths
        3. Script exit codes
        4. Output file creation in specified directories

        Jeff Dean principle: "Test the interface your production system will actually use"
        """
        # Arrange: Create test data and input files
        job = create_basic_job()
        transformer, df_x, df_y = create_basic_datatransformer_timeseries()
        arr_x, arr_y = transformer.transform(df_x, df_y)

        training_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer.uid,
            colnames_x=['feature1', 'feature2', 'feature3'],
            colnames_y=['target'],
            pattern="sequence"
        )

        # Create input files in specific locations
        job_config_path = tmp_path / "job_config.json"
        training_data_path = tmp_path / "training_data.json"
        output_dir = tmp_path / "outputs"
        output_dir.mkdir()

        with open(job_config_path, 'w') as f:
            f.write(job.model_dump_json(indent=2))
        with open(training_data_path, 'w') as f:
            f.write(training_dataset.model_dump_json(indent=2))

        # Act: Execute the script as a subprocess (real production usage)
        import subprocess
        import sys
        from pathlib import Path

        # Get the path to the seqmodel.py script
        script_path = Path(__file__).parent.parent.parent.parent.parent / "core" / "_surrogate" / "entrypoints" / "seqmodel.py"

        # Build command line arguments exactly as Azure ML/Kubernetes would
        cmd = [
            sys.executable,  # Python interpreter
            str(script_path),
            "--job_configs_path", str(job_config_path),
            "--training_data_path", str(training_data_path),
            "--output_dir", str(output_dir)
            # Note: --test_data_path is optional, omitting it
        ]

        print(f"🚀 Executing command: {' '.join(cmd)}")

        # Execute with timeout and capture output
        try:
            result = subprocess.run(
                cmd,
                cwd=str(output_dir),  # Set working directory for model.pt output
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
                check=False  # Don't raise on non-zero exit
            )

            print(f"📤 Script stdout: {result.stdout}")
            if result.stderr:
                print(f"📤 Script stderr: {result.stderr}")

        except subprocess.TimeoutExpired:
            pytest.fail("Script execution timed out after 5 minutes")
        except Exception as e:
            pytest.fail(f"Failed to execute script: {e}")

        # Assert: Verify script execution success
        assert result.returncode == 0, f"Script failed with exit code {result.returncode}. Stderr: {result.stderr}"
        assert "Training completed successfully!" in result.stdout, "Script should report successful completion"

        # Verify output files were created in the correct locations
        model_file = output_dir / "model.pt"
        job_file = output_dir / "training_job_entity.json"

        assert model_file.exists(), f"Model file should be created at {model_file}"
        assert job_file.exists(), f"Job entity file should be created at {job_file}"
        assert model_file.stat().st_size > 0, "Model file should not be empty"
        assert job_file.stat().st_size > 0, "Job entity file should not be empty"

        # Verify model can be loaded (final validation)
        import torch
        try:
            loaded_model = torch.load(model_file, map_location='cpu')
            assert loaded_model is not None, "Model should load successfully"
            print(f"✅ Model loaded successfully: {type(loaded_model)}")
        except Exception as e:
            pytest.fail(f"Failed to load saved model: {e}")

        # Verify job entity JSON is valid
        try:
            with open(job_file, 'r') as f:
                job_data = json.load(f)
            assert isinstance(job_data, dict), "Job entity should be a valid JSON object"
            assert 'uid' in job_data, "Job entity should contain UID"
            print(f"✅ Job entity loaded successfully with UID: {job_data.get('uid', 'unknown')}")
        except Exception as e:
            pytest.fail(f"Failed to load job entity JSON: {e}")

        print("✅ End-to-end script execution test completed successfully")
        print(f"📁 Output files created in: {output_dir}")
        print(f"📄 Model file size: {model_file.stat().st_size} bytes")
        print(f"📄 Job file size: {job_file.stat().st_size} bytes")

def create_basic_datatransformer_timeseries() :
    """
    Generate a data transformer for time series data with at least 70 timesets.
    
    This creates synthetic data with:
    - 70 unique timesets (different time series)
    - 10 timesteps per timeset
    - 3 feature columns (input variables)
    - 1 target column (output variable)
    
    Returns:
        SurrogateDataTransformer configured for time series data
    """
    # Generate time series data
    num_timesets = 70  # Number of unique time series
    timesteps_per_set = 10  # Number of timesteps in each series
    
    # Create lists to hold data
    timesets = []
    timesteps = []
    feature1 = []
    feature2 = []
    feature3 = []
    targets = []
    
    # Generate synthetic data
    for timeset_id in range(num_timesets):
        # Base values for this timeset with some randomness
        base_value1 = np.random.uniform(0, 10)
        base_value2 = np.random.uniform(0, 5)
        base_value3 = np.random.uniform(0, 2)
        
        for timestep in range(timesteps_per_set):
            # Add values with some time-based trends and noise
            timesets.append(timeset_id)
            timesteps.append(timestep)
            
            # Features with some time dependency and noise
            feat1 = base_value1 + timestep * 0.5 + np.random.normal(0, 0.2)
            feat2 = base_value2 + np.sin(timestep * 0.5) + np.random.normal(0, 0.1)
            feat3 = base_value3 + (0.1 * timestep**2) + np.random.normal(0, 0.05)
            
            feature1.append(feat1)
            feature2.append(feat2)
            feature3.append(feat3)
            
            # Target - some function of the features and time
            target = feat1 * 2 + feat2 - feat3 + timestep * 0.3 + np.random.normal(0, 0.3)
            targets.append(target)
    
    # Create dataframes
    df_x = pd.DataFrame({
        'timeset': timesets,
        'timestep': timesteps,
        'feature1': feature1,
        'feature2': feature2,
        'feature3': feature3
    })

    df_y = pd.DataFrame({
        'timeset': timesets,
        'timestep': timesteps,
        'target': targets
    })
    
    # Create transformer with standard scalers
    transformer = SurrogateDataTransformer(
        uid=uuid.uuid4(),
        df_x=df_x,
        df_y=df_y,
        x_scaler=sklearn.preprocessing.StandardScaler(),
        y_scaler=sklearn.preprocessing.StandardScaler(),
        timeset_col='timeset',
        timestep_col='timestep'
    )
    

    return transformer, df_x, df_y

def temp_export_csv():
    """
    Uses the create basic data transformer timeseries method to create a synthetic dataset and saves it as a csv with the title "test_csv.csv"
    
    Merges the x and y dataframes on the common timeset and timestep columns.
    Exports it as one csv
    """
    transformer, df_x, df_y = create_basic_datatransformer_timeseries()

    df_concat = pd.merge(df_x, df_y, on=['timeset', 'timestep'])
    df_concat.to_csv("test_csv.csv", index=False)




class TestSurrogateModel:
    def test_predict(self):
        """Test prediction functionality of the PyTorchSurrogateModel."""
        # Arrange
        # Create and prepare data
        datatransformer, df_x, df_y = create_basic_datatransformer_timeseries()
        
        # Split data by timeset for training and testing
        unique_timesets = df_x['timeset'].unique()
        train_timesets = unique_timesets[:int(len(unique_timesets) * 0.8)]  # 80% for training
        test_timesets = unique_timesets[int(len(unique_timesets) * 0.8):]   # 20% for testing
        
        # Filter dataframes by timeset
        df_x_train = df_x[df_x['timeset'].isin(train_timesets)]
        df_y_train = df_y[df_y['timeset'].isin(train_timesets)]
        df_x_test = df_x[df_x['timeset'].isin(test_timesets)]
        df_y_test = df_y[df_y['timeset'].isin(test_timesets)]
        
        # Transform data
        arr_x, arr_y = datatransformer.transform(df_x_train, df_y_train)
        
        # Create and train model
        original_job = create_basic_job()
        trainer = Seq2SeqTSTrainer()
        train_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=datatransformer.uid,
            colnames_x=datatransformer._initial_x_variable_cols,
            colnames_y=datatransformer._initial_y_variable_cols,
            pattern="sequence"
        )
        native_model = trainer._train_with_validation(
            train_set=train_dataset,
            val_set=train_dataset,  # Using same data for validation in test
            training_config=original_job.training_configuration,
            model_config=original_job.model_configuration,
            job=original_job
        )
        
        surrogate_model = PyTorchSurrogateModel(
            native_model,
            datatransformer
        )
        
        # Act
        predictions = surrogate_model.predict(df_x_test)
        
        # Assert
        assert predictions is not None, "Predictions should not be None"
        assert len(predictions) == len(df_x_test), "Should have same number of predictions as test samples"
        assert predictions.shape[1] == df_y_test.shape[1], "Should have same number of output columns as df_y (minus timeset/timestep)"
        
        # Verify predictions are reasonable values (not NaN)
        assert not predictions.isna().any().any(), "Predictions should not contain NaN values"
        
        # Verify predictions have been properly inverse-transformed (should be in original scale)
        # Only check the target column, not timeset/timestep columns which can be 0
        target_predictions = predictions['target'] if 'target' in predictions.columns else predictions.iloc[:, -1]
        assert target_predictions.min() >= df_y_train['target'].min() * 0.5, "Target predictions should be in reasonable range"
        assert target_predictions.max() <= df_y_train['target'].max() * 1.5, "Target predictions should be in reasonable range"
        
        print(f"Prediction test passed - generated {len(predictions)} predictions")
        return surrogate_model
    
    def test_serialize_deserialize(self):
        """Test serialization and deserialization of PyTorchSurrogateModel."""
        # Arrange
        # Create data and train model
        datatransformer, df_x, df_y = create_basic_datatransformer_timeseries()
        
        # Split data by timeset
        unique_timesets = df_x['timeset'].unique()
        train_timesets = unique_timesets[:int(len(unique_timesets) * 0.8)]
        test_timesets = unique_timesets[int(len(unique_timesets) * 0.8):]
        
        df_x_train = df_x[df_x['timeset'].isin(train_timesets)]
        df_y_train = df_y[df_y['timeset'].isin(train_timesets)]
        df_x_test = df_x[df_x['timeset'].isin(test_timesets)]
        
        arr_x, arr_y = datatransformer.transform(df_x_train, df_y_train)
        
        # Create model
        original_job = create_basic_job()
        trainer = Seq2SeqTSTrainer()
        train_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=datatransformer.uid,
            colnames_x=datatransformer._initial_x_variable_cols,
            colnames_y=datatransformer._initial_y_variable_cols,
            pattern="sequence"
        )
        native_model = trainer._train_with_validation(
            train_set=train_dataset,
            val_set=train_dataset,
            training_config=original_job.training_configuration,
            model_config=original_job.model_configuration,
            job=original_job
        )
        
        original_model = PyTorchSurrogateModel(
            native_model,
            datatransformer
        )
        
        # Get original predictions for comparison
        original_predictions = original_model.predict(df_x_test)
        
        # Act
        # Serialize model
        serialized_data = original_model.serialize_native_model()
        
        
        # Deserialize model
        deserialized_native_model = PyTorchSurrogateModel.deserialize_native_model(
            serialized_data, 
        )
        
        # Create new surrogate model with deserialized native model
        deserialized_model = PyTorchSurrogateModel(
            deserialized_native_model,
            datatransformer
        )
        
        # Make predictions with deserialized model
        deserialized_predictions = deserialized_model.predict(df_x_test)
        
        # Assert
        assert serialized_data is not None, "Serialized data should not be None"
        assert isinstance(serialized_data, str), "Serialized data should be a string"
        
        # Verify deserialized model produces identical predictions
        assert deserialized_predictions.shape == original_predictions.shape, "Prediction shapes should match"
        assert np.allclose(
            deserialized_predictions.values, 
            original_predictions.values, 
            rtol=1e-5, atol=1e-5
        ), "Predictions from deserialized model should match original model"
        
        print("Serialization and deserialization test passed")



if __name__ == "__main__":
    """
    Manual test runner for validating the entry script functionality.
    Run this to test the seqmodel.py entry script locally.
    """
    import tempfile
    import pathlib

    print("🧪 Testing seqmodel.py entry script validation...")

    # Create temporary directory for testing
    with tempfile.TemporaryDirectory() as tmp_dir:
        tmp_path = pathlib.Path(tmp_dir)

        # Initialize test class
        test_class = TestSeqModelEntryScript()

        try:
            print("1️⃣ Testing job deserialization...")
            test_class.test_deserialize_job_from_json(tmp_path)
            print("✅ Job deserialization test passed")

            print("2️⃣ Testing training data deserialization...")
            test_class.test_deserialize_training_data_from_json(tmp_path)
            print("✅ Training data deserialization test passed")

            print("3️⃣ Testing complete training execution...")
            test_class.test_entry_script_training_execution(tmp_path)
            print("✅ Training execution test passed")

            print("4️⃣ Testing model serialization round-trip...")
            test_class.test_model_serialization_round_trip_consistency(tmp_path)
            print("✅ Round-trip consistency test passed")

            print("5️⃣ Testing complete script end-to-end execution...")
            test_class.test_seqmodel_script_end_to_end_execution(tmp_path)
            print("✅ End-to-end script execution test passed")

            print("\n🎉 All entry script validation tests passed!")
            print("The seqmodel.py entry script is ready for distributed training.")
            print("✨ Both function-level and command-line interfaces validated!")

        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            raise
    