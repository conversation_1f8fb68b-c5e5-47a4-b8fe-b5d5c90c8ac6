"""
# Test Specification for ISurrogate Training Functionality

## Overview
This file implements unit tests for the surrogate model training functionality, focusing on the training 
job process from the ISurrogate interface through to the underlying trainer components. These tests 
assume the RandomForest trainer and model wrapper classes are implemented.

The tests follow a TDD approach, creating tests for functionality before implementation to ensure
proper design and complete coverage.

## Test Categories

### 1. Basic Training Job Execution
- Test creation and execution of a basic surrogate model training job
- Verify proper flow through the training process
- Test that model and job results are correctly constructed and returned
- Validate that training configuration options are properly applied

Specific test cases:
- Standard RandomForest training on regression data
- Training with various hyperparameter settings
- Training with specified random seed for reproducibility

### 2. Training Metrics and Logging
- Test that training metrics are properly captured and logged
- Verify that epoch metrics show expected improvement during training
- Test that feature importance is correctly calculated and included in results
- Validate that training history is complete and correctly structured

Specific test cases:
- Metrics validation during and after training
- Feature importance correctness and consistency
- Training history structure and completeness

### 3. Error Handling and Edge Cases
- Test graceful handling of invalid training configurations
- Verify proper error handling for failed training attempts
- Test behavior with edge case data (very small datasets, extreme values)
- Validate handling of early stopping criteria

Specific test cases:
- Failed training due to invalid parameters
- Training with minimal viable dataset
- Early stopping based on convergence criteria
- Training with missing or incomplete configuration

### 4. Persistence and Retrieval
- Test saving of trained models and their metadata
- Verify retrieval of previously saved models
- Test that training configuration is properly persisted
- Validate model registry functionality

Specific test cases:
- Save and load model verification
- Training configuration persistence
- Model registry interaction

## Helper Functions

- create_regression_dataset(): Creates synthetic regression data for testing
- create_training_config(): Creates standard VOTrainingConfiguration for testing
- create_model_config(): Creates VOModelConfiguration for RandomForest model
- verify_training_result(): Validates a training job result against expected values
- verify_training_history(): Validates that training history shows expected improvement
"""

import unittest
from unittest.mock import patch
import uuid
import numpy as np
import pandas as pd
import time
import random
from typing import Dict, List, Any, Tuple, Optional, Union
import logging

# Import from project modules
import backend.core._surrogate as su
from backend.core._surrogate._enums import EnumSurrogateAlgorithm, EnumTrainingStatus
from backend.core.interfaces.surrogate_interfaces import (
    ISurrogate, 
    SurrogateTrainingError, 
    SurrogateConfigError, 
    SurrogateRegistryPort
)
from backend.core._surrogate.valueobjects import (
    VODataset, 
    VOConfig_Training, 
    VOConfig_Model,
    VOLog_ModelMetrics,
    VOConfig_Parameter,
    VOConfig_ParamBounds,
    VOMetadata_General
)
from backend.core._surrogate.entities import ENTTrainingJob
from backend.core._surrogate.models.model_sklearn import SKLearnModel
from backend.core._surrogate.trainers.trainer_rf import RandomForestTrainer

################################
# Helper Functions

def create_regression_dataset(
    n_samples: int = 100, 
    n_features: int = 5, 
    n_targets: int = 1,
    noise: float = 0.1,
    random_seed: int = 42
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Create synthetic regression dataset for testing.
    
    Args:
        n_samples: Number of samples
        n_features: Number of features
        n_targets: Number of target variables
        noise: Noise level in the data
        random_seed: Random seed for reproducibility
        
    Returns:
        Tuple of (X_df, y_df) as pandas DataFrames
    """
    np.random.seed(random_seed)
    
    # Generate features
    X = np.random.randn(n_samples, n_features)
    
    # Generate coefficients
    coef = np.random.randn(n_features, n_targets)
    
    # Generate targets with noise
    y = X @ coef + noise * np.random.randn(n_samples, n_targets)
    
    # Create DataFrames
    X_df = pd.DataFrame(X, columns=[f"feature_{i}" for i in range(n_features)])
    y_df = pd.DataFrame(y, columns=[f"target_{i}" for i in range(n_targets)])
    
    return X_df, y_df

def create_training_samples(
    n_samples: int = 100, 
    n_features: int = 5, 
    n_targets: int = 1, 
    random_seed: int = 42
) -> su.VODataset:
    """
    Create VOSamples object with synthetic regression data for testing.
    
    Returns:
        VOSamples with synthetic data
    """
    X_df, y_df = create_regression_dataset(
        n_samples=n_samples, 
        n_features=n_features, 
        n_targets=n_targets,
        random_seed=random_seed
    )
    
    return su.VODataset(
        df_x=X_df,
        df_y=y_df,
        uid=uuid.uuid4()
    )

def create_training_config(quick_mode: bool = True) -> su.VOConfig_Training:
    """
    Create standard training configuration for testing.
    
    Args:
        quick_mode: If True, use faster but less accurate settings
        
    Returns:
        VOTrainingConfiguration for testing
    """
    return su.VOConfig_Training(
        primary_metric=su.EnumMetricName.RMSE,  # Updated to use enum
        max_iterations=10 if quick_mode else 50,
        random_seed=42,
        validation_strategy="simple_split",
        validation_split=0.2,
        enable_early_stopping=True,
        early_stopping_rounds=3 if quick_mode else 10,
        early_stopping_min_delta=0.001
    )

def create_model_config() -> su.VOConfig_Model:
    """
    Create standard model configuration for RandomForest.
    
    Returns:
        VOModelConfiguration for RandomForest
    """
    return su.VOConfig_Model(
        algorithm=su.EnumSurrogateAlgorithm.RANDOM_FOREST,
        parameters=[
            su.VOConfig_Parameter(
                name=su.Hyperparams.RF.N_ESTIMATORS, 
                value=10,
                tunable=True,
                bounds=su.VOConfig_ParamBounds.integer(10, 200)
            ),
            su.VOConfig_Parameter(
                name=su.Hyperparams.RF.MAX_DEPTH, 
                value=5,
                tunable=True,
                bounds=su.VOConfig_ParamBounds.integer(3, 20)
            ),
            su.VOConfig_Parameter(
                name=su.Hyperparams.RF.MIN_SAMPLES_LEAF, 
                value=2,
                tunable=True,
                bounds=su.VOConfig_ParamBounds.integer(1, 10)
            ),
            su.VOConfig_Parameter(
                name=su.Hyperparams.RF.MIN_SAMPLES_SPLIT, 
                value=2,
                tunable=True,
                bounds=su.VOConfig_ParamBounds.integer(2, 10)  # Integer type instead of continuous
            )
        ]
    )

def create_metadata() -> su.VOMetadata_General:
    """
    Create metadata for testing.
    
    Returns:
        VOMetadata for testing
    """
    return su.VOMetadata_General(
        label="Test Surrogate Model",
        user_reference="test_user",
        atlas_reference="test_atlas",
        surrogate_algo=su.EnumSurrogateAlgorithm.RANDOM_FOREST,
        description="Test surrogate model for unit testing"
    )

class StubExperimentRegistry(BaseAdaptor_SurrogateExperimentRegistry):
    """Stub implementation of experiment registry for testing"""
    
    def __init__(self):
        self.experiments = {}
        
    def save_experiment(
        self,
        metadata=None,
        training_config=None,
        training_results=None
    ):
        """Stub implementation that just stores the experiment data"""
        if metadata and metadata.uid:
            self.experiments[metadata.uid] = {
                "metadata": metadata,
                "training_config": training_config,
                "training_results": training_results
            }
        return True
    
    def get_experiment(
        self,
        uid=None,
        label=None,
        user_reference=None
    ):
        """Stub implementation that returns stored experiment data"""
        if uid and uid in self.experiments:
            return self.experiments[uid]
        return None

class StubModelRegistry(SurrogateRegistryPort):
    """Stub implementation of model registry for testing"""
    
    def __init__(self):
        self.models = {}
        
    def save_model(
        self,
        model=None,
        metadata=None
    ):
        """Stub implementation that just stores the model"""
        if metadata and metadata.uid:
            model_id = str(metadata.uid)
            self.models[model_id] = {
                "model": model,
                "metadata": metadata
            }
            return model_id
        return None
    
    def get_model(
        self,
        model_id=None
    ):
        """Stub implementation that returns stored model"""
        if model_id and model_id in self.models:
            return self.models[model_id].get("model")
        return None

def create_stub_repository() -> Tuple[StubExperimentRegistry, StubModelRegistry]:
    """
    Create stub repositories for testing.
    
    Returns:
        Tuple of (experiment_registry, model_registry) stubs
    """
    experiment_registry = StubExperimentRegistry()
    model_registry = StubModelRegistry()
    return experiment_registry, model_registry

def verify_training_result(
    job: su.ENTTrainingJob,
    model: su.models.BaseSurrogateModel,
    expected_status: str = su.EnumTrainingStatus.COMPLETE.value
) -> bool:
    """
    Validate training job result and model.
    
    Args:
        job: Training job to validate
        model: Trained model to validate
        expected_status: Expected job status
        
    Returns:
        True if validation passes, raises AssertionError otherwise
    """
    # Check job status
    assert job.status == expected_status, f"Expected status {expected_status}, got {job.status}"
    
    # Check job has environment info
    assert job.environment_info, "Job should have environment info"
    
    # Check job has results
    assert job.results is not None, "Job should have results"
    assert job.results.test_metrics is not None, "Job should have test metrics"
    assert job.results.feature_importance, "Job should have feature importance"
    
    # Check job has runtime info
    assert job.runtime_seconds > 0, "Job should have positive runtime"
    
    # Check model
    assert model is not None, "Model should not be None"
    assert isinstance(model, su.models.BaseSurrogateModel), "Model should be a BaseSurrogateModel"
    # Check for model_type property but don't assume specific implementation class
    assert model.model_type == "RandomForest", f"Expected RandomForest model type, got {model.model_type}"
    assert model.feature_names_in_ is not None, "Model should have feature names"
    assert model.get_feature_importance() is not None, "Model should provide feature importance"
    
    return True

def verify_training_history(job: su.ENTTrainingJob) -> bool:
    """
    Validate that training history shows expected structure.
    
    Args:
        job: Training job with history to validate
        
    Returns:
        True if validation passes, raises AssertionError otherwise
    """
    # Check epoch metrics exist
    assert job.epoch_metrics, "Job should have epoch metrics"
    
    # Check epoch metrics have expected structure
    for epoch_metric in job.epoch_metrics:
        assert epoch_metric.epoch >= 0, "Epoch number should be non-negative"
        assert epoch_metric.train_metrics is not None, "Epoch should have training metrics"
        assert epoch_metric.validation_metrics is not None, "Epoch should have validation metrics"
        
        # Check metrics have expected properties
        for metrics in [epoch_metric.train_metrics, epoch_metric.validation_metrics]:
            assert metrics.rmse is not None, "Metrics should include RMSE"
            assert metrics.mae is not None, "Metrics should include MAE"
            assert metrics.r2 is not None, "Metrics should include R2"
    
    return True

def verify_metrics_improvement(
    job: su.ENTTrainingJob, 
    metric_name: Union[str, su.EnumMetricName] = su.EnumMetricName.RMSE
) -> bool:
    """
    Verify that metrics show improvement during training.
    
    Args:
        job: Training job with history to validate
        metric_name: Name of metric to check for improvement
        
    Returns:
        True if validation passes, raises AssertionError otherwise
    """
    if len(job.epoch_metrics) <= 1:
        # Not enough data points to verify improvement
        return True
    
    # Get validation metrics across epochs
    val_metrics = []
    
    # Get metric key for attribute access
    metric_key = metric_name.value if isinstance(metric_name, su.EnumMetricName) else metric_name
    
    for epoch_metric in job.epoch_metrics:
        if epoch_metric.validation_metrics is not None:
            val_metrics.append(getattr(epoch_metric.validation_metrics, metric_key))
    
    if len(val_metrics) <= 1:
        # Not enough validation metrics to verify improvement
        return True
    
    # Check if best metric is better than first metric
    first_metric = val_metrics[0]
    
    # Determine if higher values are better using EnumMetricName if available
    if isinstance(metric_name, su.EnumMetricName):
        is_higher_better = su.EnumMetricName.is_higher_better(metric_name)
    else:
        # Fallback for string metrics
        is_higher_better = metric_key == "r2" or metric_key == "directional_accuracy"
        
    if is_higher_better:
        best_metric = max(val_metrics)
        assert best_metric >= first_metric, f"Metric {metric_name} should improve (increase) during training"
    else:
        best_metric = min(val_metrics)
        assert best_metric <= first_metric, f"Metric {metric_name} should improve (decrease) during training"
    
    return True

class TestBasicTrainingJobExecution:
    """Tests for basic training job execution functionality."""
    
    def test_standard_random_forest_training(self):
        """Test creation and execution of a basic RandomForest surrogate model training job."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        # Assert
        assert job.status == su.EnumTrainingStatus.COMPLETE.value, f"Expected COMPLETE status, got {job.status}"
        assert model is not None, "Model should not be None"
        assert isinstance(model, SKLearnModel), f"Expected SKLearnModel, got {type(model).__name__}"
        
        # Verify job and model details
        verify_training_result(job, model)
        verify_training_history(job)
        
        # Test model can make predictions
        sample_data = training_data.df_x.head(3)
        predictions = model.predict(sample_data)
        assert predictions.shape[0] == 3, "Should predict for all 3 samples"
        assert predictions.shape[1] == training_data.df_y.shape[1], "Prediction dimensions should match target dimensions"
        
        print("Basic RandomForest training test passed")
    
    def test_training_with_hyperparameters(self):
        """Test training with specific hyperparameter settings."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        training_config = create_training_config(quick_mode=True)
        
        # Create model config with specific hyperparameters
        model_config = su.VOConfig_Model(
            algorithm=su.EnumSurrogateAlgorithm.RANDOM_FOREST,
            parameters=[
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.N_ESTIMATORS, 
                    value=20,
                    tunable=False
                ),
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.MAX_DEPTH, 
                    value=10,
                    tunable=False
                ),
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.MIN_SAMPLES_LEAF, 
                    value=4,
                    tunable=False
                ),
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.BOOTSTRAP, 
                    value=False,
                    tunable=False
                )
            ]
        )
        metadata = create_metadata()
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        # Assert
        assert job.status == su.EnumTrainingStatus.COMPLETE.value, f"Expected COMPLETE status, got {job.status}"
        
        # Verify model is created with expected hyperparameters
        rf_model = model.native_model
        assert rf_model.n_estimators == 20, f"Expected n_estimators=20, got {rf_model.n_estimators}"
        assert rf_model.max_depth == 10, f"Expected max_depth=10, got {rf_model.max_depth}"
        assert rf_model.min_samples_leaf == 4, f"Expected min_samples_leaf=4, got {rf_model.min_samples_leaf}"
        assert rf_model.bootstrap is False, f"Expected bootstrap=False, got {rf_model.bootstrap}"
        
        print("Training with hyperparameters test passed")
    
    def test_reproducibility_with_fixed_seed(self):
        """Test that training with the same seed produces reproducible results."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        # Create identical training data
        training_data = create_training_samples(n_samples=150, n_features=3, random_seed=42)
        
        # Create identical training config with fixed seed
        training_config = create_training_config(quick_mode=True)
        training_config = su.VOConfig_Training(**{
            **training_config.model_dump(),
            "random_seed": 42  # Ensure fixed seed
        })
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Act - Run two identical training jobs
        model1, job1 = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        model2, job2 = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        # Assert - Results should be identical
        metrics1 = job1.results.test_metrics
        metrics2 = job2.results.test_metrics
        
        # Use almost equal for floating point comparison (with small epsilon)
        epsilon = 1e-10  # Allow for tiny floating point differences
        assert abs(metrics1.rmse - metrics2.rmse) < epsilon, f"RMSE should be nearly identical, got {metrics1.rmse} vs {metrics2.rmse}"
        assert abs(metrics1.r2 - metrics2.r2) < epsilon, f"R² should be nearly identical, got {metrics1.r2} vs {metrics2.r2}"
        
        # Compare feature importance (keys may be in different order)
        importance1 = model1.get_feature_importance()
        importance2 = model2.get_feature_importance()
        
        for feature, value in importance1.items():
            assert feature in importance2, f"Feature {feature} missing from second model"
            assert abs(importance2[feature] - value) < epsilon, f"Feature importance for {feature} not identical"
        
        # Test predictions are identical
        sample_data = training_data.df_x.head(5)
        pred1 = model1.predict(sample_data)
        pred2 = model2.predict(sample_data)
        assert np.allclose(pred1, pred2), "Predictions should be identical with same seed"
        
        print("Reproducibility with fixed seed test passed")


class TestTrainingMetricsAndLogging:
    """Tests for training metrics and logging functionality."""
    
    def test_metrics_validation_during_training(self):
        """Test that metrics are properly captured and validated during training."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        training_config = create_training_config(quick_mode=False)  # Use more iterations for better metrics
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        # Assert
        # Check that jobs have epoch metrics
        assert len(job.epoch_metrics) > 0, "Job should have epoch metrics"
        
        # Check each epoch has valid metrics
        for idx, epoch_metric in enumerate(job.epoch_metrics):
            # Check training metrics
            train_metrics = epoch_metric.train_metrics
            assert train_metrics.rmse is not None, f"Epoch {idx} missing RMSE training metric"
            assert train_metrics.r2 is not None, f"Epoch {idx} missing R² training metric"
            assert train_metrics.r2 <= 1.0, f"Epoch {idx} R² should be <= 1, got {train_metrics.r2}"
            
            # Check validation metrics
            val_metrics = epoch_metric.validation_metrics
            assert val_metrics is not None, f"Epoch {idx} missing validation metrics"
            assert val_metrics.rmse is not None, f"Epoch {idx} missing RMSE validation metric"
            assert val_metrics.r2 is not None, f"Epoch {idx} missing R² validation metric"
            assert val_metrics.r2 <= 1.0, f"Epoch {idx} R² should be <= 1, got {val_metrics.r2}"
        
        # Check final result metrics match last epoch validation metrics
        final_metrics = job.results.test_metrics
        last_val_metrics = job.epoch_metrics[-1].validation_metrics
        
        assert np.isclose(final_metrics.rmse, last_val_metrics.rmse, rtol=1e-5), "Final RMSE should approximately match last validation RMSE"
        assert np.isclose(final_metrics.r2, last_val_metrics.r2, rtol=1e-5), "Final R² should approximately match last validation R²"
        
        print("Metrics validation during training test passed")
    
    def test_feature_importance_calculation(self):
        """Test that feature importance is correctly calculated and included in results."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        # Create test data with known important features
        n_features = 5
        X_df, y_df = create_regression_dataset(
            n_samples=300, 
            n_features=n_features, 
            n_targets=1,
            random_seed=42
        )
        
        # Make first feature more important by scaling it
        X_df.iloc[:, 0] = X_df.iloc[:, 0] * 5
        
        training_data = su.VODataset(
            df_x=X_df,
            df_y=y_df,
            uid=uuid.uuid4()
        )
        
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        # Assert
        # Check feature importance in model
        feature_importance = model.get_feature_importance()
        assert len(feature_importance) == n_features, f"Expected {n_features} features, got {len(feature_importance)}"
        
        # Check feature importance in job results
        result_feature_importance = job.results.feature_importance
        assert len(result_feature_importance) == n_features, f"Job results missing feature importance"
        
        # Feature importance should sum to approximately 1.0
        assert abs(sum(feature_importance.values()) - 1.0) < 1e-5, "Feature importance should sum to approximately 1.0"
        
        # First feature should have significant importance since it was scaled
        first_feature_name = X_df.columns[0]
        other_features = [name for name in X_df.columns if name != first_feature_name]
        
        assert feature_importance[first_feature_name] > 0.1, f"Scaled feature should have significant importance (>0.1), got {feature_importance[first_feature_name]}"
        
        # The scaled feature should be in the top half of important features
        sorted_importances = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        feature_ranks = {name: i for i, (name, _) in enumerate(sorted_importances)}
        assert feature_ranks[first_feature_name] < len(feature_importance) / 2, \
            f"Scaled feature should be in top half of important features, got rank {feature_ranks[first_feature_name]+1}/{len(feature_importance)}"
        
        # No feature should unreasonably dominate (>80% importance)
        assert max(feature_importance.values()) < 0.8, f"No feature should dominate with >80% importance, got {max(feature_importance.values())}"
        
        print("Feature importance calculation test passed")
    
    def test_training_history_structure(self):
        """Test that training history is complete and correctly structured."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        
        # Configure for more epochs to test history
        training_config = su.VOConfig_Training(
            primary_metric="rmse",
            max_iterations=15,  # More iterations for better history
            random_seed=42,
            validation_strategy="simple_split",
            validation_split=0.2,
            enable_early_stopping=False  # Disable early stopping to ensure all epochs run
        )
        
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        # Assert
        # Verify training history has expected structure
        verify_training_history(job)
        
        # For RandomForest, we may only have one epoch as it's a one-shot algorithm
        # But let's make sure the metrics and epoch structure is correct
        assert len(job.epoch_metrics) >= 1, "Should have at least one epoch"
        
        if len(job.epoch_metrics) > 1:
            # Check epoch numbering is sequential
            for i, epoch_metric in enumerate(job.epoch_metrics):
                assert epoch_metric.epoch == i, f"Expected epoch {i}, got {epoch_metric.epoch}"
        
        # Check metrics retrieval methods
        rmse_history = job.get_metric_history("rmse", "validation")
        assert len(rmse_history) == len(job.epoch_metrics), "Metrics history length should match epochs"
        
        # Test learning curves
        curves = job.get_learning_curves("rmse")
        assert "train" in curves, "Learning curves should include training data"
        assert "validation" in curves, "Learning curves should include validation data"
        
        print("Training history structure test passed")


class TestErrorHandlingAndEdgeCases:
    """Tests for error handling and edge cases."""
    
    def test_invalid_algorithm_handling(self):
        """Test handling of invalid algorithm selection."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=100, n_features=3)
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        
        # Create valid metadata but mock the _get_trainer_class method to simulate an unsupported algorithm
        metadata = create_metadata()
        
        # Act & Assert
        with patch('backend.core.interfaces.surrogate_interfaces.ISurrogate._get_trainer_class') as mock_get_trainer:
            # Make the _get_trainer_class method raise a specific error for testing
            mock_get_trainer.side_effect = SurrogateConfigError("Unsupported algorithm")
            
            try:
                model, job = surrogate.execute_training_job(
                    metadata=metadata,
                    training_data=training_data,
                    training_config=training_config,
                    model_config=model_config,
                )
                assert False, "Should have raised SurrogateConfigError"
            except SurrogateConfigError:
                # Expected error
                pass
            except Exception as e:
                assert False, f"Expected SurrogateConfigError, got {type(e).__name__}: {str(e)}"
        
        print("Invalid algorithm handling test passed")
    
    def test_minimal_dataset_training(self):
        """Test training with minimal viable dataset."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        # Create minimal dataset with just enough samples
        training_data = create_training_samples(n_samples=10, n_features=2)
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        # Assert
        assert job.status == su.EnumTrainingStatus.COMPLETE.value, f"Expected COMPLETE status, got {job.status}"
        assert model is not None, "Model should not be None"
        
        # Try to predict on new data
        new_data = pd.DataFrame({
            col: [0.5] for col in training_data.df_x.columns
        })
        
        predictions = model.predict(new_data)
        assert predictions is not None, "Model should be able to make predictions"
        assert predictions.shape[0] == new_data.shape[0], "Predictions should match input row count"
        
        print("Minimal dataset training test passed")
    
    def test_mismatched_config_handling(self):
        """Test handling of mismatched configurations."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=100, n_features=3)
        training_config = create_training_config(quick_mode=True)
        
        # Create model config for RandomForest but with metadata for another algorithm
        model_config = create_model_config()  # RandomForest config
        metadata = su.VOMetadata_General(
            label="Mismatched Config Test",
            user_reference="test_user",
            atlas_reference="test_atlas",
            surrogate_algo=su.EnumSurrogateAlgorithm.GRADIENT_BOOSTING,  # Mismatch
            description="Test with mismatched configuration"
        )
        
        # Act & Assert
        with patch('backend.core.interfaces.surrogate_interfaces.ISurrogate._get_trainer_class') as mock_get_trainer:
            # Make the _get_trainer_class method raise a specific error for testing
            mock_get_trainer.side_effect = SurrogateConfigError("Algorithm mismatch")
            
            try:
                model, job = surrogate.execute_training_job(
                    metadata=metadata,
                    training_data=training_data,
                    training_config=training_config,
                    model_config=model_config,
                )
                assert False, "Should have raised SurrogateConfigError"
            except SurrogateConfigError:
                # Expected error
                pass
            except Exception as e:
                assert False, f"Expected SurrogateConfigError, got {type(e).__name__}: {str(e)}"
        
        print("Mismatched config handling test passed")


class TestMultiEpochTraining:
    """Tests for multi-epoch training behavior."""
    
    def test_metrics_improve_over_epochs(self):
        """Test that metrics show improvement over multiple training epochs."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=300, n_features=5)
        
        # Configure for multi-epoch training with no early stopping
        training_config = su.VOConfig_Training(
            primary_metric=su.EnumMetricName.RMSE,
            max_iterations=20,
            random_seed=42,
            validation_strategy="simple_split",
            validation_split=0.2,
            enable_early_stopping=False  # Disable early stopping to ensure all epochs run
        )
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        # Assert
        # Check if we have epoch metrics at all
        assert len(job.epoch_metrics) >= 1, "Should have at least one epoch of metrics"
        
        # If this is a one-shot algorithm like RandomForest, we'll only have one epoch
        # In this case, we'll just verify the metrics are valid
        if len(job.epoch_metrics) == 1:
            print("One-shot algorithm detected, verifying metrics quality")
            metrics = job.epoch_metrics[0].validation_metrics
            assert metrics.rmse is not None, "RMSE should be calculated"
            assert metrics.r2 is not None, "R² should be calculated"
            assert metrics.r2 <= 1.0, "R² should be <= 1"
            assert metrics.r2 > 0.5, "R² should be reasonable (> 0.5) for this synthetic dataset"
            
            # Test feature importance
            assert model.get_feature_importance() is not None, "Feature importance should be available"
            assert len(model.get_feature_importance()) == training_data.df_x.shape[1], "Should have importance for each feature"
            
            print("Metrics improve over epochs test passed (one-shot algorithm)")
            return
            
        # For multi-epoch algorithms, test RMSE improves (decreases) over epochs
        rmse_values = [epoch_metric.validation_metrics.rmse for epoch_metric in job.epoch_metrics]
        assert rmse_values is not None
        # Filter out None values before calculating min
        filtered_rmse_values = [value for value in rmse_values if value is not None]
        assert filtered_rmse_values, "Should have at least one valid RMSE value"
        best_rmse = min(filtered_rmse_values)
        final_rmse = rmse_values[-1]
        first_rmse = rmse_values[0]
        
        assert best_rmse < first_rmse, f"Best RMSE {best_rmse} should be better than first {first_rmse}"
        assert final_rmse <= first_rmse * 1.05, f"Final RMSE {final_rmse} should not be significantly worse than first {first_rmse}"
        
        # Similarly test R2 improves (increases)
        r2_values = [epoch_metric.validation_metrics.r2 for epoch_metric in job.epoch_metrics]
        assert r2_values is not None
        best_r2 = max(r2_values)
        first_r2 = r2_values[0]
        
        assert best_r2 > first_r2, f"Best R2 {best_r2} should be better than first {first_r2}"
        
        print("Metrics improve over epochs test passed")
    
    def test_epoch_numbering_consistency(self):
        """Test that epoch numbering is sequential and consistent."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        training_config = su.VOConfig_Training(
            primary_metric=su.EnumMetricName.RMSE,
            max_iterations=15,
            random_seed=42,
            validation_strategy="simple_split",
            validation_split=0.2,
            enable_early_stopping=False
        )
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
        )
        
        # Assert
        epoch_numbers = [m.epoch for m in job.epoch_metrics]
        
        # Check sequential numbering
        assert sorted(epoch_numbers) == epoch_numbers, "Epochs should be in sequential order"
        
        # Check for gaps
        if len(epoch_numbers) > 1:
            for i in range(1, len(epoch_numbers)):
                assert epoch_numbers[i] - epoch_numbers[i-1] == 1, f"Epoch numbering should have no gaps: {epoch_numbers}"
                
        # First epoch should be 0 or 1 (depending on implementation)
        assert epoch_numbers[0] in (0, 1), f"First epoch number should be 0 or 1, got {epoch_numbers[0]}"
        
        print("Epoch numbering consistency test passed")


class TestHyperparameterOptimization:
    """Tests for hyperparameter optimization functionality."""
    
    def test_basic_hpo_execution(self):
        """Test basic execution of hyperparameter optimization."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Create HPO config with minimal trials for quick testing
        hpo_config = su.VOConfig_HPO(
            is_enable=True,
            n_trials=5,
            sampler="tpe",
            pruner="median",
            n_parallel_jobs=1,
            timeout_seconds=60
        )
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Assert
        assert job.status == su.EnumTrainingStatus.COMPLETE.value, f"Expected COMPLETE status, got {job.status}"
        assert model is not None, "Model should not be None"
        
        # Verify HPO results
        assert job.hpo_results is not None, "Job should have HPO results"
        assert job.hpo_results.best_params is not None, "HPO results should have best params"
        assert job.hpo_results.best_value is not None, "HPO results should have best value"
        assert job.hpo_results.trials is not None, "HPO results should have trials"
        assert len(job.hpo_results.trials) == 5, f"Expected 5 trials, got {len(job.hpo_results.trials)}"
        
        # Verify model performance
        predictions = model.predict(training_data.df_x.head(5))
        assert predictions is not None, "Model should make predictions"
        
        print("Basic HPO execution test passed")
    
    def test_different_hpo_samplers(self):
        """Test HPO with different samplers."""
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Test both TPE and Random samplers
        for sampler in ["tpe", "random"]:
            hpo_config = su.VOConfig_HPO(
                n_trials=3,  # Minimal trials for quick testing
                sampler=sampler,
                pruner="median",
                n_parallel_jobs=1
            )
            
            model, job = surrogate.execute_training_job(
                metadata=metadata,
                training_data=training_data,
                training_config=training_config,
                model_config=model_config,
                hpo_config=hpo_config
            )
            
            assert job.status == su.EnumTrainingStatus.COMPLETE.value, f"HPO with {sampler} sampler failed"
            assert job.hpo_results is not None, f"Missing HPO results with {sampler} sampler"
            assert len(job.hpo_results.trials) == 3, f"Expected 3 trials with {sampler} sampler"
        
        print("HPO with different samplers test passed")
    
    def test_different_hpo_pruners(self):
        """Test HPO with different pruners."""
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Test different pruners
        for pruner in ["median", "hyperband", "none"]:
            hpo_config = su.VOConfig_HPO(
                n_trials=3,  # Minimal trials for quick testing
                sampler="tpe",
                pruner=pruner,
                n_parallel_jobs=1
            )
            
            model, job = surrogate.execute_training_job(
                metadata=metadata,
                training_data=training_data,
                training_config=training_config,
                model_config=model_config,
                hpo_config=hpo_config
            )
            
            assert job.status == su.EnumTrainingStatus.COMPLETE.value, f"HPO with {pruner} pruner failed"
            assert job.hpo_results is not None, f"Missing HPO results with {pruner} pruner"
        
        print("HPO with different pruners test passed")
    
    def test_optimization_directions(self):
        """Test HPO with different optimization directions."""
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Test both directions using different metrics
        direction_configs = [
            # Minimize RMSE
            (su.VOConfig_Training(
                primary_metric=su.EnumMetricName.RMSE,
                max_iterations=5,
                random_seed=42,
                validation_strategy="simple_split",
                validation_split=0.2
            ), "minimize"),
            
            # Maximize R2
            (su.VOConfig_Training(
                primary_metric=su.EnumMetricName.R2,
                max_iterations=5,
                random_seed=42,
                validation_strategy="simple_split",
                validation_split=0.2
            ), "maximize")
        ]
        
        for training_config, expected_direction in direction_configs:
            hpo_config = su.VOConfig_HPO(
                n_trials=3,
                sampler="tpe",
                pruner="median"
            )
            
            model, job = surrogate.execute_training_job(
                metadata=metadata,
                training_data=training_data,
                training_config=training_config,
                model_config=model_config,
                hpo_config=hpo_config
            )
            
            assert job.hpo_results is not None, "HPO results should not be None"
            assert job.hpo_results.direction == expected_direction, \
                f"Expected {expected_direction} direction for {training_config.primary_metric}, " \
                f"got {job.hpo_results.direction}"
        
        print("Optimization directions test passed")
    
    def test_parameter_tuning_ranges(self):
        """Test HPO with different parameter types and ranges."""
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        training_config = create_training_config(quick_mode=True)
        metadata = create_metadata()
        
        # Create model config with various parameter types for tuning
        model_config = su.VOConfig_Model(
            algorithm=su.EnumSurrogateAlgorithm.RANDOM_FOREST,
            parameters=[
                # Integer parameter
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.N_ESTIMATORS, 
                    value=10,
                    tunable=True,
                    bounds=su.VOConfig_ParamBounds.integer(5, 15)
                ),
                # Continuous parameter
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.MIN_SAMPLES_SPLIT, 
                    value=2,
                    tunable=True,
                    bounds=su.VOConfig_ParamBounds.integer(2, 10)  # Integer type instead of continuous
                ),
                # Categorical parameter
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.CRITERION, 
                    value="squared_error",
                    tunable=True,
                    bounds=su.VOConfig_ParamBounds.categorical(["squared_error", "absolute_error"])
                ),
                # Boolean parameter
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.BOOTSTRAP, 
                    value=True,
                    tunable=True,
                    bounds=su.VOConfig_ParamBounds.boolean()
                ),
                # Non-tunable parameter
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.MAX_DEPTH, 
                    value=5,
                    tunable=False
                )
            ]
        )
        
        hpo_config = su.VOConfig_HPO(
            n_trials=5,
            sampler="tpe",
            pruner="median"
        )
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Assert
        assert job.hpo_results is not None, "HPO results should not be None"
        assert job.hpo_results.best_params is not None, "Best parameters should not be None"
        
        # Check that only tunable parameters were optimized
        best_params = job.hpo_results.best_params
        
        # Non-tunable parameter should not be in best_params
        assert su.Hyperparams.RF.MAX_DEPTH not in best_params, \
            "Non-tunable parameter should not be in best_params"
        
        # Check parameter types
        n_estimators = best_params.get(su.Hyperparams.RF.N_ESTIMATORS)
        assert isinstance(n_estimators, int), f"N_ESTIMATORS should be integer, got {type(n_estimators)}"
        assert 5 <= n_estimators <= 15, f"N_ESTIMATORS out of bounds: {n_estimators}"
        
        min_samples_split = best_params.get(su.Hyperparams.RF.MIN_SAMPLES_SPLIT)
        assert isinstance(min_samples_split, (int, float)), \
            f"MIN_SAMPLES_SPLIT should be numeric, got {type(min_samples_split)}"
        
        criterion = best_params.get(su.Hyperparams.RF.CRITERION)
        assert criterion in ["squared_error", "absolute_error", "poisson"], \
            f"CRITERION not in allowed values: {criterion}"
        
        bootstrap = best_params.get(su.Hyperparams.RF.BOOTSTRAP)
        assert isinstance(bootstrap, bool), f"BOOTSTRAP should be boolean, got {type(bootstrap)}"
        
        print("Parameter tuning ranges test passed")
    
    def test_hpo_results_structure(self):
        """Test that HPO results contain expected metrics and information."""
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        training_data = create_training_samples(n_samples=200, n_features=5)
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # Create HPO config
        hpo_config = su.VOConfig_HPO(
            n_trials=3,
            sampler="tpe",
            pruner="median"
        )
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Assert
        assert job.hpo_results is not None, "HPO results should not be None"
        
        # Check HPO results structure
        results = job.hpo_results
        assert results.best_params is not None, "best_params missing"
        assert results.best_value is not None, "best_value missing"
        assert results.best_trial_id is not None, "best_trial_id missing"
        assert results.parameter_importance is not None, "parameter_importance missing"
        assert results.runtime_seconds > 0, "runtime_seconds should be positive"
        assert results.trials is not None and len(results.trials) == 3, "Should have 3 trials"
        
        # Check trial structure
        trial = results.trials[0]
        assert trial.trial_id is not None, "trial_id missing"
        assert trial.value is not None, "trial value missing"
        assert trial.params is not None, "trial params missing"
        assert trial.duration_seconds is not None, "trial duration missing"
        assert trial.best_value is not None, "trial best_value missing"
        
        # Test that HPO improved over random initialization
        trial_values = [t.value for t in results.trials]
        if results.direction == "minimize":
            assert results.best_value <= trial_values[0], "HPO should improve (minimize) over first trial"
        else:
            assert results.best_value >= trial_values[0], "HPO should improve (maximize) over first trial"
        
        print("HPO results structure test passed")
    
    def test_parameter_importance_calculation(self):
        """Test that parameter importance is correctly calculated in HPO."""
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        # Create dataset where one parameter will clearly matter more
        X_df, y_df = create_regression_dataset(n_samples=300, n_features=3, random_seed=42)
        training_data = su.VODataset(df_x=X_df, df_y=y_df, uid=uuid.uuid4())
        
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # More trials needed for reliable importance calculation
        hpo_config = su.VOConfig_HPO(
            n_trials=10,
            sampler="tpe",
            pruner="median"
        )
        
        # Act
        model, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Assert
        assert job.hpo_results is not None, "HPO results should not be None"
        assert job.hpo_results.parameter_importance is not None, "Parameter importance missing"
        
        # Parameter importance should be populated
        importance = job.hpo_results.parameter_importance
        assert len(importance) > 0, "Parameter importance should not be empty"
        
        # Sum of importance should be approximately 1.0
        importance_sum = sum(importance.values())
        assert 0.95 <= importance_sum <= 1.05, f"Importance sum should be approximately 1.0, got {importance_sum}"
        
        # All parameters should have non-negative importance
        for param, value in importance.items():
            assert value >= 0, f"Parameter {param} has negative importance: {value}"
        
        print("Parameter importance calculation test passed")
    
    def test_parallel_hpo_execution(self):
        """Test HPO with parallel execution using all available cores."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        # Use more samples to benefit from parallelization
        training_data = create_training_samples(n_samples=300, n_features=5)
        training_config = create_training_config(quick_mode=True)
        model_config = create_model_config()
        metadata = create_metadata()
        
        # First run with sequential execution as baseline
        sequential_hpo_config = su.VOConfig_HPO(
            n_trials=8,
            sampler="tpe",
            pruner="median",
            n_parallel_jobs=1  # Sequential
        )
        
        start_time = time.time()
        _, sequential_job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=sequential_hpo_config
        )
        sequential_runtime = time.time() - start_time
        
        # Now run with parallel execution
        parallel_hpo_config = su.VOConfig_HPO(
            n_trials=8,
            sampler="tpe",
            pruner="median",
            n_parallel_jobs=-1  # Use all cores
        )
        
        start_time = time.time()
        model, parallel_job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=parallel_hpo_config
        )
        parallel_runtime = time.time() - start_time
        
        # Assert
        assert parallel_job.status == su.EnumTrainingStatus.COMPLETE.value, "Parallel HPO execution failed"
        assert parallel_job.hpo_results is not None, "Parallel HPO should produce results"
        assert len(parallel_job.hpo_results.trials) == 8, f"Expected 8 trials, got {len(parallel_job.hpo_results.trials)}"
        
        # Verify parallel execution wasn't slower than sequential
        # Note: In some environments with few cores or overhead, this might not be significant
        logging.info(f"Sequential runtime: {sequential_runtime:.2f}s, Parallel runtime: {parallel_runtime:.2f}s")
        
        # Check that model can make predictions
        predictions = model.predict(training_data.df_x.head(5))
        assert predictions is not None, "Model trained with parallel HPO should make predictions"
        
        print("Parallel HPO execution test passed")

    def test_metrics_improvement_during_hpo(self):
        """Test that metrics improve over the course of hyperparameter optimization."""
        # Arrange
        experiment_registry, model_registry = create_stub_repository()
        surrogate = ISurrogate(experiment_registry, model_registry)
        
        # Create dataset with clear parameter sensitivity
        n_samples = 300
        n_features = 5
        X_df, y_df = create_regression_dataset(
            n_samples=n_samples, 
            n_features=n_features,
            noise=0.2,  # Add noise to make hyperparameter tuning more impactful
            random_seed=42
        )
        
        # Create training data with test/validation split
        training_data = su.VODataset(df_x=X_df, df_y=y_df, uid=uuid.uuid4())
        
        # Use more hyperparameter trials to observe improvement trend
        training_config = create_training_config(quick_mode=True)
        
        # Create model config with varied parameter ranges
        model_config = su.VOConfig_Model(
            algorithm=su.EnumSurrogateAlgorithm.RANDOM_FOREST,
            parameters=[
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.N_ESTIMATORS, 
                    value=10,
                    tunable=True,
                    bounds=su.VOConfig_ParamBounds.integer(10, 100)  # Wider range
                ),
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.MAX_DEPTH, 
                    value=5,
                    tunable=True,
                    bounds=su.VOConfig_ParamBounds.integer(3, 20)
                ),
                su.VOConfig_Parameter(
                    name=su.Hyperparams.RF.MIN_SAMPLES_LEAF, 
                    value=2,
                    tunable=True,
                    bounds=su.VOConfig_ParamBounds.integer(1, 10)
                )
            ]
        )
        metadata = create_metadata()
        
        # Configure HPO with more trials to see improvement trend
        hpo_config = su.VOConfig_HPO(
            n_trials=15,  # More trials to see improvement
            sampler="tpe",  # TPE should show improvement over random initial trials
            pruner="none",  # No pruning to see complete progression
            n_parallel_jobs=1
        )
        
        # Act
        _, job = surrogate.execute_training_job(
            metadata=metadata,
            training_data=training_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Assert
        assert job.hpo_results is not None, "HPO results should not be None"
        assert len(job.hpo_results.trials) == 15, f"Expected 15 trials, got {len(job.hpo_results.trials)}"
        
        # Extract trial metrics
        trials = sorted(job.hpo_results.trials, key=lambda t: t.trial_id)
        metric_values = [t.value for t in trials]
        
        # Verify improvement trend based on optimization direction
        direction = job.hpo_results.direction
        if direction == "minimize":
            # For minimization, values should decrease
            initial_trials = metric_values[:5]  # First third of trials
            final_trials = metric_values[-5:]   # Last third of trials
            
            min_initial = min(initial_trials)
            min_final = min(final_trials)
            
            assert min_final <= min_initial, f"HPO should improve (minimize) metrics over trials, but min_final={min_final} > min_initial={min_initial}"
            
            # Best value should be better than median of first trials
            assert job.hpo_results.best_value <= np.median(initial_trials), \
                f"Best value {job.hpo_results.best_value} should be better than median of initial trials {np.median(initial_trials)}"
                
        else:  # maximize
            # For maximization, values should increase
            initial_trials = metric_values[:5]
            final_trials = metric_values[-5:]
            
            max_initial = max(initial_trials)
            max_final = max(final_trials)
            
            assert max_final >= max_initial, f"HPO should improve (maximize) metrics over trials, but max_final={max_final} < max_initial={max_initial}"
            
            # Best value should be better than median of first trials
            assert job.hpo_results.best_value >= np.median(initial_trials), \
                f"Best value {job.hpo_results.best_value} should be better than median of initial trials {np.median(initial_trials)}"
        
        # Log improvement statistics
        logging.info(f"HPO Direction: {direction}")
        logging.info(f"Initial trials: {initial_trials}")
        logging.info(f"Final trials: {final_trials}")
        logging.info(f"Best value: {job.hpo_results.best_value}")
        
        print("HPO metrics improvement test passed")


# Simple runner for direct execution
if __name__ == "__main__":
    print("Ready for testing")
