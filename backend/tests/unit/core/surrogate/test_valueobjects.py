"""
Comprehensive tests for VODataset serialization and deserialization.

Tests cover:
- Basic serialization/deserialization round-trip integrity
- Tabular vs sequence data patterns
- Various array shapes and data types
- Large dataset handling
- Edge cases and error conditions
- Performance considerations for compression
"""

import json
import uuid
import numpy as np
from typing import Dict, Any, List, Tuple

# Import the VODataset class and related dependencies
from backend.core._surrogate.valueobjects import VODataset


def create_sample_tabular_data(n_samples: int = 100, n_features: int = 5, n_outputs: int = 1,
                              dtype: Any = np.float64) -> Tuple[np.ndarray, np.ndarray]:
    """Helper function to create sample tabular data for testing."""
    np.random.seed(42)  # For reproducible tests
    arr_x = np.random.randn(n_samples, n_features).astype(dtype)
    arr_y = np.random.randn(n_samples, n_outputs).astype(dtype)
    if n_outputs == 1:
        arr_y = arr_y.squeeze()  # Make it 1D for single output
    return arr_x, arr_y


def create_sample_sequence_data(n_samples: int = 50, n_timesteps: int = 10,
                               n_features: int = 3, n_outputs: int = 2,
                               dtype: Any = np.float32) -> Tuple[np.ndarray, np.ndarray]:
    """Helper function to create sample sequence data for testing."""
    np.random.seed(42)  # For reproducible tests
    arr_x = np.random.randn(n_samples, n_timesteps, n_features).astype(dtype)
    arr_y = np.random.randn(n_samples, n_timesteps, n_outputs).astype(dtype)
    return arr_x, arr_y


def create_large_tabular_data(n_samples: int = 10000, n_features: int = 100) -> Tuple[np.ndarray, np.ndarray]:
    """Helper function to create large tabular data for compression testing."""
    np.random.seed(42)
    arr_x = np.random.randn(n_samples, n_features).astype(np.float64)
    arr_y = np.random.randn(n_samples).astype(np.float64)
    return arr_x, arr_y


def verify_array_equality(arr1: np.ndarray, arr2: np.ndarray, tolerance: float = 1e-10) -> bool:
    """Helper function to verify array equality with tolerance for floating point precision."""
    if arr1.shape != arr2.shape:
        return False
    if arr1.dtype != arr2.dtype:
        return False
    return np.allclose(arr1, arr2, rtol=tolerance, atol=tolerance)


def verify_dataset_integrity(original: VODataset, deserialized: VODataset) -> bool:
    """Helper function to verify complete dataset integrity after round-trip serialization."""
    # Check all scalar fields
    if original.uid != deserialized.uid:
        return False
    if original.transformer_uid != deserialized.transformer_uid:
        return False
    if original.colnames_x != deserialized.colnames_x:
        return False
    if original.colnames_y != deserialized.colnames_y:
        return False
    if original.pattern != deserialized.pattern:
        return False

    # Check array integrity
    if not verify_array_equality(original.arr_x, deserialized.arr_x):
        return False
    if not verify_array_equality(original.arr_y, deserialized.arr_y):
        return False

    return True


class TestVODatasetBasicSerialization:
    """Tests for basic VODataset serialization functionality."""

    def test_tabular_dataset_json_roundtrip(self):
        """Test basic JSON serialization and deserialization for tabular data."""
        # Arrange: Create a sample tabular dataset
        arr_x, arr_y = create_sample_tabular_data(n_samples=50, n_features=3, n_outputs=1)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["feature_1", "feature_2", "feature_3"],
            colnames_y=["target"],
            pattern="tabular"
        )

        # Act: Serialize to JSON and back
        json_data = original_dataset.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_dataset = VODataset.model_validate(parsed_json)

        # Assert: Verify integrity
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Dataset integrity check failed"
        assert deserialized_dataset.pattern == "tabular", "Pattern should be preserved"
        print("Tabular dataset JSON round-trip test passed")

    def test_sequence_dataset_json_roundtrip(self):
        """Test basic JSON serialization and deserialization for sequence data."""
        # Arrange: Create a sample sequence dataset
        arr_x, arr_y = create_sample_sequence_data(n_samples=20, n_timesteps=5, n_features=2, n_outputs=1)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["sensor_1", "sensor_2"],
            colnames_y=["prediction"],
            pattern="sequence"
        )

        # Act: Serialize to JSON and back
        json_data = original_dataset.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_dataset = VODataset.model_validate(parsed_json)

        # Assert: Verify integrity
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Dataset integrity check failed"
        assert deserialized_dataset.pattern == "sequence", "Pattern should be preserved"
        print("Sequence dataset JSON round-trip test passed")


class TestVODatasetDataTypes:
    """Tests for different numpy data types in VODataset serialization."""

    def test_float32_arrays(self):
        """Test serialization with float32 arrays."""
        # Arrange: Create float32 data
        arr_x, arr_y = create_sample_tabular_data(n_samples=30, n_features=2, dtype=np.float32)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify data type preservation
        assert deserialized_dataset.arr_x.dtype == np.float32, f"Expected float32, got {deserialized_dataset.arr_x.dtype}"
        assert deserialized_dataset.arr_y.dtype == np.float32, f"Expected float32, got {deserialized_dataset.arr_y.dtype}"
        assert verify_array_equality(original_dataset.arr_x, deserialized_dataset.arr_x), "Float32 x array mismatch"
        assert verify_array_equality(original_dataset.arr_y, deserialized_dataset.arr_y), "Float32 y array mismatch"
        print("Float32 arrays test passed")

    def test_float64_arrays(self):
        """Test serialization with float64 arrays."""
        # Arrange: Create float64 data
        arr_x, arr_y = create_sample_tabular_data(n_samples=30, n_features=2, dtype=np.float64)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify data type preservation
        assert deserialized_dataset.arr_x.dtype == np.float64, f"Expected float64, got {deserialized_dataset.arr_x.dtype}"
        assert deserialized_dataset.arr_y.dtype == np.float64, f"Expected float64, got {deserialized_dataset.arr_y.dtype}"
        assert verify_array_equality(original_dataset.arr_x, deserialized_dataset.arr_x), "Float64 x array mismatch"
        assert verify_array_equality(original_dataset.arr_y, deserialized_dataset.arr_y), "Float64 y array mismatch"
        print("Float64 arrays test passed")

    def test_integer_arrays(self):
        """Test serialization with integer arrays."""
        # Arrange: Create integer data
        np.random.seed(42)
        arr_x = np.random.randint(0, 100, size=(25, 3)).astype(np.int32)
        arr_y = np.random.randint(0, 10, size=(25,)).astype(np.int32)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["int_x1", "int_x2", "int_x3"],
            colnames_y=["int_y"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify data type preservation
        assert deserialized_dataset.arr_x.dtype == np.int32, f"Expected int32, got {deserialized_dataset.arr_x.dtype}"
        assert deserialized_dataset.arr_y.dtype == np.int32, f"Expected int32, got {deserialized_dataset.arr_y.dtype}"
        assert verify_array_equality(original_dataset.arr_x, deserialized_dataset.arr_x), "Integer x array mismatch"
        assert verify_array_equality(original_dataset.arr_y, deserialized_dataset.arr_y), "Integer y array mismatch"
        print("Integer arrays test passed")


class TestVODatasetArrayShapes:
    """Tests for different array shapes in VODataset serialization."""

    def test_multidimensional_tabular_output(self):
        """Test serialization with multi-dimensional tabular output arrays."""
        # Arrange: Create tabular data with multiple outputs
        arr_x, _ = create_sample_tabular_data(n_samples=40, n_features=4, dtype=np.float64)
        arr_y = np.random.randn(40, 3).astype(np.float64)  # 3 output variables
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["f1", "f2", "f3", "f4"],
            colnames_y=["out1", "out2", "out3"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify shape preservation
        assert deserialized_dataset.arr_x.shape == (40, 4), f"Expected x shape (40, 4), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (40, 3), f"Expected y shape (40, 3), got {deserialized_dataset.arr_y.shape}"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Multi-output dataset integrity failed"
        print("Multi-dimensional tabular output test passed")

    def test_complex_sequence_shapes(self):
        """Test serialization with complex sequence data shapes."""
        # Arrange: Create sequence data with varying dimensions
        arr_x, arr_y = create_sample_sequence_data(n_samples=15, n_timesteps=8, n_features=4, n_outputs=2)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["sensor_a", "sensor_b", "sensor_c", "sensor_d"],
            colnames_y=["pred_1", "pred_2"],
            pattern="sequence"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify shape preservation
        assert deserialized_dataset.arr_x.shape == (15, 8, 4), f"Expected x shape (15, 8, 4), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (15, 8, 2), f"Expected y shape (15, 8, 2), got {deserialized_dataset.arr_y.shape}"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Complex sequence dataset integrity failed"
        print("Complex sequence shapes test passed")

    def test_single_sample_dataset(self):
        """Test serialization with single sample datasets."""
        # Arrange: Create minimal dataset with single sample
        arr_x = np.array([[1.0, 2.0, 3.0]], dtype=np.float64)
        arr_y = np.array([5.0], dtype=np.float64)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2", "x3"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify single sample preservation
        assert deserialized_dataset.arr_x.shape == (1, 3), f"Expected x shape (1, 3), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (1,), f"Expected y shape (1,), got {deserialized_dataset.arr_y.shape}"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Single sample dataset integrity failed"
        print("Single sample dataset test passed")


class TestVODatasetLargeArrays:
    """Tests for large array handling and compression in VODataset serialization."""

    def test_large_dataset_serialization(self):
        """Test serialization with large datasets that should trigger compression."""
        # Arrange: Create large dataset
        arr_x, arr_y = create_large_tabular_data(n_samples=5000, n_features=50)
        transformer_uid = uuid.uuid4()

        # Generate column names
        colnames_x = [f"feature_{i}" for i in range(50)]
        colnames_y = ["target"]

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=colnames_x,
            colnames_y=colnames_y,
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        json_str = json.dumps(json_data)
        parsed_json = json.loads(json_str)
        deserialized_dataset = VODataset.model_validate(parsed_json)

        # Assert: Verify large dataset integrity
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Large dataset integrity failed"
        assert deserialized_dataset.arr_x.shape == (5000, 50), f"Expected x shape (5000, 50), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (5000,), f"Expected y shape (5000,), got {deserialized_dataset.arr_y.shape}"

        # Verify that serialization is reasonably efficient (JSON string should not be excessively large)
        # This is a rough check - actual compression effectiveness will depend on implementation
        max_expected_size = arr_x.nbytes + arr_y.nbytes + 10000  # Allow some overhead
        assert len(json_str) < max_expected_size * 2, "Serialized data seems inefficiently large"

        print("Large dataset serialization test passed")

    def test_performance_benchmark(self):
        """Test serialization performance with moderately large datasets."""
        import time

        # Arrange: Create moderately large dataset
        arr_x, arr_y = create_large_tabular_data(n_samples=1000, n_features=20)
        transformer_uid = uuid.uuid4()

        colnames_x = [f"var_{i}" for i in range(20)]
        colnames_y = ["response"]

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=colnames_x,
            colnames_y=colnames_y,
            pattern="tabular"
        )

        # Act: Measure serialization time
        start_time = time.time()
        json_data = original_dataset.model_dump(mode="json")
        json_str = json.dumps(json_data)
        serialization_time = time.time() - start_time

        # Measure deserialization time
        start_time = time.time()
        parsed_json = json.loads(json_str)
        deserialized_dataset = VODataset.model_validate(parsed_json)
        deserialization_time = time.time() - start_time

        # Assert: Verify performance is reasonable (should complete in under 5 seconds each)
        assert serialization_time < 5.0, f"Serialization took too long: {serialization_time:.2f} seconds"
        assert deserialization_time < 5.0, f"Deserialization took too long: {deserialization_time:.2f} seconds"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Performance test dataset integrity failed"

        print(f"Performance benchmark test passed - Serialization: {serialization_time:.3f}s, Deserialization: {deserialization_time:.3f}s")


class TestVODatasetEdgeCases:
    """Tests for edge cases and error conditions in VODataset serialization."""

    def test_empty_arrays_handling(self):
        """Test handling of empty arrays (edge case)."""
        # Arrange: Create dataset with minimal valid arrays
        arr_x = np.empty((0, 3), dtype=np.float64)
        arr_y = np.empty((0,), dtype=np.float64)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2", "x3"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify empty array handling
        assert deserialized_dataset.arr_x.shape == (0, 3), f"Expected x shape (0, 3), got {deserialized_dataset.arr_x.shape}"
        assert deserialized_dataset.arr_y.shape == (0,), f"Expected y shape (0,), got {deserialized_dataset.arr_y.shape}"
        assert verify_dataset_integrity(original_dataset, deserialized_dataset), "Empty arrays dataset integrity failed"
        print("Empty arrays handling test passed")

    def test_special_float_values(self):
        """Test handling of special float values (NaN, inf, -inf)."""
        # Arrange: Create dataset with special float values
        arr_x = np.array([[1.0, np.nan, 3.0], [np.inf, 5.0, -np.inf]], dtype=np.float64)
        arr_y = np.array([np.nan, np.inf], dtype=np.float64)
        transformer_uid = uuid.uuid4()

        original_dataset = VODataset(
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=transformer_uid,
            colnames_x=["x1", "x2", "x3"],
            colnames_y=["y1"],
            pattern="tabular"
        )

        # Act: Serialize and deserialize
        json_data = original_dataset.model_dump(mode="json")
        deserialized_dataset = VODataset.model_validate(json_data)

        # Assert: Verify special values are preserved
        # Note: NaN comparison requires special handling
        assert deserialized_dataset.arr_x.shape == original_dataset.arr_x.shape, "Shape mismatch for special values"
        assert np.isnan(deserialized_dataset.arr_x[0, 1]), "NaN value not preserved in x array"
        assert np.isinf(deserialized_dataset.arr_x[1, 0]), "Inf value not preserved in x array"
        assert np.isneginf(deserialized_dataset.arr_x[1, 2]), "Negative inf value not preserved in x array"
        assert np.isnan(deserialized_dataset.arr_y[0]), "NaN value not preserved in y array"
        assert np.isinf(deserialized_dataset.arr_y[1]), "Inf value not preserved in y array"
        print("Special float values test passed")


# Simple runner for direct execution
if __name__ == "__main__":
    print("Running VODataset serialization tests...")

    # Basic serialization tests
    basic_tests = TestVODatasetBasicSerialization()
    basic_tests.test_tabular_dataset_json_roundtrip()
    basic_tests.test_sequence_dataset_json_roundtrip()

    # Data type tests
    dtype_tests = TestVODatasetDataTypes()
    dtype_tests.test_float32_arrays()
    dtype_tests.test_float64_arrays()
    dtype_tests.test_integer_arrays()

    # Array shape tests
    shape_tests = TestVODatasetArrayShapes()
    shape_tests.test_multidimensional_tabular_output()
    shape_tests.test_complex_sequence_shapes()
    shape_tests.test_single_sample_dataset()

    # Large array tests
    large_tests = TestVODatasetLargeArrays()
    large_tests.test_large_dataset_serialization()
    large_tests.test_performance_benchmark()

    # Edge case tests
    edge_tests = TestVODatasetEdgeCases()
    edge_tests.test_empty_arrays_handling()
    edge_tests.test_special_float_values()

    print("All VODataset serialization tests completed successfully!")

