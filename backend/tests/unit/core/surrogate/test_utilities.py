"""
Unit tests for the utility functions in _utilities.py
"""
import uuid
import numpy as np
import typing as T
import pytest
import pandas as pd
import base64
import binascii

from backend.core._surrogate._utilities import split_data_simple, decode_and_filter_csv
from backend.core._surrogate.valueobjects import VODataset


class TestDataSplitting:
    """Tests for data splitting utilities."""

    def setup_test_dataset(self, n_timesets=10, n_timesteps=5, n_features=3, n_outputs=2):
        """Helper function to create a test dataset."""
        # Create synthetic data arrays
        arr_x = np.random.random((n_timesets, n_timesteps, n_features))
        arr_y = np.random.random((n_timesets, n_timesteps, n_outputs))
        
        # Create column names
        colnames_x = [f"feature_{i}" for i in range(n_features)]
        colnames_y = [f"output_{i}" for i in range(n_outputs)]
        
        # Create dataset
        dataset = VODataset(
            uid=uuid.uuid4(),
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=uuid.uuid4(),
            colnames_x=colnames_x,
            colnames_y=colnames_y,
            pattern="sequence"
        )
        
        return dataset
    
    def verify_dataset_structure(self, dataset, expected_shape_x, expected_shape_y):
        """Helper function to verify dataset structure."""
        assert dataset.arr_x.shape == expected_shape_x, f"Expected X shape {expected_shape_x}, got {dataset.arr_x.shape}"
        assert dataset.arr_y.shape == expected_shape_y, f"Expected Y shape {expected_shape_y}, got {dataset.arr_y.shape}"
        assert len(dataset.colnames_x) == expected_shape_x[2], "Column names X count mismatch"
        assert len(dataset.colnames_y) == expected_shape_y[2], "Column names Y count mismatch"
        return True

    def test_basic_split_ts(self):
        """Tests split where array is 3D shape (there is timeset and timesteps)."""
        # Arrange
        n_timesets = 50
        n_timesteps = 8
        n_features = 4
        n_outputs = 2
        test_dataset = self.setup_test_dataset(
            n_timesets=n_timesets, 
            n_timesteps=n_timesteps,
            n_features=n_features,
            n_outputs=n_outputs
        )
        validation_ratio = 0.2
        random_seed = 42
        
        # Act
        train_data, val_data = split_data_simple(test_dataset, validation_ratio, random_seed)
        
        # Assert
        # Check dimensions are preserved
        expected_train_size = int(n_timesets * (1 - validation_ratio))
        expected_val_size = n_timesets - expected_train_size
        
        assert train_data.arr_x.shape == (expected_train_size, n_timesteps, n_features), \
            f"Training X shape incorrect: {train_data.arr_x.shape}"
        assert train_data.arr_y.shape == (expected_train_size, n_timesteps, n_outputs), \
            f"Training Y shape incorrect: {train_data.arr_y.shape}"
        assert val_data.arr_x.shape == (expected_val_size, n_timesteps, n_features), \
            f"Validation X shape incorrect: {val_data.arr_x.shape}"
        assert val_data.arr_y.shape == (expected_val_size, n_timesteps, n_outputs), \
            f"Validation Y shape incorrect: {val_data.arr_y.shape}"
        
        # Check that pattern is preserved (should be "sequence" for 3D data)
        assert train_data.pattern == "sequence", "Pattern should be preserved as 'sequence'"
        assert val_data.pattern == "sequence", "Pattern should be preserved as 'sequence'"
        
        print("Time series (3D) split test passed")
    
    def test_basic_split_flat(self):
        """Tests split where array is simple 2d shape. i.e. just features"""
        # Arrange
        # Create a 2D dataset (samples x features) instead of 3D
        n_samples = 100
        n_features = 5
        n_outputs = 2
        
        # Create 2D arrays
        arr_x = np.random.random((n_samples, n_features))
        arr_y = np.random.random((n_samples, n_outputs))
        
        # Create column names
        colnames_x = [f"feature_{i}" for i in range(n_features)]
        colnames_y = [f"output_{i}" for i in range(n_outputs)]
        
        # Create dataset with flat data (pattern="tabular")
        flat_dataset = VODataset(
            uid=uuid.uuid4(),
            arr_x=arr_x,
            arr_y=arr_y,
            transformer_uid=uuid.uuid4(),
            colnames_x=colnames_x,
            colnames_y=colnames_y,
            pattern="tabular"  # Using tabular for flat data
        )
        
        validation_ratio = 0.2
        random_seed = 42
        
        # Act
        train_data, val_data = split_data_simple(flat_dataset, validation_ratio, random_seed)
        
        # Assert
        # Check dimensions are preserved
        expected_train_size = int(n_samples * (1 - validation_ratio))
        expected_val_size = n_samples - expected_train_size
        
        assert train_data.arr_x.shape == (expected_train_size, n_features), \
            f"Training X shape incorrect: {train_data.arr_x.shape}"
        assert train_data.arr_y.shape == (expected_train_size, n_outputs), \
            f"Training Y shape incorrect: {train_data.arr_y.shape}"
        assert val_data.arr_x.shape == (expected_val_size, n_features), \
            f"Validation X shape incorrect: {val_data.arr_x.shape}"
        assert val_data.arr_y.shape == (expected_val_size, n_outputs), \
            f"Validation Y shape incorrect: {val_data.arr_y.shape}"
        
        # Check that pattern is preserved (should be "tabular" for 2D data)
        assert train_data.pattern == "tabular", "Pattern should be preserved as 'tabular'"
        assert val_data.pattern == "tabular", "Pattern should be preserved as 'tabular'"
        
        # Check metadata is preserved
        assert train_data.transformer_uid == flat_dataset.transformer_uid, "Transformer UID not preserved in train set"
        assert val_data.transformer_uid == flat_dataset.transformer_uid, "Transformer UID not preserved in val set"
        
        # Verify column names are preserved
        assert train_data.colnames_x == flat_dataset.colnames_x, "Feature column names should be preserved"
        assert train_data.colnames_y == flat_dataset.colnames_y, "Output column names should be preserved"
        
        print("Flat (2D) split test passed")

    
    def test_different_validation_ratios(self):
        """Test splitting with different validation ratios."""
        # Arrange
        test_dataset = self.setup_test_dataset(n_timesets=100)
        ratios = [0.1, 0.3, 0.5]
        
        for ratio in ratios:
            # Act
            train_data, val_data = split_data_simple(test_dataset, ratio, 42)
            
            # Assert
            expected_val_size = int(100 * ratio)
            expected_train_size = 100 - expected_val_size
            
            assert train_data.arr_x.shape[0] == expected_train_size, f"Training set should have {expected_train_size} timesets with ratio {ratio}"
            assert val_data.arr_x.shape[0] == expected_val_size, f"Validation set should have {expected_val_size} timesets with ratio {ratio}"
            
            # Verify both datasets combined make the full dataset
            assert train_data.arr_x.shape[0] + val_data.arr_x.shape[0] == 100, "Split sizes should add up to original size"
            
            print(f"Split test with ratio {ratio} passed")
    
    def test_random_seed_consistency(self):
        """Test that the same random seed produces the same split."""
        # Arrange
        test_dataset = self.setup_test_dataset(n_timesets=50)
        ratio = 0.2
        seed = 42
        
        # Act - Split twice with the same seed
        train_1, val_1 = split_data_simple(test_dataset, ratio, seed)
        train_2, val_2 = split_data_simple(test_dataset, ratio, seed)
        
        # Assert - Splits should be identical
        np.testing.assert_array_equal(train_1.arr_x, train_2.arr_x, "Training sets should be identical with same seed")
        np.testing.assert_array_equal(val_1.arr_x, val_2.arr_x, "Validation sets should be identical with same seed")
        
        # Act - Split with a different seed
        train_3, val_3 = split_data_simple(test_dataset, ratio, seed + 1)
        
        # Assert - Should be different with different seed (high probability)
        # It's theoretically possible but extremely unlikely that different seeds give the same split
        are_different = not np.array_equal(train_1.arr_x, train_3.arr_x) or not np.array_equal(val_1.arr_x, val_3.arr_x)
        assert are_different, "Different seeds should produce different splits (with high probability)"
        
        print("Random seed consistency test passed")
    
    def test_edge_case_empty_dataset(self):
        """Test behavior with an empty dataset."""
        # Arrange - Create dataset with 0 timesets
        empty_dataset = self.setup_test_dataset(n_timesets=0)
        ratio = 0.2
        
        # Act & Assert - Should handle empty input gracefully
        with pytest.raises(ValueError) as info:
            train_data, val_data = split_data_simple(empty_dataset, ratio, 42)
        
        print("Empty dataset test passed")

class TestCSV2Dataframe:
    """Tests for `decode_and_filter_csv` in backend/core/_surrogate/_utilities.py"""

    def create_testcase(self) -> T.Tuple[str, T.List[str]]:
        """
        Creates a simple CSV, encodes it as base64, and returns the encoded string and column names.
        """
        df = pd.DataFrame({
            'A': [1, 2, 3],
            'B': [4, 5, 6],
            'C': [7, 8, 9]
        })
        csv_str = df.to_csv(index=False)
        b64csv = base64.b64encode(csv_str.encode('utf-8')).decode('utf-8')
        columns = ['A', 'B']
        return b64csv, columns

    def test_decode_and_filter_csv(self):
        """Test normal operation: decode and filter columns."""
        b64csv, columns = self.create_testcase()
        df = decode_and_filter_csv(b64csv, columns)
        assert list(df.columns) == columns, f"Columns should be filtered to {columns}"
        assert df.shape == (3, 2), f"Shape should be (3, 2), got {df.shape}"
        print("decode_and_filter_csv normal operation test passed")

    def test_missing_column(self):
        """Test error when a requested column is missing."""
        b64csv, columns = self.create_testcase()
        missing_columns = columns + ['Z']
        try:
            decode_and_filter_csv(b64csv, missing_columns)
            assert False, "Should raise ValueError for missing columns"
        except ValueError as e:
            assert "Missing columns" in str(e), f"Error message should mention missing columns, got: {e}"
        print("decode_and_filter_csv missing column test passed")

    def test_empty_csv(self):
        """Test error on empty CSV input."""
        empty_b64 = base64.b64encode(b"\n").decode('utf-8')
        with pytest.raises(ValueError) as info:
            decode_and_filter_csv(empty_b64, ['A'])

        print("decode_and_filter_csv empty CSV test passed")

    def test_invalid_base64(self):
        """Test error on invalid base64 input."""
        invalid_b64 = "not_base64!"
        try:
            decode_and_filter_csv(invalid_b64, ['A'])
            assert False, "Should raise an exception for invalid base64 input"
        except Exception as e:
            assert isinstance(e, (binascii.Error, UnicodeDecodeError)), f"Should raise base64 or decode error, got: {type(e)}"
        print("decode_and_filter_csv invalid base64 test passed")

# Simple runner for direct execution
if __name__ == "__main__":
    print("ready for tests")