"""
Unit tests for Seq2SeqRNNTrainer in backend.core._surrogate.trainers.trainer_rnn

This file contains tests for the Seq2SeqRNNTrainer class, focusing on the
__train_with_validation method which handles time series model training.

Tests cover basic functionality, configuration variations, and edge cases
following the project's testing standards.
"""

import sys
import uuid
import unittest
import copy
import logging
import time
from typing import Dict, List, Any, Optional, Tuple, Literal
from pprint import pprint, pformat

import numpy as np
import numpy.typing as npt
import pandas as pd
import pytest
import torch
import torch.nn as nn
import optuna

# Import the split_data_simple utility
from backend.core._surrogate._utilities import split_data_simple, get_optimal_num_workers
from backend.core._surrogate.trainers.trainer_rnn import Seq2SeqTSTrainer, Model_RNN
from backend.core._surrogate.entities import ENTTrainingJob
from backend.core._surrogate._enums import (
    EnumTrainingStatus,
    EnumSurrogateAlgorithm,
    EnumMetricName,
    Hyperparams,
)
from backend.core._surrogate.valueobjects import (
    VODataset,
    VOMetadata_General,
    VOConfig_Training,
    VOConfig_Model,
    VOConfig_HPO,
    VOConfig_Parameter,
    VOConfig_ParamBounds,
    VOLog_ModelMetrics,
    VOLog_EpochMetrics,
)


# -------------------------------------------------------
# Helper functions for test data generation and validation
# -------------------------------------------------------

def create_basic_metadata() -> VOMetadata_General:
    """Generate a basic metadata object for testing."""
    return VOMetadata_General(
        label="Test Surrogate Model",
        user_reference="test_user",
        atlas_reference="test_atlas",
        surrogate_algo=EnumSurrogateAlgorithm.RNN_TS,
        description="Test metadata for surrogate model"
    )
def create_mock_training_job() -> ENTTrainingJob:
    """Create a mock ENTTrainingJob for testing."""
    training_config = create_training_config()
    model_config = create_time_series_model_config()
    
    return ENTTrainingJob(
            metadata = create_basic_metadata(),
            training_configuration=training_config,
            model_configuration=model_config,
            status=EnumTrainingStatus.PENDING.value
    )


def create_time_series_model_config(overrides: Optional[Dict[str, Any]] = None) -> VOConfig_Model:
    """
    Create a standard model configuration with optional overrides.
    
    Args:
        overrides: Dictionary of parameter name to value overrides
        
    Returns:
        VOConfig_Model with reasonable defaults
    """
    params = [
        VOConfig_Parameter(
            name=Hyperparams.NN.HIDDEN_SIZE,
            value=32,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="integer",
                min_value=16,
                max_value=128
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.SEQ_NUM_LAYERS,
            value=1,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="integer",
                min_value=1,
                max_value=3
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.DENSE_NUM_LAYERS,
            value=1,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="integer",
                min_value=1,
                max_value=3
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.RNN_TYPE,
            value="LSTM",
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="categorical",
                choices=["LSTM", "GRU"]
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.DROPOUT,
            value=0.1,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="continuous",
                min_value=0.0,
                max_value=0.5
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.BATCH_SIZE,
            value=16,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="categorical",
                choices=[8, 16, 32, 64]
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.LEARNING_RATE,
            value=0.001,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="continuous_logscale",
                min_value=1e-5,
                max_value=1e-2
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.LOSS_FUNCTION,
            value="mse",
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="categorical",
                choices=["mse", "mae"]
            )
        )
    ]
    
    # Override param values if specified
    if overrides:
        for param in params:
            if param.name.name in overrides:
                # Create a new parameter with the overridden value
                param = VOConfig_Parameter(
                    name=param.name,
                    value=overrides[param.name.name],
                    tunable=param.tunable,
                    bounds=param.bounds
                )
    
    return VOConfig_Model(
        algorithm=EnumSurrogateAlgorithm.RNN_TS,
        parameters=params
    )


def create_training_config(max_iterations: int = 10) -> VOConfig_Training:
    """
    Create a standard training configuration.
    
    Args:
        max_iterations: Number of training epochs
        
    Returns:
        VOConfig_Training instance
    """
    return VOConfig_Training(
        primary_metric=EnumMetricName.RMSE,
        max_iterations=max_iterations,
        random_seed=42,
        validation_strategy="simple_split",
        validation_split=0.2,
        enable_early_stopping=True,
        early_stopping_rounds=5,
        early_stopping_min_delta=0.001
    )


def generate_synthetic_timeseries(
    n_timesets: int = 5,
    n_timesteps: int = 20,
    n_features: int = 3,
    n_outputs: int = 2,
    noise_level: float = 0.1
) -> Tuple[VODataset, VODataset]:
    """
    Generate synthetic time series data with known patterns.
    
    Args:
        n_timesets: Number of distinct time series
        n_timesteps: Number of steps in each time series
        n_features: Number of input features
        n_outputs: Number of output features
        noise_level: Standard deviation of random noise
        
    Returns:
        Tuple of (training_dataset, validation_dataset)
    """
    # Set random seed for reproducibility
    np.random.seed(42)
    
    # Prepare empty arrays
    X = np.zeros((n_timesets * n_timesteps, n_features))
    y = np.zeros((n_timesets * n_timesteps, n_outputs))
    
    # Generate time series data with AR(1) pattern
    timeset_col = np.repeat(np.arange(n_timesets), n_timesteps)
    timestep_col = np.tile(np.arange(n_timesteps), n_timesets)
    
    # Build the DataFrame with timesets and timesteps
    df_x = pd.DataFrame()
    df_x['timeset'] = timeset_col
    df_x['timestep'] = timestep_col
    
    # Generate features with sine patterns
    for i in range(n_features):
        freq = 0.1 * (i + 1)
        phase = i * 0.5
        df_x[f'feature_{i}'] = np.sin(freq * timestep_col + phase)
        # Add noise and time series specific offset
        df_x[f'feature_{i}'] += noise_level * np.random.randn(len(timestep_col))
        df_x[f'feature_{i}'] += timeset_col * 0.1
    
    # Generate targets as a function of features with lag
    df_y = pd.DataFrame()
    df_y['timeset'] = timeset_col
    df_y['timestep'] = timestep_col
    
    for i in range(n_outputs):
        # Initialize target column
        df_y[f'target_{i}'] = 0.0
        
        # Target is a weighted sum of features with different lags
        for j in range(n_features):
            lag = j + 1
            # Apply lag - shift the feature data and fill NAs with 0
            lagged_feature = df_x.groupby('timeset')[f'feature_{j}'].shift(lag).fillna(0)
            # Add weighted contribution of this lagged feature
            weight = 0.5 / (j + 1)
            df_y[f'target_{i}'] += weight * lagged_feature
        
        # Add nonlinearity, different for each target
        if i % 2 == 0:
            df_y[f'target_{i}'] = np.sin(df_y[f'target_{i}'])
        else:
            df_y[f'target_{i}'] = np.exp(df_y[f'target_{i}'] * 0.5)
            
        # Add noise
        df_y[f'target_{i}'] += noise_level * np.random.randn(len(timestep_col))
    
    # Extract feature and target column names
    feature_cols = [f'feature_{i}' for i in range(n_features)]
    target_cols = [f'target_{i}' for i in range(n_outputs)]
    
    # Reshape data for the VODataset
    # For sequence data, we need 3D arrays: [n_timesets, n_timesteps, n_features]
    arr_x = np.zeros((n_timesets, n_timesteps, n_features))
    arr_y = np.zeros((n_timesets, n_timesteps, n_outputs))
    
    for ts in range(n_timesets):
        ts_mask = df_x['timeset'] == ts
        for step in range(n_timesteps):
            step_mask = df_x['timestep'] == step
            mask = ts_mask & step_mask
            if mask.any():
                arr_x[ts, step, :] = df_x.loc[mask, feature_cols].values.flatten()
                arr_y[ts, step, :] = df_y.loc[mask, target_cols].values.flatten()
    
    # Create transformer UID (placeholder for testing)
    transformer_uid = uuid.uuid4()
    
    # Create full dataset
    full_dataset = VODataset(
        uid=uuid.uuid4(),
        arr_x=arr_x,
        arr_y=arr_y,
        transformer_uid=transformer_uid,
        colnames_x=feature_cols,
        colnames_y=target_cols,
        pattern="sequence"
    )
    
    # Split into training and validation
    train_size = int(n_timesets * 0.8)
    
    train_dataset = VODataset(
        uid=uuid.uuid4(),
        arr_x=arr_x[:train_size],
        arr_y=arr_y[:train_size],
        transformer_uid=transformer_uid,
        colnames_x=feature_cols,
        colnames_y=target_cols,
        pattern="sequence"
    )
    
    val_dataset = VODataset(
        uid=uuid.uuid4(),
        arr_x=arr_x[train_size:],
        arr_y=arr_y[train_size:],
        transformer_uid=transformer_uid,
        colnames_x=feature_cols,
        colnames_y=target_cols,
        pattern="sequence"
    )
    
    return train_dataset, val_dataset


def verify_training_progress(job: ENTTrainingJob) -> bool:
    """
    Validate that training progressed as expected based on job history.
    
    Args:
        job: Training job to verify
        
    Returns:
        True if training showed expected improvement pattern, False otherwise
    """
    # Check for status updates
    if not job.status:
        return False
    
    # Check for epoch metrics
    if not job.epoch_metrics:
        return False
    
    # Check that we have at least 2 epochs to compare
    if len(job.epoch_metrics) < 2:
        return True  # Not enough epochs to verify progress
    
    # Get training loss history
    train_losses = [epoch.train_metric for epoch in job.epoch_metrics if epoch.train_metric]
    
    if not train_losses:
        return False
    
    # Check if training loss decreased overall
    # We don't expect monotonic decrease due to stochasticity,
    # but the final loss should be less than the initial loss
    return train_losses[-1] < train_losses[0] # type: ignore


# -------------------------------------------------------
# Test Classes
# -------------------------------------------------------

class TestSeq2SeqTrainerBasics:
    """Tests for basic functionality of the Seq2SeqRNNTrainer."""
    
    def test_model_initialization(self):
        """Test that the trainer correctly initializes a model."""
        # Arrange
        trainer = Seq2SeqTSTrainer()
        train_data, _ = generate_synthetic_timeseries(
            n_timesets=3, 
            n_timesteps=10, 
            n_features=2, 
            n_outputs=1
        )
        model_config = create_time_series_model_config()
        
        # Act
        model = trainer._initialize_model(model_config, train_data)
        
        # Assert
        assert isinstance(model, Model_RNN), "Should initialize a Seq2SeqRNNModel"
        assert isinstance(model.sequence_block, nn.Module), "Should have a sequence block"
        assert isinstance(model.dense_block, nn.Module), "Should have a dense block"
        assert isinstance(model.output_layer, nn.Module), "Should have an output layer"
        
        # Check dimensions match the data
        n_features = len(train_data.colnames_x)
        n_outputs = len(train_data.colnames_y)
        assert model.sequence_block.rnn.input_size == n_features, "Input size should match number of features"
        assert model.output_layer.out_features == n_outputs, "Output size should match number of targets"
        
        print("Model initialization test passed")
    
    def test_basic_training(self):
        """Test standard training process completes successfully."""
        # Arrange: Set up test data and configuration
        train_data, val_data = generate_synthetic_timeseries(
            n_timesets=10,
            n_timesteps=10,
            n_features=8,
            n_outputs=3
        )
        model_config = create_time_series_model_config({'BATCH_SIZE': 8})
        training_config = create_training_config(max_iterations=3)
        job = create_mock_training_job()
        
        # Act: Train the model
        trainer = Seq2SeqTSTrainer()
        model = trainer._train_with_validation(
            train_set=train_data,
            val_set=val_data,
            training_config=training_config,
            model_config=model_config,
            job=job
        )
        
        # Assert: Verify the model was trained
        assert isinstance(model, torch.nn.Module), "Should return a PyTorch model"
        assert len(job.epoch_metrics) > 0, "Should have recorded epoch metrics"
        assert job.epoch_metrics[0].train_metric is not None, "Should have training metrics"
        assert job.epoch_metrics[0].validation_metric is not None, "Should have validation metrics"
        
        print("Basic training test passed")
        return (model, train_data, val_data)
    
    def test_loss_decreases_during_training(self):
        """Verify that loss decreases over training epochs."""
        # Arrange: Create a simple dataset with clear patterns
        train_data, val_data = generate_synthetic_timeseries(
            n_timesets=3,
            n_timesteps=15,
            n_features=2,
            n_outputs=1,
            noise_level=0.05  # Low noise to ensure learning
        )
        model_config = create_time_series_model_config({
            'BATCH_SIZE': 8,
            'LEARNING_RATE': 0.01  # Higher learning rate for faster convergence
        })
        training_config = create_training_config(max_iterations=5)
        job = create_mock_training_job()
        
        # Act: Train the model
        trainer = Seq2SeqTSTrainer()
        model = trainer._train_with_validation(
            train_set=train_data,
            val_set=val_data,
            training_config=training_config,
            model_config=model_config,
            job=job
        )
        
        # Assert: Verify loss decreased
        assert verify_training_progress(job), "Loss should decrease during training"
        
        # Get training and validation losses
        train_losses = [epoch.train_metric for epoch in job.epoch_metrics]
        val_losses = [epoch.validation_metric for epoch in job.epoch_metrics if epoch.validation_metric]
        
        # Check final losses are lower than initial
        assert train_losses[-1] < train_losses[0], "Training loss should decrease" # type: ignore
        
        if len(val_losses) > 1:
            # Validation loss may fluctuate more than training loss
            # Check that minimum validation loss is lower than initial
            assert min(val_losses) < val_losses[0], "Validation loss should decrease at some point" # type: ignore
        
        print("Loss decrease test passed")


class TestSeq2SeqTrainerConfiguration:
    """Tests for training with different configurations."""
    
    def test_early_stopping(self):
        """Test that early stopping correctly terminates training."""
        # Arrange
        train_data, val_data = generate_synthetic_timeseries(
            n_timesets=3,
            n_timesteps=10,
            n_features=2,
            n_outputs=1
        )
        
        # Configure early stopping with strict parameters
        model_config = create_time_series_model_config()
        model_config = VOConfig_Model(
            algorithm=model_config.algorithm,
            parameters=model_config.parameters + [
                VOConfig_Parameter(
                    name=Hyperparams.NN.EARLY_STOPPING,
                    value=True,
                    tunable=False
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.PATIENCE,
                    value=2,  # Very low patience to trigger early stopping quickly
                    tunable=False
                ),
                VOConfig_Parameter(
                    name=Hyperparams.NN.MIN_DELTA,
                    value=0.1,  # High min_delta to make improvement unlikely
                    tunable=False
                )
            ]
        )
        
        training_config = create_training_config(max_iterations=10)
        job = create_mock_training_job()
        
        # Act
        trainer = Seq2SeqTSTrainer()
        model = trainer._train_with_validation(
            train_set=train_data,
            val_set=val_data,
            training_config=training_config,
            model_config=model_config,
            job=job
        )
        
        # Assert
        # Check that training stopped before the maximum number of epochs
        assert len(job.epoch_metrics) < training_config.max_iterations, \
            "Training should stop before reaching max iterations"
        
        # Check that early stopping is mentioned in the status
        assert "early stopping" in job.status.lower(), \
            "Job status should mention early stopping"
            
        # Check that early_stopping_triggered flag is set to True
        assert job.early_stopping_triggered is True, \
            "The early_stopping_triggered flag should be set to True"
        
        print("Early stopping test passed")
    
    def test_learning_rate_scheduler(self):
        """Test learning rate schedulers (step and plateau)."""
        # Test both scheduler types
        scheduler_types = ["step", "plateau"]
        
        for scheduler_type in scheduler_types:
            # Arrange
            train_data, val_data = generate_synthetic_timeseries(
                n_timesets=3,
                n_timesteps=10,
                n_features=2,
                n_outputs=1
            )
            
            # Configure with LR scheduler
            model_config = create_time_series_model_config()
            model_config = VOConfig_Model(
                algorithm=model_config.algorithm,
                parameters=model_config.parameters + [
                    VOConfig_Parameter(
                        name=Hyperparams.NN.LR_SCHEDULER_TYPE,
                        value=scheduler_type,
                        tunable=False
                    ),
                    VOConfig_Parameter(
                        name=Hyperparams.NN.LR_SCHEDULER_STEP_SIZE if scheduler_type == "step" else Hyperparams.NN.LR_SCHEDULER_PATIENCE,
                        value=2,  # Small value for testing
                        tunable=False
                    ),
                    VOConfig_Parameter(
                        name=Hyperparams.NN.LR_SCHEDULER_GAMMA,
                        value=0.5,  # Significant reduction for testing
                        tunable=False
                    )
                ]
            )
            
            training_config = create_training_config(max_iterations=10)
            job = create_mock_training_job()
            
            # Act
            trainer = Seq2SeqTSTrainer()
            model = trainer._train_with_validation(
                train_set=train_data,
                val_set=val_data,
                training_config=training_config,
                model_config=model_config,
                job=job
            )
            
            # Assert
            assert isinstance(model, torch.nn.Module), f"{scheduler_type} scheduler should complete training"
            assert len(job.epoch_metrics) > 0, f"{scheduler_type} scheduler should record metrics"
            assert job.status.startswith(EnumTrainingStatus.COMPLETE.value), f"Job status should start with 'Complete' for {scheduler_type} scheduler"
            
            # Verify training completed all epochs (no early termination)
            assert len(job.epoch_metrics) == training_config.max_iterations, \
                f"{scheduler_type} scheduler should complete all epochs"
                
            # Check that loss decreased during training
            train_losses = [epoch.train_metric for epoch in job.epoch_metrics]
            assert train_losses[-1] < train_losses[0], f"{scheduler_type} scheduler should enable effective training (loss decreased)" # type: ignore
            
            print(f"Learning rate scheduler '{scheduler_type}' test passed")
    
    def test_gradient_clipping(self):
        """Test model training with gradient clipping enabled."""
        # Arrange
        train_data, val_data = generate_synthetic_timeseries(
            n_timesets=3,
            n_timesteps=10,
            n_features=2,
            n_outputs=1
        )
        
        # Configure with gradient clipping
        model_config = create_time_series_model_config()
        model_config = VOConfig_Model(
            algorithm=model_config.algorithm,
            parameters=model_config.parameters + [
                VOConfig_Parameter(
                    name=Hyperparams.NN.GRADIENT_CLIP_VAL,
                    value=1.0,  # Strict clipping for testing
                    tunable=False
                )
            ]
        )
        
        training_config = create_training_config(max_iterations=3)
        job = create_mock_training_job()
        
        # Act
        trainer = Seq2SeqTSTrainer()
        model = trainer._train_with_validation(
            train_set=train_data,
            val_set=val_data,
            training_config=training_config,
            model_config=model_config,
            job=job
        )
        
        # Assert
        assert isinstance(model, torch.nn.Module), "Training should complete with gradient clipping"
        assert job.status.startswith(EnumTrainingStatus.COMPLETE.value), "Job status should start with 'Complete'"
        assert len(job.epoch_metrics) == training_config.max_iterations, "Should complete all training epochs"
        
        # Verify we have valid training and validation metrics
        for epoch in job.epoch_metrics:
            assert epoch.train_metric is not None, "Should have training metrics for each epoch"
            assert epoch.train_metric is not None, "Training MSE should be calculated"
            assert epoch.validation_metric is not None, "Should have validation metrics for each epoch"
            
        # Check that loss decreased during training (model can still learn with clipping)
        train_losses = [epoch.train_metric for epoch in job.epoch_metrics]
        assert train_losses[-1] < train_losses[0], "Training loss should decrease with gradient clipping" # type: ignore
        
        print("Gradient clipping test passed")
        
    def test_different_loss_functions(self):
        """Test training with different loss functions (MSE, MAE)."""
        # Test both loss function types
        loss_functions = ["mse", "mae"]
        
        for loss_fn in loss_functions:
            # Arrange
            train_data, val_data = generate_synthetic_timeseries(
                n_timesets=3,
                n_timesteps=10,
                n_features=2,
                n_outputs=1
            )
            
            # Configure with specified loss function
            model_config = create_time_series_model_config()
            model_config = VOConfig_Model(
                algorithm=model_config.algorithm,
                parameters=model_config.parameters + [
                    VOConfig_Parameter(
                        name=Hyperparams.NN.LOSS_FUNCTION,
                        value=loss_fn,
                        tunable=False
                    )
                ]
            )
            
            training_config = create_training_config(max_iterations=3)
            job = create_mock_training_job()
            
            # Act
            trainer = Seq2SeqTSTrainer()
            model = trainer._train_with_validation(
                train_set=train_data,
                val_set=val_data,
                training_config=training_config,
                model_config=model_config,
                job=job
            )
            
            # Assert
            assert isinstance(model, torch.nn.Module), f"{loss_fn} loss function should complete training"
            assert job.status.startswith(EnumTrainingStatus.COMPLETE.value), f"Job status should start with 'Complete' for {loss_fn} loss function"
            assert len(job.epoch_metrics) == training_config.max_iterations, f"Should complete all epochs with {loss_fn} loss"
            
            # Verify metrics are calculated correctly for each epoch
            for epoch_metrics in job.epoch_metrics:
                assert epoch_metrics.train_metric is not None, f"Should have training metrics with {loss_fn} loss"
                assert epoch_metrics.validation_metric is not None, f"Should have validation metrics with {loss_fn} loss"
                
                # MSE and RMSE should always be calculated regardless of loss function used
                assert epoch_metrics.train_metric is not None, f"MSE should be calculated with {loss_fn} loss"
                
            # Check that loss decreased during training
            train_losses = [epoch.train_metric for epoch in job.epoch_metrics]
            assert train_losses[-1] < train_losses[0], f"Training loss should decrease with {loss_fn} loss function" # type: ignore
            
            print(f"Loss function '{loss_fn}' test passed")


class TestSeq2SeqTrainerEdgeCases:
    """Tests for edge cases and error handling in the trainer."""
    
    def test_single_epoch_training(self):
        """Test training with only a single epoch."""
        # Arrange
        train_data, val_data = generate_synthetic_timeseries(
            n_timesets=2,
            n_timesteps=5,
            n_features=2,
            n_outputs=1
        )
        
        model_config = create_time_series_model_config()
        training_config = create_training_config(max_iterations=1)  # Just one epoch
        job = create_mock_training_job()
        
        # Act
        trainer = Seq2SeqTSTrainer()
        model = trainer._train_with_validation(
            train_set=train_data,
            val_set=val_data,
            training_config=training_config,
            model_config=model_config,
            job=job
        )
        
        # Assert
        assert isinstance(model, torch.nn.Module), "Should return a model even with one epoch"
        assert len(job.epoch_metrics) == 1, "Should record metrics for the single epoch"
        
        print("Single epoch training test passed")
        
    def test_extreme_batch_sizes(self):
        """Test training with very small and large batch sizes."""
        batch_sizes = [8, 64]  # Small and large, but within allowed values
        
        for batch_size in batch_sizes:
            # Arrange
            train_data, val_data = generate_synthetic_timeseries(
                n_timesets=2,
                n_timesteps=5, 
                n_features=2,
                n_outputs=1
            )
            
            # Configure with extreme batch size
            model_config = create_time_series_model_config({'BATCH_SIZE': batch_size})
            training_config = create_training_config(max_iterations=2)
            job = create_mock_training_job()
            
            try:
                # Act
                trainer = Seq2SeqTSTrainer()
                model = trainer._train_with_validation(
                    train_set=train_data,
                    val_set=val_data,
                    training_config=training_config,
                    model_config=model_config,
                    job=job
                )
                
                # Assert
                assert isinstance(model, torch.nn.Module), f"Training with batch size {batch_size} should complete"
                assert len(job.epoch_metrics) > 0, f"Should record metrics with batch size {batch_size}"
                
                print(f"Extreme batch size {batch_size} test passed")
            except Exception as e:
                # Some extreme batch sizes might legitimately fail
                # Check if this is an expected failure (e.g., batch size larger than dataset)
                if batch_size > len(train_data.arr_x) * len(train_data.arr_x[0]) and "batch size" in str(e).lower():
                    print(f"Batch size {batch_size} failed as expected with: {str(e)}")
                else:
                    raise
    
    def test_handling_empty_validation_set(self):
        """Test behavior when validation set is empty."""
        # Arrange
        train_data, _ = generate_synthetic_timeseries(
            n_timesets=2,
            n_timesteps=5,
            n_features=2,
            n_outputs=1
        )
        
        # Create an empty validation set
        empty_val_data = VODataset(
            uid=uuid.uuid4(),
            arr_x=np.zeros((0, 0, len(train_data.colnames_x))),
            arr_y=np.zeros((0, 0, len(train_data.colnames_y))),
            transformer_uid=train_data.transformer_uid,
            colnames_x=train_data.colnames_x,
            colnames_y=train_data.colnames_y,
            pattern="sequence"
        )
        
        model_config = create_time_series_model_config()
        training_config = create_training_config(max_iterations=2)
        job = create_mock_training_job()
        
        # Act & Assert
        try:
            trainer = Seq2SeqTSTrainer()
            model = trainer._train_with_validation(
                train_set=train_data,
                val_set=empty_val_data,
                training_config=training_config,
                model_config=model_config,
                job=job
            )
            
            # If it doesn't raise an exception:
            assert job.status != EnumTrainingStatus.TRAINING, "Job should not remain in TRAINING state"
            print("Empty validation set handled gracefully")
        except Exception as e:
            # Check if this is a graceful failure with appropriate error message
            assert "empty" in str(e).lower() or "zero" in str(e).lower() or "size" in str(e).lower(), \
                f"Should fail with message about empty dataset, got: {str(e)}"
            print(f"Empty validation set test failed as expected with: {str(e)}")

class TestSeq2SeqTrainerHPO:
    """Tests for hyperparameter optimization with the RNN trainer."""

    def test_run_hpo(self):
        """Test that run_hpo correctly executes hyperparameter optimization."""
        # Arrange
        np.random.seed(42)
        train_data, _ = generate_synthetic_timeseries(
            n_timesets=40,  # Small dataset for faster testing
            n_timesteps=10,
            n_features=2,
            n_outputs=1
        )
        
        # Create configurations
        model_config = create_time_series_model_config()
        training_config = create_training_config(max_iterations=2)
        
        # Create HPO configuration using the Pydantic model directly
        hpo_config = VOConfig_HPO(
            is_enable=True,
            n_trials=2,          # Minimal trials for testing
            sampler="tpe",    # Random sampler is faster for testing
            pruner="median",       # No pruning for simplicity
        )
        
        # Act
        trainer = Seq2SeqTSTrainer()
        result = trainer.run_hpo(
            training_data=train_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Assert
        assert result is not None, "HPO should return a result object"
        assert hasattr(result, 'best_params'), "Result should contain best parameters"
        assert len(result.trials) > 0, "Result should contain completed trials"
        
        # Verify trial data structure
        first_trial = result.trials[0]
        assert hasattr(first_trial, 'trial_id'), "Trial should have ID"
        assert hasattr(first_trial, 'value'), "Trial should have objective value"
        assert hasattr(first_trial, 'params'), "Trial should have parameters"
        assert hasattr(first_trial, 'duration_seconds'), "Trial should record duration"
        
        # Verify runtime tracking
        assert result.runtime_seconds > 0, "Runtime should be tracked"
        
        print(f"HPO completed with best value: {result.best_value}")

    def test_hpo_configurations(self):
        """Test HPO with different configurations (samplers and pruners)."""
        # Arrange
        np.random.seed(42)
        train_data, _ = generate_synthetic_timeseries(
            n_timesets=40,  # Sufficient dataset size to pass split validation
            n_timesteps=10,
            n_features=2,
            n_outputs=1
        )
        
        model_config = create_time_series_model_config()
        training_config = create_training_config(max_iterations=2)
        
        # Define different HPO configurations to test
        configurations = [
            # Test different samplers
            {
                "sampler": "random", 
                "pruner": "none", 
                "n_trials": 2, 
                "description": "Random sampler with no pruning"
            },
            {
                "sampler": "tpe", 
                "pruner": "none", 
                "n_trials": 2, 
                "description": "TPE sampler with no pruning"
            },
            # Test different pruners
            {
                "sampler": "random", 
                "pruner": "median", 
                "n_trials": 2, 
                "description": "Random sampler with median pruning"
            },
            {
                "sampler": "random", 
                "pruner": "hyperband", 
                "n_trials": 2, 
                "description": "Random sampler with hyperband pruning"
            }
        ]
        
        # Act & Assert
        for config in configurations:
            # Create HPO config as proper VOConfig_HPO object
            hpo_config = VOConfig_HPO(
                is_enable=True,
                n_trials=config["n_trials"],
                sampler=config["sampler"],
                pruner=config["pruner"],
                direction="minimize"
            )
            
            trainer = Seq2SeqTSTrainer()
            
            print(f"Testing HPO with {config['description']}...")
            result = trainer.run_hpo(
                training_data=train_data,
                training_config=training_config,
                model_config=model_config,
                hpo_config=hpo_config
            )
            
            # Verify HPO completed successfully
            assert result is not None, f"HPO should complete with {config['description']}"
            assert hasattr(result, 'best_params'), f"Should have best parameters with {config['description']}"
            assert len(result.trials) > 0, f"Should have completed trials with {config['description']}"
            
            print(f"HPO with {config['description']} passed")
    
    def test_hpo_pruning(self):
        """Test that trials are properly pruned during HPO."""
        # Arrange
        np.random.seed(42)
        train_data, _ = generate_synthetic_timeseries(
            n_timesets=40,  # Sufficient dataset size
            n_timesteps=10,
            n_features=2,
            n_outputs=1
        )
        
        model_config = create_time_series_model_config()
        training_config = create_training_config(max_iterations=5)
        
        # Configure aggressive pruning
        hpo_config = VOConfig_HPO(
            is_enable=True,
            n_trials=5,         # Run multiple trials to increase chance of pruning
            sampler="random",   # Random sampler for faster execution
            pruner="hyperband", # Hyperband is aggressive at pruning
            direction="minimize"
        )
        
        # Act
        trainer = Seq2SeqTSTrainer()
        result = trainer.run_hpo(
            training_data=train_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        
        # Assert
        assert result is not None, "HPO should return a result"
        assert hasattr(result, 'trials'), "Result should contain trials information"
        
        # Check if any trials were pruned
        pruned_trials = [
            t
            for t in result.trials
            if t.state == "PRUNED"
        ]
        
        # At least verify the right number of total trials were run
        assert len(pruned_trials) > 0, f"should have some pruned trials"
        
        print("HPO pruning test passed")
    
    def test_hpo_parallel_execution(self):
        """Test HPO with parallel execution using multiple workers."""
        # Arrange
        np.random.seed(42)
        train_data, _ = generate_synthetic_timeseries(
            n_timesets=40,  # Sufficient dataset size
            n_timesteps=10,
            n_features=2,
            n_outputs=1
        )
        
        model_config = create_time_series_model_config()
        training_config = create_training_config(max_iterations=2)  # Keep iterations low for speed
        
        # Configure parallel execution
        n_workers = min(2, max(1, torch.multiprocessing.cpu_count() // 2))  # Use at least 2 workers if possible
        hpo_config = VOConfig_HPO(
            is_enable=True,
            n_trials=4,            # Enough trials to benefit from parallelism
            sampler="random",      # Random sampler works well in parallel
            pruner="none",         # Disable pruning to focus on parallelism
            n_parallel_jobs=n_workers,  # Use multiple workers
            direction="minimize"
        )
        
        # Act
        trainer = Seq2SeqTSTrainer()
        start_time = time.time()
        result = trainer.run_hpo(
            training_data=train_data,
            training_config=training_config,
            model_config=model_config,
            hpo_config=hpo_config
        )
        execution_time = time.time() - start_time
        
        # Assert
        assert result is not None, "HPO should return a result when running in parallel"
        assert len(result.trials) == hpo_config.n_trials, "All trials should complete in parallel mode"
        
        # Print information about parallel execution
        print(f"Parallel HPO with {n_workers} workers completed {len(result.trials)} trials in {execution_time:.2f} seconds")
        
        # Check that the job utilized parallelism (check for optuna multiprocessing artifacts if possible)
        # This is implementation-dependent, so we'll just verify it completed successfully
        
        print("Parallel HPO execution test passed")
    

class TestComputeMethods:
    """Tests for the various '_compute...' methods in the RNN trainer."""
    
    
    def test_compute_model_metrics(self):
        """Test the _compute_model_metrics method with test data."""
        # Arrange
        trainer = Seq2SeqTSTrainer()
        model, train_dataset, val_dataset = TestSeq2SeqTrainerBasics().test_basic_training()

        x = val_dataset.arr_x
        y_true = val_dataset.arr_y
        y_pred = trainer._predict(model, x)
        y_col_names = train_dataset.colnames_y

        # Act
        metrics = trainer._compute_model_metrics(model, x, y_true, train_dataset)

        # Assert
        assert isinstance(metrics, VOLog_ModelMetrics), "Should return VOLog_ModelMetrics instance"

        # Core metrics should be calculated
        assert metrics.rmse is not None, "RMSE should be calculated"
        assert metrics.mae is not None, "MAE should be calculated"
        assert metrics.mse is not None, "MSE should be calculated"

        # Calculate per-variable metrics to match implementation
        expected_mse_values = []
        expected_rmse_values = []
        expected_mae_values = []
        
        # Calculate per-variable metrics
        for i in range(y_true.shape[-1]):
            # Extract single variable columns
            if len(y_true.shape) > 2:  # Handle sequence data case
                var_true = y_true[:, :, i].reshape(-1)
                var_pred = y_pred[:, :, i].reshape(-1)
            else:  # Handle tabular data case
                var_true = y_true[:, i]
                var_pred = y_pred[:, i]
            
            # Calculate metrics for this variable
            var_mse = np.mean((var_true - var_pred) ** 2)
            var_rmse = np.sqrt(var_mse)
            var_mae = np.mean(np.abs(var_true - var_pred))
            
            expected_mse_values.append(var_mse)
            expected_rmse_values.append(var_rmse)
            expected_mae_values.append(var_mae)
        
        # Aggregate metrics (mean across variables)
        expected_mse = np.mean(expected_mse_values)
        expected_rmse = np.mean(expected_rmse_values)
        expected_mae = np.mean(expected_mae_values)

        # Compare with calculated values with tolerance
        assert abs(metrics.mse - expected_mse) < 1e-6, f"MSE calculation should be correct, got {metrics.mse}, expected {expected_mse}"
        assert abs(metrics.rmse - expected_rmse) < 1e-6, f"RMSE calculation should be correct, got {metrics.rmse}, expected {expected_rmse}"
        assert abs(metrics.mae - expected_mae) < 1e-6, f"MAE calculation should be correct, got {metrics.mae}, expected {expected_mae}"
    
    def test_compute_model_feature_importance(self):
        """Test feature importance calculation for RNN models."""
        # Arrange
        trainer = Seq2SeqTSTrainer()
        np.random.seed(42)  # For reproducibility
        
        # Generate synthetic time series where feature_0 is much more important
        train_data, val_data = generate_synthetic_timeseries(
            n_timesets=4,
            n_timesteps=10,
            n_features=3,
            n_outputs=1,
            noise_level=0.05
        )
        
        # Manually increase importance of first feature by making target directly dependent on it
        for i in range(train_data.arr_x.shape[0]):
            for j in range(train_data.arr_x.shape[1]):
                # Make first feature 5x more important than others
                train_data.arr_y[i, j, 0] = 5 * train_data.arr_x[i, j, 0] + \
                                            train_data.arr_x[i, j, 1] + \
                                            train_data.arr_x[i, j, 2] + \
                                            0.1 * np.random.randn()
        
        # Create and train a simple model
        model_config = create_time_series_model_config({'BATCH_SIZE': 8, 'LEARNING_RATE': 0.01})
        
        # Train model briefly to learn the pattern
        training_config = create_training_config(max_iterations=100)
        job = create_mock_training_job()
        trained_model = trainer._train_with_validation(
            train_set=train_data,
            val_set=val_data,
            training_config=training_config,
            model_config=model_config,
            job=job
        )
        
        # Act
        feature_importance = trainer._compute_model_feature_importance(
            model=trained_model, 
            arr_x=val_data.arr_x, 
            arr_y=val_data.arr_y,
            training_data=train_data
        )
        
        # Assert
        assert isinstance(feature_importance, dict), "Should return a dictionary"
        assert set(feature_importance.keys()) == set(train_data.colnames_x), \
            "Should have importance for each feature"
        
        # Check importance values sum to approximately 1.0
        importance_sum = sum(feature_importance.values())
        assert abs(importance_sum - 1.0) < 1e-6, \
            f"Importance should sum to 1.0, got {importance_sum}"
        
        # Verify first feature has highest importance (as we engineered)
        importance_values = list(feature_importance.values())
        most_important_feature = max(feature_importance.items(), key=lambda x: x[1])[0]
        assert most_important_feature == train_data.colnames_x[0], \
            f"First feature should be most important, but got {most_important_feature}"
        assert feature_importance[train_data.colnames_x[0]] > \
               feature_importance[train_data.colnames_x[1]], \
            "First feature should have higher importance than second feature"
        
        print("Feature importance calculation test passed")
    
    def test_compute_model_prediction_summary(self):
        """Test statistical summary generation from model predictions."""
        # Arrange
        trainer = Seq2SeqTSTrainer()
        
        # Create synthetic predictions with known statistical properties
        # Shape: (n_timesets, n_timesteps, n_outputs)
        n_timesets, n_timesteps, n_outputs = 4, 10, 2
        
        # Set random seed for reproducibility
        np.random.seed(42)
        
        # Create true values with known distribution
        y_true = np.random.normal(loc=5.0, scale=2.0, 
                                 size=(n_timesets, n_timesteps, n_outputs))
        
        # Create predictions with systematic bias and noise
        bias = 1.0  # Systematic overprediction
        noise = 0.5  # Random noise
        y_pred = y_true + bias + np.random.normal(0, noise, y_true.shape)
        
        # Create dataset with column names
        colnames_y = [f"target_{i}" for i in range(n_outputs)]
        dummy_dataset = VODataset(
            uid=uuid.uuid4(),
            arr_x=np.zeros((n_timesets, n_timesteps, 1)),  # Dummy X values
            arr_y=y_true,
            transformer_uid=uuid.uuid4(),
            colnames_x=["dummy"],
            colnames_y=colnames_y,
            pattern="sequence"
        )
        
        # Act
        summary = trainer._compute_model_prediction_summary(y_true, y_pred, dummy_dataset)
        
        # Assert
        assert isinstance(summary, dict), "Should return a dictionary"
        
        # Check essential statistics are present
        essential_stats = ["predictions_mean", "predictions_std", "predictions_min",
                          "predictions_max", "residuals_mean", "residuals_std"]
        for stat in essential_stats:
            assert stat in summary, f"Summary should contain {stat}"
        
        # Check quantile statistics
        for q in [10, 25, 50, 75, 90]:
            assert f"predictions_q{q}" in summary, f"Summary should contain predictions_q{q}"
            assert f"residuals_q{q}" in summary, f"Summary should contain residuals_q{q}"
        
        # Verify statistical calculations against manual calculations
        # Reshape arrays to match calculation in the method
        y_true_flat = y_true.reshape(-1)
        y_pred_flat = y_pred.reshape(-1)
        residuals = y_true_flat - y_pred_flat
        
        # Check predictions stats
        np.testing.assert_allclose(summary["predictions_mean"], np.mean(y_pred_flat), rtol=1e-5)
        np.testing.assert_allclose(summary["predictions_std"], np.std(y_pred_flat), rtol=1e-5)
        np.testing.assert_allclose(summary["predictions_min"], np.min(y_pred_flat), rtol=1e-5)
        np.testing.assert_allclose(summary["predictions_max"], np.max(y_pred_flat), rtol=1e-5)
        
        # Check residuals stats
        np.testing.assert_allclose(summary["residuals_mean"], np.mean(residuals), rtol=1e-5)
        np.testing.assert_allclose(summary["residuals_std"], np.std(residuals), rtol=1e-5)
        
        # Check residuals mean is approximately -bias (since pred = true + bias)
        assert abs(summary["residuals_mean"] + bias) < 0.2, \
            f"Mean residual should be approximately -{bias}, got {summary['residuals_mean']}"
        
        # Test with edge case: identical predictions and targets
        edge_summary = trainer._compute_model_prediction_summary(y_true, y_true, dummy_dataset)
        assert edge_summary["residuals_mean"] == 0.0, "Identical arrays should have 0 mean residuals"
        assert edge_summary["residuals_std"] == 0.0, "Identical arrays should have 0 std residuals"
        
        print("Prediction summary calculation test passed")



class TestRNNTrainMethodE2E:
    """End-to-end tests for the Seq2SeqTSTrainer train method."""
    
    def test_e2e_standard_training(self):
        """Test the complete end-to-end training pipeline without HPO."""
        # Arrange
        # 1. Generate synthetic data with enough samples
        train_data, _ = generate_synthetic_timeseries(
            n_timesets=40,  # Ensure enough data for splits
            n_timesteps=10,
            n_features=3,
            n_outputs=1
        )
        
        # 2. Create configurations 
        model_config = create_time_series_model_config()
        training_config = create_training_config(
            max_iterations=5,  # Keep small for testing speed
        )
        
        # 3. Create metadata
        metadata = VOMetadata_General(
            uid=uuid.uuid4(),
            label="Test RNN Model",
            user_reference="user_ref",
            atlas_reference="atlas_ref",
            description="Test end-to-end training",
            surrogate_algo=EnumSurrogateAlgorithm.RNN_TS,
        )
        
        # Act
        trainer = Seq2SeqTSTrainer()
        model, job = trainer.train(
            metadata=metadata,
            training_data=train_data,
            training_config=training_config,
            model_config=model_config
        )
        
        # Assert
        # 1. Job completion and status checks
        assert job is not None, "Job should be returned"
        assert job.status == "COMPLETE", f"Job should complete successfully, got: {job.status}"
        assert job.error_message is None, f"No errors should occur, got: {job.error_message}"
        assert job.runtime_seconds > 0, "Runtime should be tracked"
        
        # 2. Results validation
        assert job.results is not None, "Job should have results"
        assert job.results.test_metrics is not None, "Job should have test metrics"
        assert job.results.feature_importance is not None, "Job should have feature importance"
        
        # 3. Model validation
        assert model is not None, "Model should be returned"
        # Make a prediction to ensure model works
        sample_input = train_data.arr_x[0:1]  # Take first sample
        prediction = trainer._predict(model, sample_input)
        assert prediction is not None, "Model should make predictions"
        assert prediction.shape[0] == 1, "Should predict for one sample"
        
        print(f"E2E standard training test passed. Runtime: {job.runtime_seconds}s")

    def test_e2e_training_with_hpo(self):
        """Test that HPO finds best parameters and updates model config correctly."""
        # Arrange
        train_data, _ = generate_synthetic_timeseries(
            n_timesets=40,
            n_timesteps=10,
            n_features=3,
            n_outputs=1
        )
        
        # Create configurations
        vo_modelconfig = create_time_series_model_config()
        training_config = create_training_config(max_iterations=3)
        
        # Create HPO configuration
        hpo_config = VOConfig_HPO(
            is_enable=True,
            n_trials=2,
            sampler="random",
            pruner="none",
            n_parallel_jobs=1
        )
        
        # Create metadata
        metadata = VOMetadata_General(
            uid=uuid.uuid4(),
            label="Test RNN Model",
            user_reference="user_ref",
            atlas_reference="atlas_ref",
            description="Test end-to-end training",
            surrogate_algo=EnumSurrogateAlgorithm.RNN_TS,
        )
        
        # Store original parameter values for comparison
        original_params = {p.name: p.value for p in vo_modelconfig.parameters}
        
        # Act
        trainer = Seq2SeqTSTrainer()
        model, job = trainer.train(
            metadata=metadata,
            training_data=train_data,
            training_config=training_config,
            model_config=vo_modelconfig,
            hpo_config=hpo_config
        )
        
        # Assert
        # 1. Job completed successfully
        assert job.status == "COMPLETE", f"Job should be complete, got {job.status}"
        assert job.hpo_results is not None, "HPO results should be present"
        
        # 2. Check that model_config was updated with best parameters
        best_params = job.hpo_results.best_params
        assert best_params, "Best parameters should be present"
        
        # Create a dictionary of current parameter values
        current_params = {p.name: p.value for p in job.model_configuration.parameters}
        
        # Compare best parameters with updated model config
        for param_name, best_value in best_params.items():
            assert param_name in current_params, f"Parameter {param_name} missing from model_config"
            assert current_params[param_name] == best_value, (
                f"Parameter {param_name} should be {best_value}, but got {current_params[param_name]}"
            )
        
        # 3. Verify the model works by making predictions
        sample_input = train_data.arr_x[0:1]
        prediction = trainer._predict(model, sample_input)
        assert prediction is not None and prediction.shape[0] == 1, "Model should make valid predictions"
        
        # Log parameter changes for debugging
        logging.info(f"HPO found {len(best_params)} optimized parameters:")
        for param, value in best_params.items():
            orig = original_params.get(param, "N/A")
            logging.info(f"  {param}: {orig} -> {value}")
    
    