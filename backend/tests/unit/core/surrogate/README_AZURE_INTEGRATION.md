# Azure ML Training Template Integration Tests

This directory contains comprehensive integration tests for the AzureML training template implementation, validating the complete end-to-end training workflow with real Azure ML services.

## 🎯 Test Overview

The integration tests validate the complete `submit_training_job` method implementation in the `AzureMLTrainingRunner` class, ensuring:

- **Real Azure ML Integration**: Tests connect to actual Azure ML services (not mocked)
- **Template Method Pattern**: Validates all template hooks are called correctly
- **End-to-End Workflow**: Tests complete job submission, monitoring, and completion
- **Error Handling**: Validates fault tolerance and recovery mechanisms
- **Performance Characteristics**: Ensures reasonable execution times and resource usage

## 🏗️ Architecture Validation

These tests validate the complete hexagonal architecture implementation:

```
┌─────────────────────────────────────────────────────────────┐
│                    Integration Test Scope                   │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────────────────────┐ │
│  │   Core Domain   │    │     Infrastructure Adapter     │ │
│  │                 │    │                                 │ │
│  │ ENTTrainingJob  │◄──►│   AzureMLTrainingRunner        │ │
│  │ VODataset       │    │   (Template Method Pattern)    │ │
│  │ VOConfig_*      │    │                                 │ │
│  └─────────────────┘    │  ┌─────────────────────────────┐ │ │
│                         │  │     Template Hooks         │ │ │
│                         │  │ • _provision_compute       │ │ │
│                         │  │ • _provision_environment   │ │ │
│                         │  │ • _package_context_for_job │ │ │
│                         │  │ • _build_training_command  │ │ │
│                         │  │ • _create_and_submit_job   │ │ │
│                         │  │ • _poll_and_download       │ │ │
│                         │  │ • _depickle_artifacts      │ │ │
│                         │  └─────────────────────────────┘ │ │
│                         └─────────────────────────────────┘ │
│                                        │                    │
│                                        ▼                    │
│                         ┌─────────────────────────────────┐ │
│                         │        Azure ML Services       │ │
│                         │ • Compute Provisioning         │ │
│                         │ • Environment Management       │ │
│                         │ • Job Execution                │ │
│                         │ • Artifact Storage             │ │
│                         └─────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📋 Prerequisites

### Azure ML Workspace Setup

1. **Azure Subscription**: Active Azure subscription with ML workspace
2. **Environment Variables**: Configure the following:
   ```bash
   export AZ_SUBSCRIPTION_ID="your-subscription-id"
   export AZ_RESOURCE_GROUP="your-resource-group"
   export AZ_ML_WORKSPACE="your-workspace-name"
   ```

3. **Azure Authentication**: One of the following:
   ```bash
   # Option 1: Azure CLI login
   az login
   az account set --subscription <subscription-id>
   
   # Option 2: Service Principal (for CI/CD)
   export AZURE_CLIENT_ID="your-client-id"
   export AZURE_CLIENT_SECRET="your-client-secret"
   export AZURE_TENANT_ID="your-tenant-id"
   ```

4. **Compute Quotas**: Ensure sufficient quotas for:
   - Standard_NC16as_T4_v3 (GPU instances)
   - Standard_D8s_v3 (CPU instances)

### Local Development Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Verify Azure ML connectivity
python -c "from azure.ai.ml import MLClient; from azure.identity import DefaultAzureCredential; print('Azure ML SDK ready')"
```

## 🚀 Running the Tests

### Option 1: Complete Test Suite (Recommended)

```bash
cd backend
python tests/unit/core/surrogate/test_azure_template.py
```

This runs all tests with interactive prompts and comprehensive reporting.

### Option 2: Individual Test Methods

```bash
cd backend

# Configuration tests only (fast, ~30 seconds)
python -m pytest tests/unit/core/surrogate/test_azure_template.py::TestAzureMLConfigurationValidation -v

# Complete workflow test (5-15 minutes)
python -m pytest tests/unit/core/surrogate/test_azure_template.py::TestAzureMLTrainingIntegration::test_complete_training_workflow_integration -v

# Template method pattern validation (5-15 minutes)
python -m pytest tests/unit/core/surrogate/test_azure_template.py::TestAzureMLTrainingIntegration::test_template_method_pattern_validation -v

# Error handling test (1-3 minutes)
python -m pytest tests/unit/core/surrogate/test_azure_template.py::TestAzureMLTrainingIntegration::test_error_handling_and_recovery -v

# Performance characteristics (10-25 minutes)
python -m pytest tests/unit/core/surrogate/test_azure_template.py::TestAzureMLTrainingIntegration::test_performance_characteristics -v
```

### Option 3: Demonstration Script

```bash
cd backend
python artefacts/demo_azure_integration_test.py
```

Provides guided execution with environment validation and detailed reporting.

## 📊 Test Details

### TestAzureMLTrainingIntegration

#### `test_complete_training_workflow_integration`
- **Purpose**: End-to-end validation of complete training workflow
- **Duration**: 5-15 minutes
- **Validates**: Job submission → monitoring → completion → artifact retrieval
- **Data**: Realistic sequence data (50 training + 20 test samples)

#### `test_template_method_pattern_validation`
- **Purpose**: Validates template method pattern implementation
- **Duration**: 5-15 minutes  
- **Validates**: Hook execution order, parameter passing, return value usage
- **Monitoring**: Custom runner subclass tracks all hook calls

#### `test_error_handling_and_recovery`
- **Purpose**: Tests fault tolerance and error recovery
- **Duration**: 1-3 minutes
- **Validates**: Infrastructure failure handling, cleanup, error messages
- **Scenarios**: Invalid compute targets, missing environments

#### `test_performance_characteristics`
- **Purpose**: Validates performance under realistic conditions
- **Duration**: 10-25 minutes
- **Validates**: Execution time bounds, resource efficiency, overhead ratios
- **Data**: Larger dataset (200 training + 50 test samples)

### TestAzureMLConfigurationValidation

#### `test_azureml_runner_factory_configuration`
- **Purpose**: Validates factory pattern and default configurations
- **Duration**: < 30 seconds
- **Validates**: Runner creation, dependency injection, configuration values

## 📈 Expected Results

### Success Criteria

✅ **All tests pass** with the following characteristics:
- Complete workflow execution < 30 minutes
- Template hooks called in correct order
- Error handling catches infrastructure failures
- Performance efficiency ratio > 10%
- Factory creates properly configured runners

### Performance Benchmarks

| Test | Expected Duration | Success Criteria |
|------|------------------|------------------|
| Configuration | < 30 seconds | Factory creates valid runner |
| Complete Workflow | 5-15 minutes | Job completes successfully |
| Template Pattern | 5-15 minutes | All hooks called correctly |
| Error Handling | 1-3 minutes | Errors caught and handled |
| Performance | 10-25 minutes | Efficiency > 10% |

### Artifacts Generated

- **Test logs**: Detailed execution logs with timestamps
- **Performance metrics**: Execution times and resource usage
- **Test reports**: JSON reports with validation results
- **Azure ML jobs**: Real training jobs in your workspace

## 🔧 Troubleshooting

### Common Issues

1. **Authentication Failures**
   ```
   Error: DefaultAzureCredential failed to retrieve a token
   Solution: Run 'az login' or configure service principal
   ```

2. **Quota Exceeded**
   ```
   Error: Compute creation failed: quota limits
   Solution: Request quota increase or use smaller instance types
   ```

3. **Workspace Not Found**
   ```
   Error: Workspace 'name' not found
   Solution: Verify AZ_ML_WORKSPACE environment variable
   ```

4. **Long Execution Times**
   ```
   Issue: Tests taking > 60 minutes
   Solution: Check Azure ML queue status, consider different regions
   ```

### Debug Mode

Enable detailed logging:
```bash
export AZURE_LOG_LEVEL=DEBUG
python tests/unit/core/surrogate/test_azure_template.py
```

## 🎯 Integration with CI/CD

For automated testing in CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run Azure ML Integration Tests
  env:
    AZ_SUBSCRIPTION_ID: ${{ secrets.AZ_SUBSCRIPTION_ID }}
    AZ_RESOURCE_GROUP: ${{ secrets.AZ_RESOURCE_GROUP }}
    AZ_ML_WORKSPACE: ${{ secrets.AZ_ML_WORKSPACE }}
    AZURE_CLIENT_ID: ${{ secrets.AZURE_CLIENT_ID }}
    AZURE_CLIENT_SECRET: ${{ secrets.AZURE_CLIENT_SECRET }}
    AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
  run: |
    cd backend
    python -m pytest tests/unit/core/surrogate/test_azure_template.py::TestAzureMLConfigurationValidation -v
    # Run integration tests only on main branch
    if [ "$GITHUB_REF" = "refs/heads/main" ]; then
      python -m pytest tests/unit/core/surrogate/test_azure_template.py::TestAzureMLTrainingIntegration::test_complete_training_workflow_integration -v
    fi
```

## 📚 Related Documentation

- [AzureML Runner Implementation](../../../infrastructure/runners/surrogate_trainers/runner_azureml.py)
- [Training Runner Port Interface](../../../core/_surrogate/ports.py)
- [Code Packaging Documentation](../../../infrastructure/runners/codepackager.py)
- [Azure ML SDK Documentation](https://docs.microsoft.com/en-us/python/api/overview/azure/ml/)

---

**Note**: These are real integration tests that consume Azure resources and may incur costs. Use responsibly and monitor your Azure spending.
