# import sys
# sys.path.append(r'C://Aleph//aleph_graph//')
import logging

import numpy as np
from numpy.testing import assert_almost_equal

from backend.core._athena import Anomaly_Finder


####################
# DEBUGGING
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
####################
X = np.array([[-1.1], [0.2], [101.1], [0.3], [-1.1], [0.2], [1.1], [0.3], [-1.1], [0.2], [1.1], [0.3], [-1.1], [0.2], [1.1], [0.3]])

true_labels = np.array([ 1,  1, -1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1])

def test_outlier_detection():
    outlier_clf = Anomaly_Finder.OutlierDetector()
    predicted_labels = outlier_clf.build_outlier_model(x=X)
    assert_almost_equal(predicted_labels, true_labels), "The returned value from the test does not match expected value."
 
    return predicted_labels

if __name__ == "__main__":
    test_outlier_detection() # Outlier detection test

