import sys
sys.path.append(r'C://Aleph//aleph_graph//')
import logging
import numpy as np
from numpy.testing import assert_array_equal, assert_almost_equal
from backend.core._athena import Economic_Analytics

####################
# DEBUGGING
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
####################
# Names for object instantiation
raw_mat_names = ['RM1', 'RM2', 'RM3']
prd_mat_names = ['PM1', 'PM2', 'PM3']
####################
# Compute Raw Mat Costs using following flows and unit costs
raw_mat_flow = np.array([[10.0, 11.0, 12.0],
                         [9.0, 15.0, 21.0],
                         [12.0, 15.0, 18.0]])

raw_mat_unit_cost = np.array([[1.0, 2.0, 3.0],
                              [4.0, 5.0, 6.0],
                              [7.0, 8.0, 9.0]])
####################
# Compute Product Mat Prices using following flows and unit costs
prd_mat_flow = np.array([[10.0, 11.0, 12.0],
                        [9.0, 15.0, 21.0],
                        [12.0, 15.0, 18.0]])

prd_mat_unit_price = np.array([[3.0, 6.0, 9.0],
                               [12.0, 15.0, 18.0],
                               [21.0, 24.0, 27.0]])
####################
# Compute cost distribution and margins
raw_mat_costs = np.array([[10.0, 22.0, 36.0],
                            [36.0, 75.0, 126.0],
                            [84.0, 120.0, 162.0]])

aggr_utility_costs = np.array([[11.0],
                                [37.50],
                                [60.0]])

prd_mat_price = np.array([[30.0, 66.0, 108.0],
                          [108.0, 225.0, 378.0],
                          [252.0, 360.0, 486.0]])

aggr_carbon_tax = np.array([[1.1],
                            [3.75],
                            [6.0]])

def test_compute_raw_mat_costs():
    eco_analytics = Economic_Analytics.EconomicAnalytics(raw_mat_names=raw_mat_names, prd_mat_names=prd_mat_names)
    raw_mat_costs = eco_analytics.compute_raw_mat_costs(raw_mat_flow=raw_mat_flow, raw_mat_unit_cost=raw_mat_unit_cost)
    expected_raw_mat_costs = [[10.0, 22.0, 36.0],
                              [36.0, 75.0, 126.0],
                              [84.0, 120.0, 162.0]]
    assert raw_mat_costs is not None, "Given raw material flows and/or unit costs do not match raw material names shape."
    assert_array_equal(raw_mat_costs, expected_raw_mat_costs), "The returned value from the test does not match expected value."

def test_compute_prd_mat_prices():
    eco_analytics = Economic_Analytics.EconomicAnalytics(raw_mat_names=raw_mat_names, prd_mat_names=prd_mat_names)
    prd_mat_prices = eco_analytics.compute_prd_mat_prices(prd_mat_flow=prd_mat_flow, prd_mat_unit_price=prd_mat_unit_price)    
    expected_prd_mat_prices = [[30.0, 66.0, 108.0],
                               [108.0, 225.0, 378.0],
                               [252.0, 360.0, 486.0]]
    assert prd_mat_prices is not None, "Given product material flows and/or unit costs do not match product material names shape."
    assert_array_equal(prd_mat_prices, expected_prd_mat_prices), "The returned value from the test does not match expected value."

def test_compute_cost_share():
    eco_analytics = Economic_Analytics.EconomicAnalytics(raw_mat_names=raw_mat_names, prd_mat_names=prd_mat_names)
    share_raw_mat_cost, share_utl_cost, share_carbon_tax, net_prd_margin = eco_analytics.compute_cost_share(raw_mat_costs=raw_mat_costs, aggr_utility_costs=aggr_utility_costs, carbon_tax=aggr_carbon_tax, prd_mat_prices=prd_mat_price)
    expected_share_raw_mat_cost = np.array([[4.90, 10.78, 17.65],
                                            [5.06, 10.55, 17.72],
                                            [7.65, 10.93, 14.75]])
    expected_share_utl_cost = np.array([[5.39],
                                        [5.27],
                                        [5.46]])
    expected_share_carbon_tax = np.array([[0.54],
                                          [0.53],
                                          [0.55]])
    expected_net_prd_margin = np.array([[60.74],
                                        [60.87],
                                        [60.66]])
    assert_almost_equal(share_raw_mat_cost, expected_share_raw_mat_cost, decimal=2), "The returned value from the test does not match expected value."
    assert_almost_equal(share_utl_cost, expected_share_utl_cost, decimal=2), "The returned value from the test does not match expected value."
    assert_almost_equal(share_carbon_tax, expected_share_carbon_tax, decimal=2), "The returned value from the test does not match expected value."
    assert_almost_equal(net_prd_margin, expected_net_prd_margin, decimal=2), "The returned value from the test does not match expected value."


if __name__ == '__main__':
    test_compute_raw_mat_costs() # Test 1: Check Raw Material Cost Computation
    test_compute_prd_mat_prices() # Test 2: Check Product Material Prices    
    test_compute_cost_share() # Test 3: Check Cost Share Computation