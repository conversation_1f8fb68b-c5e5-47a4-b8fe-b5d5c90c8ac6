# import sys
# sys.path.append(r'C://Aleph//aleph_graph//')
import logging

import numpy as np
from numpy.testing import assert_almost_equal

from backend.core._athena import Model_Performance


####################
# DEBUGGING
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
####################
# Data for instantiating model performance object
y_true = np.array([[0.5, 1], [-1, 1], [7, -6]])
y_pred = np.array([[0, 2], [-1, 2], [8, -5]])

def test_compute_r2():
    mod_performance = Model_Performance.ModelPerformance(y_true=y_true, y_pred=y_pred)
    assert mod_performance.y_true is not None, "The model performance object is not instantiated correctly."
    assert mod_performance.y_pred is not None, "The model performance object is not instantiated correctly."
    r2 = mod_performance.compute_r2_score()
    expected_r2 = np.array([0.96543779, 0.90816327])
    assert_almost_equal(r2, expected_r2), "The returned value from the test does not match expected value."
    return r2

def test_compute_mae_score():
    mod_performance = Model_Performance.ModelPerformance(y_true=y_true, y_pred=y_pred)
    assert mod_performance.y_true is not None, "The model performance object is not instantiated correctly."
    assert mod_performance.y_pred is not None, "The model performance object is not instantiated correctly."
    mae_score = mod_performance.compute_mae_score()
    expected_mae_score = np.array([0.5, 1. ])
    assert_almost_equal(mae_score, expected_mae_score), "The returned value from the test does not match expected value."
    return mae_score

def test_compute_mse_score():
    mod_performance = Model_Performance.ModelPerformance(y_true=y_true, y_pred=y_pred)
    assert mod_performance.y_true is not None, "The model performance object is not instantiated correctly."
    assert mod_performance.y_pred is not None, "The model performance object is not instantiated correctly."
    mse_score = mod_performance.compute_mse_score()
    expected_mase_score = [0.41666667, 1.]
    assert_almost_equal(mse_score, expected_mase_score), "The returned value from the test does not match expected value."
    return mse_score

def test_compute_rmse_score():
    mod_performance = Model_Performance.ModelPerformance(y_true=y_true, y_pred=y_pred)
    assert mod_performance.y_true is not None, "The model performance object is not instantiated correctly."
    assert mod_performance.y_pred is not None, "The model performance object is not instantiated correctly."
    rmse_score = mod_performance.compute_rmse_score()
    expected_rmse_score = [0.64549722, 1.]
    assert_almost_equal(rmse_score, expected_rmse_score), "The returned value from the test does not match expected value."
    return rmse_score

def test_compute_mape_score():
    mod_performance = Model_Performance.ModelPerformance(y_true=y_true, y_pred=y_pred)
    assert mod_performance.y_true is not None, "The model performance object is not instantiated correctly."
    assert mod_performance.y_pred is not None, "The model performance object is not instantiated correctly."
    mape_score = mod_performance.compute_mape_score()
    expected_mape_score = 0.5515873015873016
    assert_almost_equal(mape_score, expected_mape_score), "The returned value from the test does not match expected value."
    return mape_score

def test_compute_medae_score():
    mod_performance = Model_Performance.ModelPerformance(y_true=y_true, y_pred=y_pred)
    assert mod_performance.y_true is not None, "The model performance object is not instantiated correctly."
    assert mod_performance.y_pred is not None, "The model performance object is not instantiated correctly."
    medae_score = mod_performance.compute_medae_score()
    expected_medae_score = 0.75
    assert_almost_equal(medae_score, expected_medae_score), "The returned value from the test does not match expected value."
    return medae_score

def test_compute_ev_score():
    mod_performance = Model_Performance.ModelPerformance(y_true=y_true, y_pred=y_pred)
    assert mod_performance.y_true is not None, "The model performance object is not instantiated correctly."
    assert mod_performance.y_pred is not None, "The model performance object is not instantiated correctly."
    ev_score = mod_performance.compute_ev_score()
    expected_ev_score = [0.96774194, 1.]
    assert_almost_equal(ev_score, expected_ev_score), "The returned value from the test does not match expected value."
    return ev_score

def test_compute_all_scores():
    mod_performance = Model_Performance.ModelPerformance(y_true=y_true, y_pred=y_pred)
    assert mod_performance.y_true is not None, "The model performance object is not instantiated correctly."
    assert mod_performance.y_pred is not None, "The model performance object is not instantiated correctly."
    scores = mod_performance.compute_all_scores()
    assert scores is not None, "Compute scores are not returned successfully."


if __name__ == '__main__':
    test_compute_r2() # Test R2 score computation
    test_compute_mae_score() # Test MAE score computation
    test_compute_mse_score() # Test MSE score computation
    test_compute_rmse_score() # Test RMSE score computation
    test_compute_mape_score() # Test MAPE score computation
    test_compute_medae_score() # Test MedAE score computation
    test_compute_ev_score() # Test EV score computation
    test_compute_all_scores() # Test all score computation