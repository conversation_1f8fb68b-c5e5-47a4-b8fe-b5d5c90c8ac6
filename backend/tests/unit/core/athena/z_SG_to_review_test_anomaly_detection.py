import logging

import numpy as np
from numpy.testing import assert_almost_equal
from sklearn.metrics import accuracy_score
from sklearn.model_selection import train_test_split

# from backend.core.athena import Anomaly_Finder
from backend.core._athena import Anomaly_Finder


####################
# DEBUGGING
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
####################


# Dataset for testing anomaly detection
def generate_test_data():
    n_samples, n_outliers = 120, 40
    rng = np.random.RandomState(0)
    covariance = np.array([[0.5, -0.1], [0.7, 0.4]])
    cluster_1 = 0.4 * rng.randn(n_samples, 2) @ covariance + np.array([2, 2])  # general
    cluster_2 = 0.3 * rng.randn(n_samples, 2) + np.array([-2, -2])  # spherical
    outliers = rng.uniform(low=-4, high=4, size=(n_outliers, 2))

    X = np.concatenate([cluster_1, cluster_2, outliers])
    y = np.concatenate(
        [np.ones((2 * n_samples), dtype=int), -np.ones((n_outliers), dtype=int)]
    )

    X_train, X_test, y_train, y_test = train_test_split(
        X, y, stratify=y, random_state=42
    )

    return X_train, X_test, y_train, y_test


def test_anomaly_detection():
    expected_tested_labels = np.array(
        [
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            -1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            -1,
            -1,
            1,
            1,
            1,
            1,
            1,
            -1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            -1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            1,
            -1,
            -1,
            1,
            1,
            1,
            1,
            1,
            1,
        ]
    )

    expected_accuracy = 0.9571428571428572
    X_train, X_test, y_train, y_test = generate_test_data()
    anomaly_clf = Anomaly_Finder.AnomalyDetector(scaling="standardization")
    X_train_scaled_data = anomaly_clf.perform_data_scaling(x=X_train)
    anomaly_clf.build_anomaly_classifier(x=X_train_scaled_data)
    X_test_scaled_data = anomaly_clf.scaler.transform(X_test)
    tested_predicted_labels = anomaly_clf.find_anomalies(x=X_test_scaled_data)
    accuracy = accuracy_score(tested_predicted_labels, y_test)

    assert_almost_equal(
        tested_predicted_labels, expected_tested_labels
    ), "The returned value from the test does not match expected value."
    assert_almost_equal(
        accuracy, expected_accuracy
    ), "The return value from the test does not match expected value."

    return


if __name__ == "__main__":
    test_anomaly_detection()  # anomaly detection testing
