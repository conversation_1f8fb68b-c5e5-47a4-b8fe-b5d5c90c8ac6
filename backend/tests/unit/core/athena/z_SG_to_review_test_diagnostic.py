from backend.core._atlas.aggregates import *
import backend.core._atlas.aggregates as at
from backend.core._athena import DiagnosticMap
import pytest


@pytest.fixture()
def test_kpi_object():
    """
    Get the equipment and variable objects
    """
    # Build Equipment Objects and its variables
    # Heater Bulding
    test_heater = Heater("HT-01")
    test_heater_var1 = test_heater.get_variable(at.ContVarSpecEnum.OutletTemperature)
    test_heater_var2 = test_heater.get_variable(at.ContVarSpecEnum.PressureDrop)
    # Pump Building
    test_pump = Pump("PM01")
    test_pump_var1 = test_pump.get_variable(at.ContVarSpecEnum.OutletPressure)
    test_pump_var2 = test_pump.get_variable(at.ContVarSpecEnum.PowerRequired)
    # Contribution Dictionaries
    test_heater_contribution = {
        test_heater_var1.variable_enum: 6.0,
        test_heater_var2.variable_enum: 40.0,
    }
    test_pump_contribution = {
        test_pump_var1.variable_enum: 19.0,
        test_pump_var2.variable_enum: 25.0,
    }

    return test_heater, test_pump, test_heater_contribution, test_pump_contribution


def test_low_threshold_diagnostics(test_kpi_object):
    # Get the fixtures for diagnostics
    test_heater, test_pump, test_heater_contribution, test_pump_contribution = (
        test_kpi_object
    )

    # Heater Diagnostics
    test_heater_diagnostics = DiagnosticMap.Diagnostics(
        equipment_class=type(test_heater),
        variable_contributions=test_heater_contribution,
        diagnostic_threshold=0.0,
    )
    test_heater_diagnosis_results = test_heater_diagnostics.run

    # Pump Diagnostics
    test_pump_diagnostics = DiagnosticMap.Diagnostics(
        equipment_class=type(test_pump),
        variable_contributions=test_pump_contribution,
        diagnostic_threshold=0.0,
    )
    test_pump_diagnosis_results = test_pump_diagnostics.run

    # Check if the expected results match
    expected_heater_diagnostic_results = {
        ContVarSpecEnum.OutletTemperature: "Effciency of heater, Outlet Temperature, Improper Flow Rate",
        ContVarSpecEnum.PressureDrop: "Loss on Inlet Pressure, Pressure Drop Across Heater",
    }
    expected_pump_diagnostic_results = {
        ContVarSpecEnum.OutletPressure: "Clogged Impellar, Insufficient Suction Pressure (NPSH), Insufficient Suction Volume, Leakage in Piping, Valves, Vessels, Specific Gravity Too High, Inlet Strainer Partially Clogged, Cavitation, Viscosity Too High, Speed To Low, Internal Wear, Total System Head Higher than Design, Noncondensibles in Liquid, Entrained Air (Suction or Seal Leaks)",
        ContVarSpecEnum.PowerRequired: "Clogged Impellar, Misalignment, Electrical Problems (Driver), Specific Gravity Too High, Total System Head Lower than Design, Mechanical Defects, Worn, Rusted, Defective Bearings, Viscosity Too High, Internal Wear, Casing Distorted from Excessive Pipe Strain, Bent Shaft, Speed To High",
    }

    # Check if the results match expected results
    assert (
        test_heater_diagnosis_results.keys()
        == expected_heater_diagnostic_results.keys()
    ), f"The {test_heater_diagnosis_results} is expected to be {expected_heater_diagnostic_results}."
    assert (
        test_pump_diagnosis_results.keys() == expected_pump_diagnostic_results.keys()
    ), f"The {test_pump_diagnosis_results} is expected to be {expected_pump_diagnostic_results}."


def test_med_threshold_diagnostics(test_kpi_object):
    # Get the fixtures for diagnostics
    test_heater, test_pump, test_heater_contribution, test_pump_contribution = (
        test_kpi_object
    )

    # Heater Diagnostics
    test_heater_diagnostics = DiagnosticMap.Diagnostics(
        equipment_class=type(test_heater),
        variable_contributions=test_heater_contribution,
        diagnostic_threshold=10.0,
    )
    test_heater_diagnosis_results = test_heater_diagnostics.run

    # Pump Diagnostics
    test_pump_diagnostics = DiagnosticMap.Diagnostics(
        equipment_class=type(test_pump),
        variable_contributions=test_pump_contribution,
        diagnostic_threshold=10.0,
    )
    test_pump_diagnosis_results = test_pump_diagnostics.run

    # Check if the expected results match
    expected_heater_diagnostic_results = {
        ContVarSpecEnum.PressureDrop: "Loss on Inlet Pressure, Pressure Drop Across Heater",
    }
    expected_pump_diagnostic_results = {
        ContVarSpecEnum.OutletPressure: "Clogged Impellar, Insufficient Suction Pressure (NPSH), Insufficient Suction Volume, Leakage in Piping, Valves, Vessels, Specific Gravity Too High, Inlet Strainer Partially Clogged, Cavitation, Viscosity Too High, Speed To Low, Internal Wear, Total System Head Higher than Design, Noncondensibles in Liquid, Entrained Air (Suction or Seal Leaks)",
        ContVarSpecEnum.PowerRequired: "Clogged Impellar, Misalignment, Electrical Problems (Driver), Specific Gravity Too High, Total System Head Lower than Design, Mechanical Defects, Worn, Rusted, Defective Bearings, Viscosity Too High, Internal Wear, Casing Distorted from Excessive Pipe Strain, Bent Shaft, Speed To High",
    }

    # Check if the results match expected results
    assert (
        test_heater_diagnosis_results.keys()
        == expected_heater_diagnostic_results.keys()
    ), f"The {test_heater_diagnosis_results} is expected to be {expected_heater_diagnostic_results}."
    assert (
        test_pump_diagnosis_results.keys() == expected_pump_diagnostic_results.keys()
    ), f"The {test_pump_diagnosis_results} is expected to be {expected_pump_diagnostic_results}."


def test_high_threshold_diagnostics(test_kpi_object):
    # Get the fixtures for diagnostics
    test_heater, test_pump, test_heater_contribution, test_pump_contribution = (
        test_kpi_object
    )

    # Heater Diagnostics
    test_heater_diagnostics = DiagnosticMap.Diagnostics(
        equipment_class=type(test_heater),
        variable_contributions=test_heater_contribution,
        diagnostic_threshold=20.0,
    )
    test_heater_diagnosis_results = test_heater_diagnostics.run

    # Pump Diagnostics
    test_pump_diagnostics = DiagnosticMap.Diagnostics(
        equipment_class=type(test_pump),
        variable_contributions=test_pump_contribution,
        diagnostic_threshold=20.0,
    )
    test_pump_diagnosis_results = test_pump_diagnostics.run

    # Check if the expected results match
    expected_heater_diagnostic_results = {
        ContVarSpecEnum.PressureDrop: "Loss on Inlet Pressure, Pressure Drop Across Heater",
    }
    expected_pump_diagnostic_results = {
        ContVarSpecEnum.PowerRequired: "Clogged Impellar, Misalignment, Electrical Problems (Driver), Specific Gravity Too High, Total System Head Lower than Design, Mechanical Defects, Worn, Rusted, Defective Bearings, Viscosity Too High, Internal Wear, Casing Distorted from Excessive Pipe Strain, Bent Shaft, Speed To High",
    }

    # Check if the results match expected results
    assert (
        test_heater_diagnosis_results.keys()
        == expected_heater_diagnostic_results.keys()
    ), f"The {test_heater_diagnosis_results} is expected to be {expected_heater_diagnostic_results}."
    assert (
        test_pump_diagnosis_results.keys() == expected_pump_diagnostic_results.keys()
    ), f"The {test_pump_diagnosis_results} is expected to be {expected_pump_diagnostic_results}."
