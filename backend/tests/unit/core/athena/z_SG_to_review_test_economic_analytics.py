import sys
sys.path.append(r'C://Aleph//aleph_graph//')
import numpy as np
from numpy.testing import assert_array_equal, assert_almost_equal
from backend.core._athena import Athena_Analytics

# Unit test for the Economic Analytics Engine
# Test 1: Check Raw Material Cost Computation
def test_compute_raw_mat_costs():
    # Compute Raw Mat Costs using following flows and unit costs
    raw_mat_flow = [[10.0, 11.0, 12.0],
                    [9.0, 15.0, 21.0],
                    [12.0, 15.0, 18.0]]

    raw_mat_unit_cost = [[1.0, 2.0, 3.0],
                         [4.0, 5.0, 6.0],
                         [7.0, 8.0, 9.0]]

    raw_mat_costs = Athena_Analytics.compute_raw_mat_costs(raw_mat_flow=raw_mat_flow, raw_mat_unit_cost=raw_mat_unit_cost)
    expected_raw_mat_costs = [[10.0, 22.0, 36.0],
                              [36.0, 75.0, 126.0],
                              [84.0, 120.0, 162.0]]
    assert_array_equal(raw_mat_costs, expected_raw_mat_costs)

# Test 2: Check Product Material Prices    
def test_compute_prd_mat_prices():
    # Compute Product Mat Prices using following flows and unit costs
    prd_mat_flow = [[10.0, 11.0, 12.0],
                    [9.0, 15.0, 21.0],
                    [12.0, 15.0, 18.0]]

    prd_mat_unit_price = [[3.0, 6.0, 9.0],
                          [12.0, 15.0, 18.0],
                          [21.0, 24.0, 27.0]]
        
    prd_mat_prices = Athena_Analytics.compute_prd_mat_prices(prd_mat_flow=prd_mat_flow, prd_mat_unit_price=prd_mat_unit_price)
    expected_prd_mat_prices = [[30.0, 66.0, 108.0],
                               [108.0, 225.0, 378.0],
                               [252.0, 360.0, 486.0]]

    assert_array_equal(prd_mat_prices, expected_prd_mat_prices)

# Test 3: Check Cost Share Computation 
def test_compute_cost_share():
    raw_mat_costs = np.array([[10.0, 22.0, 36.0],
                              [36.0, 75.0, 126.0],
                              [84.0, 120.0, 162.0]])
    
    aggr_utility_costs = np.array([[11.0],
                                   [37.50],
                                   [60.0]])
    
    aggr_prd_mat_price = np.array([[204.0],
                                   [711.0],
                                   [1098.0]])
    
    aggr_carbon_tax = np.array([[1.1],
                                [3.75],
                                [6.0]])

    # Compute Cost Shares
    share_raw_mat_cost, share_utl_cost, share_carbon_tax = Athena_Analytics.compute_cost_share(raw_mat_costs=raw_mat_costs, aggr_utility_costs=aggr_utility_costs,
                                                                                                               carbon_tax=aggr_carbon_tax, aggr_prd_mat_prices=aggr_prd_mat_price)
    expected_share_raw_mat_cost = np.array([[4.90196078, 10.78431373, 17.64705882],
                                            [5.06329114, 10.54852321, 17.72151899],
                                            [7.65027322, 10.92896175, 14.75409836]])
    expected_share_utl_cost = np.array([[5.39215686],
                                        [5.2742616 ],
                                        [5.46448087]])
    expected_share_carbon_tax = np.array([[0.53921569],
                                          [0.52742616],
                                          [0.54644809]])
    
    assert_almost_equal(share_raw_mat_cost, expected_share_raw_mat_cost)
    assert_almost_equal(share_utl_cost, expected_share_utl_cost)
    assert_almost_equal(share_carbon_tax, expected_share_carbon_tax)


if __name__ == '__main__':
    test_compute_raw_mat_costs()
    test_compute_prd_mat_prices()
    test_compute_cost_share()