"""
Tests for valueobjects.py module in the optimizer package.
"""
from backend.tests._imports import *

from backend.core._optimizer.valueobjects import (
    EnumDirection,
    EnumSearchAlgorithm,
    EnumMetrics,
    EnumParameterSpaceType,
    VOOptimizationConfig,
    VOOptParam,
    VOOptMetric,
    VOSamples,
    VOOptimizationProblem
)

# Helper functions
def create_sample_param(name="test_param", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, 
                       min_max=(0.0, 1.0), 
                       options=None) -> VOOptParam:
    """Helper to create a parameter with default values that can be overridden."""
    if param_type in [EnumParameterSpaceType.FLOAT_UNIFORM, EnumParameterSpaceType.FLOAT_LOG_UNIFORM, 
                     EnumParameterSpaceType.FLOAT_NORMAL, EnumParameterSpaceType.INT_UNIFORM]:
        # For numeric parameters
        return VOOptParam(
            label=name,
            type_=param_type,
            min_max_inclusive=min_max,
            options=None
        )
    else:  # Categorical or boolean
        # For categorical parameters
        return VOOptParam(
            label=name,
            type_=param_type,
            min_max_inclusive=None,
            options=options
        )

def create_sample_metric(name=EnumMetrics.REG_MSE,
                        mode=EnumDirection.MINIMIZE,
                        target_metric=None,
                        target_bounds=None) -> VOOptMetric:
    """Helper to create a metric with default values that can be overridden."""
    return VOOptMetric(
        label=name,
        direction=mode,
        target_metric=target_metric ,
        target_bounds=target_bounds
    )

def create_sample_dataframes(num_rows=5) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """Create sample input and output dataframes for testing."""
    x_df = pd.DataFrame({
        'param1': [i * 0.1 for i in range(num_rows)],
        'param2': [i * 2 for i in range(num_rows)]
    })
    
    y_df = pd.DataFrame({
        'metric1': [i * 0.5 for i in range(num_rows)],
        'metric2': [i * 3 for i in range(num_rows)]
    })
    
    return x_df, y_df

class TestOptimizationConfig:
    """Tests for VOOptimizationConfig class."""
    
    def test_default_values(self):
        """Test default values are set correctly when not specified."""
        # Arrange & Act
        config = VOOptimizationConfig()

        # Assert
        assert config.max_trials == 20, "Default max_trials should be 20"
        assert config.timeout_seconds == 3600, "Default timeout_seconds should be 3600"
        assert config.pruning is True, "Default early_stopping should be True"
        assert config.strategy == EnumSearchAlgorithm.DIRECT, "Default strategy should be DIRECT"
        assert config.max_concurrent_trials == 4, "Default max_concurrent_trials should be 4"
        assert config.resources_per_trial == {"cpu": 1.0}, "Default resources_per_trial should be {'cpu': 1.0}"
        assert config.pruning_grace_iterations == 5, "Default grace_period_iterations should be 5"
        assert config.reduction_factor == 3, "Default reduction_factor should be 3"
        print("Default values test passed")
    
    def test_validation(self):
        """Test validation of configuration parameters."""
        # Arrange & Act & Assert
        
        # Test valid configuration
        valid_config = VOOptimizationConfig(
            max_trials=50,
            timeout_seconds=7200,
            pruning=False,
            strategy=EnumSearchAlgorithm.BAYESIAN,
            max_concurrent_trials=8,
            resources_per_trial={"cpu": 2.0, "gpu": 0.5},
            pruning_grace_iterations=10,
            reduction_factor=4
        )
        assert valid_config.max_trials == 50, "max_trials should be set to 50"
        assert valid_config.strategy == EnumSearchAlgorithm.BAYESIAN, "strategy should be BAYESIAN"
        assert valid_config.pruning_grace_iterations == 10, "grace_period_iterations should be 10"
        
        # Test validation errors with model validator
        with pytest.raises(ValueError):
            VOOptimizationConfig(max_concurrent_trials=0)
        
        with pytest.raises(ValueError):
            VOOptimizationConfig(max_trials=0)
        
        with pytest.raises(ValueError):
            VOOptimizationConfig(timeout_seconds=0)
        
        with pytest.raises(ValueError):
            VOOptimizationConfig(pruning_grace_iterations=0)
        
        with pytest.raises(ValueError):
            VOOptimizationConfig(reduction_factor=1)
        
        # Test resource allocation validation
        with pytest.raises(ValueError):
            VOOptimizationConfig(resources_per_trial={"cpu": 0.0})
        
        print("Validation test passed")
    

class TestOptParam:
    """Tests for VOOptParam class."""
    
    def test_numeric_param_validation(self):
        """Test validation of numeric parameters (int and float)."""
        # Arrange & Act & Assert
        
        # Test float parameter
        float_param = create_sample_param(
            name="learning_rate",
            param_type=EnumParameterSpaceType.FLOAT_LOG_UNIFORM,
            min_max=(0.001, 0.1)
        )
        assert float_param.label == "learning_rate", "Name should be set correctly"
        assert float_param.type_ == EnumParameterSpaceType.FLOAT_LOG_UNIFORM, "Type should be FLOAT_LOG_UNIFORM"
        assert float_param.min_max_inclusive == (0.001, 0.1), "min_max_inclusive should be (0.001, 0.1)"
        assert float_param.options is None, "options should be None for numeric parameters"
        
        # Test int parameter
        int_param = create_sample_param(
            name="num_layers",
            param_type=EnumParameterSpaceType.INT_UNIFORM,
            min_max=(1, 10)
        )
        assert int_param.type_ == EnumParameterSpaceType.INT_UNIFORM, "Type should be INT_UNIFORM"
        assert int_param.min_max_inclusive == (1, 10), "min_max_inclusive should be (1, 10)"
        assert int_param.options is None, "options should be None for numeric parameters"
        
        print("Numeric parameter validation test passed")
    
    def test_categorical_param_validation(self):
        """Test validation of categorical parameters."""
        # Arrange & Act
        categorical_param = create_sample_param(
            name="activation",
            param_type=EnumParameterSpaceType.CATEGORIAL,
            options=["relu", "sigmoid", "tanh"]
        )
        
        # Assert
        assert categorical_param.label == "activation", "Name should be set correctly"
        assert categorical_param.type_ == EnumParameterSpaceType.CATEGORIAL, "Type should be CATEGORIAL"
        assert categorical_param.options == ["relu", "sigmoid", "tanh"], "Options should match provided list"
        assert categorical_param.min_max_inclusive is None, "min_max_inclusive should be None for categorical"
        
        # Test boolean parameter as a special case
        bool_param = create_sample_param(
            name="use_bias",
            param_type=EnumParameterSpaceType.BOOL,
            options=[True, False]
        )
        assert bool_param.type_ == EnumParameterSpaceType.BOOL, "Type should be BOOL"
        assert bool_param.options == [True, False], "Boolean options should be [True, False]"
        assert bool_param.min_max_inclusive is None, "min_max_inclusive should be None for boolean"
        
        print("Categorical parameter validation test passed")
    
    def test_invalid_param_configurations(self):
        """Test invalid parameter configurations are properly caught."""
        # Test missing min/max for numeric types
        with pytest.raises(ValueError):
            VOOptParam(
                label="invalid_float",
                type_=EnumParameterSpaceType.FLOAT_UNIFORM,
                min_max_inclusive=None,
                options=None
            )
        
        # Test missing options for categorical
        with pytest.raises(ValueError):
            VOOptParam(
                label="invalid_categorical",
                type_=EnumParameterSpaceType.CATEGORIAL,
                min_max_inclusive=None,
                options=None
            )
        
        # Test numeric with options (should fail)
        with pytest.raises(ValueError):
            VOOptParam(
                label="invalid_numeric_with_options",
                type_=EnumParameterSpaceType.FLOAT_UNIFORM,
                min_max_inclusive=(0, 1),
                options=["should", "not", "have", "options"]
            )
        
        # Test categorical with min_max (should fail)
        with pytest.raises(ValueError):
            VOOptParam(
                label="invalid_categorical_with_minmax",
                type_=EnumParameterSpaceType.CATEGORIAL,
                min_max_inclusive=(0, 1),
                options=["option1", "option2"]
            )
        
        # Test invalid type
        with pytest.raises(ValidationError):
            VOOptParam(
                label="invalid_type",
                type_="not_a_valid_type", #type: ignore
                min_max_inclusive=(0, 1)
            )
        
        # Test None values in min_max_inclusive for numeric types
        with pytest.raises(ValueError):
            VOOptParam(
                label="invalid_none_minmax",
                type_=EnumParameterSpaceType.FLOAT_UNIFORM,
                min_max_inclusive=(None, 1.0)
            )
        
        with pytest.raises(ValueError):
            VOOptParam(
                label="invalid_none_minmax",
                type_=EnumParameterSpaceType.INT_UNIFORM,
                min_max_inclusive=(1, None)
            )
        
        print("Invalid parameter configuration test passed")
    
    def test_distribution_validation(self):
        """Test validation of parameter distributions."""
        # Test log_uniform with non-positive min_value
        with pytest.raises(ValueError):
            VOOptParam(
                label="invalid_log_param",
                type_=EnumParameterSpaceType.FLOAT_LOG_UNIFORM,
                min_max_inclusive=(0.0, 1.0)
            )
        
        # Test valid log_uniform
        valid_log_param = create_sample_param(
            name="valid_log_param",
            param_type=EnumParameterSpaceType.FLOAT_LOG_UNIFORM,
            min_max=(0.001, 1.0)  # Valid positive value
        )
        assert valid_log_param.min_max_inclusive == (0.001, 1.0), "min_max_inclusive should be (0.001, 1.0)"
        
        print("Distribution validation test passed")


class TestOptMetric:
    """Tests for VOOptMetric class."""
    
    def test_metric_creation(self):
        """Test basic metric creation with enum name."""
        # Arrange & Act
        metric = create_sample_metric(
            name=EnumMetrics.REG_MAE,
            mode=EnumDirection.MINIMIZE
        )

        # Assert
        assert metric.label == EnumMetrics.REG_MAE, "Name should be REG_MAE enum"
        assert metric.direction == EnumDirection.MINIMIZE, "Mode should be MINIMIZE"
        assert metric.target_metric is None, "target_value should be None by default"
        assert metric.target_bounds is None, "target_bounds should be None by default"

        print("Basic metric creation test passed")
    
    def test_metric_with_enum_name(self):
        """Test metric creation with enum name."""
        # Arrange & Act
        metric = create_sample_metric(
            name=EnumMetrics.REG_RMSE,
            mode=EnumDirection.MINIMIZE
        )
        
        # Assert
        assert metric.label == EnumMetrics.REG_RMSE, "Name should be the REG_RMSE enum value"
        
        # Test maximize mode
        max_metric = create_sample_metric(
            name=EnumMetrics.REG_R2_SCORE,
            mode=EnumDirection.MAXIMIZE
        )
        assert max_metric.direction == EnumDirection.MAXIMIZE, "Mode should be MAXIMIZE"
        
        print("Metric with enum name test passed")
    

class TestSamples:
    """Tests for VOSamples class."""
    
    def test_samples_validation(self):
        """Test basic validation of sample data."""
        # Arrange
        x_df, y_df = create_sample_dataframes()
        
        # Act
        samples = VOSamples(
            x_samples=x_df,
            y_samples=y_df
        )
        
        # Assert
        assert samples.x_samples.shape == (5, 2), "x_samples should have 5 rows and 2 columns"
        assert samples.y_samples.shape == (5, 2), "y_samples should have 5 rows and 2 columns"
        assert list(samples.x_samples.columns) == ['param1', 'param2'], "x_samples should have expected columns"
        assert list(samples.y_samples.columns) == ['metric1', 'metric2'], "y_samples should have expected columns"
        
        print("Basic samples validation test passed")
    
    def test_samples_dimension_validation(self):
        """Test validation of sample dimensions."""
        # Arrange
        x_df, y_df = create_sample_dataframes()
        # Create y_df with wrong dimensions
        mismatched_y_df = y_df.iloc[0:3]  # Only first 3 rows
        
        # Act & Assert
        with pytest.raises(ValueError):
            VOSamples(
                x_samples=x_df,
                y_samples=mismatched_y_df
            )
        
        print("Samples dimension validation test passed")
    
    def test_helper_methods(self):
        """Test helper methods get_param_labels and get_metric_labels."""
        # Arrange
        x_df, y_df = create_sample_dataframes()
        samples = VOSamples(
            x_samples=x_df,
            y_samples=y_df
        )
        
        # Act
        param_labels = samples.get_param_labels()
        metric_labels = samples.get_metric_labels()
        
        # Assert
        assert isinstance(param_labels, set), "get_param_labels should return a set"
        assert isinstance(metric_labels, set), "get_metric_labels should return a set"
        assert param_labels == {'param1', 'param2'}, "Parameter labels should match x_samples columns"
        assert metric_labels == {'metric1', 'metric2'}, "Metric labels should match y_samples columns"
        
        # Test with custom column names
        custom_x_df = pd.DataFrame({
            'custom_param1': [1, 2, 3],
            'custom_param2': [4, 5, 6]
        })
        custom_y_df = pd.DataFrame({
            'custom_metric1': [7, 8, 9],
            'custom_metric2': [10, 11, 12]
        })
        custom_samples = VOSamples(
            x_samples=custom_x_df,
            y_samples=custom_y_df
        )
        
        assert custom_samples.get_param_labels() == {'custom_param1', 'custom_param2'}, "Custom parameter labels should match"
        assert custom_samples.get_metric_labels() == {'custom_metric1', 'custom_metric2'}, "Custom metric labels should match"
        
        print("Helper methods test passed")
    
    def test_serialization_to_dict(self):
        """Test serialization of samples to dictionary."""
        # Arrange
        x_df, y_df = create_sample_dataframes()
        samples = VOSamples(
            x_samples=x_df,
            y_samples=y_df
        )
        
        # Act
        samples_dict = samples.to_dict()
        
        # Assert
        assert isinstance(samples_dict, dict), "to_dict should return a dictionary"
        assert "x_samples" in samples_dict, "Dictionary should contain x_samples key"
        assert "y_samples" in samples_dict, "Dictionary should contain y_samples key"
        assert isinstance(samples_dict["x_samples"], list), "x_samples should be a list of records"
        assert len(samples_dict["x_samples"]) == 5, "x_samples should have 5 records"
        assert "param1" in samples_dict["x_samples"][0], "First record should have param1 key"
        
        print("Serialization to dict test passed")
    
    def test_serialization_to_json(self):
        """Test serialization of samples to JSON."""
        # Arrange
        x_df, y_df = create_sample_dataframes()
        samples = VOSamples(
            x_samples=x_df,
            y_samples=y_df
        )
        
        # Act
        json_str = samples.to_json()
        
        # Assert
        assert isinstance(json_str, str), "to_json should return a string"
        
        # Verify the JSON can be parsed back to a dictionary
        parsed_dict = json.loads(json_str)
        assert "x_samples" in parsed_dict, "JSON should contain x_samples key"
        assert "y_samples" in parsed_dict, "JSON should contain y_samples key"
        
        print("Serialization to JSON test passed")
    
    def test_deserialization_from_dict(self):
        """Test deserialization of samples from dictionary."""
        # Arrange
        x_df, y_df = create_sample_dataframes()
        original_samples = VOSamples(
            x_samples=x_df,
            y_samples=y_df
        )
        samples_dict = original_samples.to_dict()
        
        # Act
        deserialized_samples = VOSamples.from_dict(samples_dict)
        
        # Assert
        assert isinstance(deserialized_samples, VOSamples), "from_dict should return a VOSamples instance"
        assert deserialized_samples.x_samples.shape == (5, 2), "Deserialized x_samples should have correct shape"
        assert deserialized_samples.y_samples.shape == (5, 2), "Deserialized y_samples should have correct shape"
        assert list(deserialized_samples.x_samples.columns) == ['param1', 'param2'], "Deserialized x_samples should have expected columns"
        
        # Check values are preserved
        pd.testing.assert_frame_equal(
            deserialized_samples.x_samples.reset_index(drop=True), 
            x_df.reset_index(drop=True)
        )
        
        print("Deserialization from dict test passed")
    
    def test_deserialization_from_json(self):
        """Test deserialization of samples from JSON."""
        # Arrange
        x_df, y_df = create_sample_dataframes()
        original_samples = VOSamples(
            x_samples=x_df,
            y_samples=y_df
        )
        json_str = original_samples.to_json()
        
        # Act
        deserialized_samples = VOSamples.from_json(json_str)
        
        # Assert
        assert isinstance(deserialized_samples, VOSamples), "from_json should return a VOSamples instance"
        assert deserialized_samples.x_samples.shape == (5, 2), "Deserialized x_samples should have correct shape"
        assert deserialized_samples.y_samples.shape == (5, 2), "Deserialized y_samples should have correct shape"
        
        # Test model_validate_json alias
        model_validated = VOSamples.model_validate_json(json_str)
        assert isinstance(model_validated, VOSamples), "model_validate_json should return a VOSamples instance"
        
        print("Deserialization from JSON test passed")


class TestOptimizationProblem:
    """Tests for VOOptimizationProblem class."""
    
    def test_problem_creation(self):
        """Test basic creation of an optimization problem."""
        # Arrange
        params = [
            create_sample_param(name="param1", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, min_max=(0.0, 1.0)),
            create_sample_param(name="param2", param_type=EnumParameterSpaceType.INT_UNIFORM, min_max=(1, 10))
        ]
        metrics = [
            create_sample_metric(name=EnumMetrics.REG_MSE),
            create_sample_metric(name=EnumMetrics.REG_R2_SCORE, mode=EnumDirection.MAXIMIZE)
        ]

        # Act
        problem = VOOptimizationProblem(
            parameters=params,
            metrics=metrics,
            primary_metric=EnumMetrics.REG_MSE
        )

        # Assert
        assert len(problem.parameters) == 2, "Problem should have 2 parameters"
        assert len(problem.metrics) == 2, "Problem should have 2 metrics"
        assert problem.primary_metric == EnumMetrics.REG_MSE, "Primary metric should be REG_MSE"

        # Test accessor methods
        assert problem.get_param_labels() == ["param1", "param2"], "get_param_names should return correct names"
        assert problem.get_metric_labels() == [EnumMetrics.REG_MSE, EnumMetrics.REG_R2_SCORE], "get_metric_names should return correct names"
        
        print("Problem creation test passed")
    
    def test_problem_validation(self):
        """Test comprehensive validation of problem specification."""
        # Arrange
        params = [
            create_sample_param(name="param1", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, min_max=(0.0, 1.0)),
            create_sample_param(name="param2", param_type=EnumParameterSpaceType.CATEGORIAL, options=["option1", "option2"])
        ]
        metrics = [
            create_sample_metric(name=EnumMetrics.REG_MSE),
            create_sample_metric(name=EnumMetrics.REG_R2_SCORE, mode=EnumDirection.MAXIMIZE)
        ]

        # Act & Assert
        # Valid problem should pass validation
        problem = VOOptimizationProblem(
            parameters=params,
            metrics=metrics,
            primary_metric=EnumMetrics.REG_MSE
        )
        
        # Test invalid parameter - missing options for categorical
        with pytest.raises(ValueError):
            VOOptimizationProblem(
                parameters=[
                    create_sample_param(name="param1", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, min_max=(0.0, 1.0)),
                    VOOptParam(label="invalid_param", type_=EnumParameterSpaceType.CATEGORIAL)  # Missing options
                ],
                metrics=metrics,
                primary_metric=EnumMetrics.REG_MSE
            )

        # Test invalid parameter - missing min_max_inclusive for numeric
        with pytest.raises(ValueError):
            VOOptimizationProblem(
                parameters=[
                    create_sample_param(name="param1", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, min_max=(0.0, 1.0)),
                    VOOptParam(label="invalid_param", type_=EnumParameterSpaceType.FLOAT_UNIFORM)  # Missing min_max_inclusive
                ],
                metrics=metrics,
                primary_metric=EnumMetrics.REG_MSE
            )
        
        print("Problem validation test passed")
    
    def test_parameter_access(self):
        """Test parameter and metric access by name."""
        # Arrange
        param1 = create_sample_param(name="param1", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, min_max=(0.0, 1.0))
        param2 = create_sample_param(name="param2", param_type=EnumParameterSpaceType.INT_UNIFORM, min_max=(1, 10))
        metric1 = create_sample_metric(name=EnumMetrics.REG_MSE)
        metric2 = create_sample_metric(name=EnumMetrics.REG_R2_SCORE, mode=EnumDirection.MAXIMIZE)

        problem = VOOptimizationProblem(
            parameters=[param1, param2],
            metrics=[metric1, metric2],
            primary_metric=EnumMetrics.REG_MSE
        )

        # Act
        retrieved_param = problem.get_param("param2")
        retrieved_metric = problem.get_metric(EnumMetrics.REG_R2_SCORE)
        
        # Assert
        assert retrieved_param == param2, "Retrieved parameter should match the original"
        assert retrieved_param.label == "param2", "Retrieved parameter should have name 'param2'"
        assert retrieved_param.type_ == EnumParameterSpaceType.INT_UNIFORM, "Retrieved parameter should be of type 'INT_UNIFORM'"
        
        assert retrieved_metric == metric2, "Retrieved metric should match the original"
        assert retrieved_metric.label == EnumMetrics.REG_R2_SCORE, "Retrieved metric should have name REG_R2_SCORE"
        assert retrieved_metric.direction == EnumDirection.MAXIMIZE, "Retrieved metric should have mode 'MAXIMIZE'"

        # Test non-existent parameter
        with pytest.raises(ValueError):
            problem.get_param("non_existent_param")

        # Test non-existent metric
        with pytest.raises(ValueError):
            problem.get_metric(EnumMetrics.REG_MAE)  # This metric wasn't added to the problem
        
        print("Parameter and metric access test passed")
    
    def test_duplicate_parameter_detection(self):
        """Test detection of duplicate parameter names."""
        # Arrange
        duplicate_params = [
            create_sample_param(name="param1", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, min_max=(0.0, 1.0)),
            create_sample_param(name="param1", param_type=EnumParameterSpaceType.INT_UNIFORM, min_max=(1, 10))  # Duplicate name
        ]
        
        # Act & Assert
        with pytest.raises(ValueError):
            VOOptimizationProblem(
                parameters=duplicate_params,
                metrics=[create_sample_metric(name=EnumMetrics.REG_MSE)],
                primary_metric=EnumMetrics.REG_MSE
            )
        
        print("Duplicate parameter detection test passed")
    
    def test_duplicate_metric_detection(self):
        """Test detection of duplicate metric names."""
        # Arrange
        duplicate_metrics = [
            create_sample_metric(name=EnumMetrics.REG_MSE),
            create_sample_metric(name=EnumMetrics.REG_MSE)  # Duplicate name
        ]

        with pytest.raises(ValueError):
            VOOptimizationProblem(
                parameters=[create_sample_param(name="param1", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, min_max=(0.0, 1.0))],
                metrics=duplicate_metrics,
                primary_metric=EnumMetrics.REG_MSE
            )
        
        print("Duplicate metric detection test passed")
    
    def test_invalid_primary_metric(self):
        """Test validation of primary metric existence."""
        # Arrange
        params = [create_sample_param(name="param1", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, min_max=(0.0, 1.0))]
        metrics = [create_sample_metric(name=EnumMetrics.REG_MSE)]

        # Act & Assert
        # Valid primary metric
        problem = VOOptimizationProblem(
            parameters=params,
            metrics=metrics,
            primary_metric=EnumMetrics.REG_MSE
        )

        # Invalid primary metric
        with pytest.raises(ValueError):
            VOOptimizationProblem(
                parameters=params,
                metrics=metrics,
                primary_metric=EnumMetrics.REG_MAE  # This metric wasn't added to metrics list
            )
        
        print("Invalid primary metric test passed")
    
    def test_sample_validation(self):
        """Test basic problem creation without sample validation (samples are handled separately)."""
        # Arrange
        params = [
            create_sample_param(name="param1", param_type=EnumParameterSpaceType.FLOAT_UNIFORM, min_max=(0.0, 1.0)),
            create_sample_param(name="param2", param_type=EnumParameterSpaceType.INT_UNIFORM, min_max=(1, 10))
        ]
        metrics = [
            create_sample_metric(name=EnumMetrics.REG_MSE),
            create_sample_metric(name=EnumMetrics.REG_R2_SCORE)
        ]

        # Act & Assert
        # Valid problem creation
        problem = VOOptimizationProblem(
            parameters=params,
            metrics=metrics,
            primary_metric=EnumMetrics.REG_MSE
        )

        # Verify problem was created successfully
        assert len(problem.parameters) == 2, "Problem should have 2 parameters"
        assert len(problem.metrics) == 2, "Problem should have 2 metrics"
        assert problem.primary_metric == EnumMetrics.REG_MSE, "Primary metric should be REG_MSE"

        print("Sample validation test passed")


# Simple runner for direct execution
if __name__ == "__main__":
    print("Hello")