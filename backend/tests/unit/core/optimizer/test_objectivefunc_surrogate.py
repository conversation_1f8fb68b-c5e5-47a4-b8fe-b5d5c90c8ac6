'''
# Test Specification for SurrogateObjectiveFunction

## Overview

This test suite focuses on the `SurrogateObjectiveFunction` class, which creates a callable objective function that uses a surrogate model to calculate metrics for optimization. The tests will verify:

1. Correct initialization and configuration
2. Proper metric calculation for various scenarios
3. Handling of time series data and multiple output variables
4. Parameter assembly and integration with optimization problems

## Test Components

### Mock Classes

1. **MockSurrogateModel**: Simple subclass of BaseSurrogateModel that returns predictable outputs
2. **MockDataTransformer**: Simple implementation of a data transformer for both standard and time series data

### Helper Functions

1. **create_mock_surrogate_model**: Creates a surrogate model that returns predefined predictions
2. **create_standard_data**: Creates standard (non-time-series) input and output dataframes
3. **create_time_series_data**: Creates time series data with multiple timesets and timesteps
4. **create_optimization_problem**: Creates a VOOptimizationProblem with specified metrics

### Test Classes

1. **TestSurrogateObjectiveConstructor**: Tests initialization and parameter validation
2. **TestMetricCalculations**: Tests individual metric calculation methods
3. **TestParameterAssembly**: Tests extending input data with optimization parameters
4. **TestObjectiveFunction**: Tests the `__call__` interface and integration with optimization

'''
import uuid
import numpy as np
import pandas as pd
import pytest
from typing import Dict, List, Any, Optional, Union, Tuple
import typing as T
import numpy.typing as npt


from backend.core._optimizer.objectivefuncs.objfunc_surrogate import SurrogateObjectiveFunction
from backend.core._optimizer._enums import EnumMetrics, EnumDirection
from backend.core._optimizer.valueobjects import VOOptimizationProblem, VOOptParam, VOOptMetric
from backend.core._optimizer._enums import EnumParameterSpaceType
from backend.core._surrogate.models.model_base import BaseSurrogateModel
from backend.core._surrogate.transformers.base_transformer import SurrogateDataTransformer, surrogate_datatransformer_factory

##############
# Mock Classes
##############

# class MockDataTransformer(SurrogateDataTransformer):
#     """Mock implementation that inherits from the actual SurrogateDataTransformer."""
    
#     def __init__(
#         self,
#         x_cols: List[str],
#         y_cols: List[str],
#         is_timeseries: bool = False,
#         timeset_col: Optional[str] = None,
#         timestep_col: Optional[str] = None
#     ):
#         # Initialize with minimal requirements, override key properties
#         self._initial_x_variable_cols = x_cols
#         self._initial_y_variable_cols = y_cols
#         self._timeset_col = timeset_col
#         self._timestep_col = timestep_col
#         self.uid = uuid.uuid4()
#         self._custom_is_timeseries = is_timeseries
    
#     @property
#     def is_timeseries(self):
#         return self._custom_is_timeseries

#     def transform(self, df_x: Optional[pd.DataFrame] = None, df_y: Optional[pd.DataFrame] = None) -> Tuple[npt.NDArray, npt.NDArray]:
#         """Simple mock implementation that returns arrays without actual transformation."""
#         x_array = df_x.values if df_x is not None else np.array([])
#         y_array = df_y.values if df_y is not None else np.array([])
#         return x_array, y_array
    
class MockSurrogateModel(BaseSurrogateModel):
    def __init__(
        self,
        datatransformer: SurrogateDataTransformer,
        prediction_func=None
    ):
        # Properly call super constructor to ensure type initialization
        super().__init__(native_model=None, datatransformer=datatransformer)
        self.prediction_func = prediction_func or self._default_prediction

        
    def _default_prediction(self, df: pd.DataFrame) -> pd.DataFrame:
        """Default prediction function that returns a dataframe with y columns."""
        rows = len(df)
        y_cols = self.datatransformer.y_cols
        
        # Create a dataframe with the same columns as y_cols
        result = pd.DataFrame({col: np.ones(rows) for col in y_cols})
        
        # If time series, add the time columns
        if self.datatransformer.is_timeseries:
            if self.datatransformer.timeset_col in df.columns:
                result[self.datatransformer.timeset_col] = df[self.datatransformer.timeset_col]
            if self.datatransformer.timestep_col in df.columns:
                result[self.datatransformer.timestep_col] = df[self.datatransformer.timestep_col]
                
        return result
    
    def predict(self, data: pd.DataFrame, *, fill_missing_timesteps: bool = False) -> pd.DataFrame:
        """Make predictions using the configured prediction function."""
        result = self.prediction_func(data)

        # If fill_missing_timesteps is True and this is a time series model,
        # ensure the result has complete timestep structure
        if fill_missing_timesteps and self.datatransformer.is_timeseries:
            # Use the transformer's fill_missing_timesteps functionality if available
            try:
                # Transform and get complete structure
                _, y_array = self.datatransformer.transform(
                    df_x=None, df_y=result, fill_missing_timesteps=True
                )
                # Convert back to DataFrame
                result = self.datatransformer._reshape_array2df(y_array, is_x=False)
            except Exception:
                # Fallback to original result if transformation fails
                pass

        return result
        
    def serialize_native_model(self) -> str:
        """Mock serialization function."""
        return "mock_serialized_model"
    
    @classmethod
    def deserialize_native_model(cls, data: str):
        """Mock deserialization function."""
        return None


#####################
# Helper Functions
#####################
# Update the create_mock_surrogate_model function to use the factory

def create_mock_surrogate_model(
    x_cols: List[str],
    y_cols: List[str],
    is_timeseries: bool = False,
    timeset_col: Optional[str] = None,
    timestep_col: Optional[str] = None,
    prediction_func=None,
    df_x: Optional[pd.DataFrame] = None,  # Add parameter to pass existing data
    df_y: Optional[pd.DataFrame] = None   # Add parameter to pass existing data
) -> MockSurrogateModel:
    """
    Create a mock surrogate model for testing.
    
    Args:
        x_cols: List of input column names
        y_cols: List of output column names
        is_timeseries: Whether this is a time series model
        timeset_col: Name of the timeset column (for time series)
        timestep_col: Name of the timestep column (for time series)
        prediction_func: Custom function for mock predictions
        df_x: Optional existing X dataframe (will be used to initialize transformer)
        df_y: Optional existing Y dataframe (will be used to initialize transformer)
        
    Returns:
        A mock surrogate model for testing
    """
    # Use the factory to create a properly initialized transformer
    transformer = surrogate_datatransformer_factory(
        x_cols=x_cols,
        y_cols=y_cols,
        is_timeseries=is_timeseries,
        timeset_col=timeset_col,
        timestep_col=timestep_col,
        df_x=df_x,  # Pass the actual data
        df_y=df_y   # Pass the actual data
    )
    
    # Create and return the mock model with the proper transformer
    return MockSurrogateModel(
        datatransformer=transformer,
        prediction_func=prediction_func
    )


def create_standard_data(
    n_samples: int = 10,
    x_cols: T.Optional[List[str]] = None,
    y_cols: T.Optional[ List[str] ] = None
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Create standard (non-time-series) dataframes for testing.
    
    Args:
        n_samples: Number of samples to generate
        x_cols: List of input column names
        y_cols: List of output column names
        
    Returns:
        Tuple of (df_x, df_y) dataframes
    """
    x_cols = x_cols or ["feature1", "feature2", "feature3"]
    y_cols = y_cols or ["target"]
    
    # Create input dataframe with random values
    df_x = pd.DataFrame(
        np.random.rand(n_samples, len(x_cols)),
        columns=x_cols
    )
    
    # Create output dataframe with known relationship to inputs
    df_y_data = np.zeros((n_samples, len(y_cols)))
    for i, _ in enumerate(y_cols):
        if len(x_cols) > 1:
            df_y_data[:, i] = df_x.iloc[:, 0] * 2 + df_x.iloc[:, 1] + np.random.normal(0, 0.1, n_samples)
        else:
            # Handle single column case
            df_y_data[:, i] = df_x.iloc[:, 0] * 3 + np.random.normal(0, 0.1, n_samples)
    
    df_y = pd.DataFrame(df_y_data, columns=y_cols)
    
    return df_x, df_y


def create_time_series_data(
    n_timesets: int = 5,
    n_timesteps: int = 10,
    x_cols: T.Optional[List[str]] = None,
    y_cols: T.Optional[ List[str] ] = None,
    timeset_col: str = "timeset",
    timestep_col: str = "timestep"
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Create time series data for testing.
    
    Args:
        n_timesets: Number of unique time series
        n_timesteps: Number of timesteps per series
        x_cols: List of input column names
        y_cols: List of output column names
        timeset_col: Name of the timeset column
        timestep_col: Name of the timestep column
        
    Returns:
        Tuple of (df_x, df_y) dataframes with time columns
    """
    x_cols = x_cols or ["feature1", "feature2", "feature3"]
    y_cols = y_cols or ["target"]
    
    # Calculate total rows
    total_rows = n_timesets * n_timesteps
    
    # Create time indices
    timesets = np.repeat(np.arange(n_timesets), n_timesteps)
    timesteps = np.tile(np.arange(n_timesteps), n_timesets)
    
    # Create feature values
    x_values = np.random.rand(total_rows, len(x_cols))
    df_x = pd.DataFrame(x_values, columns=x_cols)
    df_x[timeset_col] = timesets
    df_x[timestep_col] = timesteps
    
    # Create target values
    y_values = np.zeros((total_rows, len(y_cols)))
    for i, _ in enumerate(y_cols):
        # Create targets using available columns
        if len(x_cols) > 1:
            y_values[:, i] = (
                x_values[:, 0] * 2 +
                x_values[:, 1] +
                timesteps * 0.1 +
                np.random.normal(0, 0.1, total_rows)
            )
        else:
            # Single column case
            y_values[:, i] = (
                x_values[:, 0] * 3 +
                timesteps * 0.1 +
                np.random.normal(0, 0.1, total_rows)
            )
    
    df_y = pd.DataFrame(y_values, columns=y_cols)
    df_y[timeset_col] = timesets
    df_y[timestep_col] = timesteps
    
    return df_x, df_y


def create_optimization_problem(
    params: List[str],
    metrics: T.Optional[ List[EnumMetrics] ] = None,
    primary_metric: EnumMetrics = EnumMetrics.REG_MSE
) -> VOOptimizationProblem:
    """
    Create an optimization problem for testing.
    
    Args:
        params: List of parameter names
        metrics: List of metrics to compute
        primary_metric: Primary metric to optimize
        
    Returns:
        VOOptimizationProblem instance
    """
    metrics = metrics or [EnumMetrics.REG_MSE]
    
    opt_params = [
        VOOptParam(
            label=param,
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(0.0, 1.0)
        )
        for param in params
    ]
    
    opt_metrics = [
        VOOptMetric(
            label=metric,
            direction=EnumDirection.MINIMIZE
        )
        for metric in metrics
    ]
    
    return VOOptimizationProblem(
        parameters=opt_params,
        metrics=opt_metrics,
        primary_metric=primary_metric
    )


##############
# Tests
##############

class TestSurrogateObjectiveConstructor:
    """Tests for initializing the SurrogateObjectiveFunction correctly."""
    
    def test_basic_initialization(self):
        """Test initializing with valid inputs."""
        # Arrange
        x_cols = ["feature1", "feature2"]
        y_cols = ["target"]
        df_x, df_y = create_standard_data(x_cols=x_cols, y_cols=y_cols)
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols)
        
        # Act
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y
        )
        
        # Assert
        assert obj_func.surrogate_model == surrogate_model
        assert obj_func.df_x_partial.equals(df_x)
        assert obj_func.df_y_true.equals(df_y)
        assert len(obj_func.calculator_registry) > 0
        assert EnumMetrics.REG_MSE in obj_func.calculator_registry
        
        print("Basic initialization test passed")
    
    def test_with_time_series_data(self):
        """Test initialization with time series data."""
        # Arrange
        x_cols = ["feature1", "feature2"]
        y_cols = ["target"]
        timeset_col = "timeset"
        timestep_col = "timestep"
        
        df_x, df_y = create_time_series_data(
            x_cols=x_cols,
            y_cols=y_cols, 
            timeset_col=timeset_col,
            timestep_col=timestep_col
        )
        
        surrogate_model = create_mock_surrogate_model(
            x_cols, y_cols, 
            is_timeseries=True,
            timeset_col=timeset_col,
            timestep_col=timestep_col
        )
        
        # Act
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y
        )
        
        # Assert
        assert obj_func.surrogate_model == surrogate_model
        assert obj_func.df_x_partial.equals(df_x)
        assert obj_func.df_y_true.equals(df_y)
        assert EnumMetrics.TS_MAPE in obj_func.calculator_registry
        
        print("Time series initialization test passed")


class TestMetricCalculations:
    """Tests for individual metric calculation methods."""
    
    def test_mse_calculation(self):
        """Test MSE metric calculation."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        df_x, df_y_true = create_standard_data(x_cols=x_cols, y_cols=y_cols)
        
        # Create a surrogate model that returns predictable values
        def constant_prediction(_):
            # Return predictions that are exactly 1.0 higher than truth
            df_pred = df_y_true.copy()
            df_pred[y_cols[0]] = df_pred[y_cols[0]] + 1.0
            return df_pred
            
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, prediction_func=constant_prediction)
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_true
        )
        
        # Act
        df_y_pred = surrogate_model.predict(df_x)
        mse = obj_func._calculate_mse(df_y_true, df_y_pred)
        
        # Assert
        # Since we add 1.0 to each value, squared error should be 1.0
        assert isinstance(mse, float), "MSE should be a float"
        assert abs(mse - 1.0) < 1e-6, f"MSE should be 1.0, got {mse}"
        
        print("MSE calculation test passed")
    
    def test_rmse_calculation(self):
        """Test RMSE metric calculation."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        df_x, df_y_true = create_standard_data(x_cols=x_cols, y_cols=y_cols)
        
        # Create a surrogate model that returns predictable values
        def constant_prediction(_):
            # Return predictions that are exactly 4.0 higher than truth
            df_pred = df_y_true.copy()
            df_pred[y_cols[0]] = df_pred[y_cols[0]] + 4.0
            return df_pred
            
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, prediction_func=constant_prediction)
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_true
        )
        
        # Act
        df_y_pred = surrogate_model.predict(df_x)
        rmse = obj_func._calculate_rmse(df_y_true, df_y_pred)
        
        # Assert
        # Since we add 4.0 to each value, RMSE should be 4.0
        assert isinstance(rmse, float), "RMSE should be a float"
        assert abs(rmse - 4.0) < 1e-6, f"RMSE should be 4.0, got {rmse}"
        
        print("RMSE calculation test passed")
    
    def test_mae_calculation(self):
        """Test MAE metric calculation."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        df_x, df_y_true = create_standard_data(x_cols=x_cols, y_cols=y_cols)
        
        # Create a surrogate model that returns predictable values
        def constant_prediction(_):
            # Return predictions that are exactly 2.5 higher than truth
            df_pred = df_y_true.copy()
            df_pred[y_cols[0]] = df_pred[y_cols[0]] + 2.5
            return df_pred
            
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, prediction_func=constant_prediction)
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_true
        )
        
        # Act
        df_y_pred = surrogate_model.predict(df_x)
        mae = obj_func._calculate_mae(df_y_true, df_y_pred)
        
        # Assert
        # Since we add 2.5 to each value, MAE should be 2.5
        assert isinstance(mae, float), "MAE should be a float"
        assert abs(mae - 2.5) < 1e-6, f"MAE should be 2.5, got {mae}"
        
        print("MAE calculation test passed")
    
    def test_multi_column_metrics(self):
        """Test metric calculation with multiple output columns."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target1", "target2"]
        df_x, _ = create_standard_data(x_cols=x_cols, y_cols=["dummy"])  # Just need the x data
        
        # Create controlled y data with multiple columns
        df_y_true = pd.DataFrame({
            "target1": [1.0, 2.0, 3.0, 4.0, 5.0],
            "target2": [10.0, 20.0, 30.0, 40.0, 50.0]
        })
        
        # Create predictions with controlled errors
        def multi_column_prediction(_):
            # For target1: error of +1.0, For target2: error of +10.0
            return pd.DataFrame({
                "target1": [2.0, 3.0, 4.0, 5.0, 6.0],  # +1.0 error
                "target2": [20.0, 30.0, 40.0, 50.0, 60.0]  # +10.0 error
            })
            
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, prediction_func=multi_column_prediction)
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_true
        )
        
        # Act
        df_y_pred = surrogate_model.predict(df_x)
        mse = obj_func._calculate_mse(df_y_true, df_y_pred)
        rmse = obj_func._calculate_rmse(df_y_true, df_y_pred)
        mae = obj_func._calculate_mae(df_y_true, df_y_pred)
        
        # Assert
        # For MSE: average of 1^2 and 10^2 = (1 + 100)/2 = 50.5
        # For RMSE: average of sqrt(1^2) and sqrt(10^2) = (1 + 10)/2 = 5.5
        # For MAE: average of 1 and 10 = 5.5
        expected_mse = 50.5
        expected_rmse = 5.5
        expected_mae = 5.5
        
        assert abs(mse - expected_mse) < 1e-6, f"Multi-column MSE should be {expected_mse}, got {mse}"
        assert abs(rmse - expected_rmse) < 1e-6, f"Multi-column RMSE should be {expected_rmse}, got {rmse}"
        assert abs(mae - expected_mae) < 1e-6, f"Multi-column MAE should be {expected_mae}, got {mae}"
        
        print("Multi-column metrics calculation test passed")
    
    def test_mape_calculation(self):
        """Test MAPE metric calculation."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        
        # Create controlled data where truth is 100 and prediction is 110 (10% error)
        df_x = pd.DataFrame({"feature1": [1.0, 2.0, 3.0]})
        df_y_true = pd.DataFrame({"target": [100.0, 100.0, 100.0]})
        
        def mape_prediction(_):
            return pd.DataFrame({"target": [110.0, 110.0, 110.0]})
            
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, prediction_func=mape_prediction)
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_true
        )
        
        # Act
        df_y_pred = surrogate_model.predict(df_x)
        mape = obj_func._calculate_mape(df_y_true, df_y_pred)
        
        # Assert
        # MAPE should be 10% (absolute percentage error)
        expected_mape = 10.0
        assert abs(mape - expected_mape) < 1e-6, f"MAPE should be {expected_mape}, got {mape}"
        
        print("MAPE calculation test passed")
    
    def test_zero_division_handling(self):
        """Test handling of zero division in percentage error metrics."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        
        # Create controlled data with zeros in truth values
        df_x = pd.DataFrame({"feature1": [1.0, 2.0, 3.0]})
        df_y_true = pd.DataFrame({"target": [0.0, 100.0, 0.0]})
        
        def prediction(_):
            return pd.DataFrame({"target": [10.0, 110.0, 10.0]})
            
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, prediction_func=prediction)
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_true
        )
        
        # Act
        df_y_pred = surrogate_model.predict(df_x)
        mape = obj_func._calculate_mape(df_y_true, df_y_pred)
        
        # Assert
        # MAPE should only consider non-zero values (so just the 10% error from 100->110)
        expected_mape = 10.0
        assert abs(mape - expected_mape) < 1e-6, f"MAPE with zeros should be {expected_mape}, got {mape}"
        
        print("Zero division handling test passed")


class TestParameterAssembly:
    """Tests for assembling parameters with partial dataframes."""
    
    def test_parameter_assembly(self):
        """Test extending dataframe with optimization parameters."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        df_x, df_y = create_standard_data(x_cols=x_cols, y_cols=y_cols)
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols)
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y
        )
        
        # Parameters to add
        candidate_params = {
            "param1": 0.5,
            "param2": 0.75
        }
        
        # Act
        extended_df = obj_func.prepare_x_data(candidate_params)
        
        # Assert
        assert "param1" in extended_df.columns, "Parameter param1 should be added to dataframe"
        assert "param2" in extended_df.columns, "Parameter param2 should be added to dataframe"
        
        # Verify values are correct across all rows
        assert np.all(extended_df["param1"] == 0.5), "param1 should have value 0.5 for all rows"
        assert np.all(extended_df["param2"] == 0.75), "param2 should have value 0.75 for all rows"
        
        # Verify original columns are preserved
        assert "feature1" in extended_df.columns, "Original columns should be preserved"
        assert np.all(extended_df["feature1"] == df_x["feature1"]), "Original column values should be unchanged"
        
        print("Parameter assembly test passed")
    
    def test_time_series_parameter_assembly(self):
        """Test parameter assembly with time series data."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        timeset_col = "timeset"
        timestep_col = "timestep"
        
        df_x, df_y = create_time_series_data(
            x_cols=x_cols,
            y_cols=y_cols,
            timeset_col=timeset_col,
            timestep_col=timestep_col
        )
        
        surrogate_model = create_mock_surrogate_model(
            x_cols, y_cols,
            is_timeseries=True,
            timeset_col=timeset_col,
            timestep_col=timestep_col
        )
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y
        )
        
        # Parameters to add
        candidate_params = {
            "param1": 0.5,
            "param2": 0.75
        }
        
        # Act
        extended_df = obj_func.prepare_x_data(candidate_params)
        
        # Assert
        assert "param1" in extended_df.columns, "Parameter param1 should be added to dataframe"
        assert "param2" in extended_df.columns, "Parameter param2 should be added to dataframe"
        
        # Verify time columns are preserved
        assert timeset_col in extended_df.columns, "Timeset column should be preserved"
        assert timestep_col in extended_df.columns, "Timestep column should be preserved"
        
        # Verify time column values are unchanged
        assert np.all(extended_df[timeset_col] == df_x[timeset_col]), "Timeset values should be unchanged"
        assert np.all(extended_df[timestep_col] == df_x[timestep_col]), "Timestep values should be unchanged"
        
        print("Time series parameter assembly test passed")


class TestPrepareYData:
    """Comprehensive tests for the prepare_y_data method and its helper methods."""

    def test_handle_missing_timesteps_non_timeseries(self):
        """Test _handle_missing_timesteps with non-time series data."""
        # Setup
        x_cols = ["feature1"]
        y_cols = ["target"]
        df_x, df_y_true = create_standard_data(x_cols=x_cols, y_cols=y_cols, n_samples=5)
        df_y_pred = df_y_true.copy()

        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, is_timeseries=False)
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_true
        )

        # Execution
        result = obj_func._impute_missing_timesteps(df_y_true, df_y_pred)

        # Assertion
        assert result.equals(df_y_true), "Non-time series data should be returned unchanged"
        assert result is not df_y_true, "Should return a copy, not the original"

        print("Non-time series missing timesteps test passed")

    def test_handle_missing_timesteps_timeseries(self):
        """Test _handle_missing_timesteps with time series data having missing timesteps."""
        # Setup
        x_cols = ["feature1"]
        y_cols = ["target"]
        timeset_col = "timeset"
        timestep_col = "timestep"

        # Create complete time series data
        df_x, df_y_complete = create_time_series_data(
            n_timesets=2, n_timesteps=3,
            x_cols=x_cols, y_cols=y_cols,
            timeset_col=timeset_col, timestep_col=timestep_col
        )

        # Create sparse y_true with missing timesteps
        df_y_sparse = df_y_complete.iloc[[0, 2, 3, 5]].copy()  # Missing timesteps 1 and 4

        surrogate_model = create_mock_surrogate_model(
            x_cols, y_cols, is_timeseries=True,
            timeset_col=timeset_col, timestep_col=timestep_col,
            df_x=df_x, df_y=df_y_complete
        )

        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_sparse
        )

        # Execution
        result = obj_func._impute_missing_timesteps(df_y_sparse, df_y_complete)

        # Assertion
        assert len(result) == len(df_y_complete), "Should have same number of rows as complete data"
        assert set(result.columns) == set(df_y_sparse.columns), "Should have same columns as sparse data"

        # Check that time structure is complete
        time_pairs_result = set(zip(result[timeset_col], result[timestep_col]))
        time_pairs_complete = set(zip(df_y_complete[timeset_col], df_y_complete[timestep_col]))
        assert time_pairs_result == time_pairs_complete, "Should have complete time structure"

        print("Time series missing timesteps test passed")

    def test_handle_missing_timesteps_defensive_check(self):
        """Test defensive check for mismatched timesets in _impute_missing_timesteps."""
        # Setup
        x_cols = ["feature1"]
        y_cols = ["target"]
        timeset_col = "timeset"
        timestep_col = "timestep"

        # Create y_pred with timesets 0, 1
        df_y_pred = pd.DataFrame({
            timeset_col: [0, 0, 1, 1],
            timestep_col: [0, 1, 0, 1],
            "target": [1.0, 2.0, 3.0, 4.0]
        })

        # Create y_true with timeset 2 (not in y_pred)
        df_y_true = pd.DataFrame({
            timeset_col: [2],
            timestep_col: [0],
            "target": [5.0]
        })

        surrogate_model = create_mock_surrogate_model(
            x_cols, y_cols, is_timeseries=True,
            timeset_col=timeset_col, timestep_col=timestep_col
        )

        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=pd.DataFrame({"feature1": [1, 2, 3, 4]}),
            df_y_true=df_y_true
        )

        # Execution & Assertion
        with pytest.raises(ValueError, match="y_true contains timesets .* not present in y_pred"):
            obj_func._impute_missing_timesteps(df_y_true, df_y_pred)

        print("Defensive check for missing timesteps test passed")

    def test_get_null_boolmask(self):
        """Test _get_null_boolmask method."""
        # Setup
        x_cols = ["feature1"]
        y_cols = ["target1", "target2"]

        # Create data with specific NaN pattern
        df_y = pd.DataFrame({
            "timeset": [0, 0, 1, 1],
            "timestep": [0, 1, 0, 1],
            "target1": [1.0, np.nan, 2.0, np.nan],
            "target2": [np.nan, 3.0, np.nan, 4.0]
        })

        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, is_timeseries=True,
                                                    timeset_col="timeset", timestep_col="timestep")
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=pd.DataFrame({"feature1": [1, 2, 3, 4]}),
            df_y_true=df_y
        )

        # Execution
        time_cols = ["timeset", "timestep"]
        mask = obj_func._get_null_boolmask(df_y, time_cols)

        # Assertion
        # Expected boolean mask for non-null positions
        expected_mask = pd.DataFrame({
            "timeset": [False, False, False, False],
            "timestep": [False, False, False, False],
            "target1": [True, False, True, False],
            "target2": [False, True, False, True]
        })

        assert isinstance(mask, pd.DataFrame), "Should return a DataFrame"
        pd.testing.assert_frame_equal(mask, expected_mask)  # Boolean mask should match expected pattern

        # Verify time columns are excluded (set to False)
        assert not mask["timeset"].any(), "Time columns should be False"
        assert not mask["timestep"].any(), "Time columns should be False"

        # Verify feature columns have correct non-null detection
        assert mask.loc[0, "target1"] == True, "target1[0] should be True (non-null)"
        assert mask.loc[1, "target2"] == True, "target2[1] should be True (non-null)"

        print("Get null boolean mask test passed")

    def test_impute_missing_values(self):
        """Test _impute_missing_values method with boolean mask."""
        # Setup
        x_cols = ["feature1"]
        y_cols = ["target"]

        df_y_true = pd.DataFrame({"target": [1.0, np.nan, 3.0]})
        df_y_pred = pd.DataFrame({"target": [10.0, 20.0, 30.0]})

        # Create boolean mask indicating where y_true has non-null values
        non_null_mask = pd.DataFrame({"target": [True, False, True]})

        surrogate_model = create_mock_surrogate_model(x_cols, y_cols)
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=pd.DataFrame({"feature1": [1, 2, 3]}),
            df_y_true=df_y_true
        )

        # Execution
        result = obj_func._impute_missing_values(df_y_true, df_y_pred, non_null_mask)

        # Assertion
        expected = pd.DataFrame({"target": [1.0, 20.0, 3.0]})  # Actual where available, predicted otherwise
        pd.testing.assert_frame_equal(result, expected)

        print("Impute missing values test passed")

    def test_prepare_y_data_integration_standard(self):
        """Test complete prepare_y_data method with standard data."""
        # Setup
        x_cols = ["feature1"]
        y_cols = ["target"]
        df_x, df_y_true = create_standard_data(x_cols=x_cols, y_cols=y_cols, n_samples=5)

        # Create sparse y_true with some NaN values
        df_y_sparse = df_y_true.copy()
        df_y_sparse.iloc[1, 0] = np.nan  # Make one value NaN
        df_y_sparse.iloc[3, 0] = np.nan  # Make another value NaN

        def predictable_prediction(_):
            return df_y_true + 1.0  # Predictions are truth + 1

        surrogate_model = create_mock_surrogate_model(
            x_cols, y_cols, prediction_func=predictable_prediction,
            df_x=df_x, df_y=df_y_true
        )

        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_sparse
        )

        # Execution
        df_y_pred = surrogate_model.predict(df_x)
        y_true_result, y_pred_result = obj_func.prepare_y_data(df_y_sparse, df_y_pred)

        # Assertion
        assert isinstance(y_true_result, pd.Series), "Should return Series for y_true"
        assert isinstance(y_pred_result, pd.Series), "Should return Series for y_pred"
        assert len(y_true_result) == 3, "Should only include non-NaN values (3 out of 5)"
        assert len(y_pred_result) == 3, "Should have same length as y_true"

        print("Prepare Y data integration standard test passed")

    def test_prepare_y_data_integration_timeseries(self):
        """Test complete prepare_y_data method with time series data."""
        # Setup - Use a simpler approach that works with the transformer
        x_cols = ["feature1"]
        y_cols = ["target"]
        timeset_col = "timeset"
        timestep_col = "timestep"

        # Create complete time series data
        df_x, df_y_complete = create_time_series_data(
            n_timesets=2, n_timesteps=3,
            x_cols=x_cols, y_cols=y_cols,
            timeset_col=timeset_col, timestep_col=timestep_col
        )

        # Create sparse y_true by making some values NaN but keeping the same structure
        df_y_sparse = df_y_complete.copy()
        df_y_sparse.iloc[1, 0] = np.nan  # Make one target value NaN
        df_y_sparse.iloc[3, 0] = np.nan  # Make another target value NaN

        def controlled_prediction(_):
            return df_y_complete.copy()

        surrogate_model = create_mock_surrogate_model(
            x_cols, y_cols, is_timeseries=True,
            timeset_col=timeset_col, timestep_col=timestep_col,
            prediction_func=controlled_prediction,
            df_x=df_x, df_y=df_y_complete
        )

        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_sparse
        )

        # Execution
        y_true_result, y_pred_result = obj_func.prepare_y_data(df_y_sparse, df_y_complete)

        # Assertion
        assert isinstance(y_true_result, pd.Series), "Should return Series for y_true"
        assert isinstance(y_pred_result, pd.Series), "Should return Series for y_pred"
        # With the new implementation, missing timesteps are filled with NaN,
        # so we only get non-null values (4 out of 6 total: 6 total - 2 NaN values)
        assert len(y_true_result) == 4, "Should only include non-NaN values (4 out of 6 total)"
        assert len(y_pred_result) == 4, "Should have same length as y_true"

        print("Prepare Y data integration time series test passed")

    def test_prepare_y_data_shape_consistency(self):
        """Test that prepare_y_data maintains shape consistency as per SurrogateDataTransformer requirements."""
        # Setup
        x_cols = ["feature1", "feature2"]
        y_cols = ["target1", "target2"]
        df_x, df_y_true = create_standard_data(x_cols=x_cols, y_cols=y_cols, n_samples=10)

        # Create sparse y_true with various NaN patterns
        df_y_sparse = df_y_true.copy()
        df_y_sparse.iloc[1, 0] = np.nan  # target1 NaN
        df_y_sparse.iloc[2, 1] = np.nan  # target2 NaN
        df_y_sparse.iloc[5, :] = np.nan  # Both targets NaN

        def identity_prediction(_):
            return df_y_true.copy()  # Return original values as predictions

        surrogate_model = create_mock_surrogate_model(
            x_cols, y_cols, prediction_func=identity_prediction,
            df_x=df_x, df_y=df_y_true
        )

        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_sparse
        )

        # Execution
        df_y_pred = surrogate_model.predict(df_x)
        y_true_result, y_pred_result = obj_func.prepare_y_data(df_y_sparse, df_y_pred)

        # Assertion - Shape consistency
        assert len(y_true_result) == len(y_pred_result), "True and predicted should have same length"
        assert len(y_true_result) > 0, "Should have some valid data points"

        # Count actual non-NaN values: 10*2 total values - 4 NaN values (1+1+2) = 16
        # Row 1: target1 NaN (1 NaN), Row 2: target2 NaN (1 NaN), Row 5: both NaN (2 NaN)
        expected_length = 16
        assert len(y_true_result) == expected_length, f"Expected {expected_length} values, got {len(y_true_result)}"

        print("Prepare Y data shape consistency test passed")

    def test_prepare_y_data_error_handling(self):
        """Test error handling in prepare_y_data method."""
        # Setup
        x_cols = ["feature1"]
        y_cols = ["target"]
        df_x, df_y_true = create_standard_data(x_cols=x_cols, y_cols=y_cols, n_samples=5)

        # Create mismatched prediction function that returns wrong shape
        def mismatched_prediction(_):
            return pd.DataFrame({"target": [1.0, 2.0]})  # Wrong number of rows

        surrogate_model = create_mock_surrogate_model(
            x_cols, y_cols, prediction_func=mismatched_prediction,
            df_x=df_x, df_y=df_y_true
        )

        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_true
        )

        # Execution & Assertion
        df_y_pred = surrogate_model.predict(df_x)

        # This should handle the shape mismatch gracefully or raise appropriate error
        try:
            y_true_result, _ = obj_func.prepare_y_data(df_y_true, df_y_pred)
            # If no error, verify the results are reasonable
            assert len(y_true_result) >= 0, "Should handle mismatched shapes gracefully"
        except ValueError as e:
            # Expected behavior - should raise ValueError for shape mismatch
            assert "shape" in str(e).lower(), "Error should mention shape mismatch"

        print("Prepare Y data error handling test passed")


class TestObjectiveFunction:
    """Tests for the complete objective function callable interface."""
    
    def test_call_interface(self):
        """Test the __call__ interface with optimization parameters."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        df_x, df_y = create_standard_data(x_cols=x_cols, y_cols=y_cols)
        
        # Create a surrogate model with predictable results
        def constant_prediction(_):
            # Return predictions with MSE of 1.0
            df_pred = df_y.copy()
            df_pred[y_cols[0]] = df_pred[y_cols[0]] + 1.0
            return df_pred
            
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, prediction_func=constant_prediction)
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y
        )
        
        # Create optimization problem
        opt_problem = create_optimization_problem(
            params=["param1", "param2"],
            metrics=[EnumMetrics.REG_MSE, EnumMetrics.REG_RMSE]
        )
        
        # Parameters to optimize
        parameters = {
            "param1": 0.5,
            "param2": 0.75
        }
        
        # Act
        result = obj_func(parameters, opt_problem)
        
        # Assert
        assert isinstance(result, dict), "Result should be a dictionary"
        assert EnumMetrics.REG_MSE in result, "Result should contain MSE metric"
        assert EnumMetrics.REG_RMSE in result, "Result should contain RMSE metric"
        
        # Verify expected values (MSE = 1.0, RMSE = 1.0)
        assert abs(result[EnumMetrics.REG_MSE] - 1.0) < 1e-6, "MSE should be 1.0"
        assert abs(result[EnumMetrics.REG_RMSE] - 1.0) < 1e-6, "RMSE should be 1.0"
        
        print("Objective function call interface test passed")
    
    def test_integration_with_multiple_metrics(self):
        """Test the objective function with multiple metrics."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        df_x, df_y = create_standard_data(x_cols=x_cols, y_cols=y_cols)
        
        # Create a surrogate model with predictable results for different metrics
        def consistent_error_prediction(_):
            # Return predictions with constant error for consistent metric values
            df_pred = df_y.copy()
            df_pred[y_cols[0]] = df_pred[y_cols[0]] + 2.0  # MSE=4, MAE=2, RMSE=2
            return df_pred
            
        surrogate_model = create_mock_surrogate_model(x_cols, y_cols, prediction_func=consistent_error_prediction)
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y
        )
        
        # Create optimization problem with multiple metrics
        opt_problem = create_optimization_problem(
            params=["param1"],
            metrics=[EnumMetrics.REG_MSE, EnumMetrics.REG_MAE, EnumMetrics.REG_RMSE]
        )
        
        # Parameters to optimize
        parameters = {"param1": 0.5}
        
        # Act
        result = obj_func(parameters, opt_problem)
        
        # Assert
        assert EnumMetrics.REG_MSE in result, "Result should contain MSE metric"
        assert EnumMetrics.REG_MAE in result, "Result should contain MAE metric"
        assert EnumMetrics.REG_RMSE in result, "Result should contain RMSE metric"
        
        # Verify expected values
        assert abs(result[EnumMetrics.REG_MSE] - 4.0) < 1e-6, "MSE should be 4.0"
        assert abs(result[EnumMetrics.REG_MAE] - 2.0) < 1e-6, "MAE should be 2.0"
        assert abs(result[EnumMetrics.REG_RMSE] - 2.0) < 1e-6, "RMSE should be 2.0"
        
        print("Integration with multiple metrics test passed")
    
    def test_time_series_metrics(self):
        """Test time series specific metrics in the objective function."""
        # Arrange
        x_cols = ["feature1"]
        y_cols = ["target"]
        timeset_col = "timeset"
        timestep_col = "timestep"
        
        df_x, df_y_true = create_time_series_data(
            x_cols=x_cols,
            y_cols=y_cols,
            timeset_col=timeset_col,
            timestep_col=timestep_col
        )
        
        # Create a prediction function with 10% error for MAPE testing
        def percentage_error_prediction(_):
            df_pred = df_y_true.copy()
            df_pred[y_cols[0]] = df_pred[y_cols[0]] * 1.1  # 10% error
            return df_pred

        surrogate_model = create_mock_surrogate_model(
            x_cols, y_cols,
            is_timeseries=True,
            timeset_col=timeset_col,
            timestep_col=timestep_col,
            prediction_func=percentage_error_prediction,
            df_x=df_x,            # Pass the actual test data
            df_y=df_y_true        # Pass the actual test data
        )
            
        
        obj_func = SurrogateObjectiveFunction(
            surrogate_model=surrogate_model,
            df_x_partial=df_x,
            df_y_true=df_y_true
        )
        
        # Create optimization problem with time series metric
        opt_problem = create_optimization_problem(
            params=["param1"],
            metrics=[EnumMetrics.TS_MAPE],
            primary_metric=EnumMetrics.TS_MAPE
        )
        
        # Parameters to optimize
        parameters = {"param1": 0.5}
        
        # Act
        result = obj_func(parameters, opt_problem)
        
        # Assert
        assert EnumMetrics.TS_MAPE in result, "Result should contain TS_MAPE metric"
        
        # Verify expected MAPE (should be approximately 10%)
        assert abs(result[EnumMetrics.TS_MAPE] - 10.0) < 1.0, f"TS_MAPE should be approximately 10%, got {result[EnumMetrics.TS_MAPE]}"
        
        print("Time series metrics test passed")



if __name__ == "__main__":
    print("ready for testing")