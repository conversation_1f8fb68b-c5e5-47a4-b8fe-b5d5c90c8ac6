'''
# Test Specification for ScipyDirectOptimizationRunner

## Overview

This test suite evaluates the `ScipyDirectOptimizationRunner` class, which implements the DIRECT (DIviding RECTangles) optimization algorithm using SciPy. The tests verify:

1. Correct initialization and configuration
2. Parameter handling for various parameter types
3. Objective function wrapping for both minimization and maximization
4. Optimization execution and result handling
5. Error conditions and recovery mechanisms
6. Integration with the BaseOptimizationRunner abstract base class

## Test Components

### Mock Classes

1. **MockObjectiveFunction**: Simple implementation of `BaseObjectiveFunction` with controlled outputs
2. **SimpleQuadraticObjective**: Objective function with known minimum/maximum for testing convergence

### Helper Functions

1. **create_optimization_config**: Creates a standard optimization configuration
2. **create_optimization_problem**: Creates problem specification with parameters and metrics
3. **create_metadata**: Creates ENTMetadata for optimization jobs
4. **verify_optimization_result**: Validates optimization result structure and values

### Test Classes

#### TestInitialization
Tests for proper initialization and configuration handling:
- `test_create_from_config`: Verify constructor and config-based initialization
- `test_random_seed_setting`: Verify reproducibility with fixed seeds

#### TestParameterHandling
Tests for parameter space handling:
- `test_get_param_bounds_numeric`: Test bounds extraction for numeric parameters
- `test_get_param_bounds_error`: Test error handling for incompatible parameter types
- `test_get_param_map`: Test creation of parameter mappings

#### TestObjectiveWrapping
Tests for proper objective function wrapping:
- `test_wrap_minimize_objective`: Test wrapping an objective to be minimized
- `test_wrap_maximize_objective`: Test wrapping an objective to be maximized
- `test_objective_parameter_mapping`: Test parameter mapping between array and dictionary formats

#### TestTrialHandling
Tests for trial record creation and management:
- `test_create_trial`: Test creation of standardized trial records
- `test_trial_metrics_conversion`: Test metric conversion in trials

#### TestOptimizationExecution
Tests for running the optimization process:
- `test_simple_optimization`: Test optimization with a simple quadratic function
- `test_bounded_optimization`: Test optimization with specific parameter bounds
- `test_callback_execution`: Test callback function is called for each trial
- `test_max_trials_limit`: Test respecting the maximum trial count limit
- `test_optimization_convergence`: Test convergence to known solutions
- `test_multi_parameter_optimization`: Test optimization with multiple parameters with different scales
- `test_optimization_extreme_scale_differences`: Test optimization with parameters that have extremely different scales

#### TestErrorHandling
Tests for error conditions and recovery:
- `test_objective_function_error`: Test handling errors in the objective function
- `test_partial_optimization_results`: Test getting partial results when optimization fails
- `test_stop_method`: Test behavior of the stop method

#### TestIntegrationWithBase
Tests for proper integration with the base class:
- `test_base_class_implementation`: Test implementation of all abstract methods
- `test_objective_function_validation`: Test validation of objective functions

## Example Tests

The test suite will include examples like:

1. Optimizing simple analytical functions (quadratic) with known optima
2. Testing with tight bounds vs wide bounds to validate global search capability
3. Testing with multiple parameters to evaluate high-dimensional search
4. Verifying trial recording and result aggregation
5. Testing error handling and recovery mechanisms
6. Confirming that maximization objectives are properly negated for SciPy's minimizer

## Validation Criteria

Each test will verify:
1. Correct functionality according to class specifications
2. Proper integration with SciPy's DIRECT algorithm
3. Adherence to the BaseOptimizationRunner interface
4. Appropriate error handling and recovery
5. Performance within expected bounds for simple test functions
'''
import uuid
import time
import numpy as np
import pandas as pd
import pytest
import inspect
from typing import Dict, List, Any, Optional, Union, Tuple
import typing as T
from scipy.optimize import direct

from backend.core._optimizer.optimizers.optimizer_scipy_direct import ScipyDirectOptimizationRunner
from backend.core._optimizer.objectivefuncs.objfunc_base import BaseObjectiveFunction
from backend.core._optimizer._enums import EnumMetrics, EnumDirection, EnumTrialStatus, EnumSearchAlgorithm
from backend.core._optimizer.valueobjects import (
    VOOptimizationConfig, VOOptimizationProblem, VOOptParam, VOOptMetric
)
from backend.core._optimizer._enums import EnumParameterSpaceType
from backend.core._optimizer.entities import ENTMetadata, ENTTrial, ENTResult

##############
# Mock Classes
##############

class MockObjectiveFunction(BaseObjectiveFunction):
    """Simple implementation of BaseObjectiveFunction for testing."""
    
    def __init__(self, return_values=None, raise_error=False):
        """
        Initialize with optional predefined return values or error flag.
        
        Args:
            return_values: Optional dictionary to return from calls
            raise_error: If True, raises an exception when called
        """
        self.return_values = return_values or {EnumMetrics.REG_MSE: 1.0}
        self.raise_error = raise_error
        self.call_count = 0
        self.call_parameters = []
    
    def __call__(self, parameters: Dict[str, Any], problem_spec: VOOptimizationProblem) -> Dict[EnumMetrics, float]:
        """
        Simple implementation that returns predefined values or raises an error.
        
        Args:
            parameters: Dictionary of parameter values
            problem_spec: The optimization problem specification
            
        Returns:
            Dictionary of metric values
            
        Raises:
            RuntimeError: If raise_error is True
        """
        if self.raise_error:
            raise RuntimeError("Simulated error in objective function")
        
        self.call_count += 1
        self.call_parameters.append(parameters.copy())
        
        return self.return_values


class SimpleQuadraticObjective(BaseObjectiveFunction):
    """
    Objective function implementing a simple quadratic function f(x) = a(x - b)^2 + c
    with known minimum/maximum at x = b.
    
    This function can be used to test whether the optimizer correctly finds the minimum/maximum.
    """
    
    def __init__(self, param_name: str = "x", a: float = 1.0, b: float = 0.5, c: float = 0.0):
        """
        Initialize with parameters of the quadratic function.
        
        Args:
            param_name: Name of the parameter to optimize
            a: Coefficient of the quadratic term (controls steepness)
            b: Position of the minimum/maximum
            c: Constant term (vertical offset)
        """
        self.param_name = param_name
        self.a = a  # Coefficient of quadratic term (positive for minimum, negative for maximum)
        self.b = b  # Position of minimum/maximum
        self.c = c  # Constant term
        self.call_count = 0
        self.call_parameters = []
    
    def __call__(self, parameters: Dict[str, Any], problem_spec: VOOptimizationProblem) -> Dict[EnumMetrics, float]:
        """
        Calculate the quadratic function value for the given parameters.
        
        Args:
            parameters: Dictionary of parameter values
            problem_spec: The optimization problem specification
            
        Returns:
            Dictionary with the function value as a metric
        """
        self.call_count += 1
        self.call_parameters.append(parameters.copy())
        
        # Extract the parameter value
        x = parameters[self.param_name]
        
        # Calculate the quadratic function value
        value = self.a * (x - self.b)**2 + self.c
        
        # Return the value using the primary metric defined in the problem
        primary_metric = problem_spec.primary_metric
        return {primary_metric: value}


class MultiParameterQuadraticObjective(BaseObjectiveFunction):
    """
    Multi-parameter quadratic function for testing optimization with multiple parameters.
    
    Implements f(x1, x2, ..., xn) = sum(a_i * (x_i - b_i)^2) + c
    with known minimum at x_i = b_i for each parameter.
    """
    
    def __init__(self, param_coefficients: Dict[str, Tuple[float, float]], c: float = 0.0):
        """
        Initialize with parameters of the quadratic function.
        
        Args:
            param_coefficients: Dictionary mapping parameter names to tuples of (a, b) where:
                                a = coefficient of quadratic term
                                b = position of minimum for this parameter
            c: Constant term (vertical offset)
        """
        self.param_coefficients = param_coefficients
        self.c = c
        self.call_count = 0
        self.call_parameters = []
        
    def __call__(self, parameters: Dict[str, Any], problem_spec: VOOptimizationProblem) -> Dict[EnumMetrics, float]:
        """
        Calculate the multi-parameter quadratic function value.
        
        Args:
            parameters: Dictionary of parameter values
            problem_spec: The optimization problem specification
            
        Returns:
            Dictionary with the function value as a metric
        """
        self.call_count += 1
        self.call_parameters.append(parameters.copy())
        
        # Calculate the sum of squared terms
        value = self.c
        for param_name, (a, b) in self.param_coefficients.items():
            if param_name in parameters:
                x = parameters[param_name]
                value += a * (x - b)**2
        
        # Return the value using the primary metric defined in the problem
        primary_metric = problem_spec.primary_metric
        return {primary_metric: value}


#####################
# Helper Functions
#####################

def create_optimization_config(
    max_trials: int = 10,
    strategy: EnumSearchAlgorithm = EnumSearchAlgorithm.DIRECT,
    random_seed: int = 42
) -> VOOptimizationConfig:
    """
    Create a standard optimization configuration for testing.
    
    Args:
        max_trials: Maximum number of trials to run
        strategy: Search algorithm to use
        random_seed: Random seed for reproducibility
        
    Returns:
        VOOptimizationConfig instance
    """
    return VOOptimizationConfig(
        max_trials=max_trials,
        max_concurrent_trials=1,  # DIRECT is sequential
        timeout_seconds=60,  # Short timeout for tests
        strategy=strategy,
        random_seed=random_seed
    )


def create_optimization_problem(
    param_names: Optional[List[str]] = None,
    param_bounds: Optional[List[Tuple[float, float]]] = None,
    metrics: Optional[List[EnumMetrics]] = None,
    primary_metric: EnumMetrics = EnumMetrics.REG_MSE,
    maximize: bool = False
) -> VOOptimizationProblem:
    """
    Create an optimization problem for testing.
    
    Args:
        param_names: List of parameter names (defaults to ["x"])
        param_bounds: List of (min, max) bounds for each parameter (defaults to [(0.0, 1.0)])
        metrics: List of metrics to compute (defaults to [EnumMetrics.REG_MSE])
        primary_metric: Primary metric to optimize
        maximize: Whether to maximize (True) or minimize (False) the primary metric
        
    Returns:
        VOOptimizationProblem instance
    """
    param_names = param_names or ["x"]
    param_bounds = param_bounds or [(0.0, 1.0)] * len(param_names)
    metrics = metrics or [EnumMetrics.REG_MSE]
    
    opt_params = [
        VOOptParam(
            label=name,
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=bounds
        )
        for name, bounds in zip(param_names, param_bounds)
    ]
    
    direction = EnumDirection.MAXIMIZE if maximize else EnumDirection.MINIMIZE
    
    opt_metrics = [
        VOOptMetric(
            label=metric,
            direction=direction if metric == primary_metric else EnumDirection.MINIMIZE
        )
        for metric in metrics
    ]
    
    return VOOptimizationProblem(
        parameters=opt_params,
        metrics=opt_metrics,
        primary_metric=primary_metric
    )


def create_metadata(
    label: str = "Test Optimization",
    description: Optional[str] = "Test optimization run"
) -> ENTMetadata:
    """
    Create metadata for optimization jobs.
    
    Args:
        label: Label for the optimization job
        description: Description of the optimization job
        
    Returns:
        ENTMetadata instance
    """
    return ENTMetadata(
        label=label,
        description=description,
        uid=uuid.uuid4()
    )


def verify_optimization_result(
    result: ENTResult, 
    expected_trials: int,
    has_best_result: bool = True,
    expected_best: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Verify that an optimization result has the expected structure and content.
    
    Args:
        result: The optimization result to verify
        expected_trials: Expected number of completed trials
        has_best_result: Whether a best result is expected
        expected_best: Optional dictionary with expected best parameter values
        
    Returns:
        True if verification passed, raises assertion otherwise
    """
    # Verify result structure
    assert isinstance(result, ENTResult), "Result should be an ENTResult instance"
    
    # Verify trials
    assert len(result.trials) == expected_trials, f"Expected {expected_trials} trials, got {len(result.trials)}"
    assert result.trials_completed == expected_trials, f"Expected {expected_trials} completed trials"
    
    # Verify all trials have parameter, metric values and are marked as completed
    for i, trial in enumerate(result.trials):
        assert isinstance(trial, ENTTrial), f"Trial {i} should be an ENTTrial instance"
        assert len(trial.parameters) > 0, f"Trial {i} should have parameters"
        assert len(trial.metrics) > 0, f"Trial {i} should have metrics"
        assert trial.status == EnumTrialStatus.COMPLETED, f"Trial {i} should be completed"
    
    # Verify best result if expected
    if has_best_result:
        assert result.best_parameters is not None, "Should have best parameters"
        assert len(result.best_parameters) > 0, "Best parameters should not be empty"
        assert result.best_metrics is not None, "Should have best metrics"
        assert len(result.best_metrics) > 0, "Best metrics should not be empty"
        
        # Check expected best values if provided
        if expected_best:
            for param_name, expected_value in expected_best.items():
                assert param_name in result.best_parameters, f"Best parameters should include {param_name}"
                actual_value = result.best_parameters[param_name]
                assert abs(actual_value - expected_value) < 0.1, f"Best value for {param_name} should be close to {expected_value}, got {actual_value}"
    
    return True


##############
# Tests
##############

class TestInitialization:
    """Tests for proper initialization and configuration of ScipyDirectOptimizationRunner."""
    
    def test_create_from_config(self):
        """Test creating a runner from config."""
        # Arrange
        config = create_optimization_config(random_seed=123)
        
        # Act
        runner = ScipyDirectOptimizationRunner.create_from_config(config)
        
        # Assert
        assert isinstance(runner, ScipyDirectOptimizationRunner)
        assert runner.random_seed == 123, "Random seed should be set from config"
        
        print("ScipyDirectOptimizationRunner creation from config test passed")
    
    def test_random_seed_setting(self):
        """Test that random seed produces reproducible results."""
        # Arrange - Create problem with simple objective function
        objective = SimpleQuadraticObjective()
        problem = create_optimization_problem()
        config = create_optimization_config(random_seed=456, max_trials=5)
        metadata = create_metadata()
        
        # Act - Run optimization twice with the same seed
        runner1 = ScipyDirectOptimizationRunner()
        runner1.random_seed = 456
        result1 = runner1.fit(metadata, config, problem, objective)
        
        runner2 = ScipyDirectOptimizationRunner()
        runner2.random_seed = 456
        result2 = runner2.fit(metadata, config, problem, objective)
        
        # Assert - Results should be the same
        assert len(result1.trials) == len(result2.trials)
        
        # Compare parameters for each trial
        for i in range(len(result1.trials)):
            params1 = result1.trials[i].parameters
            params2 = result2.trials[i].parameters
            
            for param_name in params1.keys():
                assert param_name in params2
                assert params1[param_name] == params2[param_name], f"Parameters for trial {i} should match"
        
        # Compare best parameters
        for param_name in result1.best_parameters.keys():
            assert param_name in result2.best_parameters
            assert result1.best_parameters[param_name] == result2.best_parameters[param_name], "Best parameters should match"
        
        print("Random seed reproducibility test passed")
        
    def test_algorithm_configuration_defaults(self):
        """Test that the algorithm uses reasonable default configuration."""
        # Arrange & Act
        runner = ScipyDirectOptimizationRunner()
        
        # Assert - Runner should be created successfully with defaults
        assert runner is not None, "Runner should be created with default configuration"
        assert hasattr(runner, 'random_seed'), "Runner should have random_seed property"
        assert runner.random_seed == 42, "Default random seed should be 42"
        
        print("Algorithm configuration defaults test passed")


class TestParameterHandling:
    """Tests for parameter space handling in ScipyDirectOptimizationRunner."""
    
    def test_get_param_bounds_numeric(self):
        """Test extracting bounds from numeric parameters."""
        # Arrange
        runner = ScipyDirectOptimizationRunner()
        parameters = [
            VOOptParam(
                label="x1",
                type_=EnumParameterSpaceType.FLOAT_UNIFORM,
                min_max_inclusive=(0.0, 1.0)
            ),
            VOOptParam(
                label="x2",
                type_=EnumParameterSpaceType.FLOAT_UNIFORM,
                min_max_inclusive=(-5.0, 5.0)
            )
        ]
        
        # Act
        bounds = runner._get_param_bounds(parameters)
        
        # Assert
        assert len(bounds) == 2, "Should return bounds for both parameters"
        assert bounds[0] == (0.0, 1.0), "First bound should match first parameter"
        assert bounds[1] == (-5.0, 5.0), "Second bound should match second parameter"
        
        print("Parameter bounds extraction test passed")
    
    def test_get_param_bounds_error(self):
        """Test error handling for parameters without bounds."""
        # Arrange
        runner = ScipyDirectOptimizationRunner()
        parameters = [
            VOOptParam(
                label="categorical_param",
                type_=EnumParameterSpaceType.CATEGORIAL,
                options=["A", "B", "C"]  # No min_max_inclusive
            )
        ]
        
        # Act & Assert - Should raise ValueError
        with pytest.raises(ValueError) as excinfo:
            runner._get_param_bounds(parameters)
        
        # Check error message mentions the parameter name
        assert "categorical_param" in str(excinfo.value), "Error should mention the parameter name"
        
        print("Parameter bounds error handling test passed")
    


class TestObjectiveWrapping:
    """Tests for objective function wrapping."""
    
    def test_wrap_minimize_objective(self):
        """Test wrapping an objective function for minimization."""
        # Arrange
        runner = ScipyDirectOptimizationRunner()
        objective = MockObjectiveFunction(return_values={EnumMetrics.REG_MSE: 2.0})
        problem = create_optimization_problem(
            param_names=["x1", "x2"],
            primary_metric=EnumMetrics.REG_MSE,
            maximize=False  # Minimize
        )
        param_names = ["x1", "x2"]
        
        # Act
        wrapped_obj = runner._create_wrapped_objective(objective, problem, param_names)
        # Direct algorithm calls the wrapped function with just the parameter array
        # The *args is for additional parameters passed by scipy.optimize.direct
        result = wrapped_obj(np.array([0.5, 0.7]), None)  # Add a dummy parameter to satisfy the function signature
        
        # Assert
        assert objective.call_count == 1, "Original objective should be called once"
        assert "x1" in objective.call_parameters[0], "Parameters should be passed as dict"
        assert objective.call_parameters[0]["x1"] == 0.5, "x1 value should be passed correctly"
        assert objective.call_parameters[0]["x2"] == 0.7, "x2 value should be passed correctly"
        assert result == 2.0, "For minimization, value should be returned as-is"
        
        print("Objective wrapping for minimization test passed")
    
    def test_wrap_maximize_objective(self):
        """Test wrapping an objective function for maximization."""
        # Arrange
        runner = ScipyDirectOptimizationRunner()
        objective = MockObjectiveFunction(return_values={EnumMetrics.REG_R2_SCORE: 0.8})
        problem = create_optimization_problem(
            param_names=["x"],
            metrics=[EnumMetrics.REG_R2_SCORE],  # Include the R2 score in the metrics
            primary_metric=EnumMetrics.REG_R2_SCORE,
            maximize=True  # Maximize
        )
        param_names = ["x"]
        
        # Act
        wrapped_obj = runner._create_wrapped_objective(objective, problem, param_names)
        result = wrapped_obj(np.array([0.5]), None)  # Pass array of values with dummy parameter
        
        # Assert
        assert objective.call_count == 1, "Original objective should be called once"
        assert "x" in objective.call_parameters[0], "Parameters should be passed as dict"
        assert result == -0.8, "For maximization, value should be negated"
        
        print("Objective wrapping for maximization test passed")
    
    def test_objective_parameter_mapping(self):
        """Test parameter mapping between array and dictionary formats."""
        # Arrange
        runner = ScipyDirectOptimizationRunner()
        objective = MockObjectiveFunction()
        problem = create_optimization_problem(
            param_names=["param1", "param2", "param3"],
            primary_metric=EnumMetrics.REG_MSE
        )
        param_names = ["param1", "param2", "param3"]
        
        # Act
        wrapped_obj = runner._create_wrapped_objective(objective, problem, param_names)
        result = wrapped_obj(np.array([0.1, 0.2, 0.3]), None)  # Pass numpy array of values with dummy parameter
        
        # Assert
        assert len(objective.call_parameters) == 1, "Objective should be called once"
        params = objective.call_parameters[0]
        assert params["param1"] == 0.1, "param1 should be mapped correctly"
        assert params["param2"] == 0.2, "param2 should be mapped correctly"
        assert params["param3"] == 0.3, "param3 should be mapped correctly"
        
        print("Parameter mapping test passed")


class TestTrialHandling:
    """Tests for trial creation and handling."""
    
    def test_create_trial(self):
        """Test creation of trial records."""
        # Arrange
        runner = ScipyDirectOptimizationRunner()
        problem = create_optimization_problem()
        
        context = {
            "params": {"x": 0.5, "y": 0.7},
            "metrics": {EnumMetrics.REG_MSE: 1.2, "custom_metric": 3.4},
            "trial_number": 5
        }
        
        # Act
        trial = runner._create_trial(problem, context)
        
        # Assert
        assert isinstance(trial, ENTTrial), "Should return an ENTTrial instance"
        assert trial.trial_label == "trial_5", "Trial label should include the trial number"
        assert trial.parameters == {"x": 0.5, "y": 0.7}, "Parameters should match context"
        assert trial.metrics[str(EnumMetrics.REG_MSE)] == 1.2, "Metrics should include converted EnumMetrics as string keys"
        assert trial.metrics["custom_metric"] == 3.4, "Metrics should include custom metrics"
        assert trial.status == EnumTrialStatus.COMPLETED, "Trial should be marked as completed"
        
        print("Trial creation test passed")
    
    def test_trial_metrics_conversion(self):
        """Test conversion of metric values in trials."""
        # Arrange
        runner = ScipyDirectOptimizationRunner()
        problem = create_optimization_problem()
        
        # Use metrics with both string and enum keys
        mixed_metrics = {
            EnumMetrics.REG_MSE: 1.2,  # Enum key
            "custom_metric": 3.4,      # String key
        }
        
        context = {
            "params": {"x": 0.5},
            "metrics": mixed_metrics,
            "trial_number": 1
        }
        
        # Act
        trial = runner._create_trial(problem, context)
        
        # Assert
        assert str(EnumMetrics.REG_MSE) in trial.metrics, "Enum metric should be converted to string"
        assert trial.metrics[str(EnumMetrics.REG_MSE)] == 1.2, "Enum metric value should be preserved"
        assert "custom_metric" in trial.metrics, "String metric should be preserved"
        assert trial.metrics["custom_metric"] == 3.4, "String metric value should be preserved"
        
        print("Trial metrics conversion test passed")


class TestOptimizationExecution:
    """Tests for running the optimization process."""
    
    def test_simple_optimization(self):
        """Test optimization with a simple quadratic function."""
        # Arrange
        objective = SimpleQuadraticObjective(param_name="x", a=1.0, b=0.5, c=0.0)
        problem = create_optimization_problem(
            param_names=["x"],
            param_bounds=[(0.0, 1.0)],
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=10)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert len(result.trials) <= 10, "Should respect max_trials limit"
        assert result.best_parameters is not None, "Should find best parameters"
        assert "x" in result.best_parameters, "Best parameters should include 'x'"
        # The minimum of the function is at x=0.5
        assert abs(result.best_parameters["x"] - 0.5) < 0.1, "Should find minimum close to x=0.5"
        
        print("Simple optimization test passed")
    
    def test_bounded_optimization(self):
        """Test optimization with specific parameter bounds."""
        # Arrange - Create a function with minimum at x=2.0, but limit search to [0, 1]
        objective = SimpleQuadraticObjective(param_name="x", a=1.0, b=2.0, c=0.0)
        problem = create_optimization_problem(
            param_names=["x"],
            param_bounds=[(0.0, 1.0)],  # Restrict bounds to not include the true minimum
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=10)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert result.best_parameters is not None, "Should find best parameters"
        assert "x" in result.best_parameters, "Best parameters should include 'x'"
        # The minimum within bounds should be at the upper bound (1.0)
        assert abs(result.best_parameters["x"] - 1.0) < 0.1, "Should find minimum at upper bound"
        
        print("Bounded optimization test passed")
    
    def test_callback_execution(self):
        """Test callback function is called for each trial."""
        # Arrange
        objective = SimpleQuadraticObjective(param_name="x")
        problem = create_optimization_problem()
        config = create_optimization_config(max_trials=5)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert len(result.trials) > 0, "Should have trials recorded"
        # Note: In SciPy's DIRECT algorithm, the objective function is typically called
        # multiple times per iteration, and the callback is called once per iteration.
        # Therefore, objective.call_count may be greater than len(result.trials).
        assert objective.call_count >= len(result.trials), "Objective should be called at least once per trial"
        # Verify that each unique parameter set in the objective calls appears in the trials
        param_sets_in_trials = set(tuple(sorted(t.parameters.items())) for t in result.trials)
        assert len(param_sets_in_trials) > 0, "Should have unique parameter sets in trials"
        
        print("Callback execution test passed")
    
    def test_max_trials_limit(self):
        """Test respecting the maximum trial count limit."""
        # Arrange
        objective = SimpleQuadraticObjective(param_name="x")
        problem = create_optimization_problem()
        max_trials = 7
        config = create_optimization_config(max_trials=max_trials)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert len(result.trials) <= max_trials, "Should respect max_trials limit"
        # Note: The SciPy DIRECT algorithm may call the objective function multiple times
        # during its internal working, not just through the callback. This is expected
        # behavior and doesn't mean the max_trials limit is being ignored.
        
        print("Max trials limit test passed")
    
    def test_optimization_convergence(self):
        """Test convergence to known solutions."""
        # Arrange - Multi-parameter optimization problem
        param_coefficients = {
            "x1": (1.0, 0.3),  # (a, b) - minimum at x1=0.3
            "x2": (2.0, 0.7),  # minimum at x2=0.7
        }
        objective = MultiParameterQuadraticObjective(param_coefficients=param_coefficients)
        problem = create_optimization_problem(
            param_names=["x1", "x2"],
            param_bounds=[(0.0, 1.0), (0.0, 1.0)],
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=20)  # More trials for better convergence
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert result.best_parameters is not None, "Should find best parameters"
        assert "x1" in result.best_parameters, "Best parameters should include 'x1'"
        assert "x2" in result.best_parameters, "Best parameters should include 'x2'"
        # Check if it found the minimum with reasonable accuracy
        assert abs(result.best_parameters["x1"] - 0.3) < 0.1, "Should find minimum close to x1=0.3"
        assert abs(result.best_parameters["x2"] - 0.7) < 0.1, "Should find minimum close to x2=0.7"
        
        print("Optimization convergence test passed")
    
    def test_multi_parameter_optimization(self):
        """Test optimization with multiple parameters with different scales."""
        # Arrange - Parameters with different scales and search ranges
        param_coefficients = {
            "small_range": (1.0, 0.05),    # min at 0.05, range [0, 0.1]
            "large_range": (1.0, 50.0),    # min at 50.0, range [0, 100]
        }
        objective = MultiParameterQuadraticObjective(param_coefficients=param_coefficients)
        problem = create_optimization_problem(
            param_names=["small_range", "large_range"],
            param_bounds=[(0.0, 0.1), (0.0, 100.0)],
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=25)  # More trials for better convergence
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert - Check if it can handle parameters with different scales
        assert result.best_parameters is not None, "Should find best parameters"
        assert "small_range" in result.best_parameters, "Best parameters should include 'small_range'"
        assert "large_range" in result.best_parameters, "Best parameters should include 'large_range'"
        # Check if it found the minima with reasonable accuracy relative to their scales
        assert abs(result.best_parameters["small_range"] - 0.05) < 0.02, "Should find minimum close to small_range=0.05"
        assert abs(result.best_parameters["large_range"] - 50.0) < 10.0, "Should find minimum close to large_range=50.0"
        
        print("Multi-parameter optimization test passed")
    
    def test_optimization_extreme_scale_differences(self):
        """Test optimization with parameters that have extremely different scales."""
        # Arrange - Parameters with extreme scale differences
        param_coefficients = {
            "micro_param": (1.0, 0.0000001),     # min at 0.0000001, range [0, 0.000001]
            "macro_param": (1.0, 1000000.0),     # min at 1000000, range [0, 10000000]
        }
        objective = MultiParameterQuadraticObjective(param_coefficients=param_coefficients)
        problem = create_optimization_problem(
            param_names=["micro_param", "macro_param"],
            param_bounds=[(0.0, 0.000001), (0.0, 10000000.0)],
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=30)  # More trials for better convergence
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert - Check if it can handle parameters with extreme scale differences
        assert result.best_parameters is not None, "Should find best parameters"
        assert "micro_param" in result.best_parameters, "Best parameters should include 'micro_param'"
        assert "macro_param" in result.best_parameters, "Best parameters should include 'macro_param'"
        
        # Check if it found the minima with reasonable relative accuracy
        # We're looking for order-of-magnitude correctness, not exact matches
        micro_relative_error = abs(result.best_parameters["micro_param"] - 0.0000001) / 0.0000001
        macro_relative_error = abs(result.best_parameters["macro_param"] - 1000000.0) / 1000000.0
        
        # The errors may be large due to the extreme scales, but we expect the optimizer to get
        # somewhat close in relative terms - error less than 1000% of target value
        assert micro_relative_error < 10, f"Micro parameter relative error should be reasonable, got {micro_relative_error}"
        assert macro_relative_error < 10, f"Macro parameter relative error should be reasonable, got {macro_relative_error}"
        
        print("Extreme scale differences test passed")


class TestErrorHandling:
    """Tests for error conditions and recovery."""
    
    def test_objective_function_error(self):
        """Test handling errors in the objective function."""
        # Arrange
        objective = MockObjectiveFunction(raise_error=True)
        problem = create_optimization_problem()
        config = create_optimization_config(max_trials=5)
        metadata = create_metadata()
        
        # Act & Assert
        with pytest.raises(ValueError) as excinfo:
            runner = ScipyDirectOptimizationRunner()
            result = runner.fit(metadata, config, problem, objective)
        
        print("Objective function error handling test passed")
    
    def test_partial_optimization_results(self):
        """Test getting partial results when optimization fails after some trials."""
        # Arrange - Create an objective function that fails after 3 calls
        class FailAfterCountObjective(BaseObjectiveFunction):
            def __init__(self, fail_after=3):
                self.call_count = 0
                self.fail_after = fail_after
                
            def __call__(self, parameters, problem_spec):
                self.call_count += 1
                if self.call_count > self.fail_after:
                    raise RuntimeError(f"Simulated error after {self.fail_after} calls")
                return {problem_spec.primary_metric: 1.0}
        
        objective = FailAfterCountObjective(fail_after=3)
        problem = create_optimization_problem()
        config = create_optimization_config(max_trials=10)
        metadata = create_metadata()
        
        # Patch the scipy direct function to allow us to control when the error happens
        original_direct = direct
        
        def mock_direct(*args, **kwargs):
            # Let the callback be called a few times to collect some trials
            callback = kwargs.get('callback')
            if callback:
                # Call the callback to create 3 trials
                for i in range(3):
                    callback(np.array([0.5]))  # This triggers objective function call
            
            # Then raise the error from the objective function on the 4th call
            objective({"x": 0.5}, problem)
            
            # This should not be reached
            return None
        
        # Apply the patch
        import scipy.optimize
        scipy.optimize.direct = mock_direct
        
        try:
            # Act - The optimization should fail but return partial results
            runner = ScipyDirectOptimizationRunner()
            with pytest.raises(ValueError):
                result = runner.fit(metadata, config, problem, objective)
        finally:
            # Restore the original function
            scipy.optimize.direct = original_direct
        
        print("Partial optimization results test passed")
    
    def test_stop_method(self):
        """Test behavior of the stop method."""
        # Arrange
        runner = ScipyDirectOptimizationRunner()
        
        # Act
        result = runner.stop("test_job")
        
        # Assert
        assert result is False, "Stop method should return False for non-existent job"
        
        print("Stop method test passed")


class TestIntegrationWithBase:
    """Tests for proper integration with the base class."""
    
    def test_base_class_implementation(self):
        """Test implementation of all abstract methods."""
        # Arrange
        runner = ScipyDirectOptimizationRunner()
        
        # Assert - Verify all abstract methods are implemented
        assert hasattr(runner, "_run_optimization"), "Should implement _run_optimization"
        assert hasattr(runner, "stop"), "Should implement stop"
        assert hasattr(runner, "_get_param_map"), "Should implement _get_param_map"
        assert hasattr(runner, "_create_trial"), "Should implement _create_trial"
        
        # Check method signatures to ensure they match base class requirements
        base_methods = {
            "_run_optimization": 4,  # Number of required parameters
            "stop": 1,
            "_get_param_map": 1,
            "_create_trial": 2
        }
        
        for method_name, param_count in base_methods.items():
            method = getattr(runner, method_name)
            sig = inspect.signature(method)
            assert len(sig.parameters) >= param_count, f"{method_name} should have at least {param_count} parameters"
        
        print("Base class implementation test passed")
    
    def test_objective_function_validation(self):
        """Test validation of objective functions."""
        # Arrange - Create a problem and an objective function with mismatched metrics
        problem = create_optimization_problem(
            metrics=[EnumMetrics.REG_MSE, EnumMetrics.REG_R2_SCORE],
            primary_metric=EnumMetrics.REG_MSE
        )
        objective = MockObjectiveFunction(return_values={EnumMetrics.REG_RMSE: 1.0})  # Wrong metric
        
        # Act & Assert - Should raise ValueError during validation
        with pytest.raises(ValueError) as excinfo:
            runner = ScipyDirectOptimizationRunner()
            runner.fit(create_metadata(), create_optimization_config(), problem, objective)
        
        # Check error message
        assert "Objective function validation failed" in str(excinfo.value), "Error should mention validation failure"
        assert "doesn't return all required metrics" in str(excinfo.value), "Error should mention missing metrics"
        assert str(EnumMetrics.REG_MSE) in str(excinfo.value), "Error should mention the primary metric"
        
        print("Objective function validation test passed")


# Additional robustness tests
class TestRobustness:
    """Additional tests for robustness."""
    
    def test_same_random_seed_reproducibility(self):
        """Test that the same random seed produces identical optimization results."""
        # Arrange
        objective = SimpleQuadraticObjective(param_name="x", a=1.0, b=0.5, c=0.0)
        problem = create_optimization_problem()
        
        # Act - Run optimization twice with the same seed
        runner1 = ScipyDirectOptimizationRunner()
        runner1.random_seed = 789
        result1 = runner1.fit(create_metadata(), create_optimization_config(random_seed=789), problem, objective)
        
        runner2 = ScipyDirectOptimizationRunner()
        runner2.random_seed = 789
        result2 = runner2.fit(create_metadata(), create_optimization_config(random_seed=789), problem, objective)
        
        # Assert - Results should be identical (reproducible)
        assert len(result1.trials) == len(result2.trials), "Same number of trials should be performed"
        
        for i in range(len(result1.trials)):
            params1 = result1.trials[i].parameters
            params2 = result2.trials[i].parameters
            
            for param_name in params1.keys():
                assert param_name in params2, f"Parameter {param_name} should exist in both runs"
                assert params1[param_name] == params2[param_name], f"Parameter {param_name} values should match"
        
        # Compare best parameters
        for param_name in result1.best_parameters.keys():
            assert param_name in result2.best_parameters, f"Best parameter {param_name} should exist in both runs"
            assert result1.best_parameters[param_name] == result2.best_parameters[param_name], "Best parameter values should match"
        
        print("Same random seed reproducibility test passed")
    
    def test_zero_random_seed(self):
        """Test optimization with zero as the random seed."""
        # Arrange
        objective = SimpleQuadraticObjective()
        problem = create_optimization_problem()
        config = create_optimization_config(random_seed=0)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        runner.random_seed = 0
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert - Should complete without errors
        assert result is not None, "Should complete optimization with zero seed"
        assert len(result.trials) > 0, "Should have trials even with zero seed"
        assert result.best_parameters is not None, "Should find best parameters with zero seed"
        
        print("Zero random seed test passed")
    
    def test_large_random_seed(self):
        """Test optimization with a large random seed (but within valid range)."""
        # Arrange
        objective = SimpleQuadraticObjective()
        problem = create_optimization_problem()
        large_seed = 2**31 - 1  # Max signed 32-bit integer, well within np.random's valid range
        config = create_optimization_config(random_seed=large_seed)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        runner.random_seed = large_seed
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert - Should complete without errors
        assert result is not None, "Should complete optimization with large seed"
        assert len(result.trials) > 0, "Should have trials even with large seed"
        assert result.best_parameters is not None, "Should find best parameters with large seed"
        
        print("Large random seed test passed")
    
    def test_optimization_edge_case_flat_function(self):
        """Test optimization with a completely flat objective function."""
        # Arrange - Function that returns the same value regardless of input
        class FlatObjective(BaseObjectiveFunction):
            def __init__(self):
                self.call_count = 0
                
            def __call__(self, parameters, problem_spec):
                self.call_count += 1
                return {problem_spec.primary_metric: 1.0}  # Always return the same value
        
        objective = FlatObjective()
        problem = create_optimization_problem()
        config = create_optimization_config(max_trials=10)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert - Should complete optimization and return something reasonable
        assert result is not None, "Should complete optimization with flat function"
        assert len(result.trials) > 0, "Should have trials even with flat function"
        assert result.best_parameters is not None, "Should find best parameters with flat function"
        # No assertion about the particular value, since any point is equally good
        
        print("Flat function optimization test passed")



class TestEdgeCases:
    """Tests for edge cases and boundary conditions."""
    
    def test_single_parameter_optimization(self):
        """Test optimization with a single parameter to ensure minimal case works."""
        # Arrange
        objective = SimpleQuadraticObjective(param_name="single_param", a=1.0, b=0.3, c=0.0)
        problem = create_optimization_problem(
            param_names=["single_param"],
            param_bounds=[(0.0, 1.0)],
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=8)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert result is not None, "Should handle single parameter optimization"
        assert len(result.best_parameters) == 1, "Should have one best parameter"
        assert "single_param" in result.best_parameters, "Should include the single parameter"
        assert abs(result.best_parameters["single_param"] - 0.3) < 0.15, "Should find minimum close to expected value"
        
        print("Single parameter optimization test passed")
    
    def test_very_tight_bounds(self):
        """Test optimization with very tight parameter bounds."""
        # Arrange
        objective = SimpleQuadraticObjective(param_name="x", a=1.0, b=0.5, c=0.0)
        problem = create_optimization_problem(
            param_names=["x"],
            param_bounds=[(0.49, 0.51)],  # Very tight bounds around the minimum
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=5)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert result is not None, "Should handle tight bounds"
        assert result.best_parameters is not None, "Should find best parameters"
        assert 0.49 <= result.best_parameters["x"] <= 0.51, "Best parameter should be within tight bounds"
        
        print("Very tight bounds test passed")
    
    def test_wide_parameter_bounds(self):
        """Test optimization with very wide parameter bounds."""
        # Arrange
        objective = SimpleQuadraticObjective(param_name="x", a=1.0, b=50.0, c=0.0)
        problem = create_optimization_problem(
            param_names=["x"],
            param_bounds=[(-1000.0, 1000.0)],  # Very wide bounds
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=20)  # More trials for wide search
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert result is not None, "Should handle wide bounds"
        assert result.best_parameters is not None, "Should find best parameters"
        # Should find minimum reasonably close to 50.0, even with wide bounds
        assert abs(result.best_parameters["x"] - 50.0) < 100.0, "Should find minimum within reasonable range"
        
        print("Wide parameter bounds test passed")


class TestPerformanceAndScaling:
    """Tests for performance characteristics and scaling behavior."""
    
    def test_optimization_with_many_parameters(self):
        """Test optimization performance with a larger number of parameters."""
        # Arrange - Create a 5-parameter optimization problem
        param_coefficients = {
            f"param_{i}": (1.0, 0.1 * i + 0.1)  # Each parameter has minimum at different location
            for i in range(5)
        }
        objective = MultiParameterQuadraticObjective(param_coefficients=param_coefficients)
        
        param_names = list(param_coefficients.keys())
        param_bounds = [(0.0, 1.0)] * len(param_names)
        
        problem = create_optimization_problem(
            param_names=param_names,
            param_bounds=param_bounds,
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=30)  # More trials for higher dimensions
        metadata = create_metadata()
        
        # Act
        start_time = time.time()
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        end_time = time.time()
        
        # Assert
        assert result is not None, "Should handle many parameters"
        assert len(result.best_parameters) == 5, "Should have 5 best parameters"
        assert end_time - start_time < 60, "Should complete within reasonable time (60s)"
        
        # Check if it found reasonable values for each parameter
        for i, (param_name, (a, expected_min)) in enumerate(param_coefficients.items()):
            assert param_name in result.best_parameters, f"Should include {param_name}"
            actual_value = result.best_parameters[param_name]
            # Allow larger tolerance for multi-dimensional optimization
            assert abs(actual_value - expected_min) < 0.3, f"Parameter {param_name} should be reasonably close to minimum"
        
        print("Many parameters optimization test passed")
    
    def test_minimal_trials_execution(self):
        """Test optimization with minimal number of trials."""
        # Arrange
        objective = SimpleQuadraticObjective()
        problem = create_optimization_problem()
        config = create_optimization_config(max_trials=1)  # Minimal trials
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert result is not None, "Should handle minimal trials"
        assert len(result.trials) >= 1, "Should have at least one trial"
        assert result.best_parameters is not None, "Should find best parameters"
        assert result.best_metrics is not None, "Should have best metrics"
        
        print("Minimal trials execution test passed")


class TestDifferentMetrics:
    """Tests for handling different metric types and optimization directions."""
    
    def test_maximization_objective(self):
        """Test optimization when maximizing instead of minimizing."""
        # Arrange - Use negative quadratic to create a maximum at x=0.5
        objective = SimpleQuadraticObjective(param_name="x", a=-1.0, b=0.5, c=1.0)  # Maximum at x=0.5
        problem = create_optimization_problem(
            param_names=["x"],
            param_bounds=[(0.0, 1.0)],
            metrics = [EnumMetrics.REG_R2_SCORE],
            primary_metric=EnumMetrics.REG_R2_SCORE,  # Use a metric that's typically maximized
            maximize=True
        )
        config = create_optimization_config(max_trials=15)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert result is not None, "Should handle maximization"
        assert result.best_parameters is not None, "Should find best parameters"
        assert "x" in result.best_parameters, "Should include x parameter"
        # Should find maximum close to x=0.5
        assert abs(result.best_parameters["x"] - 0.5) < 0.15, "Should find maximum close to x=0.5"
        
        print("Maximization objective test passed")
    
    def test_multiple_metrics_optimization(self):
        """Test optimization with multiple metrics defined."""
        # Arrange - Objective function that returns multiple metrics
        class MultiMetricObjective(BaseObjectiveFunction):
            def __init__(self):
                self.call_count = 0
                
            def __call__(self, parameters, problem_spec):
                self.call_count += 1
                x = parameters["x"]
                
                # Return multiple metrics based on the same parameter
                return {
                    EnumMetrics.REG_MSE: (x - 0.3)**2,      # Primary metric to minimize
                    EnumMetrics.REG_MAE: abs(x - 0.3),      # Secondary metric
                    EnumMetrics.REG_R2_SCORE: 1.0 - (x - 0.3)**2  # Another metric
                }
        
        objective = MultiMetricObjective()
        problem = create_optimization_problem(
            param_names=["x"],
            param_bounds=[(0.0, 1.0)],
            metrics=[EnumMetrics.REG_MSE, EnumMetrics.REG_MAE, EnumMetrics.REG_R2_SCORE],
            primary_metric=EnumMetrics.REG_MSE
        )
        config = create_optimization_config(max_trials=10)
        metadata = create_metadata()
        
        # Act
        runner = ScipyDirectOptimizationRunner()
        result = runner.fit(metadata, config, problem, objective)
        
        # Assert
        assert result is not None, "Should handle multiple metrics"
        assert result.best_parameters is not None, "Should find best parameters"
        assert result.best_metrics is not None, "Should have best metrics"
        
        # Check that all metrics are present in the result
        assert EnumMetrics.REG_MSE in result.best_metrics, "Should include primary metric"
        assert EnumMetrics.REG_MAE in result.best_metrics, "Should include secondary metric"
        assert EnumMetrics.REG_R2_SCORE in result.best_metrics, "Should include tertiary metric"
        
        # Should optimize for the primary metric (MSE)
        assert abs(result.best_parameters["x"] - 0.3) < 0.15, "Should minimize primary metric"
        
        print("Multiple metrics optimization test passed")