####################
from tabulate import tabulate
from tqdm import tqdm
import math
import copy
import logging
import os
import random
from contextlib import contextmanager
from datetime import datetime, timedelta
from pathlib import Path
from typing import (
    Any,
    Callable,
    Dict,
    Generator,
    List,
    Optional,
    Tuple,
    Type,
    Union,
    TypeVar,
)
import time
import numpy as np
import pandas as pd
import pytest

import backend.core._sharedutils.Utilities as sharedutils
import backend.core._atlas._singletons
import backend.core._atlas.aggregates as at

import backend.infrastructure._db.repo as re
import backend.core._matrix.matrix_dwsim as ma

# SETUP

MODEL_ID = "process_model_1"
RELATIVE_ARTEFACT_FOLDERPATH = "backend/infrastructure/artefacts/bin/dwsim"
TEMPLATE_NAME = "Empty_Template.dwxmz"

####################

# Helper Functions


def compare_objects(
    obj1, obj2, attrs: Optional[List[str]] = None
) -> Tuple[bool, Optional[str]]:
    """
    Compares two objects attribute by attribute and returns detailed comparison results.

    Returns:
        Tuple[bool, str]: (True, None) if objects match, (False, failure_message) if they differ
    """
    logging.info(f"COMPARING: {obj1}, {obj2}")
    _attrs = attrs if attrs is not None else obj1.__dict__.keys()

    for attr in _attrs:
        logging.info(f"checking: {attr}")

        if not hasattr(obj2, attr):
            return False, f"Attribute {attr} missing in second object"

        val1 = getattr(obj1, attr)
        val2 = getattr(obj2, attr)

        if val1 is None and val2 is None:
            continue
        elif val1 is None or val2 is None:
            return (
                False,
                f"Attribute {attr}: One value is None - \nval1: {val1}\nval2: {val2}",
            )
        elif val1 != val2:
            return (
                False,
                f"Attribute {attr}: Values differ - \nval1: {val1}\nval2: {val2}",
            )

    return True, "Values are equal"


def setup_atlas_basic() -> at.AtlasRoot:
    """Returns atlas, entity and a deepcopy of the entity"""

    # Create Root
    atlas = at.AtlasRoot("pmodel-1", "willywonka-1")
    compound1 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Methane)
    compound3 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Propane)
    compound2 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Ethane)
    atlas.register_compound(compound1)
    atlas.register_compound(compound3)
    atlas.register_compound(compound2)

    # Create generic equipment
    eqp1 = atlas.add_equipment(at.Heater, "HT-01")
    eqp2 = atlas.add_equipment(at.Heater, "HT-02")

    # Join things up
    stream = atlas.add_stream(eqp1, eqp2)

    # Create material stream
    stream.add_variable(at.VOCompoundMassRatio(compound1, 0.8))
    stream.add_variable(at.VOCompoundMassRatio(compound2, 0.2))
    assert math.isclose(sum(stream.compoundmix.values()), 1)

    return atlas


def setup_atlas_4chain() -> at.AtlasRoot:
    """Returns atlas, entity and a deepcopy of the entity"""

    # Create Root
    atlas = at.AtlasRoot("pmodel-1", "willywonka-1")
    compound1 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Methane)
    compound2 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Ethane)
    atlas.register_compound(compound1)
    atlas.register_compound(compound2)

    # Create generic equipment
    batteryin = atlas.add_equipment(at.BatteryIn, "bin-01")
    eqp1 = atlas.add_equipment(at.Heater, "HT-01")
    eqp2 = atlas.add_equipment(at.Heater, "HT-02")
    bout = atlas.add_equipment(at.BatteryOut, "bin-01")

    # Join things up
    pairs = [
        (batteryin, eqp1),
        (batteryin, eqp2),
        (eqp1, bout),
        (eqp2, bout),
    ]
    for pair in pairs:
        # Create material stream
        stream = atlas.add_stream(pair[0], pair[1])
        # Add compound mass ratio
        rand = round(random.random(), 3)
        stream.add_variable(at.VOCompoundMassRatio(compound1, rand))
        stream.add_variable(at.VOCompoundMassRatio(compound2, 1 - rand))

    return atlas


def setup_matrix(atlas_ref: at.AtlasRoot, filename: str = "WillyWonka"):
    matrix = ma.MatrixDWSim(filename)
    matrix.attach_model(atlas_ref, share_model_state=False)
    matrix._setup_endpoint()
    return matrix


def setup_interface_object_materialstream(
    matrix_root: ma.MatrixDWSim, atlas_materialstream: at.ENTBaseStream
):
    # Create a standalone atlas

    e_interface = ma.DWSimMaterialStreamInterface(atlas_materialstream, matrix_root)
    return e_interface


####################

# TESTS

repo = re.FolderRepoForDWSim("backend/artefacts/bin/dwsim", "entity_tests")


def test_setup_atlas():
    atlas = setup_atlas_basic()
    assert isinstance(atlas, at.AtlasRoot)

    stream = atlas.get_stream(by_equipments=("HT-01", "HT-02"))
    assert isinstance(stream, at.MaterialStream)
    assert len(stream.compoundmix) > 0
    logging.info("Compoundmix:", stream.compoundmix)
    assert math.isclose(sum(stream.compoundmix.values()), 1)

    # compoundmixes = stream.get_collection(at.VarCollectionCompoundMassMix)
    print(stream.compoundmix)


def test_materialstream_basic_roundtrip():

    atlas = setup_atlas_basic()
    stream = atlas.get_stream(by_equipments=("HT-01", "HT-02"))
    # streams = atlas.streams
    # stream = atlas.get_stream(by_label=streams[0])
    assert isinstance(stream, at.ENTBaseStream)
    assert len(stream.compoundmix) > 0
    logging.info("Compoundmix:", stream.compoundmix)
    assert math.isclose(sum(stream.compoundmix.values()), 1)

    # Create Matrix root
    matrix = setup_matrix(atlas, "push_compoundmassmix")
    e_inferface = setup_interface_object_materialstream(matrix, stream)
    assert isinstance(e_inferface, ma.DWSimMaterialStreamInterface)

    # Push
    e_inferface.push_to_endpoint()
    repo.save(matrix)

    # Pull
    e_inferface.pull_from_endpoint()
    stream2 = e_inferface.atlas_entity
    assert isinstance(stream2, at.MaterialStream), f"{type(stream2)}"
    # logging.info(stream2)

    assert math.isclose(sum(stream.compoundmix.values()), 1)
    assert math.isclose(sum(stream2.compoundmix.values()), 1)
    assert stream.compoundmix == stream2.compoundmix


def test_energystream():
    atlas = setup_atlas_basic()
    matrix = setup_matrix(atlas, "Automated_EStream")

    energy_stream = ma.DWSimEnergyStreamInterface(atlas, matrix)
    energy_stream.push_to_endpoint()
    assert energy_stream.graphic_obj is not None

    result = energy_stream.pull_from_endpoint()
    repo.save(matrix)
    # assert result is not None


def test_materialstream_basic_roundtrip_w_changes():

    atlas = setup_atlas_basic()
    streams = atlas.streams
    stream = atlas.get_stream(by_label=streams[0])
    assert isinstance(stream, at.ENTBaseStream)
    assert len(stream.compoundmix) > 0

    # Create Matrix root
    matrix = setup_matrix(atlas, "push_compoundmassmix_w_changes")
    e_inferface = setup_interface_object_materialstream(matrix, stream)
    assert isinstance(e_inferface, ma.DWSimMaterialStreamInterface)

    # Push
    e_inferface.push_to_endpoint()

    # Pull
    e_inferface.pull_from_endpoint()
    stream2 = e_inferface.atlas_entity
    assert isinstance(stream2, at.MaterialStream), f"{type(stream2)}"
    logging.info(stream2.compoundmix)
    logging.info(stream2)

    assert math.isclose(sum(stream2.compoundmix.values()), 1)
    assert stream.compoundmix == stream2.compoundmix

    # Update and push again
    stream2 = e_inferface.atlas_entity
    compound1 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Methane)
    compound2 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Ethane)
    compound3 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Propane)
    stream2.set_value(compound1, 0.125)
    stream2.set_value(compound2, 0.125)
    stream2.add_variable(at.VOCompoundMassRatio(compound3, 0.75))
    e_inferface.push_to_endpoint()
    repo.save(matrix)

    # Scramble the entity values
    stream2 = e_inferface.atlas_entity
    compound1 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Methane)
    compound2 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Ethane)
    compound3 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Propane)
    stream2.set_value(compound1, 0.3)
    stream2.set_value(compound2, 0.333)
    stream2.add_variable(at.VOCompoundMassRatio(compound3, 0.1))

    # Pull
    e_inferface.pull_from_endpoint()
    stream2 = e_inferface.atlas_entity
    assert isinstance(stream2, at.MaterialStream), f"{type(stream2)}"
    logging.info(stream2.compoundmix)
    logging.info(stream2)

    assert math.isclose(sum(stream2.compoundmix.values()), 1)
    assert compound3 in stream2.compounds


class MassFlowDiagnostics:
    """
    Group of functions to diagnose failreasons for Mass Flow Dynamics
    """

    def setup_atlas_mfd(self) -> at.AtlasRoot:
        """Returns atlas, entity and a deepcopy of the entity"""

        # Create Root
        atlas = at.AtlasRoot("pmodel-1", "willywonka-1")
        compound1 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Methane)
        compound2 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Ethane)
        compound3 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Propane)
        atlas.register_compound(compound1)
        atlas.register_compound(compound2)
        atlas.register_compound(compound3)

        # Create generic equipment
        eqp1 = atlas.add_equipment(at.Heater, "HT-01")
        eqp2 = atlas.add_equipment(at.Heater, "HT-02")

        # Create material stream
        stream = atlas.add_stream(eqp1, eqp2)

        # Add compoundmass
        stream.add_variable(at.VOCompoundMassRatio(compound1, 0.8))
        stream.add_variable(at.VOCompoundMassRatio(compound2, 0.2))

        return atlas

    def roundtrip_basic(self, count) -> Dict[str, int]:
        title = "mfd_roundtrip_basic"
        failcount = 0
        for i in tqdm(range(count), title):
            atlas = self.setup_atlas_mfd()
            streams = atlas.streams
            stream = atlas.get_stream(by_label=streams[0])

            # Create Matrix root
            matrix = setup_matrix(atlas, f"{title}_{i}")
            e_inferface = setup_interface_object_materialstream(matrix, stream)

            # Push
            e_inferface.push_to_endpoint()

            # Pull
            e_inferface.pull_from_endpoint()
            stream2 = e_inferface.atlas_entity
            assert isinstance(stream2, at.MaterialStream)

            if self.validate_entity(stream, stream2) is False:
                failcount += 1

        return {title: failcount}

    def roundtrip_sleep(self, count) -> Dict[str, int]:

        title = "mfd_roundtrip_sleep"
        failcount = 0
        for i in tqdm(range(count), title):
            atlas = self.setup_atlas_mfd()
            streams = atlas.streams
            stream = atlas.get_stream(by_label=streams[0])

            # Create Matrix root
            matrix = setup_matrix(atlas, f"{title}_{i}")
            e_inferface = setup_interface_object_materialstream(matrix, stream)

            # Push
            e_inferface.push_to_endpoint()
            time.sleep(0.2)

            # Pull
            e_inferface.pull_from_endpoint()
            stream2 = e_inferface.atlas_entity
            assert isinstance(stream2, at.MaterialStream)

            if self.validate_entity(stream, stream2) is False:
                failcount += 1

            time.sleep(0.2)
            repo.save(matrix)

        return {title: failcount}

    def roundtrip_basic_save(self, count) -> Dict[str, int]:

        title = "mfd_roundtrip_save"
        failcount = 0
        for i in tqdm(range(count), title):
            atlas = self.setup_atlas_mfd()
            streams = atlas.streams
            stream = atlas.get_stream(by_label=streams[0])

            # Create Matrix root
            matrix = setup_matrix(atlas, f"{title}_{i}")
            e_inferface = setup_interface_object_materialstream(matrix, stream)

            # Push
            e_inferface.push_to_endpoint()

            # Pull
            e_inferface.pull_from_endpoint()
            stream2 = e_inferface.atlas_entity
            assert isinstance(stream2, at.MaterialStream)

            if self.validate_entity(stream, stream2) is False:
                failcount += 1

            repo.save(matrix)

        return {title: failcount}

    def validate_entity(
        self, base_entity: at.ENTBaseStream, new_entity: at.MaterialStream
    ) -> bool:

        assert len(base_entity.compounds) > 0
        assert math.isclose(sum(base_entity.compoundmix.values()), 1)
        print("Entity ", base_entity.compoundmix)
        print("Entity' ", new_entity.compoundmix)

        if new_entity.compoundmix != base_entity.compoundmix:
            return False

        return True

    def show_df(self, df: pd.DataFrame):
        print(tabulate(df, "keys", tablefmt="psql"))  # type: ignore

    def run(self):
        logging_original = logging.getLogger().getEffectiveLevel()
        logging.getLogger().setLevel(logging.ERROR)

        try:
            data: Dict[str, Any] = {}
            count = 30
            # Get data
            funcs = [
                self.roundtrip_basic,
                self.roundtrip_basic_save,
                self.roundtrip_sleep,
            ]
            for func in funcs:
                data.update(func(count))

            # Analyse
            df = pd.DataFrame(
                {
                    "Test": list(data.keys()),
                    "Attempts": [count] * len(data),
                    "Fails": list(data.values()),
                }
            )
            df["Failrate"] = df["Fails"].astype(float) / df["Attempts"]
            self.show_df(df)

            output_path = Path("MFD.csv")
            output_path.parent.mkdir(exist_ok=True)
            df.to_csv(output_path, index=False)

        finally:
            logging.getLogger().setLevel(logging_original)


# if __name__ == "__main__":
#     diagnostics = MassFlowDiagnostics()
#     diagnostics.run()
