####################

import copy
import logging
import os
import random
from contextlib import contextmanager
from datetime import datetime, timedelta
from pathlib import Path
from typing import (
    Any,
    Callable,
    Dict,
    Generator,
    List,
    Optional,
    Tuple,
    Type,
    Union,
    TypeVar,
)

import numpy as np
import pandas as pd
import pytest

import backend.core._sharedutils.Utilities as sharedutils
import backend.core._atlas.aggregates as at

import backend.core._matrix.matrix_dwsim as ma
import backend.infrastructure._db.repo as re

# DEBUGGING


####################

# FIXTURE

# ABS_EXCEL_TEMPLATE_FILEPATH = sharedutils.create_absolute_path_object(
#     "tests/_data/anyplant02/anyplant02_model_template.xlsx"
# )
MODEL_ID = "process_model_1"
# RELATIVE_ARTEFACT_FOLDERPATH = "artefacts/bin"
RELATIVE_ARTEFACT_FOLDERPATH = "backend/infrastructure/artefacts/bin/dwsim"
TEMPLATE_NAME = "Empty_Template.dwxmz"


repo = re.FolderRepoForDWSim("backend/artefacts/bin/dwsim", "entity_tests")
####################


# Helper Functions
def compare_objects(
    obj1: at.ENTBase,
    obj2: at.ENTBase,
    attrs: Optional[List[str]] = None,
) -> Tuple[bool, Optional[str]]:
    """
    Compares two objects attribute by attribute and returns detailed comparison results.
    Handles CollectionRouter and Collection abstractions.
    """
    logging.info(f"COMPARING: {obj1.label}, {obj2.label}")

    # Get collections from both objects
    collections1 = obj1.get_collections()
    collections2 = obj2.get_collections()

    # Compare collection contents
    for c1, c2 in zip(collections1, collections2):

        logging.info(f"...collection comparison: {c1}, {c2}")
        if c1.collection != c2.collection:
            return (
                False,
                f"Collections differ: \nc1: {c1.collection}\nc2: {c2.collection}",
            )

    return True, "Values are equal"


def _check_and_set_none_cvar(eqpt: at.ENTBaseEquipment):
    """
    Checks for disc vars which are None and sets an arbitrary item
    """
    # Get all dvar, get alt and set
    cvars = eqpt.get_variables(filter=[at.VarCollectionContinuous])

    for var in cvars:
        if var.value == 0.0 or var.value == 0:
            var.set_value(0.75, override_all_policies=True)  # type: ignore


def _check_and_set_none_dvar(eqpt: at.ENTBaseEquipment):
    """
    Checks for disc vars which are None and sets an arbitrary item
    """
    # Get all dvar, get alt and set
    dvars = eqpt.get_variables(filter=[at.VarCollectionDiscreteSet])

    for var in dvars:
        if var.value == None and len(var.bounds or []) > 0:
            sample = list(var.bounds)[0]  # type: ignore
            assert isinstance(sample, (at.VOCompound, at.DiscreteItemSpecEnum))
            var.set_value(sample, override_all_policies=True)  # type: ignore


def _test_endpoint_roundtrip(
    AtlasEquipment: Type[at.ENTBaseEquipment],
    EquipmentInterface: Type[ma.DWSimEquipmentInterface],
    is_save: bool = False,
):

    ####################
    # SETUP ATLAS
    ####################
    title = f"test_matrix_eqpt-{ AtlasEquipment.__name__ }"

    atlas = at.AtlasRoot("pmodel-1", "plant-1")
    # Register Reactions
    reactions = []
    for reaction in [
        at.CASCompoundEnum.Water,
        at.CASCompoundEnum.CarbonDioxide,
        at.CASCompoundEnum.Ethane,
        at.CASCompoundEnum.Propane,
        at.CASCompoundEnum.Benzene,
        at.CASCompoundEnum.Toluene
    ]:
        reactions.append(at.CompoundRef.get_vocompound_from_enum(reaction))
        atlas.register_compound(reactions[-1])
    
    atlas.propogate_compounds_across_streams() 
    atlas.propogate_vars_to_uuid_collection()

    matrix = ma.MatrixDWSim(title)
    matrix.attach_model(atlas)
    matrix._setup_endpoint()

    # Add to atlas
    e_id = f"{AtlasEquipment.__name__}-01"
    entity = atlas.add_equipment(AtlasEquipment, e_id)

    # Custom for reactionset
    if AtlasEquipment == at.ConversionReactor:
        # Create Reactions and reactionset
        assert isinstance(entity, at.ConversionReactor)

        # Create Reaction Entity
        reaction1 = at.ConversionReaction("R1")
        reaction1.add_variable(at.VOReactionStoich(reactions[0], 1))
        reaction1.add_variable(at.VOReactionStoich(reactions[1], -2))
        reaction1.set_value(at.DiscreteSetSpecEnum.BaseCompound, reactions[0])
        reaction1.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "0.8")
        atlas.add_reaction_to_reactor(entity, reaction1)

        # Create Reaction Entity
        reaction2 = at.ConversionReaction("R2")
        reaction2.add_variable(at.VOReactionStoich(reactions[2], 1))
        reaction2.set_value(at.DiscreteSetSpecEnum.BaseCompound, reactions[2])
        reaction2.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "0.2")
        atlas.add_reaction_to_reactor(entity, reaction2)

        # Create Reaction Entity
        reaction3 = at.ConversionReaction("R3")
        reaction3.add_variable(at.VOReactionStoich(reactions[3], 1))
        reaction3.set_value(at.DiscreteSetSpecEnum.BaseCompound, reactions[3])
        reaction3.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "0.5")
        atlas.add_reaction_to_reactor(entity, reaction3)

        assert reaction1 in entity.reactionset
        assert reaction2 in entity.reactionset

        # Push
        for reaction in [reaction1, reaction2, reaction3]:
            reaction_interface = ma.DWSimConversionReactionInterface(reaction, matrix)
            reaction_interface.push_to_endpoint()

    # Set arbitrary values so defaults are overridden
    _check_and_set_none_dvar(entity)
    _check_and_set_none_cvar(entity)
    e_atlas_deepcopy = copy.deepcopy(entity)

    ####################
    # SETUP MATRIX
    ####################

    e_interface = EquipmentInterface(entity, matrix)
    e_interface.push_to_endpoint()
    e_interface.pull_from_endpoint()

    ####################
    # ASSERT EQUALITY
    ####################

    result, debug = compare_objects(e_atlas_deepcopy, e_interface.atlas_entity)  # type: ignore . we know atlas is attached.
    if not isinstance(entity, at.StreamMixer):  # nothing to change, skip
        assert result == True, debug

    ####################
    # ASSERT NOT EQUAL WHEN CHANGE WITHOUT SYNC (test to make sure states are independent)
    ####################

    param_vars = e_interface.atlas_entity.get_variables(
        filter=[at.VarCollectionContinuous]
    )
    for var in param_vars:
        assert isinstance(var, at.VOContinuousVariable)
        if isinstance(var._value, (tuple, list)):
            var._value = tuple(0.3 for _ in range(len(var._value)))
        else:
            var._value = 3.0

    e_interface.push_to_endpoint()
    e_interface.pull_from_endpoint()  # DPLICATE HAPPENS HERE

    # ASSERT SET AND PULL CORRECT
    result, debug = compare_objects(e_atlas_deepcopy, e_interface.atlas_entity)  # type: ignore . we know atlas is attached
    assert result == False, debug

    ####################
    # ASSERT EQUAL WHEN CHANGE WITH SOME SYNC (test to make sure comparisons are correct)
    ####################

    # Set same val in deepcopy
    for var in param_vars:
        assert isinstance(var, at.VOContinuousVariable)
        copy_var = e_atlas_deepcopy.get_variable(var.variable_enum)
        assert copy_var.variable_enum == var.variable_enum
        assert isinstance(copy_var, at.VOContinuousVariable)
        if isinstance(var._value, (tuple, list)):
            copy_var._value = tuple(0.3 for _ in range(len(var._value)))
        else:
            copy_var._value = 3.0
        # copy_var.set_value(3.0, override_all_policies=True)

    # SAVE
    if is_save == True:
        repo.save(matrix)
    # ASSERT SET AND PULL CORRECT
    result, debug = compare_objects(e_atlas_deepcopy, e_interface.atlas_entity)  # type: ignore . we know atlas is attached

    assert result == True, debug


####################

# TESTS


def test_exchanger_equipments():
    # Logic
    equipments = [
        (at.Heater, ma.DWSimHeaterInterface),
        (at.Cooler, ma.DWSimCoolerInterface),
        (at.HeatExchanger, ma.DWSimHeatExchangerInterface),
    ]

    for AtlasEquipment, EquipmentInterface in equipments:
        _test_endpoint_roundtrip(AtlasEquipment, EquipmentInterface)


def test_exchanger_equipments_aircooler_only():
    """
    SEPERATE TEST THAT WILL ONLY PASS AFTER ENERGY STREAM IS SOLVED
    """
    # Logic
    equipments = [(at.AirCooler2, ma.DWSimAirCooler2Interface)]
    for AtlasEquipment, EquipmentInterface in equipments:
        _test_endpoint_roundtrip(AtlasEquipment, EquipmentInterface)


def test_pressure_changer_equipment():
    equipments = [
        (at.OrificePlate, ma.DWSimOrificePlateInterface),
        (at.Compressor, ma.DWSimCompressorInterface),
        (at.Pump, ma.DWSimPumpInterface),
        (at.Expander, ma.DWSimExpanderInterface),
        (at.Valve, ma.DWSimValveInterface),
    ]
    for AtlasEquipment, EquipmentInterface in equipments:
        _test_endpoint_roundtrip(AtlasEquipment, EquipmentInterface)


def z_test_mixer_and_splitter():
    equipments = [
        # (at.StreamSplitter, ma.DWSimSplitterInterface),
        (at.StreamMixer, ma.DWSimMixerInterface),
    ]
    for AtlasEquipment, EquipmentInterface in equipments:
        _test_endpoint_roundtrip(AtlasEquipment, EquipmentInterface)


def test_seperators():
    equipments = [
        (at.Vessel, ma.DWSimVesselInterface),
    ]
    for AtlasEquipment, EquipmentInterface in equipments:
        _test_endpoint_roundtrip(AtlasEquipment, EquipmentInterface)


def test_columns():
    equipments = [
        (at.ShortcutColumn, ma.DWSimShortcutColumnInterface)
        # (at.DistillationColumn, dw.DWSimDistillationColumnManager),
    ]
    for AtlasEquipment, EquipmentInterface in equipments:
        _test_endpoint_roundtrip(AtlasEquipment, EquipmentInterface)


def test_reactors():
    equipments = [
        (at.ConversionReactor, ma.DWSimConversionReactorInterface),
        # (at.DistillationColumn, dw.DWSimDistillationColumnManager),
    ]
    for AtlasEquipment, EquipmentInterface in equipments:
        _test_endpoint_roundtrip(AtlasEquipment, EquipmentInterface, True)

def test_shortcutcolumn_setting_compounds():
    title = f"test_matrix_eqpt-shortcutcol_2"

    atlas = at.AtlasRoot("pmodel-1", "plant-1")
    # Register Reactions
    reactions = []
    for reaction in [
        at.CASCompoundEnum.Water,
        at.CASCompoundEnum.CarbonDioxide,
        at.CASCompoundEnum.Ethane,
        at.CASCompoundEnum.Propane,
    ]:
        reactions.append(at.CompoundRef.get_vocompound_from_enum(reaction))
        atlas.register_compound(reactions[-1])
    
    atlas.propogate_compounds_across_streams() 
    atlas.propogate_vars_to_uuid_collection()

    matrix = ma.MatrixDWSim(title)
    matrix.attach_model(atlas)
    matrix._setup_endpoint()
    
    e_id = f"ShortcutColumn-01"
    entity = atlas.add_equipment(at.ShortcutColumn, e_id)
    
    # Set discvar for light and heavyky
    entity.set_value(at.DiscreteSetSpecEnum.LightKeyCompound, reactions[0])
    entity.set_value(at.DiscreteSetSpecEnum.HeavyKeyCompound, reactions[1])
    
    # Setup matrix
    e_interface = ma.DWSimShortcutColumnInterface(entity, matrix)
    e_interface.push_to_endpoint()
    
    repo.save(matrix)
    
    
    



# if __name__ == "__main__":
#     test_shortcutcolumn_setting_compounds()
