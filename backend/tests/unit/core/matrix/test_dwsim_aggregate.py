####################
import time
import math
import copy
import logging
import os
import random
from contextlib import contextmanager
from datetime import datetime, timedelta
from pathlib import Path
from typing import (
    Any,
    Callable,
    Dict,
    Generator,
    List,
    Optional,
    Tuple,
    Type,
    Union,
    TypeVar,
)
from pprint import pformat
import numpy as np
import pandas as pd
import pytest

import backend.core._sharedutils.Utilities as sharedutils
import backend.core._atlas._singletons
import backend.core._atlas.aggregates as at

import backend.core._matrix.matrix_dwsim as ma
import backend.application.usecase_templates as eg
import backend.infrastructure._db.repo as re

# SETUP

MODEL_ID = "process_model_1"
RELATIVE_ARTEFACT_FOLDERPATH = "backend/infrastructure/artefacts/bin/dwsim"
TEMPLATE_NAME = "Empty_Template.dwxmz"


####################

# Helper Functions


def compare_objects(
    obj1: at.ENTBase,
    obj2: at.ENTBase,
    attrs: Optional[List[str]] = None,
) -> <PERSON>ple[bool, Optional[str]]:
    """
    Compares two objects attribute by attribute and returns detailed comparison results.
    Handles CollectionRouter and Collection abstractions.
    """
    logging.info(f"COMPARING: {obj1.label}, {obj2.label}")

    # Get collections from both objects
    collections1 = obj1.get_collections()
    collections2 = obj2.get_collections()

    # Compare collection contents
    for c1, c2 in zip(collections1, collections2):

        logging.info(f"...collection comparison: {c1}, {c2}")
        if c1.collection != c2.collection:
            return (
                False,
                f"Collections differ: \nc1: {c1.collection}\nc2: {c2.collection}",
            )

    return True, "Values are equal"



def setup_atlas_w_materialstream():
    """Returns atlas, entity and a deepcopy of the entity"""

    # Create Root
    atlas = at.AtlasRoot("pmodel-1", "willywonka-1")

    # Create generic equipment
    eqp1 = atlas.add_equipment(at.Heater, "HT-01")
    eqp2 = atlas.add_equipment(at.Heater, "HT-02")

    # Create material stream
    compound1 = atlas.retreive_compound(at.CASCompoundEnum.Water)
    compound2 = atlas.retreive_compound(at.CASCompoundEnum.CarbonDioxide)
    stream1 = at.ENTBaseStream("S-1")
    stream1.add_variable(at.VOCompoundMassRatio(compound1, 0.5))
    stream1.add_variable(at.VOCompoundMassRatio(compound2, 0.5))
    stream1.equalize_compoundmassmix()

    # Join things up
    atlas.add_stream(eqp1, eqp2, stream1)
    return atlas


def setup_matrix_root(atlas_ref: at.AtlasRoot, filename="WillyWonka"):
    matrix = ma.MatrixDWSim(filename)
    matrix.attach_model(atlas_ref)
    matrix._setup_endpoint()
    return matrix


def setup_interface_object_materialstream(
    matrix_root: ma.MatrixDWSim, atlas_materialstream: at.ENTBaseStream
):
    # Create a standalone atlas

    e_interface = ma.DWSimMaterialStreamInterface(atlas_materialstream, matrix_root)
    return e_interface


####################

# TESTS
repo = re.FolderRepoForDWSim("backend/artefacts/bin/dwsim", "aggregate_tests")


def test_create_connection():

    # Create Atlas
    equipment = at.AirCooler2
    atlas = at.AtlasRoot("chocolate-frosties-1", "willywonka-plant-1")
    up = atlas.add_equipment(equipment, "HT-1")
    down = atlas.add_equipment(equipment, "HT-2")

    # REgister compounds
    compound1 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Water)
    compound2 = at.CompoundRef.get_vocompound_from_enum(
        at.CASCompoundEnum.CarbonDioxide
    )
    atlas.register_compound(compound1)
    atlas.register_compound(compound2)

    # Stream
    stream = at.ENTBaseStream("S-1")
    stream.add_variable(at.VOCompoundMassRatio(compound1, 0.6))
    stream.add_variable(at.VOCompoundMassRatio(compound2, 0.4))
    stream.equalize_compoundmassmix()
    atlas.add_stream(up, down, stream)

    # Create Matrix & Interfaces
    matrix = setup_matrix_root(atlas, "AirCoolerTest")

    i1 = ma.DWSimAirCooler2Interface(up, matrix)
    i2 = ma.DWSimAirCooler2Interface(down, matrix)
    s1 = ma.DWSimMaterialStreamInterface(stream, matrix)

    for item in [i1, i2]:
        item.push_to_endpoint()
        matrix._add_interface(item)

    s1.push_to_endpoint()
    matrix._add_interface(s1)

    # Connect Items
    matrix._connect_objects(up.label, down.label, stream.label)
    repo.save(matrix)


def test_run_single_compound():
    # Create Atlas
    atlas = at.AtlasRoot("chocolate-frosties-1", "willywonka-plant-1")

    battery_in = atlas.add_equipment(at.BatteryIn, "BatteryIN-1")
    equipment = atlas.add_equipment(at.Pump, "PMP-1")
    battery_out = atlas.add_equipment(at.BatteryOut, "BatteryOut-1")
    equipment.set_value(at.ContVarSpecEnum.PressureIncrease, 201325)

    # Register compounds
    compound1 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Methanol)
    compound2 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Ethanol)
    atlas.register_compound(compound1)
    atlas.register_compound(compound2)

    # Stream
    stream = atlas.add_stream(battery_in, equipment)
    stream.add_variable(at.VOCompoundMassRatio(compound1, 1.0))
    stream.set_value(at.ContVarSpecEnum.Mass_flow_rate, 10.0)
    # stream.add_variable(at.VOCompoundMassRatio(compound2, 0.4))
    # stream.equalize_compoundmassmix()
    stream2 = atlas.add_stream(equipment, battery_out)

    # Create Matrix
    matrix = setup_matrix_root(atlas, "stream_pump_test")

    # Create interfaces
    i1 = ma.DWSimPumpInterface(equipment, matrix)
    s1 = ma.DWSimMaterialStreamInterface(stream, matrix)
    s2 = ma.DWSimMaterialStreamInterface(stream2, matrix)

    for interface in [i1, s1, s2]:
        assert isinstance(
            interface, (ma.DWSimStreamInterface, ma.DWSimEquipmentInterface)
        )
        matrix._add_interface(interface)
        interface.push_to_endpoint()

    # Connect objects
    matrix._connect_objects(None, equipment.label, stream.label)
    matrix._connect_objects(equipment.label, None, stream2.label)

    # Run and save
    matrix._handle_run_model()
    repo.save(matrix)


def test_run_2compound():
    # Create Atlas
    atlas = at.AtlasRoot("chocolate-frosties-1", "willywonka-plant-1")

    battery_in = atlas.add_equipment(at.BatteryIn, "BatteryIN-1")
    equipment = atlas.add_equipment(at.Pump, "PMP-1")
    battery_out = atlas.add_equipment(at.BatteryOut, "BatteryOut-1")
    equipment.set_value(at.ContVarSpecEnum.PressureIncrease, 201325)

    # Register compounds
    compound1 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Methanol)
    compound2 = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Ethanol)
    atlas.register_compound(compound1)
    atlas.register_compound(compound2)

    # Stream
    stream = atlas.add_stream(battery_in, equipment)
    stream.add_variable(at.VOCompoundMassRatio(compound1, 0.2))
    stream.add_variable(at.VOCompoundMassRatio(compound2, 0.8))
    stream2 = atlas.add_stream(equipment, battery_out)

    # Create Matrix
    matrix = setup_matrix_root(atlas, "stream_pump_test_2compound")

    # Create interfaces
    i1 = ma.DWSimPumpInterface(equipment, matrix)
    s1 = ma.DWSimMaterialStreamInterface(stream, matrix)
    s2 = ma.DWSimMaterialStreamInterface(stream2, matrix)

    for interface in [i1, s1, s2]:
        assert isinstance(
            interface, (ma.DWSimStreamInterface, ma.DWSimEquipmentInterface)
        )
        matrix._add_interface(interface)
        interface.push_to_endpoint()

    # Connect objects
    matrix._connect_objects(None, equipment.label, stream.label)
    matrix._connect_objects(equipment.label, None, stream2.label)

    # Run and save
    matrix._handle_run_model()
    repo.save(matrix)



def test_case_industrial_gas_boiler():
    atlas =eg.industrial_natural_gas_boiler_with_preheating_trains()
    matrix = ma.MatrixDWSim("industiral_natural_gas_boiler")

    # Call Protocol methods
    matrix.attach_model(domain_model=atlas, share_model_state=True)
    matrix.setup_matrix()
    matrix.run_matrix()

    # Save to check
    repo.save(matrix)


def test_SWANCOR_model_1_HE_1_Topology():
    atlas = eg.SWANCOR_model_1_HE_1()
    matrix = ma.MatrixDWSim("SWANCOR_model_1_HE_1")

    # Call Protocol methods
    matrix.attach_model(domain_model=atlas, share_model_state=True)
    matrix.setup_matrix()
    matrix.run_matrix()

    # Save to check
    repo.save(matrix)


def test_SWANCOR_model_2_HE_2_Topology():
    atlas = eg.SWANCOR_model_2_HE_2()
    matrix = ma.MatrixDWSim("SWANCOR_model_2_HE_2")

    # Call Protocol methods
    matrix.attach_model(domain_model=atlas, share_model_state=True)
    matrix.setup_matrix()
    matrix.run_matrix()

    # Save to check
    repo.save(matrix)


def test_SWANCOR_model_3_HE_3_Topology():
    atlas = eg.SWANCOR_model_3_HE_3()
    matrix = ma.MatrixDWSim("SWANCOR_model_3_HE_3")

    # Call Protocol methods
    matrix.attach_model(domain_model=atlas, share_model_state=True)
    matrix.setup_matrix()
    matrix.run_matrix()

    # Save to check
    repo.save(matrix)
