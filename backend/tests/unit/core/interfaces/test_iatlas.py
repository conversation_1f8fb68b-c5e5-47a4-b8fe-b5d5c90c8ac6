from backend.tests._imports import *

from sqlalchemy.engine.base import Engine
import backend.core as core
from backend.infrastructure.adaptors.atlas_adaptors import *
from backend.core.interfaces.atlas_interfaces import AtlasBaseError, AtlasLookupError, AlephDomainError, AlephProcessingError, AtlasGeneralError, AtlasAdaptorError, AlephValidationError

example_files = [
    eg.pump_use_case(),
    eg.heat_exchanger_use_case(),
    eg.industrial_natural_gas_boiler_with_preheating_trains(),
]

class TestStaticMethods_HappyPath_w_UseCases:
    """
    Tests for static methods. 
    - get_kpi
    - get_variable
    - get_ui_label
    """
    @staticmethod
    def test_get_kpi():
        def _test(atlas: core.at.AtlasRoot):

            # Add vars
            for var in atlas.variables_collection.items:
                title = f"{var.parent.label}_{core.IAtlas.get_ui_label(atlas, obj=var)}_kpi"
                expression = f"{{{{ {var.parent.label} - {core.IAtlas.get_ui_label(atlas, obj=var)}}}}}"
                kpi = core.at.KPI(title,expression, atlas.variables_collection)
                atlas.kpi_collection.add_item(kpi)
            
            atlas.kpi_collection._refresh_cache()
            #baseline

            kpi_set= set(atlas.kpi_collection.items)
            
            # test get KPI, uuid
            kpis = {
                core.IAtlas.get_kpi(atlas, uid = kpi.uid)
                for kpi in atlas.kpi_collection.items
            }
            
            assert kpis ==  kpi_set

            kpis_2= {
                core.IAtlas.get_kpi(atlas, ui_label = core.IAtlas.get_ui_label(atlas, obj=kpi))
                for kpi in atlas.kpi_collection.items
            }
            
            # pprint(kpis_2)
            # pprint(kpi_set)
            assert kpis_2 == kpi_set 

            print(f"\n############\nAtlas: {atlas.label},\n\n{pformat(kpis)}")
            print(f"\n############\nAtlas: {atlas.label},\n\n{pformat(kpis_2)}")
            
        for atlas in example_files:
            _test(atlas)

        print("Test Done")

    @staticmethod
    def test_get_ui_label():
        def _test(atlas: core.at.AtlasRoot):
            
            ui_labels = [
                f"{var.parent.label}_{core.IAtlas.get_ui_label(atlas, obj=var)}"
                for var in atlas.variables_collection.items
            ]
            
            print(f"\n############\nAtlas: {atlas.label},\n\n{pformat(ui_labels)}")
        for atlas in example_files:
            _test(atlas)

        print("Test Done")
        
    @staticmethod
    def test_get_variable():

        def _test(atlas: core.at.AtlasRoot):
            # Test get via UUID
            variable_uuids = [
                var.uid
                for var in atlas.variables_collection.items
            ]
            
            vars = {
                core.IAtlas.get_variable(atlas, uid = uid)
                for uid in variable_uuids
            }
            
            assert vars == set(atlas.variables_collection.items)
            
            print(f"\n############\nAtlas: {atlas.label},\n\n{pformat(vars)}")
            # Test via Tuple
            var_tuples = [
                (var.parent.label, core.IAtlas.get_ui_label(atlas, obj =var))
                for var in atlas.variables_collection.items
            ]

            vars = {
                core.IAtlas.get_variable(atlas, ui_tuple = tup)
                for tup in var_tuples
            }

            assert vars == set(atlas.variables_collection.items)

            print(f"\n############\nAtlas: {atlas.label},\n\n{pformat(vars)}")

        for atlas in example_files:
            _test(atlas)

        print("Test Done")




class TestStaticMethods_UnhappyPaths:
    """Tests for error handling in IAtlas static methods"""
    
    @staticmethod
    def test_get_kpi_errors():
        """Test error cases for get_kpi method"""
        atlas = example_files[0]  # Get a test atlas
        
        # Case 1: Both parameters provided
        with pytest.raises(AttributeError):
            core.IAtlas.get_kpi(atlas, ui_label="some_label", uid=uuid.uuid4())
        
        # Case 2: Neither parameter provided
        with pytest.raises(AttributeError):
            core.IAtlas.get_kpi(atlas)
        
        # Case 3: Invalid UI label
        with pytest.raises(AtlasLookupError):
            core.IAtlas.get_kpi(atlas, ui_label="nonexistent_kpi")
        
        # Case 4: Invalid UUID
        random_uid = uuid.uuid4()
        with pytest.raises(AtlasLookupError):
            core.IAtlas.get_kpi(atlas, uid=random_uid)
        
        # Case 5: UUID is a string not a UUID object
        uid_str = str(uuid.uuid4())
        with pytest.raises(TypeError):
            core.IAtlas.get_kpi(atlas, uid=uid_str) # type: ignore 

    @staticmethod
    def test_get_variable_errors():
        """Test error cases for get_variable method"""
        atlas = example_files[0]  # Get a test atlas
        
        # Case 1: Both parameters provided
        with pytest.raises(AttributeError):
            core.IAtlas.get_variable(atlas, ui_tuple=("entity", "var"), uid=uuid.uuid4())
        
        # Case 2: Neither parameter provided
        with pytest.raises(AttributeError):
            core.IAtlas.get_variable(atlas)
        
        # Case 3: Invalid ui_tuple format (not a tuple)
        with pytest.raises(TypeError):
            core.IAtlas.get_variable(atlas, ui_tuple="not-a-tuple")# type: ignore
        
        # Case 4: Invalid ui_tuple format (wrong length)
        with pytest.raises(TypeError):
            core.IAtlas.get_variable(atlas, ui_tuple=("single_value",))# type: ignore
        
        # Case 5: Invalid entity label in ui_tuple
        with pytest.raises(AtlasBaseError):  # Could be AtlasLookupError or other subclass
            core.IAtlas.get_variable(atlas, ui_tuple=("nonexistent_entity", "var"))
        
        # Case 6: Invalid variable label in ui_tuple (assuming we have a valid entity)
        # Find a valid entity first
        valid_entity = None
        for var in atlas.variables_collection.items:
            valid_entity = var.parent.label
            if valid_entity:
                break
        
        if valid_entity:
            with pytest.raises(AtlasBaseError):  # Could be AtlasLookupError 
                core.IAtlas.get_variable(atlas, ui_tuple=(valid_entity, "nonexistent_var"))
        
        # Case 7: UUID is not UUID object
        with pytest.raises(TypeError):
            core.IAtlas.get_variable(atlas, uid="not-a-uuid")# type: ignore
        
        # Case 8: Invalid UUID
        random_uid = uuid.uuid4()
        with pytest.raises(AtlasBaseError):  # AtlasLookupError is subclass of AtlasError
            core.IAtlas.get_variable(atlas, uid=random_uid)

    @staticmethod
    def test_get_ui_label_errors():
        """Test error cases for get_ui_label method"""
        atlas = example_files[0]  # Get a test atlas
        
        # Case 1: Object is neither KPI nor variable
        with pytest.raises(TypeError):
            core.IAtlas.get_ui_label(atlas, obj="not-a-valid-object")# type: ignore
        
# Ensure proper cleanup if running as main script
if __name__ == "__main__":
    pass
    # check_connection()