import pytest
from backend.core._atlas.aggregates import *
from backend.core._atlas.aggregates import VariableUIDCollection, KPIUIDCollection


def create_test_kpi(
    expression: str = "{{HT-01 - Heat Added Or Removed}} + {{PM01 - Power Required}}",
):
    """Create test KPI with standard equipment setup"""
    # Create equipment with parent reference
    test_heater = Heater("HT-01")
    test_pump = Pump("PM01")

    # Initialize collection
    var_collection = VariableUIDCollection()

    # Add variables and ensure parent references
    for var in test_heater.get_variables(filter=[VarCollectionContinuous]):
        var.parent = test_heater  # Set parent reference
        var_collection.add_item(var)

    for var in test_pump.get_variables(filter=[VarCollectionContinuous]):
        var.parent = test_pump  # Set parent reference
        var_collection.add_item(var)

    return (KPI("test", expression, var_collection), var_collection)


def test_transform_uid_str_to_val():
    kpi, var_collection = create_test_kpi()
    var = next(iter(var_collection.items))
    val = kpi._transform_uid_str_to_val(str(var.uid))
    assert val == str(var.value)


def test_transform_uid_str_to_kpi_label():
    kpi, var_collection = create_test_kpi()
    heater_var = next(
        var for var in var_collection.items if var.parent.label == "HT-01"
    )
    label = kpi._transform_uid_str_to_kpi_label(str(heater_var.uid))
    assert label == f"HT-01{kpi.delimiter}{KPI.get_var_label(heater_var)}"
    logging.info(label)


def test_transform_kpi_label_to_uuid_label():
    kpi, var_collection = create_test_kpi()
    var = next(var for var in var_collection.items if var.parent.label == "HT-01")
    kpi_label = f"HT-01{kpi.delimiter}{KPI.get_var_label(var)}"
    uid_str = kpi._transform_kpi_label_to_uuid_label_and_store_UUID(kpi_label)
    assert UUID(uid_str) == var.uid
    assert var.uid in kpi._variables


def test_get_uuid_expression():
    kpi, _ = create_test_kpi()
    uuid_expr = kpi.get_uuid_expression()
    for var_uid in kpi._variables:
        assert str(var_uid) in uuid_expr
    logging.info(uuid_expr)


def test_get_ui_expression():
    expression = "{{HT-01 - Heat Added Or Removed}} + {{PM01 - Power Required}}"
    kpi, _ = create_test_kpi(expression)
    assert kpi.get_ui_expression() == expression


def test_get_kpi_value():
    kpi, _ = create_test_kpi()
    value = kpi.get_kpi_value()
    assert isinstance(value, (int, float))
    logging.info(value)
