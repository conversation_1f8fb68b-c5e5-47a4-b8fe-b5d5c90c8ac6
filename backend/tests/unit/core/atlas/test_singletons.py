import pytest

from backend.core._atlas._singletons import CASCompoundEnum
import backend.core._atlas._singletons as singletons


def test_get_enum_by_name():
    enum = singletons.get_enum_by_name(singletons.CASCompoundEnum, "Methane")
    assert enum == singletons.CASCompoundEnum.Methane


def test_get_enum_by_value():
    Methane = "74-82-8"
    enum = singletons.get_enum_by_value(singletons.CASCompoundEnum, Methane)
    assert enum == singletons.CASCompoundEnum.Methane


def test_mixin_enum_to_str_w_override():
    name = CASCompoundEnum.Methane.stringify
    assert name == "74-82-8"


def test_mixin_enum_from_str_w_override():
    enum_instance = CASCompoundEnum.from_stringify("74-82-8")
    assert enum_instance == CASCompoundEnum.Methane
