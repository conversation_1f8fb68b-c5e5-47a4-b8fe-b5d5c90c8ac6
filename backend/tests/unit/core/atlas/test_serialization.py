import numpy as np
import pandas as pd
import backend.application.usecase_templates as eg
import backend.core._atlas.aggregates as at
import pytest
import logging
from typing import Any, Optional
from pprint import pprint, pformat
import uuid


def get_var_name(atlas: at.AtlasRoot, uuid_str: str) -> Optional[str]:
    try:
        var = atlas.variables_collection.get_item(uuid.UUID(uuid_str))
        entity_label = var.parent.label
        var_label = atlas.variables_collection.get_ui_label(var)
        return f"{entity_label}_{var_label}"
    except (KeyError, AttributeError):
        return None


def test_serialize_uuid():
    atlas = eg.heat_exchanger_use_case()
    equipment = atlas.get_equipment(by_label="HEX-100")
    vars = equipment.get_variables(filter=[at.VarCollectionContinuous])

    # Test pandas serialization
    df = atlas.serialize_variables(vars)

    # Get column UUIDS
    pandas_uuids = df.columns.to_list()
    result = {}
    for sample in pandas_uuids:
        var_name = None
        try:
            var_name = get_var_name(atlas, sample)
        except:
            pass

        result[sample] = var_name

    found_count = len([x for x in result.values() if x is not None])
    pprint(result)
    assert found_count == len(result), result


def test_serialize_default():
    atlas = eg.heat_exchanger_use_case()
    equipment = atlas.get_equipment(by_label="HEX-100")
    vars = equipment.get_variables(filter=[at.VarCollectionContinuous])

    # Test pandas serialization
    df = atlas.serialize_variables(vars)
    assert isinstance(df, pd.DataFrame)
    assert df.shape[0] == 1
    assert len(df.columns) == len(vars)

    # Test round trip with pandas, making sure UUIDs are safe
    original_values = {var.uid: var.value for var in vars}
    atlas.deserialize_values(df)
    for var in vars:
        assert var.value == original_values[var.uid]


def test_serialize_variables_pandas():
    atlas = eg.heat_exchanger_use_case()
    equipment = atlas.get_equipment(by_label="HEX-100")
    vars = equipment.get_variables(filter=[at.VarCollectionContinuous])

    # Test pandas serialization
    df = atlas.serialize_variables(vars, output_type="pandas")
    assert isinstance(df, pd.DataFrame)
    assert df.shape[0] == 1
    assert len(df.columns) == len(vars)

    # Test round trip with pandas, making sure UUIDs are safe
    original_values = {var.uid: var.value for var in vars}
    atlas.deserialize_values(df)
    for var in vars:
        assert var.value == original_values[var.uid]


def test_serialize_variables_numpy():
    atlas = eg.heat_exchanger_use_case()
    equipment = atlas.get_equipment(by_label="HEX-100")
    vars = equipment.get_variables(filter=[at.VarCollectionContinuous])

    # Test numpy serialization
    uids, values = atlas.serialize_variables(vars, output_type="numpy")
    assert isinstance(uids, np.ndarray)
    assert isinstance(values, np.ndarray)
    assert len(uids) == len(vars)
    assert len(values) == len(vars)

    # Test round trip with numpy
    original_values = {var.uid: var.value for var in vars}
    atlas.deserialize_values((uids, values))
    for var in vars:
        assert var.value == original_values[var.uid]


def test_serialize_bounds_pandas():
    atlas = eg.heat_exchanger_use_case()
    equipment = atlas.get_equipment(by_label="HEX-100")
    vars = equipment.get_variables(filter=[at.VarCollectionContinuous])

    # Test pandas serialization
    df = atlas.serialize_bounds(vars, output_type="pandas")
    assert isinstance(df, pd.DataFrame)
    assert df.shape[0] == 1
    assert len(df.columns) == len(vars)

    # Verify bounds structure
    original_bounds = {var.uid: var.bounds for var in vars}
    atlas.deserialize_bounds(df)
    for var in vars:
        assert var.bounds == original_bounds[var.uid]


def test_serialize_bounds_numpy():
    atlas = eg.heat_exchanger_use_case()
    equipment = atlas.get_equipment(by_label="HEX-100")
    vars = equipment.get_variables(filter=[at.VarCollectionContinuous])

    # Test numpy serialization
    uids, bounds = atlas.serialize_bounds(vars, output_type="numpy")
    assert isinstance(uids, np.ndarray)
    assert isinstance(bounds, np.ndarray)
    assert len(uids) == len(vars)
    assert bounds.shape[0] == len(vars)
    assert bounds.shape[1] == 2  # Lower and upper bounds

    # Test round trip
    original_bounds = {var.uid: var.bounds for var in vars}
    atlas.deserialize_bounds((uids, bounds))
    for var in vars:
        assert var.bounds == original_bounds[var.uid]
