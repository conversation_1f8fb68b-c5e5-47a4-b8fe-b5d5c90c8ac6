import pprint
from pprint import pformat
import pytest
import math
from ordered_set import OrderedSet

from backend.core._atlas._singletons import (
    _DiscreteItemSpecification,
    DiscreteItemSpecEnum,
    DiscreteSetSpecEnum,
    ContVarSpecEnum,
)
from backend.core._atlas.entities import *
from backend.core._atlas.valueobjects import *


def test_material_stream_initialization():
    """Test material stream initialization and defaults"""
    compounds = [VOCompound("C1"), VOCompound("C2")]
    stream = ENTBaseStream(
        "TestStream-00",
        compounds=compounds,
    )

    # Test compound initialization
    assert len(stream.compounds) == 2


def test_stream_compoundmix_management():
    """Test compound addition and ratio management"""
    stream = ENTBaseStream("TestStream-00")

    # Test adding compounds
    c1 = VOCompound("C1")
    c2 = VOCompound("C2")
    stream.add_variable(VOCompoundMassRatio(c1, 0.4))
    stream.add_variable(VOCompoundMassRatio(c2, 0.6))

    assert len(stream.compounds) == 2

    # Get collection and verify ratios
    collection = stream._collections.get_collection(
        collection_type=VarCollectionCompoundMassRatio
    )
    assert math.isclose(sum(var.value for var in collection.items), 1.0)

    # Assert set value by compound key
    stream.set_value(c1, 0.9)
    massmix = sum(var.value for var in collection.items)
    logging.info(massmix)
    assert massmix > 1.0


def test_property_defaults_stream():
    """Test material stream property defaults"""
    stream = ENTBaseStream("TestStream-00")

    # Test all property defaults dynamically
    for var in ENTBaseStream.CONTVAR_DEFAULTS:
        value = stream.get_value(var.variable_enum)
        assert value == var.value
        assert stream.get_bounds(var.variable_enum) == var.bounds


def validate_parameter_state(
    entity: ENTBase,
    discrete_var: DiscreteSetSpecEnum,
    discrete_var_val: DiscreteItemSpecEnum,
) -> Tuple[bool, dict]:
    """
    Validates that parameters are correctly toggled when a discrete variable is set.

    Args:
        entity: The equipment/stream entity to validate
        discrete_var: The discrete variable that was set
        discrete_var_val: The value it was set to

    Returns:
        tuple[bool, dict]: (passed validation, detailed validation results)
    """
    # Verify discrete variable was set correctly
    actual_val = entity.get_value(discrete_var)
    if actual_val != discrete_var_val:
        return False, {
            "error": f"Discrete variable {discrete_var} expected {discrete_var_val}, got {actual_val}"
        }

    # Get toggle specification
    specification: _DiscreteItemSpecification = discrete_var_val.value
    validation_results = {}
    passed = True

    if specification is None:
        return True, {"info": "No default specs"}

    if not specification.toggle_dep and not specification.toggle_indep:
        return True, {"info": "No toggle specifications defined"}

    # Validate dependent parameters are not independent
    if specification.toggle_dep:
        for param in specification.toggle_dep:
            var = entity.get_variable(param)
            if var is None:
                validation_results[param] = (None, "Parameter not found")
                passed = False
                continue

            is_indep = var.is_independent
            is_valid = not is_indep  # Should NOT be independent
            validation_results[param] = {
                "discrete_var": discrete_var,
                "current": is_indep,
                "expected": False,
                "valid": is_valid,
            }
            passed = passed and is_valid

    # Validate independent parameters are independent
    if specification.toggle_indep:
        for param in specification.toggle_indep:
            var = entity.get_variable(param)
            if var is None:
                validation_results[param] = (None, "Parameter not found")
                passed = False
                continue

            is_indep = var.is_independent
            is_valid = is_indep  # Should be independent
            validation_results[param] = {
                "valid": is_valid,
                "current": is_indep,
                "expected": True,
            }
            passed = passed and is_valid

    # Log validation results with context
    logging.debug(
        f"Parameter state validation for {entity.__class__.__name__}:\n"
        f"Discrete var: {discrete_var}\n"
        f"Value: {discrete_var_val}\n"
        f"Results:\n{pformat(validation_results, indent=2)}"
    )
    return passed, validation_results


def test_equipment_initialization():
    """Test property defaults for all equipment types"""
    equipment_classes = [
        Heater,
        Cooler,
        HeatExchanger,
        AirCooler2,
        OrificePlate,
        Compressor,
        Pump,
        Expander,
        Valve,
        StreamMixer,
        # StreamSplitter,
        Vessel,
        ShortcutColumn,
        BatteryIn,
        BatteryOut,
        ConversionReactor,
        Crystallizer,
        TemperatureControl,
        SimulationDuration,
        PositionSensitiveDetector,
    ]

    equipment_vars = {}
    for equipment_class in equipment_classes:
        equipment: ENTBase = equipment_class(f"test_{equipment_class.__name__}")

        # Test all contvar defaults
        for var in equipment_class.CONTVAR_DEFAULTS:
            value = equipment.get_value(var.variable_enum)
            assert value == var.value
            assert equipment.get_bounds(var.variable_enum) == var.bounds

        # Test all discrete set defaults
        for var in equipment_class.DISCRETESET_DEFAULTS:
            value = equipment.get_value(var.variable_enum)
            assert value == var.value
            assert equipment.get_bounds(var.variable_enum) == var.bounds

            # Test state
            if var.value == None or not isinstance(var.value, DiscreteItemSpecEnum):
                continue
            is_pass, logs = validate_parameter_state(
                equipment, var.variable_enum, var.value
            )  # ensure its following default state

            assert (
                is_pass == True
            ), f"Failed state toggles: {equipment_class}\nState logs:\n{pformat(logs, indent=2, width=80)}"

        # Collect variables for logging
        equipment_vars[equipment_class.__name__] = {
            "ContVars": {
                var.variable_enum.name: equipment.get_value(var.variable_enum)
                for var in equipment_class.CONTVAR_DEFAULTS
            },
            "DiscreteSet": {
                var.variable_enum.name: equipment.get_value(var.variable_enum)
                for var in equipment_class.DISCRETESET_DEFAULTS
            },
        }

    logging.info(
        "\nEquipment Variables Summary:\n"
        + pformat(equipment_vars, indent=2, width=120)
    )


def _test_equipment_toggling(
    Equipment: Type[ENTBaseEquipment],
    discreteset: DiscreteSetSpecEnum,
    selection: DiscreteItemSpecEnum,
):
    entity = Equipment("test")
    entity.set_value(discreteset, selection)

    # assert indeps based on param are expected
    expected_independent: Tuple[ContVarSpecEnum, ...] = (
        selection.get_specification().toggle_indep
    )
    expected_dependent: Tuple[ContVarSpecEnum, ...] = (
        selection.get_specification().toggle_dep
    )

    for var in expected_independent:
        assert entity.get_variable(var).is_independent == True
    for var in expected_dependent:
        assert entity.get_variable(var).is_independent == False

    variables = entity.get_variables()
    logging.info(
        f"Indep Variables: {pformat([var for var in variables if var.is_independent and isinstance(var, VOContinuousVariable)])}"
    )
    logging.info(
        f"Dep Variables: {pformat([var for var in variables if not var.is_independent and isinstance(var, VOContinuousVariable)])}"
    )


def test_equipment_state_independence():
    equipments = [HeatExchanger]
    _test_equipment_toggling(
        HeatExchanger,
        DiscreteSetSpecEnum.CalculationType,
        DiscreteItemSpecEnum.CalculateOutletTemperatures_UA,
    )
    _test_equipment_toggling(
        Crystallizer,
        DiscreteSetSpecEnum.CR_VolumeSpecification,
        DiscreteItemSpecEnum.CR_SpecifiedHeightAndDiameter,
    )
    _test_equipment_toggling(
        TemperatureControl,
        DiscreteSetSpecEnum.TemperatureControlOptimizationMode,
        DiscreteItemSpecEnum.TC_OptimizationModeOn,
    )


def _test_equipment_dependent_vars(
    Equipment: Type[ENTBaseEquipment],
    selection_changes: List[Tuple[DiscreteSetSpecEnum, DiscreteItemSpecEnum]],
    cont_vars: List[ContVarSpecEnum],
):
    """Helper function to verify variables maintain dependent status across state transitions.

    Applies a sequence of discrete selection changes to an equipment instance and verifies
    that specified continuous variables remain in dependent status throughout all
    state transitions.

    Args:
        Equipment: Equipment class to instantiate and test
        selection_changes: List of (discrete variable, value) pairs representing
            state changes to apply sequentially
        cont_vars: List of continuous variables that should remain dependent
            regardless of state changes

    Example:
        >>> _test_equipment_dependent_vars(
        ...     TemperatureControl,
        ...     [(DiscreteSetSpecEnum.TemperatureControlProfile,
        ...       DiscreteItemSpecEnum.TC_TimeInvariant)],
        ...     [ContVarSpecEnum.HeatInput_TC]
        ... )
    """

    equipment = Equipment("test")

    for discrete_set, selection in selection_changes:
        if selection == discrete_set.value:
            continue
        equipment.set_value(discrete_set, selection)
        for cont_var in cont_vars:
            assert equipment.get_variable(cont_var).is_independent == False


def test_equipment_state_dependence():
    """Verify equipment variables maintain their dependent status across state changes.

    Tests that specific variables (like outputs and KPIs) remain dependent regardless
    of equipment state changes. This ensures these variables cannot be incorrectly
    set as independent inputs.
    """
    _test_equipment_dependent_vars(
        TemperatureControl,
        [
            (
                DiscreteSetSpecEnum.TemperatureControlProfile,
                DiscreteItemSpecEnum.TC_TimeInvariant,
            ),
            (
                DiscreteSetSpecEnum.TemperatureControlProfile,
                DiscreteItemSpecEnum.TC_PiecewiseLinear,
            ),
        ],
        [
            ContVarSpecEnum.HeatInput_TC,
            ContVarSpecEnum.TemperatureMeasured_TC,
        ],
    )
