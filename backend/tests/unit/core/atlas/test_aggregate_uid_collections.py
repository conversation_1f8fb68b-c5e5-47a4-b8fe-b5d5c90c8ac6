import pytest
from collections import Counter
from backend.core._atlas._base import UUIDBase
from backend.core._atlas.aggregates import *
from backend.core._atlas.aggregates import VariableUIDCollection, _AbstractUIDCollection
import backend.core._atlas.aggregates as at
import backend.application.usecase_templates as eg


# Mock concrete implementation


def sample_vars() -> List[VOBaseVariable]:
    entity = at.Heater("HT-01")
    vars = entity.get_variables(filter=[at.VarCollectionContinuous])
    return list(vars)


# Tests



def test_variable_collection_label():
    collection = VariableUIDCollection()
    for var in sample_vars():
        collection.add_item(var)

    for label in collection.get_ui_labels():
        assert "_" not in label

    logging.info(f"Variable UI Labels: {collection.get_ui_labels()}")


class TestUsecase:

    @staticmethod
    def test_entity_var_uuids_are_unique():
        """Tests that reference UUIDs from entities are unique"""
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

        # Get all var UUIDS
        uid_list = []
        for label in atlas.equipments:
            vars = atlas.get_equipment(by_label=label).get_variables()
            uid_list.extend([var.uid for var in vars])
        for label in atlas.streams:
            vars = atlas.get_stream(by_label=label).get_variables()
            uid_list.extend([var.uid for var in vars])

        # Assert all UUIDS are unique
        duplicates = {
            uid: count for uid, count in Counter(uid_list).items() if count > 1
        }
        assert not duplicates, f"duplicate UUIDs found: {pformat(duplicates)}"
        print(f"passed: {duplicates}")

    @staticmethod
    def test_uuid_var_collection_is_clean():
        """
        Test that collection key is same as item uuid
        """
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

        mismatched_sets = {
            k_uid: v_var
            for k_uid, v_var in atlas.variables_collection.collection.items()
            if k_uid != v_var.uid
        }

        assert (
            not mismatched_sets
        ), f"mismatched key-value items found: {mismatched_sets}"

    @staticmethod
    def test_uuid_var_collection_matches_entity_variables():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

        uid_list: List[uuid.UUID] = []
        for label in atlas.equipments:
            vars = atlas.get_equipment(by_label=label).get_variables()
            uid_list.extend([var.uid for var in vars])
        for label in atlas.streams:
            vars = atlas.get_stream(by_label=label).get_variables()
            uid_list.extend([var.uid for var in vars])

        entity_uid_set = set(uid_list)
        collection_uid_set = {v.uid for v in atlas.variables_collection.items}

        if entity_uid_set != collection_uid_set:
            in_entities = entity_uid_set - collection_uid_set
            in_collection = collection_uid_set - entity_uid_set
            assert (
                False
            ), f"mismatch between collection & entity sets: \nIn Entities Only: {pformat(in_entities)}\nIn Collection Only: {pformat(in_collection)}"

    @staticmethod
    def test_kpi_collection_has_correct_label():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

        # Reset the collection
        atlas.kpi_collection._collection = {}

        var_collection = atlas.variables_collection
        kpi_collection = atlas.kpi_collection
        test_kpi1 = at.KPI(
            label="HEX-100 Heat Exchange",
            expression="{{HEX-100 - Heat Exchange}}",
            collection_reference=var_collection,
        )
        kpi_collection.add_item(test_kpi1)

        assert test_kpi1.get_ui_expression

    @staticmethod
    def test_basic_kpi_transformations_eqp():
        """base case hardcoded test"""

        kpi_delim = at.KPI.delimiter
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
        # reset collection
        atlas.kpi_collection._collection = {}

        # BRUTE ADD
        entity = atlas.get_equipment(by_label="HEX-101")
        var = entity.get_variable(at.ContVarSpecEnum.HeatLoss)
        var_ui_label = atlas.variables_collection.get_ui_label(var)  # type: ignore

        static_var = f"{entity.label}{kpi_delim}{var_ui_label}"  # type: ignore

        # Add Static KPI vars, dynamic KPI vars, compound KPIs
        kpi = at.KPI(
            label="static var kpi",
            expression=f"{{{{ {static_var} }}}}",
            collection_reference=atlas.variables_collection,
        )
        atlas.kpi_collection.add_item(kpi)

        # Assert UI Expression
        ui_expression = kpi.get_ui_expression()
        assert "HEX-101" in ui_expression
        assert var_ui_label in ui_expression

        # Assert UID
        uid_expression = kpi.get_uuid_expression()
        assert (
            str(var.uid) in uid_expression
        ), f"UID for varible mismatch with UID from UID expression"

    @staticmethod
    def test_simple_equipment_kpi_has_correct_label_and_uuid():

        def _test_equipment_kpi(
            atlas: at.AtlasRoot, equipment_label: str, var: at.VOBaseVariable
        ):

            kpi_delim = at.KPI.delimiter
            # reset collection
            atlas.kpi_collection._collection = {}

            # BRUTE ADD
            entity = atlas.get_equipment(by_label=equipment_label)
            var_ui_label = atlas.variables_collection.get_ui_label(var)  # type: ignore

            static_var = f"{entity.label}{kpi_delim}{var_ui_label}"  # type: ignore

            # Add Static KPI vars, dynamic KPI vars, compound KPIs
            kpi = at.KPI(
                label="static var kpi",
                expression=f"{{{{ {static_var} }}}}",
                collection_reference=atlas.variables_collection,
            )
            atlas.kpi_collection.add_item(kpi)

            # Assert UI Expression
            ui_expression = kpi.get_ui_expression()
            assert equipment_label in ui_expression
            assert var_ui_label in ui_expression

            # Assert UID
            uid_expression = kpi.get_uuid_expression()
            assert (
                str(var.uid) in uid_expression
            ), f"UID for varible mismatch with UID from UID expression"

        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
        for label in atlas.equipments:
            entity = atlas.get_equipment(by_label=label)
            for var in entity.get_variables(
                filter=[at.VarCollectionContinuous, at.VarCollectionReactionStoich]
            ):
                _test_equipment_kpi(atlas, label, var)

    @staticmethod
    def test_simple_stream_kpi():
        kpi_delim = at.KPI.delimiter
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
        # reset collection
        atlas.kpi_collection._collection = {}

        # BRUTE ADD
        entity = atlas.get_stream(by_equipments=("B-100", "HEX-100"))
        var = entity.get_variable(at.ContVarSpecEnum.Temperature)
        var_ui_label = atlas.variables_collection.get_ui_label(var)  # type: ignore

        static_var = f"{entity.label}{kpi_delim}{var_ui_label}"  # type: ignore

        # Add Static KPI vars, dynamic KPI vars, compound KPIs
        kpi = at.KPI(
            label="static var kpi",
            expression=f"{{{{ {static_var} }}}}",
            collection_reference=atlas.variables_collection,
        )
        atlas.kpi_collection.add_item(kpi)

        ui_expression = kpi.get_ui_expression()
        uid_expression = kpi.get_uuid_expression()

        # Assert UI Expression
        assert "B-100->HEX-100" in ui_expression
        assert var_ui_label in ui_expression

        # Assert UID
        assert (
            str(var.uid) in uid_expression
        ), f"UID for varible mismatch with UID from UID expression"

    @staticmethod
    def test_simple_stream_kpi_has_correct_label_and_uuid():

        def _test_stream_kpi(
            atlas: at.AtlasRoot, stream_label: str, var: at.VOBaseVariable
        ):

            kpi_delim = at.KPI.delimiter
            # reset collection
            atlas.kpi_collection._collection = {}

            # BRUTE ADD
            entity = atlas.get_stream(by_label=stream_label)
            var_ui_label = atlas.variables_collection.get_ui_label(var)  # type: ignore

            static_var = f"{entity.label}{kpi_delim}{var_ui_label}"  # type: ignore

            # Add Static KPI vars, dynamic KPI vars, compound KPIs
            kpi = at.KPI(
                label="static var kpi",
                expression=f"{{{{ {static_var} }}}}",
                collection_reference=atlas.variables_collection,
            )
            atlas.kpi_collection.add_item(kpi)

            ui_expression = kpi.get_ui_expression()
            uid_expression = kpi.get_uuid_expression()

            # Assert UI Expression
            assert stream_label in ui_expression
            assert var_ui_label in ui_expression

            # Assert UID
            assert (
                str(var.uid) in uid_expression
            ), f"UID for varible mismatch with UID from UID expression"

        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
        for label in atlas.streams:
            entity = atlas.get_stream(by_label=label)

            # TEST FOR CONTVAR
            for var in entity.get_variables(filter=[at.VarCollectionContinuous]):
                _test_stream_kpi(atlas, label, var)

            # TEST FOR COMPOUNDMASS
            for var in entity.get_variables(filter=[at.VarCollectionCompoundMassRatio]):
                _test_stream_kpi(atlas, label, var)
