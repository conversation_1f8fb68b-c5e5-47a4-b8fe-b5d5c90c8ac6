import pytest
from backend.core._atlas.entities import *
from backend.core._atlas.valueobjects import *
from backend.core._atlas._singletons import *


@pytest.fixture
def sample_compounds():
    return [VOCompound("A"), V<PERSON><PERSON>pound("B"), VOCompound("C")]


@pytest.fixture
def sample_equipment():
    return ConversionReactor("test_reactor")


class TestConversionReaction:
    def test_initialization(self):
        """Test basic initialization"""
        reaction = ConversionReaction("test_reaction")

        # Check collections initialized
        assert reaction.get_collection(VarCollectionReactionStoich) is not None
        assert reaction.get_collection(VarCollectionDiscreteSet) is not None

        # Check discrete defaults
        phase = reaction.get_value(DiscreteSetSpecEnum.ReactionPhase)
        assert phase == DiscreteItemSpecEnum.REACTION_LIQUID

        # Check empty equipment set
        assert len(reaction.equipments) == 0

    def test_stoichiometry_management(self, sample_compounds):
        """Test adding/removing compounds with stoichiometry"""
        reaction = ConversionReaction("test_reaction")
        collection = reaction.get_collection(VarCollectionReactionStoich)

        # Add compounds with stoichiometry
        collection.add_item(VOReactionStoich(sample_compounds[0], 1.0))
        collection.add_item(VOReactionStoich(sample_compounds[1], -1.0))

        # Verify compounds list
        compounds = reaction.reaction_compounds
        assert len(compounds) == 2
        assert sample_compounds[0] in compounds
        assert sample_compounds[1] in compounds

    def test_setting_stoich(self, sample_compounds):
        """Test adding/removing compounds with stoichiometry"""
        reaction = ConversionReaction("test_reaction")
        collection = reaction.get_collection(VarCollectionReactionStoich)
        # Verify setval
        for c in sample_compounds:
            collection.add_item(VOReactionStoich(c, 1.0))
            reaction.set_value(c, 10)
            assert reaction.get_value(c) == 10

    def test_equipment_association(self, sample_equipment):
        """Test equipment reference management"""
        reaction = ConversionReaction("test_reaction")

        # Add equipment
        reaction.add_equipment_reference(sample_equipment)
        assert sample_equipment in reaction.equipments

        # Remove equipment
        reaction.remove_equipment_reference(sample_equipment)
        assert sample_equipment not in reaction.equipments

        # Test duplicate add
        reaction.add_equipment_reference(sample_equipment)
        reaction.add_equipment_reference(sample_equipment)
        assert len(reaction.equipments) == 1

    def test_discrete_settings(self, sample_compounds):
        """Test discrete variable management"""
        reaction = ConversionReaction("test_reaction")
        collection = reaction.get_collection(VarCollectionReactionStoich)

        # Add compound and set as base
        collection.add_item(VOReactionStoich(sample_compounds[0], 1.0))
        reaction.set_value(DiscreteSetSpecEnum.BaseCompound, sample_compounds[0])

        # Set conversion expression
        reaction.set_value(DiscreteSetSpecEnum.ConversionExpression, "0.5")

        # Verify settings
        assert (
            reaction.get_value(DiscreteSetSpecEnum.BaseCompound) == sample_compounds[0]
        )
        assert reaction.get_value(DiscreteSetSpecEnum.ConversionExpression) == "0.5"

    def test_phase_validation(self):
        """Test reaction phase settings"""
        reaction = ConversionReaction("test_reaction")

        # Test valid phase change
        reaction.set_value(
            DiscreteSetSpecEnum.ReactionPhase, DiscreteItemSpecEnum.REACTION_VAPOR
        )
        assert (
            reaction.get_value(DiscreteSetSpecEnum.ReactionPhase)
            == DiscreteItemSpecEnum.REACTION_VAPOR
        )
