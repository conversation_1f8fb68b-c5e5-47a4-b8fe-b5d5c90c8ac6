import pytest
from backend.core._atlas.valueobjects import VOCompound, CompoundRef
from backend.core._atlas._singletons import *


def test_get_labels():
    labels = CompoundRef.get_labels()
    assert isinstance(labels, list)
    assert len(labels) > 0
    assert all(isinstance(label, str) for label in labels)


def test_get_label_from_id():
    id = "7732-18-5"  # Water CAS number
    label = CompoundRef.get_label_from_id(id)
    assert isinstance(label, str)
    assert label == "Water"


def test_get_id_from_label():
    label = "Water"
    id = CompoundRef.get_id_from_label(label)
    assert isinstance(id, str)
    assert id == "7732-18-5"


def test_get_all_vocompounds():
    compounds = CompoundRef.get_all_dwsim_compounds()
    assert isinstance(compounds, list)
    assert len(compounds) > 0
    assert all(isinstance(c, VOCompound) for c in compounds)


def test_get_vocompound_by_id():
    compound = CompoundRef.get_vocompound_from_label("7732-18-5")
    assert isinstance(compound, VOCompound)
    assert compound.label == "Water"


def test_get_vocompound_by_label():
    compound = CompoundRef.get_vocompound_from_label("Water")
    assert isinstance(compound, VOCompound)
    assert compound.id == "7732-18-5"


def test_get_vocompound_from_enum():
    compound = CompoundRef.get_vocompound_from_enum(CASCompoundEnum.Water)
    assert isinstance(compound, VOCompound)
    assert compound.label == "Water"
    assert compound.id == "7732-18-5"
