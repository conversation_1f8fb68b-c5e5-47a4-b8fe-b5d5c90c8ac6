from pprint import pformat
import pytest
import math
from backend.core._atlas.entities import *
from backend.core._atlas.valueobjects import *
from backend.core._atlas.aggregates import *


def create_reaction(label: str) -> ConversionReaction:
    reactionset = ConversionReaction(label)
    compoundA = VOCompound(compound_id="aaa", label="AAA")
    compoundb = VOCompound(compound_id="bbb", label="BBB")
    compoundc = VOCompound(compound_id="ccc", label="CCC")

    # ASSERT ADD REACTIONSTOICH
    rs_a = VOReactionStoich(compoundA, 3)
    rs_b = VOReactionStoich(compoundb, -23.1)

    reactionset.add_variable(rs_a)
    reactionset.add_variable(rs_b)
    # NOTE - warning is raised when set to zero, but then the value defaults to it. Not changing for now, this can be managed FE.

    # Test Add discrete vars
    reactionset.set_value(DiscreteSetSpecEnum.BaseCompound, compoundA)
    reactionset.set_value(DiscreteSetSpecEnum.ConversionExpression, "haha")
    reactionset.set_value(
        DiscreteSetSpecEnum.ReactionPhase, DiscreteItemSpecEnum.REACTION_MIXTURE
    )

    assert reactionset.get_value(DiscreteSetSpecEnum.BaseCompound) == compoundA
    assert reactionset.get_value(DiscreteSetSpecEnum.ConversionExpression) == "haha"
    assert (
        reactionset.get_value(DiscreteSetSpecEnum.ReactionPhase)
        == DiscreteItemSpecEnum.REACTION_MIXTURE
    )

    return reactionset


####################


def test_create_reactionset_entity():
    reactionset = ConversionReaction("snicker_reactionset")
    compoundA = VOCompound(compound_id="aaa", label="AAA")
    compoundb = VOCompound(compound_id="bbb", label="BBB")
    compoundc = VOCompound(compound_id="ccc", label="CCC")

    # ASSERT CREATION
    assert isinstance(reactionset, ConversionReaction)

    # ASSERT ADD REACTIONSTOICH
    rs_a = VOReactionStoich(compoundA, 3)
    rs_b = VOReactionStoich(compoundb, -23.1)

    reactionset.add_variable(rs_a)
    reactionset.add_variable(rs_b)

    assert reactionset.get_value(compoundA) == 3
    assert compoundb in reactionset.reaction_compounds

    # NOTE - warning is raised when set to zero, but then the value defaults to it. Not changing for now, this can be managed FE.

    # Test Add discrete vars
    reactionset.set_value(DiscreteSetSpecEnum.BaseCompound, compoundA)
    reactionset.set_value(DiscreteSetSpecEnum.ConversionExpression, "haha")
    reactionset.set_value(
        DiscreteSetSpecEnum.ReactionPhase, DiscreteItemSpecEnum.REACTION_MIXTURE
    )

    assert reactionset.get_value(DiscreteSetSpecEnum.BaseCompound) == compoundA
    assert reactionset.get_value(DiscreteSetSpecEnum.ConversionExpression) == "haha"
    assert (
        reactionset.get_value(DiscreteSetSpecEnum.ReactionPhase)
        == DiscreteItemSpecEnum.REACTION_MIXTURE
    )
    logging.info(f"{pformat(reactionset, indent=4)}")


def test_assign_reaction_to_reactor():
    reaction = create_reaction("rset1")
    reactor = ConversionReactor("CR-1")

    reactor.add_reaction(reaction)

    # Assert equivalence
    assert list(reactor.reactionset)[0] == reaction

    # Assert reactionset lookup is updated
    assert reactor in reaction.equipments

    # Assert removal and reassignment
    reactionset2 = create_reaction("rs2")
    reactor.add_reaction(reactionset2)
    assert reactionset2 in reactor.reactionset

    logging.info(f"{pformat(reactor, indent=4)}")


def test_reactionset_variable_register():
    atlas = AtlasRoot("model", plant_id="na")
    reactor = atlas.add_equipment(ConversionReactor, "CR-1")
    assert isinstance(reactor, ConversionReactor)

    reaction = create_reaction("rs")
    reaction.parent_obj = atlas
    atlas.add_reaction_to_reactor(reactor, reaction)

    # ASSERT VARIALBES ADDED TO VARUIDCOLLECTION
    vars = reaction.get_variables()
    logging.info(vars)
    for var in vars:
        assert var in atlas.variables_collection.items

    logging.info(f"{pformat(atlas.variables_collection.items, indent=4)}")

    # ASSERT VARIALBE ADDED WHEN RSET CHANGED
    compoundc = VOCompound(compound_id="ccc", label="CCC")
    stoichReac = VOReactionStoich(compoundc, -23)
    reaction.add_variable(stoichReac)

    assert stoichReac in atlas.variables_collection.items
    assert reactor.label is not None
    logging.info(f"reactor label: {reactor.label}")
