import logging
import random
import time
from datetime import datetime
from typing import Optional, <PERSON>ple

import pytest

from backend.core._atlas._singletons import (
    DiscreteItemSpecEnum,
    DiscreteSetSpecEnum,
    ContVarSpecEnum,
    PolicyActionEnum,
)
from backend.core._atlas.entities import ENTBase
from backend.core._atlas.valueobjects import *
from backend.core._atlas.valueobjects import (
    _ContextAwarePolicies,
    _ContextFreePolicies,
)

####################

# HELPER FUNCTIONS


def return_continuous_variable():
    return VOContinuousVariable(
        ContVarSpecEnum.OutletPressure,
        20.0,
        (10.0, 100.0),
    )


def generate_oob_value(
    value, bounds: Optional[Tuple[Optional[float], Optional[float]]]
) -> float:
    """
    Returns a value that is intentionally out of bounds.
    Accounts for the possibility of None for the bounds.
    """
    lower, upper = bounds or [None, None]
    if lower is not None and upper is not None:
        if value <= upper:
            return float(upper + 1)
        else:
            return float(lower - 1)
    elif lower is not None:
        return float(lower - 1)
    elif upper is not None:
        return float(upper + 1)
    else:
        return float(
            value + 1
        )  # If both bounds are None, return a value different from input


def generate_inbound_value(
    value, bounds: Optional[Tuple[Optional[float], Optional[float]]]
) -> float:
    """
    Returns a value that is intentionally in bounds.
    Accounts for the possibility of None for the bounds.
    """
    lower, upper = bounds or [None, None]
    if lower is not None and upper is not None:
        if value <= upper:
            return float(value + 1)
        else:
            return float(value - 1)
    elif lower is not None:
        return float(value - 1)
    elif upper is not None:
        return float(value + 1)
    else:
        return float(
            value + 1
        )  # If both bounds are None, return a value different from input


####################

# TESTS


def test_units():
    collection = return_continuous_variable()

    for label in ContVarSpecEnum:
        collection.variable_enum = label
        unit = label.unit
        assert collection.unit == unit


def test_init():
    var = VOContinuousVariable(
        ContVarSpecEnum.OutletPressure,
        20.0,
        (10.0, 100.0),
    )

    # ID
    logging.info("\n\n#####TESTING ID")
    assert var.variable_enum == ContVarSpecEnum.OutletPressure
    var.variable_enum = ContVarSpecEnum.Temperature_Condenser
    assert var.variable_enum != ContVarSpecEnum.OutletPressure
    var.variable_enum = ContVarSpecEnum.OutletPressure

    # Value
    logging.info("\n\n####################TESTING VAL")
    assert var.value == 20.0
    new_val = generate_inbound_value(var.value, var.bounds)
    var.set_value(new_val)
    assert var.value == new_val

    # is_indep
    logging.info("\n\n####################TESTING INDEP")
    assert var._is_independent == True
    var._is_independent = False
    assert var._is_independent == False
    var._is_independent = True


def test_equality_contvar():
    # as value objects, equality is determined by same ID and value

    # Assert Basecase: equal on val an unit
    var1 = return_continuous_variable()
    var2 = return_continuous_variable()
    var1_value = var1.value
    assert isinstance(var1_value, float)
    assert var1 == var2

    # Assert not equal on val change
    var1 = return_continuous_variable()
    var2 = return_continuous_variable()
    var1.set_value(generate_inbound_value(var1.value, var1.bounds))
    assert var1 != var2

    # Assert not equal on same value but different unit
    var1 = VOContinuousVariable(ContVarSpecEnum.BaffleSpacing)
    var2 = VOContinuousVariable(ContVarSpecEnum.TubeFouling)
    var1._value = 10
    var2._value = 10
    assert var1 != var2


def test_equality_discvar():
    var1 = VODiscreteVariable(
        DiscreteSetSpecEnum.CalculationType,
        DiscreteItemSpecEnum.CalculateHotFluidOutletTemperature,
    )
    var2 = VODiscreteVariable(
        DiscreteSetSpecEnum.CalculationType,
        DiscreteItemSpecEnum.CalculateHotFluidOutletTemperature,
    )
    assert var1 == var2

    var2.set_value(DiscreteItemSpecEnum.CalculateArea, override_all_policies=True)
    assert var1 != var2

    var2 = VODiscreteVariable(
        DiscreteSetSpecEnum.CalculationType,
        DiscreteItemSpecEnum.CalculateHotFluidOutletTemperature,
    )
    var2.variable_enum = DiscreteSetSpecEnum.CondenserType
    assert var1 != var2


def test_is_inbounds_continuous():
    def _test(val, bounds, expected):
        var_entity = VOContinuousVariable(ContVarSpecEnum.OutletPressure, 50, bounds)
        assert (
            var_entity._validate_inbounds(val) == expected
        ), f"Expected {expected}, got {var_entity._validate_inbounds(val)}. Val=`{val}`, bounds=`{bounds}`"

    tests = {
        # Y, Y
        (10, (10.0, 100.0), True),
        (100, (10.0, 100.0), True),
        (100.1, (10.0, 100.0), False),
        (0.1, (10.0, 100.0), False),
        # Y, N
        (100.1, (10.0, None), True),
        (0.1, (10.0, None), False),
        (-100.1, (10.0, None), False),
        # N, Y
        (100.1, (None, 100.0), False),
        (0.1, (None, 100.0), True),
        (-1.1, (None, 100.0), True),
        # N, N
        (100.1, (None, None), True),
        (0.1, (None, None), True),
        (-100.1, (None, None), True),
    }
    for test in tests:
        _test(*test)


def test_policy_oob_continuous():
    INIT_VALUE = 20
    BOUNDS = (10.0, 100.0)
    SET_VAL = 1000000

    var_entity = VOContinuousVariable(
        ContVarSpecEnum.OutletPressure,
        INIT_VALUE,
        BOUNDS,
        is_independent=True,
    )
    # return var_entity

    var_entity.set_value(SET_VAL)

    assert var_entity.value != SET_VAL


def test_policy_val_is_none():
    INIT_VALUE = 20
    BOUNDS = (10, 100)
    SET_VAL = None

    var_entity = VOContinuousVariable(
        ContVarSpecEnum.OutletPressure,
        INIT_VALUE,
        BOUNDS,
        is_independent=True,
    )
    assert var_entity.value == INIT_VALUE
    var_entity.set_value(None)
    assert var_entity.value == INIT_VALUE
    # return var_entity


# NOTE - policy is no longer in use
# def test_policy_var_is_dep():
#     INIT_VALUE = 20
#     BOUNDS = (10, 100)
#     SET_VAL = 50
#     var_entity = VOContinuousVariable(
#         ContVarSpecEnum.OutletPressure,
#         INIT_VALUE,
#         BOUNDS,
#         is_independent=False,
#     )

#     var_entity.set_value(SET_VAL)
#     assert var_entity.value == INIT_VALUE


####################

# DISCREET ONLY


def create_discreet_var():
    variable = VODiscreteVariable(
        DiscreteSetSpecEnum.CalculationType, DiscreteItemSpecEnum.CalculateArea
    )
    assert isinstance(variable, VODiscreteVariable)
    variable.policies = [_ContextFreePolicies.no_setval_on_oob_discrete]  # type: ignore
    variable.bounds = {
        DiscreteItemSpecEnum.CalcMode_SC_Partial,
        DiscreteItemSpecEnum.RCT_Conversion_Adiabatic,
        DiscreteItemSpecEnum.CalculateArea,
    }
    return variable


def test_discreet_value_oob():
    variable = VODiscreteVariable(
        DiscreteSetSpecEnum.CalculationType, DiscreteItemSpecEnum.CalculateArea
    )
    assert isinstance(variable, VODiscreteVariable)
    variable.policies = [_ContextFreePolicies.no_setval_on_oob_discrete]  # type: ignore
    variable.bounds = {
        DiscreteItemSpecEnum.CalcMode_SC_Partial,
        DiscreteItemSpecEnum.RCT_Conversion_Adiabatic,
        DiscreteItemSpecEnum.CalculateArea,
    }

    # Assert set in bound
    variable.set_value(DiscreteItemSpecEnum.CalculateArea)
    assert variable.value == DiscreteItemSpecEnum.CalculateArea

    # Assert set out of bounds
    variable.set_value(DiscreteItemSpecEnum.SpecifyTubeGeometry)
    assert variable.value != DiscreteItemSpecEnum.SpecifyTubeGeometry


def test_context_aware_prereq_met():
    """Test prerequisite policy for discrete variables"""
    equipment = ENTBase("MOCK-01")

    # Create variable requiring prerequisites
    var_contextbound = VODiscreteVariable(
        DiscreteSetSpecEnum.CalculationType,
        DiscreteItemSpecEnum.CalculateArea,
        {
            DiscreteItemSpecEnum.CalculateArea,
            DiscreteItemSpecEnum.HeatAddedOrRemoved,
        },
        prerequisites_for_selection=({DiscreteItemSpecEnum.SpecifyTubeGeometry},),
    )
    var_contextbound.policies = [_ContextAwarePolicies.no_setval_on_preqreq_not_met]  # type: ignore
    assert len(var_contextbound.policies) == 1
    logging.info(f"policies for var_contextbound: `{var_contextbound.policies}`")

    equipment.add_variable(var_contextbound)
    # Create prerequisite variable
    var_prereq = VODiscreteVariable(
        DiscreteSetSpecEnum.TubeLayout,
        DiscreteItemSpecEnum.SpecifyTubeGeometry,
        {
            DiscreteItemSpecEnum.SpecifyTubeGeometry,
            DiscreteItemSpecEnum.OutletPressure,
        },
    )
    var_prereq.policies = []
    equipment.add_variable(var_prereq)

    # Verify variables are cDiscreteSetSpecEnum
    assert var_contextbound == equipment.get_variable(
        DiscreteSetSpecEnum.CalculationType
    )
    assert var_prereq.variable_enum == DiscreteSetSpecEnum.TubeLayout
    assert (
        equipment.get_value(DiscreteSetSpecEnum.CalculationType)
        == DiscreteItemSpecEnum.CalculateArea
    )
    assert (
        equipment.get_value(DiscreteSetSpecEnum.TubeLayout)
        == DiscreteItemSpecEnum.SpecifyTubeGeometry
    )

    # Test policy pass when prerequisite met
    equipment.set_value(
        var_prereq.variable_enum, DiscreteItemSpecEnum.SpecifyTubeGeometry
    )
    equipment.set_value(
        var_contextbound.variable_enum, DiscreteItemSpecEnum.HeatAddedOrRemoved
    )
    assert (
        equipment.get_value(var_prereq.variable_enum)
        == DiscreteItemSpecEnum.SpecifyTubeGeometry
    )
    assert (
        equipment.get_value(var_contextbound.variable_enum)
        == DiscreteItemSpecEnum.HeatAddedOrRemoved
    )

    # Test policy fail when prerequisite not met
    equipment.set_value(var_prereq.variable_enum, DiscreteItemSpecEnum.OutletPressure)
    assert (
        equipment.get_value(var_prereq.variable_enum)
        == DiscreteItemSpecEnum.OutletPressure
    )
    equipment.set_value(
        var_contextbound.variable_enum, DiscreteItemSpecEnum.CalculateArea
    )
    assert (
        equipment.get_value(var_contextbound.variable_enum)
        != DiscreteItemSpecEnum.CalculateArea
    )


def test_toggle_independence_policy():
    """Test independence toggle policy for discrete variables"""
    equipment = ENTBase("MOCK-01")

    # Setup parameters to toggle
    param_vars = [
        (ContVarSpecEnum.PressureIncrease, 1.2),
        (ContVarSpecEnum.Efficiency, 1.2),
        (ContVarSpecEnum.OutletPressure, 1.2),
        (ContVarSpecEnum.TemperatureChange, 1.2),
        (ContVarSpecEnum.PowerRequired, 1.2),
        (ContVarSpecEnum.OutletTemperature, 1.2),
    ]

    for enum, value in param_vars:
        cont_var = VOContinuousVariable(enum, value, bounds=(0, 100))
        equipment.add_variable(cont_var)

    # Setup discrete variable with toggle policy
    disc_var = VODiscreteVariable(
        DiscreteSetSpecEnum.ColumnType,
        DiscreteItemSpecEnum.PressureIncrease,
        bounds={
            DiscreteItemSpecEnum.PressureIncrease,
            DiscreteItemSpecEnum.OutletPressure,
        },
    )
    disc_var.parent = equipment
    disc_var.policies = [_ContextAwarePolicies.toggle_indep_on_setval]  # type: ignore
    equipment.add_variable(disc_var)

    # Test toggle behavior
    disc_var.set_value(DiscreteItemSpecEnum.OutletPressure)

    # Verify correct independence states
    indep_params = [
        ContVarSpecEnum.OutletPressure,
        ContVarSpecEnum.Efficiency,
    ]
    dep_params = [
        ContVarSpecEnum.PressureIncrease,
        ContVarSpecEnum.OutletTemperature,
        ContVarSpecEnum.TemperatureChange,
        ContVarSpecEnum.PowerRequired,
    ]

    for param in indep_params:
        assert equipment.get_variable(param).is_independent == True

    for param in dep_params:
        assert equipment.get_variable(param).is_independent == False

    # # Test policy rollback on missing parameter
    eff = equipment.get_variable(ContVarSpecEnum.Efficiency)
    equipment.remove_variable(eff)  # type:ignore
    with pytest.raises(ValueError) as info:
        equipment.set_value(
            disc_var.variable_enum, DiscreteItemSpecEnum.PressureIncrease
        )

        # Verify error message
    assert "ContVarSpecEnum.Efficiency" in str(info.value)

    # Verify state remained unchanged
    assert (
        equipment.get_value(disc_var.variable_enum)
        == DiscreteItemSpecEnum.OutletPressure
    )


# def test_toggle_indep_and_dep_var():
#     """
#     REFERENCE POLICY
#     PressureIncrease = _EntityStateSpecification(
#         "pressure_increase",
#         toggle_indep=[
#             ParameterLabel.PressureIncrease,
#             ParameterLabel.Efficiency,
#         ],
#         toggle_dep=[
#             ParameterLabel.OutletPressure,
#             ParameterLabel.OutletTemperature,
#             ParameterLabel.TemperatureChange,
#             ParameterLabel.PowerRequired,
#         ],
#     )
#     OutletPressure = _EntityStateSpecification(
#         "outlet_pressure",
#         toggle_indep=[ParameterLabel.OutletPressure, ParameterLabel.Efficiency],
#         toggle_dep=[
#             ParameterLabel.PressureIncrease,
#             ParameterLabel.OutletTemperature,
#             ParameterLabel.TemperatureChange,
#             ParameterLabel.PowerRequired,
#         ],
#     )
#     """
#     print("\n\nCREATING BASE EQUIPMENT")
#     equipment = ENTBaseEquipmentAndStream("MOCK-01")

#     def add_valueobj(eqpt, vo_callable, vo_id, vo_init, bounds=None):
#         vo = vo_callable(vo_id, vo_init)
#         vo.parent = eqpt
#         vo.policies = []  # purge policies
#         vo.bounds = bounds
#         if isinstance(vo, VODiscreteVariable):
#             eqpt.discreteVO_collection[vo.variable_enum] = vo
#         if isinstance(vo, VOContinuousVariable):
#             eqpt.parameterVO_collection[vo.variable_enum] = vo
#         return vo

#     # PARAMS TO TOGGLE
#     add_valueobj(
#         equipment, VOContinuousVariable, ParameterLabelEnum.PressureIncrease, 1.2
#     )
#     add_valueobj(equipment, VOContinuousVariable, ParameterLabelEnum.Efficiency, 1.2)
#     add_valueobj(
#         equipment, VOContinuousVariable, ParameterLabelEnum.OutletPressure, 1.2
#     )
#     add_valueobj(
#         equipment, VOContinuousVariable, ParameterLabelEnum.OutletTemperature, 1.2
#     )
#     add_valueobj(
#         equipment, VOContinuousVariable, ParameterLabelEnum.TemperatureChange, 1.2
#     )
#     add_valueobj(equipment, VOContinuousVariable, ParameterLabelEnum.PowerRequired, 1.2)

#     # VAR TO TOGGLE
#     var = add_valueobj(
#         equipment,
#         VODiscreteVariable,
#         DiscreteSetLabelEnum.ColumnType,
#         DiscreteItemEnum.PressureIncrease,
#         {DiscreteItemEnum.PressureIncrease, DiscreteItemEnum.OutletPressure},
#     )
#     var.policies = [_ContextAwarePolicies.toggle_indep_on_setval]

#     logging.info(
#         "\n\n#################### ASSERT TOGGLE VALS WORK ####################"
#     )
#     for param in equipment.parameterVO_collection.values():
#         param._is_independent = None  # type: ignore
#         assert param.is_independent == None
#     var.set_value(DiscreteItemEnum.OutletPressure)
#     toggle_indep = [ParameterLabelEnum.OutletPressure, ParameterLabelEnum.Efficiency]
#     toggle_dep = [
#         ParameterLabelEnum.PressureIncrease,
#         ParameterLabelEnum.OutletTemperature,
#         ParameterLabelEnum.TemperatureChange,
#         ParameterLabelEnum.PowerRequired,
#     ]
#     map = [[toggle_dep, False], [toggle_indep, True]]
#     for parameters, expected in map:
#         for parameter in parameters:
#             assert (
#                 equipment.parameterVO_collection[parameter].is_independent == expected
#             )

#     logging.info(
#         "\n\n#################### ASSERT TOGGLE VALS RAISE EXCEPTION WITH MISSING VARS####################"
#     )
#     # remove a parameter
#     equipment.parameterVO_collection.pop(ParameterLabelEnum.Efficiency)
#     # var.set_value(DiscreteItem.PressureIncrease)
#     equipment.set_value(var.variable_enum, DiscreteItemEnum.PressureIncrease)
#     assert (
#         equipment.discreteVO_collection[var.variable_enum].value
#         != DiscreteItemEnum.PressureIncrease
#     )
#     # Check for rollback
#     for parameters, expected in map:
#         for parameter in parameters:
#             # skip for efficiency because we pulled it out
#             if parameter == ParameterLabelEnum.Efficiency:
#                 continue
#             assert (
#                 equipment.parameterVO_collection[parameter].is_independent == expected
#             )


def test_compoundmassratio():
    # assert creation
    compound1 = VOCompound("333", "water")
    var1 = VOCompoundMassRatio(compound1, 0.2)

    assert var1.value == 0.2
    var1.set_value(0.3)
    assert var1.value == 0.3

    logging.info(f"{var1}")

    ####################


# if __name__ == "__main__":
#     pytest.main([__file__])
