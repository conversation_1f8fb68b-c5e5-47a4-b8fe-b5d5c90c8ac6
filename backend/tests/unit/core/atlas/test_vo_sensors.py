from uuid import UUID
import pytest

from backend.core._atlas.valueobjects import VOSensor
from backend.core._atlas._singletons import ContVarSpecEnum
from backend.core._atlas.entities import HeatExchanger


@pytest.fixture
def equipment():
    return HeatExchanger("HEX-100")


def test_sensor_initialization(equipment):
    var = equipment.get_variable(ContVarSpecEnum.ColdFluidPressureDrop)
    sensor_label = "S-001"
    sensor = VOSensor(
        label=sensor_label,
        variable_uid=var.uid,
    )
    assert sensor.label == sensor_label
    assert sensor.variable_uid == var.uid
    assert isinstance(sensor.uid, UUID)


def test_sensor_equality(equipment):
    var = equipment.get_variable(ContVarSpecEnum.ColdFluidPressureDrop)
    sensor_label_1 = "S-001"
    sensor_label_2 = "S-002"
    sensor_1 = VOSensor(
        label=sensor_label_1,
        variable_uid=var.uid,
    )
    sensor_2 = VOSensor(
        label=sensor_label_2,
        variable_uid=var.uid,
    )
    sensor_3 = VOSensor(
        label=sensor_label_1,
        variable_uid=var.uid,
    )
    assert sensor_1 == sensor_3
    assert sensor_1 != sensor_2
