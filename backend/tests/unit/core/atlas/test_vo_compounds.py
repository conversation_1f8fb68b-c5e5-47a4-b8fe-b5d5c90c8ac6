import pytest
import math

from backend.core._atlas.valueobjects import VOC<PERSON>pound


def test_compound_vo_initialization():
    compound = VOCompound("C1", "methane")
    assert compound.id == "C1"
    assert compound.label == "methane"


def test_compound_vo_default_label():
    compound = VOCompound("C2")
    assert compound.id == "C2"
    assert compound.label == "C2_label"


def test_compound_vo_equality():
    compound1 = VOCompound("C1", "methane")
    compound2 = VOCompound("C1", "methane")
    compound3 = VOCompound("C2", "ethane")

    assert compound1 == compound2
    assert compound1 != compound3
    assert compound1 != "C1"


def test_compound_vo_hash():
    compound1 = VOCompound("C1", "methane")
    compound2 = VOCompound("C1", "methane")

    assert hash(compound1) == hash(compound2)
