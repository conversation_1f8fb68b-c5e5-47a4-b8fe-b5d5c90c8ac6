from pprint import pformat
import pytest
from backend.core._atlas.entities import *
from backend.core._atlas.entities import (
    VarCollectionContinuous,
    VarCollectionDiscreteSet,
    VarCollectionCompoundMassRatio,
    _VariableCollectionRouter,
    AbstractVariableCollection,
)

from backend.core._atlas.valueobjects import *


def test_collection_type_getters():
    """Test collection key and item type getters"""

    class TestCollection(AbstractCollection[str, int]):  # type: ignore
        def _get_key(self, item):
            return str(item)

    collection = TestCollection()

    assert collection.keytype == str
    assert collection.itemtype == int
    logging.info(collection.keytype)
    logging.info(collection.itemtype)


def test_contvar_collection():
    collection = VarCollectionContinuous()

    # Test registration and retrieval
    var = VOContinuousVariable(ContVarSpecEnum.HeatExchangeArea, 5.0, (0, 10))
    collection.add_item(var)

    assert collection.get_value(ContVarSpecEnum.HeatExchangeArea) == 5.0

    # Test value and bounds updates
    collection.set_value(ContVarSpecEnum.HeatExchangeArea, 7.0)
    collection.set_bounds(ContVarSpecEnum.HeatExchangeArea, (1, 9))

    assert collection.get_value(ContVarSpecEnum.HeatExchangeArea) == 7.0
    assert collection.get_bounds(ContVarSpecEnum.HeatExchangeArea) == (1, 9)


def test_discrete_set_collection():
    collection = VarCollectionDiscreteSet()

    # Test registration and value setting
    var = VODiscreteVariable(
        DiscreteSetSpecEnum.CalculationType,
        DiscreteItemSpecEnum.HeatAddedOrRemoved,
    )
    collection.add_item(var)

    assert (
        collection.get_value(DiscreteSetSpecEnum.CalculationType)
        == DiscreteItemSpecEnum.HeatAddedOrRemoved
    )

    collection.set_value(
        DiscreteSetSpecEnum.CalculationType, DiscreteItemSpecEnum.PressureIncrease
    )
    assert (
        collection.get_value(DiscreteSetSpecEnum.CalculationType)
        == DiscreteItemSpecEnum.PressureIncrease
    )


def test_compound_mass_mix_collection():
    collection = VarCollectionCompoundMassRatio()

    # Test compound management
    compound1 = VOCompound("c1", "water")
    compound2 = VOCompound("c2", "ethanol")

    var1 = VOCompoundMassRatio(compound1, 0.6)
    var2 = VOCompoundMassRatio(compound2, 0.4)

    collection.add_item(var1)
    collection.add_item(var2)

    # Test mass ratio operations
    assert collection.get_value(compound1) == 0.6
    assert len(collection.compounds) == 2

    collection.equalize()
    assert collection.get_value(compound1) == 0.5
    assert collection.get_value(compound2) == 0.5


def test_compound_mass_mix_collection_missing():
    """Test setting value for missing compound"""
    collection = VarCollectionCompoundMassRatio()

    # Create compounds
    compound1 = VOCompound("c1", "water")
    compound2 = VOCompound("c2", "ethanol")

    # Set value for non-existent compound
    collection.set_value(compound1, 0.6)

    # Verify created and set
    assert collection.get_value(compound1) == 0.6
    assert len(collection.compounds) == 1

    # Add another
    collection.set_value(compound2, 0.4)
    assert collection.get_value(compound2) == 0.4
    assert len(collection.compounds) == 2


def test_collection_router():
    # SETUP - Add type hints and validate initial state
    COLLECTION_DEFAULTS = [VarCollectionDiscreteSet, VarCollectionContinuous]
    router = _VariableCollectionRouter(COLLECTION_DEFAULTS)
    assert len(router._registry) == 2

    # TEST COLLECTION REGISTRATION
    collection2 = VarCollectionCompoundMassRatio()
    router.add_collection(collection2)
    assert collection2 in router._registry

    # TEST COLLECTION RETRIEVAL
    collections = router.get_collections()
    assert len(collections) == 3
    discrete_collections = router.get_collections([VarCollectionDiscreteSet])
    assert len(discrete_collections) == 1
    assert isinstance(discrete_collections[0], VarCollectionDiscreteSet)

    # TEST VARIABLE REGISTRATION AND RETRIEVAL
    var_cont = VOContinuousVariable(ContVarSpecEnum.TemperatureChange, 3, (0, 10))
    var_cont2 = VOContinuousVariable(
        ContVarSpecEnum.Temperature_Condenser, 99.9, (0, 120)
    )
    compound = VOCompound("compound_id", "compound_label")
    var_compoundmassratio = VOCompoundMassRatio(compound, 120)

    router.add_variable(var_cont)
    router.add_variable(var_cont2)
    router.add_variable(var_compoundmassratio)

    # Verify correct collection routing
    assert var_cont in router.get_collection(variable_type=type(var_cont)).items
    assert (
        var_compoundmassratio
        in router.get_collection(variable_type=type(var_compoundmassratio)).items
    )

    # # TEST VARIABLE LOOKUP
    assert router.get_variable(ContVarSpecEnum.TemperatureChange) is var_cont
    assert router.get_variable(compound) is var_compoundmassratio

    # # TEST COLLECTION TYPE RESOLUTION
    collection = router.get_collection(collection_type=VarCollectionContinuous)
    assert isinstance(collection, VarCollectionContinuous)
    logging.info(
        f"{pformat([collection.items for collection in collections], indent=4)}"
    )


def test_entity_methods_delegated_from_collection_router():
    # Setup test entity
    entity = ENTBase("test_entity")

    # Test variable management
    var_cont = VOContinuousVariable(ContVarSpecEnum.TemperatureDifference, 3, (0, 10))
    var_discrete = VODiscreteVariable(
        DiscreteSetSpecEnum.CalculationType, DiscreteItemSpecEnum.HeatAddedOrRemoved
    )

    # Test add_variable and get_variable
    entity.add_variable(var_cont)
    entity.add_variable(var_discrete)

    assert entity.get_variable(ContVarSpecEnum.TemperatureDifference) == var_cont
    assert entity.get_variable(DiscreteSetSpecEnum.CalculationType) == var_discrete

    # TEST GET_VARIABLES WITH FILTERS
    cont_vars = entity.get_variables(filter=[VarCollectionContinuous])
    assert len(cont_vars) == 1
    assert var_cont in cont_vars

    # TEST VALUE OPERATIONS
    entity.set_value(ContVarSpecEnum.TemperatureDifference, 5.0)
    assert entity.get_value(ContVarSpecEnum.TemperatureDifference) == 5.0

    # TEST BOUNDS OPERATIONS
    entity.set_bounds(ContVarSpecEnum.TemperatureDifference, (1, 8))
    assert entity.get_bounds(ContVarSpecEnum.TemperatureDifference) == (1, 8)

    # TEST VARIABLE REMOVAL
    entity.remove_variable(var_cont)
    with pytest.raises(KeyError) as e:
        entity.get_variable(ContVarSpecEnum.TemperatureDifference)


def test_equipment_sample():
    """Test variable initialization using Heater as concrete example"""
    heater = Heater("test_heater")

    # Test discrete sets
    discrete_vars = heater.get_variables(filter=[VarCollectionDiscreteSet])
    calc_type_var = next(
        var
        for var in discrete_vars
        if var.variable_enum == DiscreteSetSpecEnum.CalculationType
    )
    assert calc_type_var.value == DiscreteItemSpecEnum.HeatAddedOrRemoved
    assert DiscreteItemSpecEnum.OutletTemperature in calc_type_var.bounds  # type: ignore

    # Test parameters
    cont_vars = heater.get_variables(filter=[VarCollectionContinuous])
    contvar_enums = {var.variable_enum for var in cont_vars}
    expected_enums = {
        ContVarSpecEnum.Efficiency,
        ContVarSpecEnum.PressureDrop,
        ContVarSpecEnum.OutletTemperature,
        ContVarSpecEnum.TemperatureChange,
        ContVarSpecEnum.OutletVaporFraction,
        ContVarSpecEnum.HeatAddedOrRemoved,
    }
    assert expected_enums == contvar_enums

    # Verify parameter values and bounds
    efficiency_var = next(
        var for var in cont_vars if var.variable_enum == ContVarSpecEnum.Efficiency
    )
    assert efficiency_var.value == 1.0
    assert efficiency_var.bounds == (0.0, 1.0)

    # Verify total count matches defaults
    expected_count = len(Heater.DISCRETESET_DEFAULTS) + len(Heater.CONTVAR_DEFAULTS)
    assert len(heater.get_variables()) == expected_count


def z_test_deterministic_variable_uuids():
    # NOTE - removed because variable UUIDs no longer need to be deterministic, as we use variable_collection now
    """Test that variables receive deterministic UUIDs when added to equipment"""
    entity = ENTBase("test_entity")

    # Create two identical variables
    var1 = VOContinuousVariable(ContVarSpecEnum.Efficiency, 1, (0, 10))
    var2 = VOContinuousVariable(ContVarSpecEnum.Efficiency, 2, (0, 10))

    # Initial UUIDs should be different
    assert var1.uid != var2.uid

    # Add to entity - should get deterministic UUIDs
    entity.add_variable(var1)
    uuid1 = var1.uid

    # Remove and add new variable with same identifier
    entity.remove_variable(var1)
    entity.add_variable(var2)
    uuid2 = var2.uid

    # UUIDs should match since they share same uid_seed
    assert uuid1 == uuid2
