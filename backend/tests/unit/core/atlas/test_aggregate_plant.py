import pytest
from backend.core._atlas.entities import (
    BatteryIn,
    BatteryOut,
    ENTBaseEquipment,
    ENTBaseStream,
)
from backend.core._atlas.valueobjects import *
from backend.core._atlas.aggregates import *


def mock_pmodel() -> AtlasRoot:
    return AtlasRoot("process_model_1", "pfizer_plant_a")


def mock_material_stream_a(stream_id: str) -> ENTBaseStream:
    compounds = [VOCompound("A1"), VOC<PERSON>pound("A2"), VOCompound("A3")]
    return ENTBaseStream(stream_id, compounds=compounds)


def mock_material_stream_b(stream_id: str) -> ENTBaseStream:
    compounds = [VOCompound("B1"), VOCompound("B2")]
    return ENTBaseStream(stream_id, compounds=compounds)


def test_add_and_get_equipment():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, p):
            super().__init__(equipment_id, p)

    atlas = mock_pmodel()
    atlas.base_equipment_set.add(DummyEquipment)
    e = atlas.add_equipment(DummyEquipment, "dummy_equipment")
    assert isinstance(e, ENTBaseEquipment)
    assert atlas.get_equipment(by_label="dummy_equipment") == e

    # Test stream is in VarCollection
    e_vars = e.get_variables()
    for var in e_vars:
        collection_var = atlas.variables_collection.get_item(var.uid)
        assert collection_var is not None
        assert collection_var.variable_enum == var.variable_enum

    var_collection = atlas.variables_collection.items

    logging.info(
        f"Var Collection:\n{pprint([KPI.get_kpi_label(v) for v in var_collection])}"
    )


def test_add_and_get_material_stream():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    # Creation w Name
    atlas = mock_pmodel()
    atlas.base_equipment_set.add(DummyEquipment)

    e_up = atlas.add_equipment(DummyEquipment, "upstream-1")
    e_down = atlas.add_equipment(DummyEquipment, "downstream-2")
    # s = mock_material_stream_a("stream-01")
    atlas.add_stream(
        e_up,
        e_down,
    )

    # Test getting
    s_by_eqpt = atlas.get_stream(by_equipments=(e_up, e_down))
    assert isinstance(s_by_eqpt, MaterialStream)
    s_by_eqpt = atlas.get_stream(by_equipments=("upstream-1", "downstream-2"))
    assert isinstance(s_by_eqpt, MaterialStream)

    # Test stream is in VarCollection
    s_vars = s_by_eqpt.get_variables()
    for var in s_vars:
        collection_var = atlas.variables_collection.get_item(var.uid)
        assert collection_var is not None
        assert collection_var.variable_enum == var.variable_enum

    var_collection = atlas.variables_collection.items

    logging.info(
        f"Var Collection:\n{pprint([KPI.get_kpi_label(v) for v in var_collection])}"
    )


def test_add_and_get_input_and_output_stream():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    # Creation w Name
    atlas = mock_pmodel()
    atlas.base_equipment_set.add(DummyEquipment)
    e_up = atlas.add_equipment(DummyEquipment, "upstream-1")
    e_down = atlas.add_equipment(DummyEquipment, "downstream-2")
    # s = mock_material_stream_a("stream-01")

    ####################
    # CREATION W NONE

    # INPUTSTREAM
    # Test getting
    atlas.add_stream(None, e_down, stream_type=InputStream)
    # via obj
    battery_in_stream = atlas.get_stream(by_equipments=(None, e_down))
    assert isinstance(battery_in_stream, InputStream)
    assert atlas.get_equipment(by_output_stream=battery_in_stream) is not None
    # via str
    battery_in_stream = atlas.get_stream(by_equipments=(None, e_down.label))
    assert isinstance(battery_in_stream, InputStream)
    assert atlas.get_equipment(by_output_stream=battery_in_stream) is not None

    # OUTPUT STREAM
    atlas.add_stream(e_up, None, stream_type=OutputStream)
    battery_out_stream = atlas.get_stream(by_equipments=(e_up, None))
    assert isinstance(battery_out_stream, OutputStream)
    assert atlas.get_equipment(by_input_stream=battery_out_stream) is not None
    battery_out_stream = atlas.get_stream(by_equipments=(e_up.label, None))
    assert isinstance(battery_out_stream, OutputStream)
    assert atlas.get_equipment(by_input_stream=battery_out_stream) is not None


def test_add_and_get_input_stream_w_spec():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    # Creation w Name
    atlas = mock_pmodel()
    atlas.base_equipment_set.add(DummyEquipment)
    e_up = atlas.add_equipment(DummyEquipment, "upstream-1")
    e_down = atlas.add_equipment(DummyEquipment, "downstream-2")
    # s = mock_material_stream_a("stream-01")

    ####################
    # CREATION W SPEC

    # Test getting
    atlas.add_stream(e_up, e_down, stream_type=InputStream)

    # via obj
    battery_in_stream = atlas.get_stream(by_equipments=(e_up, e_down))
    assert isinstance(battery_in_stream, InputStream)
    assert atlas.get_equipment(by_output_stream=battery_in_stream) is not None

    # via str
    battery_in_stream = atlas.get_stream(by_equipments=(e_up.label, e_down.label))
    assert isinstance(battery_in_stream, InputStream)
    assert atlas.get_equipment(by_output_stream=battery_in_stream) is not None


def test_add_and_get_output_stream_w_spec():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    # Creation w Name
    atlas = mock_pmodel()
    atlas.base_equipment_set.add(DummyEquipment)
    e_up = atlas.add_equipment(DummyEquipment, "upstream-1")
    e_down = atlas.add_equipment(DummyEquipment, "downstream-2")
    # s = mock_material_stream_a("stream-01")

    ####################
    # CREATION W SPEC

    # Test getting
    atlas.add_stream(e_up, e_down, stream_type=OutputStream)

    # via obj
    battery_in_stream = atlas.get_stream(by_equipments=(e_up, e_down))
    assert isinstance(battery_in_stream, OutputStream)
    assert atlas.get_equipment(by_output_stream=battery_in_stream) is not None

    # via str
    battery_in_stream = atlas.get_stream(by_equipments=(e_up.label, e_down.label))
    assert isinstance(battery_in_stream, OutputStream)
    assert atlas.get_equipment(by_output_stream=battery_in_stream) is not None


def test_add_and_get_autoname_stream():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    # Creation w Name
    p = mock_pmodel()
    p.base_equipment_set.add(DummyEquipment)
    e_up = p.add_equipment(DummyEquipment, "upstream-1")
    e_down = p.add_equipment(DummyEquipment, "downstream-2")
    p.add_stream(e_up, e_down)

    # Test getting
    s_by_eqpt = p.get_stream(by_equipments=(e_up, e_down))
    assert e_up.label in s_by_eqpt.label and e_down.label in s_by_eqpt.label
    logging.info(f"stream name:{s_by_eqpt.label}")

    s_by_eqpt = p.get_stream(by_equipments=("upstream-1", "downstream-2"))
    assert e_up.label in s_by_eqpt.label and e_down.label in s_by_eqpt.label

    s_by_id = p.get_stream(by_label="upstream-1->downstream-2")
    assert s_by_id.label == "upstream-1->downstream-2"


def test_streams_property():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    p = mock_pmodel()
    p.base_equipment_set.add(DummyEquipment)
    a = p.add_equipment(DummyEquipment, "A")
    b = p.add_equipment(DummyEquipment, "B")
    c = p.add_equipment(DummyEquipment, "C")
    p.add_stream("A", "B")
    p.add_stream("B", "C")
    p.add_stream("A", "C")
    # return p

    # Test getting
    streams = p.streams
    assert len(streams) == 3


def test_equipment_property():

    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    p = mock_pmodel()
    p.base_equipment_set.add(DummyEquipment)
    a = p.add_equipment(DummyEquipment, "A")
    b = p.add_equipment(DummyEquipment, "B")
    c = p.add_equipment(DummyEquipment, "C")
    p.add_stream("A", "B")
    p.add_stream("B", "C")
    p.add_stream("A", "C")
    # return p

    # Test getting
    streams = p.equipments
    assert len(streams) == 3


def test_remove_equipment():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    p = mock_pmodel()
    p.base_equipment_set.add(DummyEquipment)
    e = p.add_equipment(DummyEquipment, "dummy_equipment")
    p.remove_equipment(e)
    with pytest.raises(KeyError):
        p.get_equipment(by_label="dummy_equipment")


def test_remove_stream():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    p = mock_pmodel()
    p.base_equipment_set.add(DummyEquipment)
    e_up = p.add_equipment(DummyEquipment, "upstream-1")
    e_down = p.add_equipment(DummyEquipment, "downstream-2")
    s = mock_material_stream_a("s")
    p.add_stream(e_up, e_down)
    p.remove_stream(by_equipment=(e_up, e_down))
    with pytest.raises(KeyError):
        p.get_stream(by_label="s")


def test_get_compound():
    p = mock_pmodel()
    p.register_compound(CompoundRef.get_vocompound_from_enum(CASCompoundEnum.Water))
    compound = p.retreive_compound(CASCompoundEnum.Water)
    assert isinstance(compound, VOCompound)
    assert compound.id == CASCompoundEnum.Water.value.cas


def test_stream_variable_registration():
    """Test stream variable registration in collection"""

    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    atlas = mock_pmodel()
    atlas.base_equipment_set.add(DummyEquipment)
    e_up = atlas.add_equipment(DummyEquipment, "up")
    e_down = atlas.add_equipment(DummyEquipment, "down")

    # Add logging to trace variable registration
    logging.info("Before stream creation:")
    logging.info(f"Vars: {len(atlas.variables_collection.items)}")

    stream = atlas.add_stream(e_up, e_down)

    logging.info("After stream creation:")
    logging.info(f"Stream vars: {len(stream.get_variables())}")
    logging.info(f"Collection vars: {len(atlas.variables_collection.items)}")

    # Check each variable
    for var in stream.get_variables():
        logging.info(f"Checking var: {var.variable_enum}")
        # Get variable from collection using UID
        collection_var = atlas.variables_collection.get_item(var.uid)
        assert (
            collection_var is not None
        ), f"Variable {var.variable_enum} not found in collection"
        assert collection_var.variable_enum == var.variable_enum
        assert collection_var.uid == var.uid


def test_atlas_compound_init():
    atlas = mock_pmodel()

    # Assert compound registry is empty
    assert len(atlas.compounds_collection.items) == 0

    # Assert compound registry is not empty
    compound = CompoundRef.get_vocompound_from_enum(CASCompoundEnum.Water)
    atlas.register_compound(compound)
    assert len(atlas.compounds_collection.items) == 1


def test_stream_propogation_of_compounds():
    class DummyEquipment(ENTBaseEquipment):
        def __init__(self, equipment_id, plant):
            super().__init__(equipment_id, plant)

    atlas = mock_pmodel()
    atlas.base_equipment_set.add(DummyEquipment)

    e_up = atlas.add_equipment(DummyEquipment, "up")
    e_down = atlas.add_equipment(DummyEquipment, "down")

    # Add compound
    compound = CompoundRef.get_vocompound_from_enum(CASCompoundEnum.Water)
    atlas.register_compound(compound)
    compound = CompoundRef.get_vocompound_from_enum(CASCompoundEnum.Toluene)
    atlas.register_compound(compound)
    compound = CompoundRef.get_vocompound_from_enum(CASCompoundEnum.Benzaldehyde)
    atlas.register_compound(compound)

    # Add logging to trace variable registration
    logging.info("Before stream creation:")
    logging.info(f"Vars: {len(atlas.variables_collection.items)}")

    stream = atlas.add_stream(e_up, e_down)
    atlas.propogate_compounds_across_streams()

    logging.info("After stream creation:")
    logging.info(f"Stream vars: {len(stream.get_variables())}")
    logging.info(f"Collection vars: {len(atlas.variables_collection.items)}")

    # Check each variable
    for var in stream.get_variables():
        logging.info(f"Checking var: {var.variable_enum}")
        if isinstance(var, VOCompoundMassRatio):
            logging.info(f"...{var.compound.label}")
        # Get variable from collection using UID
        collection_var = atlas.variables_collection.get_item(var.uid)
        assert (
            collection_var is not None
        ), f"Variable {var.variable_enum} not found in collection"
        assert collection_var.variable_enum == var.variable_enum
        assert collection_var.uid == var.uid


def test_user_config():
    user_config = UserAtlasConfig(
        user_industry=UserIndustryEnum.CHEMICAL,
        matrix_engine=MatrixEngineEnum.DWSIM
    )
    dump = user_config.model_dump()
    user_config_2 = UserAtlasConfig.model_validate(dump)
    print(dump)
    print(user_config)
    assert user_config == user_config_2
