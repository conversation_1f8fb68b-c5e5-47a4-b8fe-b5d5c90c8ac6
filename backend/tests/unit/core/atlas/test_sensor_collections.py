import pytest
from backend.core._atlas.aggregates import *
from backend.core._atlas.aggregates import VariableUIDCollection, KPIUIDCollection


@pytest.fixture
def mock_atlas() -> AtlasRoot:
    """Return an Atlas object with equipment"""
    mock_atlas = AtlasRoot("test_atlas", "test_plant")
    mock_atlas.add_equipment(Heater, "HT-01")
    mock_atlas.add_equipment(Pump, "PM-01")
    return mock_atlas


@pytest.fixture
def mock_var(mock_atlas) -> VOContinuousVariable:
    """Return a variable to be associated with a sensor"""
    test_heater = mock_atlas.get_equipment(by_label="HT-01")
    var = test_heater.get_variable(ContVarSpecEnum.OutletTemperature)
    if var is None:
        raise ValueError("Unable to get variable from test heater equipment.")
    return var


@pytest.fixture
def mock_sensor(mock_atlas, mock_var) -> VOSensor:
    """Create test sensor that references mock_var"""
    sensor = VOSensor(variable_uid=mock_var.uid, label="S-001")
    mock_atlas.sensor_collection.add_item(sensor)
    return sensor


@pytest.fixture
def mock_var_wo_association(mock_atlas) -> VOContinuousVariable:
    """Returns variable with no sensor association"""
    test_heater = mock_atlas.get_equipment(by_label="HT-01")
    var = test_heater.get_variable(ContVarSpecEnum.PressureDrop)
    if var is None:
        raise ValueError("Unable to get variable from test heater equipment.")
    return var


def test_get_var_from_sensor(mock_atlas, mock_sensor, mock_var):
    var = mock_atlas.get_var_from_sensor(mock_sensor)
    heater = mock_atlas.get_equipment(by_label="HT-01")
    assert var == mock_var


def test_get_sensor_from_var(mock_atlas, mock_sensor, mock_var):
    sensor = mock_atlas.get_sensor_from_var(mock_var)
    assert mock_sensor == sensor


def test_get_sensor_from_var_with_no_association(mock_atlas, mock_var_wo_association):
    sensor = mock_atlas.get_sensor_from_var(mock_var_wo_association)
    assert sensor is None
