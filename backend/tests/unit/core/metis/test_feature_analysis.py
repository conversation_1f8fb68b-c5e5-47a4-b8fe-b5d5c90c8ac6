'''
Author: LO
'''
import pandas as pd
import numpy as np
from backend.core._metis.df_analyzers import FeatureSensitivityAnalyzer
from backend.core._metis.df_analyzers import AnalyserColEnums_FS

# Utility function to generate synthetic data
def generate_test_data(n_samples=100, n_features=5, random_seed=42):
    """
    Generate synthetic data for testing feature importance analysis.
    Creates a dataset where some features are more important than others.
    
    Returns:
        X: Feature DataFrame
        y: Target DataFrame with one column
    """
    np.random.seed(random_seed)
    
    # Create feature data
    X = pd.DataFrame(
        np.random.randn(n_samples, n_features),
        columns=[f"feature_{i}" for i in range(n_features)]
    )
    
    # Create target with known importance
    # feature_0 and feature_2 are important, others are noise
    y_series = pd.Series(
        2 * X["feature_0"] + 0.5 * X["feature_2"] + 0.1 * np.random.randn(n_samples),
        name="target"
    )
    
    # Convert Series to DataFrame to match the new expected format
    y = pd.DataFrame(y_series)
    
    return X, y


class TestBasicFeatureAnalysis:
    # Class variables for test configurations
    estimator_names = [
        "linear", "ridge", "lasso", "random_forest", 
        "gradient_boosting", "support_vector"
    ]
    
    selection_ratios = [
        (0.25, 1),  # (ratio, expected_count) - Select 1 out of 4 features
        (0.5, 2),   # Select 2 out of 4 features
        (0.75, 3),  # Select 3 out of 4 features
        (1.0, 4)    # Select all features
    ]
    
    def test_all_estimators(self):
        """Test that all estimators work with the analyse method."""
        for estimator_name in self.estimator_names:
            # Generate test data
            X, y = generate_test_data()
            
            # Create feature sensitivity analysis with specific estimator
            fsa = FeatureSensitivityAnalyzer(
                estimator_name=estimator_name,
                features_to_select=0.6,  # Select 3 out of 5 features
                n_repeats=2  # Low repeats for faster tests
            )
            
            # Run analysis
            results = fsa.analyse(X, y)
            
            # Basic assertions
            assert isinstance(results, pd.DataFrame)
            assert not results.empty
            assert AnalyserColEnums_FS.SELECTED.value in results.columns
            assert AnalyserColEnums_FS.IMPORTANCE_MEAN.value in results.columns
            print(f"Estimator {estimator_name} passed basic assertions")

    def test_result_structure(self):
        """Test that the result DataFrame has the expected structure."""
        X, y = generate_test_data(n_features=4)
        
        fsa = FeatureSensitivityAnalyzer(
            features_to_select=0.5,  # Select 2 out of 4 features
            n_repeats=2
        )
        
        # Pass include_unselected=True to get rows for all features
        results = fsa.analyse(X, y, include_unselected=True)
        
        # Check structure
        assert isinstance(results, pd.DataFrame)
        assert results.shape[0] == 4  # All 4 features should be included
        
        # Check all expected metrics are present using enums
        expected_metrics = [
            AnalyserColEnums_FS.SELECTED.value,
            AnalyserColEnums_FS.IMPORTANCE_MEAN.value,
            AnalyserColEnums_FS.IMPORTANCE_STD.value,
            AnalyserColEnums_FS.IMPORTANCE_MEAN_PERCENT.value,
            AnalyserColEnums_FS.COL_LABEL_X.value
        ]
        assert all(metric in results.columns for metric in expected_metrics)
        
        # Check feature names in index
        feature_names = [f"feature_{i}" for i in range(4)]
        assert all(feature in results.index for feature in feature_names)
        
        # Check selected features count
        assert results[AnalyserColEnums_FS.SELECTED.value].sum() == 2  # 2 features should be selected
        print("Result structure test passed")

    def test_feature_selection_ratio(self):
        """Test that the feature selection ratio works correctly."""
        for selection_ratio, expected_count in self.selection_ratios:
            X, y = generate_test_data(n_features=4)
            
            fsa = FeatureSensitivityAnalyzer(
                features_to_select=selection_ratio,
                n_repeats=2
            )
            
            results = fsa.analyse(X, y)
            
            # Check number of selected features
            assert results[AnalyserColEnums_FS.SELECTED.value].sum() == expected_count
            print(f"Selection ratio {selection_ratio} correctly selected {expected_count} features")

    def test_feature_importance_identification(self):
        """Test that the most important features have higher importance scores."""
        X, y = generate_test_data()
        
        fsa = FeatureSensitivityAnalyzer(
            features_to_select=0.6,  # Select 3 out of 5 features
            n_repeats=5
        )
        
        results = fsa.analyse(X, y)
        
        # Get selected features
        selected_features = results.index[results[AnalyserColEnums_FS.SELECTED.value]].tolist()
        
        # Our synthetic data makes feature_0 and feature_2 important
        # At least one of these should be selected
        important_features = {"feature_0", "feature_2"}
        assert any(feature in important_features for feature in selected_features)
        
        # The importance scores should be higher for the known important features
        # Get the top 2 features by importance_mean
        top_features = results.nlargest(2, AnalyserColEnums_FS.IMPORTANCE_MEAN.value).index.tolist()
        assert any(feature in important_features for feature in top_features)
        print("Feature importance identification test passed")


# Simple runner to execute tests when the file is run directly
if __name__ == "__main__":
    test_instance = TestBasicFeatureAnalysis()
    
    print("Running tests...")
    test_instance.test_all_estimators()
    test_instance.test_result_structure()
    test_instance.test_feature_selection_ratio()
    test_instance.test_feature_importance_identification()
    print("All tests completed successfully!")