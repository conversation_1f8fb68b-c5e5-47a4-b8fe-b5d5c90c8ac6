import logging
import random
import pytest
import numpy as np
from numpy.testing import assert_array_equal, assert_almost_equal
from backend.core._metis import z_SG_review_SimOptimizer
from backend.core._metis import z_SG_review_SimTester

####################
# DEBUGGING
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
random.seed(42)
####################
# Names and attributes for object instantiation
test_opt_name = "Test"
test_obj_func_name = "Cost"
test_dec_var_names = ["var1", "var2"]
test_dec_var_values = [float(0.5), float(2.5)]
test_dec_var_bounds = [
    [np.float64(0.1), np.float64(0.1)],
    [np.float64(1.0), np.float64(1.0)],
]
test_constraints = ["var1+var2", "var2/var1"]


####################
# Tests
def test_opt_prob_validity():
    test_sim_object = z_SG_review_SimTester.SimTester(name=test_opt_name)
    test_sim_opt = z_SG_review_SimOptimizer.SimOptimizer(
        optimization_name=test_opt_name,
        simulation_object=test_sim_object,
        obj_func_name=test_obj_func_name,
        dec_var_names=test_dec_var_names,
        dec_var_values=test_dec_var_values,
        dec_var_bounds=test_dec_var_bounds,
        constraints=test_constraints,
        constraint_bounds=test_constraint_bounds,
    )
    # Decision variable compliance
    expected_dec_var_values = [np.float64(0.5), np.float64(2.5)]
    has_dec_var_values = hasattr(test_sim_opt, "dec_var_values")
    assert (
        has_dec_var_values is True
    ), "Given decision variable values may not have correct shape or size. Defaulting to None."
    if has_dec_var_values:
        assert_array_equal(
            test_sim_opt.dec_var_values, expected_dec_var_values
        ), "The returned value does not match the expected value."

    # Decision variable bounds
    has_dec_var_bounds_lb = hasattr(test_sim_opt, "dec_var_low_bounds")
    has_dec_var_bounds_ub = hasattr(test_sim_opt, "dec_var_up_bounds")
    assert (
        has_dec_var_bounds_lb is True
    ), "Given decision variable bounds may not have correct shape or size. Defaulting to None."
    assert (
        has_dec_var_bounds_ub is True
    ), "Given decision variable bounds may not have correct shape or size. Defaulting to None."

    # Constraint Objects
    expected_con_object_vars = {
        "var1+var2": ["var1", "var2"],
        "var2/var1": ["var2", "var1"],
    }
    expected_con_var_set = ["var1", "var2"]
    if sorted(test_sim_opt.con_object_vars) == sorted(expected_con_object_vars):
        has_equal_object_vars = True
    else:
        has_equal_object_vars = False

    if sorted(test_sim_opt.con_var_set) == sorted(expected_con_var_set):
        has_equal_var_set = True
    else:
        has_equal_var_set = False

    assert (
        has_equal_object_vars is True
    ), "The given constraints do not have expected constraint variable sets. Check specified constraints."
    assert (
        has_equal_var_set is True
    ), "The given constraints do not have expected constraint variables. Check specified constraints."

    del test_sim_opt
    del test_sim_object


def test_run_optimization_direct():
    test_sim_object = z_SG_review_SimTester.SimTester(name=test_opt_name)

    test_sim_opt = z_SG_review_SimOptimizer.SimOptimizer(
        optimization_name=test_opt_name,
        simulation_object=test_sim_object,
        obj_func_name=test_obj_func_name,
        dec_var_names=test_dec_var_names,
        dec_var_values=test_dec_var_values,
        dec_var_bounds=test_dec_var_bounds,
        constraints=test_constraints,
        constraint_bounds=test_constraint_bounds,
    )

    expected_x_opt = np.array([0.28, 0.22])
    expected_opt_obj_val = 0.435

    (
        test_x_opt,
        test_opt_obj_val,
        test_opt_constraint_values,
        test_opt_constraint_violation,
    ) = test_sim_opt.run_optimization(optimization_approach="Direct", max_iter=150)

    assert_almost_equal(
        test_x_opt, expected_x_opt, decimal=2
    ), "The returned optimal decision variable value from the test does not match expected value."
    assert_almost_equal(
        test_opt_obj_val, expected_opt_obj_val, decimal=2
    ), "The returned optimal objective value from the test does not match expected value."

    del test_sim_opt
    del test_sim_object


def test_run_optimization_de():
    test_sim_object = z_SG_review_SimTester.SimTester(name=test_opt_name)

    test_sim_opt = z_SG_review_SimOptimizer.SimOptimizer(
        optimization_name=test_opt_name,
        simulation_object=test_sim_object,
        obj_func_name=test_obj_func_name,
        dec_var_names=test_dec_var_names,
        dec_var_values=test_dec_var_values,
        dec_var_bounds=test_dec_var_bounds,
        constraints=test_constraints,
        constraint_bounds=test_constraint_bounds,
    )

    expected_x_opt = np.array([0.28, 0.22])
    expected_opt_obj_val = 0.435

    (
        test_x_opt,
        test_opt_obj_val,
        test_opt_constraint_values,
        test_opt_constraint_violation,
    ) = test_sim_opt.run_optimization(
        optimization_approach="Differential_Evolution", max_iter=200
    )

    assert_almost_equal(
        test_x_opt, expected_x_opt, decimal=2
    ), "The returned optimal decision variable value from the test does not match expected value."
    assert_almost_equal(
        test_opt_obj_val, expected_opt_obj_val, decimal=2
    ), "The returned optimal objective value from the test does not match expected value."

    del test_sim_opt
    del test_sim_object


def test_run_optimization_shgo():
    test_sim_object = z_SG_review_SimTester.SimTester(name=test_opt_name)

    test_sim_opt = z_SG_review_SimOptimizer.SimOptimizer(
        optimization_name=test_opt_name,
        simulation_object=test_sim_object,
        obj_func_name=test_obj_func_name,
        dec_var_names=test_dec_var_names,
        dec_var_values=test_dec_var_values,
        dec_var_bounds=test_dec_var_bounds,
        constraints=test_constraints,
        constraint_bounds=test_constraint_bounds,
    )

    expected_x_opt = np.array([0.28, 0.22])
    expected_opt_obj_val = 0.435

    (
        test_x_opt,
        test_opt_obj_val,
        test_opt_constraint_values,
        test_opt_constraint_violation,
    ) = test_sim_opt.run_optimization(optimization_approach="SHGO", max_iter=150)

    assert_almost_equal(
        test_x_opt, expected_x_opt, decimal=2
    ), "The returned optimal decision variable value from the test does not match expected value."
    assert_almost_equal(
        test_opt_obj_val, expected_opt_obj_val, decimal=2
    ), "The returned optimal objective value from the test does not match expected value."

    del test_sim_opt
    del test_sim_object


def test_run_optimization_gpm():
    test_sim_object = z_SG_review_SimTester.SimTester(name=test_opt_name)

    test_sim_opt = z_SG_review_SimOptimizer.SimOptimizer(
        optimization_name=test_opt_name,
        simulation_object=test_sim_object,
        obj_func_name=test_obj_func_name,
        dec_var_names=test_dec_var_names,
        dec_var_values=test_dec_var_values,
        dec_var_bounds=test_dec_var_bounds,
        constraints=test_constraints,
        constraint_bounds=test_constraint_bounds,
    )

    expected_x_opt = np.array([0.28, 0.22])
    expected_opt_obj_val = 0.435

    (
        test_x_opt,
        test_opt_obj_val,
        test_opt_constraint_values,
        test_opt_constraint_violation,
    ) = test_sim_opt.run_optimization(optimization_approach="GP_Minimize", max_iter=150)

    assert_almost_equal(
        test_x_opt, expected_x_opt, decimal=2
    ), "The returned optimal decision variable value from the test does not match expected value."
    assert_almost_equal(
        test_opt_obj_val, expected_opt_obj_val, decimal=2
    ), "The returned optimal objective value from the test does not match expected value."

    del test_sim_opt
    del test_sim_object


def test_run_optimization_pso():
    test_sim_object = z_SG_review_SimTester.SimTester(name=test_opt_name)

    test_sim_opt = z_SG_review_SimOptimizer.SimOptimizer(
        optimization_name=test_opt_name,
        simulation_object=test_sim_object,
        obj_func_name=test_obj_func_name,
        dec_var_names=test_dec_var_names,
        dec_var_values=test_dec_var_values,
        dec_var_bounds=test_dec_var_bounds,
        constraints=test_constraints,
        constraint_bounds=test_constraint_bounds,
    )

    expected_x_opt = np.array([0.28, 0.22])
    expected_opt_obj_val = 0.435

    (
        test_x_opt,
        test_opt_obj_val,
        test_opt_constraint_values,
        test_opt_constraint_violation,
    ) = test_sim_opt.run_optimization(optimization_approach="PSO", max_iter=150)

    assert_almost_equal(
        test_x_opt, expected_x_opt, decimal=2
    ), "The returned optimal decision variable value from the test does not match expected value."
    assert_almost_equal(
        test_opt_obj_val, expected_opt_obj_val, decimal=2
    ), "The returned optimal objective value from the test does not match expected value."

    del test_sim_opt
    del test_sim_object


if __name__ == "__main__":

    test_dec_var_values = [np.float64(0.5), np.float64(2.5)]
    test_dec_var_bounds = [
        [np.float64(0.1), np.float64(0.1)],
        [np.float64(1.0), np.float64(1.0)],
    ]
    test_constraint_bounds = [
        (np.float64(0.5), np.float64(2.0)),
        (np.float64(0.25), np.float64(4.0)),
    ]

    test_opt_prob_validity()  # Test 1: Check validity of the specified optimization problem.
    test_run_optimization_direct()  # Test 2: Check success of optimization using direct optimization algorithm.
    test_run_optimization_gpm()  # Test 5: Check success of optimization using Gaussian Process optimization algorithm.
    test_run_optimization_pso()  # Test 6: Check success of optimization using Particle Swarm optimization algorithm.
