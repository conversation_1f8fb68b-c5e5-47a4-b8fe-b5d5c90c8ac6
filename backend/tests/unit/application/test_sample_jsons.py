from pprint import pprint
import backend.endpoint.v1.schema as v1endpt

EXPERIMENT_CONFIG_BASIC = {
    'title': 'Simulate and predict impact on KPIs by changing process conditions',
    'config_name': 'Pump Use Case',
    'entity_settings': {
        'value': [
            {
                'entity_id': 'P-100',
                'spec_variables': [
                    {
                        'name': 'Power Required',
                        'entity_name': 'P-100',
                        'type': 'Setpoint',
                        'bounds': [0.75, 1.45],
                        'ranges': [0.8075, 0.8925],
                        'unit': 'kW'
                    },
                    {
                        'name': 'Efficiency',
                        'entity_name': 'P-100',
                        'type': 'Condition',
                        'bounds': [0.65, 0.85],
                        'ranges': [0.7124999999999999, 0.7875000000000001],
                        'unit': ''
                    }
                ],
                'entity_type': 'Pump'
            },
            {
                'entity_id': 'From Tank 100 BL->P-100',
                'spec_variables': [
                    {
                        'name': 'Methanol',
                        'entity_name': 'From Tank 100 BL->P-100',
                        'type': 'Setpoint',
                        'bounds': [0, 1],
                        'ranges': [0.5, 0.5],
                        'unit': ''
                    },
                    {
                        'name': 'Water',
                        'entity_name': 'From Tank 100 BL->P-100',
                        'type': 'Setpoint',
                        'bounds': [0, 1],
                        'ranges': [0.5, 0.5],
                        'unit': ''
                    }
                ],
                'entity_type': 'InputStream'
            }
        ]
    },
    'sim_kpi': {
        'name': 'P-100 - Pressure Increase',
        'value': '{{P-100 - Pressure Increase}}'
    }
}
EXPERIMENT_RESULTS_BASIC = {
    'main_summary_text': 'Based on the simulations performed, following variables have the highest impact:  Temperature on Entity Air BL->M-100, Pressure on Entity Air BL->M-100, Mass Flow Rate on Entity Air BL->M-100, Temperature on Entity Oil Feed BL->HEX-100, Pressure on Entity Oil Feed BL->HEX-100, Global Heat Transfer Coefficient on Entity HEX-100, Cold Fluid Pressure Drop on Entity HEX-100, Hot Fluid Pressure Drop on Entity HEX-100.',
    'top_summary_text': '',
    'top_impact': {
        'Air BL->M-100_Temperature': 11.871,
        'Air BL->M-100_Pressure': 10.451,
        'Air BL->M-100_Mass Flow Rate': 22.614,
        'Oil Feed BL->HEX-100_Temperature': 16.549,
        'Oil Feed BL->HEX-100_Pressure': 8.888,
        'HEX-100_Global Heat Transfer Coefficient': 6.96,
        'HEX-100_Cold Fluid Pressure Drop': 13.792,
        'HEX-100_Hot Fluid Pressure Drop': 8.876,
        'Others': 0.0
    },
    'top_variables': [
        {
            'name': 'Temperature',
            'entity': 'Air BL->M-100',
            'type': 'Setpoint',
            'value': 324.056,
            'impact_value': 11.871,
            'unit': 'K'
        },
        {
            'name': 'Pressure',
            'entity': 'Air BL->M-100',
            'type': 'Setpoint',
            'value': 116776.776,
            'impact_value': 10.451,
            'unit': 'Pa'
        },
        {
            'name': 'Mass Flow Rate',
            'entity': 'Air BL->M-100',
            'type': 'Setpoint',
            'value': 0.219,
            'impact_value': 22.614,
            'unit': 'kg/s'
        },
        {
            'name': 'Temperature',
            'entity': 'Oil Feed BL->HEX-100',
            'type': 'Setpoint',
            'value': 330.404,
            'impact_value': 16.549,
            'unit': 'K'
        },
        {
            'name': 'Pressure',
            'entity': 'Oil Feed BL->HEX-100',
            'type': 'Setpoint',
            'value': 98180.416,
            'impact_value': 8.888,
            'unit': 'Pa'
        },
        {
            'name': 'Global Heat Transfer Coefficient',
            'entity': 'HEX-100',
            'type': 'Condition',
            'value': 132.873,
            'impact_value': 6.96,
            'unit': 'W/(m².K)'
        },
        {
            'name': 'Cold Fluid Pressure Drop',
            'entity': 'HEX-100',
            'type': 'Condition',
            'value': 867.027,
            'impact_value': 13.792,
            'unit': 'Pa'
        },
        {
            'name': 'Hot Fluid Pressure Drop',
            'entity': 'HEX-100',
            'type': 'Condition',
            'value': 26.469,
            'impact_value': 8.876,
            'unit': 'Pa'
        }
    ],
    'impact_summary_text': 'Based on the simulation, following weightages are assigned based on how each variable impacts the KPI. Prioritize analyzing the variables with higher weightages.',
    'setpoint_impact_summary': [
        {
            'entity': 'Air BL->M-100',
            'setpoint': 'Temperature',
            'weightage': 11.871,
            'unit': 'K'
        },
        {
            'entity': 'Air BL->M-100',
            'setpoint': 'Pressure',
            'weightage': 10.451,
            'unit': 'Pa'
        },
        {
            'entity': 'Air BL->M-100',
            'setpoint': 'Mass Flow Rate',
            'weightage': 22.614,
            'unit': 'kg/s'
        },
        {
            'entity': 'Oil Feed BL->HEX-100',
            'setpoint': 'Temperature',
            'weightage': 16.549,
            'unit': 'K'
        },
        {
            'entity': 'Oil Feed BL->HEX-100',
            'setpoint': 'Pressure',
            'weightage': 8.888,
            'unit': 'Pa'
        }
    ],
    'condition_impact_summary': [
        {
            'entity': 'HEX-100',
            'condition': 'Global Heat Transfer Coefficient',
            'weightage': 6.96,
            'unit': 'W/(m².K)'
        },
        {
            'entity': 'HEX-100',
            'condition': 'Cold Fluid Pressure Drop',
            'weightage': 13.792,
            'unit': 'Pa'
        },
        {
            'entity': 'HEX-100',
            'condition': 'Hot Fluid Pressure Drop',
            'weightage': 8.876,
            'unit': 'Pa'
        }
    ],
    'simulated_summary': {
        'simulated_data': [
            # Scenario 0
            {
                'scenario': 'Scenario 0',
                'entity_specification': [
                    {
                        'entity': 'Air BL->M-100',
                        'variables': [
                            {
                                'name': 'Temperature',
                                'type': 'Setpoint',
                                'value': 329.657,
                                'unit': 'K'
                            },
                            {
                                'name': 'Pressure',
                                'type': 'Setpoint',
                                'value': 201692.132,
                                'unit': 'Pa'
                            },
                            {
                                'name': 'Mass Flow Rate',
                                'type': 'Setpoint',
                                'value': 0.132,
                                'unit': 'kg/s'
                            }
                        ]
                    },
                    {
                        'entity': 'Oil Feed BL->HEX-100',
                        'variables': [
                            {
                                'name': 'Temperature',
                                'type': 'Setpoint',
                                'value': 319.102,
                                'unit': 'K'
                            },
                            {
                                'name': 'Pressure',
                                'type': 'Setpoint',
                                'value': 100990.235,
                                'unit': 'Pa'
                            },
                            {
                                'name': 'Mass Flow Rate',
                                'type': 'Setpoint',
                                'value': 0.887,
                                'unit': 'kg/s'
                            }
                        ]
                    },
                    {
                        'entity': 'HEX-100',
                        'variables': [
                            {
                                'name': 'Global Heat Transfer Coefficient',
                                'type': 'Condition',
                                'value': 114.215,
                                'unit': 'W/(m².K)'
                            },
                            {
                                'name': 'Heat Exchange Area',
                                'type': 'Condition',
                                'value': 1.942,
                                'unit': 'm²'
                            },
                            {
                                'name': 'Cold Fluid Pressure Drop',
                                'type': 'Condition',
                                'value': 843.634,
                                'unit': 'Pa'
                            },
                            {
                                'name': 'Hot Fluid Pressure Drop',
                                'type': 'Condition',
                                'value': 25.167,
                                'unit': 'Pa'
                            },
                            {
                                'name': 'Heat Loss',
                                'type': 'Condition',
                                'value': 1.896,
                                'unit': 'kW'
                            }
                        ]
                    }
                ],
                'kpi': 'HEX-100 Heat Exchange',
                'kpi_value': 0.0
            },
            # Remaining 143 scenarios follow the same structure - showing just a few more as examples
            {
                'scenario': 'Scenario 1',
                'entity_specification': [
                    # Similar structure as Scenario 0
                ],
                'kpi': 'HEX-100 Heat Exchange',
                'kpi_value': 63.434
            },
            # ... Additional scenarios 2-142 ...
            {
                'scenario': 'Scenario 143',
                'entity_specification': [
                    # Similar structure
                ],
                'kpi': 'HEX-100 Heat Exchange',
                'kpi_value': 36.616
            }
        ]
    }
}

DIAGNOSTIC_CONFIG_BASIC = {
    'title': 'Manually input operational data and let Aleph pinpoint inefficiency in my operation',
    'config_name': 'Pump Use Case',
    'entity_settings': {
        'value': [
            {
                'entity_id': 'P-100',
                'entity_type': 'Pump',
                'spec_variables': [
                    {
                        'name': 'Power Required',
                        'entity_name': 'P-100',
                        'type': 'Setpoint',
                        'bounds': [0.75, 1.45],
                        'ranges': [0.8075, 0.8925],
                        'value': 0.85,
                        'unit': 'kW'
                    },
                    {
                        'name': 'Efficiency',
                        'entity_name': 'P-100',
                        'type': 'Condition',
                        'bounds': [0.65, 0.85],
                        'ranges': [0.7124999999999999, 0.7875000000000001],
                        'value': 0.75,
                        'unit': ''
                    }
                ]
            },
            {
                'entity_id': 'From Tank 100 BL->P-100',
                'entity_type': 'InputStream',
                'spec_variables': [
                    {
                        'name': 'Methanol',
                        'entity_name': 'From Tank 100 BL->P-100',
                        'type': 'Setpoint',
                        'bounds': [0, 1],
                        'ranges': [0.5, 0.5],
                        'value': 0.5,
                        'unit': ''
                    },
                    {
                        'name': 'Water',
                        'entity_name': 'From Tank 100 BL->P-100',
                        'type': 'Setpoint',
                        'bounds': [0, 1],
                        'ranges': [0.5, 0.5],
                        'value': 0.5,
                        'unit': ''
                    }
                ]
            }
        ]
    },
    'sim_kpi': {
        'name': 'P-100 - Pressure Increase',
        'value': '{{P-100 - Pressure Increase}}',
        'range': [-1, 3001325]
    }
}

DIAGNOSTIC_RESULT_BASIC = {
    'main_summary_text': 'The specified KPI range is outside the simulated KPI values. Consider changing the KPI range. Based on the provided data, the current value of the selected KPI is 1.5 % lower than expected. Following variables are contributing the most to this difference:  Power Required on Entity P-100.',
    'top_summary_text': 'Focus on the following variables to improve KPI ',
    'top_impact': {
        'P-100_Power Required': 100.0,
        'Others': 0.0
    },
    'top_variables': [
        {
            'name': 'Power Required',
            'entity': 'P-100',
            'type': 'Setpoint',
            'value': 0.85,
            'impact_value': 100.0,
            'unit': 'kW'
        }
    ],
    'assessment_summary': 'Actual KPI is 1.5 lower than the expected.',
    'assessment': {
        'lower_bound': 540544.14,
        'upper_bound': 626991.988,
        'current_kpi': 580221.4558166019,
        'expected_kpi': 581514.494
    },
    'sensitivity_analysis_text': 'Focus on the following variables to achieve the expected KPI value.',
    'setpoint_impact_summary': [
        {
            'entity': 'P-100',
            'setpoint': 'Power Required',
            'weightage': 100.0,
            'unit': 'kW'
        }
    ],
    'condition_impact_summary': [],
    'analysis': [
        {
            'entity_type': 'Pump',
            'entity': 'P-100',
            'name': 'Power Required',
            'current_value': 0.85,
            'recommended_value': 0.85,
            'diagnosis_text': 'Potential causes of the given variable are not found in diagnosis.',
            'unit': 'kW'
        }
    ]
}


#############################

json_samples =[
            (EXPERIMENT_CONFIG_BASIC, v1endpt.ExperimentConfig),
            (EXPERIMENT_RESULTS_BASIC, v1endpt.ExperimentResults),
            (DIAGNOSTIC_CONFIG_BASIC, v1endpt.DiagnosticConfig),
            (DIAGNOSTIC_RESULT_BASIC, v1endpt.DiagnosticResults),
            ]

class TestSampleJSON():
    '''Tests the configuration strategy map at creating their associated Pydantic Objects. This is to ensure they are valid'''
    
    def test_all_samples(self):
        """Test all sample dictionaries at once."""
        for sample_dict, model_class in json_samples:
            print(f"\nTesting application_dto: `{model_class.__name__}`...")
            # Create the Pydantic object from the dictionary
            pydantic_object = model_class(**sample_dict)
            # Verify object was created successfully
            assert pydantic_object is not None, f"Failed to create {model_class.__name__}"
            print("... Passed")
        print("\nAll sample tests passed")


# Simple runner to execute tests when the file is run directly
if __name__ == "__main__":
    print("Running experiment schema tests...")
    
    test_instance = TestSampleJSON()
    test_instance.test_all_samples()
    
    print("All tests completed successfully!")
