import pytest
import logging
from pprint import pformat
from backend.endpoint.v1.schema import *
import backend.core._atlas.aggregates as at


def log_schema(schema, msg="Schema"): 
    """Consistent schema logging"""
    logging.info(f"\n{msg}:\n{pformat(schema.model_dump(), indent=2)}")


@pytest.fixture
def mock_atlas():
    return at.AtlasRoot("test_model", "test_plant")


class TestPresetDiscreteVar:
    def test_basic_hydration(self, mock_atlas):
        """Test basic discrete var hydration"""
        var = at.VODiscreteVariable(
            variable_enum=at.DiscreteSetSpecEnum.CalculationType,
            init_value=at.DiscreteItemSpecEnum.HeatAddedOrRemoved,
            bounds={
                at.DiscreteItemSpecEnum.HeatAddedOrRemoved,
                at.DiscreteItemSpecEnum.TemperatureChange,
                at.DiscreteItemSpecEnum.OutletTemperature,
                at.DiscreteItemSpecEnum.OutletVaporMoleFraction,
            },
        )

        schema = Preset_DiscreteVar.hydrate_schema(var, mock_atlas)
        log_schema(schema, "Discrete Schema")

        assert schema.title == "Calculation Type Selections"
        assert schema.value == "Heat Added Or Removed"
        assert "Heat Added Or Removed" in schema.options
        assert len(schema.prerequisites) == 0

    def test_with_prerequisites(self, mock_atlas):
        """Test discrete var with prerequisites"""
        var = at.VODiscreteVariable(
            variable_enum=at.DiscreteSetSpecEnum.FluidInTubes,
            init_value=None,
            bounds={
                at.DiscreteItemSpecEnum.FluidInTubes_Cold,
                at.DiscreteItemSpecEnum.FluidInTubes_Hot,
            },
            prerequisites_for_selection=(
                {at.DiscreteItemSpecEnum.ShellAndTubesExchangerFoulingFactor},
                {at.DiscreteItemSpecEnum.ShellAndTubesExchangerRating},
            ),
        )

        schema = Preset_DiscreteVar.hydrate_schema(var, mock_atlas)
        assert len(schema.prerequisites) > 0


class TestPresetContVar:
    def test_basic_hydration(self, mock_atlas):
        """Test continuous var hydration"""
        var = at.VOContinuousVariable(
            variable_enum=at.ContVarSpecEnum.OutletTemperature,
            bounds=(0, 100),
            category=at.VariableCategoryEnum.SETPOINT,
        )

        schema = Preset_ContVar.hydrate_schema(var, mock_atlas, at.ENTBaseEquipment)
        log_schema(schema, "Continuous Schema")

        assert schema.title == "Outlet Temperature"
        assert schema.unit == "K"
        assert schema.bounds == (0, 100)


class TestPresetEquipmentType:
    def test_heater_presets(self, mock_atlas):
        """Test heater equipment presets"""
        schema = PresetEquipmentType.hydrate_schema(at.Heater, mock_atlas)
        log_schema(schema, "Heater Presets")

        assert schema.type == "Heater"
        assert len(schema.selections) > 0
        assert len(schema.setpoints) > 0

        # Verify sorting
        for items in [schema.selections, schema.setpoints, schema.conditions]:
            prereq_lengths = [len(x.prerequisites or []) for x in items]
            assert prereq_lengths == sorted(prereq_lengths)

    def test_equipment_presets(self, mock_atlas):
        """Test heater equipment presets"""

        def _test_equipment_presets(Equipment: Type[at.ENTBaseEquipment]):
            """Test heater equipment presets"""
            schema = PresetEquipmentType.hydrate_schema(Equipment, mock_atlas)
            log_schema(schema, f"{Equipment.get_entity_type()}")

            assert schema.type == f"{Equipment.get_entity_type()}"
            assert len(schema.selections) > 0
            assert len(schema.setpoints) > 0

            # Verify sorting
            for items in [schema.selections, schema.setpoints, schema.conditions]:
                prereq_lengths = [len(x.prerequisites or []) for x in items]
                assert prereq_lengths == sorted(prereq_lengths)

        equipments = [
            at.HeatExchanger,
        ]

        for e in equipments:
            _test_equipment_presets(e)


class TestPresetStreamType:
    def test_material_stream(self, mock_atlas):
        """Test material stream presets"""
        schema = PresetStreamType.hydrate_schema(at.MaterialStream, mock_atlas)
        log_schema(schema, "Stream Presets")

        assert schema.type == "MaterialStream"
        assert len(schema.selections) >= 0
        assert len(schema.setpoints) >= 0

        # Verify categories routed correctly
        for sp in schema.setpoints:
            assert sp.title != "Conversions"

    def test_input_stream(self, mock_atlas):
        """Test material stream presets"""
        schema = PresetStreamType.hydrate_schema(at.InputStream, mock_atlas)
        log_schema(schema, "Stream Presets")

        assert schema.type == "InputStream"
        assert len(schema.selections) >= 0
        assert len(schema.setpoints) >= 0

        # Verify categories routed correctly
        for sp in schema.setpoints:
            assert sp.title != "Conversions"
