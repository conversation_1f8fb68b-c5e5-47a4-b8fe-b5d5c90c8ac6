from backend.tests._imports import *
from backend.tests.unit.application.test_sample_jsons import (
    EXPERIMENT_CONFIG_BASIC,
    EXPERIMENT_RESULTS_BASIC,
    DIAGNOSTIC_CONFIG_BASIC,
    DIAGNOSTIC_RESULT_BASIC,
)
import backend.endpoint.v1.schema as v1endpt
from backend.application.api_services import APIService

USER_ID = "user"


def test_experiment_service():
    api_svc = APIService()
    config = v1endpt.ExperimentConfig.model_validate(EXPERIMENT_CONFIG_BASIC)
    result = api_svc.run_experiment(config, USER_ID)
    assert isinstance(result, v1endpt.ExperimentResults)


def test_diagnostic_service():
    api_svc = APIService()
    config = v1endpt.DiagnosticConfig.model_validate(DIAGNOSTIC_CONFIG_BASIC)
    result = api_svc.run_diagnostic(config, USER_ID)
    assert isinstance(result, v1endpt.DiagnosticResults)


if __name__ == "__main__":
    test_experiment_service()
