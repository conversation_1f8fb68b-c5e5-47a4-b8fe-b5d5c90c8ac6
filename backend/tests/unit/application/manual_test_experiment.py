from backend.endpoint.v1.schema import (
    ExperimentConfig,
    ExperimentResults,
)
import asyncio
import logging
from backend.infrastructure._db.dto import AtlasJSONSerializer
import json
import backend.core._atlas.aggregates as at
import backend.core._matrix.matrix_dwsim as ma
import backend.infrastructure._db.dto as dto
import pandas as pd
import backend.infrastructure._db.repo as re
from pydantic import BaseModel, field_validator
from pprint import pprint, pformat
from typing import Dict, Any
from datetime import datetime
import json
import uuid

from backend.tests.integration.services.manual_test_prefect_sim_task import (
    PREFECT_LOG,
)


class PydanticLogs(BaseModel):
    simulation_id: str
    atlas_json: str
    kpi_name: str
    sample_dict: Dict[str, Any]  # error,


experiment_config_str = {
    "title": "Simulate and predict impact on KPIs by changing process conditions",
    "config_name": "SWANCOR HE-2",
    "entity_settings": {
        "value": [
            {
                "entity_id": "HE-2",
                "spec_variables": [
                    {
                        "name": "Cold Fluid Outlet Temperature",
                        "entity_name": "HE-2",
                        "type": "Setpoint",
                        "bounds": [-1000, 1000],
                        "ranges": [287.99249999999995, 318.3075],
                        "unit": "K",
                    },
                    {
                        "name": "Heat Loss",
                        "entity_name": "HE-2",
                        "type": "Condition",
                        "bounds": [0, 10],
                        "ranges": [0, 5],
                        "unit": "kW",
                    },
                    {
                        "name": "Heat Exchange Area",
                        "entity_name": "HE-2",
                        "type": "Condition",
                        "bounds": [-1000, 1000],
                        "ranges": [19.095, 21.105000000000004],
                        "unit": "m²",
                    },
                    {
                        "name": "Cold Fluid Pressure Drop",
                        "entity_name": "HE-2",
                        "type": "Condition",
                        "bounds": [0, 50000],
                        "ranges": [0, 5],
                        "unit": "Pa",
                    },
                    {
                        "name": "Hot Fluid Pressure Drop",
                        "entity_name": "HE-2",
                        "type": "Condition",
                        "bounds": [0, 30000],
                        "ranges": [0, 5],
                        "unit": "Pa",
                    },
                ],
                "entity_type": "HeatExchanger",
            },
            {
                "entity_id": "BatteryInput001->Valve-1",
                "spec_variables": [
                    {
                        "name": "Methacrylic Acid",
                        "entity_name": "BatteryInput001->Valve-1",
                        "type": "Setpoint",
                        "bounds": [0, 1],
                        "ranges": [1.0, 1.0],
                        "unit": "",
                    }
                ],
                "entity_type": "InputStream",
            },
            {
                "entity_id": "BatteryInput002->HE-2",
                "spec_variables": [
                    {
                        "name": "Water",
                        "entity_name": "BatteryInput002->HE-2",
                        "type": "Setpoint",
                        "bounds": [0, 1],
                        "ranges": [1.0, 1.0],
                        "unit": "",
                    }
                ],
                "entity_type": "InputStream",
            },
        ]
    },
    "sim_kpi": {
        "name": "Vapor Loss",
        "value": "{{V-1->BatteryOutput001 - Mass Flow Rate}}",
    },
}
experiment_config = ExperimentConfig.model_validate(experiment_config_str)

REFECT_WILD_MOSQUITO = json.dumps(
    {
        "kpi_name": "Vapor Loss",
        "atlas_json": '{"label":"SWANCOR HE-2","description":"","version":0,"uid":"abaef1c2-7364-4dee-b3ec-e98eb180037f","plant_id":"auth0|67bf08b179a52a2858a274f6","compounds":[{"uid":"5bc0f6fd-c59f-483a-b757-f3e914a212f5","id":"7732-18-5","label":"Water","description":"na"},{"uid":"3782bb5a-5762-4230-a407-936d4c206541","id":"79-41-4","label":"Methacrylic acid","description":"na"}],"equipments":[{"entity_type":"Valve","label":"Valve-1","uid":"b07a77b5-**************-79c14fb39c89","states":[{"uid":"db83aae3-cd06-451c-9280-20eb0cd5f612","collection":"varcollection_discrete","var_key":"calculation_type_selections","val":"pressure_drop","bounds":null},{"uid":"70a0150b-538a-43a2-9e36-84ac21bfe042","collection":"varcollection_contvar","var_key":"outlet pressure","val":0.0,"bounds":[0.0,100000000.0]},{"uid":"48b9343d-ca8d-4398-a66d-794fef119424","collection":"varcollection_contvar","var_key":"pressure drop","val":0.0,"bounds":[0.0,100000.0]}]},{"entity_type":"Valve","label":"Valve-2","uid":"198c2815-e1d2-4b71-91e3-************","states":[{"uid":"69ce4645-dcaf-49cd-9c79-bd11d6dd5513","collection":"varcollection_discrete","var_key":"calculation_type_selections","val":"pressure_drop","bounds":null},{"uid":"ae4d0c0f-271d-496d-8a91-412331a26f9a","collection":"varcollection_contvar","var_key":"outlet pressure","val":0.0,"bounds":[0.0,100000000.0]},{"uid":"d13d4d2e-5c89-48d9-abeb-59751fe7e5b7","collection":"varcollection_contvar","var_key":"pressure drop","val":0.0,"bounds":[0.0,100000.0]}]},{"entity_type":"HeatExchanger","label":"HE-2","uid":"8bd186a2-f24a-4638-b153-4313dbdddff3","states":[{"uid":"541a60f3-8975-4546-a53f-1d1b6228b802","collection":"varcollection_discrete","var_key":"flow_direction_selections","val":"counter_current","bounds":null},{"uid":"23896c32-07fa-4016-8d78-ff6f74fd50e6","collection":"varcollection_discrete","var_key":"calculation_type_selections","val":"hot_fluid_outlet_temperature","bounds":null},{"uid":"8ec0bc45-b6a0-4ead-bb37-e700711d73f0","collection":"varcollection_discrete","var_key":"fluid_in_tubes_selections","val":null,"bounds":null},{"uid":"4b1bfa3d-2db9-4c4f-8591-6af3c9c622ee","collection":"varcollection_discrete","var_key":"tube_layout_selections","val":null,"bounds":null},{"uid":"7253c5f0-6bdb-4803-955f-b4c529738230","collection":"varcollection_contvar","var_key":"internal diameter of tube","val":50.0,"bounds":[1.0,1000.0]},{"uid":"f0c6f0c2-e5e9-474f-96c8-75882a47eee3","collection":"varcollection_contvar","var_key":"cold fluid outlet temperature","val":303.15,"bounds":[-1000.0,1000.0]},{"uid":"555c9e92-86e5-42ad-b5fa-76694bf4ce52","collection":"varcollection_contvar","var_key":"roughness tube","val":0.045,"bounds":[0.0,1000.0]},{"uid":"8d10d0d2-65f4-441b-ba84-695bd2527fbf","collection":"varcollection_contvar","var_key":"baffle spacing","val":250.0,"bounds":[0.0,10000.0]},{"uid":"3e1c6582-d4bc-450c-a7a0-a8623d40a07a","collection":"varcollection_contvar","var_key":"hot fluid outlet temperature","val":298.15,"bounds":[-1000.0,1000.0]},{"uid":"7ced8316-dade-419b-9966-e49c08977633","collection":"varcollection_contvar","var_key":"shell in series","val":1.0,"bounds":[0.0,1000.0]},{"uid":"34520fb6-3754-42f2-96b9-ad080244e98e","collection":"varcollection_contvar","var_key":"tube fouling","val":0.0,"bounds":[0.0,1000.0]},{"uid":"914f35b7-3bfb-49ef-931e-82edb3a687c0","collection":"varcollection_contvar","var_key":"heat exchange area","val":20.1,"bounds":[-1000.0,1000.0]},{"uid":"b229f56b-4b53-456e-af13-3c291b074dec","collection":"varcollection_contvar","var_key":"minimum temperature difference","val":0.0,"bounds":[-1000.0,1000.0]},{"uid":"f2eb55de-8935-4e44-911c-b53f0ae44a83","collection":"varcollection_contvar","var_key":"heat loss","val":0.0,"bounds":[0.0,10.0]},{"uid":"993b8f06-d65b-4c46-bbe6-0995137d9027","collection":"varcollection_contvar","var_key":"heat transfer efficiency","val":0.0,"bounds":[0.0,1.0]},{"uid":"bad4d979-2cc9-48b7-8a5b-738ae5f5b9eb","collection":"varcollection_contvar","var_key":"cold fluid pressure drop","val":0.0,"bounds":[0.0,50000.0]},{"uid":"81d2be55-a6d5-4272-b3ae-a6dfd5313466","collection":"varcollection_contvar","var_key":"external diameter of tube","val":60.0,"bounds":[1.0,1000.0]},{"uid":"1106252a-68a5-455d-8343-c4ff2d50a90e","collection":"varcollection_contvar","var_key":"outlet vapor fraction fluid 2","val":0.0,"bounds":[0.0,1.0]},{"uid":"3a2a89bb-aa3e-4215-adae-79d8ff08c0ae","collection":"varcollection_contvar","var_key":"hot fluid pressure drop","val":0.0,"bounds":[0.0,30000.0]},{"uid":"ad40a326-4114-4e6e-add7-a345fd5198b9","collection":"varcollection_contvar","var_key":"internal diameter of shell","val":500.0,"bounds":[0.0,10000.0]},{"uid":"921ccc53-ec66-4693-b600-90d7b76f6b63","collection":"varcollection_contvar","var_key":"passes per shell","val":2.0,"bounds":[0.0,1000.0]},{"uid":"821cd765-bce3-4272-b745-d736c959c6a2","collection":"varcollection_contvar","var_key":"thermal conductivity of tube","val":70.0,"bounds":[0.0,1000.0]},{"uid":"83a2a0b0-06b7-47bc-a616-3d9f6a5f9a7c","collection":"varcollection_contvar","var_key":"tube length","val":5.0,"bounds":[1.0,1000.0]},{"uid":"9405af9f-2e2b-4ee0-9c1a-3c1257627767","collection":"varcollection_contvar","var_key":"heat exchange","val":0.0,"bounds":[-1000.0,1000.0]},{"uid":"05c4069e-c067-4a77-9548-63cee3dd9519","collection":"varcollection_contvar","var_key":"tube spacing","val":70.0,"bounds":[0.0,1000.0]},{"uid":"81376a1b-5ed5-4afa-8d7c-b02b2acc9542","collection":"varcollection_contvar","var_key":"outlet vapor fraction fluid 1","val":0.0,"bounds":[0.0,1.0]},{"uid":"14025548-fecc-448b-bba3-6635de919111","collection":"varcollection_contvar","var_key":"baffle cut","val":20.0,"bounds":[0.0,100.0]},{"uid":"bf926f89-3d9d-4357-b79c-8e6dc17443a2","collection":"varcollection_contvar","var_key":"shell passes","val":2.0,"bounds":[0.0,10000.0]},{"uid":"bd3f9ce0-548b-43a6-8846-9dc9877326df","collection":"varcollection_contvar","var_key":"global heat transfer coefficient","val":1000.0,"bounds":[-1000.0,1000.0]},{"uid":"b651789a-aa69-495e-ba97-119c3768a3f5","collection":"varcollection_contvar","var_key":"shell fouling","val":0.0,"bounds":[0.0,1000.0]},{"uid":"39efa8ec-f65d-43bc-8871-406678b165f1","collection":"varcollection_contvar","var_key":"tubes per shell","val":50.0,"bounds":[0.0,1000.0]}]},{"entity_type":"Vessel","label":"V-1","uid":"6011c3bb-f126-44c4-915b-678b1dd13bb6","states":[{"uid":"755f4402-754c-44ef-8582-5fb4705bd8c3","collection":"varcollection_discrete","var_key":"pressure_calculation_selections","val":"inlet_minimum","bounds":null},{"uid":"aba14c45-b5b3-47d4-8854-514f30d47995","collection":"varcollection_discrete","var_key":"override_pressure_selections","val":"no_override_pressure","bounds":null},{"uid":"37ebc698-6034-4a9e-b3dc-359e1c8b6bfa","collection":"varcollection_discrete","var_key":"override_temperature_selections","val":"no_override_temperature","bounds":null},{"uid":"319ebb39-f58d-4d18-b890-dd706a733abd","collection":"varcollection_contvar","var_key":"override saturation pressure","val":101325.0,"bounds":[101325.0,10000000.0]},{"uid":"b192d8aa-4933-489e-b308-48900ae56c9c","collection":"varcollection_contvar","var_key":"override saturation temp","val":298.15,"bounds":[0.0,100000000.0]}]},{"entity_type":"BatteryIn","label":"BatteryInput001","uid":"d47678a8-c5f6-44d3-a8f3-636dc7c87c17","states":[]},{"entity_type":"BatteryIn","label":"BatteryInput002","uid":"0d3259e7-9266-4698-b8f0-21e435f6a0c2","states":[]},{"entity_type":"BatteryOut","label":"BatteryOutput001","uid":"23f25a47-f20d-4abe-8382-666b4d2d4cf3","states":[]},{"entity_type":"BatteryOut","label":"BatteryOutput002","uid":"13b59282-f7ad-447f-8dd9-1c5eb35ff4b4","states":[]},{"entity_type":"BatteryOut","label":"BatteryOutput003","uid":"1d033d84-6fd5-4670-aed9-b64eac9e68bf","states":[]}],"streams":[{"entity_type":"MaterialStream","uid":"f2eb252f-c832-47e7-87a1-a9ad4bf18535","connection_uuid":["b07a77b5-**************-79c14fb39c89","8bd186a2-f24a-4638-b153-4313dbdddff3"],"connection_label":["Valve-1","HE-2"],"stream_label":"Valve-1->HE-2","states":[{"uid":"01a0009c-0b4c-4008-a915-ac120f31b11b","collection":"varcollection_discrete","var_key":"material_stream_basis_selections","val":"mass_basis","bounds":null},{"uid":"d4552c81-99b6-40cf-bc82-0ef5fcdef7e9","collection":"varcollection_discrete","var_key":"flash_spec_selections","val":"temperature_and_pressure","bounds":null},{"uid":"ff537ab4-7548-4e71-b9dd-5c77b27d9299","collection":"varcollection_contvar","var_key":"mass flow rate","val":10.0,"bounds":[0.0,100000000.0]},{"uid":"48bcb1fb-be2d-4a64-a294-395fd5f1aabe","collection":"varcollection_contvar","var_key":"temperature","val":300.0,"bounds":[0.0,100000000.0]},{"uid":"da0b1761-68ee-42f7-bd2d-aa1ddc223234","collection":"varcollection_contvar","var_key":"pressure","val":101325.0,"bounds":[0.0,100000000.0]},{"uid":"c03194d6-0ae9-42ed-8489-c61a9b46feee","collection":"varcollection_compoundmassmix","var_key":"7732-18-5","val":0.0,"bounds":null},{"uid":"1eea82b1-03b6-4399-8558-dfa8618f19d8","collection":"varcollection_compoundmassmix","var_key":"79-41-4","val":0.0,"bounds":null}]},{"entity_type":"MaterialStream","uid":"d5de245c-0743-4ba5-99d4-a83a813575dd","connection_uuid":["198c2815-e1d2-4b71-91e3-************","6011c3bb-f126-44c4-915b-678b1dd13bb6"],"connection_label":["Valve-2","V-1"],"stream_label":"Valve-2->V-1","states":[{"uid":"37846314-e2f7-4319-a67a-c6490a1f6278","collection":"varcollection_discrete","var_key":"material_stream_basis_selections","val":"mass_basis","bounds":null},{"uid":"4069cafd-f6b1-4f4f-8f03-3f85891c3407","collection":"varcollection_discrete","var_key":"flash_spec_selections","val":"temperature_and_pressure","bounds":null},{"uid":"8c93392f-cd1f-44eb-b49b-29c486ffe243","collection":"varcollection_contvar","var_key":"mass flow rate","val":10.0,"bounds":[0.0,100000000.0]},{"uid":"036c8dd0-d909-4c63-a95d-399c7e0170ed","collection":"varcollection_contvar","var_key":"temperature","val":300.0,"bounds":[0.0,100000000.0]},{"uid":"8a6fb5ee-0773-49cd-845e-25db4754af38","collection":"varcollection_contvar","var_key":"pressure","val":101325.0,"bounds":[0.0,100000000.0]},{"uid":"ee43fb69-89da-4476-ba34-489b55a5c7ed","collection":"varcollection_compoundmassmix","var_key":"7732-18-5","val":0.0,"bounds":null},{"uid":"90a02046-2a2c-4f82-ba6d-c003e65b7b60","collection":"varcollection_compoundmassmix","var_key":"79-41-4","val":0.0,"bounds":null}]},{"entity_type":"MaterialStream","uid":"99b1ac4e-04b6-47c4-a7f1-f1aa5a39e889","connection_uuid":["8bd186a2-f24a-4638-b153-4313dbdddff3","198c2815-e1d2-4b71-91e3-************"],"connection_label":["HE-2","Valve-2"],"stream_label":"HE-2->Valve-2","states":[{"uid":"f484b06a-c0f0-41e0-9210-e335cc2a1d15","collection":"varcollection_discrete","var_key":"material_stream_basis_selections","val":"mass_basis","bounds":null},{"uid":"16d894a8-ad9e-4186-969e-816b67082649","collection":"varcollection_discrete","var_key":"flash_spec_selections","val":"temperature_and_pressure","bounds":null},{"uid":"62d50b38-9aa4-470e-8c0b-14644f5fd1b3","collection":"varcollection_contvar","var_key":"mass flow rate","val":10.0,"bounds":[0.0,100000000.0]},{"uid":"9aa21010-a2c9-4af6-aea7-ac0c437e0148","collection":"varcollection_contvar","var_key":"temperature","val":300.0,"bounds":[0.0,100000000.0]},{"uid":"47e3b694-e88e-4fc6-b2e2-ad87d0e523ca","collection":"varcollection_contvar","var_key":"pressure","val":101325.0,"bounds":[0.0,100000000.0]},{"uid":"2581f342-f526-4d41-b0de-219af8f9352e","collection":"varcollection_compoundmassmix","var_key":"7732-18-5","val":0.0,"bounds":null},{"uid":"b715e8ed-a106-4c2f-8a00-e7b7e9a1a4b1","collection":"varcollection_compoundmassmix","var_key":"79-41-4","val":0.0,"bounds":null}]},{"entity_type":"OutputStream","uid":"e2231df4-207b-44b7-8098-086f643ba509","connection_uuid":["8bd186a2-f24a-4638-b153-4313dbdddff3","1d033d84-6fd5-4670-aed9-b64eac9e68bf"],"connection_label":["HE-2","BatteryOutput003"],"stream_label":"HE-2->BatteryOutput003","states":[{"uid":"e9fa6faa-4f7b-4e9a-af59-43bcdfb97e1f","collection":"varcollection_discrete","var_key":"material_stream_basis_selections","val":"mass_basis","bounds":null},{"uid":"831ea7cd-14e1-4bb1-8741-f236604bcc46","collection":"varcollection_discrete","var_key":"flash_spec_selections","val":"temperature_and_pressure","bounds":null},{"uid":"8565916c-bec0-4c61-ab1f-4d9d481fe23c","collection":"varcollection_contvar","var_key":"mass flow rate","val":10.0,"bounds":[0.0,100000000.0]},{"uid":"07b94241-97da-49eb-acf1-70c281a25a24","collection":"varcollection_contvar","var_key":"temperature","val":300.0,"bounds":[0.0,100000000.0]},{"uid":"0ab74d2a-66a1-44ac-ad25-b0e2fa77dad3","collection":"varcollection_contvar","var_key":"pressure","val":101325.0,"bounds":[0.0,100000000.0]},{"uid":"be5c2321-7285-4b98-b4ac-aaca19c12acb","collection":"varcollection_compoundmassmix","var_key":"7732-18-5","val":0.0,"bounds":null},{"uid":"5252eeb7-1adc-4fb7-9972-4ff0eac4d979","collection":"varcollection_compoundmassmix","var_key":"79-41-4","val":0.0,"bounds":null}]},{"entity_type":"OutputStream","uid":"42f7800d-5822-486b-b8e9-bf490d679abc","connection_uuid":["6011c3bb-f126-44c4-915b-678b1dd13bb6","23f25a47-f20d-4abe-8382-666b4d2d4cf3"],"connection_label":["V-1","BatteryOutput001"],"stream_label":"V-1->BatteryOutput001","states":[{"uid":"9d38cbe7-86ca-4ccb-bb3c-160f0b694be5","collection":"varcollection_discrete","var_key":"material_stream_basis_selections","val":"mass_basis","bounds":null},{"uid":"5e6debf7-b85a-4718-9491-ff9f017f0867","collection":"varcollection_discrete","var_key":"flash_spec_selections","val":"temperature_and_pressure","bounds":null},{"uid":"8be13a46-d9d9-4e52-a639-496b9d27d4ab","collection":"varcollection_contvar","var_key":"mass flow rate","val":10.0,"bounds":[0.0,100000000.0]},{"uid":"7f45adce-b5ef-4205-97dc-8b59f06defaf","collection":"varcollection_contvar","var_key":"temperature","val":300.0,"bounds":[0.0,100000000.0]},{"uid":"b17f2ba1-7dc5-49ad-a1e1-a2522ec96580","collection":"varcollection_contvar","var_key":"pressure","val":101325.0,"bounds":[0.0,100000000.0]},{"uid":"03997516-31b5-46ec-b8e5-31cd2bfc31e1","collection":"varcollection_compoundmassmix","var_key":"7732-18-5","val":0.0,"bounds":null},{"uid":"7eb37302-c9e8-4cfb-a24e-d52461f1d73f","collection":"varcollection_compoundmassmix","var_key":"79-41-4","val":0.0,"bounds":null}]},{"entity_type":"OutputStream","uid":"4edea91d-5e22-46c8-85b4-94a3139755df","connection_uuid":["6011c3bb-f126-44c4-915b-678b1dd13bb6","13b59282-f7ad-447f-8dd9-1c5eb35ff4b4"],"connection_label":["V-1","BatteryOutput002"],"stream_label":"V-1->BatteryOutput002","states":[{"uid":"a07ee4cd-2808-4d19-a14f-0c45b0f0c30e","collection":"varcollection_discrete","var_key":"material_stream_basis_selections","val":"mass_basis","bounds":null},{"uid":"71e796d3-4419-4e45-bf12-ff90f9189883","collection":"varcollection_discrete","var_key":"flash_spec_selections","val":"temperature_and_pressure","bounds":null},{"uid":"f3ea7118-ad36-4ba0-b58c-7d277cc384eb","collection":"varcollection_contvar","var_key":"mass flow rate","val":10.0,"bounds":[0.0,100000000.0]},{"uid":"8e4db794-bb91-413d-9383-7b140f9afea9","collection":"varcollection_contvar","var_key":"temperature","val":300.0,"bounds":[0.0,100000000.0]},{"uid":"dd78b540-4103-47fa-a66f-39a4f84c3b75","collection":"varcollection_contvar","var_key":"pressure","val":101325.0,"bounds":[0.0,100000000.0]},{"uid":"ce44afa4-3cd4-404a-be2a-7fae34bf17b5","collection":"varcollection_compoundmassmix","var_key":"7732-18-5","val":0.0,"bounds":null},{"uid":"76de3aed-47c5-4697-b420-29c2165a92b7","collection":"varcollection_compoundmassmix","var_key":"79-41-4","val":0.0,"bounds":null}]},{"entity_type":"InputStream","uid":"0f8b69e0-6c3a-40f2-ab0c-4ca98ae955c1","connection_uuid":["d47678a8-c5f6-44d3-a8f3-636dc7c87c17","b07a77b5-**************-79c14fb39c89"],"connection_label":["BatteryInput001","Valve-1"],"stream_label":"BatteryInput001->Valve-1","states":[{"uid":"5acd2ff6-d330-4267-a1b6-4dc9f0ea5688","collection":"varcollection_discrete","var_key":"material_stream_basis_selections","val":"mass_basis","bounds":null},{"uid":"bf036d15-9cdd-4cf0-948f-4af489f94ef6","collection":"varcollection_discrete","var_key":"flash_spec_selections","val":"temperature_and_pressure","bounds":null},{"uid":"0bfdb52f-54c0-469a-8bd3-f5778af2e325","collection":"varcollection_contvar","var_key":"mass flow rate","val":1.0,"bounds":[0.416,1.1111]},{"uid":"ca543507-e5dc-4b99-a8ee-7bc3e13f6ace","collection":"varcollection_contvar","var_key":"temperature","val":432.0,"bounds":[431.537,438.094]},{"uid":"eca56f72-2c59-4463-873b-8f5b428b8564","collection":"varcollection_contvar","var_key":"pressure","val":101325.0,"bounds":[90000.0,110000.0]},{"uid":"b6ea06af-4102-4818-abaa-50074046d942","collection":"varcollection_compoundmassmix","var_key":"7732-18-5","val":0.0,"bounds":null},{"uid":"a240195f-cf55-481e-9dfd-97a962822e40","collection":"varcollection_compoundmassmix","var_key":"79-41-4","val":0.0,"bounds":null}]},{"entity_type":"InputStream","uid":"8fb1d631-f1c3-4bad-8d94-820fcfce6901","connection_uuid":["0d3259e7-9266-4698-b8f0-21e435f6a0c2","8bd186a2-f24a-4638-b153-4313dbdddff3"],"connection_label":["BatteryInput002","HE-2"],"stream_label":"BatteryInput002->HE-2","states":[{"uid":"306b5868-e7a4-4ae3-919f-4b504d084243","collection":"varcollection_discrete","var_key":"material_stream_basis_selections","val":"mass_basis","bounds":null},{"uid":"0b874cee-bef7-4d31-8715-6b2b1bcb51ca","collection":"varcollection_discrete","var_key":"flash_spec_selections","val":"temperature_and_pressure","bounds":null},{"uid":"ebd95845-9e81-48a9-9314-0237dcddd194","collection":"varcollection_contvar","var_key":"mass flow rate","val":20.0,"bounds":[19.36,58.09]},{"uid":"0fc3185d-0c1c-422f-aa56-68bccfe058a8","collection":"varcollection_contvar","var_key":"temperature","val":300.0,"bounds":[299.15,303.15]},{"uid":"02e6e462-cb9f-4c8c-a7fd-613c5fb9186e","collection":"varcollection_contvar","var_key":"pressure","val":101325.0,"bounds":[100000.0,200000.0]},{"uid":"7be4d6dc-a6f6-4023-9f83-039d7ecc139f","collection":"varcollection_compoundmassmix","var_key":"7732-18-5","val":0.0,"bounds":null},{"uid":"bae29ca7-e833-4400-9463-6bf1e02cc43c","collection":"varcollection_compoundmassmix","var_key":"79-41-4","val":0.0,"bounds":null}]}],"reactions":[],"kpis":[{"label":"Vapor Loss","uuid_expression":"{{8be13a46-d9d9-4e52-a639-496b9d27d4ab}}","label_expression":"{{V-1->BatteryOutput001 - Mass Flow Rate}}"}]}',
        "sample_dict": {
            "02e6e462-cb9f-4c8c-a7fd-613c5fb9186e": {
                "0": 171915.2939505875,
                "1": 171915.2939505875,
                "2": 171915.2939505875,
                "3": 103452.75085419416,
                "4": 171915.2939505875,
                "5": 171915.2939505875,
                "6": 171915.2939505875,
                "7": 171915.2939505875,
                "8": 171915.2939505875,
                "9": 171915.2939505875,
            },
            "0bfdb52f-54c0-469a-8bd3-f5778af2e325": {
                "0": 0.7881127902250737,
                "1": 0.7881127902250737,
                "2": 0.7881127902250737,
                "3": 0.7881127902250737,
                "4": 0.7881127902250737,
                "5": 0.7881127902250737,
                "6": 0.7881127902250737,
                "7": 0.7881127902250737,
                "8": 0.7881127902250737,
                "9": 0.7881127902250737,
            },
            "0fc3185d-0c1c-422f-aa56-68bccfe058a8": {
                "0": 299.54206884801386,
                "1": 299.54206884801386,
                "2": 302.23557921648023,
                "3": 299.54206884801386,
                "4": 299.54206884801386,
                "5": 299.54206884801386,
                "6": 299.54206884801386,
                "7": 299.54206884801386,
                "8": 299.54206884801386,
                "9": 299.54206884801386,
            },
            "3a2a89bb-aa3e-4215-adae-79d8ff08c0ae": {
                "0": 8.048315178602934,
                "1": 8.048315178602934,
                "2": 8.048315178602934,
                "3": 8.048315178602934,
                "4": 8.048315178602934,
                "5": 8.048315178602934,
                "6": 8.048315178602934,
                "7": 8.048315178602934,
                "8": 5.485382378101349,
                "9": 8.048315178602934,
            },
            "7be4d6dc-a6f6-4023-9f83-039d7ecc139f": {
                "0": 1,
                "1": 1,
                "2": 1,
                "3": 1,
                "4": 1,
                "5": 1,
                "6": 1,
                "7": 1,
                "8": 1,
                "9": 1,
            },
            "914f35b7-3bfb-49ef-931e-82edb3a687c0": {
                "0": 20.045973154716194,
                "1": 20.045973154716194,
                "2": 20.045973154716194,
                "3": 20.045973154716194,
                "4": 20.045973154716194,
                "5": 20.05881131608039,
                "6": 20.045973154716194,
                "7": 20.045973154716194,
                "8": 20.045973154716194,
                "9": 20.045973154716194,
            },
            "bad4d979-2cc9-48b7-8a5b-738ae5f5b9eb": {
                "0": 4.307940285652876,
                "1": 4.307940285652876,
                "2": 4.307940285652876,
                "3": 4.307940285652876,
                "4": 4.307940285652876,
                "5": 4.307940285652876,
                "6": 4.307940285652876,
                "7": 0.6704921834170818,
                "8": 4.307940285652876,
                "9": 4.307940285652876,
            },
            "bae29ca7-e833-4400-9463-6bf1e02cc43c": {
                "0": 1,
                "1": 1,
                "2": 1,
                "3": 1,
                "4": 1,
                "5": 1,
                "6": 1,
                "7": 1,
                "8": 1,
                "9": 1,
            },
            "ca543507-e5dc-4b99-a8ee-7bc3e13f6ace": {
                "0": 436.03813659804126,
                "1": 436.03813659804126,
                "2": 436.03813659804126,
                "3": 436.03813659804126,
                "4": 436.03813659804126,
                "5": 436.03813659804126,
                "6": 436.03813659804126,
                "7": 436.03813659804126,
                "8": 436.03813659804126,
                "9": 436.03813659804126,
            },
            "ebd95845-9e81-48a9-9314-0237dcddd194": {
                "0": 48.60924258032814,
                "1": 28.596299192579465,
                "2": 48.60924258032814,
                "3": 48.60924258032814,
                "4": 48.60924258032814,
                "5": 48.60924258032814,
                "6": 48.60924258032814,
                "7": 48.60924258032814,
                "8": 48.60924258032814,
                "9": 48.60924258032814,
            },
            "eca56f72-2c59-4463-873b-8f5b428b8564": {
                "0": 106681.77390471101,
                "1": 109556.79731443524,
                "2": 109556.79731443524,
                "3": 109556.79731443524,
                "4": 109556.79731443524,
                "5": 109556.79731443524,
                "6": 109556.79731443524,
                "7": 109556.79731443524,
                "8": 109556.79731443524,
                "9": 109556.79731443524,
            },
            "f0c6f0c2-e5e9-474f-96c8-75882a47eee3": {
                "0": 302.3516065120697,
                "1": 302.3516065120697,
                "2": 302.3516065120697,
                "3": 302.3516065120697,
                "4": 305.3666891172528,
                "5": 302.3516065120697,
                "6": 302.3516065120697,
                "7": 302.3516065120697,
                "8": 302.3516065120697,
                "9": 302.3516065120697,
            },
            "f2eb55de-8935-4e44-911c-b53f0ae44a83": {
                "0": 5.728664742782712,
                "1": 5.728664742782712,
                "2": 5.728664742782712,
                "3": 5.728664742782712,
                "4": 5.728664742782712,
                "5": 5.728664742782712,
                "6": 3.684317832812667,
                "7": 5.728664742782712,
                "8": 5.728664742782712,
                "9": 5.728664742782712,
            },
        },
        "simulation_id": "SWANCOR HE-2",
    }
)
atlas = AtlasJSONSerializer.deserialize(
    PydanticLogs.model_validate_json(PREFECT_LOG).atlas_json
)


def z_test_experiment():
    # Build WorkflowManager object using the Experiment Config

    test_wf_manager = experiment_config.bootstrap_wf_manager(atlas=atlas)

    # Get SummaryExperiment object.
    smr_exp_results = asyncio.run(test_wf_manager.run_experiment())

    # Generate Experiment Results object
    exp_results = ExperimentResults.hydrate_schema(
        ent_settings=experiment_config.entity_settings,
        kpi_spec=experiment_config.sim_kpi,
        results=smr_exp_results,
    )

    # Dump exp_results to JSON
    exp_results_json = exp_results.model_dump_json()
    with open("test_experiment.json", "w") as jdf:
        jdf.write(exp_results_json)
    assert isinstance(exp_results, ExperimentResults)
    logging.info(f"Experiment Results object is hydrated {exp_results_json}")


# if __name__ == "__main__":
#     test_experiment()
