import pytest
import logging
from pprint import pformat
from backend.endpoint.v1.schema import *
import backend.core._atlas.aggregates as at

from backend.application.usecase_templates import (
    industrial_natural_gas_boiler_with_preheating_trains,
)


def log_schema(schema, msg="Schema"):
    """Consistent schema logging"""
    logging.info(f"\n{msg}:\n{pformat(schema.model_dump(), indent=2)}")


def mock_atlas() -> at.AtlasRoot:
    return at.AtlasRoot("test_model", "test_plant")


@pytest.fixture
def basic_heater(mock_atlas):
    heater = mock_atlas.add_equipment(at.Heater, "HT-001")
    heater.set_value(at.ContVarSpecEnum.Efficiency, 0.666)
    return heater, mock_atlas


class TestPlantConfigEquipment:
    def _test_entity(self, equipment: Type[at.ENTBaseEquipment]):
        """Test basic heater hydration"""
        atlas: at.AtlasRoot = mock_atlas()
        entity = atlas.add_equipment(equipment, "LABEL")
        # entity.set_value(at.ContVarSpecEnum.Efficiency, 0.666)

        # Hydrate
        schema = PlantConfigEquipment.hydrate_schema(entity, atlas)
        log_schema(schema, "Schema")

        # Verify core fields
        assert schema.equipmentId == "LABEL"
        assert schema.equipmentType == entity.entity_type

        new_entity = schema.bootstrap_atlas(atlas)
        for var in entity.get_variables():
            assert entity.get_value(var.variable_enum) == new_entity.get_value(
                var.variable_enum
            )

    def test_equipment_entity(self):
        entities = [at.Heater, at.HeatExchanger]

        for e in entities:
            self._test_entity(e)


class TestPlantConfigStreams:
    def test_input_stream(self):
        """Test basic heater hydration"""
        atlas: at.AtlasRoot = mock_atlas()
        eqp_0 = atlas.add_equipment(at.BatteryIn, "BatteryIn0")
        eqp_1 = atlas.add_equipment(at.Heater, "H1")
        entity = atlas.add_stream(eqp_0, eqp_1, stream_type=at.InputStream)

        # Hydrate
        schema = PlantConfigConnection.hydrate_schema(entity, atlas)
        log_schema(schema, f"Schema from {entity.entity_type}")

        # Verify core fields
        assert schema.upstreamEquipment == eqp_0.label
        assert schema.downstreamEquipment == eqp_1.label
        assert schema.type == entity.entity_type

        new_entity = schema.bootstrap_atlas(atlas)
        for var in entity.get_variables():
            assert entity.get_value(var.variable_enum) == new_entity.get_value(
                var.variable_enum
            )

    def test_output_stream(self):
        """Test basic heater hydration"""
        atlas: at.AtlasRoot = mock_atlas()
        eqp_0 = atlas.add_equipment(at.Heater, "E0")
        eqp_1 = atlas.add_equipment(at.BatteryOut, "E1")
        entity = atlas.add_stream(eqp_0, eqp_1, stream_type=at.OutputStream)

        # Hydrate
        schema = PlantConfigConnection.hydrate_schema(entity, atlas)
        log_schema(schema, f"Schema from {entity.entity_type}")

        # Verify core fields
        assert schema.upstreamEquipment == eqp_0.label
        assert schema.downstreamEquipment == eqp_1.label
        assert schema.type == entity.entity_type

        new_entity = schema.bootstrap_atlas(atlas)
        for var in entity.get_variables():
            assert entity.get_value(var.variable_enum) == new_entity.get_value(
                var.variable_enum
            )

    def test_material_stream(self):
        """Test basic heater hydration"""
        atlas: at.AtlasRoot = mock_atlas()
        eqp_0 = atlas.add_equipment(at.Heater, "H0")
        eqp_1 = atlas.add_equipment(at.Heater, "H1")
        entity = atlas.add_stream(eqp_0, eqp_1, stream_type=at.MaterialStream)

        # Hydrate
        schema = PlantConfigConnection.hydrate_schema(entity, atlas)
        log_schema(schema, f"Schema from {entity.entity_type}")

        # Verify core fields
        assert schema.upstreamEquipment == eqp_0.label
        assert schema.downstreamEquipment == eqp_1.label
        assert schema.type == entity.entity_type

        new_entity = schema.bootstrap_atlas(atlas)
        for var in entity.get_variables():
            assert entity.get_value(var.variable_enum) == new_entity.get_value(
                var.variable_enum
            )


class TestPlantConfigSample:

    def test_example_atlas(self):
        from backend.application.usecase_templates import (
            industrial_natural_gas_boiler_with_preheating_trains,
        )

        def _test_example(atlas: at.AtlasRoot):
            n_streams = len(atlas.streams)

            # Hydrate
            schema = PlantConfiguration.hydrate_schema(atlas, False)

            log_schema(schema, f"Schema from {atlas.label}")
            atlas2 = PlantConfiguration.bootstrap_atlas(
                schema, model_name=f"{atlas.label}"
            )
            schema2 = PlantConfiguration.hydrate_schema(atlas2, is_template=False)
            log_schema(schema2, f"Schema from {atlas2.label}")
            logging.info(f"{[pformat(conn, 2) for conn in schema2.connections]}")

            atlas3 = PlantConfiguration.bootstrap_atlas(schema, atlas2)

            # ASSERT LENGTHS
            assert n_streams == len(
                schema2.connections
            ), f"reconstructed stream lengths do not match"

            # ASSERT TOPOLOGY
            def normalize_edgelist(edges):
                return sorted(str(edge) for edge in edges)

            atlas_edges = normalize_edgelist(atlas.edgelist)
            atlas2_edges = normalize_edgelist(atlas2.edgelist)
            atlas3_edges = normalize_edgelist(atlas3.edgelist)

            assert (
                atlas_edges == atlas2_edges
            ), f"Topology mismatch:\nOld: {atlas_edges}\nNew: {atlas2_edges}"

            assert (
                atlas3_edges == atlas2_edges
            ), f"Topology mismatch:\nOld: {atlas_edges}\nNew: {atlas2_edges}"

            logging.info(f"{[pformat(conn, 2) for conn in schema2.connections]}")
            logging.info(f"\n{atlas.label}_1 Edgelist:")
            for edge in normalize_edgelist(atlas.edgelist):
                logging.info(f"  {edge}")

            logging.info(f"\n{atlas2.label}_2 Edgelist:")
            for edge in normalize_edgelist(atlas2.edgelist):
                logging.info(f"  {edge}")

            logging.info(f"\n{atlas3.label}_3 Edgelist:")
            for edge in normalize_edgelist(atlas2.edgelist):
                logging.info(f"  {edge}")

        example = [industrial_natural_gas_boiler_with_preheating_trains()]

        for eg in example:
            _test_example(eg)


def test_industrial_boiler_kpis():

    atlas = industrial_natural_gas_boiler_with_preheating_trains()
    results = []

    for var in atlas.variables_collection.items:
        # Defensive
        if at.VariableUIDCollection.is_kpi(var) == False:
            continue

        kpi_label = at.KPI.get_kpi_label(var)
        results.append(KPIVariableItem.hydrate_schema(var, kpi_label))

    logging.info(f"results: \n {pformat([var_item for var_item in results], 2)}")


def test_get_kpi_static():
    kpi_delim = " - "
    atlas = industrial_natural_gas_boiler_with_preheating_trains()

    entity = atlas.get_equipment(by_label="HEX-100")
    var = entity.get_variable(at.ContVarSpecEnum.HeatLoss)

    static_var = f"{entity.label}{kpi_delim}{atlas.variables_collection.get_ui_label(var)}"  # type: ignore

    # Add Static KPI vars, dynamic KPI vars, compound KPIs
    kpi_static = at.KPI(
        label="static var kpi",
        expression=f"{{{{ {static_var} }}}}",
        collection_reference=atlas.variables_collection,
    )

    atlas.kpi_collection.add_item(kpi_static)

    kpi_schema = KPIItem.hydrate_schema(kpi_static, atlas)
    pprint(kpi_schema)

    ## Assert UUID is same as Variable Type
    kpi_var_uids = kpi_static.variable_uids
    assert var.uid in kpi_var_uids


def test_get_kpi_streamcompoundmassratio():
    kpi_delim = " - "
    atlas = industrial_natural_gas_boiler_with_preheating_trains()

    entity_fuel = atlas.get_stream(by_equipments=("Fuel BL", "M-100"))
    fuel_compound_key = at.CompoundRef.get_vocompound_from_enum(
        at.CASCompoundEnum.Methane
    )
    var = entity_fuel.get_variable(fuel_compound_key)

    compound_var = f"{entity_fuel.label}{kpi_delim}{atlas.variables_collection.get_ui_label(var)}"  # type: ignore

    # Add Static KPI vars, dynamic KPI vars, compound KPIs
    kpi_static = at.KPI(
        label="static var kpi",
        expression=f"{{{{ {compound_var} }}}}",
        collection_reference=atlas.variables_collection,
    )

    atlas.kpi_collection.add_item(kpi_static)

    kpi_schema = KPIItem.hydrate_schema(kpi_static, atlas)
    pprint(kpi_schema)

    ## Assert UUID is same as Variable Type
    kpi_var_uids = kpi_static.variable_uids
    assert var.uid in kpi_var_uids


def test_get_kpi_mixedvars():
    kpi_delim = " - "
    atlas = industrial_natural_gas_boiler_with_preheating_trains()

    # COMPOUNDVAR
    entity_fuel = atlas.get_stream(by_equipments=("Fuel BL", "M-100"))
    fuel_compound_key = at.CompoundRef.get_vocompound_from_enum(
        at.CASCompoundEnum.Methane
    )
    var_compound = entity_fuel.get_variable(fuel_compound_key)
    str_compound_var = f"{entity_fuel.label}{kpi_delim}{atlas.variables_collection.get_ui_label(var_compound)}"  # type:ignore

    # CONTVAR
    entity_hex = atlas.get_equipment(by_label="HEX-100")
    var_static = entity_hex.get_variable(at.ContVarSpecEnum.HeatLoss)
    str_static_var = f"{entity_hex.label}{kpi_delim}{atlas.variables_collection.get_ui_label(var_static)}"  # type:ignore

    # Add Static KPI vars, dynamic KPI vars, compound KPIs
    kpi_mixed = at.KPI(
        label="mixed var kpi",
        expression=f"{{{{ {str_static_var} }}}} - {{{{{str_compound_var}}}}}",
        collection_reference=atlas.variables_collection,
    )

    atlas.kpi_collection.add_item(kpi_mixed)

    kpi_schema = KPIItem.hydrate_schema(kpi_mixed, atlas)
    pprint(kpi_schema)

    ## Assert UUID is same as Variable Type
    kpi_var_uids = kpi_mixed.variable_uids
    assert var_static.uid in kpi_var_uids
    assert var_compound.uid in kpi_var_uids


def test_post_kpis():
    kpi_delim = " - "
    atlas = industrial_natural_gas_boiler_with_preheating_trains()

    # COMPOUNDVAR
    entity_fuel = atlas.get_stream(by_equipments=("Fuel BL", "M-100"))
    fuel_compound_key = at.CompoundRef.get_vocompound_from_enum(
        at.CASCompoundEnum.Methane
    )
    var_compound = entity_fuel.get_variable(fuel_compound_key)
    str_compound_var = f"{entity_fuel.label}{kpi_delim}{atlas.variables_collection.get_ui_label(var_compound)}"  # type:ignore

    # CONTVAR
    entity_hex = atlas.get_equipment(by_label="HEX-100")
    var_static = entity_hex.get_variable(at.ContVarSpecEnum.HeatLoss)
    str_static_var = f"{entity_hex.label}{kpi_delim}{atlas.variables_collection.get_ui_label(var_static)}"  # type:ignore

    # Add Static KPI vars, dynamic KPI vars, compound KPIs
    kpi_mixed = at.KPI(
        label="mixed var kpi",
        expression=f"{{{{ {str_static_var} }}}} - {{{{{str_compound_var}}}}}",
        collection_reference=atlas.variables_collection,
    )

    # HYDRATE AND BOOTSTRAP
    kpi_schema_obj = KPIItem.hydrate_schema(kpi_mixed, atlas)
    kpi = kpi_schema_obj.bootstrap_obj(atlas.variables_collection)
    atlas.kpi_collection.sync_collection([kpi])

    # ASSERT THAT KPI IS IN COLLECTION
    assert kpi.label == kpi_mixed.label  # ensure they are the same
    assert len(atlas.kpi_collection.items) == 1
    assert kpi in atlas.kpi_collection.items


def test_get_sensor_mappings():
    """Test hydrate schema in SensorMappingConfig for GET sensor mappings"""
    atlas = industrial_natural_gas_boiler_with_preheating_trains()
    entity = atlas.get_equipment(by_label="HEX-100")
    var = entity.get_variable(at.ContVarSpecEnum.HeatLoss)
    if var is None:
        raise ValueError("Failed to get contvar for heat exchanger.")

    # Add sensor to collection
    sensor = VOSensor(variable_uid=var.uid, label="S-001")
    atlas.sensor_collection.sync_collection([sensor])

    timeset_col = "Case No"
    timestep_col = "Timestep"
    atlas.sensor_timeset_col = timeset_col
    atlas.sensor_timestep_col = timestep_col

    sensor_mapping_schema = SensorMappingConfig.hydrate_schema(atlas)
    pprint(sensor_mapping_schema)

    assert sensor.label in sensor_mapping_schema.csvCol2varUUID
    assert sensor.variable_uid == sensor_mapping_schema.csvCol2varUUID[sensor.label]
    assert timeset_col == sensor_mapping_schema.timeset_col
    assert timestep_col == sensor_mapping_schema.timestep_col


def test_update_sensor_mappings():
    """Test hydrate and bootstrap for SensorItem in POST sensor mappings"""
    atlas = industrial_natural_gas_boiler_with_preheating_trains()
    entity = atlas.get_equipment(by_label="HEX-100")
    var = entity.get_variable(at.ContVarSpecEnum.HeatLoss)
    if var is None:
        raise ValueError("Failed to get contvar for heat exchanger.")

    # Create sensor obj
    sensor = VOSensor(variable_uid=var.uid, label="S-001")

    # Hydrate, bootstrap and add to collection
    sensor_schema = SensorItem.hydrate_schema(sensor)
    sensor_obj = SensorItem.bootstrap_obj(
        sensor_schema.label, sensor_schema.variable_uid
    )
    atlas.sensor_collection.sync_collection([sensor_obj])

    assert sensor_obj.label == sensor_obj.label
    assert len(atlas.sensor_collection.items) == 1
    assert sensor_obj in atlas.sensor_collection.items
