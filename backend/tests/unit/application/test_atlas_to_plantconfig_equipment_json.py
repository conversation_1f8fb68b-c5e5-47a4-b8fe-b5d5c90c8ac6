import logging
import pytest
import pprint
import backend.core._atlas.aggregates as at
from backend.core._atlas.entities import HeatExchanger
from backend.core._atlas.aggregates import AtlasRoot
from backend.endpoint.v1.schema import PlantConfigEquipment, PresetEquipmentType


def test_pydantic_plant_config_equipment_hydration():
    """Test PC_Equipment schema hydration with HeatExchanger"""
    # Setup
    atlas = AtlasRoot("test_model", "test_plant")
    heat_exchanger = atlas.add_equipment(HeatExchanger, "HX-001")

    # Get UI labels from atlas for discrete selections
    variables_collection = atlas.variables_collection
    prereq_free_selections = [
        variables_collection.get_ui_label(heat_exchanger.get_variable(disc_var))  # type: ignore
        for disc_var in [
            at.DiscreteSetSpecEnum.CalculationType,
            at.DiscreteSetSpecEnum.FlowDirection,
        ]
    ]

    # Get UI labels from atlas for parameters
    expected_params = {
        variables_collection.get_ui_label(heat_exchanger.get_variable(param))  # type: ignore
        for param in [
            at.ContVarSpecEnum.ColdFluidPressureDrop,
            at.ContVarSpecEnum.HotFluidPressureDrop,
            at.ContVarSpecEnum.ColdFluidOutletTemperature,
            at.ContVarSpecEnum.HotFluidOutletTemperature,
            at.ContVarSpecEnum.GlobalHeatTransferCoefficient,
            at.ContVarSpecEnum.HeatExchangeArea,
            at.ContVarSpecEnum.HeatExchange,
            at.ContVarSpecEnum.MinimumTemperatureDifference,
        ]
    }

    # Execute
    pc_equipment = PlantConfigEquipment.hydrate_schema(heat_exchanger, atlas)

    # Verify core attributes
    assert pc_equipment.equipmentId == "HX-001"
    assert pc_equipment.equipmentType == "HeatExchanger"

    # Verify selections with non-preq in front
    selection_titles = [s.title for s in pc_equipment.selections][:2]
    assert set(selection_titles) == set(prereq_free_selections)

    # Verify parameters
    param_titles = {p.title for p in pc_equipment.setpoints + pc_equipment.conditions}
    assert expected_params.issubset(param_titles)
    logging.info(
        f"PC_Equipment Schema: \n{pprint.pformat(pc_equipment.model_dump(), indent=4)}"
    )


def test_pydantic_plant_config_equipment_bootstrap():
    """Test PC_Equipment schema bootstrap back to Atlas"""
    # Setup initial state
    atlas = AtlasRoot("test_model", "test_plant")
    heat_exchanger = atlas.add_equipment(HeatExchanger, "HX-001")

    # Get UI label from atlas
    # flow_var = heat_exchanger.get_variable(at.DiscreteSetLabelEnum.FlowDirection)
    # assert flow_var is not None
    # flow_direction_label = atlas.variables_collection.get_ui_label(flow_var)

    pc_equipment = PlantConfigEquipment.hydrate_schema(heat_exchanger, atlas)

    # Bootstrap back to atlas
    updated_equipment = pc_equipment.bootstrap_atlas(atlas)
    assert updated_equipment == heat_exchanger
    logging.info(
        f"PC_Equipment Schema: \n{pprint.pformat(pc_equipment.model_dump(), indent=4)}"
    )


def test_pydantic_equipment_type_hydration():
    """Test PC_EquipmentType schema hydration with HeatExchanger"""
    # Setup
    atlas = AtlasRoot("test_model", "test_plant")
    variables_collection = atlas.variables_collection
    heat_exchanger = atlas.add_equipment(HeatExchanger, "HEX-01")

    # Get UI labels for discrete selections
    discrete_vars = heat_exchanger.get_variables(filter=[at.VarCollectionDiscreteSet])
    discrete_var_labels = [
        at.VariableUIDCollection.get_ui_label(var) for var in discrete_vars
    ]

    cont_vars = heat_exchanger.get_variables(filter=[at.VarCollectionContinuous])
    cont_var_labels = [at.VariableUIDCollection.get_ui_label(var) for var in cont_vars]

    # Execute
    equipment_type_schema = PresetEquipmentType.hydrate_schema(HeatExchanger, atlas)

    # Verify core attributes
    assert equipment_type_schema.type == "HeatExchanger"

    # Verify selections
    assert len(equipment_type_schema.selections) == len(discrete_var_labels)
    for selection in equipment_type_schema.selections:
        assert selection.title in discrete_var_labels

    # Verify setpoints and conditions
    schema_cont_vars = (
        equipment_type_schema.setpoints + equipment_type_schema.conditions
    )
    assert len(schema_cont_vars) == len(cont_var_labels)
    for schema_var in schema_cont_vars:
        assert schema_var.title in cont_var_labels

    # Log output for debugging
    logging.info(
        f"PC_EquipmentType Schema: \n{pprint.pformat(equipment_type_schema.model_dump(), indent=4)}"
    )
