from venv import logger
import pytest
import logging
from pathlib import Path
from backend.infrastructure.adaptors.logger_adaptors import FileLogger, SQLiteLogger
import sqlite3


@pytest.fixture
def temp_log_dir(tmp_path) -> Path: 
    log_dir = tmp_path / "logs"
    log_dir.mkdir()
    return log_dir

class TestSQLiteLogger: 
    def test_sqlite_logger_initialization(self, temp_log_dir): 
        db_path = temp_log_dir / "test.db"
        logger = SQLiteLogger(str(db_path))
        logger.setup()

        assert db_path.exists()

        with sqlite3.connect(db_path) as conn:
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            assert ("logs",) in tables

    def test_sqlite_logger_writes_logs(self, temp_log_dir):
        """Test SQLiteLogger writes log messages to database"""
        db_path = temp_log_dir / "test.db"
        logger = SQLiteLogger(str(db_path))
        logger.setup()
        
        test_message = "Test log message"
        logging.info(test_message)
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.execute("SELECT message FROM logs")
            messages = cursor.fetchone()
            assert test_message in messages[0]

    def test_sqlite_logger_stores_function_name(self, temp_log_dir):
        """Test function name is correctly stored in database"""
        db_path = temp_log_dir / "test.db"
        logger = SQLiteLogger(str(db_path))
        logger.setup()
        
        def test_function():
            logging.info("Test message")
            
        test_function()
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.execute("SELECT function_name FROM logs")
            function_names = cursor.fetchall()
            assert ("test_function",) in function_names
    
    @pytest.mark.parametrize("log_level", [
        logging.DEBUG,
        logging.INFO,
        logging.WARNING,
        logging.ERROR,
        logging.CRITICAL
    ])
    def test_sqlite_logger_levels(self, temp_log_dir, log_level):
        """Test different log levels are correctly stored"""
        db_path = temp_log_dir / "test.db"
        logger = SQLiteLogger(str(db_path))
        logger.setup()

        level_name = logging.getLevelName(log_level)
        getattr(logging, level_name.lower())("Test message")
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.execute("SELECT level FROM logs")
            level = cursor.fetchone()
            assert logging.getLevelName(log_level) in level[0]