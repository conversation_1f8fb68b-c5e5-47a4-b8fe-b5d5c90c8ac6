import pytest
import logging
from pathlib import Path
from backend.infrastructure.adaptors.logger_adaptors import <PERSON><PERSON>og<PERSON>, SQLiteLogger

@pytest.fixture
def temp_log_dir(tmp_path) -> Path:
    """Fixture to create temporary log directory"""
    log_dir = tmp_path / "logs"
    log_dir.mkdir()
    return log_dir

class TestFileLogger:
    def test_file_logger_initialization(self, temp_log_dir):
        """Test FileLogger creates directory and initializes correctly"""
        log_path = temp_log_dir / "test.log"
        logger = FileLogger(str(log_path))
        logger.setup()
        
        assert log_path.parent.exists()
        assert logger._level == logging.DEBUG
        
    def test_file_logger_writes_logs(self, temp_log_dir):
        """Test FileLogger writes log messages correctly"""
        log_path = temp_log_dir / "test.log"
        logger = FileLogger(str(log_path))
        logger.setup()
        
        test_message = "Test log message"
        logging.info(test_message)
        
        assert log_path.exists()
        log_content = log_path.read_text()
        assert test_message in log_content
        
    def test_file_logger_formats_correctly(self, temp_log_dir):
        """Test log messages are formatted according to specification"""
        log_path = temp_log_dir / "test.log"
        logger = FileLogger(str(log_path))
        logger.setup()
        
        logging.info("Test message")
        
        log_content = log_path.read_text()
        # Check ISO format date
        assert " - root - INFO - " in log_content