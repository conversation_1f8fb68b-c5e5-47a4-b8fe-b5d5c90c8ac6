"""
End-to-End Integration Tests for Training Runner Abstraction

This module provides comprehensive integration tests to validate that the
training runner abstraction maintains identical behavior to direct trainer calls.
These tests use real data and actual training to ensure the abstraction works
correctly in practice.

Test Coverage:
- Complete training pipeline with LocalRunner vs direct trainer calls
- Error handling and edge cases
- Performance and behavior validation
- Real data processing and model training
"""

import pytest
import uuid
import numpy as np
import pandas as pd
from typing import Any, Tuple
import time

import backend.core._surrogate as su
from backend.core.interfaces.surrogate_interfaces import ISurrogate, SurrogateRegistryPort
from backend.infrastructure.runners.surrogate_trainers.runner_local import SurrogateTrainingLocalRunner
from backend.infrastructure.adaptors.surrogate_adaptors import LocalSurrogate
from backend.core._surrogate.factories import RNNSurrogateTrainerFactory


# Helper functions for creating realistic test data

def _create_realistic_test_data() -> Tuple[su.VODataset, su.VODataset]:
    """Create realistic test datasets for training and testing."""
    np.random.seed(42)  # For reproducibility

    # Create time series data (sequence pattern) - larger dataset to meet minimum requirements
    n_timesets = 100  # Increased from 20 to meet minimum partition requirements
    n_timesteps = 10
    n_features = 3
    n_outputs = 1
    
    # Generate synthetic time series data
    arr_x_train = np.random.randn(n_timesets, n_timesteps, n_features)
    arr_y_train = np.random.randn(n_timesets, n_timesteps, n_outputs)
    
    # Create smaller test set
    arr_x_test = np.random.randn(20, n_timesteps, n_features)  # Increased from 5
    arr_y_test = np.random.randn(20, n_timesteps, n_outputs)
    
    transformer_uid = uuid.uuid4()
    
    train_dataset = su.VODataset(
        arr_x=arr_x_train,
        arr_y=arr_y_train,
        transformer_uid=transformer_uid,
        colnames_x=[f'feature_{i}' for i in range(n_features)],
        colnames_y=[f'target_{i}' for i in range(n_outputs)],
        pattern="sequence"
    )
    
    test_dataset = su.VODataset(
        arr_x=arr_x_test,
        arr_y=arr_y_test,
        transformer_uid=transformer_uid,
        colnames_x=[f'feature_{i}' for i in range(n_features)],
        colnames_y=[f'target_{i}' for i in range(n_outputs)],
        pattern="sequence"
    )
    
    return train_dataset, test_dataset


def _create_test_transformer() -> su.transformers.SurrogateDataTransformer:
    """Create a test data transformer."""
    # Create minimal test data for transformer
    df_x = pd.DataFrame({
        'feature_0': [1, 2, 3, 4, 5],
        'feature_1': [0.1, 0.2, 0.3, 0.4, 0.5],
        'feature_2': [10, 20, 30, 40, 50]
    })
    df_y = pd.DataFrame({
        'target_0': [100, 200, 300, 400, 500]
    })
    
    return su.transformers.SurrogateDataTransformer(
        uid=uuid.uuid4(),
        df_x=df_x,
        df_y=df_y,
        timeset_col=None,
        timestep_col=None,
        col2uid_dict={}
    )

# class MockSurrogateRegistry:
#     """Mock registry for testing that stores data in memory."""
    
#     def __init__(self):
#         self.jobs = {}
#         self.models = {}
#         self.transformers = {}
    
#     def save_job(self, metadata: su.VOMetadata_General, job: su.ENTTrainingJob) -> None:
#         key = f"{metadata.user_reference}/{metadata.atlas_reference}"
#         self.jobs[key] = job
    
#     def get_job(self, user_ref: str, atlas_ref: str) -> su.ENTTrainingJob:
#         key = f"{user_ref}/{atlas_ref}"
#         return self.jobs[key]
    
#     def save_model(self, metadata: su.VOMetadata_General, model: su.models.BaseSurrogateModel) -> None:
#         key = f"{metadata.user_reference}/{metadata.atlas_reference}"
#         self.models[key] = model
    
#     def get_model(self, metadata: su.VOMetadata_General) -> su.models.BaseSurrogateModel:
#         key = f"{metadata.user_reference}/{metadata.atlas_reference}"
#         return self.models[key]
    
#     def save_datatransformer(self, metadata: su.VOMetadata_General, transformer: su.transformers.SurrogateDataTransformer) -> None:
#         key = f"{metadata.user_reference}/{metadata.atlas_reference}"
#         self.transformers[key] = transformer
    
#     def get_datatransformer(self, user_ref: str, atlas_ref: str) -> su.transformers.SurrogateDataTransformer:
#         key = f"{user_ref}/{atlas_ref}"
#         return self.transformers[key]


class TestEndToEndIntegration:
    """End-to-end integration tests for the training runner abstraction."""

    def test_local_surrogate_complete_workflow(self):
        """Test complete training workflow using LocalSurrogate with LocalRunner."""
        # Create LocalSurrogate instance (uses LocalRunner internally)
        local_surrogate = LocalSurrogate()
        
        # Verify it uses LocalRunner
        assert isinstance(local_surrogate.training_runner, SurrogateTrainingLocalRunner)
        
        # Create test data
        train_dataset, test_dataset = _create_realistic_test_data()
        datatransformer = _create_test_transformer()
        
        # Create configurations using factory
        config = RNNSurrogateTrainerFactory.create_complete_config("fast")
        metadata = config["metadata"]
        training_config = config["training_config"]
        model_config = config["model_config"]
        hpo_config = config["hpo_config"]
        
        # Create training job
        job = su.ENTTrainingJob(
            metadata=metadata,
            training_configuration=training_config,
            model_configuration=model_config,
            hpo_configuration=hpo_config
        )
        
        # Execute training through the abstraction
        start_time = time.time()
        try:
            model, result_job = local_surrogate.execute_training_job(
                job=job,
                train_dataset=train_dataset,
                test_dataset=test_dataset,
                datatransformer=datatransformer
            )
            execution_time = time.time() - start_time
            
            # Validate results
            assert model is not None, "Model should not be None"
            assert result_job.status == su.EnumTrainingStatus.COMPLETE.value, f"Job should be complete, got {result_job.status}"
            assert result_job.runtime_seconds > 0, "Runtime should be recorded"
            assert execution_time < 60, "Fast configuration should complete quickly"  # Should be very fast
            
            print(f"Complete workflow test passed in {execution_time:.2f} seconds")
            
        except Exception as e:
            print(f"Complete workflow test failed: {e}")
            raise

    def test_runner_abstraction_behavior_consistency(self):
        """Test that LocalRunner produces consistent behavior."""
        # Create multiple LocalRunner instances
        runner1 = SurrogateTrainingLocalRunner()
        runner2 = SurrogateTrainingLocalRunner()
        
        # Setup infrastructure
        runner1.setup_infrastructure()
        runner2.setup_infrastructure()
        
        # Create identical test data and configurations
        train_dataset, test_dataset = _create_realistic_test_data()
        config = RNNSurrogateTrainerFactory.create_complete_config("fast")
        
        # Create identical jobs
        job1 = su.ENTTrainingJob(
            metadata=config["metadata"],
            training_configuration=config["training_config"],
            model_configuration=config["model_config"],
            hpo_configuration=config["hpo_config"]
        )
        job2 = su.ENTTrainingJob(
            metadata=config["metadata"],
            training_configuration=config["training_config"],
            model_configuration=config["model_config"],
            hpo_configuration=config["hpo_config"]
        )
        
        # Execute training with both runners
        try:
            model1, result_job1 = runner1.submit_training_job(
                metadata=config["metadata"],
                training_data=train_dataset,
                training_config=config["training_config"],
                model_config=config["model_config"],
                job=job1,
                test_data=test_dataset,
                hpo_config=config["hpo_config"]
            )
            
            model2, result_job2 = runner2.submit_training_job(
                metadata=config["metadata"],
                training_data=train_dataset,
                training_config=config["training_config"],
                model_config=config["model_config"],
                job=job2,
                test_data=test_dataset,
                hpo_config=config["hpo_config"]
            )
            
            # Validate consistency
            assert result_job1.status == result_job2.status, "Job statuses should be consistent"
            assert model1 is not None and model2 is not None, "Both models should be created"
            
            print("Runner behavior consistency test passed")
            
        except Exception as e:
            print(f"Runner behavior consistency test failed: {e}")
            raise
        finally:
            runner1.teardown_infrastructure()
            runner2.teardown_infrastructure()

    def test_error_handling_through_abstraction(self):
        """Test error handling through the runner abstraction."""
        runner = SurrogateTrainingLocalRunner()
        runner.setup_infrastructure()
        
        # Create invalid configuration that should cause training to fail
        config = RNNSurrogateTrainerFactory.create_complete_config("fast")
        
        # Create dataset that passes validation but is too small for training
        invalid_dataset = su.VODataset(
            arr_x=np.random.randn(5, 2),  # Very small dataset - same size for both
            arr_y=np.random.randn(5, 1),  # Matching size but too small for splitting
            transformer_uid=uuid.uuid4(),
            colnames_x=['feature1', 'feature2'],
            colnames_y=['target'],
            pattern="tabular"
        )
        
        job = su.ENTTrainingJob(
            metadata=config["metadata"],
            training_configuration=config["training_config"],
            model_configuration=config["model_config"]
        )
        
        # Should handle the error gracefully
        try:
            model, result_job = runner.submit_training_job(
                metadata=config["metadata"],
                training_data=invalid_dataset,
                training_config=config["training_config"],
                model_config=config["model_config"],
                job=job
            )
            # If we get here, the training somehow succeeded despite invalid data
            print("Error handling test: Training unexpectedly succeeded")
        except Exception as e:
            # This is expected - the runner should propagate training errors
            assert "training" in str(e).lower() or "error" in str(e).lower()
            print("Error handling test passed - errors properly propagated")
        finally:
            runner.teardown_infrastructure()




# Simple runner for direct execution
if __name__ == "__main__":
    print("Running end-to-end integration tests...")
    
    # End-to-end integration tests
    integration_tests = TestEndToEndIntegration()
    
    try:
        integration_tests.test_local_surrogate_complete_workflow()
        integration_tests.test_runner_abstraction_behavior_consistency()
        integration_tests.test_error_handling_through_abstraction()
        
        print("All end-to-end integration tests completed successfully!")
        
    except Exception as e:
        print(f"Integration tests failed: {e}")
        raise
