"""
Standalone Tests for AzureMLClientFactory

This module provides comprehensive tests for the AzureMLClientFactory class
without requiring Azure SDK dependencies. It uses mocking to simulate Azure
components and focuses on testing the factory pattern implementation.

Test Coverage:
- Factory initialization with explicit parameters
- Factory initialization with environment variables  
- Validation logic for required parameters
- Error handling for missing configuration
- MLClient creation with proper credential handling
- Authentication failure scenarios
"""

import os
import sys
from unittest.mock import patch, MagicMock

# Mock Azure modules before any imports
sys.modules['azure'] = MagicMock()
sys.modules['azure.ai'] = MagicMock() 
sys.modules['azure.ai.ml'] = MagicMock()
sys.modules['azure.identity'] = MagicMock()

# Mock the specific classes we need
mock_ml_client = MagicMock()
mock_default_credential = MagicMock()

sys.modules['azure.ai.ml'].MLClient = mock_ml_client
sys.modules['azure.identity'].DefaultAzureCredential = mock_default_credential


class MockSurrogateConfigError(Exception):
    """Mock version of SurrogateConfigError for testing"""
    pass


class MockAzureMLClientFactory:
    """
    Mock implementation of AzureMLClientFactory for testing.
    
    This replicates the exact logic from the real implementation
    but uses mocked Azure dependencies.
    """
    
    def __init__(self, subscription_id=None, resource_group=None, workspace_name=None):
        self.subscription_id = subscription_id or os.getenv("AZ_SUBSCRIPTION_ID")
        self.resource_group = resource_group or os.getenv("AZ_RESOURCE_GROUP")
        self.workspace_name = workspace_name or os.getenv("AZ_ML_WORKSPACE")
        self.validate()
        
    def validate(self):
        if not all([self.subscription_id, self.resource_group, self.workspace_name]):
            raise MockSurrogateConfigError(
                "Missing required Azure environment variables: "
                "AZ_SUBSCRIPTION_ID, AZ_RESOURCE_GROUP, AZ_ML_WORKSPACE"
            )
    
    def create_ml_client(self):
        credential = mock_default_credential()
        return mock_ml_client(
            credential=credential,
            subscription_id=self.subscription_id,
            resource_group_name=self.resource_group,
            workspace_name=self.workspace_name
        )


class TestAzureMLClientFactory:
    """
    Comprehensive tests for AzureMLClientFactory functionality.
    
    Tests the factory pattern implementation for Azure ML client creation,
    credential management, and environment variable handling.
    """
    
    def test_initialization_with_explicit_params(self):
        """Test factory initialization with all parameters explicitly provided."""
        # Arrange: Set up explicit parameters
        subscription_id = "test-subscription-123"
        resource_group = "test-rg"
        workspace_name = "test-workspace"
        
        # Act: Create factory with explicit parameters
        factory = MockAzureMLClientFactory(
            subscription_id=subscription_id,
            resource_group=resource_group,
            workspace_name=workspace_name
        )
        
        # Assert: Verify parameters are set correctly
        assert factory.subscription_id == subscription_id, "Subscription ID should match input"
        assert factory.resource_group == resource_group, "Resource group should match input"
        assert factory.workspace_name == workspace_name, "Workspace name should match input"
        
        print("✅ Initialization with explicit parameters test passed")
    
    @patch.dict(os.environ, {
        'AZ_SUBSCRIPTION_ID': 'env-subscription-456',
        'AZ_RESOURCE_GROUP': 'env-rg',
        'AZ_ML_WORKSPACE': 'env-workspace'
    })
    def test_initialization_with_env_vars(self):
        """Test factory initialization using environment variables."""
        # Arrange: Environment variables are set via patch decorator
        
        # Act: Create factory without explicit parameters
        factory = MockAzureMLClientFactory()
        
        # Assert: Verify environment variables are used
        assert factory.subscription_id == 'env-subscription-456', "Should use env var for subscription"
        assert factory.resource_group == 'env-rg', "Should use env var for resource group"
        assert factory.workspace_name == 'env-workspace', "Should use env var for workspace"
        
        print("✅ Initialization with environment variables test passed")
    
    @patch.dict(os.environ, {
        'AZ_SUBSCRIPTION_ID': 'valid-subscription',
        'AZ_RESOURCE_GROUP': 'valid-rg',
        'AZ_ML_WORKSPACE': 'valid-workspace'
    })
    def test_validation_success(self):
        """Test that validation passes when all required environment variables are present."""
        # Arrange: Valid environment variables set via patch decorator
        
        # Act: Create factory (validation happens in __init__)
        factory = MockAzureMLClientFactory()
        
        # Assert: No exception should be raised, factory should be created successfully
        assert factory.subscription_id is not None, "Subscription ID should be set"
        assert factory.resource_group is not None, "Resource group should be set"
        assert factory.workspace_name is not None, "Workspace name should be set"
        
        print("✅ Validation success test passed")
    
    @patch.dict(os.environ, {}, clear=True)
    def test_validation_failure_missing_subscription(self):
        """Test validation failure when subscription_id is missing."""
        # Arrange: Clear environment variables
        
        # Act & Assert: Expect MockSurrogateConfigError for missing subscription
        try:
            MockAzureMLClientFactory()
            assert False, "Should have raised MockSurrogateConfigError for missing subscription"
        except MockSurrogateConfigError as e:
            assert "AZ_SUBSCRIPTION_ID" in str(e), "Error should mention missing subscription ID"
        
        print("✅ Validation failure for missing subscription test passed")
    
    @patch.dict(os.environ, {'AZ_SUBSCRIPTION_ID': 'test-sub'}, clear=True)
    def test_validation_failure_missing_resource_group(self):
        """Test validation failure when resource_group is missing."""
        # Arrange: Only subscription ID is set
        
        # Act & Assert: Expect MockSurrogateConfigError for missing resource group
        try:
            MockAzureMLClientFactory()
            assert False, "Should have raised MockSurrogateConfigError for missing resource group"
        except MockSurrogateConfigError as e:
            assert "AZ_RESOURCE_GROUP" in str(e), "Error should mention missing resource group"
        
        print("✅ Validation failure for missing resource group test passed")
    
    @patch.dict(os.environ, {
        'AZ_SUBSCRIPTION_ID': 'test-sub',
        'AZ_RESOURCE_GROUP': 'test-rg'
    }, clear=True)
    def test_validation_failure_missing_workspace(self):
        """Test validation failure when workspace_name is missing."""
        # Arrange: Subscription and resource group set, workspace missing
        
        # Act & Assert: Expect MockSurrogateConfigError for missing workspace
        try:
            MockAzureMLClientFactory()
            assert False, "Should have raised MockSurrogateConfigError for missing workspace"
        except MockSurrogateConfigError as e:
            assert "AZ_ML_WORKSPACE" in str(e), "Error should mention missing workspace"
        
        print("✅ Validation failure for missing workspace test passed")
    
    def test_create_ml_client_success(self):
        """Test successful MLClient creation with proper credential handling."""
        # Arrange: Set up factory
        factory = MockAzureMLClientFactory(
            subscription_id="test-sub",
            resource_group="test-rg", 
            workspace_name="test-workspace"
        )
        
        # Act: Create ML client
        client = factory.create_ml_client()
        
        # Assert: Verify client creation
        assert client is not None, "Should return a client instance"
        
        print("✅ MLClient creation success test passed")
    
    def test_create_ml_client_with_auth_failure(self):
        """Test handling of authentication failures during client creation."""
        # Arrange: Set up credential to raise authentication error
        original_credential = mock_default_credential
        mock_default_credential.side_effect = Exception("Authentication failed")
        
        factory = MockAzureMLClientFactory(
            subscription_id="test-sub",
            resource_group="test-rg",
            workspace_name="test-workspace"
        )
        
        try:
            # Act & Assert: Expect exception to propagate
            try:
                factory.create_ml_client()
                assert False, "Should have raised exception for authentication failure"
            except Exception as e:
                assert "Authentication failed" in str(e), "Should propagate authentication error"
            
            print("✅ Authentication failure handling test passed")
        finally:
            # Cleanup: Reset mock
            mock_default_credential.side_effect = None


# ==================================================
# DEFAULTAZURECREDENTIAL EXPLANATION
# ==================================================

"""
## What is DefaultAzureCredential?

DefaultAzureCredential is Azure's recommended authentication class that provides a simplified
authentication experience for Azure services. It automatically attempts multiple authentication
methods in a specific order until one succeeds, making it ideal for applications that need to
work across different environments (local development, CI/CD, production).

### Authentication Chain (in order of precedence):

1. **Environment Variables**: Checks for service principal credentials in environment variables:
   - AZURE_CLIENT_ID
   - AZURE_TENANT_ID
   - AZURE_CLIENT_SECRET (for client secret authentication)
   - AZURE_CLIENT_CERTIFICATE_PATH (for certificate authentication)

2. **Managed Identity**: Uses system-assigned or user-assigned managed identity when running
   on Azure resources (VMs, App Service, Functions, etc.)

3. **Visual Studio**: Uses credentials from Visual Studio if available

4. **Azure CLI**: Uses credentials from `az login` if Azure CLI is installed and authenticated

5. **Azure PowerShell**: Uses credentials from Azure PowerShell if available

6. **Interactive Browser**: Falls back to interactive browser authentication as last resort

### Benefits:

- **Environment Agnostic**: Same code works in development, testing, and production
- **Security**: No need to embed credentials in code or configuration files
- **Simplicity**: Single credential object handles multiple authentication scenarios
- **Best Practices**: Follows Azure's recommended authentication patterns

### Usage in AzureMLClientFactory:

The factory pattern encapsulates credential creation and management:
- Credentials are created fresh for each MLClient instance
- No credential objects are stored or passed around
- Reduces risk of credential exposure or misuse
- Provides clean separation between authentication and business logic

### Example Authentication Flow:

```python
# In development: Uses Azure CLI credentials from `az login`
# In CI/CD: Uses service principal from environment variables
# In production: Uses managed identity assigned to the Azure resource

credential = DefaultAzureCredential()
ml_client = MLClient(
    credential=credential,
    subscription_id="your-subscription",
    resource_group_name="your-rg",
    workspace_name="your-workspace"
)
```

This approach ensures secure, seamless authentication across all deployment environments
without requiring code changes or credential management complexity.

### Why Factory Pattern for Credentials?

The AzureMLClientFactory implements several important patterns:

1. **Encapsulation**: Credential logic is isolated from business logic
2. **Validation**: Required parameters are validated at construction time
3. **Environment Flexibility**: Supports both explicit params and environment variables
4. **Security**: No credential storage - credentials created fresh each time
5. **Testability**: Easy to mock and test without real Azure dependencies

This design follows Azure security best practices and provides a clean, maintainable
approach to Azure ML authentication.
"""


# ==================================================
# TEST RUNNER
# ==================================================

if __name__ == "__main__":
    print("Running TestAzureMLClientFactory tests...")
    print("=" * 60)
    
    # Create test instance
    factory_tests = TestAzureMLClientFactory()
    
    # Run all test methods
    try:
        factory_tests.test_initialization_with_explicit_params()
        factory_tests.test_initialization_with_env_vars()
        factory_tests.test_validation_success()
        factory_tests.test_validation_failure_missing_subscription()
        factory_tests.test_validation_failure_missing_resource_group()
        factory_tests.test_validation_failure_missing_workspace()
        factory_tests.test_create_ml_client_success()
        factory_tests.test_create_ml_client_with_auth_failure()
        
        print("=" * 60)
        print("🎉 All TestAzureMLClientFactory tests passed successfully!")
        
    except Exception as e:
        print("=" * 60)
        print(f"❌ Test failed: {str(e)}")
        raise
