from pathlib import Path
import pytest
import os
from typing import List, Optional
import pandas as pd
import logging

from backend.core._atlas.aggregates import KPI
from backend.core._matrix.matrix import BaseMatrixSimulator
import backend.core._matrix.matrix_dwsim as ma
import backend.core._atlas.aggregates as at
from backend.application.usecase_templates import (
    industrial_natural_gas_boiler_with_preheating_trains,
)
from backend.infrastructure.adaptors.matrix_adaptors import MatrixPrefectRunner


class TestMatrixPrefectRunner:
    @pytest.fixture
    def mock_simulator(self) -> BaseMatrixSimulator:
        simulator: BaseMatrixSimulator = ma.MatrixDWSim(
            simulation_label="Industrial Natural Gas Boiler With Preheating Trains"
        )
        atlas: at.AtlasRoot = industrial_natural_gas_boiler_with_preheating_trains()
        simulator.attach_model(atlas, True)
        return simulator

    @pytest.fixture
    def mock_kpis(self, mock_simulator) -> List[KPI]:
        atlas: at.AtlasRoot = mock_simulator._atlas
        kpi: KPI = atlas.kpi_collection.get_item("HEX-100 Heat Exchange")
        return [kpi]

    @pytest.fixture
    def mock_samples(self, mock_simulator):
        atlas: at.AtlasRoot = mock_simulator._atlas

        current_dir = Path(__file__).parent
        raw_samples: pd.DataFrame = pd.read_csv(
            current_dir / "df_samples.csv",
            usecols=[
                "Fuel BL->M-100_Temperature",
                "Fuel BL->M-100_Pressure",
                "Fuel BL->M-100_Mass Flow Rate",
                "Air BL->M-100_Temperature",
                "Air BL->M-100_Pressure",
                "Air BL->M-100_Mass Flow Rate",
                "Oil Feed BL->HEX-100_Mass Flow Rate",
                "HEX-100_Global Heat Transfer Coefficient",
                "HEX-100_Heat Loss",
            ],
        )
        in_var_names: List[str] = list(raw_samples.columns)
        delim: str = "_"
        in_var_name_uids = []

        for name in in_var_names:
            entity_label, var_stringify = [term.strip() for term in name.split(delim)]
            var: Optional[at.VOBaseVariable] = None
            logging.info(
                f"The variable {name} has entity label: {entity_label} and variable ui label: {var_stringify}."
            )
            # Go through all the variables from the collection to get specific item
            for variable in atlas.variables_collection.items:
                if (
                    variable.parent.label == entity_label
                    # NOTE review below to be aware of gotcha for string keys
                    and var_stringify  # Carbon dioxide <- comes from CompoundUUIDCollection
                    == atlas.variables_collection.get_ui_label(
                        variable
                    )  # Carbon Dioxide <- comes from ui_label of VariableUUIDCollection.
                ):
                    var = variable
                    logging.info(f"Variable object is obtained successfully.")
                    break
            # If variable is not present in the collection then it will raise key error.
            if var is None:
                raise KeyError(
                    f"{name},eqp:`{entity_label}`,var:`{var_stringify}` not found in variables. Ensure the variable name is correctly specified."
                )

            # Build the list of uids corresponding to the pretty name.
            logging.info(f"The variable {name} has uid: {str(var.uid)}.")
            in_var_name_uids.append(str(var.uid))

        in_var_name_map = dict(zip(in_var_names, in_var_name_uids))
        logging.info(f"Var name to UUID mapping: {in_var_name_map}")

        renamed_samples = raw_samples.rename(mapper=in_var_name_map, axis=1)
        logging.info(f"Renamed samples: {renamed_samples.to_dict('records')}")

        return renamed_samples

    def test_simulator(self, mock_kpis, mock_simulator, mock_samples):
        adaptor: MatrixPrefectRunner = MatrixPrefectRunner()
        kpis = adaptor.run_simulations(mock_simulator, mock_samples, mock_kpis)

        logging.info(f"Resultant KPI dataframe: {kpis}")
        assert kpis[0].min() > 150, "KPI results exceed lower bound of 150"
        assert kpis[0].max() < 250, "KPI results exceed upper bound of 250"
