from prefect.testing.utilities import prefect_test_harness
import pytest
import pandas as pd

from backend.infrastructure._orchaestration.flows.TestFlow import TestFlow
from backend.infrastructure._orchaestration.flows.Simulation import Simulation
from backend.application.example_repo import (
    industrial_natural_gas_boiler_with_preheating_trains,
)
from backend.infrastructure._db.dto import AtlasJSONSerializer
from backend.core._metis.z_WorkflowManager import WorkflowManager
from backend.tests.unit.endpoints.z_test_experiment import experiment_config
from pprint import pprint
import os


@pytest.fixture(autouse=True, scope="session")
def prefect_test_fixture():
    # Use an in-memory SQLite database to avoid disk space issues
    # os.environ["PREFECT_DATABASE_CONNECTION_URL"] = "sqlite:///:memory:"
    with prefect_test_harness():
        yield


def test_test_flow():
    flow_result = TestFlow.flow(run=1)
    expected_result = pd.DataFrame({"run": [1]})
    assert isinstance(flow_result, pd.DataFrame)
    assert expected_result.equals(flow_result)


def test_simulation_flow():
    # simulation_id = "Industrial Natural Gas Boiler With Preheating Trains"
    # atlas = industrial_natural_gas_boiler_with_preheating_trains()
    # # TODO @ZL Serialize atlas
    # atlas_json = AtlasJSONSerializer.serialize(atlas=atlas)
    # kpi_name = ...
    # sample_df = ...

    atlas = WorkflowManager.z_get_atlas("")
    test_stream = atlas.get_stream(by_label="Fuel BL->M-100")
    variables = test_stream.get_variables()
    print("variables = ")
    pprint(variables)
    atlas_json = AtlasJSONSerializer.serialize(atlas)

    test_wf_manager = experiment_config.bootstrap_wf_manager()
    kpi_name = test_wf_manager.ps.output_var_names[0]
    simulation_id = test_wf_manager.sim_id

    samples = test_wf_manager.ps.generate_samples
    all_in_var_names = (
        test_wf_manager.ps.input_var_names + test_wf_manager.ps.fixed_input_var_names
    )
    all_in_var_name_uids = test_wf_manager._convert_in_var_names_to_uidstr(
        in_var_names=all_in_var_names, atlas=atlas
    )
    in_var_name_map = dict(zip(all_in_var_names, all_in_var_name_uids))
    pprint(f"in_var_name_map = {in_var_name_map}")
    renamed_samples = samples.rename(mapper=in_var_name_map, axis=1)
    sample_df = renamed_samples.iloc[[0]]

    flow_result = Simulation.flow(
        simulation_id=simulation_id,
        atlas_json=atlas_json,
        kpi_name=kpi_name,
        sample_df=sample_df,
    )
    assert isinstance(flow_result, float)
    # TODO @ZL Assert KPI result value.
    assert ...


if __name__ == "__main__":
    test_simulation_flow()
