import backend.core._atlas.aggregates as at
import backend.core._matrix.matrix_dwsim as ma
import backend.infrastructure._db.dto as dto
import backend.application.usecase_templates as eg


def test_run_matrix_using_dto():
    atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

    serialized = dto.AtlasJSONSerializer.serialize(atlas)
    atlas2 = dto.AtlasJSONSerializer.deserialize(serialized)

    matrix = ma.MatrixDWSim("sim")

    matrix.attach_model(atlas2)
    matrix.setup_matrix()
    matrix.run_matrix()

