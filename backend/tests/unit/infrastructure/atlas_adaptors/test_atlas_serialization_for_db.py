from backend.core._atlas._singletons import ContVarSpecEnum
from backend.core._atlas.valueobjects import VOSensor
import backend.infrastructure._db.dto as se
import backend.application.usecase_templates as eg
import backend.core._atlas.aggregates as at
from copy import deepcopy
import json
from pprint import pprint, pformat
from deepdiff.diff import DeepDiff
from typing import Optional, Tuple, List, Dict
import logging
import uuid
from collections import Counter


def compare_objects(
    obj1, obj2, attrs: Optional[List[str]] = None
) -> Tuple[bool, Optional[str]]:
    """
    Compares two objects attribute by attribute and returns detailed comparison results.

    Returns:
        Tuple[bool, str]: (True, None) if objects match, (False, failure_message) if they differ
    """
    logging.info(f"COMPARING: {obj1}, {obj2}")
    _attrs = attrs if attrs is not None else obj1.__dict__.keys()

    for attr in _attrs:
        logging.info(f"checking: {attr}")

        if not hasattr(obj2, attr):
            return False, f"Attribute {attr} missing in second object"

        val1 = getattr(obj1, attr)
        val2 = getattr(obj2, attr)

        if val1 is None and val2 is None:
            continue
        elif val1 is None or val2 is None:
            return (
                False,
                f"Attribute {attr}: One value is None - \nval1: {val1}\nval2: {val2}",
            )
        elif val1 != val2:
            return (
                False,
                f"Attribute {attr}: Values differ - \nval1: {val1}\nval2: {val2}",
            )

    return True, "Values are equal"


def assert_model_dumps_equal(dump1, dump2, exclude_paths=None):
    """Compare two model dumps ignoring UUIDs and specified paths"""

    default_excludes = [
        "root['uid']",  # Ignore root UUID
        "root['version']",  # Ignore version numbers
        "root['created_at']",
    ]

    if exclude_paths:
        default_excludes.extend(exclude_paths)

    diff = DeepDiff(
        dump1,
        dump2,
        ignore_order=True,
        exclude_paths=default_excludes,
        report_repetition=True,
        verbose_level=2,
    )

    if diff:
        raise AssertionError(
            f"Model dumps are not equal. Differences:\n{diff.pretty()}"
        )
    return True


def extract_var_str_from_uid(atlas: at.AtlasRoot, var_uuid: uuid.UUID):
    try:
        var = atlas.variables_collection.get_item(var_uuid)
        var_parent_label = var.parent.label
        var_label = atlas.variables_collection.get_ui_label(var)

        return f"{var_parent_label}_{var_label}"
    except:
        return None


class TestHandlers:
    @staticmethod
    def test_serializer():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
        serialized = se.AtlasJSONSerializer.serialize(atlas)

        atlas2 = se.AtlasJSONSerializer.deserialize(serialized)

        assert atlas == atlas2

        dto = se.DTO_Atlas.hydrate(atlas)
        dto_2 = se.DTO_Atlas.hydrate(atlas2)

        assert_model_dumps_equal(
            dto.model_dump(),
            dto_2.model_dump(),
            exclude_paths=["root['equipments'][*]['uid']"],
        )

    @staticmethod
    def test_compound_handler():
        """Test serialization/deserialization of compound handlers with different combination of compounds"""
        # Test with additional compound types
        atlas = at.AtlasRoot("test", "test")
        test_compounds = [
            at.CASCompoundEnum.Water,
            at.CASCompoundEnum.CarbonDioxide,
            at.CASCompoundEnum.Methane,
            at.CASCompoundEnum.Ethane,
            at.CASCompoundEnum.Propane,
            at.CASCompoundEnum.Oxygen,
            at.CASCompoundEnum.Nitrogen,
            at.CASCompoundEnum.Benzene,
        ]

        for compound_enum in test_compounds:
            # Serialize
            compound = at.CompoundRef.get_vocompound_from_enum(compound_enum)
            compound_serialized = se.HandleVOCompound.serialize(compound, atlas)

            # Deserialize
            compound_deserialized = se.HandleVOCompound.deserialize(
                compound_serialized, atlas
            )

            # Compare original and deserialized
            assert compound == compound_deserialized
            assert compound.id == compound_deserialized.id
            assert compound.label == compound_deserialized.label
            assert compound == compound_deserialized

    @staticmethod
    def test_enum_handler():
        atlas = at.AtlasRoot("test", "test")
        enums = se.get_subclasses(at.BaseSpecEnum)
        for _enum in enums:
            print(f"CHECKING ENUM: {_enum}")
            for enum_item in _enum:
                # Serialize
                enum_serialized = se.HandleEnum.serialize(enum_item, atlas)

                # Deserialize
                enum_deserialized = se.HandleEnum.deserialize(enum_serialized, atlas)

                # Compare
                print("", enum_item, " ->\n", enum_deserialized, "\n---------")
                assert enum_item == enum_deserialized


class TestSerialization:
    @staticmethod
    def test_dto_compound():
        atlas = eg.pump_use_case()
        compound = at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Water)
        dto = se.DTO_Compound.hydrate(compound, atlas)
        pprint(json.loads(dto.model_dump_json()))
        assert dto.id == compound.id

    @staticmethod
    def test_dto_variable_contvar_collection():
        atlas = eg.pump_use_case()

        entity = atlas.get_equipment(by_label="P-100")
        collection = entity.get_collection(at.VarCollectionContinuous)

        vars = collection.items
        for var in vars:
            assert isinstance(var, at.VOBaseVariable)
            dto = se.DTO_Variable.hydrate(var, atlas, collection=collection)

            print(
                f"\nVAR: {var.variable_enum}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
            )
            assert dto.val == var.value
            assert dto.bounds == var.bounds

    @staticmethod
    def test_dto_variable_discvar_collection():
        atlas = eg.pump_use_case()

        entity = atlas.get_equipment(by_label="P-100")
        collection = entity.get_collection(at.VarCollectionDiscreteSet)

        vars = collection.items
        for var in vars:
            assert isinstance(var, at.VOBaseVariable)
            dto = se.DTO_Variable.hydrate(var, atlas, collection=collection)

            print(
                f"\nVAR: {var.variable_enum}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
            )
            _enum = var.value
            assert isinstance(_enum, at.BaseGenericEnum)

            assert dto.val == _enum.stringify
            assert dto.bounds == None

    @staticmethod
    def test_dto_variable_compoundmassmix_collection():
        atlas = eg.pump_use_case()

        entity = atlas.get_stream(by_equipments=("From Tank 100 BL", "P-100"))
        collection = entity.get_collection(at.VarCollectionCompoundMassRatio)

        vars = collection.items
        for var in vars:
            assert isinstance(var, at.VOBaseVariable)
            dto = se.DTO_Variable.hydrate(var, atlas, collection=collection)

            print(
                f"\nVAR: {var.variable_enum}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) } \nUUID OF ORIGINAL = {var.uid}"
            )
            assert dto.val == var.value
            assert dto.bounds == None

    @staticmethod
    def test_dto_variable_reaction_stoich():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

        reaction = atlas.reaction_collection.items[0]
        collection = reaction.get_collection(at.VarCollectionReactionStoich)
        for var in collection.items:
            assert isinstance(var, at.VOReactionStoich)
            dto = se.DTO_Variable.hydrate(var, atlas, collection=collection)

            print(
                f"\nVAR: {var.variable_enum}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
            )
            assert dto.val == var.value
            assert dto.bounds == None

    @staticmethod
    def test_dto_sensor():
        atlas = eg.pump_use_case()
        entity = atlas.get_equipment(by_label="P-100")
        var = entity.get_variable(ContVarSpecEnum.PressureIncrease)

        # Add sensor to Atlas
        sensor = VOSensor(variable_uid=var.uid, label="S-001")
        atlas.sensor_collection.add_item(sensor)

        dto = se.DTO_Sensor.hydrate(sensor)
        print(
            f"\nSENSOR: {sensor.label}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
        )
        assert dto.label == sensor.label
        assert dto.variable_uid == sensor.variable_uid

    @staticmethod
    def test_dto_equipment_basic():
        atlas = eg.pump_use_case()

        entity = atlas.get_equipment(by_label="P-100")
        dto = se.DTO_Equipment.hydrate(entity, atlas)

        print(
            f"\nENTITY: {entity.entity_type}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
        )
        assert dto.entity_type == entity.entity_type
        assert dto.uid == entity.uid
        assert dto.label == entity.label
        assert len(dto.states) == len(entity.get_variables())

    @staticmethod
    def test_dto_equipment_reactor():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

        entity = atlas.get_equipment(by_label="B-100")
        dto = se.DTO_Equipment.hydrate(entity, atlas)

        print(
            f"\nENTITY: {entity.entity_type}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
        )
        assert dto.entity_type == entity.entity_type
        assert dto.uid == entity.uid
        assert dto.label == entity.label
        assert len(dto.states) == len(entity.get_variables())

    @staticmethod
    def test_dto_stream():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

        for entity in atlas.get_streams():
            up, down = atlas.get_stream_connections(entity.label)

            dto = se.DTO_Stream.hydrate(entity, atlas)

            print(
                f"\nENTITY: {entity.entity_type}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
            )
            assert dto.entity_type == entity.entity_type
            assert dto.uid == entity.uid
            assert dto.connection_label == (up, down)
            assert len(dto.states) == len(entity.get_variables())

    @staticmethod
    def test_dto_reactions():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

        for entity in atlas.reaction_collection.items:
            assert isinstance(entity, at.ConversionReaction)
            dto = se.DTO_Reaction.hydrate(entity, atlas)

            print(
                f"\nENTITY: {entity.entity_type}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
            )
            assert dto.entity_type == entity.entity_type
            assert dto.uid == entity.uid
            assert len(dto.states) == len(entity.get_variables())
            assert set(dto.equipment_uuids) == set(e.uid for e in entity.equipments)

    @staticmethod
    def test_dto_kpis():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
        entity = atlas.kpi_collection.items[0]

        dto = se.DTO_KPI.hydrate(entity, atlas)
        print(
            f"\nENTITY: {entity.label}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
        )

    @staticmethod
    def test_dto_serialize():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
        dto = se.DTO_Atlas.hydrate(atlas)

        print(
            f"\nENTITY: {atlas.label}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
        )
        assert dto.label == atlas.label

    @staticmethod
    def test_dto_serialize_2():
        atlas = eg.pump_use_case()
        dto = se.DTO_Atlas.hydrate(atlas)

        print(
            f"\nENTITY: {atlas.label}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
        )
        assert dto.label == atlas.label

    @staticmethod
    def test_dto_preserves_user_config():
        # Create atlas with non-default config
        atlas = at.AtlasRoot(
            label="test_config",
            plant_id=str(uuid.uuid4()),
            user_config=at.UserAtlasConfig(
                user_industry=at.UserIndustryEnum.PHARMA,  # Non-default value
                matrix_engine=at.MatrixEngineEnum.GPROMS,  # Non-default value
            ),
        )

        # Create another atlas with default config for comparison
        default_atlas = at.AtlasRoot(label="default_config", plant_id=str(uuid.uuid4()))

        # Act
        dto = se.DTO_Atlas.hydrate(atlas)
        default_dto = se.DTO_Atlas.hydrate(default_atlas)

        # Assert
        print("\nTest Debug Information:")
        print(f"Custom config: {atlas.user_config.model_dump()}")
        print(f"Default config: {default_atlas.user_config.model_dump()}")
        print(f"DTO config: {dto.user_config}")

        # Verify configs are different
        assert (
            dto.user_config != default_dto.user_config
        ), "DTO config should not match default config"

        # Verify specific values were preserved
        assert (
            dto.user_config["user_industry"] == at.UserIndustryEnum.PHARMA
        ), f"Expected PHARMA industry, got {dto.user_config['user_industry']}"
        assert (
            dto.user_config["matrix_engine"] == at.MatrixEngineEnum.GPROMS
        ), f"Expected GPROMS engine, got {dto.user_config['matrix_engine']}"

    @staticmethod
    def test_dto_serialize_with_sensor():
        atlas = eg.pump_use_case()
        entity = atlas.get_equipment(by_label="P-100")
        var = entity.get_variable(ContVarSpecEnum.PressureIncrease)

        # Add sensor to Atlas
        sensor = VOSensor(variable_uid=var.uid, label="S-001")
        atlas.sensor_collection.add_item(sensor)
        timeset_col = "Case No"
        timestep_col = "timestep"
        atlas.sensor_timestep_col = timestep_col
        atlas.sensor_timeset_col = timeset_col

        dto = se.DTO_Atlas.hydrate(atlas)

        print(
            f"\nATLAS: {atlas.label}\nJSON:\n{pformat(json.loads(dto.model_dump_json())) }"
        )
        assert dto.label == atlas.label
        assert dto.sensor_timestep_col == atlas.sensor_timestep_col
        assert dto.sensor_timeset_col == atlas.sensor_timeset_col
        assert len(dto.sensors) == 1
        assert dto.sensors[0].label == sensor.label
        assert dto.sensors[0].variable_uid == sensor.variable_uid


class TestDeserialization:

    @staticmethod
    def test_compound_deserialization():
        compounds = [
            at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.Water),
            at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.CarbonDioxide),
            at.CompoundRef.get_vocompound_from_enum(
                at.CASCompoundEnum._1__1__2_Trichloroethane
            ),
            at.CompoundRef.get_vocompound_from_enum(at.CASCompoundEnum.R134a),
        ]
        atlas = at.AtlasRoot("test", "test")
        for c in compounds:
            assert isinstance(c, at.VOCompound)
            atlas.register_compound(c)
            compound_dto = se.DTO_Compound.hydrate(c, atlas)
            compound_obj = compound_dto.bootstrap(atlas)
            assert compound_obj.label == c.label
            assert compound_obj.id == c.id

    @staticmethod
    def test_get_subclasses():
        subclasses = se.get_subclasses(at.BaseSpecEnum)
        pprint(subclasses)
        assert subclasses is not None

    @staticmethod
    def test_handle_enum():
        atlas = at.AtlasRoot("dummy", "dummy")

        # Cont Var
        e = at.ContVarSpecEnum.ActualAirFlow
        enum_serialize = se.HandleEnum.serialize(e, atlas)
        enum_deserialize = se.HandleEnum.deserialize(enum_serialize, atlas)
        print(enum_deserialize)
        assert e == enum_deserialize

        # DiscItem
        e = at.DiscreteItemSpecEnum.CalculateArea
        enum_serialize = se.HandleEnum.serialize(e, atlas)
        enum_deserialize = se.HandleEnum.deserialize(enum_serialize, atlas)
        print(enum_deserialize)
        assert e == enum_deserialize

        # DiscSet
        e = at.DiscreteSetSpecEnum.FlashSpec
        enum_serialize = se.HandleEnum.serialize(e, atlas)
        enum_deserialize = se.HandleEnum.deserialize(enum_serialize, atlas)
        print(enum_deserialize)
        assert e == enum_deserialize

    @staticmethod
    def test_sensor_deserialization():
        atlas = eg.pump_use_case()
        entity = atlas.get_equipment(by_label="P-100")
        var = entity.get_variable(ContVarSpecEnum.PressureIncrease)

        # Add sensor to Atlas
        sensor = VOSensor(variable_uid=var.uid, label="S-001")
        sensor_dto = se.DTO_Sensor.hydrate(sensor)

        sensor_obj = sensor_dto.bootstrap(atlas)
        assert sensor_obj.label == sensor.label
        assert sensor_obj.variable_uid == sensor.variable_uid
        assert sensor_obj in atlas.sensor_collection.items

    @staticmethod
    def test_equipment_and_var_deserialization_basic():
        atlas = at.AtlasRoot("test", "test")
        entity = atlas.add_equipment(at.Heater, "H-001")

        dto = se.DTO_Equipment.hydrate(entity, atlas)

        atlas_copy = at.AtlasRoot("test2", "test2")
        entity_2 = dto.bootstrap(atlas_copy)

        pprint(entity_2)
        assert entity.label == entity_2.label
        dto_2 = se.DTO_Equipment.hydrate(entity_2, atlas_copy)
        assert dto_2.model_dump() == dto.model_dump()

    @staticmethod
    def test_stream_and_var_deserialization_basic():
        atlas = at.AtlasRoot("test", "test")
        e = atlas.add_equipment(at.Heater, "H-001")
        entity = atlas.add_stream(None, e, stream_type=at.InputStream)

        dto = se.DTO_Stream.hydrate(entity, atlas)
        atlas_copy = deepcopy(atlas)
        atlas_copy.remove_stream(by_stream=entity)

        dto_deserialized = dto.bootstrap(atlas_copy)

        assert entity.label == dto_deserialized.label
        dto_2 = se.DTO_Stream.hydrate(dto_deserialized, atlas_copy)
        assert dto.model_dump() == dto_2.model_dump()

    @staticmethod
    def test_reaction_deserialization():
        atlas = at.AtlasRoot("test", "test")
        # ADD EQUIPMENT
        B_100 = atlas.add_equipment(at.ConversionReactor, "B-100")

        # ADD COMPOUNDS
        compounds = [
            at.CASCompoundEnum.Methane,
            at.CASCompoundEnum.Ethane,
            at.CASCompoundEnum.Propane,
            at.CASCompoundEnum.CarbonDioxide,
            at.CASCompoundEnum.Nitrogen,
            at.CASCompoundEnum.Oxygen,
            at.CASCompoundEnum.N_Dodecane,
            at.CASCompoundEnum.N_Tetradecane,
            at.CASCompoundEnum.Cyclohexane,
            at.CASCompoundEnum.Toluene,
            at.CASCompoundEnum.N_Hexadecane,
            at.CASCompoundEnum.N_Octadecane,
            at.CASCompoundEnum.Water,
            at.CASCompoundEnum.Benzene,
        ]
        for c in compounds:
            atlas.register_compound(at.CompoundRef.get_vocompound_from_enum(c))

        Methane = atlas.retreive_compound(at.CASCompoundEnum.Methane)
        Ethane = atlas.retreive_compound(at.CASCompoundEnum.Ethane)
        Propane = atlas.retreive_compound(at.CASCompoundEnum.Propane)

        Carbon_dioxide = atlas.retreive_compound(at.CASCompoundEnum.CarbonDioxide)
        Nitrogen = atlas.retreive_compound(at.CASCompoundEnum.Nitrogen)
        Oxygen = atlas.retreive_compound(at.CASCompoundEnum.Oxygen)

        N_dodecane = atlas.retreive_compound(at.CASCompoundEnum.N_Dodecane)
        N_tetradecane = atlas.retreive_compound(at.CASCompoundEnum.N_Tetradecane)
        Cyclohexane = atlas.retreive_compound(at.CASCompoundEnum.Cyclohexane)
        Toluene = atlas.retreive_compound(at.CASCompoundEnum.Toluene)
        N_hexadecane = atlas.retreive_compound(at.CASCompoundEnum.N_Hexadecane)
        N_octadecane = atlas.retreive_compound(at.CASCompoundEnum.N_Octadecane)

        Water = atlas.retreive_compound(at.CASCompoundEnum.Water)

        Benzene = atlas.retreive_compound(at.CASCompoundEnum.Benzene)

        # ADD REACTIONS
        r1 = at.ConversionReaction("Combustion Reaction of Methane")
        r2 = at.ConversionReaction("Combustion Reaction of Ethane")
        r3 = at.ConversionReaction("Combustion Reaction of Propane")

        # Add Reaction Stoich
        c_s_one: Dict[at.VOCompound, int] = {
            Methane: -1,
            Oxygen: -2,
            Water: 2,
            Carbon_dioxide: 1,
        }
        for k, v in c_s_one.items():
            r1.add_variable(at.VOReactionStoich(k, v))

        r1.set_value(at.DiscreteSetSpecEnum.BaseCompound, Methane)
        r1.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "1")
        r1.set_value(
            at.DiscreteSetSpecEnum.ReactionPhase, at.DiscreteItemSpecEnum.REACTION_VAPOR
        )

        c_s_two: Dict[at.VOCompound, int] = {
            Ethane: -2,
            Oxygen: -7,
            Water: 6,
            Carbon_dioxide: 4,
        }
        for k, v in c_s_two.items():
            r2.add_variable(at.VOReactionStoich(k, v))

        r2.set_value(at.DiscreteSetSpecEnum.BaseCompound, Ethane)
        r2.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "1")
        r2.set_value(
            at.DiscreteSetSpecEnum.ReactionPhase, at.DiscreteItemSpecEnum.REACTION_VAPOR
        )
        c_s_three: Dict[at.VOCompound, int] = {
            Propane: -1,
            Oxygen: -5,
            Water: 4,
            Carbon_dioxide: 3,
        }
        for k, v in c_s_three.items():
            r3.add_variable(at.VOReactionStoich(k, v))

        r3.set_value(at.DiscreteSetSpecEnum.BaseCompound, Propane)
        r3.set_value(at.DiscreteSetSpecEnum.ConversionExpression, "1")
        r3.set_value(
            at.DiscreteSetSpecEnum.ReactionPhase, at.DiscreteItemSpecEnum.REACTION_VAPOR
        )

        # Add to atlas
        atlas.add_reaction_to_reactor(B_100, r1)
        atlas.add_reaction_to_reactor(B_100, r2)
        atlas.add_reaction_to_reactor(B_100, r3)

        # Test Serial and Deserial
        for reaction in [r1, r2, r3]:
            dto = se.DTO_Reaction.hydrate(reaction, atlas)
            atlas_2 = deepcopy(atlas)
            atlas_2.reaction_collection.remove_item(r1)
            reaction_2 = dto.bootstrap(atlas_2)
            dto_2 = se.DTO_Reaction.hydrate(reaction_2, atlas_2)
            pprint(
                DeepDiff(
                    dto.model_dump(),
                    dto_2.model_dump(),
                    ignore_order=True,
                    verbose_level=2,
                ).pretty()
            )
            assert dto.model_dump() == dto_2.model_dump()

    @staticmethod
    def test_full_roundtrip():
        atlas = eg.pump_use_case()
        dto = se.DTO_Atlas.hydrate(atlas)

        atlas_2 = dto.bootstrap()
        dto_2 = se.DTO_Atlas.hydrate(atlas_2)

        # assert that compoundmassmix is in stream
        for stream in atlas.get_streams():
            vars = stream.get_variables(filter=[at.VarCollectionCompoundMassRatio])
            assert len(vars) == len(atlas.compounds)
            for var in vars:
                collection_vars = atlas.variables_collection.items
                assert var in collection_vars

        pprint(
            DeepDiff(
                dto.model_dump(), dto_2.model_dump(), ignore_order=True, verbose_level=2
            ).pretty()
        )
        assert_model_dumps_equal(
            dto.model_dump(),
            dto_2.model_dump(),
            exclude_paths=["root['equipments'][*]['uid']"],
        )

    @staticmethod
    def test_full_roundtrip_2():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
        dto = se.DTO_Atlas.hydrate(atlas)

        atlas_2 = dto.bootstrap()
        dto_2 = se.DTO_Atlas.hydrate(atlas_2)

        pprint(
            DeepDiff(
                dto.model_dump(), dto_2.model_dump(), ignore_order=True, verbose_level=2
            ).pretty()
        )
        assert_model_dumps_equal(
            dto.model_dump(),
            dto_2.model_dump(),
            exclude_paths=["root['equipments'][*]['uid']"],
        )

    @staticmethod
    def test_full_roundtrip_with_sensor():
        atlas = eg.pump_use_case()
        entity = atlas.get_equipment(by_label="P-100")
        var = entity.get_variable(ContVarSpecEnum.PressureIncrease)

        # Add sensor to Atlas
        sensor = VOSensor(variable_uid=var.uid, label="S-001")
        atlas.sensor_collection.add_item(sensor)
        dto = se.DTO_Atlas.hydrate(atlas)

        # Hydrate bootstrapped Atlas so that sensor-variable association is kept
        atlas_2 = dto.bootstrap()
        dto_2 = se.DTO_Atlas.hydrate(atlas_2)

        pprint(
            DeepDiff(
                dto.model_dump(), dto_2.model_dump(), ignore_order=True, verbose_level=2
            ).pretty()
        )
        assert_model_dumps_equal(
            dto.model_dump(),
            dto_2.model_dump(),
            exclude_paths=["root['equipments'][*]['uid']", "root['sensors'][*]['uid']"],
        )

    @staticmethod
    def test_variable_uid_is_stable():
        atlas_i = eg.pump_use_case()
        dto = se.DTO_Atlas.hydrate(atlas_i)
        atlas_ii = dto.bootstrap()
        atlas_i_vars = atlas_i.variables_collection._collection
        atlas_ii_vars = atlas_ii.variables_collection._collection

        atlas_i_var_uids = [var.uid for var in atlas_i.variables_collection.items]
        atlas_ii_var_uids = [var.uid for var in atlas_ii.variables_collection.items]
        # NOTE - a variable is being created with duplicate IDs. This is in DTO BOotstrap

        atlas_i_varset = set(atlas_i_var_uids)
        atlas_ii_varset = set(atlas_ii_var_uids)

        assert len(atlas_i_varset) == len(atlas_i_var_uids)
        assert len(atlas_ii_varset) == len(atlas_ii_var_uids)

        if atlas_i_varset != atlas_ii_varset:
            only_in_i = atlas_i_varset - atlas_ii_varset
            only_in_ii = atlas_ii_varset - atlas_i_varset
            assert (
                False
            ), f"Sets differ:\nOnly in atlas_i: {only_in_i}\nOnly in atlas_ii: {only_in_ii}"
        assert atlas_i_varset == atlas_ii_varset

    @staticmethod
    def test_variable_uid_is_stable_2():
        atlas_i = eg.industrial_natural_gas_boiler_with_preheating_trains()
        dto = se.DTO_Atlas.hydrate(atlas_i)
        atlas_ii = dto.bootstrap()

        atlas_i_var_uids = [var.uid for var in atlas_i.variables_collection.items]
        atlas_ii_var_uids = [var.uid for var in atlas_ii.variables_collection.items]

        atlas_i_varset = set(atlas_i_var_uids)
        atlas_ii_varset = set(atlas_ii_var_uids)

        # # assert no duplicate UUIDs within each atlsa
        # assert len(atlas_i_varset) == len(atlas_i_var_uids)

        # Find duplicates in atlas_ii_var_uids
        duplicates = [
            item for item, count in Counter(atlas_ii_var_uids).items() if count > 1
        ]
        if duplicates:
            log_dup = {}
            for dup_uid in duplicates:
                pretty_var_name = extract_var_str_from_uid(atlas_ii, dup_uid)
                log_dup[dup_uid] = pretty_var_name

            assert False, f"Found duplicate UUIDs in atlas_ii: {pformat(log_dup)}"
        assert len(atlas_ii_varset) == len(atlas_ii_var_uids)
        assert len(atlas_i_var_uids) == len(atlas_ii_var_uids)

        if atlas_i_varset != atlas_ii_varset:
            only_in_i = atlas_i_varset - atlas_ii_varset
            only_in_ii = atlas_ii_varset - atlas_i_varset
            assert (
                False
            ), f"Sets differ:\nOnly in atlas_i: {only_in_i}\nOnly in atlas_ii: {only_in_ii}"

        log_atlas_i = {}
        for var_uuid in atlas_i_var_uids:
            pretty_label = extract_var_str_from_uid(atlas_i, var_uuid)
            log_atlas_i[pretty_label] = str(var_uuid)
        log_atlas_ii = {}

        for var_uuid in atlas_i_var_uids:
            pretty_label = extract_var_str_from_uid(atlas_ii, var_uuid)
            log_atlas_ii[pretty_label] = str(var_uuid)

        log = {}
        for pretty_str, var_uid in log_atlas_i.items():
            log[pretty_str] = (var_uid, log_atlas_ii[pretty_str])

        pprint(log)

        # Check labels generated
        atlas_i_prettylabels = set(log_atlas_i.keys())
        atlas_ii_prettylabels = set(log_atlas_ii.keys())
        if atlas_i_prettylabels != atlas_ii_prettylabels:
            only_in_i = atlas_i_prettylabels - atlas_ii_prettylabels
            only_in_ii = atlas_ii_prettylabels - atlas_i_prettylabels
            assert (
                False
            ), f"Sets differ:\nOnly in atlas_i: {only_in_i}\nOnly in atlas_ii: {only_in_ii}"

        assert atlas_i_varset == atlas_ii_varset

    @staticmethod
    def test_serializer():
        atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()
        serialized = se.AtlasJSONSerializer.serialize(atlas)

        atlas2 = se.AtlasJSONSerializer.deserialize(serialized)

        assert atlas == atlas2

        dto = se.DTO_Atlas.hydrate(atlas)
        dto_2 = se.DTO_Atlas.hydrate(atlas2)

        pprint(
            DeepDiff(
                dto.model_dump(), dto_2.model_dump(), ignore_order=True, verbose_level=2
            ).pretty()
        )
        assert_model_dumps_equal(
            dto.model_dump(),
            dto_2.model_dump(),
            exclude_paths=["root['equipments'][*]['uid']"],
        )

    @staticmethod
    def test_dto_deserialize_preserves_user_config():
        # Create atlas with non-default config
        original_atlas = at.AtlasRoot(
            label="test_config",
            plant_id=str(uuid.uuid4()),
            user_config=at.UserAtlasConfig(
                user_industry=at.UserIndustryEnum.PHARMA,  # Non-default value
                matrix_engine=at.MatrixEngineEnum.GPROMS,  # Non-default value
            ),
        )

        # Convert to DTO and back
        dto = se.DTO_Atlas.hydrate(original_atlas)
        deserialized_atlas = dto.bootstrap()

        # Assert
        print("\nTest Debug Information:")
        print(f"Original config: {original_atlas.user_config.model_dump()}")
        print(f"Deserialized config: {deserialized_atlas.user_config.model_dump()}")

        # Verify specific values were preserved through serialization/deserialization
        assert (
            deserialized_atlas.user_config.user_industry == at.UserIndustryEnum.PHARMA
        ), f"Expected PHARMA industry, got {deserialized_atlas.user_config.user_industry}"
        assert (
            deserialized_atlas.user_config.matrix_engine == at.MatrixEngineEnum.GPROMS
        ), f"Expected GPROMS engine, got {deserialized_atlas.user_config.matrix_engine}"

        # Verify the entire config object matches
        assert (
            deserialized_atlas.user_config.model_dump()
            == original_atlas.user_config.model_dump()
        ), "Deserialized config doesn't match original"


# if __name__ == "__main__":
#     TestHandlers.test_compound_handler()
