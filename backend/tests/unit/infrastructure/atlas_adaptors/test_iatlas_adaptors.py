from backend.tests._imports import *

import backend.core as core
from backend.infrastructure.adaptors.atlas_adaptors import *
from backend.infrastructure.adaptors.atlas_adaptors import _AtlasUnitOfWork

from sqlalchemy import create_engine, text
from sqlalchemy.engine.base import Engine

import backend.infrastructure._db.orm as orm

example_files = [
    eg.pump_use_case(),
    eg.heat_exchanger_use_case(),
    eg.industrial_natural_gas_boiler_with_preheating_trains(),
]

#############################

# Declarative Cases

@dataclass
class AdaptorTestCases:
    user_name: str
    n_models_total: int
    n_models_undeleted: int
    n_templates: int
    data: List[Tuple[core.at.AtlasRoot, core.at.AtlasMetadata]]

#############################

class AdaptorTestBase:
    """Abstract base class for testing Atlas adaptor implementations.
    
    This class implements a declarative testing pattern where test expectations
    are defined upfront in data structures rather than as inline assertions.
    
    Key benefits:
    1. Test expectations are separated from test execution logic
    2. Test cases can be easily extended without modifying test methods
    3. Expected results are clearly documented alongside test setup
    4. Tests are more maintainable as assertions check against the oracle
    5. Different adaptor implementations can be verified against the same expectations
    
    Usage:
        1. Subclass AdaptorTestBase
        2. Implement get_adaptor() method to return your adaptor implementation
        3. Add any implementation-specific test methods
        4. Run tests to verify your implementation conforms to IAtlas interface
    """
    
    # NOTE - adding an INIT stops test discovery. Intentionally left out
    
    def setup_test_cases(self):
        """Initialize test cases before each test method."""
        # Create test oracle with expected results
        self.test_cases: Dict[str, AdaptorTestCases] = self._build_test_cases()

        
    def _build_test_cases(self):
        """Build all test cases with their expected outcomes."""
        cases = {}
        
        # Helper function
        def _create_atlas_metadata(atlas, user_id="test_user", is_template=False, date_deleted=None):
            return core.at.AtlasMetadata(
                atlas_label=atlas.label,
                user_id=user_id,
                date_created=datetime.now(),
                date_modified=datetime.now(),
                date_deleted=date_deleted,
                is_template=is_template
            )
            
        # USER 1 DATA - ALL ACTIVE
        user_1_samples = [
            eg.pump_use_case(),
            eg.heat_exchanger_use_case(),
            eg.industrial_natural_gas_boiler_with_preheating_trains(),
        ]
        cases["user1"] = AdaptorTestCases(
            user_name="user1",
            n_models_total=len(user_1_samples),
            n_models_undeleted=len(user_1_samples),
            n_templates=0,
            data=[
                (atlas, _create_atlas_metadata(atlas, "user1", False))
                for atlas in user_1_samples
            ]
        )
        
        # USER 2 DATA - ONE DELETED
        user_2_samples = [
            eg.pump_use_case(),
            eg.heat_exchanger_use_case(),
            eg.industrial_natural_gas_boiler_with_preheating_trains(),
        ]
        user_2_samples_deleted = [
            eg.steam_turbine_use_case()
        ]
        user2_data = [
            (atlas, _create_atlas_metadata(atlas, "user2", False))
            for atlas in user_2_samples  
        ]
        user2_data.extend([
            (atlas, _create_atlas_metadata(atlas, "user2", False, datetime.now()))
            for atlas in user_2_samples_deleted 
        ])
        cases["user2"] = AdaptorTestCases(
            user_name="user2",
            n_models_total=len(user_2_samples) + len(user_2_samples_deleted),
            n_models_undeleted=len(user_2_samples),  
            n_templates=0,
            data=user2_data
        )
        
        # TEMPLATES - 2 TEMPLATES
        template_samples = [
            eg.pump_use_case(),
            eg.heat_exchanger_use_case(),
        ]
        template_samples_deleted= [
            eg.industrial_natural_gas_boiler_with_preheating_trains(),
        ]
        template_data= [
            (atlas, _create_atlas_metadata(atlas, "system",True))
            for atlas in  template_samples
        ]
        template_data.extend([
            (atlas, _create_atlas_metadata(atlas, "system",True, datetime.now()))
            for atlas in  template_samples_deleted
        ])
        cases["system"] = AdaptorTestCases(
            user_name="system",
            n_models_total=len(template_samples)+len(template_samples_deleted),
            n_models_undeleted=len(template_samples),
            n_templates=len(template_samples),
            data=template_data
        )
        
        return cases
    
    @abstractmethod
    def get_adaptor(self) -> core.IAtlas:
        """Get a fresh adaptor instance - override in subclass."""
        raise NotImplementedError("Subclasses must implement get_adaptor()")
    
    def get_example_atlases(self):
        """Get all test atlas data as a single flat list. test"""
        result = []
        for case in self.test_cases.values():
            result.extend(case.data)
        return result
        
    def setup_populated_adaptor(self):
        """Create and populate an adaptor with test data."""
        adaptor = self.get_adaptor()
        for atlas, metadata in self.get_example_atlases():
            adaptor.create(atlas, metadata)
        return adaptor
    
    def test_list_by_user(self):
        """Test filtering by user_id using oracle."""
        # Setup
        adaptor = self.setup_populated_adaptor()
        
        # Test each user case
        for user_name, case in self.test_cases.items():
            if user_name == "system":
                continue
                
            # Get all records for this user
            results = adaptor.list({"user_id": case.user_name})
            
            # Verify against our test oracle
            assert len(results) == case.n_models_total, \
                f"Expected {case.n_models_total} items for {case.user_name}, got {len(results)}"
                
            # Get only undeleted records
            active_results = adaptor.list({
                "user_id": case.user_name,
                "date_deleted_is_none": True
            })
            
            assert len(active_results) == case.n_models_undeleted, \
                f"Expected {case.n_models_undeleted} active items for {case.user_name}, got {len(active_results)}"
    
        
    def test_list_by_template(self):
        """Test template filtering independent of user_id."""
        # Setup
        adaptor = self.setup_populated_adaptor()
        
        # Get templates case from test oracle
        template_case = self.test_cases["system"]
        
        # Get all templates
        results = adaptor.list({"is_template": True})
        
        # Verify against test oracle
        assert len(results) == template_case.n_models_total, \
            f"Expected {template_case.n_models_total} templates, got {len(results)}"
            
        # Get only active templates
        active_results = adaptor.list({
            "is_template": True,
            "date_deleted_is_none": True
        })
        
        # Verify against test oracle 
        assert len(active_results) == template_case.n_templates, \
            f"Expected {template_case.n_templates} active templates, got {len(active_results)}"
            
        # Verify user_id is consistent
        for _, metadata in results:
            assert metadata.user_id == template_case.user_name, \
                f"Expected template user_id to be {template_case.user_name}"

    def test_save_updates_metadata(self):
        """Test that save updates the metadata with correct timestamps."""
        # Setup
        adaptor = self.setup_populated_adaptor()
        
        # Get first atlas and metadata for test
        atlas_sample, metadata_sample = list(self.get_example_atlases())[0]
        
        # Get original metadata from repository
        original_atlas, original_metadata = adaptor.get(
            metadata_sample.atlas_label, metadata_sample.user_id
        )
        
        # Record original modification date
        original_modified_date = original_metadata.date_modified
        
        # Wait to ensure timestamp difference would be detectable
        time.sleep(2)
        adaptor.save(original_atlas, original_metadata) # when saving, time modified should automatically increment
        
        # Retrieve updated atlas and metadata
        _, updated_metadata = adaptor.get(
            original_metadata.atlas_label, original_metadata.user_id
        )
        
            
        # Other metadata fields should remain unchanged
        assert updated_metadata.atlas_label == original_metadata.atlas_label, \
            "Atlas ID should not change"

        # Other metadata fields should remain unchanged
        assert updated_metadata.user_id == original_metadata.user_id, \
            "User ID should not change"
            
        assert updated_metadata.date_created == original_metadata.date_created, \
            "Creation date should not change"

        assert updated_metadata.date_modified > original_modified_date, \
            "Modification date was not updated"

    def test_soft_delete(self):
        """Test that delete properly marks items as deleted without removing them."""
        # Setup
        adaptor = self.setup_populated_adaptor()
        
        # Get first undeleted atlas for test
        test_sample = next(
            ((atlas, metadata) for atlas, metadata in self.get_example_atlases() 
             if metadata.date_deleted is None),
            None  # Default if no undeleted atlas is found
        )
                
        assert test_sample is not None, "No undeleted atlas available for test"
        atlas_sample, metadata_sample = test_sample
        
        # Count atlases for the user before deletion
        results_before = adaptor.list({"user_id": metadata_sample.user_id})
        active_before = adaptor.list({
            "user_id": metadata_sample.user_id, 
            "date_deleted_is_none": True
        })
        
        # Delete the atlas
        adaptor.delete(metadata_sample.atlas_label, metadata_sample.user_id) # adaptor takes care of abstraction
        
        # Verify we can still get it after deletion
        deleted_atlas, deleted_metadata = adaptor.get(
            metadata_sample.atlas_label, metadata_sample.user_id
        )
        
        # Verify it's marked as deleted
        assert deleted_metadata.date_deleted is not None, \
            "Deleted atlas should have date_deleted set"
            
        # Verify item counts after deletion
        results_after = adaptor.list({"user_id": metadata_sample.user_id})
        active_after = adaptor.list({
            "user_id": metadata_sample.user_id, 
            "date_deleted_is_none": True
        })
        
        # Total count should remain the same, active count should decrease by 1
        assert len(results_after) == len(results_before), \
            "Total atlas count should not change after soft delete"
            
        assert len(active_after) == len(active_before) - 1, \
            "Active atlas count should decrease by 1 after soft delete"
        
    def test_load_templates(self): 
        adaptor = self.setup_populated_adaptor()

        adaptor.load_templates()

        templates = adaptor.list({core.AltasMetadataEnums.IS_TEMPLATE.value: True})

        expected_template_labels = sorted([atlas.label for atlas in eg.USER_TEMPLATES])
        template_labels = sorted([metadata.atlas_label for _, metadata in templates])
        
        assert template_labels == expected_template_labels, "Templates should match example repo"


class TestAtlasInMemAdaptor(AdaptorTestBase):
    """Implementation-specific tests for the in-memory adaptor.
    
    This class implements concrete tests for the AtlasInMemAdaptor implementation,
    leveraging the declarative test pattern defined in AdaptorTestBase.
    """
    
    def setup_method(self):
        """Setup test cases and prepare test environment."""
        self.setup_test_cases()
    
    def get_adaptor(self) -> core.IAtlas:
        """Get a fresh in-memory adaptor instance."""
        return AtlasInMemAdaptor()


####################

# POSTGRE


class TestAtlasPostgre(AdaptorTestBase):
    """PostgreSQL implementation tests for the AtlasPostgreAdaptor.
    
    Tests PostgreSQL-specific behaviors including:
    - Database persistence across connections
    - Transaction isolation
    - Connection pooling characteristics
    - PostgreSQL error handling
    """
    
    USERNAME = 'alephuser'
    PASSWORD = 'alephpassword'
    HOST = 'localhost'  # Docker service name
    PORT = 5432
    DATABASE_ROOT = 'alephdb'
    
    db_engine = None
    db_name = None

    @classmethod
    def check_db_connection(cls):
        """Verify PostgreSQL connection before attempting tests"""
        import psycopg2
        try:
            print(f"Attempting to connect to PostgreSQL at {cls.HOST}:{cls.PORT}/{cls.DATABASE_ROOT}")
            conn = psycopg2.connect(
                dbname=cls.DATABASE_ROOT,
                user=cls.USERNAME,
                password=cls.PASSWORD,
                host=cls.HOST,
                port=cls.PORT,
                connect_timeout=5
            )
            conn.close()
            print("PostgreSQL connection successful!")
            return True
        except Exception as e:
            print(f"PostgreSQL connection failed: {str(e)}")
            
            # Try localhost as fallback when not in Docker
            try:
                print("Trying localhost fallback...")
                conn = psycopg2.connect(
                    dbname=cls.DATABASE_ROOT,
                    user=cls.USERNAME,
                    password=cls.PASSWORD,
                    host='localhost',
                    port=cls.PORT,
                    connect_timeout=5
                )
                conn.close()
                print("Localhost connection successful!")
                cls.HOST = 'localhost'
                return True
            except Exception as e2:
                print(f"Localhost fallback failed: {str(e2)}")
                return False

    @classmethod
    def setup_db_engine(cls):
        """Create test database once for all tests in the class."""
        # Check connection first
        if not cls.check_db_connection():
            pytest.skip("Could not connect to PostgreSQL database")
            return

        try:
            cls.db_engine, cls.db_name = cls.create_postgres_test_database()
            print(f"Created test database: {cls.db_name}")
        except Exception as e:
            print(f"Failed to create test database: {str(e)}")
            pytest.skip(f"PostgreSQL test setup failed: {str(e)}")

    @classmethod
    def teardown_class(cls):
        """Clean up test database after all tests complete."""
        if cls.db_engine and cls.db_name:
            cls.teardown_postgres_test_database(cls.db_engine, cls.db_name)
            print(f"Dropped test database: {cls.db_name}")

    @classmethod
    def create_postgres_test_database(cls) -> Tuple[Engine, str]:
        """Create a single test database for all tests in this class."""
        temp_db_name = f"test_db_{uuid.uuid4().hex}"

        # Create main DB on Postgres
        main_engine = create_engine(
            f"postgresql://{cls.USERNAME}:{cls.PASSWORD}@{cls.HOST}:{cls.PORT}/{cls.DATABASE_ROOT}"
        )
        try:
            with main_engine.connect() as conn:
                conn.execute(text("COMMIT"))
                conn.execute(text(f'CREATE DATABASE "{temp_db_name}"'))
        except Exception as e:
            print(f"Error creating test database: {str(e)}")
            raise

        # Configure ephemeral DB
        ephemeral_engine = create_engine(
            f"postgresql://{cls.USERNAME}:{cls.PASSWORD}@{cls.HOST}:{cls.PORT}/{temp_db_name}"
        )

        # Use orm to initialize schema
        orm.Base.metadata.create_all(ephemeral_engine)

        return ephemeral_engine, temp_db_name

    @classmethod
    def teardown_postgres_test_database(cls, engine: Engine, db_name: str):
        """Drop the test database completely."""
        engine.dispose()
        main_engine = create_engine(
            f"postgresql://{cls.USERNAME}:{cls.PASSWORD}@{cls.HOST}:{cls.PORT}/{cls.DATABASE_ROOT}"
        )

        with main_engine.connect() as conn:
            conn.execute(text("COMMIT"))
            conn.execute(
                text(
                    f"""
                    SELECT pg_terminate_backend(pg_stat_activity.pid)
                    FROM pg_stat_activity
                    WHERE pg_stat_activity.datname = '{db_name}'
                      AND pid <> pg_backend_pid()
                    """
                )
            )
            conn.execute(text(f'DROP DATABASE "{db_name}"'))

    @classmethod
    def drop_all_test_dbs(cls, prefix: str = "test_db_") -> None:
        """
        Removes all test databases with the given prefix.
        Useful for cleanup after tests that may have failed and left databases behind.
        """
        main_engine = create_engine(
            f"postgresql://{cls.USERNAME}:{cls.PASSWORD}@{cls.HOST}:{cls.PORT}/{cls.DATABASE_ROOT}"
        )

        with main_engine.connect() as conn:
            # Get list of test databases
            result = conn.execute(
                text(f"SELECT datname FROM pg_database WHERE datname LIKE '{prefix}%'")
            )
            test_dbs = [row[0] for row in result]

            # Drop each test database
            for db_name in test_dbs:
                conn.execute(text("COMMIT"))
                conn.execute(
                    text(
                        f"""
                        SELECT pg_terminate_backend(pid) 
                        FROM pg_stat_activity 
                        WHERE datname = '{db_name}'
                        """
                    )
                )
                conn.execute(text(f'DROP DATABASE IF EXISTS "{db_name}"'))
                print(f"Dropped leftover test database: {db_name}")

    def setup_method(self):
        """Pytest method called by pyest before EACH test method
        Reset tables between tests and initialize test cases."""
        self.setup_test_cases()
        
        # Reset tables to ensure test isolation
        # This is faster than recreating the database for each test
        if self.db_engine:
            orm.Base.metadata.drop_all(self.db_engine)
            orm.Base.metadata.create_all(self.db_engine)
            print(f"Reset tables in database: {self.db_name}")
    
    def get_adaptor(self) -> core.IAtlas:
        """Get a PostgreSQL adaptor instance configured to use the test database."""
        if self.db_name is None or self.db_engine is None:
            self.__class__.setup_db_engine()
        
        uow = _AtlasUnitOfWork(
            user=self.USERNAME,
            password=self.PASSWORD,
            host=self.HOST,
            database=self.db_name
        )
        return AtlasPostgreAdaptor(uow)
    
######################################

# import psycopg2

# def check_connection():
#     configs = [
#         {"host": "localhost", "dbname": "alephdb", "user": "alephuser", "password": "alephpassword", "port": 5432},
#         # {"host": "backend_db", "dbname": "alephdb", "user": "alephuser", "password": "alephpassword", "port": 5432},
#     ]
    
#     for config in configs:
#         try:
#             print(f"Trying {config['host']}:{config['port']}/{config['dbname']}...")
#             conn = psycopg2.connect(**config, connect_timeout=3)
#             print(f"Connection successful to {config['host']}!")
#             conn.close()
#             return True
#         except Exception as e:
#             print(f"Connection failed: {str(e)}")
    
#     return False


# Ensure proper cleanup if running as main script
if __name__ == "__main__":
    pass
    # check_connection()