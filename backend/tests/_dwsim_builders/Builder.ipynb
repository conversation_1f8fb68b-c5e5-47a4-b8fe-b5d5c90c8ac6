{"cells": [{"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(\n", "    Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "    \"/code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\",\n", ")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["# add compounds\n", "cnames = [\"Water\", \"Methanol\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding Objects"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["PUMP_1 = sim.AddObject(ObjectType.Pump, 50, 50, \"PUMP-1\")\n", "one = sim.AddObject(ObjectType.MaterialStream, 100, 100, \"1\")\n", "two = sim.AddObject(ObjectType.MaterialStream, 150, 150, \"2\")\n", "E1 = sim.AddObject(ObjectType.EnergyStream, 200, 200, \"E1\")\n", "HT_1 = sim.AddObject(ObjectType.Heater, 250, 250, \"HT-1\")\n", "E2 = sim.AddObject(ObjectType.EnergyStream, 300, 300, \"E2\")\n", "four = sim.AddObject(ObjectType.MaterialStream, 350, 350, \"4\")\n", "V_1 = sim.AddObject(ObjectType.Vessel, 400, 400, \"V-1\")\n", "six = sim.AddObject(ObjectType.MaterialStream, 450, 450, \"6\")\n", "seven = sim.AddObject(ObjectType.MaterialStream, 500, 500, \"7\")\n", "CL_1 = sim.AddObject(ObjectType.Cooler, 550, 550, \"CL-1\")\n", "E3 = sim.AddObject(ObjectType.EnergyStream, 600, 600, \"E3\")\n", "nine = sim.AddObject(ObjectType.MaterialStream, 650, 650, \"9\")\n", "C_1 = sim.AddObject(ObjectType.Compressor, 700, 700, \"C-1\")\n", "E4 = sim.AddObject(ObjectType.EnergyStream, 750, 750, \"E4\")\n", "ten = sim.AddObject(ObjectType.MaterialStream, 800, 800, \"10\")\n", "X_1 = sim.AddObject(ObjectType.Expander, 850, 850, \"X-1\")\n", "E5 = sim.AddObject(ObjectType.EnergyStream, 900, 900, \"E5\")\n", "eleven = sim.AddObject(ObjectType.MaterialStream, 950, 950, \"11\")\n", "valve_1 = sim.AddObject(ObjectType.Valve, 1000, 1000, \"VALVE-1\")\n", "thirteen = sim.AddObject(ObjectType.MaterialStream, 1050, 1050, \"13\")\n", "OP_1 = sim.AddObject(ObjectType.OrificePlate, 1100, 1100, \"OP-1\")\n", "fourteen = sim.AddObject(ObjectType.MaterialStream, 1150, 1150, \"14\")\n", "HX_1 = sim.AddObject(ObjectType.HeatExchanger, 1200, 1200, \"HX-1\")\n", "fifteen = sim.AddObject(ObjectType.MaterialStream, 1250, 1250, \"15\")\n", "sixteen = sim.AddObject(ObjectType.MaterialStream, 1300, 1300, \"16\")\n", "seventeen = sim.AddObject(ObjectType.MaterialStream, 1350, 1350, \"17\")\n", "# tank_1 = sim.AddObject(ObjectType.Tank,1400,1400, 'TANK-1')\n", "# nineteen = sim.AddObject(ObjectType.MaterialStream,1450,1450, '19')\n", "# CS_1 = sim.AddObject(ObjectType.ComponentSeparator,1500,1500, 'CS-1')\n", "# E6 = sim.AddObject(ObjectType.EnergyStream,1550,1550, 'E6')\n", "# twenety = sim.AddObject(ObjectType.MaterialStream,1600,1600, '20')\n", "# twentyone = sim.AddObject(ObjectType.MaterialStream,1650,1650, '21')\n", "SPL_1 = sim.AddObject(ObjectType.NodeOut, 1700, 1700, \"SPL-1\")\n", "twentytwo = sim.AddObject(ObjectType.MaterialStream, 1750, 1750, \"22\")\n", "twentythree = sim.AddObject(ObjectType.MaterialStream, 1800, 1800, \"23\")\n", "MIX_1 = sim.AddObject(ObjectType.NodeIn, 1850, 1850, \"MIX-1\")\n", "twentysix = sim.AddObject(ObjectType.MaterialStream, 1900, 1900, \"26\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Getting as objects"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["PUMP_1 = PUMP_1.GetAsObject()\n", "one = one.GetAsObject()\n", "two = two.GetAsObject()\n", "E1 = E1.GetAsObject()\n", "HT_1 = HT_1.GetAsObject()\n", "E2 = E2.GetAsObject()\n", "four = four.GetAsObject()\n", "V_1 = V_1.GetAsObject()\n", "six = six.GetAsObject()\n", "seven = seven.GetAsObject()\n", "CL_1 = CL_1.GetAsObject()\n", "E3 = E3.GetAsObject()\n", "nine = nine.GetAsObject()\n", "C_1 = C_1.GetAsObject()\n", "E4 = E4.GetAsObject()\n", "ten = ten.GetAsObject()\n", "X_1 = X_1.GetAsObject()\n", "E5 = E5.GetAsObject()\n", "eleven = eleven.GetAsObject()\n", "valve_1 = valve_1.GetAsObject()\n", "thirteen = thirteen.GetAsObject()\n", "OP_1 = OP_1.GetAsObject()\n", "fourteen = fourteen.GetAsObject()\n", "HX_1 = HX_1.GetAsObject()\n", "fifteen = fifteen.GetAsObject()\n", "sixteen = sixteen.GetAsObject()\n", "seventeen = seventeen.GetAsObject()\n", "# tank_1 = tank_1.GetAsObject()\n", "# nineteen = nineteen.GetAsObject()\n", "# CS_1 = CS_1.GetAsObject()\n", "# E6 = E6.GetAsObject()\n", "# twenety = twenety.GetAsObject()\n", "# twentyone = twentyone.GetAsObject()\n", "SPL_1 = SPL_1.GetAsObject()\n", "twentytwo = twentytwo.GetAsObject()\n", "twentythree = twentythree.GetAsObject()\n", "MIX_1 = MIX_1.GetAsObject()\n", "twentysix = twentysix.GetAsObject()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Connecting Objects\n", "\n", "The `.ConnectObject` method is based on a simple one on one approach i.e. from and to connections"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["sim.ConnectObjects(one.GraphicObject, PUMP_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(E1.GraphicObject, PUMP_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(PUMP_1.GraphicObject, two.GraphicObject, -1, -1)\n", "sim.ConnectObjects(two.GraphicObject, HT_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(E2.GraphicObject, HT_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(HT_1.GraphicObject, four.GraphicObject, -1, -1)\n", "sim.ConnectObjects(four.GraphicObject, V_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(V_1.GraphicObject, six.GraphicObject, -1, -1)\n", "sim.ConnectObjects(V_1.GraphicObject, seven.GraphicObject, -1, -1)\n", "sim.ConnectObjects(six.GraphicObject, C_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(E4.GraphicObject, C_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(C_1.GraphicObject, ten.GraphicObject, -1, -1)\n", "sim.ConnectObjects(ten.GraphicObject, X_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(X_1.GraphicObject, E5.GraphicObject, -1, -1)\n", "sim.ConnectObjects(X_1.GraphicObject, eleven.GraphicObject, -1, -1)\n", "sim.ConnectObjects(eleven.GraphicObject, valve_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(valve_1.GraphicObject, thirteen.GraphicObject, -1, -1)\n", "sim.ConnectObjects(thirteen.GraphicObject, MIX_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(seven.GraphicObject, CL_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(CL_1.GraphicObject, nine.GraphicObject, -1, -1)\n", "sim.ConnectObjects(CL_1.GraphicObject, E3.GraphicObject, -1, -1)\n", "sim.ConnectObjects(nine.GraphicObject, OP_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(OP_1.GraphicObject, fourteen.GraphicObject, -1, -1)\n", "sim.ConnectObjects(fourteen.GraphicObject, HX_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(HX_1.GraphicObject, sixteen.GraphicObject, -1, -1)\n", "sim.ConnectObjects(fifteen.GraphicObject, HX_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(HX_1.GraphicObject, seventeen.GraphicObject, -1, -1)\n", "# sim.ConnectObjects(sixteen.GraphicObject,tank_1.GraphicObject,-1,-1)\n", "# sim.ConnectObjects(tank_1.GraphicObject,nineteen.GraphicObject,-1,-1)\n", "# sim.ConnectObjects(nineteen.GraphicObject,CS_1.GraphicObject,-1,-1)\n", "# sim.ConnectObjects(CS_1.GraphicObject,twenety.GraphicObject,-1,-1)\n", "# sim.ConnectObjects(CS_1.GraphicObject,twentyone.GraphicObject,-1,-1)\n", "# sim.ConnectObjects(CS_1.GraphicObject,E6.GraphicObject,-1,-1)\n", "sim.ConnectObjects(seventeen.GraphicObject, SPL_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(SPL_1.GraphicObject, twentytwo.GraphicObject, -1, -1)\n", "sim.ConnectObjects(SPL_1.GraphicObject, twentythree.GraphicObject, -1, -1)\n", "sim.ConnectObjects(twentytwo.GraphicObject, MIX_1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(MIX_1.GraphicObject, twentysix.GraphicObject, -1, -1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Particular method of connecting equipments\n", "\n", "In this method there are a total of 5 keywords named as follows:\n", "- `.ConnectEnergyStream`\n", "- `.ConnectFeedEnergyStream`\n", "- `.ConnectFeedMaterialStream`\n", "- `.ConnectProductEnergyStream`\n", "- `.ConnectProductMaterialStream`\n", "\n", "The method is similar to `.ConnectObject` the only key difference is now is the object goes in first place followed by port number"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["# Creating objects\n", "# HX_2 = sim.AddObject(ObjectType.HeatExchanger,50,50,\"HX-2\")\n", "# thirty1 = sim.AddObject(ObjectType.MaterialStream,50,50,\"31\")\n", "# thirty2 = sim.AddObject(ObjectType.MaterialStream,50,50,\"32\")\n", "# thirty3 = sim.AddObject(ObjectType.MaterialStream,50,50,\"33\")\n", "# thirty4 = sim.AddObject(ObjectType.MaterialStream,50,50,\"34\")\n", "\n", "# Getting as objects\n", "# HX_2 = HX_2.GetAsObject()\n", "# thirty1 = thirty1.GetAsObject()\n", "# thirty2 = thirty2.GetAsObject()\n", "# thirty3 = thirty3.GetAsObject()\n", "# thirty4 = thirty4.GetAsObject()\n", "\n", "# Connecting objects in a particular manner\n", "# HX_2.ConnectFeedMaterialStream(thirty1,0)\n", "# HX_2.ConnectFeedMaterialStream(thirty2,1)\n", "# HX_2.ConnectProductMaterialStream(thirty3,0)\n", "# HX_2.ConnectProductMaterialStream(thirty4,1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Auto Layout"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["sim.AutoLayout()"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["# # Adding Objects\n", "# Feed = sim.AddObject(ObjectType.MaterialStream,50,50,\"1\")\n", "# Pressurized_Feed = sim.AddObject(ObjectType.MaterialStream,50,50,\"2\")\n", "# Heated_Feed = sim.AddObject(ObjectType.MaterialStream,50,50,\"3\")\n", "# Pump = sim.AddObject(ObjectType.Pump,50,50,\"PP-100\")\n", "# Heater = sim.AddObject(ObjectType.Heater,50,50,\"HT-100\")\n", "\n", "# # Getting as objects\n", "# Feed = Feed.GetAsObject()\n", "# Pressurized_Feed = Pressurized_Feed.GetAsObject()\n", "# Heated_Feed = Heated_Feed.GetAsObject()\n", "# Pump = Pump.GetAsObject()\n", "# Heater = Heater.GetAsObject()"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["# # Connecting Objects\n", "# sim.ConnectObjects(Feed.GraphicObject, Pump.GraphicObject, -1, -1)\n", "# sim.ConnectObjects(Pump.GraphicObject, Pressurized_Feed.GraphicObject, -1, -1)\n", "# sim.ConnectObjects(Pressurized_Feed.GraphicObject, Heater.GraphicObject, -1, -1)\n", "# sim.ConnectObjects(Heater.GraphicObject, Heated_Feed.GraphicObject, -1, -1)\n", "\n", "# # Auto Layout\n", "# sim.AutoLayout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Thermodynamics addition"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7fa85d943b00>"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["sim.CreateAndAddPropertyPackage(\"NRTL\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Properties addition on main streams"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"data": {"text/plain": ["'15: mass flow set to 1 kg/s'"]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["# Setting the properties of the input streams\n", "one.SetOverallComposition(Array[float]([0.5, 0.5]))\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(298.15)  # K\n", "one.<PERSON><PERSON><PERSON><PERSON>(101325)  # Pa\n", "one.Set<PERSON><PERSON><PERSON><PERSON>(1)  # kg/s\n", "\n", "# Setting the properties of the input stream\n", "fifteen.Set<PERSON><PERSON>allComposition(Array[float]([0.99, 0.01]))\n", "fifteen.Set<PERSON><PERSON><PERSON><PERSON>(300)  # K\n", "fifteen.<PERSON><PERSON><PERSON><PERSON>(101325)  # Pa\n", "fifteen.Set<PERSON>ass<PERSON>low(1)  # kg/s"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Getting the calc modes of the equipments"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [], "source": ["# Setting pump calc mode\n", "PUMP_1.CalcMode = UnitOperations.Pump.CalculationMode.OutletPressure\n", "PUMP_1.Pout = 301325\n", "\n", "# Setting heater calc mode\n", "HT_1.CalcMode = UnitOperations.Heater.CalculationMode.OutletTemperature\n", "HT_1.OutletTemperature = 382.06\n", "\n", "# Setting compressor calc mode\n", "C_1.CalcMode = UnitOperations.Compressor.CalculationMode.OutletPressure\n", "C_1.OutPressure = 501325\n", "\n", "# Setting expander calc mode\n", "X_1.CalcMode = UnitOperations.Expander.CalculationMode.PowerGenerated\n", "X_1.DeltaQ = 100\n", "\n", "# Setting valve calc mode\n", "valve_1.CalcMode = UnitOperations.Valve.CalculationMode.DeltaP\n", "valve_1.DeltaP = 50\n", "\n", "# Setting cooler calc mode\n", "CL_1.CalcMode = UnitOperations.Cooler.CalculationMode.OutletTemperature\n", "CL_1.OutletTemperature = 298.15\n", "\n", "# Setting Orifice Plate Parameters\n", "OP_1.Tapping = UnitOperations.OrificePlate.OrificeType.FlangeTaps\n", "OP_1.Orifice_Diameter = UnitOperations.OrificePlate.OrificeDiameter\n", "OP_1.OrificeDiameter = 100\n", "OP_1.Interal_Pipe_Diamter = UnitOperations.OrificePlate.InternalPipeDiameter\n", "OP_1.Interal_Pipe_Diamter = 200\n", "\n", "# Setting Heat Exchanger Parameters\n", "HX_1.CalcMode = UnitOperations.HeatExchangerCalcMode.CalcBothTemp_UA\n", "# HX_1.Hot_Side_Pressure_Drop = UnitOperations.HeatExchanger.HotSidePressureDrop\n", "HX_1.HotSidePressureDrop = 10\n", "# HX_1.Cold_Side_Pressure_Drop = UnitOperations.HeatExchanger.ColdSidePressureDrop\n", "HX_1.ColdSidePressureDrop = 0\n", "# HX_1.Global_HTC = UnitOperations.HeatExchanger.OverallCoefficient\n", "HX_1.OverallCoefficient = 1000\n", "# HX_1.Heat_Area = UnitOperations.HeatExchanger.Area\n", "HX_1.Area = 1\n", "# HX_1.Heat_Loss = UnitOperations.HeatExchanger.HeatLoss\n", "HX_1.HeatLoss = 0\n", "\n", "# Setting Tank Parameters\n", "# tank_1.Vol = UnitOperations.Tank.Volume\n", "# tank_1.Volume = 0\n", "\n", "# TODO Compound seperator specs needs to be defined\n", "# Component_Spec = {\n", "#     \"ID\":cnames,\n", "#     \"Component_Sep_Spec\":{\n", "#     \"Spec_Type\":['PercentInletMassFlow','PercentInletMassFlow'],\n", "#     \"Spec_Value\":[100,0],\n", "#     \"Spec_Unit\":['%','%']\n", "# }\n", "# }\n", "# CS_1.Comp_Spec = UnitOperations.ComponentSeparator.ComponentSepSpecs\n", "# CS_1.ComponentSepSpecs"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"text/plain": ["<CalculationMode.HeatRemoved: 0>"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["x = UnitOperations.Cooler.CalculationMode\n", "<PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [], "source": ["# Setting heater calc mode\n", "# HT_1.CalcMode = UnitOperations.Heater.CalculationMode.TemperatureChange\n", "# HT_1.DeltaT = 50"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Requesting a solution"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["# # Setting the properties\n", "# Feed.SetOverallComposition(Array[float]([0.4, 0.4, 0.2]))\n", "# Feed.SetTemperature(300) #K\n", "# Feed.SetPressure(101325)\n", "# Feed.SetMassFlow(1) #kg/s\n", "# # Setting Outlet Pressure\n", "\n", "# Pump.CalcMode = UnitOperations.Pump.CalculationMode.OutletPressure\n", "# Pump.Pout = 301325\n", "\n", "# # set heater outlet temperature\n", "\n", "# Heater.CalcMode = UnitOperations.Heater.CalculationMode.OutletTemperature\n", "# Heater.OutletTemperature = 400 # K\n", "\n", "# # request a calculation\n", "\n", "# Settings.SolverMode = 0\n", "# errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(\n", "    Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "    \"/code/sandbox/DWSim Builder/Process Models/Any Plant Test_V1.dwxmz\",\n", ")\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave, True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}