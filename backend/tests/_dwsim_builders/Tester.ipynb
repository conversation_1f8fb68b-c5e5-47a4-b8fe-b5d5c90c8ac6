{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code/.venv/lib/python3.8/site-packages/clr_loader/mono.py:180: UserWarning: Hosting Mono versions before v6.12 is known to be problematic. If the process crashes shortly after you see this message, try updating Mono to at least v6.12.\n", "  warnings.warn(\n"]}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Basis/Any Plant.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### The below code will give all the object type in the simualtion file"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.359894312779356, 0.640105687220644]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["one = sim.GetObject(\"HT-1\")\n", "one = one.GetAsObject()\n", "list(one.GetOverallMassComposition())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.5, 0.5]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["one = sim.GetObject(\"1\")\n", "one = one.GetAsObject()\n", "MF = one.GetOverallComposition()\n", "list(MF)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["<SimulationObjectClass.PressureChangers: 1>"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["Pump = sim.GetObject('PUMP-1')\n", "Pump.ObjectClass"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["sim.MasterUnitOp"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<ObjectType.Pump: 3>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.Heater: 12>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Vessel: 5>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Cooler: 11>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Compressor: 8>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Expander: 9>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Valve: 14>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.OrificePlate: 37>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.HeatExchanger: 28>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Tank: 4>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.ComponentSeparator: 36>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.NodeOut: 1>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.NodeIn: 0>,\n", " <ObjectType.MaterialStream: 6>]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["all_objects = [obj.Value.get_ObjectType() for obj in sim.get_GraphicObjects()]\n", "all_objects"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### The below code will give all the object type assosciated tags in the simualtion file"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["['PUMP-1',\n", " '1',\n", " '2',\n", " 'E1',\n", " 'HT-1',\n", " 'E2',\n", " '4',\n", " 'V-1',\n", " '6',\n", " '7',\n", " 'CL-1',\n", " 'E3',\n", " '9',\n", " 'C-1',\n", " 'E4',\n", " '10',\n", " 'X-1',\n", " 'E5',\n", " '11',\n", " 'VALVE-1',\n", " '13',\n", " 'OP-1',\n", " '14',\n", " 'HX-1',\n", " '15',\n", " '16',\n", " '17',\n", " 'TANK-1',\n", " '19',\n", " 'CS-1',\n", " 'E6',\n", " '20',\n", " '21',\n", " 'SPL-1',\n", " '22',\n", " '23',\n", " 'MIX-1',\n", " '26']"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["all_objects_tags = [obj.Value.get_Tag() for obj in sim.get_GraphicObjects()]\n", "all_objects_tags"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'GetAsObject'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[16], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m CSCOL_1 \u001b[38;5;241m=\u001b[39m sim\u001b[38;5;241m.\u001b[39mGetObject(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mCSCOL-1\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m----> 2\u001b[0m CSCOL_1 \u001b[38;5;241m=\u001b[39m \u001b[43mCSCOL_1\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mGetAsObject\u001b[49m()\n\u001b[1;32m      3\u001b[0m CSCOL_1\u001b[38;5;241m.\u001b[39mGetPropertyValue(prop\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mAllowed temperature difference\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mAttributeError\u001b[0m: 'NoneType' object has no attribute 'GetAsObject'"]}], "source": ["CSCOL_1 = sim.GetObject('CSCOL-1')\n", "CSCOL_1 = CSCOL_1.GetAsObject()\n", "CSCOL_1.GetPropertyValue(prop='Allowed temperature difference')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["feed = sim.GetObject('feed')\n", "feed = feed.GetAsObject()\n", "feed.GetTemperature()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "#fileNameToSave = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Any Plant Test_V1.dwxmz\")\n", "\n", "#interf.SaveFlowsheet(sim, fileNameToSave, True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}