{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7f859293d9c0>"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "# add compounds\n", "cnames = [\"Methane\",\"Ethane\",\"Propane\",\"N-octane\",\"N-nonane\",\"N-decane\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)\n", "\n", "sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2: mass flow set to 1 kg/s'"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["# Making some material streams\n", "one = sim.AddObject(ObjectType.MaterialStream,50,50,'1')\n", "two = sim.AddObject(ObjectType.MaterialStream,50,50,'2')\n", "three = sim.AddObject(ObjectType.MaterialStream,50,50,'3')\n", "four = sim.AddObject(ObjectType.MaterialStream,50,50,'4')\n", "# Making Air Cooler\n", "AbosroptionColumn = sim.AddObject(ObjectType.AbsorptionColumn,50,50,'ABS-000')\n", "\n", "# Getting All Objects\n", "one = one.GetAsObject()\n", "two = two.GetAsObject()\n", "three = three.GetAsObject()\n", "four = four.GetAsObject()\n", "AbosroptionColumn = AbosroptionColumn.GetAsObject()\n", "\n", "# Connecting the simulation objects.\n", "\n", "\n", "# Material Stream Specs\n", "one.SetOverallComposition(Array[float]([0,0,0,0.33,0.33,0.33]))\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(298.15) #K\n", "one.<PERSON><PERSON><PERSON><PERSON>(101325) #Pa\n", "one.SetMassFlow(100) #kg/s\n", "\n", "two.SetOverallComposition(Array[float]([0.33,0.33,0.33,0,0,0]))\n", "two.<PERSON><PERSON><PERSON><PERSON><PERSON>(298.15) #K\n", "two.<PERSON><PERSON><PERSON><PERSON>(101325) #Pa\n", "two.Set<PERSON><PERSON><PERSON><PERSON>(1) #kg/s"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Specs"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["AbosroptionColumn.NumberOfStages = 12\n", "AbosroptionColumn.SetTopPressure = 101325\n", "AbosroptionColumn.ColumnPressureDrop = 0"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["AbosroptionColumn.ConnectFeed(one,0)\n", "AbosroptionColumn.ConnectFeed(two,0)\n", "AbosroptionColumn.ConnectTopProduct(three)\n", "AbosroptionColumn.ConnectBottoms(four)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"data": {"text/plain": ["nan"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# Getting the parameters of absorption column\n", "AbosroptionColumn.NumberOfStages\n", "AbosroptionColumn.ColumnPressureDrop\n", "AbosroptionColumn.EstimatedDiameter\n", "AbosroptionColumn.EstimatedHeight"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Absorption Column/Absorption Column Test.dwxmz\")\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave,True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}