{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mRunning cells with '.venv (Python 3.8.10)' requires the ipykernel package.\n", "\u001b[1;31mRun the following command to install 'ipykernel' into the Python environment. \n", "\u001b[1;31mCommand: '/code/.venv/bin/python -m pip install ipykernel -U --force-reinstall'"]}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array, Double\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary, List\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"code\\sandbox\\DWSim Builder\\Process Models\\Empty_Template.dwxmz\")\n", "\n", "import source.backend._sharedutils.Utilities as sharedutils\n", "filepath = sharedutils.create_absolute_path_object(\"sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(str(filepath))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# add compounds\n", "cnames = [\"Argon\", \"Methane\", \"Hydrogen\", \"Nitrogen\", \"Ammonia\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding Objects"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [], "source": ["S_01 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-01')\n", "S_03 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-03')\n", "S_04 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-04')\n", "VALV_01 =  sim.AddObject(ObjectType.Valve, 50 , 50 , 'VALV-01')\n", "S_06 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-06')\n", "COOL_01 =  sim.AddObject(ObjectType.Cooler, 50 , 50 , 'COOL-01')\n", "S_07 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-07')\n", "E_02 =  sim.AddObject(ObjectType.EnergyStream, 50 , 50 , 'E-02')\n", "FS_01 =  sim.AddObject(ObjectType.Vessel, 50 , 50 , 'FS-01')\n", "S_09 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-09')\n", "S_08 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-08')\n", "E_03 =  sim.AddObject(ObjectType.EnergyStream, 50 , 50 , 'E-03')\n", "COMP_01 =  sim.AddObject(ObjectType.Compressor, 50 , 50 , 'COMP-01')\n", "S_12 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-12')\n", "E_05 =  sim.AddObject(ObjectType.EnergyStream, 50 , 50 , 'E-05')\n", "COOL_02 =  sim.AddObject(ObjectType.Cooler, 50 , 50 , 'COOL-02')\n", "S_13 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-13')\n", "E_06 =  sim.AddObject(ObjectType.EnergyStream, 50 , 50 , 'E-06')\n", "REC_01 =  sim.AddObject(ObjectType.OT_Recycle, 50 , 50 , 'REC-01')\n", "S_14 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-14')\n", "MIX_01 =  sim.AddObject(ObjectType.NodeIn, 50 , 50 , 'MIX-01')\n", "S_02 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-02')\n", "RC_01 =  sim.AddObject(ObjectType.RCT_<PERSON>, 50 , 50 , 'RC-01')\n", "E_01 =  sim.AddObject(ObjectType.EnergyStream, 50 , 50 , 'E-01')\n", "SPLT_01 =  sim.AddObject(ObjectType.NodeOut, 50 , 50 , 'SPLT-01')\n", "S_15 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-15')\n", "S_19 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-19')\n", "COMP_02 =  sim.AddObject(ObjectType.Compressor, 50 , 50 , 'COMP-02')\n", "S_16 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-16')\n", "E_07 =  sim.AddObject(ObjectType.EnergyStream, 50 , 50 , 'E-07')\n", "HEAT_01 =  sim.AddObject(ObjectType.Heater, 50 , 50 , 'HEAT-01')\n", "S_17 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-17')\n", "E_08 =  sim.AddObject(ObjectType.EnergyStream, 50 , 50 , 'E-08')\n", "REC_02 =  sim.AddObject(ObjectType.OT_Recycle, 50 , 50 , 'REC-02')\n", "S_18 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-18')\n", "MIX_02 =  sim.AddObject(ObjectType.NodeIn, 50 , 50 , 'MIX-02')\n", "S_05 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-05')\n", "VALV_02 =  sim.AddObject(ObjectType.Valve, 50 , 50 , 'VALV-02')\n", "S_20 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-20')\n", "S_21 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-21')\n", "S_22 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-22')\n", "FS_02 =  sim.AddObject(ObjectType.Vessel, 50 , 50 , 'FS-02')\n", "FS_03 =  sim.AddObject(ObjectType.Vessel, 50 , 50 , 'FS-03')\n", "S_11 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-11')\n", "S_10 =  sim.AddObject(ObjectType.MaterialStream, 50 , 50 , 'S-10')\n", "E_04 =  sim.AddObject(ObjectType.EnergyStream, 50 , 50 , 'E-04')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Getting as objects"]}, {"cell_type": "code", "execution_count": 112, "metadata": {}, "outputs": [], "source": ["S_01 = S_01.GetAsObject()\n", "S_03 = S_03.GetAsObject()\n", "S_04 = S_04.GetAsObject()\n", "VALV_01 = VALV_01.GetAsObject()\n", "S_06 = S_06.GetAsObject()\n", "COOL_01 = COOL_01.GetAsObject()\n", "S_07 = S_07.GetAsObject()\n", "E_02 = E_02.GetAsObject()\n", "FS_01 = FS_01.GetAsObject()\n", "S_09 = S_09.GetAsObject()\n", "S_08 = S_08.GetAsObject()\n", "E_03 = E_03.GetAsObject()\n", "COMP_01 = COMP_01.GetAsObject()\n", "S_12 = S_12.GetAsObject()\n", "E_05 = E_05.GetAsObject()\n", "COOL_02 = COOL_02.GetAsObject()\n", "S_13 = S_13.GetAsObject()\n", "E_06 = E_06.GetAsObject()\n", "REC_01 = REC_01.GetAsObject()\n", "S_14 = S_14.GetAsObject()\n", "MIX_01 = MIX_01.GetAsObject()\n", "S_02 = S_02.GetAsObject()\n", "RC_01 = RC_01.GetAsObject()\n", "E_01 = E_01.GetAsObject()\n", "SPLT_01 = SPLT_01.GetAsObject()\n", "S_15 = S_15.GetAsObject()\n", "S_19 = S_19.GetAsObject()\n", "COMP_02 = COMP_02.GetAsObject()\n", "S_16 = S_16.GetAsObject()\n", "E_07 = E_07.GetAsObject()\n", "HEAT_01 = HEAT_01.GetAsObject()\n", "S_17 = S_17.GetAsObject()\n", "E_08 = E_08.GetAsObject()\n", "REC_02 = REC_02.GetAsObject()\n", "S_18 = S_18.GetAsObject()\n", "MIX_02 = MIX_02.GetAsObject()\n", "S_05 = S_05.GetAsObject()\n", "VALV_02 = VALV_02.GetAsObject()\n", "S_20 = S_20.GetAsObject()\n", "S_21 = S_21.GetAsObject()\n", "S_22 = S_22.GetAsObject()\n", "FS_02 = FS_02.GetAsObject()\n", "FS_03 = FS_03.GetAsObject()\n", "S_11 = S_11.GetAsObject()\n", "S_10 = S_10.GetAsObject()\n", "E_04 = E_04.GetAsObject()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Connecting Objects\n", "\n", "The `.ConnectObject` method is based on a simple one on one approach i.e. from and to connections"]}, {"cell_type": "code", "execution_count": 113, "metadata": {}, "outputs": [], "source": ["sim.ConnectObjects(S_01.GraphicObject,MIX_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(MIX_01.GraphicObject,S_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_02.GraphicObject,RC_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(RC_01.GraphicObject,S_03.GraphicObject,-1,-1)\n", "sim.ConnectObjects(RC_01.GraphicObject,S_04.GraphicObject,-1,-1)\n", "sim.ConnectObjects(E_01.GraphicObject,RC_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_04.GraphicObject,MIX_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_18.GraphicObject,MIX_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(MIX_02.GraphicObject,S_05.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_05.GraphicObject,VALV_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(VALV_01.GraphicObject,S_06.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_06.GraphicObject,COOL_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(COOL_01.GraphicObject,S_07.GraphicObject,-1,-1)\n", "sim.ConnectObjects(COOL_01.GraphicObject,E_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_07.GraphicObject,FS_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(FS_01.GraphicObject,S_09.GraphicObject,-1,-1)\n", "sim.ConnectObjects(FS_01.GraphicObject,S_08.GraphicObject,-1,-1)\n", "sim.ConnectObjects(E_03.GraphicObject,FS_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_08.GraphicObject,SPLT_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(SPLT_01.GraphicObject,S_19.GraphicObject,-1,-1)\n", "sim.ConnectObjects(SPLT_01.GraphicObject,S_15.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_19.GraphicObject,VALV_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(VALV_02.GraphicObject,S_20.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_20.GraphicObject,FS_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(FS_02.GraphicObject,S_21.GraphicObject,-1,-1)\n", "sim.ConnectObjects(FS_02.GraphicObject,S_22.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_15.GraphicObject,COMP_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(E_07.GraphicObject,COMP_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(COMP_02.GraphicObject,S_16.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_16.GraphicObject,HEAT_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(E_08.GraphicObject,HEAT_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(HEAT_01.GraphicObject,S_17.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_17.GraphicObject,REC_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(REC_02.GraphicObject,S_18.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_09.GraphicObject,FS_03.GraphicObject,-1,-1)\n", "sim.ConnectObjects(E_04.GraphicObject,FS_03.GraphicObject,-1,-1)\n", "sim.ConnectObjects(FS_03.GraphicObject,S_10.GraphicObject,-1,-1)\n", "sim.ConnectObjects(FS_03.GraphicObject,S_11.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_11.GraphicObject,COMP_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(E_05.GraphicObject,COMP_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(COMP_01.GraphicObject,S_12.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_12.GraphicObject,COOL_02.GraphicObject,-1,-1)\n", "sim.ConnectObjects(COOL_02.GraphicObject,E_06.GraphicObject,-1,-1)\n", "sim.ConnectObjects(COOL_02.GraphicObject,S_13.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_13.GraphicObject,REC_01.GraphicObject,-1,-1)\n", "sim.ConnectObjects(REC_01.GraphicObject,S_14.GraphicObject,-1,-1)\n", "sim.ConnectObjects(S_14.GraphicObject,MIX_01.GraphicObject,-1,-1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Auto Layout"]}, {"cell_type": "code", "execution_count": 114, "metadata": {}, "outputs": [], "source": ["sim.AutoLayout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting up the reaction"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [], "source": ["# add kinetic reaction\n", "\n", "# stoichiometric coefficients\n", "comps = Dictionary[str, float]()\n", "comps.Add(\"Hydrogen\", -3.0);\n", "comps.Add(\"Nitrogen\", -1.0);\n", "comps.Add(\"Ammonia\", 2.0);\n", "\n", "# create conversion reaction object\n", "# https://dwsim.org/api_help/html/M_DWSIM_FlowsheetBase_FlowsheetBase_CreateConversionReaction.htm\n", "# https://github.com/DanWBR/dwsim/blob/windows/DWSIM.FlowsheetBase/FlowsheetBase.vb#L3959\n", "kr1 = sim.CreateConversionReaction(\"Ammonia Production\", \"Ammonia\", \n", "        comps, \"Ammonia\", \"Mixture\", \"95\")\n", "\n", "sim.AddReaction(kr1)\n", "sim.AddReactionToSet(kr1.ID, \"DefaultSet\", True, 0)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Thermodynamics addition"]}, {"cell_type": "code", "execution_count": 116, "metadata": {}, "outputs": [{"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7f7984349240>"]}, "execution_count": 116, "metadata": {}, "output_type": "execute_result"}], "source": ["sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Properties addition on main streams"]}, {"cell_type": "code", "execution_count": 117, "metadata": {}, "outputs": [{"data": {"text/plain": ["'S-01: mass flow set to 12.1873 kg/s'"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["# Setting the properties of the input streams\n", "S_01.SetOverallComposition(Array[float]([0.01, 0.01, 0.74, 0.24, 0.00]))\n", "S_01.SetTemperature(323.15) #K\n", "S_01.SetPressure(9E+06) #Pa\n", "S_01.<PERSON><PERSON><PERSON><PERSON><PERSON>(12.1873) #kg/s"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Getting the calc modes of the equipments"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [], "source": ["RC_01.ReactorOperationMode = RC_01.ReactorOperationMode.OutletTemperature\n", "RC_01.OutletTemperature = 323.15\n", "# Convert Python list to .NET List of strings\n", "Component_ID = ['Argon', 'Methane', 'Hydrogen', 'Nitrogen', 'Ammonia']\n", "net_list = List[str]()\n", "\n", "# Add each item from the Python list to the .NET list\n", "for item in Component_ID:\n", "    net_list.Add(item)\n", "\n", "# Assign the converted list to the ComponentIDs property\n", "RC_01.ComponentIDs = net_list\n", "\n", "# TODO the compound matrix\n", "# Convert the list to a 2D array of Double\n", "element_matrix = [[1, 0, 0, 0, 0],\n", "                  [1, 1, 0, 0, 0],\n", "                  [1, 4, 2, 0, 3],\n", "                  [1, 0, 0, 2, 1]]\n", "\n", "# Convert the Python list to a .NET array of Double\n", "net_array = Array.CreateInstance(Double, len(element_matrix), len(element_matrix[0]))\n", "\n", "for i in range(len(element_matrix)):\n", "    for j in range(len(element_matrix[i])):\n", "        net_array[i, j] = float(element_matrix[i][j])\n", "\n", "# Assuming RC_01 is a .NET object\n", "RC_01.ElementMatrix = net_array\n", "\n", "VALV_01.CalcMode = VALV_01.CalcMode.OutletPressure\n", "VALV_01.OutletPressure = 1.5E+06\n", "\n", "COOL_01.CalcMode = COOL_01.CalculationMode.OutletTemperature\n", "COOL_01.OutletTemperature = 238.15\n", "COOL_01.Eficiencia = 100\n", "\n", "FS_01.OverrideT = True\n", "FS_01.FlashTemperature = 238.15\n", "\n", "SPLT_01.OperationMode = SPLT_01.OperationMode.SplitRatios\n", "SPLT_01.<PERSON>[0] = 0.38\n", "SPLT_01.<PERSON><PERSON>[1] = 0.62\n", "\n", "VALV_02.CalcMode = VALV_02.CalcMode.OutletPressure\n", "VALV_02.OutletPressure = 200000\n", "\n", "COMP_02.CalcMode = COMP_02.CalculationMode.OutletPressure\n", "COMP_02.POut = 9E+06\n", "\n", "HEAT_01.CalcMode = HEAT_01.CalculationMode.OutletTemperature\n", "HEAT_01.OutletTemperature = 323.15\n", "HEAT_01.Eficiencia = 100\n", "\n", "FS_03.OverrideT = True\n", "FS_03.OverrideP = True\n", "FS_03.FlashTemperature = 238.15\n", "FS_03.FlashPressure = 1.5E+06\n", "\n", "COMP_01.CalcMode = COMP_01.CalculationMode.OutletPressure\n", "COMP_01.POut = 9E+06\n", "COMP_01.AdiabaticEfficiency = 100\n", "\n", "COOL_02.CalcMode = COOL_02.CalculationMode.OutletTemperature\n", "COOL_02.OutletTemperature = 323.15\n", "COOL_02.Eficiencia = 100"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Requesting a solution"]}, {"cell_type": "code", "execution_count": 119, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "Settings.SolverMode = 1\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": 120, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Video/235 Synthesis_of_Ammonia_in_Cryogenic_Process/235 Synthesis_of_Ammonia_in_Cryogenic_Process_Automation.dwxmz\")\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave,True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}