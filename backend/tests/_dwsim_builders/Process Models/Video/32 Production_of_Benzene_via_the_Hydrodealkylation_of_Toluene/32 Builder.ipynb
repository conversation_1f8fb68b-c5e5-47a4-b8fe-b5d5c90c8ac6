{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "import source.backend._sharedutils.Utilities as sharedutils\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "# fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/sandbox/DWSim Builder/Process Models/Empty Template.dwxmz\")\n", "relative_path= \"sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\"\n", " \n", "fileNameToLoad = sharedutils.create_absolute_path_object(relative_path)\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(str(fileNameToLoad))"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# add compounds\n", "cnames = [\"Toluene\", \"Hydrogen chloride\", \"Hydrogen\", \"Methane\", \"Benzene\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Adding Objects"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["toluene = sim.AddObject(ObjectType.MaterialStream,50,50,'toluene')\n", "PUMP_001 = sim.AddObject(ObjectType.Pump,100,100, 'PUMP-001')\n", "high_pressure_toluene = sim.AddObject(ObjectType.MaterialStream,150,150, 'high pressure toluene')\n", "pump_utility_supply = sim.AddObject(ObjectType.EnergyStream,200,200, 'pump utility supply')\n", "Hydrogen = sim.AddObject(ObjectType.MaterialStream,250,250, 'Hydrogen')\n", "feed_to_heater = sim.AddObject(ObjectType.MaterialStream,300,300, 'feed to heater')\n", "feed_heater = sim.AddObject(ObjectType.Heater,350,350, 'feed heater')\n", "duty_of_feed_heater = sim.AddObject(ObjectType.EnergyStream,400,400, 'duty of feed heater')\n", "heated_feed = sim.AddObject(ObjectType.MaterialStream,450,450, 'heated feed')\n", "Packed_bed_catalytic_reactor = sim.AddObject(ObjectType.RCT_Conversion,500,500, 'Packed bed catalytic reactor')\n", "Product_stream_ = sim.AddObject(ObjectType.MaterialStream,550,550, 'Product stream ')\n", "MSTR_014 = sim.AddObject(ObjectType.MaterialStream,600,600, 'MSTR-014')\n", "Heat_removal_for_isothermal_operation = sim.AddObject(ObjectType.EnergyStream,650,650, 'Heat removal for isothermal operation')\n", "Product_cooler = sim.AddObject(ObjectType.Cooler,700,700, 'Product cooler')\n", "Cooled_products = sim.AddObject(ObjectType.MaterialStream,750,750, 'Cooled products')\n", "Heat_duty_of_the_cooler = sim.AddObject(ObjectType.EnergyStream,800,800, 'Heat duty of the cooler')\n", "High_pressure_phase_separator = sim.AddObject(ObjectType.Vessel,850,850, 'High pressure phase separator')\n", "Liquid_stream = sim.AddObject(ObjectType.MaterialStream,900,900, 'Liquid stream')\n", "low_pressure_phase_separator = sim.AddObject(ObjectType.Vessel,950,950, 'low pressure phase separator')\n", "liquid_stream = sim.AddObject(ObjectType.MaterialStream,1000,1000, 'liquid stream')\n", "vapor_stream = sim.AddObject(ObjectType.MaterialStream,1050,1050, 'vapor stream')\n", "ESTR_024 = sim.AddObject(ObjectType.EnergyStream,1100,1100, 'ESTR-024')\n", "Distillation_feed_heater = sim.AddObject(ObjectType.Heater,1150,1150, 'Distillation feed heater')\n", "ESTR_027 = sim.AddObject(ObjectType.EnergyStream,1200,1200, 'ESTR-027')\n", "distillate = sim.AddObject(ObjectType.MaterialStream,1250,1250, 'distillate')\n", "Condensor_duty = sim.AddObject(ObjectType.EnergyStream,1300,1300, 'Condensor duty')\n", "reboiler_duty = sim.AddObject(ObjectType.EnergyStream,1350,1350, 'reboiler duty')\n", "distillation_feed = sim.AddObject(ObjectType.MaterialStream,1400,1400, 'distillation feed')\n", "SEP_033 = sim.AddObject(ObjectType.Vessel,1450,1450, 'SEP-033')\n", "vapours_methane_and_hydrogen = sim.AddObject(ObjectType.MaterialStream,1500,1500, 'vapours (methane and hydrogen)')\n", "benzene = sim.AddObject(ObjectType.MaterialStream,1550,1550, 'benzene')\n", "ESTR_036 = sim.AddObject(ObjectType.EnergyStream,1600,1600, 'ESTR-036')\n", "MIX_037 = sim.AddObject(ObjectType.NodeIn,1650,1650, 'MIX-037')\n", "fuel_gas = sim.AddObject(ObjectType.MaterialStream,1700,1700, 'fuel gas')\n", "vapor_stream_from_high_pressure_phase_separator = sim.AddObject(ObjectType.MaterialStream,1750,1750, 'vapor stream from high pressure phase separator')\n", "MIX_045 = sim.AddObject(ObjectType.NodeIn,1800,1800, 'MIX-045')\n", "MIX_004 = sim.AddObject(ObjectType.NodeIn,1850,1850, 'MIX-004')\n", "MIX_044 = sim.AddObject(ObjectType.NodeIn,1900,1900, 'MIX-044')\n", "ESTR_045 = sim.AddObject(ObjectType.EnergyStream,1900,1900, 'ESTR-045')\n", "bottom_product_to_recycle_toluene = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'bottom product to recycle (toluene)')\n", "reactor_input = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'reactor input')\n", "toluene_feed_low_pressure = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'toluene feed (low pressure)')\n", "benzene_toluene_separation_column = sim.AddObject(ObjectType.ShortcutColumn,1900,1900, 'benzene toluene separation column')\n", "SPLT_047 = sim.AddObject(ObjectType.NodeOut,1900,1900, 'SPLT-047')\n", "to_fuel_gas = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'to fuel gas')\n", "recycle_stream = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'recycle stream')\n", "COMP_050 = sim.AddObject(ObjectType.Compressor,1900,1900, 'COMP-050')\n", "compressed_recycle_stream = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'compressed recycle stream')\n", "ESTR_052 = sim.AddObject(ObjectType.EnergyStream,1900,1900, 'ESTR-052')\n", "recycle_splitter = sim.AddObject(ObjectType.NodeOut,1900,1900, 'recycle splitter')\n", "MSTR_054 = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'MSTR-054')\n", "MSTR_055 = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'MSTR-055')\n", "hydrogen_recycle = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'hydrogen recycle')\n", "hydrogen_recyle_to_reactor = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'hydrogen recyle to reactor')\n", "MSTR_057 = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'MSTR-057')\n", "toluene_recycle_block = sim.AddObject(ObjectType.OT_Recycle,1900,1900, 'toluene recycle block')\n", "REC_056 = sim.AddObject(ObjectType.OT_Recycle,1900,1900, 'REC-056')\n", "REC_057 = sim.AddObject(ObjectType.OT_Recycle,1900,1900, 'REC-057')\n", "benzene_cooler = sim.AddObject(ObjectType.Cooler,1900,1900, 'benzene cooler')\n", "cooled_benzene = sim.AddObject(ObjectType.MaterialStream,1900,1900, 'cooled benzene')\n", "ESTR_060 = sim.AddObject(ObjectType.EnergyStream,1900,1900, 'ESTR-060')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Getting as objects"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["toluene = toluene.GetAsObject()\n", "PUMP_001 = PUMP_001.GetAsObject()\n", "high_pressure_toluene = high_pressure_toluene.GetAsObject()\n", "pump_utility_supply = pump_utility_supply.GetAsObject()\n", "Hydrogen = Hydrogen.GetAsObject()\n", "feed_to_heater = feed_to_heater.GetAsObject()\n", "feed_heater = feed_heater.GetAsObject()\n", "duty_of_feed_heater = duty_of_feed_heater.GetAsObject()\n", "heated_feed = heated_feed.GetAsObject()\n", "Packed_bed_catalytic_reactor = Packed_bed_catalytic_reactor.GetAsObject()\n", "Product_stream_ = Product_stream_.GetAsObject()\n", "MSTR_014 = MSTR_014.GetAsObject()\n", "Heat_removal_for_isothermal_operation = Heat_removal_for_isothermal_operation.GetAsObject()\n", "Product_cooler = Product_cooler.GetAsObject()\n", "Cooled_products = Cooled_products.GetAsObject()\n", "Heat_duty_of_the_cooler = Heat_duty_of_the_cooler.GetAsObject()\n", "High_pressure_phase_separator = High_pressure_phase_separator.GetAsObject()\n", "Liquid_stream = Liquid_stream.GetAsObject()\n", "low_pressure_phase_separator = low_pressure_phase_separator.GetAsObject()\n", "liquid_stream = liquid_stream.GetAsObject()\n", "vapor_stream = vapor_stream.GetAsObject()\n", "ESTR_024 = ESTR_024.GetAsObject()\n", "Distillation_feed_heater = Distillation_feed_heater.GetAsObject()\n", "ESTR_027 = ESTR_027.GetAsObject()\n", "distillate = distillate.GetAsObject()\n", "Condensor_duty = Condensor_duty.GetAsObject()\n", "reboiler_duty = reboiler_duty.GetAsObject()\n", "distillation_feed = distillation_feed.GetAsObject()\n", "SEP_033 = SEP_033.GetAsObject()\n", "vapours_methane_and_hydrogen = vapours_methane_and_hydrogen.GetAsObject()\n", "benzene = benzene.GetAsObject()\n", "ESTR_036 = ESTR_036.GetAsObject()\n", "MIX_037 = MIX_037.GetAsObject()\n", "fuel_gas = fuel_gas.GetAsObject()\n", "vapor_stream_from_high_pressure_phase_separator = vapor_stream_from_high_pressure_phase_separator.GetAsObject()\n", "MIX_045 = MIX_045.GetAsObject()\n", "MIX_004 = MIX_004.GetAsObject()\n", "MIX_044 = MIX_044.GetAsObject()\n", "ESTR_045 = ESTR_045.GetAsObject()\n", "bottom_product_to_recycle_toluene = bottom_product_to_recycle_toluene.GetAsObject()\n", "reactor_input = reactor_input.GetAsObject()\n", "toluene_feed_low_pressure = toluene_feed_low_pressure.GetAsObject()\n", "benzene_toluene_separation_column = benzene_toluene_separation_column.GetAsObject()\n", "SPLT_047 = SPLT_047.GetAsObject()\n", "to_fuel_gas = to_fuel_gas.GetAsObject()\n", "recycle_stream = recycle_stream.GetAsObject()\n", "COMP_050 = COMP_050.GetAsObject()\n", "compressed_recycle_stream = compressed_recycle_stream.GetAsObject()\n", "ESTR_052 = ESTR_052.GetAsObject()\n", "recycle_splitter = recycle_splitter.GetAsObject()\n", "MSTR_054 = MSTR_054.GetAsObject()\n", "MSTR_055 = MSTR_055.GetAsObject()\n", "hydrogen_recycle = hydrogen_recycle.GetAsObject()\n", "hydrogen_recyle_to_reactor = hydrogen_recyle_to_reactor.GetAsObject()\n", "MSTR_057 = MSTR_057.GetAsObject()\n", "toluene_recycle_block = toluene_recycle_block.GetAsObject()\n", "REC_056 = REC_056.GetAsObject()\n", "REC_057 = REC_057.GetAsObject()\n", "benzene_cooler = benzene_cooler.GetAsObject()\n", "cooled_benzene = cooled_benzene.GetAsObject()\n", "ESTR_060 = ESTR_060.GetAsObject()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Connecting Objects\n", "\n", "The `.ConnectObject` method is based on a simple one on one approach i.e. from and to connections"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["sim.ConnectObjects(toluene.GraphicObject,MIX_044.GraphicObject,-1,-1)\n", "sim.ConnectObjects(MIX_044.GraphicObject,toluene_feed_low_pressure.GraphicObject,-1,-1)\n", "sim.ConnectObjects(toluene_feed_low_pressure.GraphicObject,PUMP_001.GraphicObject,-1,-1)\n", "sim.ConnectObjects(PUMP_001.GraphicObject,high_pressure_toluene.GraphicObject,-1,-1)\n", "sim.ConnectObjects(pump_utility_supply.GraphicObject,PUMP_001.GraphicObject,-1,-1)\n", "sim.ConnectObjects(high_pressure_toluene.GraphicObject,MIX_004.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Hydrogen.GraphicObject,MIX_004.GraphicObject,-1,-1)\n", "sim.ConnectObjects(hydrogen_recycle.GraphicObject,MIX_004.GraphicObject,-1,-1)\n", "sim.ConnectObjects(MIX_004.GraphicObject,feed_to_heater.GraphicObject,-1,-1)\n", "sim.ConnectObjects(feed_to_heater.GraphicObject,feed_heater.GraphicObject,-1,-1)\n", "sim.ConnectObjects(duty_of_feed_heater.GraphicObject,feed_heater.GraphicObject,-1,-1)\n", "sim.ConnectObjects(feed_heater.GraphicObject,heated_feed.GraphicObject,-1,-1)\n", "sim.ConnectObjects(hydrogen_recyle_to_reactor.GraphicObject,MIX_045.GraphicObject,-1,-1)\n", "sim.ConnectObjects(heated_feed.GraphicObject,MIX_045.GraphicObject,-1,-1)\n", "sim.ConnectObjects(MIX_045.GraphicObject,reactor_input.GraphicObject,-1,-1)\n", "sim.ConnectObjects(reactor_input.GraphicObject,Packed_bed_catalytic_reactor.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Packed_bed_catalytic_reactor.GraphicObject,Product_stream_.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Packed_bed_catalytic_reactor.GraphicObject,MSTR_014.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Heat_removal_for_isothermal_operation.GraphicObject,Packed_bed_catalytic_reactor.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Product_stream_.GraphicObject,Product_cooler.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Product_cooler.GraphicObject,Cooled_products.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Product_cooler.GraphicObject,Heat_duty_of_the_cooler.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Cooled_products.GraphicObject,High_pressure_phase_separator.GraphicObject,-1,-1)\n", "sim.ConnectObjects(ESTR_045.GraphicObject,High_pressure_phase_separator.GraphicObject,-1,-1)\n", "sim.ConnectObjects(High_pressure_phase_separator.GraphicObject,vapor_stream_from_high_pressure_phase_separator.GraphicObject,-1,-1)\n", "sim.ConnectObjects(High_pressure_phase_separator.GraphicObject,Liquid_stream.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Liquid_stream.GraphicObject,low_pressure_phase_separator.GraphicObject,-1,-1)\n", "sim.ConnectObjects(ESTR_024.GraphicObject,low_pressure_phase_separator.GraphicObject,-1,-1)\n", "sim.ConnectObjects(low_pressure_phase_separator.GraphicObject,vapor_stream.GraphicObject,-1,-1)\n", "sim.ConnectObjects(low_pressure_phase_separator.GraphicObject,liquid_stream.GraphicObject,-1,-1)\n", "sim.ConnectObjects(liquid_stream.GraphicObject,Distillation_feed_heater.GraphicObject,-1,-1)\n", "sim.ConnectObjects(ESTR_027.GraphicObject,Distillation_feed_heater.GraphicObject,-1,-1)\n", "sim.ConnectObjects(Distillation_feed_heater.GraphicObject,distillation_feed.GraphicObject,-1,-1)\n", "sim.ConnectObjects(distillation_feed.GraphicObject,benzene_toluene_separation_column.GraphicObject,-1,-1)\n", "sim.ConnectObjects(benzene_toluene_separation_column.GraphicObject,Condensor_duty.GraphicObject,-1,-1)\n", "sim.ConnectObjects(reboiler_duty.GraphicObject,benzene_toluene_separation_column.GraphicObject,-1,-1)\n", "sim.ConnectObjects(benzene_toluene_separation_column.GraphicObject,bottom_product_to_recycle_toluene.GraphicObject,-1,-1)\n", "sim.ConnectObjects(benzene_toluene_separation_column.GraphicObject,distillate.GraphicObject,-1,-1)\n", "sim.ConnectObjects(distillate.GraphicObject,SEP_033.GraphicObject,-1,-1)\n", "sim.ConnectObjects(ESTR_036.GraphicObject,SEP_033.GraphicObject,-1,-1)\n", "sim.ConnectObjects(SEP_033.GraphicObject,benzene.GraphicObject,-1,-1)\n", "sim.ConnectObjects(SEP_033.GraphicObject,vapours_methane_and_hydrogen.GraphicObject,-1,-1)\n", "sim.ConnectObjects(benzene.GraphicObject,benzene_cooler.GraphicObject,-1,-1)\n", "sim.ConnectObjects(benzene_cooler.GraphicObject,cooled_benzene.GraphicObject,-1,-1)\n", "sim.ConnectObjects(benzene_cooler.GraphicObject,ESTR_060.GraphicObject,-1,-1)\n", "sim.ConnectObjects(vapours_methane_and_hydrogen.GraphicObject,MIX_037.GraphicObject,-1,-1)\n", "sim.ConnectObjects(vapor_stream_from_high_pressure_phase_separator.GraphicObject,SPLT_047.GraphicObject,-1,-1)\n", "sim.ConnectObjects(SPLT_047.GraphicObject,to_fuel_gas.GraphicObject,-1,-1)\n", "sim.ConnectObjects(to_fuel_gas.GraphicObject,MIX_037.GraphicObject,-1,-1)\n", "sim.ConnectObjects(vapor_stream.GraphicObject,MIX_037.GraphicObject,-1,-1)\n", "sim.ConnectObjects(MIX_037.GraphicObject,fuel_gas.GraphicObject,-1,-1)\n", "sim.ConnectObjects(SPLT_047.GraphicObject,recycle_stream.GraphicObject,-1,-1)\n", "sim.ConnectObjects(recycle_stream.GraphicObject,COMP_050.GraphicObject,-1,-1)\n", "sim.ConnectObjects(COMP_050.GraphicObject,compressed_recycle_stream.GraphicObject,-1,-1)\n", "sim.ConnectObjects(ESTR_052.GraphicObject,COMP_050.GraphicObject,-1,-1)\n", "sim.ConnectObjects(compressed_recycle_stream.GraphicObject,recycle_splitter.GraphicObject,-1,-1)\n", "sim.ConnectObjects(recycle_splitter.GraphicObject,MSTR_054.GraphicObject,-1,-1)\n", "sim.ConnectObjects(recycle_splitter.GraphicObject,MSTR_055.GraphicObject,-1,-1)\n", "sim.ConnectObjects(MSTR_054.GraphicObject,REC_057.GraphicObject,-1,-1)\n", "sim.ConnectObjects(MSTR_055.GraphicObject,REC_056.GraphicObject,-1,-1)\n", "sim.ConnectObjects(REC_057.GraphicObject,hydrogen_recyle_to_reactor.GraphicObject,-1,-1)\n", "sim.ConnectObjects(REC_056.GraphicObject,hydrogen_recycle.GraphicObject,-1,-1)\n", "sim.ConnectObjects(bottom_product_to_recycle_toluene.GraphicObject,toluene_recycle_block.GraphicObject,-1,-1)\n", "sim.ConnectObjects(toluene_recycle_block.GraphicObject,MSTR_057.GraphicObject,-1,-1)\n", "sim.ConnectObjects(MSTR_057.GraphicObject,MIX_044.GraphicObject,-1,-1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Auto Layout"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["sim.AutoLayout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting up the reaction"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# add kinetic reaction\n", "\n", "# stoichiometric coefficients\n", "comps = Dictionary[str, float]()\n", "comps.Add(\"Toluene\", -1.0);\n", "comps.Add(\"Hydrogen\", -1.0);\n", "comps.Add(\"Methane\", 1.0);\n", "comps.Add(\"Benzene\", 1.0);\n", "\n", "# create conversion reaction object\n", "# https://dwsim.org/api_help/html/M_DWSIM_FlowsheetBase_FlowsheetBase_CreateConversionReaction.htm\n", "# https://github.com/DanWBR/dwsim/blob/windows/DWSIM.FlowsheetBase/FlowsheetBase.vb#L3959\n", "kr1 = sim.CreateConversionReaction(\"Dealkylation of toluene\", \"Dealkylation\", \n", "        comps, \"Toluene\", \"Vapor\", \"75\")\n", "\n", "sim.AddReaction(kr1)\n", "sim.AddReactionToSet(kr1.ID, \"DefaultSet\", True, 0)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Thermodynamics addition"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7fde5ade6200>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Properties addition on main streams"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Hydrogen: mass flow set to 0.22 kg/s'"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# Setting the properties of the input streams\n", "toluene.SetOverallComposition(Array[float]([0.996, 0.0, 0.0, 0.0, 0.004]))\n", "toluene.SetTemperature(298) #K\n", "toluene.SetPressure(202650) #Pa\n", "toluene.SetMassFlow(2.77) #kg/s\n", "\n", "# Setting the properties of the input stream\n", "Hydrogen.SetOverallComposition(Array[float]([0.0, 0.0, 0.95, 0.05, 0.0]))\n", "Hydrogen.SetTemperature(298) #K\n", "Hydrogen.SetPressure(2583787.5) #Pa\n", "Hydrogen.SetMassFlow(0.22) #kg/s"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Getting the calc modes of the equipments"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["# Setting pump calc mode\n", "PUMP_001.CalcMode = UnitOperations.Pump.CalculationMode.Delta_P\n", "PUMP_001.DeltaP = 1519875\n", "\n", "# Setting heater calc mode\n", "feed_heater.CalcMode = UnitOperations.Heater.CalculationMode.TemperatureChange\n", "feed_heater.DeltaT = 811.15\n", "\n", "# Setting reactor mode of operation\n", "Packed_bed_catalytic_reactor.ReactorOperationMode =  Packed_bed_catalytic_reactor.ReactorOperationMode.Isothermic\n", "\n", "# Setting the cooler calc mode\n", "Product_cooler.CalcMode = UnitOperations.Cooler.CalculationMode.OutletTemperature\n", "Product_cooler.OutletTemperature = 311\n", "\n", "# Setting the specs of seperator\n", "High_pressure_phase_separator.OverrideT = True\n", "High_pressure_phase_separator.FlashTemperature = 311.15\n", "High_pressure_phase_separator.OverrideP = True\n", "High_pressure_phase_separator.FlashPressure = 2400000.0\n", "\n", "low_pressure_phase_separator.OverrideT = True\n", "low_pressure_phase_separator.FlashTemperature = 311.0\n", "low_pressure_phase_separator.OverrideP = True\n", "low_pressure_phase_separator.FlashPressure = 300000.0\n", "\n", "# Setting heater calc mode\n", "Distillation_feed_heater.CalcMode = UnitOperations.Heater.CalculationMode.OutletTemperature\n", "Distillation_feed_heater.OutletTemperature = 363.0\n", "\n", "# Setting shortcut column specs\n", "benzene_toluene_separation_column.m_lightkey = \"Benzene\"\n", "benzene_toluene_separation_column.m_heavykey = \"Toluene\"\n", "benzene_toluene_separation_column.m_lightkeymolarfrac = 0.030800\n", "benzene_toluene_separation_column.m_heavykeymolarfrac = 0.000100\n", "benzene_toluene_separation_column.m_refluxratio = 1.5\n", "benzene_toluene_separation_column.CondenserType = \"Total\"\n", "benzene_toluene_separation_column.m_condenserpressure = 303975\n", "benzene_toluene_separation_column.m_boilerpressure = 1114575\n", "\n", "# Setting the specs of seperator\n", "SEP_033.OverrideT = True\n", "SEP_033.FlashTemperature = 385.15\n", "SEP_033.OverrideP = True\n", "SEP_033.FlashPressure = 253312.5\n", "\n", "# Setting cooler specs\n", "benzene_cooler.CalcMode = UnitOperations.Cooler.CalculationMode.OutletTemperature\n", "benzene_cooler.OutletTemperature = 311.15\n", "\n", "# Setting compressor spec\n", "COMP_050.CalcMode = UnitOperations.Compressor.CalculationMode.Delta_P\n", "COMP_050.DeltaP = 151987.5\n", "\n", "# Setting splitters\n", "recycle_splitter.OperationMode = recycle_splitter.OperationMode.SplitRatios\n", "recycle_splitter.Ratios[0] = 0.05\n", "recycle_splitter.Ratios[1] = 0.95\n", "\n", "SPLT_047.OperationMOde = SPLT_047.OperationMode.SplitRatios\n", "SPLT_047.<PERSON><PERSON>[0] = 0.27\n", "SPLT_047.<PERSON><PERSON>[1] = 0.73\n", "\n", "# Setting the mixer pressure calculation parameter\n", "MIX_044.PressureCalculation = MIX_044.PressureCalculation.Maximum\n", "MIX_004.PressureCalculation = MIX_004.PressureCalculation.Minimum\n", "MIX_037.PressureCalculation = MIX_037.PressureCalculation.Minimum\n", "MIX_045.PressureCalculation = MIX_045.PressureCalculation.Minimum\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'System.Collections.ArrayList'> System.Collections.ArrayList\n"]}], "source": ["ratios = recycle_splitter.Ratios\n", "print(type(ratios), ratios)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'float'> 0.0\n", "<class 'float'> 1.0\n"]}], "source": ["recycle_splitter.StreamFlowSpec = 0.0\n", "stream_flow_spec = recycle_splitter.StreamFlowSpec \n", "print(type(stream_flow_spec), stream_flow_spec)\n", "\n", "recycle_splitter.StreamFlowSpec = 1.0\n", "stream_flow_spec = recycle_splitter.StreamFlowSpec \n", "print(type(stream_flow_spec), stream_flow_spec)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Requesting a solution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Video/32 Production_of_Benzene_via_the_Hydrodealkylation_of_Toluene/32 Production_of_Benzene_via_the_Hydrodealkylation_of_Toluene_Autoamtion.dwxmz\")\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave,True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}