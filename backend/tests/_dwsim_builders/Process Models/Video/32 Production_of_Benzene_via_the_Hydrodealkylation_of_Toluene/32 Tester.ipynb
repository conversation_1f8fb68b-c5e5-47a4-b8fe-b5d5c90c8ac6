{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code/.venv/lib/python3.8/site-packages/clr_loader/mono.py:180: UserWarning: Hosting Mono versions before v6.12 is known to be problematic. If the process crashes shortly after you see this message, try updating Mono to at least v6.12.\n", "  warnings.warn(\n"]}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Video/32 Production_of_Benzene_via_the_Hydrodealkylation_of_Toluene/32 Production_of_Benzene_via_the_Hydrodealkylation_of_Toluene.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### The below code will give all the object type in the simualtion file"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<ObjectType.MaterialStream: 6>,\n", " <ObjectType.Pump: 3>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Heater: 12>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.RCT_Conversion: 23>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.Cooler: 11>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.Vessel: 5>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Vessel: 5>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.Heater: 12>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Vessel: 5>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.NodeIn: 0>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.NodeIn: 0>,\n", " <ObjectType.NodeIn: 0>,\n", " <ObjectType.NodeIn: 0>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.ShortcutColumn: 29>,\n", " <ObjectType.NodeOut: 1>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.Compressor: 8>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.EnergyStream: 7>,\n", " <ObjectType.NodeOut: 1>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.OT_Recycle: 22>,\n", " <ObjectType.OT_Recycle: 22>,\n", " <ObjectType.OT_Recycle: 22>,\n", " <ObjectType.Cooler: 11>,\n", " <ObjectType.MaterialStream: 6>,\n", " <ObjectType.EnergyStream: 7>]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["all_objects = [obj.Value.get_ObjectType() for obj in sim.get_GraphicObjects()]\n", "all_objects"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### The below code will give all the object type assosciated tags in the simualtion file"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["['toluene',\n", " 'PUMP-001',\n", " 'high pressure toluene',\n", " 'pump utility supply',\n", " 'Hydrogen',\n", " 'feed to heater',\n", " 'feed heater',\n", " 'duty of feed heater',\n", " 'heated feed',\n", " 'Packed bed catalytic reactor',\n", " 'Product stream ',\n", " 'MSTR-014',\n", " 'Heat removal for isothermal operation',\n", " 'Product cooler',\n", " 'Cooled products',\n", " 'Heat duty of the cooler',\n", " 'High pressure phase separator',\n", " 'Liquid stream',\n", " 'low pressure phase separator',\n", " 'liquid stream',\n", " 'vapor stream',\n", " 'ESTR-024',\n", " 'Distillation feed heater',\n", " 'ESTR-027',\n", " 'distillate',\n", " 'Condensor duty',\n", " 'reboiler duty',\n", " 'distillation feed',\n", " 'SEP-033',\n", " 'vapours (methane and hydrogen)',\n", " 'benzene',\n", " 'ESTR-036',\n", " 'MIX-037',\n", " 'fuel gas',\n", " 'vapor stream from high pressure phase separator',\n", " 'MIX-045',\n", " 'MIX-004',\n", " 'MIX-044',\n", " 'ESTR-045',\n", " 'bottom product to recycle (toluene)',\n", " 'reactor input',\n", " 'toluene feed (low pressure)',\n", " 'benzene toluene separation column',\n", " 'SPLT-047',\n", " 'to fuel gas',\n", " 'recycle stream',\n", " 'COMP-050',\n", " 'compressed recycle stream',\n", " 'ESTR-052',\n", " 'recycle splitter',\n", " 'MSTR-054',\n", " 'MSTR-055',\n", " 'hydrogen recycle',\n", " 'hydrogen recyle to reactor',\n", " 'MSTR-057',\n", " 'toluene recycle block',\n", " 'REC-056',\n", " 'REC-057',\n", " 'benzene cooler',\n", " 'cooled benzene',\n", " 'ESTR-060']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["all_objects_tags = [obj.Value.get_Tag() for obj in sim.get_GraphicObjects()]\n", "all_objects_tags"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["<System.Collections.Generic.Dictionary[String,Double] object at 0x7f9ea4078840>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["RC_001 = sim.GetObject('Packed bed catalytic reactor')\n", "RC_001 = RC_001.GetAsObject()\n", "RC_001.get_Conversions()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "# fileNameToSave = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Any Plant Test_V1.dwxmz\")\n", "\n", "# interf.SaveFlowsheet(sim, fileNameToSave, True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}