{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code/.venv/lib/python3.8/site-packages/clr_loader/mono.py:180: UserWarning: Hosting Mono versions before v6.12 is known to be problematic. If the process crashes shortly after you see this message, try updating Mono to at least v6.12.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7f4a339caa40>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "# add compounds\n", "cnames = [\"Water\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)\n", "\n", "sim.CreateAndAddPropertyPackage(\"Steam Tables (IAPWS-IF97)\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'2: mass flow set to 1 kg/s'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Making some material streams\n", "one = sim.AddObject(ObjectType.MaterialStream,50,50,'1')\n", "two = sim.AddObject(ObjectType.MaterialStream,50,50,'2')\n", "three = sim.AddObject(ObjectType.MaterialStream,50,50,'3')\n", "four = sim.AddObject(ObjectType.MaterialStream,50,50,'4')\n", "# Making HEX\n", "HeatExchanger = sim.AddObject(ObjectType.HeatExchanger,50,50,'HX-1')\n", "\n", "# Getting All Objects\n", "one = one.GetAsObject()\n", "two = two.GetAsObject()\n", "three = three.GetAsObject()\n", "four = four.GetAsObject()\n", "HeatExchanger = HeatExchanger.GetAsObject()\n", "\n", "# Connecting the simulation objects.\n", "sim.ConnectObjects(one.GraphicObject,HeatExchanger.GraphicObject, -1, -1)\n", "sim.ConnectObjects(HeatExchanger.GraphicObject,three.GraphicObject, -1, -1)\n", "sim.ConnectObjects(two.GraphicObject,HeatExchanger.GraphicObject, -1, -1)\n", "sim.ConnectObjects(HeatExchanger.GraphicObject,four.GraphicObject, -1, -1)\n", "\n", "# Material Stream Specs\n", "one.SetOverallComposition(Array[float]([1.0]))\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(298.15) #K\n", "one.<PERSON><PERSON><PERSON><PERSON>(101325) #Pa\n", "one.Set<PERSON><PERSON><PERSON><PERSON>(1) #kg/s\n", "\n", "two.SetOverallComposition(Array[float]([1.0]))\n", "two.<PERSON><PERSON><PERSON><PERSON><PERSON>(353.15) #K\n", "two.<PERSON><PERSON><PERSON><PERSON>(101325) #Pa\n", "two.Set<PERSON><PERSON><PERSON><PERSON>(1) #kg/s"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Defining the calc mode of the heat exchanger\n", "# https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_HeatExchangerCalcMode.htm\n", "HeatExchanger.CalculationMode = HeatExchanger.CalcMode.ShellandTube_Rating\n", "\n", "# Setting/changing some of the settings of heat exchanger rating mode\n", "# https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Auxiliary_HeatExchanger_STHXProperties.htm\n", "# Fc = Pipe Wall Resistance\n", "# Ff = Fouling Resistance\n", "# Fs = Shell-side Resistance\n", "# Ft = Tube-side Resistance\n", "# OverallFoulingFactor = This one is similar to Ff i.e. fouling resistance\n", "# ReS = Shell-side Reynolds Number\n", "# ReT = Tube-side Reynolds Number\n", "# Shell_BaffleCut = Baffle Cut (% diameter)\n", "# Shell_BaffleOrientation = Baffle Orientation \n", "# Shell_BaffleSpacing = Baffle Spacing\n", "# Shell_BaffleType = Baffle Type\n", "# Shell_Di = Internal Diameter\n", "# Shell_Fluid = This one is similar to the Tube_Fluid if one is defined then is fine not to define the other one\n", "# Shell_Fouling = Fouling Factor\n", "# Shell_NumberOfPasses = Shell Passes\n", "# Shell_NumberOfShellsInSeries = Shell in Series\n", "# Shell_Roughness = \n", "# Tube_De = External Diameter\n", "# Tube_Di = Internal Diameter\n", "# Tube_Fluid = Fluid in Tubes 0 = Cold, 1 = Hot\n", "# Tube_Fouling = Fouling Factor\n", "# Tube_Layout = Tube Layout, 0 = <PERSON>gle, 1 = Rotated Triangle, 2 = Square, 3 = Rotated Square\n", "# Tube_Length = Length\n", "# Tube_NumberPerShell = Tubes per Shell\n", "# Tube_PassesPerShell = Passes per Shell\n", "# Tube_Pitch = Tube Spacing\n", "# Tube_Roughness = Roughness\n", "# Tu<PERSON>_Scaling_FricCorrFactor = #TODO Is it needed or not? \n", "# Tube_ThermalConductivity = Thermal Conductivity\n", "\n", "HeatExchanger.STProperties.Tube_Pitch = 70 # this is in mm\n", "HeatExchanger.STProperties.Tube_Roughness = 0.044 # this is in mm\n", "\n", "# REMOVED FROM ATLAS\n", "HeatExchanger.STProperties.Shell_BaffleOrientation = 1 # For some reason this is always 1 # LO - removed from class\n", "HeatExchanger.STProperties.Shell_BaffleType = 0 # For some reason this is always 0 # LO - removed from class\n", "\n", "# IMPLEMENTED\n", "HeatExchanger.STProperties.Shell_BaffleCut = 20.5 # LO - added\n", "HeatExchanger.STProperties.Shell_Di = 500 # this is in mm # LO - added\n", "HeatExchanger.STProperties.Shell_NumberOfPasses = 2\n", "HeatExchanger.STProperties.Shell_NumberOfShellsInSeries = 1\n", "HeatExchanger.STProperties.Tube_De = 60 # this is in mm\n", "HeatExchanger.STProperties.Tube_Di = 50 # this is in mm\n", "HeatExchanger.STProperties.Tube_Length = 5 # this in in m\n", "HeatExchanger.STProperties.Tube_NumberPerShell = 50\n", "HeatExchanger.STProperties.Tube_PassesPerShell = 2\n", "HeatExchanger.STProperties.Tube_ThermalConductivity = 71 # this is in W/[m.K]\n", "\n", "HeatExchanger.STProperties.Tube_Fouling = 0.02 # this is in K.m2/W\n", "HeatExchanger.STProperties.Shell_Fouling = 0.02 # this is in K.m2/W\n", "HeatExchanger.STProperties.Tube_Layout = 1\n", "# TODO - TO CHECK - These are not captured as param enums\n", "\n", "# TODO - TO CHECK, these are in param enums but not here\n", "# atlas.ParameterEnum.FluidInTubes: _AttributeAccessPath(\"\"),\n", "# HeatExchanger.STProperties.Tube_Fluid = 1 # 1 is hot, 0 is cold\n", "# Layout\n", "sim.AutoLayout()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Tube Layouts"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.UnitOperations.UnitOperations.HeatExchanger'> HX-1\n"]}, {"data": {"text/plain": ["20.5"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Get Unit Obj\n", "print(type(HeatExchanger), HeatExchanger)\n", "\n", "\n", "ST = getattr(HeatExchanger, \"STProperties\")\n", "Shell = getattr(ST, \"Shell_BaffleCut\")\n", "\n", "getattr(HeatExchanger.STProperties, \"Shell_BaffleCut\")\n", "\n", "# Get Sim Obj"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "TubeLayout_Triangle", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[0;32m/tmp/ipykernel_42666/2291917391.py\u001b[0m in \u001b[0;36m<cell line: 4>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     26\u001b[0m     \u001b[0matlas\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mParameterEnum\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mPassesPerShell\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;34m\"Tube_PassesPerShell\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     27\u001b[0m     \u001b[0matlas\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mParameterEnum\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mTubesPerShell\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;34m\"Tube_NumberPerShell\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 28\u001b[0;31m     \u001b[0matlas\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mParameterEnum\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mTubeLayout_Triangle\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;34m\"\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     29\u001b[0m     \u001b[0matlas\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mParameterEnum\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mTubeLayout_Rotated_Triangle\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;34m\"\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     30\u001b[0m     \u001b[0matlas\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mParameterEnum\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mTubeLayout_Square\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0;34m\"\"\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/.pyenv/versions/3.8.18/lib/python3.8/enum.py\u001b[0m in \u001b[0;36m__getattr__\u001b[0;34m(cls, name)\u001b[0m\n\u001b[1;32m    382\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mcls\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_member_map_\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    383\u001b[0m         \u001b[0;32mexcept\u001b[0m \u001b[0mKeyError\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 384\u001b[0;31m             \u001b[0;32mraise\u001b[0m \u001b[0mAttributeError\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mname\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    385\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    386\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0m__getitem__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcls\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mname\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mAttributeError\u001b[0m: TubeLayout_Triangle"]}], "source": ["\n", "from typing import Any, Callable, Dict, List, Optional, Tuple, Type, Union\n", "import source.backend.atlas_domainmodel.aggregates.base_aggregate as atlas\n", "\n", "PARAMETER_KEYWORDS: Dict[atlas.ParameterEnum, str] = {\n", "    atlas.ParameterEnum.ColdFluidPressureDrop: ([\"\", \"\"],\"ColdSidePressureDrop\"),  # https://dwsim.org/api_help/html/Properties_T_DWSIM_UnitOperations_UnitOperations_HeatExchanger.htm\n", "    atlas.ParameterEnum.HotFluidPressureDrop: \"HotSidePressureDrop\",\n", "    atlas.ParameterEnum.ColdFluidOutletTemperature: \"ColdSideOutletTemperature\",\n", "    atlas.ParameterEnum.HotFluidOutletTemperature: \"HotSideOutletTemperature\",\n", "    atlas.ParameterEnum.HeatExchangeArea: \"Area\",\n", "    atlas.ParameterEnum.HeatLoss: \"HeatLoss\",\n", "    atlas.ParameterEnum.GlobalHeatTransferCoefficient: \"OverallCoefficient\",\n", "    atlas.ParameterEnum.HeatExchange: \"Q\",\n", "    atlas.ParameterEnum.MinimumTemperatureDifference: \"MITA\",\n", "    atlas.ParameterEnum.HeatTransferEfficiency: \"ThermalEfficiency\",\n", "    atlas.ParameterEnum.OutletVaporFractionFluid1: \"OutletVaporFraction1\",\n", "    atlas.ParameterEnum.OutletVaporFractionFluid2: \"OutletVaporFraction2\",\n", "    atlas.ParameterEnum.ShellInSeries: \"Shell_NumberOfShellsInSeries\",\n", "    atlas.ParameterEnum.ShellPasses: \"Shell_NumberOfPasses\",\n", "    atlas.ParameterEnum.InternalDiameterOfShell: \"Shell_Di\",\n", "    atlas.ParameterEnum.BaffleCut: \"Shell_BaffleCut\",\n", "    atlas.ParameterEnum.BaffleSpacing: \"Shell_BaffleSpacing\",\n", "    atlas.ParameterEnum.InternalDiameterOfTube: \"Tube_Di\",\n", "    atlas.ParameterEnum.ExternalDiameterOfTube: \"Tube_De\",\n", "    atlas.ParameterEnum.TubeLength: \"Tube_Length\",\n", "    atlas.ParameterEnum.ThermalConductivityOfTube: \"Tube_ThermalConductivity\",\n", "    atlas.ParameterEnum.PassesPerShell: \"Tube_PassesPerShell\",\n", "    atlas.ParameterEnum.TubesPerShell: \"Tube_NumberPerShell\",\n", "    atlas.ParameterEnum.TubeLayout_Triangle: \"\",\n", "    atlas.ParameterEnum.TubeLayout_Rotated_Triangle: \"\",\n", "    atlas.ParameterEnum.TubeLayout_Square: \"\",\n", "    atlas.ParameterEnum.TubeLayout_Rotated_Square: \"\",\n", "    atlas.ParameterEnum.FluidInTubes: \"\",\n", "}\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/HEX/HEX Test.dwxmz\")\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave,True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}