{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Current working directory: /usr/local/lib/dwsim\n", "File not found at: /code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\n", "File found at: /code/backend/tests/_dwsim_builders/Process Models/Empty_Template.dwxmz\n"]}], "source": ["import os\n", "\n", "# Check current working directory\n", "print(f\"Current working directory: {os.getcwd()}\")\n", "\n", "# Option 1: Use the original path but check if it exists\n", "original_path = \"/code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\"\n", "if os.path.exists(original_path):\n", "    fileNameToLoad = original_path\n", "    print(f\"File exists at: {original_path}\")\n", "else:\n", "    print(f\"File not found at: {original_path}\")\n", "\n", "    # Option 2: Try looking in the same directory as the notebook\n", "    notebook_dir = \"/code/backend/tests/_dwsim_builders/Process Models/ShortCut Column\"\n", "    alt_path = os.path.join(notebook_dir, \"Empty_Template.dwxmz\")\n", "    if os.path.exists(alt_path):\n", "        fileNameToLoad = alt_path\n", "        print(f\"File found at: {alt_path}\")\n", "    else:\n", "        # Option 3: Look in parent directories\n", "        parent_dir = os.path.dirname(notebook_dir)\n", "        alt_path = os.path.join(parent_dir, \"Empty_Template.dwxmz\")\n", "        if os.path.exists(alt_path):\n", "            fileNameToLoad = alt_path\n", "            print(f\"File found at: {alt_path}\")\n", "        else:\n", "            # Option 4: Traverse up to _dwsim_builders directory\n", "            dwsim_builders_dir = \"/code/backend/tests/_dwsim_builders\"\n", "            alt_path = os.path.join(dwsim_builders_dir, \"Empty_Template.dwxmz\")\n", "            if os.path.exists(alt_path):\n", "                fileNameToLoad = alt_path\n", "                print(f\"File found at: {alt_path}\")\n", "            else:\n", "                # Let's just search for any .dwxmz files to help troubleshoot\n", "                print(\"Searching for any .dwxmz files in the _dwsim_builders directory...\")\n", "                for root, dirs, files in os.walk(dwsim_builders_dir):\n", "                    for file in files:\n", "                        if file.endswith(\".dwxmz\"):\n", "                            print(f\"Found template file: {os.path.join(root, file)}\")\n", "                \n", "                raise FileNotFoundError(\"Could not find Empty_Template.dwxmz in any expected locations\")\n", "\n", "# Use the string path directly with LoadFlowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7faa59922440>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# add compounds\n", "cnames = [\"Benzene\", \"Toluene\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)\n", "\n", "sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Making some material streams\n", "one = sim.AddObject(ObjectType.MaterialStream,50,50,'1')\n", "two = sim.AddObject(ObjectType.MaterialStream,50,50,'2')\n", "three = sim.AddObject(ObjectType.MaterialStream,50,50,'3')\n", "# Making some energy streams\n", "# E1 = sim.AddObject(ObjectType.EnergyStream,50,50,'E1')\n", "# E2 = sim.AddObject(ObjectType.EnergyStream,50,50,'E2')\n", "# Making distillation column\n", "ShortcutColumn = sim.AddObject(ObjectType.ShortcutColumn,50,50,'SCOL-1')\n", "\n", "# Getting All Objects\n", "one = one.GetAsObject()\n", "two = two.GetAsObject()\n", "three = three.GetAsObject()\n", "# E1 = E1.GetAsObject()\n", "# E2 = E2.GetAsObject()\n", "ShortcutColumn = ShortcutColumn.GetAsObject()\n", "\n", "# Connecting the simulation objects.\n", "sim.ConnectObjects(one.GraphicObject,ShortcutColumn.GraphicObject, -1, -1)\n", "sim.ConnectObjects(ShortcutColumn.GraphicObject,two.GraphicObject, -1, -1)\n", "sim.ConnectObjects(ShortcutColumn.GraphicObject,three.GraphicObject, -1, -1)\n", "# sim.ConnectObjects(ShortcutColumn.GraphicObject,E1.GraphicObject, -1, -1)\n", "# sim.ConnectObjects(E2.GraphicObject,ShortcutColumn.GraphicObject, -1, -1)\n", "\n", "# Material Stream Specs\n", "one.SetOverallComposition(Array[float]([0.5,0.5]))\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(298.15) #K\n", "one.<PERSON><PERSON><PERSON><PERSON>(101325) #Pa\n", "one.Set<PERSON><PERSON><PERSON><PERSON>(1) #kg/s\n", "\n", "# For layout\n", "sim.AutoLayout()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["<CondenserType.TotalCond: 0>"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# Setting Shortcut column specs\n", "ShortcutColumn.m_lightkey = \"Benzene\"\n", "ShortcutColumn.m_heavykey = \"Toluene\"\n", "ShortcutColumn.m_lightkeymolarfrac = 0.01\n", "ShortcutColumn.m_heavykeymolarfrac = 0.01\n", "ShortcutColumn.m_refluxratio = 1.5\n", "ShortcutColumn.m_condenserpressure = 101325\n", "ShortcutColumn.m_boilerpressure = 101325\n", "ShortcutColumn.CondenserType.TotalCond "]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "0.01\n", "0.01\n", "1.5\n", "101325.0\n", "101325.0\n", "TotalCond\n", "0.0\n", "0.0\n", "0.0\n", "0.0\n", "0.0\n", "0.0\n", "0.0\n", "0.0\n", "0.0\n", "0.0\n"]}], "source": ["# Getting the properties\n", "print(ShortcutColumn.m_lightkey) # Light key compound\n", "print(ShortcutColumn.m_heavykey) # heavy key compound\n", "print(ShortcutColumn.m_lightkeymolarfrac) # light key mol frac\n", "print(ShortcutColumn.m_heavykeymolarfrac) # heavy key mol frac\n", "print(ShortcutColumn.m_refluxratio) # reflux ratio\n", "print(ShortcutColumn.m_condenserpressure) # condenser pressure\n", "print(ShortcutColumn.m_boilerpressure) # reboiler pressure\n", "print(ShortcutColumn.condtype) # condenser type\n", "\n", "print(ShortcutColumn.m_Rmin) # This is minimum reflux ratio\n", "print(ShortcutColumn.m_Nmin) # This is minimum number of stages\n", "print(ShortcutColumn.m_N) # This is Actual Number of stages\n", "print(ShortcutColumn.ofs) # This is optimal feed stage\n", "print(ShortcutColumn.L_) # This is stripping liquid\n", "print(ShortcutColumn.L) # This is rectifying liquid\n", "print(ShortcutColumn.V_) # This is stripping vapor\n", "print(ShortcutColumn.V) # This is rectifying vapor\n", "print(ShortcutColumn.m_Qc) # this is condenser duty\n", "print(ShortcutColumn.m_Qb) # This is reboiler duty"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "# fileNameToSave = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/backend/artefacts/Process Models/ShortCut Column/Shortcut Column Test.dwxmz\")\n", "import os\n", "\n", "# Ensure the directory exists before saving\n", "save_dir = \"/code/backend/artefacts/bin/dwsim/entity_tests\"\n", "os.makedirs(save_dir, exist_ok=True)\n", "\n", "# Just use the direct path you want\n", "fileNameToSave = \"/code/backend/artefacts/bin/dwsim/entity_tests/Shortcut Column Test.dwxmz\"\n", "\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave,True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}