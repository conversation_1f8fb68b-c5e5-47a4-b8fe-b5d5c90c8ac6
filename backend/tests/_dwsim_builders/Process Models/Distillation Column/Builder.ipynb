{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code/.venv/lib/python3.8/site-packages/clr_loader/mono.py:180: UserWarning: Hosting Mono versions before v6.12 is known to be problematic. If the process crashes shortly after you see this message, try updating Mono to at least v6.12.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7ff56254d040>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "# add compounds\n", "cnames = [\"Benzene\", \"Toluene\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)\n", "\n", "sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1: mass flow set to 1 kg/s'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# Making some material streams\n", "one = sim.AddObject(ObjectType.MaterialStream,50,50,'1')\n", "two = sim.AddObject(ObjectType.MaterialStream,50,50,'2')\n", "three = sim.AddObject(ObjectType.MaterialStream,50,50,'3')\n", "# Making some energy streams\n", "E1 = sim.AddObject(ObjectType.EnergyStream,50,50,'E1')\n", "E2 = sim.AddObject(ObjectType.EnergyStream,50,50,'E2')\n", "# Making distillation column\n", "DistillationColumn = sim.AddObject(ObjectType.DistillationColumn,50,50,'DCOL-1')\n", "\n", "# Getting All Objects\n", "one = one.GetAsObject()\n", "two = two.GetAsObject()\n", "three = three.GetAsObject()\n", "E1 = E1.GetAsObject()\n", "E2 = E2.GetAsObject()\n", "DistillationColumn = DistillationColumn.GetAsObject()\n", "\n", "# Connecting the simulation objects.\n", "'''\n", "While exploration of Distillation column it was investigated on a later stage that the connectiing method works differently over here as compared\n", "To traditional Connecting methods, There are specific key words which connects and assigns to column \n", "'''\n", "\n", "# Material Stream Specs\n", "one.SetOverallComposition(Array[float]([0.5,0.5]))\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(298.15) #K\n", "one.<PERSON><PERSON><PERSON><PERSON>(101325) #Pa\n", "one.Set<PERSON><PERSON><PERSON><PERSON>(1) #kg/s"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Defining column specs\n", "# https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_DistillationColumn.htm\n", "# Settinng general specs\n", "DistillationColumn.SetNumberOfStages(14)  # in class\n", "DistillationColumn.SetTopPressure(101324)  # in class\n", "DistillationColumn.set_ColumnPressureDrop(0)  # in class\n", "# DistillationColumn.set_MaxIterations(99)\n", "\n", "# Setting condenser type and other attributes\n", "# For other type of condenser pls click → https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Column_condtype.htm\n", "DistillationColumn.CondenserType = DistillationColumn.CondenserType.Total_Condenser\n", "DistillationColumn.set_CondenserDeltaP(1) # in class\n", "\n", "# Setting top specs or top degree of freedom\n", "# allowed specs:\n", "# Heat_Duty = 0, supported units = \"*kW,kcal/h,BTU/h,BTU/s,HP,kJ/h,kJ/d,MW,W\"\n", "# Product_Molar_Flow_Rate = 1, supported units = \"*mol/s,lbmol/h,mol/h,mol/d,kmol/s,kmol/h,kmol/d\"\n", "# Component_Molar_Flow_Rate = 2, supported units = \"*mol/s,lbmol/h,mol/h,mol/d,kmol/s,kmol/h,kmol/d\"\n", "# Product_Mass_Flow_Rate = 3, supported unit = \"*g/s,lbm/h,kg/s,kg/h,kg/d,kg/min,lb/min,lb/s\"\n", "# Component_Mass_Flow_Rate = 4, compound choice will be open from the list of selected compounds in the simulation, supported unit = \"*g/s,lbm/h,kg/s,kg/h,kg/d,kg/min,lb/min,lb/s\"\n", "# Component_Fraction = 5, compound choice will be open from the list of selected compounds in the simulation, supported unit = \"*Molar, Mass\"\n", "# Component_Recovery = 6, compound choice will be open from the list of selected compounds in the simulation, supported unit = \"*%M/M, %W/W\"\n", "# Stream_Ratio = 7 \n", "# Temperature = 8, supported unit = \"*K, R, C ,F\"\n", "# for more details pls visit → https://dwsim.org/api_help/html/T_DWSIM_UnitOperations_UnitOperations_Auxiliary_SepOps_ColumnSpec_SpecType.htm\n", "DistillationColumn.SetCondenserSpec(spectype=\"Reflux Ratio\",value=0.81,units=\"\",compound=\"\")\n", "DistillationColumn.set_TotalCondenserSubcoolingDeltaT(1)  # in class \n", "\n", "# Setting bottom specs or bottom degree of freedom\n", "DistillationColumn.SetReboilerSpec(spectype=\"Product_Molar_Flow_Rate\",value=5.87,units=\"mol/s\",compound=\"\")\n", "\n", "# Setting feed tray\n", "# This is exclusive for distillation column\n", "DistillationColumn.ConnectFeed(one,7)\n", "DistillationColumn.ConnectDistillate(two)\n", "DistillationColumn.ConnectBottoms(three)\n", "DistillationColumn.ConnectCondenserDuty(E1)\n", "DistillationColumn.ConnectReboilerDuty(E2)\n", "\n", "# Layout\n", "sim.AutoLayout()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'DistillationColumn' object has no attribute 'ReboilerSpec'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;43mgetattr\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mDistillationColumn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mReboilerSpec\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mAttributeError\u001b[0m: 'DistillationColumn' object has no attribute 'ReboilerSpec'"]}], "source": ["getattr(DistillationColumn, \"ReboilerSpec\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "duplicate values found in <enum 'PropertyEnum'>: FLUID_IN_TUBES_COLD -> FLUID_IN_TUBES_HOT", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[6], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01msource\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON>nd\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01matlas_domainmodel\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01maggregates\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbase_aggregate\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01matlas\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Any, Callable, Dict, List, Optional, Tuple, Type, Union\n\u001b[1;32m      4\u001b[0m PARAMETER_KEYWORDS: Dict[atlas\u001b[38;5;241m.\u001b[39mParameterEnum, \u001b[38;5;28mstr\u001b[39m] \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m      5\u001b[0m \n\u001b[1;32m      6\u001b[0m     \u001b[38;5;66;03m# General Specs\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     40\u001b[0m     atlas\u001b[38;5;241m.\u001b[39mParameterEnum\u001b[38;5;241m.\u001b[39mBaseCompound: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     41\u001b[0m }\n", "File \u001b[0;32m/code/source/backend/atlas_domainmodel/aggregates/base_aggregate.py:19\u001b[0m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msource\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_sharedutils\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Utilities \u001b[38;5;28;01mas\u001b[39;00m util\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msource\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_sharedutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmix<PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ReprMixin, StrMixin\n\u001b[0;32m---> 19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msource\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01matlas_domainmodel\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mentities\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbase_entity\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m PropAndParamLog\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msource\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01matlas_domainmodel\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mentities\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mequipment\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m EquipmentEntity\n\u001b[1;32m     22\u001b[0m \u001b[38;5;66;03m# Internal Module Imports\u001b[39;00m\n", "File \u001b[0;32m/code/source/backend/atlas_domainmodel/entities/base_entity.py:20\u001b[0m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;66;03m# Inter-Module Imports\u001b[39;00m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msource\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_sharedutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmixins\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m <PERSON>r<PERSON><PERSON><PERSON>, StrMixin\n\u001b[0;32m---> 20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mpolicies\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mequipment_and_stream_policies\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m CalcPolicyEnum, ParameterEnum\n\u001b[1;32m     22\u001b[0m \u001b[38;5;66;03m# Intra-Module Imports\u001b[39;00m\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mvalueobjects\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mall\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m PropertyEnum\n", "File \u001b[0;32m/code/source/backend/atlas_domainmodel/policies/equipment_and_stream_policies.py:16\u001b[0m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01menum\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Enum, unique\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Any, Dict, List, Optional, Tuple, Union, cast\n\u001b[0;32m---> 16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01msource\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mbackend\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01matlas_domainmodel\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mvalueobjects\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mall\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m PropertyEnum\n\u001b[1;32m     18\u001b[0m \u001b[38;5;66;03m########################################\u001b[39;00m\n\u001b[1;32m     19\u001b[0m \n\u001b[1;32m     20\u001b[0m \u001b[38;5;66;03m# PARAMS\u001b[39;00m\n\u001b[1;32m     23\u001b[0m \u001b[38;5;129m@dataclass\u001b[39m\n\u001b[1;32m     24\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01m_ParamConstructorToggles\u001b[39;00m:\n", "File \u001b[0;32m/code/source/backend/atlas_domainmodel/valueobjects/all.py:750\u001b[0m\n\u001b[1;32m    741\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\n\u001b[1;32m    744\u001b[0m \u001b[38;5;66;03m########################################\u001b[39;00m\n\u001b[1;32m    745\u001b[0m \n\u001b[1;32m    746\u001b[0m \u001b[38;5;66;03m# PROPERTIES\u001b[39;00m\n\u001b[1;32m    749\u001b[0m \u001b[38;5;129;43m@unique\u001b[39;49m\n\u001b[0;32m--> 750\u001b[0m \u001b[38;5;28;43;01mclass\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;21;43;01mPropertyEnum\u001b[39;49;00m\u001b[43m(\u001b[49m\u001b[43mEnum\u001b[49m\u001b[43m)\u001b[49m\u001b[43m:\u001b[49m\n\u001b[1;32m    751\u001b[0m \u001b[38;5;250;43m    \u001b[39;49m\u001b[38;5;124;43;03m\"\"\"\u001b[39;49;00m\n\u001b[1;32m    752\u001b[0m \u001b[38;5;124;43;03m    Enumeration class representing different properties and their corresponding units of measure.\u001b[39;49;00m\n\u001b[1;32m    753\u001b[0m \u001b[38;5;124;43;03m    Based off DWSIM SI units\u001b[39;49;00m\n\u001b[1;32m    754\u001b[0m \u001b[38;5;124;43;03m    \"\"\"\u001b[39;49;00m\n\u001b[1;32m    756\u001b[0m \u001b[43m    \u001b[49m\u001b[43mTEMPERATURE\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mK\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mTemperature\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.pyenv/versions/3.8.18/lib/python3.8/enum.py:974\u001b[0m, in \u001b[0;36munique\u001b[0;34m(enumeration)\u001b[0m\n\u001b[1;32m    971\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m duplicates:\n\u001b[1;32m    972\u001b[0m     alias_details \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mjoin(\n\u001b[1;32m    973\u001b[0m             [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m -> \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m (alias, name) \u001b[38;5;28;01mfor\u001b[39;00m (alias, name) \u001b[38;5;129;01min\u001b[39;00m duplicates])\n\u001b[0;32m--> 974\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mduplicate values found in \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m \u001b[38;5;241m%\u001b[39m\n\u001b[1;32m    975\u001b[0m             (enumeration, alias_details))\n\u001b[1;32m    976\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m enumeration\n", "\u001b[0;31mValueError\u001b[0m: duplicate values found in <enum 'PropertyEnum'>: FLUID_IN_TUBES_COLD -> FLUID_IN_TUBES_HOT"]}], "source": ["import source.backend.atlas_domainmodel.aggregates.base_aggregate as atlas\n", "from typing import Any, Callable, Dict, List, Optional, Tuple, Type, Union\n", "\n", "PARAMETER_KEYWORDS: Dict[atlas.ParameterEnum, str] = {\n", "\n", "    # General Specs\n", "    atlas.ParameterEnum.NumberofStages: \"NumberOfStages\",\n", "    atlas.ParameterEnum.CondenserPressure: \"TopPressure\",\n", "    atlas.ParameterEnum.ColumnPressureDrop: \"ColumnPressureDrop\",\n", "\n", "    # Condensor Type\n", "    atlas.ParameterEnum.CondenserDeltaP: \"CondenserDeltaP\",\n", "    atlas.ParameterEnum.TotalCondenserSubcoolingTemperatureDrop: \"TotalCondenserSubcoolingDeltaT\",\n", "    atlas.ParameterEnum.CondenserDuty: \"CondenserDuty\",\n", "    \n", "    # Reboiler Type\n", "    atlas.ParameterEnum.ReboilerDuty: \"ReboilerDuty\",\n", "\n", "    # Bottom Specs\n", "    # -------------------\n", "    atlas.ParameterEnum.VaporProductFlowRate: \"VaporFlowRate\",\n", "    atlas.ParameterEnum.HeatLoad_Condenser: \"\",\n", "    atlas.ParameterEnum.ProductMolarFlow_Condenser: \"\",\n", "    atlas.ParameterEnum.CompoundMolarFlowInProductStream_Condenser: \"\",\n", "    atlas.ParameterEnum.ProductMassFlow_Condenser: \"DistillateFlowRate\",\n", "    atlas.ParameterEnum.CompoundMassFlowInProductStream_Condenser: \"\",\n", "    atlas.ParameterEnum.CompoundFractionInProductStream_Condenser: \"\",\n", "    atlas.ParameterEnum.CompoundRecovery_Condenser: \"\",\n", "    atlas.ParameterEnum.RefluxRatio: \"RefluxRatio\",\n", "    atlas.ParameterEnum.Temperature_Condenser: \"\",\n", "    atlas.ParameterEnum.HeatLoad_Reboiler: \"\",\n", "    atlas.ParameterEnum.ProductMolarFlow_Reboiler: \"\",\n", "    atlas.ParameterEnum.CompoundMolarFlowInProductStream_Reboiler: \"\",\n", "    atlas.ParameterEnum.ProductMassFlow_Reboiler: \"\",\n", "    atlas.ParameterEnum.CompoundMassFlowInProductStream_Reboiler: \"\",\n", "    atlas.ParameterEnum.CompoundFractionInProductStream_Reboiler: \"\",\n", "    atlas.ParameterEnum.CompoundRecovery_Reboiler: \"\",\n", "    atlas.ParameterEnum.Boilup_Ratio: \"\",\n", "    atlas.ParameterEnum.Temperature_Reboiler: \"\",\n", "    atlas.ParameterEnum.BaseCompound: \"\",\n", "}\n", "\n", "for k, v in PARAMETER_KEYWORDS.items():\n", "    _attr = getattr(DistillationColumn, v)\n", "    print(k, \"-\",  _attr)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["14"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["DistillationColumn.NumberOfStages\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Distillation Column/Distillation Column Test.dwxmz\")\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave,True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}