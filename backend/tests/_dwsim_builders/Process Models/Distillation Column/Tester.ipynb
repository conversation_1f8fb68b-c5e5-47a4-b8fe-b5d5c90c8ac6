{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[<ObjectType.MaterialStream: 6>, <ObjectType.DistillationColumn: 30>, <ObjectType.EnergyStream: 7>, <ObjectType.EnergyStream: 7>, <ObjectType.MaterialStream: 6>, <ObjectType.MaterialStream: 6>]\n", "['1', 'DCOL-1', 'E1', 'E2', '2', '3']\n"]}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Distillation Column/Distillation Column.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "all_objects = [obj.Value.get_ObjectType() for obj in sim.get_GraphicObjects()]\n", "print(all_objects)\n", "\n", "all_objects_tags = [obj.Value.get_Tag() for obj in sim.get_GraphicObjects()]\n", "print(all_objects_tags)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["DCOL_1 = sim.GetObject('DCOL-1')\n", "DCOL_1 = DCOL_1.GetAsObject()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DistillationColumn 14 nan 100\n"]}], "source": ["print(DCOL_1.get_ColumnType(),\n", "DCOL_1.get_NumberOfStages(),\n", "DCOL_1.get_ColumnPressureDrop(),\n", "DCOL_1.get_MaxIterations())"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f475580>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f475740>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f4759c0>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f4754c0>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f4751c0>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f4756c0>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f475080>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f4753c0>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f475f80>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f475300>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f475b00>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f475680>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f475400>,\n", " <DWSIM.UnitOperations.UnitOperations.Auxiliary.SepOps.Stage object at 0x7fdc2f4750c0>]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["DCOL_1.get_ComponentDescription()\n", "DCOL_1.get_ComponentName()\n", "DCOL_1.get_CondenserDuty()\n", "DCOL_1.get_CondenserType()\n", "DCOL_1.get_TraySpacing()\n", "list(DCOL_1.get_Stages())\n", "DCOL_1.feed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["DCOL_1.get_CondenserDeltaP()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}