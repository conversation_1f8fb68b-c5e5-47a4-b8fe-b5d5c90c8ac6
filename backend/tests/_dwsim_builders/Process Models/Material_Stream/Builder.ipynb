{"cells": [{"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/code/backend/artefacts/bin/dwsim/Empty_Template.dwxmz\n"]}, {"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7f1c7d5e2340>"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "import backend.core._sharedutils.Utilities as sharedutils\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array, Double\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "# fileNameToLoad = Path.Combine(\n", "#     Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "#     \"/code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\",\n", "# )\n", "\n", "# fileNameToLoad = Path.Combine(\n", "#     \"/code/backend/infrastructure/artefacts/bin/dwsim/Empty_Template.dwxmz\"\n", "# )\n", "# Alternative using project root\n", "project_root = sharedutils.get_project_root()\n", "fileNameToLoad = Path.Combine(\n", "    str(project_root), \"backend/artefacts/bin/dwsim/Empty_Template.dwxmz\"\n", ")\n", "print(fileNameToLoad)\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "# add compounds\n", "cnames = [\"Benzene\", \"Toluene\", \"Water\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)\n", "\n", "sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "code", "execution_count": 77, "metadata": {}, "outputs": [], "source": ["# Making some material streams\n", "Mass_Basis = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"Mass Basis\")\n", "Mol_Basis = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"Mol Basis\")"]}, {"cell_type": "code", "execution_count": 78, "metadata": {}, "outputs": [{"data": {"text/plain": ["'MAT-063005eb-8e68-4fb7-958e-a4751c87500a'"]}, "execution_count": 78, "metadata": {}, "output_type": "execute_result"}], "source": ["Mass_Basis.Name"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["# Getting All Objects\n", "Mass_Basis = Mass_Basis.GetAsObject()\n", "Mol_Basis = Mol_Basis.GetAsObject()\n", "\n", "# Material Stream Specs\n", "# Mass_Basis.SetOverallComposition(Array[float]([0.5,0.5]))\n", "# Mass_Basis.NormalizeOverallMassComposition()\n", "# Mass_Basis.SetOverallMassComposition(Array[float]([0.5,0.5])) # this will crash\n", "Mass_Basis.SetMassFlow(2)  # kg/s\n", "Mass_Basis.SetOverallCompoundMassFlow(\"Benzene\", 5)\n", "Mass_Basis.SetOverallCompoundMassFlow(\"Toluene\", 5)\n", "Mass_Basis.SetTemperature(298.15)  # K\n", "Mass_Basis.SetPressure(101325)  # Pa\n", "\n", "Mol_Basis.SetOverallComposition(Array[float]([0.5, 0.3, 0.2]))\n", "Mol_Basis.SetTemperature(298.15)  # K\n", "Mol_Basis.SetPressure(101325)  # Pa\n", "Mol_Basis.SetMolarFlow(1)  # mol/s\n", "# For layout\n", "sim.AutoLayout()"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["stream_obj = sim.AddObject(\n", "    ObjectType.MaterialStream, 50, 50, \"stream_obj\"\n", ").GetAsObject()"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [{"data": {"text/plain": ["'stream_obj: mass flow set to 0.127338 kg/s'"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["stream_obj.SetMassFlow(0.127338)"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.127338"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["stream_obj.GetMassFlow()"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.5, 0.5, 0.0]"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["list(Mass_Basis.GetOverallMassComposition())"]}, {"cell_type": "code", "execution_count": 84, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.3333333333333333, 0.3333333333333333, 0.3333333333333333]"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["Mass_Basis.SetOverallCompoundMassFlow(\"Water\", 5)\n", "list(Mass_Basis.GetOverallMassComposition())"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [{"data": {"text/plain": ["[0.0, 0.5, 0.5]"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["Mass_Basis.SetOverallCompoundMassFlow(\"Benzene\", 0)\n", "list(Mass_Basis.GetOverallMassComposition())"]}, {"cell_type": "code", "execution_count": 86, "metadata": {}, "outputs": [{"data": {"text/plain": ["'flash spec set to Pressure_and_VaporFraction'"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["Mass_Basis.SetFlashSpec(\"PVF\")"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["101325.0\n", "360.28765076258077\n", "10.0\n", "331.8083485007541\n", "0.011277186250572401\n", "-1263.9286176186786\n", "-3.268180731578261\n", "[0.0, 0.5, 0.5000000000000001]\n", "[0.0, 0.1635467384620999, 0.8364532615379001]\n", "PVF\n", "########################################################\n", "101325.0\n", "298.15\n", "0.070300505\n", "1.0\n", "7.995738199547129e-05\n", "-511.67747437992386\n", "-1.2865281172689962\n", "[0.5555567488455453, 0.3931910446447006, 0.051252206509754095]\n", "[0.5, 0.3, 0.2]\n"]}], "source": ["# Getting the properties\n", "print(Mass_Basis.GetPressure())\n", "print(Mass_Basis.GetTemperature())\n", "print(Mass_Basis.GetMassFlow())\n", "print(Mass_Basis.GetMolarFlow())\n", "print(Mass_Basis.GetVolumetricFlow())\n", "print(Mass_Basis.GetMassEnthalpy())\n", "print(Mass_Basis.GetMassEntropy())\n", "print(list(Mass_Basis.GetOverallMassComposition()))\n", "print(list(Mass_Basis.GetOverallComposition()))\n", "print(Mass_Basis.GetFlashSpec())\n", "print(\"########################################################\")\n", "print(Mol_Basis.GetPressure())\n", "print(Mol_Basis.GetTemperature())\n", "print(Mol_Basis.GetMassFlow())\n", "print(Mol_Basis.GetMolarFlow())\n", "print(Mol_Basis.GetVolumetricFlow())\n", "print(Mol_Basis.GetMassEnthalpy())\n", "print(Mol_Basis.GetMassEntropy())\n", "print(list(Mol_Basis.GetOverallMassComposition()))\n", "print(list(Mol_Basis.GetOverallComposition()))"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["E-S_ss-3\n"]}], "source": ["# ENERGY STREAM\n", "\n", "e_sim = sim.AddObject(ObjectType.EnergyStream, 0, 0, \"E-S_ss-3\")\n", "e_unit = e_sim.GetAsObject()\n", "\n", "print(e_unit)"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [{"data": {"text/plain": ["<DWSIM.Interfaces.IGraphicObject object at 0x7f1c1aa5a6c0>"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["e_unit.GraphicObject"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(\n", "    Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "    \"/code/sandbox/DWSim Builder/Process Models/Material Stream/Material Stream Test.dwxmz\",\n", ")\n", "\n", "project_root = sharedutils.get_project_root()\n", "fileNameToSave = Path.Combine(\n", "    str(project_root),\n", "    \"backend/artefacts/bin/dwsim/aggregate_tests/matstream_push.dwxmz\",\n", ")\n", "interf.SaveFlowsheet(sim, fileNameToSave, True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}