{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code/.venv/lib/python3.8/site-packages/clr_loader/mono.py:180: UserWarning: Hosting Mono versions before v6.12 is known to be problematic. If the process crashes shortly after you see this message, try updating Mono to at least v6.12.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7f8b382f2dc0>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "# add compounds\n", "cnames = [\"Cis-2-butene\", \"Trans-2-butene\", \"Ethylene\", \"Hydrogen\", \"Ethane\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)\n", "\n", "sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1: mass flow set to 1.55851 kg/s'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["one = sim.AddObject(ObjectType.MaterialStream,50,50,'1')\n", "two = sim.AddObject(ObjectType.MaterialStream,50,50,'2')\n", "E1 = sim.AddObject(ObjectType.EnergyStream,50,50,'E1')\n", "RCT_PFR = sim.AddObject(ObjectType.RCT_PFR,50,50,'Equilibrium reactor')\n", "\n", "one = one.GetAsObject()\n", "two = two.GetAsObject()\n", "E1 = E1.GetAsObject()\n", "RCT_PFR = RCT_PFR.GetAsObject()\n", "\n", "sim.ConnectObjects(one.GraphicObject,RCT_PFR.GraphicObject, -1, -1)\n", "sim.ConnectObjects(E1.GraphicObject,RCT_PFR.GraphicObject, -1, -1)\n", "sim.ConnectObjects(RCT_PFR.GraphicObject,two.GraphicObject, -1, -1)\n", "\n", "sim.AutoLayout()\n", "\n", "one.SetOverallComposition(Array[float]([1,0,0,0,0]))\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(298.15) #K\n", "one.SetPressure(1.2E+06) #Pa\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(1.55851) #kg/s"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reactions added to sets:\n", "- DefaultSet: Transesterification reaction\n", "- HydrogenationSet: Hydrogenation of Ethylene\n", "\n"]}], "source": ["# https://dwsim.org/api_help/html/M_DWSIM_FlowsheetBase_FlowsheetBase_CreateKineticReaction.htm\n", "\n", "# Define stoichiometric coefficients for each reaction\n", "# Reaction 1: Transesterification reaction\n", "\n", "# Stoichiometric coeffs\n", "comps1 = Dictionary[str, float]()\n", "comps1.Add(\"Cis-2-butene\", -1.0)\n", "comps1.Add(\"Trans-2-butene\", 1.0)\n", "\n", "# Direct order coeff\n", "dorders1 = Dictionary[str, float]()\n", "dorders1.Add(\"Cis-2-butene\", 1.0)\n", "dorders1.Add(\"Trans-2-butene\", 0)\n", "\n", "# Reverse order coeff\n", "rorders1 = Dictionary[str, float]()\n", "rorders1.Add(\"Cis-2-butene\", 0)\n", "rorders1.Add(\"Trans-2-butene\", 0)\n", "\n", "# Reaction 2: Hydrogenation of Ethylene\n", "comps2 = Dictionary[str, float]()\n", "comps2.Add(\"Ethylene\", -1.0)\n", "comps2.Add(\"Hydrogen\", -1.0)\n", "comps2.Add(\"Ethane\", 1.0)\n", "\n", "# Direct order coeff\n", "dorders2 = Dictionary[str, float]()\n", "dorders2.Add(\"Ethylene\", 1.0)\n", "dorders2.Add(\"Hydrogen\", 1.0)\n", "dorders2.Add(\"Ethan<PERSON>\", 0)\n", "\n", "# Reverse order coeff\n", "rorders2 = Dictionary[str, float]()\n", "rorders2.Add(\"Ethylene\", 0)\n", "rorders2.Add(\"Hydrogen\", 0)\n", "rorders2.Add(\"Ethane\", 1.0)\n", "\n", "# Create kinetic reaction objects\n", "kr1 = sim.CreateKineticReaction(name=\"Transesterification reaction\", \n", "                                description=\"CH3CHCHCH3 <--> CH3CHCHCH3\",\n", "                                compounds_and_stoichcoeffs=comps1, \n", "                                directorders=dorders1, reverseorders=rorders1, \n", "                                basecompound=\"Cis-2-butene\", reactionphase=\"Liquid\", \n", "                                basis=\"Molar Concentrations\", amountunits=\"kmol/m3\",\n", "                                rateunits=\"kmol/[m3.s]\", Aforward=0.003833,Eforward=0,\n", "                                Areverse=0,Ereverse=0, Expr_forward=\"\",Expr_reverse=\"\")\n", "\n", "# Reaction 2\n", "kr2 = sim.CreateKineticReaction(name=\"Hydrogenation of Ethylene\", \n", "                                description=\"C2H4 + H2 <--> C2H6\", \n", "                                compounds_and_stoichcoeffs=comps2, \n", "                                directorders=dorders2, reverseorders=rorders2, \n", "                                basecompound=\"Ethylene\", reactionphase=\"Vapor\", \n", "                                basis=\"Molar Concentrations\", amountunits=\"kmol/m3\", \n", "                                rateunits=\"kmol/[m3.s]\", Aforward=0.1, Eforward=50, \n", "                                Areverse=0.01, Ereverse=45, Expr_forward=\"\", Expr_reverse=\"\")\n", "\n", "# Add reactions to the simulation\n", "sim.AddReaction(kr1)\n", "sim.AddReaction(kr2)\n", "\n", "# Check if reaction sets already exist and create them if not\n", "existing_sets = sim.get_ReactionSets()\n", "\n", "if not existing_sets.ContainsKey(\"DefaultSet\"):\n", "    reactionSet1 = sim.CreateReactionSet(\"DefaultSet\", \"Description for Default Set\")\n", "    sim.AddReactionSet(reactionSet1)\n", "else:\n", "    reactionSet1 = existing_sets[\"DefaultSet\"]\n", "\n", "if not existing_sets.ContainsKey(\"HydrogenationSet\"):\n", "    reactionSet2 = sim.CreateReactionSet(\"HydrogenationSet\", \"Description for Hydrogenation Set\")\n", "    sim.AddReactionSet(reactionSet2)\n", "else:\n", "    reactionSet2 = existing_sets[\"HydrogenationSet\"]\n", "\n", "# Add reactions to the sets\n", "sim.AddReactionToSet(kr1.ID, \"DefaultSet\", True, 1)\n", "sim.AddReactionToSet(kr2.ID, \"HydrogenationSet\", True, 1)\n", "\n", "# Print confirmation of the sets\n", "print(f\"Reactions added to sets:\\n\"\n", "      f\"- DefaultSet: {kr1.Name}\\n\"\n", "      f\"- HydrogenationSet: {kr2.Name}\\n\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Description: De<PERSON><PERSON> Reaction Set\n", "ID: DefaultSet\n", "Name: De<PERSON><PERSON>\n", "Assigned ReactionSetID: DefaultSet\n"]}], "source": ["# Retrieve the reaction sets as a dictionary\n", "reaction_sets = dict(sim.get_ReactionSets())\n", "# Get the WGSSet\n", "default_set = reaction_sets[\"DefaultSet\"]\n", "default_set_id = default_set.ID\n", "# Print details to confirm\n", "print(f\"Description: {default_set.Description}\")\n", "print(f\"ID: {default_set.ID}\")\n", "print(f\"Name: {default_set.Name}\")\n", "# Assign the Water-Gas Shift reaction set to the reactor\n", "RCT_PFR.ReactionSetID = default_set_id\n", "# Print confirmation\n", "print(f\"Assigned ReactionSetID: {RCT_PFR.ReactionSetID}\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["RCT_PFR.ReactorOperationMode =RCT_PFR.ReactorOperationMode.Isothermic"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0\n", "-94.55006184076888\n", "396.954635612892\n", "3822.383087932598\n", "{'Transesterification reaction': 0.02777779758144029}\n", "{'Transesterification reaction': 2.4894695429048064e-10}\n", "{'Transesterification reaction': -100.00007129318503}\n", "{'Cis-2-butene': 0.9999999999996888, 'Trans-2-butene': 0.0}\n"]}], "source": ["print(RCT_PFR.DeltaT)\n", "print(RCT_PFR.DeltaQ)\n", "print(RCT_PFR.ResidenceTime)\n", "print(RCT_PFR.DeltaP)\n", "print(dict(RCT_PFR.RxiT)) # This one is reaction coordinate\n", "print(dict(RCT_PFR.Rxi)) # This one is reaction rate\n", "print(dict(RCT_PFR.DHRi))# This one is reaction heat\n", "print(dict(RCT_PFR.get_ComponentConversions()))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Reactors/PFR/Kinetc Reaction/PFR Reactor Test.dwxmz\")\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave,True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}