{"cells": [{"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[<ObjectType.MaterialStream: 6>, <ObjectType.RCT_PFR: 27>, <ObjectType.EnergyStream: 7>, <ObjectType.MaterialStream: 6>]\n", "['1', 'PFR-1', 'E1', '3']\n"]}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Reactors/PFR/Hetrogeneous Catalytic Reaction/Hetrogeneous Catalytic Reaction PFR.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "all_objects = [obj.Value.get_ObjectType() for obj in sim.get_GraphicObjects()]\n", "print(all_objects)\n", "\n", "all_objects_tags = [obj.Value.get_Tag() for obj in sim.get_GraphicObjects()]\n", "print(all_objects_tags)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DefaultSet\n"]}], "source": ["PFR_1 = sim.GetObject('PFR-1')\n", "PFR_1 = PFR_1.GetAsObject()\n", "print(PFR_1.get_ReactionSetID())\n", "x = PFR_1.get_ReactionSetID()"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_values([<DWSIM.Interfaces.IReactionSet object at 0x7f64245712c0>])\n", "dict_keys(['DefaultSet'])\n"]}], "source": ["y = dict(sim.get_ReactionSets())\n", "print(y.values())\n", "print(y.keys())"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<System.Collections.Generic.KeyValuePair[String,IReactionSet] object at 0x7f63fd87f1c0>]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["z = list(sim.get_ReactionSets())\n", "z"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Default Reaction Set\n", "DefaultSet\n", "Default Set\n"]}, {"data": {"text/plain": ["'9f89fbec-e0a1-4268-a501-114f94af4389'"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["p = z[0]\n", "q = p.get_Value()\n", "print(q.Description)\n", "print(q.ID)\n", "print(q.Name)\n", "r = list(q.Reactions)\n", "a = r[0].get_Value()\n", "a.get_ReactionID()"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0\n", "-2.77575865074194\n", "29.6304713858717\n", "2130.19391136011\n", "[]\n", "[]\n", "{}\n", "{}\n"]}, {"data": {"text/plain": ["0.01"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["print(PFR_1.DeltaT)\n", "print(PFR_1.DeltaQ)\n", "print(PFR_1.ResidenceTime)\n", "print(PFR_1.DeltaP)\n", "print(list(PFR_1.DHRT))\n", "print(list(PFR_1.DHRi))\n", "print(dict(PFR_1.Rxi))\n", "print(dict(PFR_1.RxiT))\n", "dict(PFR_1.ComponentConversions)\n", "PFR_1.get_dV()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}