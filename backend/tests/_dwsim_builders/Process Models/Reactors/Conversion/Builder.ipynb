{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code/.venv/lib/python3.8/site-packages/clr_loader/mono.py:180: UserWarning: Hosting Mono versions before v6.12 is known to be problematic. If the process crashes shortly after you see this message, try updating Mono to at least v6.12.\n", "  warnings.warn(\n"]}, {"ename": "DirectoryNotFoundException", "evalue": "Could not find a part of the path \"/code/backend/infrastructure/artefacts/bin/dwsim/Empty_Template.dwxmz\".\n  at DWSIM.Automation.Automation3.LoadFlowsheet (System.String filepath) [0x00017] in <b9e27a2a8bf3484782d02fa21a4c0779>:0 \n  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)\n  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <a17fa1457c5d44f2885ac746c1764ea5>:0 ", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;*****************************\u001b[0m                Traceback (most recent call last)", "\u001b[0;32m/tmp/ipykernel_7334/4020719162.py\u001b[0m in \u001b[0;36m<cell line: 49>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     47\u001b[0m \u001b[0;31m# The method takes a single argument, which is the file path of the flowsheet to be loaded\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     48\u001b[0m \u001b[0;31m# The method returns a Simulation object that represents the loaded flowsheet\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 49\u001b[0;31m \u001b[0msim\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0minterf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mLoadFlowsheet\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfileNameToLoad\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     50\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     51\u001b[0m \u001b[0;31m# add compounds\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;*****************************\u001b[0m: Could not find a part of the path \"/code/backend/infrastructure/artefacts/bin/dwsim/Empty_Template.dwxmz\".\n  at DWSIM.Automation.Automation3.LoadFlowsheet (System.String filepath) [0x00017] in <b9e27a2a8bf3484782d02fa21a4c0779>:0 \n  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)\n  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <a17fa1457c5d44f2885ac746c1764ea5>:0 "]}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(\n", "    Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "    \"/code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\",\n", ")\n", "\n", "fileNameToLoad = Path.Combine(\n", "    \"/code/backend/infrastructure/artefacts/bin/dwsim/Empty_Template.dwxmz\"\n", ")\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "# add compounds\n", "cnames = [\n", "    \"Toluene\",\n", "    \"Water\",\n", "    \"Hydrogen\",\n", "    \"Methane\",\n", "    \"Benzene\",\n", "    \"Nitrogen\",\n", "    \"Ammonia\",\n", "    \"Carbon monoxide\",\n", "    \"Carbon dioxide\",\n", "]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)\n", "\n", "sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1: mass flow set to 2.801625 kg/s'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["one = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"1\")\n", "two = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"2\")\n", "three = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"3\")\n", "E1 = sim.AddObject(ObjectType.EnergyStream, 50, 50, \"E1\")\n", "RCT_Converison = sim.AddObject(ObjectType.RCT_Conversion, 50, 50, \"Converison reactor\")\n", "\n", "one = one.GetAsObject()\n", "two = two.GetAsObject()\n", "three = three.GetAsObject()\n", "E1 = E1.GetAsObject()\n", "RCT_Converison = RCT_Converison.GetAsObject()\n", "\n", "sim.ConnectObjects(one.GraphicObject, RCT_Converison.GraphicObject, -1, -1)\n", "sim.ConnectObjects(RCT_Converison.GraphicObject, E1.GraphicObject, -1, -1)\n", "sim.ConnectObjects(RCT_Converison.GraphicObject, two.GraphicObject, -1, -1)\n", "sim.ConnectObjects(RCT_Converison.GraphicObject, three.GraphicObject, -1, -1)\n", "\n", "sim.AutoLayout()\n", "\n", "one.SetOverallComposition(Array[float]([0, 0.56, 0, 0, 0, 0, 0, 0.44, 0]))\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(373.15)  # K\n", "one.SetPressure(100000)  # Pa\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(2.801625)  # kg/s"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# unit_obj = sim.GetObject(\"Conversion reactor\").GetAsObject()\n", "# print(unit_obj)\n", "# unit_obj = unit_obj.GetAsObject()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# https://dwsim.org/api_help/html/M_DWSIM_FormFlowsheet_CreateConversionReaction.htm\n", "\n", "# Define stoichiometric coefficients for each reaction\n", "comps1 = Dictionary[str, float]()\n", "comps1.Add(\"Toluene\", -1.0)\n", "comps1.Add(\"Hydrogen\", -1.0)\n", "comps1.Add(\"Methane\", 1.0)\n", "comps1.Add(\"Benzene\", 1.0)\n", "\n", "comps2 = Dictionary[str, float]()\n", "comps2.Add(\"Carbon monoxide\", -1.0)\n", "comps2.Add(\"Water\", -1.0)\n", "comps2.Add(\"Carbon dioxide\", 1.0)\n", "comps2.Add(\"Hydrogen\", 1.0)\n", "\n", "method = sim.CreateConversionReaction\n", "# Create conversion reaction objects\n", "kr1 = sim.CreateConversionReaction(\n", "    \"Dealkylation of toluene\",\n", "    \"Description Dealkylation\",\n", "    comps1,\n", "    \"Toluene\",\n", "    \"Vapor\",\n", "    \"75\",\n", ")\n", "kr2 = sim.CreateConversionReaction(\n", "    \"Water-Gas Shift Reaction\",\n", "    \"Description WGS\",\n", "    comps2,\n", "    \"Carbon monoxide\",\n", "    \"Vapor\",\n", "    \"50\",\n", ")\n", "kr2 = sim.CreateConversionReaction(\n", "    \"Water-Gas Shift Reaction\",\n", "    \"Description WGS\",\n", "    comps2,\n", "    \"Carbon monoxide\",\n", "    \"Vapor\",\n", "    \"T-273.15\",\n", ")\n", "\n", "# Add reactions to the simulation\n", "sim.AddReaction(kr1)\n", "sim.AddReaction(kr2)\n", "\n", "# Check if reaction sets already exist\n", "existing_sets = sim.get_ReactionSets()  #  Method to get existing sets\n", "\n", "if not existing_sets.ContainsKey(\"DefaultSet\"):\n", "    # Create reaction set if it doesn't exist\n", "    reactionSet1 = sim.CreateReactionSet(\"DefaultSet\", \"Description for Default Set\")\n", "    sim.AddReactionSet(reactionSet1)\n", "\n", "if not existing_sets.ContainsKey(\"WGSSet\"):\n", "    # Create another reaction set with a unique name\n", "    reactionSet2 = sim.CreateReactionSet(\"WGSSet\", \"Description for WGS Set\")\n", "    sim.AddReactionSet(reactionSet2)\n", "\n", "# Add reactions to the sets\n", "# sim.AddReactionToSet(kr1.ID, \"DefaultSet\", True, 1)\n", "# sim.AddReactionToSet(kr2.ID, \"WGSSet\", True, 1)\n", "sim.AddReactionToSet(kr1.ID, \"DefaultSet\", True, 1)\n", "sim.AddReactionToSet(kr2.ID, \"WGSSet\", True, 1)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'str'> Vapor\n"]}], "source": ["# Testing phasename returns\n", "rp = kr1.ReactionPhase.ToString()\n", "print(type(rp), rp)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Test unit object and ReactionSetID\n", "RCT_Converison.ReactionSetID = \"WGSSet\"\n", "# dict(sim.get_ReactionSets())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Test Creation via dict\n", "\n", "import backend.core._sharedutils.Utilities as sharedutils\n", "\n", "comps_dict = {\n", "    \"Carbon monoxide\": -1.0,\n", "    \"Water\": -1.0,\n", "    \"Carbon dioxide\": 1.0,\n", "    \"Hydrogen\": 1.0,\n", "}\n", "\n", "comps1 = Dictionary[str, float]()\n", "comps1.Add(\"Toluene\", -1.0)\n", "comps1.Add(\"Hydrogen\", -1.0)\n", "comps1.Add(\"Methane\", 1.0)\n", "comps1.Add(\"Benzene\", 1.0)\n", "# Create conversion reaction objects\n", "reaction_args = {\n", "    \"name\": \"Dealkylation of toluene\",\n", "    \"description\": \"Description\",\n", "    \"compounds_and_stoichoeffs\": comps1,\n", "    \"basecompound\": \"Toluene\",\n", "    \"reactionphase\": \"Vapour\",\n", "    \"conversionExpression\": \"75\",\n", "}\n", "\n", "# r_args = sharedutils.cast_value_py2dotnet(reaction_args) # NOTE - did not work, could not unpack the dict\n", "# r_args = [sharedutils.cast_value_py2dotnet(val) for val in reaction_args.values()]\n", "# conv_reaction_test = sim.CreateConversionReaction(*[reaction_args.values()])\n", "conv_reaction_test = sim.CreateConversionReaction(*list(reaction_args.values()))\n", "# conv_reaction_test = sim.CreateConversionReaction(**reaction_args)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["'T-273.15'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["kr2.Expression"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.IReaction'> Water-Gas Shift Reaction\n"]}], "source": ["# Test get reaction\n", "\n", "\"\"\"\n", "kr2 = sim.CreateConversionReaction(\n", "    \"Water-Gas Shift Reaction\",\n", "    \"Description WGS\",\n", "    comps2,\n", "    \"Carbon monoxide\",\n", "    \"Vapor\",\n", "    \"T-273.15\",\n", ")\n", "\"\"\"\n", "\n", "reaction = sim.GetReaction(\"Water-Gas Shift Reaction\")\n", "print(type(reaction), reaction)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reaction Tests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- reaction has to be added to sim before it can be gotten\n", "- reactions once added cannot be replaced -> we will simply use a datetime stamp to make it unique to replace  "]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ID-1 <class 'DWSIM.Interfaces.IReaction'>\n", "ID-1 <class 'DWSIM.Interfaces.IReaction'>\n", "ID-241023152203 <class 'DWSIM.Interfaces.IReaction'>\n"]}], "source": ["# Testing for Reaction Removal\n", "comp_dict = {\"Toluene\": 1.0, \"Methane\": 1.0, \"Water\": 1.0}\n", "composition = Dictionary[str, float]()\n", "\n", "for k, v in comp_dict.items():\n", "    composition.Add(k, v)\n", "\n", "reaction_remove_test = sim.CreateConversionReaction(\n", "    \"ID-1\", \"Description Dealkylation\", composition, \"Toluene\", \"Vapor\", \"75\"\n", ")\n", "\n", "print(reaction_remove_test, type(reaction_remove_test))\n", "\n", "\n", "# Get Reaction\n", "sim.AddReaction(reaction_remove_test)\n", "b = sim.GetReaction(\"ID-1\")\n", "print(b, type(b))\n", "\n", "# Remove Reaction\n", "\n", "# Replace Reaction\n", "comp_dict = {\"Toluene\": 1.0, \"Hydrogen\": 1.0, \"Water\": 1.0}\n", "composition = Dictionary[str, float]()\n", "\n", "for k, v in comp_dict.items():\n", "    composition.Add(k, v)\n", "\n", "from datetime import datetime\n", "\n", "timestamp = datetime.now().strftime(\"%y%m%d%H%M%S\")\n", "reaction_id = f\"ID-{timestamp}\"\n", "\n", "reaction_remove_test = sim.CreateConversionReaction(\n", "    reaction_id, \"Description Dealkylation\", composition, \"Hydrogen\", \"Vapor\", \"75\"\n", ")\n", "\n", "sim.AddReaction(reaction_remove_test)\n", "print(reaction_remove_test, type(reaction_remove_test))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Reaction Set\n", "\n", "- not able to remove, so timestamps used to replace"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.Interfaces.IReactionSet'> ID-REACTIONSET-241023152203\n"]}], "source": ["timestamp = datetime.now().strftime(\"%y%m%d%H%M%S\")\n", "reaction_set_id = f\"ID-REACTIONSET-{timestamp}\"\n", "\n", "# Create / Get\n", "sim.AddReactionSet(sim.CreateReactionSet(reaction_set_id, \"abc\"))\n", "sim.AddReactionToSet(reaction_id, reaction_set_id, True, 1)\n", "reaction_set = sim.GetReactionSet(reaction_set_id)\n", "print(type(reaction_set), reaction_set.ID)\n", "\n", "\n", "# Remove / Replace"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Associating Reaction Set"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## Others"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["r"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'DWSIM.UnitOperations.Reactors.Reactor_Conversion'>\n"]}], "source": ["print(type(RCT_Converison))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Description: Description for WGS Set\n", "ID: WGSSet\n", "Name: WGSSet\n", "Assigned ReactionSetID: WGSSet\n"]}], "source": ["# Retrieve the reaction sets as a dictionary\n", "reaction_sets = dict(sim.get_ReactionSets())\n", "# Get the WGSSet\n", "import clr\n", "import System\n", "from System.Collections.Generic import Dictionary\n", "\n", "\n", "def cast_to_dotnet(value):\n", "    \"\"\"\n", "    Cast Python data types to .NET types using pythonnet.\n", "\n", "    Conversions:\n", "    - int/float -> System.Double\n", "    - str -> System.String\n", "    - bool -> System.Boolean\n", "    - dict -> System.Collections.Generic.Dictionary<object, object>\n", "    - list -> System.Array[System.Object]\n", "    - None -> null\n", "\n", "    For dictionaries and lists, the function recursively casts all nested elements.\n", "\n", "    Args:\n", "    value: The Python value to be cast.\n", "\n", "    Returns:\n", "    The equivalent .NET type.\n", "\n", "    Raises:\n", "    ValueError: If an unsupported type is encountered.\n", "\n", "    Example:\n", "    >>> python_value = {\"name\": \"<PERSON>\", \"age\": 30, \"scores\": [85, 90, 95]}\n", "    >>> dotnet_value = cast_to_dotnet(python_value)\n", "    >>> type(dotnet_value)\n", "    <class 'System.Collections.Generic.Dictionary[System.Object,System.Object]'>\n", "    >>> type(dotnet_value[\"name\"])\n", "    <class 'System.String'>\n", "    >>> type(dotnet_value[\"age\"])\n", "    <class 'System.Double'>\n", "    >>> type(dotnet_value[\"scores\"])\n", "    <class 'System.Array[System.Object]'>\n", "    \"\"\"\n", "    if isinstance(value, (int, float)):\n", "        return System.Double(value)\n", "    elif isinstance(value, str):\n", "        return System.String(value)\n", "    elif isinstance(value, bool):\n", "        return System.Boolean(value)\n", "    elif isinstance(value, dict):\n", "        dotnet_dict = Dictionary[System.Object, System.Object]()\n", "        for k, v in value.items():\n", "            dotnet_dict[cast_to_dotnet(k)] = cast_to_dotnet(v)\n", "        return dotnet_dict\n", "    elif isinstance(value, list):\n", "        return System.Array[System.Object](list(map(cast_to_dotnet, value)))\n", "    elif value is None:\n", "        return None\n", "    else:\n", "        raise ValueError(f\"Unsupported type: {type(value)}\")\n", "\n", "\n", "# Example usage:\n", "# python_value = {\"name\": \"<PERSON>\", \"age\": 30, \"scores\": [85, 90, 95]}\n", "# dotnet_value = cast_to_dotnet(python_value)\n", "def cast_from_dotnet(value):\n", "    if isinstance(value, System.Double):\n", "        return float(value)\n", "    elif isinstance(value, System.String):\n", "        return str(value)\n", "    elif isinstance(value, System.Boolean):\n", "        return bool(value)\n", "    elif isinstance(\n", "        value, System.Collections.Generic.Dictionary[System.Object, System.Object]\n", "    ):\n", "        return {cast_from_dotnet(k): cast_from_dotnet(v) for k, v in value.items()}\n", "    elif isinstance(value, System.Array):\n", "        return asNumpyArray(value)\n", "    # Add more type conversions as needed\n", "    else:\n", "        return value  # Return as-is if no conversion is defined\n", "\n", "\n", "import ctypes\n", "import numpy as np\n", "import System\n", "from System import Array\n", "from System.Runtime.InteropServices import GCHandle, GCHandleType\n", "\n", "\n", "def as<PERSON><PERSON><PERSON><PERSON>(netArray: System.Array) -> np.ndarray:\n", "    \"\"\"\n", "    Converts a .NET array to a NumPy array.\n", "    \"\"\"\n", "    # Define type mappings\n", "    _MAP_NET_NP = {\n", "        \"Single\": np.float32,\n", "        \"Double\": np.float64,\n", "        \"SByte\": np.int8,\n", "        \"Int16\": np.int16,\n", "        \"Int32\": np.int32,\n", "        \"Int64\": np.int64,\n", "        \"Byte\": np.uint8,\n", "        \"UInt16\": np.uint16,\n", "        \"UInt32\": np.uint32,\n", "        \"UInt64\": np.uint64,\n", "        \"Boolean\": np.bool_,\n", "    }\n", "\n", "    # Get array dimensions and type\n", "    dims = tuple(netArray.GetLength(i) for i in range(netArray.Rank))\n", "    netType = netArray.GetType().GetElementType().Name\n", "\n", "    # Create empty NumPy array with correct type and shape\n", "    try:\n", "        npArray = np.empty(dims, order=\"C\", dtype=_MAP_NET_NP[netType])\n", "    except KeyError:\n", "        raise NotImplementedError(\n", "            f\"asNumpyArray does not support System type {netType}\"\n", "        )\n", "\n", "    # Copy data from .NET array to NumPy array\n", "    sourceHandle = GCHandle.Alloc(netArray, GCHandleType.Pinned)\n", "    try:\n", "        sourcePtr = sourceHandle.AddrOfPinnedObject().ToInt64()\n", "        destPtr = npArray.__array_interface__[\"data\"][0]\n", "        ctypes.memmove(destPtr, sourcePtr, npArray.nbytes)\n", "    finally:\n", "        sourceHandle.Free()\n", "\n", "    return npArray\n", "\n", "\n", "def asNetArray(npArray: np.ndarray) -> System.Array:\n", "    \"\"\"\n", "    Converts a NumPy array to a .NET array.\n", "    \"\"\"\n", "    # Define type mappings\n", "    _MAP_NP_NET = {\n", "        np.dtype(np.float32): System.Single,\n", "        np.dtype(np.float64): System.Double,\n", "        np.dtype(np.int8): System.SByte,\n", "        np.dtype(np.int16): System.Int16,\n", "        np.dtype(np.int32): System.Int32,\n", "        np.dtype(np.int64): System.Int64,\n", "        np.dtype(np.uint8): System.Byte,\n", "        np.dtype(np.uint16): System.UInt16,\n", "        np.dtype(np.uint32): System.UInt32,\n", "        np.dtype(np.uint64): System.UInt64,\n", "        np.dtype(np.bool_): <PERSON><PERSON>Bo<PERSON>,\n", "    }\n", "\n", "    dtype = npArray.dtype\n", "    dims = npArray.shape\n", "\n", "    # Handle complex number arrays\n", "    if dtype in (np.complex64, np.complex128):\n", "        dtype = np.float32 if dtype == np.complex64 else np.float64\n", "        dims += (2,)\n", "        npArray = npArray.view(dtype).reshape(dims)\n", "\n", "    # Ensure array is C-contiguous\n", "    if not npArray.flags.c_contiguous:\n", "        npArray = np.ascontiguousarray(npArray)\n", "\n", "    # Create .NET array with correct type and shape\n", "    try:\n", "        netArray = Array.CreateInstance(_MAP_NP_NET[dtype], *dims)\n", "    except KeyError:\n", "        raise NotImplementedError(f\"asNetArray does not support dtype {dtype}\")\n", "\n", "    # Copy data from NumPy array to .NET array\n", "    destHandle = GCHandle.Alloc(netArray, GCHandleType.Pinned)\n", "    try:\n", "        sourcePtr = npArray.__array_interface__[\"data\"][0]\n", "        destPtr = destHandle.AddrOfPinnedObject().ToInt64()\n", "        ctypes.memmove(destPtr, sourcePtr, npArray.nbytes)\n", "    finally:\n", "        destHandle.Free()\n", "\n", "    return netArray\n", "\n", "\n", "wgs_set = reaction_sets[\"WGSSet\"]\n", "wgs_set_id = wgs_set.ID\n", "# Print details to confirm\n", "print(f\"Description: {wgs_set.Description}\")\n", "print(f\"ID: {wgs_set.ID}\")\n", "print(f\"Name: {wgs_set.Name}\")\n", "# Assign the Water-Gas Shift reaction set to the reactor\n", "RCT_Converison.ReactionSetID = wgs_set_id\n", "# Print confirmation\n", "print(f\"Assigned ReactionSetID: {RCT_Converison.ReactionSetID}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Get ReactionSet"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WGSSet <class 'DWSIM.Interfaces.IReactionSet'>\n"]}], "source": ["pwgs_set = reaction_sets[\"WGSSet\"]\n", "print(pwgs_set, type(pwgs_set))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Others"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["RCT_Converison.ReactorOperationMode = RCT_Converison.ReactorOperationMode.Isothermic"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.0\n", "-2243.310860331585\n", "{'Water-Gas Shift Reaction': 1.0}\n", "{'Toluene': nan, 'Water': 0.7857142857142856, 'Hydrogen': -inf, 'Methane': nan, 'Benzene': nan, 'Nitrogen': nan, 'Ammonia': nan, 'Carbon monoxide': 1.0, 'Carbon dioxide': -inf}\n", "0.0\n", "None\n", "-2243.310860331585\n", "{'Water-Gas Shift Reaction': 1.0}\n", "{'Toluene': nan, 'Water': 0.7857142857142856, 'Hydrogen': -inf, 'Methane': nan, 'Benzene': nan, 'Nitrogen': nan, 'Ammonia': nan, 'Carbon monoxide': 1.0, 'Carbon dioxide': -inf}\n"]}], "source": ["print(RCT_Converison.get_DeltaT())\n", "print(RCT_Converison.get_DeltaQ())\n", "print(dict(RCT_Converison.get_Conversions()))\n", "print(dict(RCT_Converison.get_ComponentConversions()))\n", "print(RCT_Converison.DeltaT)\n", "print(RCT_Converison.DeltaP)\n", "print(RCT_Converison.DeltaQ)\n", "print(dict(RCT_Converison.Conversions))\n", "print(dict(RCT_Converison.ComponentConversions))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## TypeChecking"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'System.Collections.Generic.Dictionary[String,Double]'>\n", "{'Water-Gas Shift Reaction': 1.0}\n"]}], "source": ["conversions = RCT_Converison.Conversions\n", "\n", "print(type(conversions))\n", "print(dict(conversions))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'System.Collections.Generic.Dictionary[String,Double]'>\n", "{'Toluene': nan, 'Water': 0.7857142857142856, 'Hydrogen': -inf, 'Methane': nan, 'Benzene': nan, 'Nitrogen': nan, 'Ammonia': nan, 'Carbon monoxide': 1.0, 'Carbon dioxide': -inf}\n", "=============\n", "Toluene <class 'str'>\n", "nan <class 'float'>\n", "-inf False\n", "nan <PERSON>\n", "=============\n", "Water <class 'str'>\n", "0.7857142857142856 <class 'float'>\n", "-inf False\n", "nan False\n", "=============\n", "Hydrogen <class 'str'>\n", "-inf <class 'float'>\n", "-inf True\n", "nan False\n", "=============\n", "Methane <class 'str'>\n", "nan <class 'float'>\n", "-inf False\n", "nan <PERSON>\n", "=============\n", "Benzene <class 'str'>\n", "nan <class 'float'>\n", "-inf False\n", "nan <PERSON>\n", "=============\n", "Nitrogen <class 'str'>\n", "nan <class 'float'>\n", "-inf False\n", "nan <PERSON>\n", "=============\n", "Ammonia <class 'str'>\n", "nan <class 'float'>\n", "-inf False\n", "nan <PERSON>\n", "=============\n", "Carbon monoxide <class 'str'>\n", "1.0 <class 'float'>\n", "-inf False\n", "nan False\n", "=============\n", "Carbon dioxide <class 'str'>\n", "-inf <class 'float'>\n", "-inf True\n", "nan False\n"]}], "source": ["component_conversions = RCT_Converison.ComponentConversions\n", "\n", "\n", "print(type(component_conversions))\n", "print(dict(component_conversions))\n", "\n", "import math\n", "\n", "for k, v in dict(component_conversions).items():\n", "    print(\"=============\")\n", "    print(k, type(k))\n", "    print(v, type(v))\n", "    print(\"-inf\", math.isinf(v))\n", "    print(\"nan\", math.isnan(v))"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"ename": "DirectoryNotFoundException", "evalue": "Could not find a part of the path \"/code/sandbox/DWSim Builder/Process Models/Reactors/Conversion/Conversion Reactor Test.dwxmz\".\n  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x00164] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize) [0x00000] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int)\n  at System.IO.File.Create (System.String path, System.Int32 bufferSize) [0x00000] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at System.IO.File.Create (System.String path) [0x00000] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at DWSIM.Automation.Flowsheet2.SaveSimulation (System.String path, System.Boolean backup) [0x00059] in <b9e27a2a8bf3484782d02fa21a4c0779>:0 \n  at DWSIM.Automation.Automation3.SaveFlowsheet (DWSIM.Interfaces.IFlowsheet flowsheet, System.String filepath, System.Boolean compressed) [0x00000] in <b9e27a2a8bf3484782d02fa21a4c0779>:0 \n  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)\n  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <a17fa1457c5d44f2885ac746c1764ea5>:0 ", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;*****************************\u001b[0m                Traceback (most recent call last)", "\u001b[0;32m/tmp/ipykernel_69408/3235878204.py\u001b[0m in \u001b[0;36m<cell line: 8>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      6\u001b[0m )\n\u001b[1;32m      7\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 8\u001b[0;31m \u001b[0minterf\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mSaveFlowsheet\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0msim\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfileNameToSave\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;*****************************\u001b[0m: Could not find a part of the path \"/code/sandbox/DWSim Builder/Process Models/Reactors/Conversion/Conversion Reactor Test.dwxmz\".\n  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize, System.Boolean anonymous, System.IO.FileOptions options) [0x00164] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at System.IO.FileStream..ctor (System.String path, System.IO.FileMode mode, System.IO.FileAccess access, System.IO.FileShare share, System.Int32 bufferSize) [0x00000] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at (wrapper remoting-invoke-with-check) System.IO.FileStream..ctor(string,System.IO.FileMode,System.IO.FileAccess,System.IO.FileShare,int)\n  at System.IO.File.Create (System.String path, System.Int32 bufferSize) [0x00000] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at System.IO.File.Create (System.String path) [0x00000] in <a17fa1457c5d44f2885ac746c1764ea5>:0 \n  at DWSIM.Automation.Flowsheet2.SaveSimulation (System.String path, System.Boolean backup) [0x00059] in <b9e27a2a8bf3484782d02fa21a4c0779>:0 \n  at DWSIM.Automation.Automation3.SaveFlowsheet (DWSIM.Interfaces.IFlowsheet flowsheet, System.String filepath, System.Boolean compressed) [0x00000] in <b9e27a2a8bf3484782d02fa21a4c0779>:0 \n  at (wrapper managed-to-native) System.Reflection.RuntimeMethodInfo.InternalInvoke(System.Reflection.RuntimeMethodInfo,object,object[],System.Exception&)\n  at System.Reflection.RuntimeMethodInfo.Invoke (System.Object obj, System.Reflection.BindingFlags invokeAttr, System.Reflection.Binder binder, System.Object[] parameters, System.Globalization.CultureInfo culture) [0x0006a] in <a17fa1457c5d44f2885ac746c1764ea5>:0 "]}], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(\n", "    Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "    \"/code/sandbox/DWSim Builder/Process Models/Reactors/Conversion/Conversion Reactor Test.dwxmz\",\n", ")\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave, True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}