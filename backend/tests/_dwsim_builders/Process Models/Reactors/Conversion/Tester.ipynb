{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code/.venv/lib/python3.8/site-packages/clr_loader/mono.py:180: UserWarning: Hosting Mono versions before v6.12 is known to be problematic. If the process crashes shortly after you see this message, try updating Mono to at least v6.12.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[<ObjectType.MaterialStream: 6>, <ObjectType.RCT_Conversion: 23>, <ObjectType.EnergyStream: 7>, <ObjectType.MaterialStream: 6>, <ObjectType.MaterialStream: 6>]\n", "['1', 'RCONV-1', 'E1', '2', '3']\n"]}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), \"/code/sandbox/DWSim Builder/Process Models/Reactors/Conversion/Conversion Reactor.dwxmz\")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "all_objects = [obj.Value.get_ObjectType() for obj in sim.get_GraphicObjects()]\n", "print(all_objects)\n", "\n", "all_objects_tags = [obj.Value.get_Tag() for obj in sim.get_GraphicObjects()]\n", "print(all_objects_tags)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DefaultSet\n"]}], "source": ["RC_001 = sim.GetObject('RCONV-1')\n", "RC_001 = RC_001.GetAsObject()\n", "print(RC_001.get_ReactionSetID())\n", "x = RC_001.get_ReactionSetID()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_values([<DWSIM.Interfaces.IReactionSet object at 0x7f5107b0b800>, <DWSIM.Interfaces.IReactionSet object at 0x7f5107b0b7c0>])\n", "dict_keys(['DefaultSet', '13d5dc99-7278-4079-8417-5b87cfe91bea'])\n"]}], "source": ["y = dict(sim.get_ReactionSets())\n", "print(y.values())\n", "print(y.keys())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["[<System.Collections.Generic.KeyValuePair[String,IReactionSet] object at 0x7f5107b0bf40>,\n", " <System.Collections.Generic.KeyValuePair[String,IReactionSet] object at 0x7f5107b0bf80>]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["z = list(sim.get_ReactionSets())\n", "z"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Water Gas Shift\n", "DefaultSet\n", "Water Gas Shift \n"]}, {"data": {"text/plain": ["'f707aea3-65d7-4f3a-9d67-4d94467c12a1'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["p = z[0]\n", "q = p.get_Value()\n", "print(q.Description)\n", "print(q.ID)\n", "print(q.Name)\n", "r = list(q.Reactions)\n", "a = r[0].get_Value()\n", "a.get_ReactionID()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}