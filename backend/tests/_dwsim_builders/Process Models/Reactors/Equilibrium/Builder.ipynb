{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/code/.venv/lib/python3.8/site-packages/clr_loader/mono.py:180: UserWarning: Hosting Mono versions before v6.12 is known to be problematic. If the process crashes shortly after you see this message, try updating Mono to at least v6.12.\n", "  warnings.warn(\n"]}, {"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7f0b8d9d03c0>"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "fileNameToLoad = Path.Combine(\n", "    Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "    \"backend/infrastructure/artefacts/bin/dwsim/Empty_Template.dwxmz\",\n", ")\n", "\n", "fileNameToLoad = Path.Combine(\n", "    \"/code/backend/infrastructure/artefacts/bin/dwsim/Empty_Template.dwxmz\"\n", ")\n", "\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToLoad)\n", "\n", "# add compounds\n", "cnames = [\n", "    \"Methanol\",\n", "    \"1-butanol\",\n", "    \"Methyl acetate\",\n", "    \"N-butyl acetate\",\n", "    \"Ethanol\",\n", "    \"Acetic acid\",\n", "    \"Ethyl acetate\",\n", "    \"Water\",\n", "    \"Nitrogen\",\n", "    \"Hydrogen\",\n", "    \"Ammonia\",\n", "    \"Carbon monoxide\",\n", "]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)\n", "\n", "sim.CreateAndAddPropertyPackage(\"Peng<PERSON>Robinson (PR)\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1: mass flow set to 37.04 kg/s'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["one = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"1\")\n", "two = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"2\")\n", "three = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"3\")\n", "E1 = sim.AddObject(ObjectType.EnergyStream, 50, 50, \"E1\")\n", "RCT_Equilibrium = sim.AddObject(\n", "    ObjectType.RCT_Equilibrium, 50, 50, \"Equilibrium reactor\"\n", ")\n", "\n", "one = one.GetAsObject()\n", "two = two.GetAsObject()\n", "three = three.GetAsObject()\n", "E1 = E1.GetAsObject()\n", "RCT_Equilibrium = RCT_Equilibrium.GetAsObject()\n", "\n", "sim.ConnectObjects(one.GraphicObject, RCT_Equilibrium.GraphicObject, -1, -1)\n", "sim.ConnectObjects(E1.GraphicObject, RCT_Equilibrium.GraphicObject, -1, -1)\n", "sim.ConnectObjects(RCT_Equilibrium.GraphicObject, two.GraphicObject, -1, -1)\n", "sim.ConnectObjects(RCT_Equilibrium.GraphicObject, three.GraphicObject, -1, -1)\n", "\n", "sim.AutoLayout()\n", "\n", "one.SetOverallComposition(Array[float]([0, 0.4, 0.6, 0, 0, 0, 0, 0, 0, 0, 0, 0]))\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(303.15)  # K\n", "one.SetPressure(500000)  # Pa\n", "one.<PERSON><PERSON><PERSON><PERSON>(37.04)  # kg/s"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Equilibrium reactor <class 'DWSIM.UnitOperations.Reactors.Reactor_Equilibrium'>\n"]}], "source": ["sim_obj = sim.GetObject(\"Equilibrium reactor\").GetAsObject()\n", "print(sim_obj, type(sim_obj))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["RCT_Equilibrium = sim.AddObject(ObjectType.RCT_Conversion, 50, 50, \"conversion_reactor\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["conversion_reactor <class 'DWSIM.Interfaces.ISimulationObject'>\n", "conversion_reactor <class 'DWSIM.UnitOperations.Reactors.Reactor_Conversion'>\n"]}], "source": ["sim_obj = sim.GetObject(\"conversion_reactor\")\n", "print(sim_obj, type(sim_obj))\n", "\n", "unit_obj = sim_obj.GetAsObject()\n", "print(unit_obj, type(unit_obj))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reactions added to sets:\n", "- DefaultSet: Butyl acetate production\n", "- EsterificationSet: Esterification of Ethanol\n", "- AmmoniaSet: Ammonia Synthesis\n", "- MethanolSet: Methanol Synthesis\n", "\n"]}], "source": ["# https://dwsim.org/api_help/html/M_DWSIM_Interfaces_IFlowsheet_CreateEquilibriumReaction.htm\n", "\n", "# Define stoichiometric coefficients for each reaction\n", "# Reaction 1: Butyl acetate production\n", "comps1 = Dictionary[str, float]()\n", "comps1.Add(\"Methanol\", 1.0)\n", "comps1.Add(\"1-butanol\", -1.0)\n", "comps1.Add(\"Methyl acetate\", -1.0)\n", "comps1.Add(\"N-butyl acetate\", 1.0)\n", "\n", "# Reaction 2: Esterification of Ethanol and Acetic Acid\n", "comps2 = Dictionary[str, float]()\n", "comps2.Add(\"Ethanol\", -1.0)\n", "comps2.Add(\"Acetic acid\", -1.0)\n", "comps2.Add(\"Ethyl acetate\", 1.0)\n", "comps2.Add(\"Water\", 1.0)\n", "\n", "# Reaction 3: Formation of Ammonia\n", "comps3 = Dictionary[str, float]()\n", "comps3.Add(\"Nitrogen\", -1.0)\n", "comps3.Add(\"Hydrogen\", -3.0)\n", "comps3.Add(\"Ammonia\", 2.0)\n", "\n", "# Reaction 4: Synthesis of Methanol from CO and H2\n", "comps4 = Dictionary[str, float]()\n", "comps4.Add(\"Carbon monoxide\", -1.0)\n", "comps4.Add(\"Hydrogen\", -2.0)\n", "comps4.Add(\"Methanol\", 1.0)\n", "\n", "# Create equilibrium reaction objects\n", "\n", "# Reaction 1\n", "kr1 = sim.CreateEquilibriumReaction(\n", "    name=\"Butyl acetate production\",\n", "    description=\"Butyl acetate production test\",\n", "    compounds_and_stoichcoeffs=comps1,\n", "    basecompound=\"1-butanol\",\n", "    reactionphase=\"Liquid\",\n", "    basis=\"Fugacity\",\n", "    units=\"\",\n", "    Tapproach=0,\n", "    lnKeq_fT=\"\",\n", ")\n", "\n", "# Reaction 2\n", "kr2 = sim.CreateEquilibriumReaction(\n", "    name=\"Esterification of Ethanol\",\n", "    description=\"Ethanol + Acetic Acid <-> Ethyl Acetate + Water\",\n", "    compounds_and_stoichcoeffs=comps2,\n", "    basecompound=\"Ethanol\",\n", "    reactionphase=\"Liquid\",\n", "    basis=\"Fugacity\",\n", "    units=\"\",\n", "    Tapproach=0,\n", "    lnKeq_fT=\"\",\n", ")\n", "\n", "# Reaction 3\n", "kr3 = sim.CreateEquilibriumReaction(\n", "    name=\"Ammonia Synthesis\",\n", "    description=\"N2 + 3H2 <-> 2NH3\",\n", "    compounds_and_stoichcoeffs=comps3,\n", "    basecompound=\"Nitrogen\",\n", "    reactionphase=\"Vapor\",\n", "    basis=\"Fugacity\",\n", "    units=\"\",\n", "    Tapproach=0,\n", "    lnKeq_fT=\"\",\n", ")\n", "\n", "# Reaction 4\n", "kr4 = sim.CreateEquilibriumReaction(\n", "    name=\"Methanol Synthesis\",\n", "    description=\"CO + 2H2 <-> CH3OH\",\n", "    compounds_and_stoichcoeffs=comps4,\n", "    basecompound=\"Carbon monoxide\",\n", "    reactionphase=\"Vapor\",\n", "    basis=\"Fugacity\",\n", "    units=\"\",\n", "    Tapproach=0,\n", "    lnKeq_fT=\"\",\n", ")\n", "\n", "# Add reactions to the simulation\n", "\n", "sim.AddReaction(kr1)\n", "sim.AddReaction(kr2)\n", "sim.AddReaction(kr3)\n", "sim.AddReaction(kr4)\n", "\n", "# Check if reaction sets already exist and create them if not\n", "existing_sets = sim.get_ReactionSets()\n", "\n", "if not existing_sets.ContainsKey(\"DefaultSet\"):\n", "    reactionSet1 = sim.CreateReactionSet(\"DefaultSet\", \"Description for Default Set\")\n", "    sim.AddReactionSet(reactionSet1)\n", "else:\n", "    reactionSet1 = existing_sets[\"DefaultSet\"]\n", "\n", "if not existing_sets.ContainsKey(\"EsterificationSet\"):\n", "    reactionSet2 = sim.CreateReactionSet(\n", "        \"EsterificationSet\", \"Description for Esterification Set\"\n", "    )\n", "    sim.AddReactionSet(reactionSet2)\n", "else:\n", "    reactionSet2 = existing_sets[\"EsterificationSet\"]\n", "\n", "if not existing_sets.ContainsKey(\"AmmoniaSet\"):\n", "    reactionSet3 = sim.CreateReactionSet(\n", "        \"AmmoniaSet\", \"Description for Ammonia Synthesis Set\"\n", "    )\n", "    sim.AddReactionSet(reactionSet3)\n", "else:\n", "    reactionSet3 = existing_sets[\"AmmoniaSet\"]\n", "\n", "if not existing_sets.ContainsKey(\"MethanolSet\"):\n", "    reactionSet4 = sim.CreateReactionSet(\n", "        \"MethanolSet\", \"Description for Methanol Synthesis Set\"\n", "    )\n", "    sim.AddReactionSet(reactionSet4)\n", "else:\n", "    reactionSet4 = existing_sets[\"MethanolSet\"]\n", "\n", "# Add reactions to the sets\n", "sim.AddReactionToSet(kr1.ID, \"DefaultSet\", True, 1)\n", "sim.AddReactionToSet(kr2.ID, \"EsterificationSet\", True, 1)\n", "sim.AddReactionToSet(kr3.ID, \"AmmoniaSet\", True, 1)\n", "sim.AddReactionToSet(kr4.ID, \"MethanolSet\", True, 1)\n", "\n", "# Print confirmation of the sets\n", "print(\n", "    f\"Reactions added to sets:\\n\"\n", "    f\"- DefaultSet: {kr1.Name}\\n\"\n", "    f\"- EsterificationSet: {kr2.Name}\\n\"\n", "    f\"- AmmoniaSet: {kr3.Name}\\n\"\n", "    f\"- MethanolSet: {kr4.Name}\\n\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Description: De<PERSON><PERSON> Reaction Set\n", "ID: DefaultSet\n", "Name: De<PERSON><PERSON>\n", "Assigned ReactionSetID: DefaultSet\n"]}], "source": ["# Retrieve the reaction sets as a dictionary\n", "reaction_sets = dict(sim.get_ReactionSets())\n", "# Get the WGSSet\n", "default_set = reaction_sets[\"DefaultSet\"]\n", "default_set_id = default_set.ID\n", "# Print details to confirm\n", "print(f\"Description: {default_set.Description}\")\n", "print(f\"ID: {default_set.ID}\")\n", "print(f\"Name: {default_set.Name}\")\n", "# Assign the Water-Gas Shift reaction set to the reactor\n", "RCT_Equilibrium.ReactionSetID = default_set_id\n", "# Print confirmation\n", "print(f\"Assigned ReactionSetID: {RCT_Equilibrium.ReactionSetID}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "No method matches given arguments for IFlowsheet.GetObject: ()", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[0;32m/tmp/ipykernel_99019/2778010581.py\u001b[0m in \u001b[0;36m<cell line: 1>\u001b[0;34m()\u001b[0m\n\u001b[0;32m----> 1\u001b[0;31m \u001b[0msim\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mGetObject\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;31mTypeError\u001b[0m: No method matches given arguments for IFlowsheet.GetObject: ()"]}], "source": ["sim.GetObject()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rset = default_set\n", "\n", "\n", "prints = {\n", "    \"rset\": rset,\n", "    \"id\": rset.ID,\n", "    \"desc\": rset.Description,\n", "    \"name\": rset.Name,\n", "    \"reactions\": rset.Reactions,\n", "}\n", "\n", "for k, v in prints.items():\n", "    print(f\"\\n{k}: {v}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rset_reactions = prints.get(\"reactions\")\n", "print(dict(rset_reactions))\n", "print(dict(rset_reactions).keys())\n", "\n", "# Key is the reaction Name\n", "\n", "reactionsetbase = list(dict(rset_reactions).values())[0]\n", "print(reactionsetbase, type(reactionsetbase), reactionsetbase.ReactionID)\n", "\n", "reaction = sim.GetReaction(reactionsetbase.ReactionID)\n", "print(reaction, type(reaction), reaction.ReactionBasis)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Lets get the reaction data\n", "\"\"\"\n", "kr1 = sim.CreateEquilibriumReaction(\n", "    name=\"Butyl acetate production\",\n", "    description=\"Butyl acetate production test\",\n", "    compounds_and_stoichcoeffs=comps1,\n", "    basecompound=\"1-butanol\",\n", "    reactionphase=\"Liquid\",\n", "    basis=\"Fugacity\",\n", "    units=\"\",\n", "    Tapproach=0,\n", "    lnKeq_fT=\"\",\n", ")\n", "\"\"\"\n", "\n", "data = {\n", "    \"name\": reaction.Name,\n", "    \"desc\": reaction.Description,\n", "    \"compounds\": {\n", "        stoichbase.CompName: stoichbase.StoichCoeff\n", "        for stoichbase in reaction.Components.values()\n", "    },\n", "    \"stoich\": reaction.StoichBalance,\n", "    \"base\": reaction.BaseReactant,\n", "    \"basis\": reaction.ReactionBasis,\n", "    \"r_phase\": reaction.ReactionPhase,\n", "    \"conversion_expression\": reaction.Expression,\n", "}\n", "\n", "for k, v in data.items():\n", "    print(f\"\\n{k}: {v}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["RCT_Equilibrium.get_ReactionSetID()\n", "print(type(RCT_Equilibrium))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["RCT_Equilibrium.ReactorOperationMode = RCT_Equilibrium.ReactorOperationMode.Isothermic"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(RCT_Equilibrium.DeltaP)\n", "print(RCT_Equilibrium.DeltaT)\n", "print(RCT_Equilibrium.DeltaQ)\n", "print(RCT_Equilibrium.InitialGibbsEnergy)\n", "print(RCT_Equilibrium.FinalGibbsEnergy)\n", "print(dict(RCT_Equilibrium.ComponentConversions))\n", "print(dict(RCT_Equilibrium.ReactionExtents))"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "# fileNameToSave = Path.Combine(\n", "#     Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "#     \"/code/sandbox/DWSim Builder/Process Models/Reactors/Equilibrium/Equilibrium Reactor Test.dwxmz\",\n", "# )\n", "\n", "# interf.SaveFlowsheet(sim, fileNameToSave, True)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# GET"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}