{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["<DWSIM.Interfaces.IPropertyPackage object at 0x7fb353fe4f40>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# import pythoncom\n", "# pythoncom.CoInitialize()\n", "import pytest\n", "import clr\n", "\n", "from System.IO import Directory, Path, File\n", "from System import String, Environment\n", "from System import Array\n", "from System.Collections import ArrayList\n", "from System.Collections.Generic import Dictionary\n", "\n", "dwsimpath = \"/usr/local/lib/dwsim/\"\n", "\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"CapeOpen.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Automation.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON>nce(dwsimpath + \"DWSIM.Interfaces.dll\")\n", "clr.Add<PERSON><PERSON>erence(dwsimpath + \"DWSIM.GlobalSettings.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.SharedClasses.dll\")\n", "clr.<PERSON>d<PERSON><PERSON>erence(dwsimpath + \"DWSIM.Thermodynamics.dll\")\n", "clr.AddR<PERSON>erence(dwsimpath + \"DWSIM.UnitOperations.dll\")\n", "clr.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"DWSIM.Inspector.dll\")\n", "clr.<PERSON>d<PERSON><PERSON><PERSON><PERSON>(dwsimpath + \"System.Buffers.dll\")\n", "\n", "from DWSIM.Interfaces.Enums.GraphicObjects import ObjectType\n", "from DWSIM.Thermodynamics import Streams, PropertyPackages\n", "from DWSIM.UnitOperations import UnitOperations\n", "from DWSIM.UnitOperations import UnitOperations, Reactors\n", "from DWSIM.Automation import Automation3\n", "from DWSIM.GlobalSettings import Settings\n", "\n", "Directory.SetCurrentDirectory(dwsimpath)\n", "# Create an instance of the Automation3 class from the DWSIM.Automation module\n", "# This class provides methods for automating tasks in DWSIM, such as creating and manipulating flowsheets\n", "interf = Automation3()\n", "\n", "# Set the file path of an existing DWSIM flowsheet to be loaded using the Path.Combine method from the System.IO module\n", "# The flowsheet file path is constructed using the Environment.GetFolderPath method to obtain the path to the desktop folder and the relative path to the flowsheet file\n", "# fileNameToLoad = Path.Combine(\n", "#     Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "#     \"/code/sandbox/DWSim Builder/Process Models/Empty_Template.dwxmz\",\n", "# )\n", "\n", "fileNameToSave = Path.Combine(\"/code/backend/artefacts/bin/dwsim/Empty_Template.dwxmz\")\n", "# Load the DWSIM flowsheet using the LoadFlowsheet method of the Automation3 class\n", "# The method takes a single argument, which is the file path of the flowsheet to be loaded\n", "# The method returns a Simulation object that represents the loaded flowsheet\n", "sim = interf.LoadFlowsheet(fileNameToSave)\n", "\n", "# add compounds\n", "cnames = [\"Water\"]\n", "for compound in cnames:\n", "    sim.Add<PERSON><PERSON><PERSON>und(compound)\n", "\n", "sim.CreateAndAddPropertyPackage(\"Steam Tables (IAPWS-IF97)\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["'1: mass flow set to 8.47219 kg/s'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Making some material streams\n", "one = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"1\")\n", "two = sim.AddObject(ObjectType.MaterialStream, 50, 50, \"2\")\n", "E1 = sim.AddObject(ObjectType.EnergyStream, 50, 50, \"E1\")\n", "# Making Air Cooler\n", "AirCooler = sim.AddObject(ObjectType.AirCooler2, 50, 50, \"AC-1\")\n", "\n", "# Getting All Objects\n", "one = one.GetAsObject()\n", "two = two.GetAsObject()\n", "E1 = E1.GetAsObject()\n", "AirCooler = AirCooler.GetAsObject()\n", "\n", "# Connecting the simulation objects.\n", "\n", "# Material Stream Specs\n", "one.SetOverallComposition(Array[float]([1.0]))\n", "one.Set<PERSON><PERSON>per<PERSON>(365.15)  # K\n", "one.SetPressure(120000)  # Pa\n", "one.<PERSON><PERSON><PERSON><PERSON><PERSON>(8.47219)  # kg/s"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setting Tube Layouts"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["AirCooler.CalculationMode = AirCooler.CalcMode.SpecifyGeometry"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["DWSIM.UnitOperations.UnitOperations.AirCooler2"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["type(AirCooler)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["AirCooler.AirInletTemperature = 298.15  # this is in K\n", "AirCooler.AirPressure = 101325  # this is in Pa\n", "AirCooler.Tube_Di = 21.320  # this is in mm it automatically gets multiplied by 1000\n", "AirCooler.Tube_De = 25.400  # this is in mm it automatically gets multiplied by 1000\n", "AirCooler.Tube_Length = 7.561  # this is in m\n", "AirCooler.Tube_Fouling = 0  # this is in K/m2/W\n", "AirCooler.Tube_Roughness = 46  # this is in mm\n", "AirCooler.Tube_ThermalConductivity = 71  # this is in W/[m.K]\n", "AirCooler.Tube_PassesPerShell = 2\n", "AirCooler.Tube_NumberPerShell = 163\n", "AirCooler.Tube_Pitch = 58.500  # this is in mm it automatically gets multiplied by 1000\n", "\n", "AirCooler.ReferenceFanSpeed = 999  # this is rpm\n", "AirCooler.ReferenceAirFlow = 77941  # this is m3/s\n", "AirCooler.ActualFanSpeed = 999  # this is rpm\n", "AirCooler.ElectricalPowerConversionFactor = 0.99  # this is a factor"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Exclusive call to make for air cooler connections\n", "AirCooler.CreateConnectors()\n", "AirCooler.CreateConnectors()\n", "AirCooler.CreateConnectors()\n", "AirCooler.CreateConnectors()\n", "AirCooler.CreateConnectors()\n", "AirCooler.CreateConnectors()\n", "AirCooler.CreateConnectors()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["sim.ConnectObjects(one.GraphicObject, AirCooler.GraphicObject, -1, -1)\n", "sim.ConnectObjects(E1.GraphicObject, AirCooler.GraphicObject, -1, -1)\n", "sim.ConnectObjects(AirCooler.GraphicObject, two.GraphicObject, -1, -1)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["Settings.SolverMode = 0\n", "errors = interf.CalculateFlowsheet2(sim)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["561030509.9641008\n", "1801.0835890771455\n", "298.15\n", "101325.0\n", "299.9323609896261\n", "676711.3228096424\n", "nan\n", "2376.2162933593036\n", "0.0\n", "298.15\n", "101325.0\n", "21.32\n", "25.4\n", "7.561\n", "0.0\n", "46.0\n", "71.0\n", "2\n", "163\n", "58.5\n", "999.0\n", "77941.0\n", "999.0\n", "77941.0\n", "0.99\n", "0.0\n"]}], "source": ["# Now getting the properties of the air cooler\n", "print(AirCooler.PressureDrop)  # Fluid Pressure Drop\n", "print(AirCooler.OutletTemperature)  # Outlet Fluid Temp\n", "print(AirCooler.AirInletTemperature)  # Inlet air temp\n", "print(AirCooler.AirPressure)  # Inlet air press\n", "print(AirCooler.AirOutletTemperature)  # Outlet Air temp\n", "print(AirCooler.OverallUA)  # Overall UA\n", "print(AirCooler.HeatLoad)  # Heat exchange\n", "print(AirCooler.MaxHeatExchange)  # Max heat exchange\n", "print(AirCooler.ExchangerEfficiency)  # Exchanger Eff\n", "\n", "print(AirCooler.AirInletTemperature)  # this is in K\n", "print(AirCooler.AirPressure)  # this is in Pa\n", "print(AirCooler.Tube_Di)  # this is in mm it automatically gets multiplied by 1000\n", "print(AirCooler.Tube_De)  # this is in mm it automatically gets multiplied by 1000\n", "print(AirCooler.Tube_Length)  # this is in m\n", "print(AirCooler.Tube_Fouling)  # this is in K/m2/W\n", "print(AirCooler.Tube_Roughness)  # this is in mm\n", "print(AirCooler.Tube_ThermalConductivity)  # this is in W/[m.K]\n", "print(AirCooler.Tube_PassesPerShell)\n", "print(AirCooler.Tube_NumberPerShell)\n", "print(AirCooler.Tube_Pitch)  # this is in mm it automatically gets multiplied by 1000\n", "\n", "print(AirCooler.ReferenceFanSpeed)  # this is rpm\n", "print(AirCooler.ReferenceAirFlow)  # this is m3/s\n", "print(AirCooler.ActualFanSpeed)  # this is rpm\n", "print(AirCooler.ActualAirFlow)  # air flow\n", "print(AirCooler.ElectricalPowerConversionFactor)  # this is a factor\n", "print(AirCooler.ElectricalPowerLoad)  # power load"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# saving the modified version of file at same path\n", "\n", "fileNameToSave = Path.Combine(\n", "    \"/code/backend/artefacts/bin/dwsim/aggregate_tests/aircoolerbuilder.dwxmz\"\n", ")\n", "# fileNameToSave = Path.Combine(\n", "#     Environment.GetFolderPath(Environment.SpecialFolder.Desktop),\n", "#     \"/code/sandbox/DWSim Builder/Process Models/AirCooler2/AC2 Test.dwxmz\",\n", "# )\n", "\n", "interf.SaveFlowsheet(sim, fileNameToSave, True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 2}