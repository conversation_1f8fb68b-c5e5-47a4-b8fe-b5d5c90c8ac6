import pandas as pd
import uuid
import sklearn
from backend.core._surrogate.transformers.base_transformer import SurrogateDataTransformer
from backend.core._optimizer.optimizers.optimizer_scipy_direct import ScipyDirectOptimizationRunner, allen_config
from backend.core._optimizer.objectivefuncs import SurrogateObjectiveFunction
from backend.core._optimizer.valueobjects import VOOptimizationProblem, VOOptParam, VOOptMetric, VOOptimizationConfig
from backend.core._optimizer.entities import EnumParameterSpaceType, EnumMetrics, EnumDirection, EnumSearchAlgorithm, ENTMetadata
from backend.core._surrogate.models.model_base import PyTorchSurrogateModel
import pathlib
import backend.endpoint.v1.schema as v1endpt
import numpy as np
cur_dir = pathlib.Path(__file__).parent.resolve()

train_dataset = pd.read_csv(cur_dir/"surrogate_comprison_alen_aleph/training_data.csv")
train_dataset = train_dataset.drop(columns=["Unnamed: 0"])
print(train_dataset)



# Get model

# Create DataTransformer
print("Set up Transformer!")
x_cols = ["Case",  "Time", "SNA_RC", "SNA_SF", "SNAt_RC", "SNAt_SSO", "SNAt_MPS", "CGD_AE", "CGD_GR", "CGD_SSO", "CGD_DC"]
y_cols = ["Case",  "Time", "CRY_MF_API", "PSD_D10", "PSD_D50", "PSD_D90"]
transformer = SurrogateDataTransformer(
        uid=uuid.uuid4(),
        df_x=train_dataset[x_cols],
        df_y=train_dataset[y_cols],
        x_scaler=sklearn.preprocessing.StandardScaler(),
        y_scaler=sklearn.preprocessing.MinMaxScaler(feature_range=(1,2)),
        timeset_col='Case',
        timestep_col='Time'
    )

print("Get Serialized Model")

with open(cur_dir/"surrogate_comprison_alen_aleph/exp_res_latest_aleph/model_serialized.txt", "r") as f:
    serialized_str = f.read()

deserialized_model = PyTorchSurrogateModel.deserialize_native_model(serialized_str)
print("Deserialized model!")
surrogate_model = PyTorchSurrogateModel(deserialized_model, transformer)
print("Done!")

# data in experimental data format
experimental_data= pd.read_csv(cur_dir/"model_calibration_aleph/experimental_data.csv")

df_x_partial = experimental_data[x_cols]
df_y_true = experimental_data[y_cols]

objective_fn = SurrogateObjectiveFunction(
    surrogate_model=surrogate_model,
    df_x_partial=df_x_partial,
    df_y_true=df_y_true
)

print("Objective Function Setup!")
PARAMS = [
        VOOptParam(
            label="SNA_RC",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(-50, -10)
        ),
        VOOptParam(
            label="SNA_SF",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(0.1, 1)
        ),
        VOOptParam(
            label="SNAt_RC",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(-50, 50)
        ),
        VOOptParam(
            label="SNAt_SSO",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(0.001, 3)
        ),
        VOOptParam(
            label="SNAt_MPS",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(0.01, 100)
        ),
        VOOptParam(
            label="CGD_AE",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(1E-12,5E4)
        ),
        VOOptParam(
            label="CGD_GR",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(1E-5, 1)
        ),
        VOOptParam(
            label="CGD_SSO",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(1.5, 3)
        ),
        VOOptParam(
            label="CGD_DC",
            type_=EnumParameterSpaceType.FLOAT_UNIFORM,
            min_max_inclusive=(0.01, 5)
        )
    ]
problem_spec = VOOptimizationProblem(
    parameters=PARAMS,
    metrics=[
        VOOptMetric(
            label=EnumMetrics.REG_MSE,
            direction=EnumDirection.MINIMIZE
        ),
        VOOptMetric(
            label=EnumMetrics.TS_MAPE,
            direction=EnumDirection.MAXIMIZE
        )
    ],
    primary_metric=EnumMetrics.REG_MSE
)

print("Optimization Specs done!")

# Configure optimization run
config = VOOptimizationConfig(
    max_trials=50,
    max_concurrent_trials=3,
    timeout_seconds=600,
    strategy=EnumSearchAlgorithm.DIRECT,
    random_seed=42
)

# Create metadata
metadata = ENTMetadata(
    label="Parameter Optimization",
    description="Finding optimal parameters for the surrogate model",
    uid=uuid.uuid4()
)
print("Metdata created and starting to Optimize!")

# Create and run optimizer
runner = ScipyDirectOptimizationRunner(extra_config=allen_config)
result = runner.fit(metadata, config, problem_spec, objective_fn)



# Access results
print(f"Best parameters: {result.best_parameters}")
print(f"Best metrics: {result.best_metrics}")
print(f"Total trials: {result.trials_total}")
print(f"Optimization duration: {result.duration_seconds} seconds")


# print(create a simulation input)
df_x_copy = df_x_partial.copy(deep=True)
for i in result.best_parameters.keys():
    df_x_partial[i] = result.best_parameters[i]
simulated_data = surrogate_model.predict(df_x_partial) 

print(simulated_data)
# calbrated data
calib_data = df_y_true
# simulated data   
print(calib_data)
output_vars = [y for y in y_cols if y!=transformer.timeset_col]

datatransformer = transformer

print({f"{int(t_set)}": {y: list(simulated_data[simulated_data[datatransformer.timeset_col]==t_set][y]) for y in output_vars } 
                            for t_set in list(simulated_data[datatransformer.timeset_col].drop_duplicates())})
print({ f"{int(t_set)}": {y: list(calib_data[calib_data[datatransformer.timeset_col]==t_set][y]) for y in output_vars } 
                            for t_set in list(calib_data[datatransformer.timeset_col].drop_duplicates()) })

chart1 = v1endpt.SimpleChart(
            title = f"Setpoint versus Simulation Plot", 
            pri_data = {f"{int(t_set)}": {y: list(simulated_data[simulated_data[datatransformer.timeset_col]==t_set][y]) for y in output_vars } 
                            for t_set in list(simulated_data[datatransformer.timeset_col].drop_duplicates())},
            sec_data= { f"{int(t_set)}": {y: list(calib_data[calib_data[datatransformer.timeset_col]==t_set][y]) for y in output_vars } 
                            for t_set in list(calib_data[datatransformer.timeset_col].drop_duplicates()) },
            axes = (datatransformer.timeset_col, "Value") ,
            chart_type = "line") 

print(chart1.pri_data, chart1.sec_data)
# FOR CHART 2
x_time = simulated_data["Time"].to_numpy()
# mape transformations
y_data = {y: np.abs((calib_data[y]-simulated_data[y])/calib_data[y])*100 for y in output_vars}

chart2= v1endpt.SimpleChart(
                        title = "Set point vs Prediction Errors",
                        pri_data = {f"{y} Error": {"x": list(x_time), "y": list(y_data[y])} for y in output_vars if y!=datatransformer.timestep_col},
                        axes = ("Time", "Error"),
                        chart_type = "vertical_bar"
                    )
print(chart2.pri_data)

# For table
params = [param.label for param in PARAMS]
old_vals = [df_x_copy[param].iloc[0] for param in params]
new_vals = [result.best_parameters[param] for param in params]
table = v1endpt.SimpleTable(
    title = "New VS Calibrated Value",
    data = {"Parameters": params, "Old Values": old_vals, "New Values": new_vals},
    summary_text = "Comparison in new and Old calibration Values."
        )

print(table.data)