from dash import Dash, html, dcc, Input, Output, callback
import dash_ag_grid as dag
import pandas as pd
import numpy as np
import plotly.express as px
import pathlib
from random import sample
# import os
# from backend.core._surrogate.valueobjects import VOLog_ModelMetrics

cur_path = pathlib.Path(__file__).resolve().parent

training_df_true= pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/test_data.csv").drop(columns=["Unnamed: 0"])
training_df_true["Time"] = training_df_true.eval("Time / 300")
training_df_pred= pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/exp_res_latest_aleph/test_predictions_aleph.csv").drop(columns=["Unnamed: 0"])


def df_bin_plot_comparisons(ground_truth_df, predictions_df, 
                            output_col=["CRY_MF_API", "PSD_D10", "PSD_D50", "PSD_D90"], 
                            timeset_col="Case", timestep_col="Time"):
    ground_truth_df = ground_truth_df.sort_values([timeset_col, timestep_col], ascending=[True, True])
    predictions_df = predictions_df.sort_values([timeset_col, timestep_col], ascending=[True, True]) 
    n_timeset = len(ground_truth_df["Case"].unique())
    n_timestep = len(ground_truth_df["Time"].unique())
    true_data_array = ground_truth_df[output_col].to_numpy().reshape( n_timeset, n_timestep, -1)
    pred_data_array = predictions_df[output_col].to_numpy().reshape( n_timeset, n_timestep, -1)
    mape_data = np.mean(np.abs(true_data_array-pred_data_array)/true_data_array, axis=(0,1))
    mape_ = {output_col[i]: round(mape_data[i]*100, 3) for i in range(len(mape_data))}
    
    return  px.bar(None, x=list(mape_.keys()), y=list(mape_.values()), 
                      title="Percentage Error in Predicting Response Variables")


def df_comparison(ground_truth_df, predictions_df, 
                  target_cols=["CRY_MF_API", "PSD_D10", "PSD_D50", "PSD_D90"], 
                  timeset_col="Case", timestep_col="Time"):
    ground_truth_df = ground_truth_df.sort_values(by=timestep_col, ascending=True)
    predictions_df = predictions_df.sort_values(by=timestep_col, ascending=True) 
    
    n_timeset = len(ground_truth_df[timeset_col].unique())
    n_timestep = len(ground_truth_df[timestep_col].unique())

    timesteps = pd.DataFrame(np.array([i for i in range(n_timestep)]).reshape(-1,1), columns=[timestep_col])

    residuals = 100*np.abs((ground_truth_df[target_cols].to_numpy() - predictions_df[target_cols].to_numpy())/(ground_truth_df[target_cols].to_numpy())).reshape(n_timestep, n_timeset, -1)
    
    for i in range(len(target_cols)):
        target_cols[i] = target_cols[i] + "\tMAPE"
    residual_values = pd.DataFrame(np.mean(residuals, axis=1), columns=target_cols)
    data = pd.concat([timesteps, residual_values], axis=1)    
    return data

def df_comparison_time_window(ground_truth_df, predictions_df, 
                  target_col="CRY_MF_API", 
                  timeset_col="Case", timestep_col="Time",
                  time_range=[1,20], sample_amount=200):
    # filter based on time interval
    ground_truth_df = ground_truth_df.query(f"{timestep_col} >= {time_range[0]} & {timestep_col} <= {time_range[1]}")
    predictions_df = predictions_df.query(f"{timestep_col} >= {time_range[0]} & {timestep_col} <= {time_range[1]}")
    # sample timesets at random
    sampled_timesets = set(sample(list(ground_truth_df[timeset_col].unique()),
                              sample_amount))
    # Ground Truth column creation
    ground_truth_df = ground_truth_df[ground_truth_df["Case"].isin(sampled_timesets)]
    ground_truth_df["Actual Value"] = ground_truth_df[target_col]
    # Predictions column creation
    predictions_df = predictions_df[predictions_df["Case"].isin(sampled_timesets)]
    predictions_df["Predicted Value"] = predictions_df[target_col]

    data = pd.concat([ground_truth_df["Actual Value"], predictions_df["Predicted Value"]], axis=1)
     
    return data
# def getMetrics(ground_truth_df, predictions_df, 
#                target_cols=["CRY_MF_API", "PSD_D10", "PSD_D50", "PSD_D90"], 
#                   timeset_col="Case", timestep_col="Time"):
#     # function working has not been tested. Unable to run dash web app using docker, need to configure ports.
    
#     ground_truth_df = ground_truth_df.sort_values(by=timestep_col, ascending=True)
#     predictions_df = predictions_df.sort_values(by=timestep_col, ascending=True) 

#     metrics = dict(VOLog_ModelMetrics.create_from_predictions(ground_truth_df[target_cols].to_numpy(), 
#                                                               predictions_df[target_cols].to_numpy(), 
#                                                               response_var_labels=target_cols))
#     del_cols = []
#     for i in metrics.keys():
#         if isinstance(metrics[i], list) and len(metrics[i])==len(metrics['variable_names']):                        
#             continue
#         else:
#             del_cols.append(i)
        
#     for i in del_cols:
#         del metrics[i]

#     return pd.DataFrame(metrics)


app = Dash()

comparison_df = df_comparison_time_window(training_df_true, training_df_pred)
# metrics_df = getMetrics(training_df_true, training_df_pred)

target_cols=["CRY_MF_API", "PSD_D10", "PSD_D50", "PSD_D90"]

# metrics_grid = dag.AgGrid(id="model-metrics-tables",
#                                 rowData=metrics_df.to_dict("records"), 
#                                 columnDefs=[{'field': i} for i in metrics_df.columns ],
#                                 scrollTo={"columnPosition":"auto"},
#                                 dashGridOptions = {"domLayout": "autoHeight"}
#                                 )


app.layout = [html.H1("Model Review"),
              dcc.Graph(figure=df_bin_plot_comparisons(training_df_true, training_df_pred)),
              html.Div([
                  dcc.Dropdown(target_cols, target_cols[0], id="property-drop-down"),
                  dcc.Graph(figure=px.scatter(comparison_df, x="Actual Value", y="Predicted Value",title="Prediction VS Actual"), id="comparison-fig"),
                  dcc.RangeSlider(training_df_true["Time"].min(), training_df_true["Time"].max(), value = [training_df_true["Time"].drop_duplicates().sort_values()[0], 
                                                                                                           training_df_true["Time"].drop_duplicates().sort_values()[5]],
                                                                                                           id="range-time")
                  
                  ])
]
                        
              
            #   metrics_grid,]


@callback(
    Output('comparison-fig', 'figure'),
    Input('property-drop-down', 'value'),
    Input('range-time', 'value')
)
def update_graph(dropdown_value, time_range):
    comparison_df = df_comparison_time_window(training_df_true, training_df_pred, target_col=dropdown_value, time_range=time_range)
    return px.scatter(comparison_df, x="Actual Value", y="Predicted Value")




if __name__ == '__main__':
    app.run(debug=True)
