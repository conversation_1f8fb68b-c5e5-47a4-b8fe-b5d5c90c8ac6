import sys
import uuid
import unittest
import copy
import logging
import time
from typing import Dict, List, Any, Optional, Tuple, Literal
from pprint import pprint, pformat
import pathlib
cur_dir = pathlib.Path(__file__).parent.resolve()

import numpy as np
import numpy.typing as npt
import pandas as pd


import pytest
import torch
import torch.nn as nn
import optuna
import sklearn
# Import the split_data_simple utility
print(str(cur_dir.parent.parent.parent.parent.parent))
sys.path.append(str(cur_dir.parent.parent.parent.parent.parent))
#from backend.core._surrogate.entities import ENTTrainingJob
from backend.core._surrogate.models.model_base import PyTorchSurrogateModel
from backend.core._surrogate.transformers.base_transformer import SurrogateDataTransformer
from backend.core._surrogate._enums import EnumSurrogateAlgorithm, EnumTrainingStatus
from backend.core._surrogate.valueobjects import *
from backend.core._surrogate.entities import ENTTrainingJob
from backend.core._surrogate.trainers.trainer_rnn import Seq2SeqTSTrainer
#training metadata
meta_data =  VOMetadata_General(
        label="Test Surrogate Model",
        user_reference="test_user",
        atlas_reference="test_atlas",
        surrogate_algo=EnumSurrogateAlgorithm.RNN_TS,
        description="Test metadata for surrogate model"
    )

# model config
model_config_params = [
        VOConfig_Parameter(
            name=Hyperparams.NN.HIDDEN_SIZE,
            value=1024,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="integer",
                min_value=16,
                max_value=2048
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.SEQ_NUM_LAYERS,
            value=1,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="integer",
                min_value=1,
                max_value=3
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.DENSE_NUM_LAYERS,
            value=10,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="integer",
                min_value=1,
                max_value=20
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.RNN_TYPE,
            value="GRU",
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="categorical",
                choices=["LSTM", "GRU"]
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.DROPOUT,
            value=0.01,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="continuous",
                min_value=0.0,
                max_value=0.5
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.BATCH_SIZE,
            value=32,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="categorical",
                choices=[8, 16, 32, 64]
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.LEARNING_RATE,
            value=0.0001,
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="continuous_logscale",
                min_value=1e-5,
                max_value=1e-2
            )
        ),
        VOConfig_Parameter(
            name=Hyperparams.NN.LOSS_FUNCTION,
            value="mse",
            tunable=True,
            bounds=VOConfig_ParamBounds(
                type="categorical",
                choices=["mse", "mae"]
            )
        )
    ]

model_config =VOConfig_Model(
                        algorithm=EnumSurrogateAlgorithm.RNN_TS,
                        parameters=model_config_params
                        )

training_config = VOConfig_Training(
        primary_metric=EnumMetricName.RMSE,
        max_iterations=150,
        random_seed=42,
        validation_strategy="simple_split",
        validation_split=0.2,
        enable_early_stopping=False,
        early_stopping_rounds=5,
        early_stopping_min_delta=0.001
    )

def create_timesteps(row, memo={}):
    if row["Case"] not in memo.keys():
        memo[row["Case"]] = {row["Unnamed: 0"]: 0}
    else:
        memo[row["Case"]][row["Unnamed: 0"]] = memo[row["Case"]][row["Unnamed: 0"]-1] + 300
    row["Time"] = memo[row["Case"]][row["Unnamed: 0"]] 
    return row

test_dataset = pd.read_csv(cur_dir/"surrogate_comprison_alen_aleph/test_data.csv")
test_dataset = test_dataset.drop(columns=["Unnamed: 0"])
print(test_dataset)
train_dataset = pd.read_csv(cur_dir/"surrogate_comprison_alen_aleph/training_data.csv")
train_dataset = train_dataset.drop(columns=["Unnamed: 0"])
print(train_dataset)
val_dataset = pd.read_csv(cur_dir/"surrogate_comprison_alen_aleph/val_data.csv")
val_dataset = val_dataset.drop(columns=["Unnamed: 0"])
print(val_dataset)

x_cols = ["Case",  "Time","SNA_RC", "SNA_SF", "SNAt_RC", "SNAt_SSO", "SNAt_MPS", "CGD_AE", "CGD_GR", "CGD_SSO", "CGD_DC"]
y_cols = ["Case",  "Time","CRY_MF_API", "PSD_D10", "PSD_D50", "PSD_D90"]

print("Set up Transformer!")
transformer = SurrogateDataTransformer(
        uid=uuid.uuid4(),
        df_x=train_dataset[x_cols],
        df_y=train_dataset[y_cols],
        x_scaler=sklearn.preprocessing.StandardScaler(),
        y_scaler=sklearn.preprocessing.MinMaxScaler(feature_range=(1,2)),
        timeset_col='Case',
        timestep_col='Time'
    )
print("Set up Job!")
job = ENTTrainingJob(
            metadata= meta_data,
            training_configuration=training_config,
            model_configuration=model_config,
            status=EnumTrainingStatus.PENDING.value
    )
print("Set up Trainer!")
trainer = Seq2SeqTSTrainer()
print(print("Create Datasets!"))
# train
arr_x, arr_y = transformer.transform(train_dataset[x_cols], train_dataset[y_cols])
t_dataset = VODataset(
    arr_x=arr_x,
    arr_y=arr_y,
    transformer_uid=transformer.uid, 
    colnames_x=transformer._initial_x_variable_cols,
    colnames_y=transformer._initial_y_variable_cols,
    pattern="sequence"
    )
# val
arr_x, arr_y = transformer.transform(val_dataset[x_cols], val_dataset[y_cols])
v_dataset = VODataset(
    arr_x=arr_x,
    arr_y=arr_y, 
    transformer_uid=transformer.uid, 
    colnames_x=transformer._initial_x_variable_cols,
    colnames_y=transformer._initial_y_variable_cols,
    pattern="sequence"
    )


#native_model = trainer._train_with_validation(
#    train_set=t_dataset,
#    val_set=v_dataset,
#    training_config=training_config,
#    model_config=model_config,
#    job=job
#)
print("Get Serialized Model")

with open(cur_dir/"surrogate_comprison_alen_aleph/exp_res_latest_aleph/model_serialized.txt", "r") as f:
    serialized_str = f.read()

deserialized_model = PyTorchSurrogateModel.deserialize_native_model(serialized_str)
print("Deserialized model!")
surrogate_model = PyTorchSurrogateModel(deserialized_model, transformer)
print("Done!")
surrogate_model.predict(train_dataset[x_cols]).to_csv(cur_dir/"train_predictions_aleph.csv")
print("Done!")
surrogate_model.predict(val_dataset[x_cols]).to_csv(cur_dir/"val_predictions_aleph.csv")
print("Done!")
surrogate_model.predict(test_dataset[x_cols]).to_csv(cur_dir/"test_predictions_aleph.csv")
print("Done!")