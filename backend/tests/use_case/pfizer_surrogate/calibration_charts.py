from dash import Dash, html, dcc, Input, Output, callback
import dash_ag_grid as dag
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
import pathlib

cur_path = pathlib.Path(__file__).resolve().parent

plant_data = pd.read_csv(cur_path/"model_calibration_aleph/experimental_data.csv")
opt_sim_data = pd.read_csv(cur_path/"model_calibration_aleph/final_output.csv")


def plant_data_vs_simulation(plant_data: pd.DataFrame,
                             optimal_simulation: pd.DataFrame,
                             property: str):
    x_plant = optimal_simulation["Time"].to_numpy()
    y_plant = plant_data[property].to_numpy()
    y_simulation = optimal_simulation[property].to_numpy()

    return x_plant, y_plant, y_simulation

app = Dash()

target_cols=["CRY_MF_API", "PSD_D90"]
    
app.layout = [html.H1("Model Calibration Results"),
              html.Div([
                  dcc.Dropdown(target_cols, target_cols[0], id="property-drop-down"),
                  html.H3("Simulation Vs Setpoint Trends"),
                  dcc.Graph(id="comparison-fig"),
                  html.H3("Calibration Accuracy"),
                  dcc.Graph(id="time_step-comparison-fig"),
                  
                  ])
]
    






@app.callback(
    Output("comparison-fig", "figure"),
    Input("property-drop-down", "value"))
def graph_update(value):
    x_plant, y_plant, y_simulation = plant_data_vs_simulation(plant_data, opt_sim_data, value)
    
    
    fig = go.Figure() 
    
    fig.add_trace(go.Scatter(x=x_plant, y=y_plant, marker = go.scatter.Marker(size=10), mode="markers", name="Batch Plant Data"))
    fig.add_trace(go.Scatter(x=x_plant, y=y_simulation, line=go.scatter.Line(color="orange"), mode="lines", name="Simulation Data")) 
    fig.update_layout(
        xaxis=dict(title=dict(text="Time")),
        yaxis=dict(title=dict(text=f"{value}"))
    )

    return fig

@app.callback(
    Output("time_step-comparison-fig", "figure"),
    Input("property-drop-down", "value"))
def graph_update(value):
    x_plant, y_plant, y_simulation = plant_data_vs_simulation(plant_data, opt_sim_data, value)
    
    y_mape = np.abs((y_plant-y_simulation)/y_plant)*100

    fig = go.Figure() 
    
    fig.add_trace(go.Bar(x=x_plant, y=y_mape,  name="MAPE per time-step"))

    fig.add_trace(go.Scatter(x=x_plant, y=np.array([2.5 for i in range(len(x_plant))]), name="Threshold"))
    fig.update_layout(
        xaxis=dict(title=dict(text="Time")),
        yaxis=dict(title=dict(text=f"{value}_MAPE"))
    )
    

    return fig


if __name__ == '__main__':
    app.run(debug=True)