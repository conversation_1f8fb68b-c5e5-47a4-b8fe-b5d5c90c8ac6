'''
To compare the `VOLog_ModelMetrics` generated from the same data between our Production model and the Research Model.

- ensure same dataset is used
- ensure that `RNNConfigs` is correctly set for the production model
- for research model you might need to save it as a csv and then import it

'''
import pandas as pd
import pathlib
cur_path = pathlib.Path(__file__).parent.resolve()
import numpy as np
from backend.core._surrogate.valueobjects import VOLog_ModelMetrics
def test_compare_models(
        df_y_actual:pd.DataFrame,
        df_y_research:pd.DataFrame,
        df_y_production:pd.DataFrame,
        cols:list
):      
       
        research_metrics = dict(VOLog_ModelMetrics.create_from_predictions(df_y_actual[cols].to_numpy(), df_y_research[cols].to_numpy(), response_var_labels=cols))
        research_metrics["Group"] = ["Research" for i in range(len(research_metrics['variable_names']))]
        production_metrics = dict(VOLog_ModelMetrics.create_from_predictions(df_y_actual[cols].to_numpy(), df_y_production[cols].to_numpy(), response_var_labels=cols))
        production_metrics["Group"] = ["Production" for i in range(len(research_metrics['variable_names']))]
        del_cols = []
        for i in research_metrics.keys():
                if isinstance(research_metrics[i], list) and len(research_metrics[i])==len(research_metrics['variable_names']):
                        continue
                else:
                        del_cols.append(i)
        
        for i in del_cols:
                del research_metrics[i]
                del production_metrics[i]
        
        return pd.concat([pd.DataFrame(research_metrics), pd.DataFrame(production_metrics)]) 

if __name__=="__main__":
        train_dataset = pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/training_data.csv")
        train_dataset = train_dataset.drop(columns=["Unnamed: 0"])
        print("Testing!")
        true_df = pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/test_data.csv").drop(columns=["Unnamed: 0"])
        research_df= pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/test_preds.csv").drop(columns=["Unnamed: 0"])
        production_df= pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/exp_res_latest_aleph/test_predictions_aleph.csv").drop(columns=["Unnamed: 0"])
        fin_df = test_compare_models(df_y_actual=true_df, df_y_research=research_df, df_y_production=production_df, 
                            cols=["CRY_MF_API", "PSD_D10", "PSD_D50", "PSD_D90"])
        fin_df.to_csv("metrics_testing.csv")
        print("Validation!")
        true_df = pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/val_data.csv").drop(columns=["Unnamed: 0"])
        research_df= pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/val_preds.csv").drop(columns=["Unnamed: 0"])
        production_df= pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/exp_res_latest_aleph/val_predictions_aleph.csv").drop(columns=["Unnamed: 0"])
        fin_df = test_compare_models(df_y_actual=true_df, df_y_research=research_df, df_y_production=production_df, 
                            cols=["CRY_MF_API", "PSD_D10", "PSD_D50", "PSD_D90"])
        fin_df.to_csv("metrics_val.csv")
        print("Training!")
        true_df = pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/training_data.csv").drop(columns=["Unnamed: 0"])
        research_df= pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/training_preds.csv").drop(columns=["Unnamed: 0"])
        production_df= pd.read_csv(cur_path/"surrogate_comprison_alen_aleph/exp_res_latest_aleph/train_predictions_aleph.csv").drop(columns=["Unnamed: 0"])
        fin_df = test_compare_models(df_y_actual=true_df, df_y_research=research_df, df_y_production=production_df, 
                            cols=["CRY_MF_API", "PSD_D10", "PSD_D50", "PSD_D90"])
        fin_df.to_csv("metrics_training.csv")
        


