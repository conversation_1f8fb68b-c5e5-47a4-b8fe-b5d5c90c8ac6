####################
import numpy as np
import math
import copy
import logging
import os
import random
from pathlib import Path
from collections import defaultdict
from typing import (
    Any,
    Callable,
    Dict,
    Generator,
    List,
    Optional,
    Tuple,
    Type,
    Union,
    TypeVar,
)
from pprint import pformat, pprint
import numpy as np
import pandas as pd
from tabulate import tabulate
import backend.core._sharedutils.Utilities as sharedutils
import backend.core._atlas.aggregates as at

import backend.core._matrix.matrix_dwsim as ma
import backend.application.usecase_templates as eg
import backend.infrastructure._db.repo as re


########################

repo = re.FolderRepoForDWSim("backend/artefacts/bin/dwsim", "usecase_tests")


class NaturalGasBoilerTest:

    # UTILITIES

    def print_df(self, df: pd.DataFrame, title: str = ""):
        print(f"\n\n{'#'* (len(title) + 10)}\n{title.upper()}")
        print(f"\n\nCol Names:\n{pformat(df.columns.tolist())}")
        print(f"\n\nSamples:\n{df.head(5)}")

    def generate_names(self, df: pd.DataFrame, delim: str = "_"):
        """
        Helper Function
        Takes a DF and genreates a dict of the entity and variable strings
        """
        colnames = df.columns.to_list()
        data = defaultdict(set)

        for col_name in colnames:
            entity_str, var_str = [term.strip() for term in col_name.split(delim)]
            data["entity_strings"].add(entity_str)
            data["var_strings"].add(var_str)

        print(f"{'#' * 30}\n GENERATE NAMES")
        print(pformat(data))

    # METHODS

    def import_data(self, filename: str) -> pd.DataFrame:
        curr_directory = Path(__file__).resolve().parent
        filepath = curr_directory / filename
        # df = pd.read_csv(filepath)
        df = pd.read_excel(filepath, "LO")

        # print(df.head())
        pprint(df.columns.tolist())
        return df

    def drop_cols(self, df: pd.DataFrame, term: str):

        # Remove all columns with name "Unnamed"
        invalid_cols = [
            col_name
            for col_name in df.columns.tolist()
            if str(col_name).upper().find(term.upper()) != -1
        ]
        print(f"Dropping Columns:\n{pformat(invalid_cols)}")
        df = df.drop(invalid_cols, axis=1)
        return df

    # RUN SIM STUFF

    def get_id_and_var(self, col_name: str) -> Tuple[str, at.ContVarSpecEnum]:
        # Split by _ and strip whitespace in front and back
        entity_str, var_str = [term.strip() for term in col_name.split("_")]

        # Lookup
        entity_lookup = {
            "Air Flow": ("Air BL", "M-100"),
            "Benzene Toluene Feed": ("Benzene Toluene Feed BL", "HEX-101"),
            "Fuel Flow": ("Fuel BL", "M-100"),
            "HEX-100": "HEX-100",
            "HEX-101": "HEX-101",
            "Oil Feed": ("Oil Feed BL", "HEX-100"),
            "To Stack": ("HEX-101", "valve"),
        }

        var_lookup = {
            "Global HTC": at.ContVarSpecEnum.GlobalHeatTransferCoefficient,
            "Heat Capacity (Vapor)": None,
            "Heat Load": at.ContVarSpecEnum.HeatExchange,
            "Heat Loss": at.ContVarSpecEnum.HeatLoss,
            "Mass Flow": at.ContVarSpecEnum.Mass_flow_rate,
            "Pressure": at.ContVarSpecEnum.Pressure,
            "Temp": at.ContVarSpecEnum.Temperature,
            "Temperature": at.ContVarSpecEnum.Temperature,
        }

        return entity_lookup[entity_str], var_lookup[var_str]

    def get_entity(
        self, atlas: at.AtlasRoot, entity_name: Union[str, tuple]
    ) -> at.ENTBase:

        entity = None
        if isinstance(entity_name, str):
            entity = atlas.get_equipment(by_label=entity_name)
        elif isinstance(entity_name, tuple):
            entity = atlas.get_stream(by_equipments=entity_name)

        if entity is None:
            raise KeyError(f"{entity_name} not found")
        return entity

    def run_sim(
        self,
        df_config: pd.DataFrame,
        df_expected: pd.DataFrame,
        n_rows: int = 10,
        to_save: bool = False,
    ) -> pd.DataFrame:

        if n_rows <= df_expected.shape[0]:
            df_config = df_config.iloc[:n_rows].copy()
            df_expected = df_expected.iloc[:n_rows].copy()

        df_computed = pd.DataFrame(index=df_expected.index)

        for idx, row in df_config.iterrows():
            assert isinstance(idx, int)

            # LOAD MODEL
            atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

            # SET DATA
            for col_name, val in row.items():
                assert isinstance(col_name, str)
                entity_name, var_enum = self.get_id_and_var(col_name)
                entity = self.get_entity(atlas, entity_name)
                entity.set_value(var_enum, val)

            # RUN SIM
            matrix = ma.MatrixDWSim(f"natural-gas-boiler_run_20250203_noRank-{idx:03d}")
            matrix.attach_model(atlas, share_model_state=True)
            matrix.setup_matrix()
            matrix.run_matrix()
            if to_save == True:
                repo.save(matrix)

            # GET DATA
            if idx not in df_expected.index:
                raise ValueError(f"{idx} not found in df_results. Check")

            row_results = df_expected.loc[idx]
            for col_name in df_expected.columns:
                val_expected = row_results[col_name]
                e_name, v_enum = self.get_id_and_var(col_name)
                e = self.get_entity(atlas, e_name)

                if v_enum is not None:
                    val_computed = e.get_value(v_enum)
                else:
                    val_computed = val_expected

                assert isinstance(val_computed, (int, float))
                assert isinstance(val_expected, (int, float))

                df_computed.loc[idx, f"{col_name}_from-model"] = val_computed
                df_computed.loc[idx, f"{col_name}_match"] = (
                    1 if math.isclose(val_expected, val_computed) else 0
                )

            self.print_df(df_computed, f"DF Computed, {idx+1} / {df_computed.shape[0]}")

        # MERGE DF TOGETHER. Ensure rows are aligned.
        return pd.concat([df_config, df_expected, df_computed], axis=1)

    def analyse_df(self, df: pd.DataFrame) -> pd.DataFrame:

        correct_rows = df.loc[
            df.filter(like="_match").sum(axis=1) == (df.filter(like="_match").shape[1])
        ]
        percent_correct = (len(correct_rows) / len(df)) * 100
        df["all_match"] = (
            df.filter(like="_match").sum(axis=1) == df.filter(like="_match").shape[1]
        ).astype(int)

        report = {
            "Total Rows": len(df),
            "Correct Rows": len(correct_rows),
            "Percent Correct": percent_correct,
        }
        report_df = pd.DataFrame([report])  # convert to DataFrame for saving/printing

        return report_df

    # GO

    def run(self, filename: str, n_rows: int, save: bool) -> Dict[str, Any]:
        df = self.import_data(filename)

        # CLEAN DATA
        drop_terms = ["Unnamed", "5", "Price", "KPI"]
        for term in drop_terms:
            df = self.drop_cols(df, term)
        self.generate_names(df)
        self.print_df(df, "DF Master")

        # SPLIT DATA
        n_cols = len(df.columns.tolist())
        df_config, df_expected = df.iloc[:, : n_cols - 6], df.iloc[:, n_cols - 6 :]
        self.print_df(df_config, "DF Config")
        self.print_df(df_expected, "DF Expected")

        # RUN SIM
        df_run = self.run_sim(df_config, df_expected, n_rows, save)

        # ANALYSE
        df_report = self.analyse_df(df_run)

        # SAVE
        curr_directory = Path(__file__).resolve().parent
        df_report.to_csv(curr_directory / "report.csv", index=False)
        df_run.to_csv(curr_directory / "results.csv", index=False)

        print(tabulate(df_report, headers="keys", tablefmt="psql"))  # type: ignore

        return {
            "df_run": df_run,
            "df_report": df_report,
            "percent_correct": df_report["Percent Correct"].iloc[0],
        }


def test_get_kpi_from_collection():
    atlas = eg.industrial_natural_gas_boiler_with_preheating_trains()

    kpis = atlas.kpi_collection.items
    assert len(kpis) == 1

    kpi_label = kpis.pop().label

    uid = atlas.kpi_collection.get_uid(kpi_label)
    kpi2 = atlas.kpi_collection.get_item(uid)
    val = kpi2.get_kpi_value()
    assert val is not None

    print(f"kpi: \n{kpi2}")


def test_natural_gas_boiler_spreadsheet():
    test = NaturalGasBoilerTest()
    result = test.run("matrix_validation.xlsx", 3, True)
    assert result["percent_correct"] > 90, "Simulation accuracy is below 90%"


if __name__ == "__main__":
    test = NaturalGasBoilerTest()
    df = test.run("matrix_validation.xlsx", 3, True)

# NOTE SUGGESTED REFACTOR
"""
class DataImporter:
    def __init__(self, filename: str):
        self.filename = filename

    def import_data(self) -> pd.DataFrame:
        curr_directory = Path(__file__).resolve().parent
        filepath = curr_directory / self.filename
        df = pd.read_excel(filepath, "LO")
        return df

    def drop_cols(self, df: pd.DataFrame, terms: List[str]) -> pd.DataFrame:
        invalid_cols = [col for col in df.columns if any(term in col for term in terms)]
        return df.drop(invalid_cols, axis=1)

class SimulationRunner:
    def __init__(self, atlas_factory: Callable[[], at.AtlasRoot]):
        self.atlas_factory = atlas_factory

    def run_simulation(self, df_config: pd.DataFrame, df_expected: pd.DataFrame, n_rows: int, save: bool) -> pd.DataFrame:
        if n_rows <= df_expected.shape[0]:
            df_config = df_config.iloc[:n_rows].copy()
            df_expected = df_expected.iloc[:n_rows].copy()

        df_computed = pd.DataFrame(index=df_expected.index)

        for idx, row in df_config.iterrows():
            atlas = self.atlas_factory()
            for col_name, val in row.items():
                entity_name, var_enum = self.get_id_and_var(col_name)
                entity = self.get_entity(atlas, entity_name)
                entity.set_value(var_enum, val)

            matrix = ma.MatrixDWSim(f"natural-gas-boiler_run_{idx:03d}")
            matrix.attach_model(atlas, share_model_state=True)
            matrix.setup_simulation()
            matrix.run_simulation()
            if save:
                repo.save(matrix)

            for col_name in df_expected.columns:
                val_expected = df_expected.at[idx, col_name]
                entity_name, var_enum = self.get_id_and_var(col_name)
                entity = self.get_entity(atlas, entity_name)
                val_computed = entity.get_value(var_enum) if var_enum else val_expected
                df_computed.at[idx, f"{col_name}_from-model"] = val_computed
                df_computed.at[idx, f"{col_name}_match"] = int(math.isclose(val_expected, val_computed))

        return pd.concat([df_config, df_expected, df_computed], axis=1)

    def get_id_and_var(self, col_name: str) -> Tuple[str, at.ContVarSpecEnum]:
        entity_str, var_str = [term.strip() for term in col_name.split("_")]
        entity_lookup = {
            "Air Flow": ("Air BL", "M-100"),
            "Benzene Toluene Feed": ("Benzene Toluene Feed BL", "HEX-101"),
            "Fuel Flow": ("Fuel BL", "M-100"),
            "HEX-100": "HEX-100",
            "HEX-101": "HEX-101",
            "Oil Feed": ("Oil Feed BL", "HEX-100"),
            "To Stack": ("HEX-101", "valve"),
        }
        var_lookup = {
            "Global HTC": at.ContVarSpecEnum.GlobalHeatTransferCoefficient,
            "Heat Capacity (Vapor)": None,
            "Heat Load": at.ContVarSpecEnum.HeatExchange,
            "Heat Loss": at.ContVarSpecEnum.HeatLoss,
            "Mass Flow": at.ContVarSpecEnum.Mass_flow_rate,
            "Pressure": at.ContVarSpecEnum.Pressure,
            "Temp": at.ContVarSpecEnum.Temperature,
            "Temperature": at.ContVarSpecEnum.Temperature,
        }
        return entity_lookup[entity_str], var_lookup[var_str]

    def get_entity(self, atlas: at.AtlasRoot, entity_name: Union[str, tuple]) -> at.ENTBase:
        if isinstance(entity_name, str):
            return atlas.get_equipment(by_label=entity_name)
        elif isinstance(entity_name, tuple):
            return atlas.get_stream(by_equipments=entity_name)
        raise KeyError(f"{entity_name} not found")

class ResultAnalyzer:
    def analyze(self, df: pd.DataFrame) -> pd.DataFrame:
        correct_rows = df.loc[df.filter(like="_match").sum(axis=1) == df.filter(like="_match").shape[1]]
        percent_correct = (len(correct_rows) / len(df)) * 100
        df["all_match"] = (df.filter(like="_match").sum(axis=1) == df.filter(like="_match").shape[1]).astype(int)
        report = {
            "Total Rows": len(df),
            "Correct Rows": len(correct_rows),
            "Percent Correct": percent_correct,
        }
        return pd.DataFrame([report])

class NaturalGasBoilerTest:
    def __init__(self, filename: str, atlas_factory: Callable[[], at.AtlasRoot]):
        self.data_importer = DataImporter(filename)
        self.simulation_runner = SimulationRunner(atlas_factory)
        self.result_analyzer = ResultAnalyzer()

    def run(self, n_rows: int, save: bool) -> Dict[str, Any]:
        df = self.data_importer.import_data()
        df = self.data_importer.drop_cols(df, ["Unnamed", "5", "Price", "KPI"])
        self.data_importer.generate_names(df)
        n_cols = len(df.columns)
        df_config, df_expected = df.iloc[:, : n_cols - 6], df.iloc[:, n_cols - 6 :]
        df_run = self.simulation_runner.run_simulation(df_config, df_expected, n_rows, save)
        df_report = self.result_analyzer.analyze(df_run)
        curr_directory = Path(__file__).resolve().parent
        df_report.to_csv(curr_directory / "report.csv", index=False)
        df_run.to_csv(curr_directory / "results.csv", index=False)
        print(tabulate(df_report, headers="keys", tablefmt="psql"))
        return {
            "df_run": df_run,
            "df_report": df_report,
            "percent_correct": df_report["Percent Correct"].iloc[0]
        }

def test_natural_gas_boiler_spreadsheet():
    test = NaturalGasBoilerTest("matrix_validation.xlsx", eg.industrial_natural_gas_boiler_with_preheating_trains)
    result = test.run(3, True)
    assert result["percent_correct"] > 90, "Simulation accuracy is below 90%"

if __name__ == "__main__":
    test = NaturalGasBoilerTest("matrix_validation.xlsx", eg.industrial_natural_gas_boiler_with_preheating_trains)
    result = test.run(3, True)


"""
