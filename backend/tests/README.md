# Test Standards

## Test Organization and Structure

- **Class-Based Organization**: Group related tests into classes that focus on specific components or behaviors (e.g., `TestDataFiltering`, `TestTransformationFunctionality`).

- **Descriptive Method Names**: Use clear, descriptive method names that explain exactly what aspect is being tested (e.g., `test_basic_filtering`, `test_out_of_range_filtering`).

- **Documentation**: Include docstrings for test classes and methods that explain the purpose and scope of the tests.

- **Helper Functions**: Extract common setup, data generation, and validation logic into reusable helper functions to keep test methods clean and focused.

### Example

```python
# Example of well-organized tests with helper functions

def verify_transform_result(result, expected_shape):
    """Helper function to verify transformation results."""
    assert result.shape == expected_shape, f"Expected shape {expected_shape}, got {result.shape}"
    return True
    
class TestDataProcessor:
    """Tests for the DataProcessor component functionality."""
    
    def test_basic_processing(self):
        """Test the basic processing functionality with standard inputs."""
        # Test implementation...
        print("Basic processing test passed")
    
    def test_empty_input_handling(self):
        """Test how the processor handles empty inputs."""
        # Test implementation...
        print("Empty input handling test passed")
```

## Test Implementation

- **Simple Method Structure**: Write tests as simple class methods without relying on framework-specific decorators to maintain readability and simplicity.

- **Arrange-Act-Assert Pattern**: Structure tests to first set up the test data, then perform the action being tested, and finally verify the expected outcomes.

- **Explicit Assertions**: Include clear assertion messages that explain what's being verified, making test failures easier to diagnose.

- **Print Statements**: Add print statements at the end of tests to indicate successful completion during direct execution.

### Example

```python
# Example of Arrange-Act-Assert pattern with explicit assertions

def test_config_validation(self):
    """Test validation of configuration objects."""
    # Arrange: Set up test data
    sample_config = {
        'title': 'Test Configuration',
        'parameters': {
            'value': [5.0, 10.0],
            'range': [0.0, 15.0]
        }
    }
    
    # Act: Perform the action being tested
    config_object = ConfigProcessor(**sample_config)
    
    # Assert: Verify the expected outcomes
    assert config_object.title == sample_config['title'], "Title should match input"
    assert config_object.parameters.value == sample_config['parameters']['value'], "Parameters should match input"
    assert len(config_object.parameters.value) == 2, "Should have 2 parameter values"
    
    print("Configuration validation test passed")
```

## Test Coverage and Validation

- **Edge Cases**: Specifically test edge cases and error conditions (e.g., empty inputs, invalid data, boundary values) in dedicated test methods.

- **Comprehensive Coverage**: Test both positive scenarios (expected behavior) and negative scenarios (error handling and validation).

- **Parameter Variations**: Test functions with different parameter combinations to ensure robust behavior across inputs.

- **Direct Execution Support**: Include a simple runner at the end of test files to allow them to be executed directly, making it easier to run and debug individual test files.

### Example

```python
# Example of edge case testing and direct execution support

class TestEdgeCases:
    """Tests for edge cases and error handling."""
    
    def test_empty_dataset(self):
        """Test with empty input dataset."""
        empty_data = []
        processor = DataProcessor()
        
        # Test empty input handling
        result = processor.process(empty_data)
        assert result is not None, "Should handle empty input gracefully"
        assert len(result) == 0, "Result should be empty but defined"
        print("Empty dataset test passed")
    
    def test_invalid_input(self):
        """Test with invalid input that should raise an exception."""
        invalid_data = "not-a-list"
        processor = DataProcessor()
        
        # Expect an exception for invalid input
        try:
            processor.process(invalid_data)
            assert False, "Should have raised TypeError for invalid input"
        except TypeError:
            # This is the expected behavior
            pass
        print("Invalid input test passed")

# Simple runner for direct execution
if __name__ == "__main__":
    print("Running all tests...")
    
    edge_tests = TestEdgeCases()
    edge_tests.test_empty_dataset()
    edge_tests.test_invalid_input()
    
    print("All tests completed successfully!")
```

# Validation Standards for Value Objects

## Pydantic Validation Best Practices

Value Objects represent immutable data structures with built-in validation rules. For consistent implementation across our codebase, follow these standards when implementing validation in Pydantic models:

### Field Definitions

- **Use `Field` for All Attributes**: Always use `Field()` with descriptive parameters to enhance documentation.
  ```python
  name: str = Field(description="Human-readable identifier for the parameter")
  ```

- **Include Comprehensive Descriptions**: Each field should have a clear description that documents:
  - Purpose of the field
  - Valid formats or constraints
  - Default behavior if applicable
  
  ```python
  min_max_inclusive: Optional[Tuple[Union[int, float], Union[int, float]]] = Field(
      default=None, 
      description="Inclusive min and max bounds for numeric parameters. Required for numeric types."
  )
  ```

### Validator Implementation

1. **Model Validators for Multi-Field Interactions**
   - Use `@model_validator(mode='after')` for validations involving multiple fields
   - Decompose complex validation into private helper methods for clarity
   - Return the validated values object

   ```python
   @model_validator(mode='after')
   def validate_parameter_consistency(self) -> 'ModelClass':
       """Validate consistency between related fields"""
       self._validate_type_specific_constraints()
       self._validate_numeric_constraints()
       return self
   ```

2. **Field Validators for Single-Field Validation**
   - Use `@field_validator` only for simple validations specific to one field
   - Reference fields directly by name, not through the info parameter
   - Return the validated value

   ```python
   @field_validator('items')
   def validate_items_not_empty(cls, v: List[str]) -> List[str]:
       """Ensure the items list is not empty"""
       if not v:
           raise ValueError("Items list cannot be empty")
       return v
   ```

3. **Avoid Using `info` Parameter**
   - The `info` parameter in validators is implementation-specific and may change
   - Access other fields through direct attribute references in model validators
   - For field validators that need to reference other fields, consider using a model validator instead

4. **Avoid Single-Error Termination**
    - When doing validation, do not return a Error for each clause. Instead, accumuate all errors into a simple log and return that at the end
    ```python
    @model_validator(mode='after')
    def validate_parameter_consistency(self) -> 'OptimizationParameter':
        """Ensure parameter configuration is consistent with its type"""
        error_log = []
        
        # Numeric types validation
        if self.type in [ParameterType.FLOAT, ParameterType.INTEGER]:
            if self.min_max_inclusive is None:
                error_log.append(f"Numeric parameter '{self.name}' requires min_max_inclusive")
            if self.options is not None:
                error_log.append(f"Numeric parameter '{self.name}' should not have options")
        
        # Categorical types validation
        if self.type == ParameterType.CATEGORICAL:
            if not self.options:
                error_log.append(f"Categorical parameter '{self.name}' requires non-empty options")
            if self.min_max_inclusive is not None:
                error_log.append(f"Categorical parameter '{self.name}' should not have min_max_inclusive")
        
        # Raise consolidated exception if errors were found
        if error_log:
            raise ValueError(f"Parameter '{self.name}' has multiple validation errors: {'; '.join(error_log)}")
        
        return self
    ```

### Example Implementation

```python
class OptimizationParameter(BaseModel):
    """Parameter definition for optimization with built-in validation"""
    
    name: str = Field(description="Unique identifier for the parameter")
    type: ParameterType = Field(
        description="Type of parameter (numeric, categorical, etc.)"
    )
    min_max_inclusive: Optional[Tuple[float, float]] = Field(
        default=None, 
        description="Min and max bounds for numeric parameters"
    )
    options: Optional[List[Any]] = Field(
        default=None,
        description="Available options for categorical parameters"
    )
    
    model_config = ConfigDict(extra="forbid")

    @model_validator(mode='after')
    def validate_parameter_consistency(self) -> 'OptimizationParameter':
        """Ensure parameter configuration is consistent with its type"""
        error_log = []
        
        # Numeric types validation
        if self.type in [ParameterType.FLOAT, ParameterType.INTEGER]:
            if self.min_max_inclusive is None:
                error_log.append(f"Numeric parameter '{self.name}' requires min_max_inclusive")
            if self.options is not None:
                error_log.append(f"Numeric parameter '{self.name}' should not have options")
        
        # Categorical types validation
        if self.type == ParameterType.CATEGORICAL:
            if not self.options:
                error_log.append(f"Categorical parameter '{self.name}' requires non-empty options")
            if self.min_max_inclusive is not None:
                error_log.append(f"Categorical parameter '{self.name}' should not have min_max_inclusive")
        
        # Raise consolidated exception if errors were found
        if error_log:
            raise ValueError(f"Parameter '{self.name}' has multiple validation errors: {'; '.join(error_log)}")
        
        return self
    
    
    @field_validator('min_max_inclusive')
    def validate_bounds(cls, v: Optional[Tuple[float, float]]) -> Optional[Tuple[float, float]]:
        """Validate that min_max_inclusive has valid bounds"""
        error_log = []
        if v is not None:
            min_val, max_val = v
            if min_val > max_val:
                error_log.append(f"Minimum value {min_val} cannot be greater than maximum value {max_val}")

        if error_log:
            raise ValueError(f"Parameter '{self.name}' has multiple validation errors: {'; '.join(error_log)}")

        return v
```

