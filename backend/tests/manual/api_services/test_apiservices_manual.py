import logging
from dotenv import load_dotenv
from os.path import join, dirname

import pytest
from backend.application.api_services import APIService
from backend.endpoint.v1.schema.config_dtos import PlantConfiguration


"""Integration tests for APIService with local database access.

This test module allows testing the APIService layer using a real database running 
locally in a Docker container instead of mocks. It provides a way to verify 
actual data persistence and retrieval.

Setup:
    1. Copy the `.env` file to this directory
    2. Ensure the DB_HOST in .env is set to 'localhost'
    3. Make sure the required Docker containers are running

Note:
    - These tests are marked as 'manual' as they require external dependencies
    - They should not run as part of the automated CI pipeline
    - They are useful for local development and debugging
    - Examples are given below

Todo:
    * Add proper teardown to clean test data
"""

@pytest.fixture(scope="module")
def api_service():
    """Provides configured APIService instance with local DB connection.
    
    Requires:
        - .env file in test directory
        - DB_HOST=localhost in .env
        - Running Docker containers
    """
    
    logging.info("Loading ENV...")
    try:
        dotenv_path = join(dirname(__file__), '.env')
        load_success = load_dotenv(dotenv_path=dotenv_path, override=True)
        if not load_success: 
            raise FileNotFoundError(f"Please ensure there is a .env file here: {dotenv_path}")
        logging.info("Loaded ENV ✅")
    except Exception as e:
        logging.error(f"Failed to load ENV ❌ | {e}")

    return APIService()

# ==================================
# TEST PRESETS
# def test_get_equipment_presets(api_service):
#     api_service.get_equipment_types_and_defaults("<testconfig>", "<testuser>")


# ==================================
# TEST UPDATE PLANT CONFIG WITH GPROMS EQUIPMENT
# def test_update_plant_config(api_service):
#     plant_configuration = """
#     {
#     "user_industry": "Pharmaceuticals",
#     "matrix_engine": "GRPROMS",
#     "is_template": false,
#     "equipments": [
#         {
#         "equipmentId": "test",
#         "equipmentType": "SimulationDuration",
#         "selections": [],
#         "setpoints": [
#             {
#             "title": "Simulation Duration",
#             "value": 0,
#             "bounds": [
#                 0,
#                 10000000000
#             ],
#             "unit": "s"
#             }
#         ],
#         "conditions": [],
#         "reactions": null
#         }
#     ],
#     "connections": [],
#     "materials": [],
#     "reactions": []
#     }"""
#     plant_config_obj = PlantConfiguration.model_validate_json(plant_configuration)
#     api_service.update_plant_configuration("<testconfig>", plant_config_obj, "<testuser>")


# ==================================
# TEST GET PLANT CONFIG AFTER UPDATE
# def test_get_plant_config(api_service):
#     api_service.get_plant_configuration("<testconfig>", "<testuser>")
