{
    "name": "sonic:dwsim84-postgre",
    "image": "lennardong/sonic:dwsim84-postgre",
    "forwardPorts": [
        3000
    ],
    "containerEnv": {
        "PYTHONPATH": "/code",
        "PROJECT_ROOT": "/code"
    },
    "mounts": [
        "source=${localWorkspaceFolder},target=/code,type=bind,consistency=cached",
        "source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind"
    ],
    "workspaceFolder": "/code",
    "customizations": {
        "vscode": {
            "extensions": [
                "ms-python.python",
                "esbenp.prettier-vscode",
                "ms-python.vscode-pylint",
                "ms-python.black-formatter",
                "ms-python.debugpy",
                "ms-python.vscode-pylance",
                "ms-toolsai.jupyter",
                "ms-azuretools.vscode-docker"
            ],
            "settings": {
                "terminal.integrated.defaultProfile.linux": "bash",
                "python.analysis.typeCheckingMode": "standard",
                "python.testing.pytestPath": "pytest",
                "python.venvPath": "${workspaceFolder}/.venv",
                "python.defaultInterpreterPath": "${workspaceFolder}/.venv/bin/python",
                "python.terminal.activateEnvInCurrentTerminal": true,
                "python.venvFolders": [
                    "${workspaceFolder}/.venv"
                ],
                "[python]": {
                    "editor.codeActionsOnSave": {
                        "source.organizeImports": true
                    },
                    "editor.defaultFormatter": "ms-python.black-formatter",
                    "editor.formatOnSave": true
                }
            }
        }
    },
    "features": {
        "docker-from-docker": "latest"
    },
    "runArgs": [
        "--network=host"
    ],
    // "postCreateCommand": "/bin/bash -c 'poetry config virtualenvs.in-project true && poetry install --no-root && sudo apt-get update && sudo apt-get install -y docker-compose'",
    // "postStartCommand": "/bin/bash -c '\
    // if [ -f /code/.devcontainer/welcome-message.sh ]; then \
    //     /code/.devcontainer/welcome-message.sh; \
    // else \
    //     echo \"Welcome message script not found\"; \
    // fi \
    // && poetry shell'",
    // "remoteUser": "root"
    "postCreateCommand": "/bin/bash -c 'poetry config virtualenvs.in-project true && poetry install --no-root && sudo apt-get update && sudo apt-get install -y docker-compose && chmod +x /code/.devcontainer/dwsim_patch.sh && /code/.devcontainer/dwsim_patch.sh'",
    "postStartCommand": "/bin/bash -c '\
        if [ -f /code/.devcontainer/welcome-message.sh ]; then \
            /code/.devcontainer/welcome-message.sh; \
        else \
            echo \"Welcome message script not found\"; \
        fi && \
        poetry shell'",
    "remoteUser": "root"
}
