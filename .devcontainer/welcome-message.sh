#!/bin/bash

cat << "EOF"

[<PERSON><PERSON><PERSON> Developer Toolkit]

##################################
#._____.._____._..._._____._____.#
#/..___||.._..|.\.|.|_..._/..__.\#
#\.`--..|.|.|.|..\|.|.|.|.|./..\/#
#.`--..\|.|.|.|...`.|.|.|.|.|....#
#/\__/./\.\_/./.|\..|_|.|_|.\__/\#
#\____/..\___/\_|.\_/\___/.\____/#
#................................#
#................................#
##################################

SONIC 
Accelerated Environment Creation for Python Development. 

INGREDIENTS

- `Poetry` for project dependency management
- `Pyenv` for python version isolation
- `Docker(hub)` for containerization and image sharing
- `devcontainer.json` for VSCode initialization
- for more details, see: https://github.com/AlephTechAi/sonic

REMINDERS FOR TERMINAL USAGE:

- use `poetry shell` to activate the poetry features for any terminal commands
- to install new packages, use `poetry add`

MODULE PROBLEMS?

Occasionally, Sonic / Poetry caches need a refresh:
`poetry cache clear PYPI --all`
`poetry install`

EOF
