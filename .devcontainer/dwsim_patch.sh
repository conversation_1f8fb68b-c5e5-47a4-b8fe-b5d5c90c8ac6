#!/bin/bash

# Update package list and install necessary packages
apt-get update && apt-get install -y \
    locales \
    language-pack-en \
    libcanberra-gtk-module \
    libcanberra-gtk3-module \
    libnotify4 \
    wget \
    && rm -rf /var/lib/apt/lists/* 

# Configure locale
locale-gen en_SG.UTF-8 
update-locale LANG=en_SG.UTF-8 LC_ALL=en_SG.UTF-8 

# Setup Fira font
mkdir -p /usr/local/share/fonts/fira 
wget https://github.com/mozilla/Fira/raw/master/ttf/FiraSans-Regular.ttf -P /usr/local/share/fonts/fira/ 
chmod 644 /usr/local/share/fonts/fira/* 
fc-cache -f -v 

# Set environment variables for locale
export LANG=en_SG.UTF-8 
export LC_ALL=en_SG.UTF-8 

echo "Setup complete!"
