#!/bin/bash

# Function to clean up background processes
cleanup() {
  echo "Stopping backend and frontend..."
  kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
  wait $BACKEND_PID $FRONTEND_PID 2>/dev/null
  echo "Processes stopped."
}

# Trap SIGINT (Ctrl+C) and SIGTER<PERSON> to run the cleanup function
trap cleanup SIGINT SIGTERM

# Start the FastAPI backend
uvicorn backend.main:app --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!

# Start the Dash frontend
python frontend/app.py &
FRONTEND_PID=$!

# Wait for both processes to finish
wait $BACKEND_PID $FRONTEND_PID
