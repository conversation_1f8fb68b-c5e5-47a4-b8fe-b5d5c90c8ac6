services:
  backend_db:
    env_file:
      - .env
    image: ${ACR_NAME}.azurecr.io/backend_db:${IMAGE_TAG}
    ports:
      - "5432:5432"
    volumes:
      - /data/postgres:/var/lib/postgresql/data
    networks:
      - aleph
  backup:
    env_file:
      - .env
    image: ${ACR_NAME}.azurecr.io/backup:${IMAGE_TAG}
    volumes:
      - /data/backups:/backups
    depends_on:
      - backend_db
    networks:
      - aleph
    restart: "no"
  backend_webserver:
    env_file:
      - .env
    image: ${ACR_NAME}.azurecr.io/backend:${IMAGE_TAG}
    ports:
      - "8000:8000"
    # volumes:
    #   - ./backend:/code/backend
    networks:
      - aleph
    depends_on:
      - backend_db
    healthcheck:
      test: ["CMD", "curl", "-f", "http://0.0.0.0:8000/api/v1/presets/stream-types"]
      interval: 30s
      timeout: 5s
      retries: 5
      start_period: 15s
    volumes:
      - /data/logs:/backend/logs
  frontend_webserver:
    env_file:
      - .env
    image: ${ACR_NAME}.azurecr.io/frontend:${IMAGE_TAG}
    ports:
      - "8050:8050"
    depends_on:
      backend_webserver:
        condition: service_healthy
    networks:
      - aleph
  celery:
    env_file:
      - .env
    image: ${ACR_NAME}.azurecr.io/celery:${IMAGE_TAG}
    depends_on:
      redis:
        condition: service_started
      backend_webserver: 
        condition: service_healthy
    networks:
      - aleph
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    networks:
      - aleph

networks:
  aleph:
    name: aleph

volumes:
  postgres_data:
  postgres_backup:
