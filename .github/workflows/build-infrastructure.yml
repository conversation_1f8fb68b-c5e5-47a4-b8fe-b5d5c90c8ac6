name: 'Manual Azure Infra Provision'

on:
  # push: 
  #   branches: 
  #     - '**'
  workflow_dispatch:
    inputs:
      location:
        description: 'Azure Region (e.g., eastus, westeurope)'
        required: false
        default: 'southeastasia'
        type: string
      resource_group_name:
        description: 'Name of the Resource Group'
        required: true
        type: string
      environment:
        description: 'Environment to deploy to'
        required: true
        type: string
        default: 'uat'

jobs:
  terraform:
    permissions:
      contents: read
      actions: write 
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    defaults: 
      run: 
        working-directory: ./deployment
    env:
      # Azure Service Principal credentials
      ARM_CLIENT_ID: ${{ secrets.ARM_CLIENT_ID }}
      ARM_CLIENT_SECRET: ${{ secrets.ARM_CLIENT_SECRET }}
      ARM_SUBSCRIPTION_ID: ${{ secrets.ARM_SUBSCRIPTION_ID }}
      ARM_TENANT_ID: ${{ secrets.ARM_TENANT_ID }}
      # Variable replacements that override /deployment/variables.tf
      TF_VAR_location: ${{ inputs.location }}
      TF_VAR_resource_group_name: ${{ inputs.resource_group_name }}
      TF_VAR_environment: ${{ inputs.environment }}
      TF_VAR_prefect_vm_username: ${{ secrets.PREFECT_VM_USERNAME }}
      TF_VAR_prefect_vm_password: ${{ secrets.PREFECT_VM_PASSWORD }}
      TF_VAR_app_vm_username: ${{ secrets.APP_VM_USERNAME }}
      TF_VAR_app_vm_password: ${{ secrets.APP_VM_PASSWORD }}
    outputs: 
      key_vault_name: ${{ steps.terraform_outputs.outputs.key_vault_name }}
    
    steps:
      # Get access token to be able to write GitHub variables
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}

      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Validate Required Secrets
        run: | 
          missing_secrets=()

          if [[ -z $TF_VAR_prefect_vm_username ]]; then
            missing_secrets+=("PREFECT_VM_USERNAME")
          fi

          if [[ -z $TF_VAR_prefect_vm_password ]]; then
            missing_secrets+=("PREFECT_VM_PASSWORD")
          fi

          if [[ -z $TF_VAR_app_vm_username ]]; then
            missing_secrets+=("APP_VM_USERNAME")
          fi

          if [[ -z $TF_VAR_app_vm_password ]]; then
            missing_secrets+=("APP_VM_PASSWORD")
          fi

          if [ ${#missing_secrets[@]} -ne 0 ]; then
            echo "::error::Missing or empty required secrets in environment '${{ inputs.environment }}': ${missing_secrets[*]}"
            echo "::error::Please add these secrets in GitHub Repository Settings > Environments > ${{ inputs.environment }}"
            exit 1
          fi

          echo "::notice::Validation passed!"

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
      
      - name: Terraform Init
        run: |
          terraform init \
            -backend-config="resource_group_name=${{ secrets.TF_RESOURCE_GROUP_NAME }}" \
            -backend-config="storage_account_name=${{ secrets.TF_STORAGE_ACCOUNT_NAME }}" \
            -backend-config="container_name=${{ secrets.TF_CONTAINER_NAME }}" \
            -backend-config="key=${{ inputs.environment }}/terraform.tfstate"
      
      - name: Terraform Format
        run: terraform fmt

      - name: Debug Template Rendering
        run: |
          echo "::group::Template Content"
          cat ./templates/app-server-config.tpl
          echo "::endgroup::"
      
      - name: Terraform Validate
        run: terraform validate

      - name: Terraform Plan
        run: terraform plan -out=tfplan
        
      - name: Terraform Apply
        run: terraform apply -auto-approve tfplan

      - name: Save Terraform Outputs
        id: terraform_outputs
        run: |
          gh auth login --with-token <<< "${{ steps.app-token.outputs.token }}"
          KEY_VAULT_NAME=$(terraform output -raw key_vault_name)
          gh variable set KEY_VAULT_NAME --body "$KEY_VAULT_NAME" --env ${{ inputs.environment }}
          ACR_NAME=$(terraform output -raw acr_name)
          gh variable set ACR_NAME --body "$ACR_NAME" --env ${{ inputs.environment }}
          APP_SERVER_PIP=$(terraform output -raw app_server_pip)
          gh secret set APP_SERVER_PIP --body "$APP_SERVER_PIP" --env ${{ inputs.environment }}
          PREFECT_SERVER_PIP=$(terraform output -raw prefect_server_pip)
          gh secret set PREFECT_SERVER_PIP --body "$PREFECT_SERVER_PIP" --env ${{ inputs.environment }}
          SERVER_IDENTITY_CLIENT_ID=$(terraform output -raw server_identity_client_id)
          gh secret set SERVER_IDENTITY_CLIENT_ID --body "$SERVER_IDENTITY_CLIENT_ID" --env ${{ inputs.environment }}

      - name: Output Run Details
        run: |
          echo "Deployment completed by: ${{ github.actor }}"
          echo "Deployment time (UTC): $(date -u '+%Y-%m-%d %H:%M:%S')"
          echo "Resource Group: ${{ inputs.resource_group_name }}"
          echo "Location: ${{ inputs.location }}"
          echo "Environment: ${{ inputs.environment }}"