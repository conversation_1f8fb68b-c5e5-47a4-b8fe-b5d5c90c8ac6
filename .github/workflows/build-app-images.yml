name: 'Build App Images'

on:
  # push: 
  #   branches: 
  #     - '**'
  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string
      image_tag: 
        required: true
        type: string
      ref: 
        required: true
        type: string
      matrix_config: 
        required: false
        type: string
        default: >-
          [   
            {
              "service": "backend",
              "dockerfile": "backend/app.dockerfile",
              "context": "."
            },
            {
              "service": "backend_db",
              "dockerfile": "backend/infrastructure/_db/postgre.dockerfile",
              "context": "."
            },
            {
              "service": "frontend",
              "dockerfile": "frontend/app.dockerfile",
              "context": "."
            },
            {
              "service": "celery",
              "dockerfile": "frontend/celery.dockerfile",
              "context": "."
            },
            {
              "service": "backup",
              "dockerfile": "backend/infrastructure/_db/backup.dockerfile",
              "context": "."
            },
            {
              "service": "dwsim_aci",
              "dockerfile": "backend/infrastructure/_orchaestration/dwsim-aci.dockerfile",
              "context": "."
            },
          ]
  workflow_call:
    inputs: 
      environment: 
        required: true
        type: string
      image_tag: 
        required: true
        type: string
      ref: 
        required: true
        type: string
      matrix_config:
        required: false
        type: string
        default: >-
          [   
            {
              "service": "backend",
              "dockerfile": "backend/app.dockerfile",
              "context": "."
            },
            {
              "service": "backend_db",
              "dockerfile": "backend/infrastructure/_db/postgre.dockerfile",
              "context": "."
            },
            {
              "service": "frontend",
              "dockerfile": "frontend/app.dockerfile",
              "context": "."
            },
            {
              "service": "celery",
              "dockerfile": "frontend/celery.dockerfile",
              "context": "."
            },
            {
              "service": "backup",
              "dockerfile": "backend/infrastructure/_db/backup.dockerfile",
              "context": "."
            },
            {
              "service": "dwsim_aci",
              "dockerfile": "backend/infrastructure/_orchaestration/dwsim-aci.dockerfile",
              "context": "."
            },
          ]
            
jobs:
  build-app-images: 
    uses: ./.github/workflows/build-images.yml
    secrets: inherit
    with: 
      environment: ${{ inputs.environment }}
      image_tag: ${{ inputs.image_tag }}  # Static for now, to change depending on workflow
      ref: ${{ inputs.ref }}
      matrix_config: ${{ inputs.matrix_config }}

  build-ACI-worker-image: 
    uses: ./.github/workflows/build-images.yml
    secrets: inherit
    with: 
      environment: ${{ inputs.environment }}
      image_tag: "latest"
      ref: ${{ inputs.ref }}
      matrix_config: >-
        [
          {
            "service": "dwsim_aci",
            "dockerfile": "backend/infrastructure/_orchaestration/dwsim-aci.dockerfile",
            "context": "."
          }
        ]