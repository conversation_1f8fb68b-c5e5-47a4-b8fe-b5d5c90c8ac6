name: Build images

on:
  # push: 
  #   branches: 
  #     - '**'
  workflow_call:
    inputs: 
      environment: 
        required: true
        type: string
      image_tag: 
        required: true
        type: string
      ref: 
        required: true
        type: string
      matrix_config:
        required: true
        type: string
    
jobs:
  build:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    strategy: 
      fail-fast: false
      matrix: 
        include: ${{ fromJSON(inputs.matrix_config) }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - name: Login to Azure
        uses: azure/login@v2
        with:
          creds: '{"clientId":"${{ secrets.ARM_CLIENT_ID }}","clientSecret":"${{ secrets.ARM_CLIENT_SECRET }}","subscriptionId":"${{ secrets.ARM_SUBSCRIPTION_ID }}","tenantId":"${{ secrets.ARM_TENANT_ID }}"}'
          
      - name: Build ${{ matrix.service }} Image
        run: |
          az acr build -r ${{ vars.ACR_NAME }} \
            -t ${{ matrix.service }}:${{ inputs.image_tag }} \
            -f ${{ matrix.dockerfile }} \
            ${{ matrix.context }}