# Pipeline Documentation

## Purpose
This pipeline architecture enables continuous deployment with clear separation between infrastructure, orchestration (Prefect), and application layers. It's designed to:
- Maintain infrastructure as code using Terraform
- Support multiple environments (dev/uat/prod) with identical setups
- Enable automated deployments while maintaining security controls

## Pipeline Overview

```mermaid
graph TB
    %% Infrastructure Pipeline
    A[Manual Trigger]
    A -->|Input Parameters| B[Provision Azure Resources with Terraform Init/Plan/Apply]
    B --> D[Output & Store Variables: Key vault name, Public IPs of VMs, etc]

    %% Prefect Deployment Pipeline
    D -->|Infrastructure Ready| E[Manual Trigger]
    E --> F[Build Prefect Images]
    F --> G[Push to ACR]
    G --> H[Pull from ACR]
    H --> I[Spin Up Images using <code>docker compose</code>]

    %% Application Deployment Pipeline
    D -->|Infrastructure Ready| K[PR Merged to UAT]
    K --> L[Build App Images]
    L --> M[Push to ACR]
    M --> N[Pull from ACR]
    N --> O[Spin Up Images using <code>docker compose</code>]

    subgraph Infrastructure Pipeline
        A & B & D
    end

    subgraph Prefect Deployment
        E & F & G & H & I
    end

    subgraph Application Deployment
        K & L & M & N & O
    end
```
Our deployment is split into 4 components: 
1. Infrastructure Building
2. Secrets Configuration
3. Prefect deployment
4. App deployment (continuous)

These steps are all driven by different GitHub Workflows. More info below. 

## Detailed Environment Setup and Deployment Flow
### 1. Infrastructure Pipeline


#### 1a. Branch Creation
First, we create a new `development`/`uat`/`production` branch depending on the environment we want. 

#### 1b. Infrastructure Provisioning
Next, we need to provision the Azure infrastructure using the `build-infrastructure.yml` workflow. 
- Uses Terraform for reproducible infrastructure
- Outputs critical values to GitHub environment variables
- Creates isolated network segments for app and Prefect VMs
- Provisions Azure Key Vault with RBAC for secret management

```
gh workflow run build-infrastructure.yml -f resource_group_name=uat-alephapp -f environment=uat --ref uat
```

```mermaid
graph TB
    A[Create new branch] --> B[Define VM usernames and passwords in GitHub Secrets for SSH]
    B -->|Manual Trigger<br/>Input Parameters: environment, location, resource_group_name| C
    subgraph build-infrastructure.yml
        C[Terraform Init/Plan/Apply]
    end

    C --> E[Output & Store Variables: Key vault name, Public IPs of VMs, etc]
```


### 2. Secrets Configuration
Before any deployment, we need to configure secrets in Azure Key Vault provisioned in step 1.

> **⚠ Note**
>
> Secret names have to be declared with dashes instead of underscores, i.e. `POSTGRES_HOST` should be `POSTGRES-HOST`
> 
> This is a limitation of Azure Key Vault. 


### 3. Prefect Server Deployment
After infrastructure has been set up, we can deploy Prefect server and DB using two workflows:

```mermaid
graph TB
    A[Start] --> B
    subgraph build-prefect-images.yml
        B[Build Images:<br/>- prefect_server<br/>- prefect_db<br/>- prefect_backup]
        B --> D[Push Images to ACR]
    end

    subgraph deploy-prefect.yml
        D --> E[Pull Images from ACR]
        E --> F[Spin Up Images using <code>docker compose</code>]
    end
```

### 4. Application Deployment
UAT deployments are triggered automatically on PR merges to UAT branches via `deploy-uat.yml`:

```mermaid
graph TB
    A[PR Merged to UAT] -->|Triggers| C[<code>deploy-uat.yml</code> Triggered]
    subgraph deploy-uat.yml
        C[Build Application Images]
        C --> D[Push to ACR]
        D --> E[Deploy to VM]
    end
```

### Deployment Environments

Each environment has its own:
- Azure Resource Group
- Key Vault containing `ENV` secrets
- Container Registry (ACR)
- VMs (App and Prefect)
- Environment secrets/variables in GitHub