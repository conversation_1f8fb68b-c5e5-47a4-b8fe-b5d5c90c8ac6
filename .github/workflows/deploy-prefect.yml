name: Deploy to Prefect VM

on:
  # push: 
  #   branches: 
  #     - '**'
  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string
      image_tag:
        required: true
        type: string
      ref: 
        required: true
        type: string
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      image_tag:
        required: true
        type: string
      ref: 
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - name: Generate Deployment Files
        run: |
          envsubst < docker-compose-prefect.yml > generated-compose.yml
        env:
          IMAGE_TAG: ${{ inputs.image_tag }}
          ACR_NAME: ${{ vars.ACR_NAME }}

      - name: Setup deployment directory
        run: |
          mkdir -p deploy
          cp generated-compose.yml deploy/docker-compose.yml
          cp deployment/deployment-prefect.sh deploy/

      - name: Prepare target directory
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.PREFECT_SERVER_PIP }}
          username: ${{ secrets.PREFECT_VM_USERNAME }}
          password: ${{ secrets.PREFECT_VM_PASSWORD }}
          script: |
            sudo mkdir -p /prefect
            sudo chown ${{ secrets.PREFECT_VM_USERNAME }}:${{ secrets.PREFECT_VM_USERNAME }} /prefect
            echo "::notice::Directory created"

      - name: Copy deployment files to VM
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.PREFECT_SERVER_PIP }}
          username: ${{ secrets.PREFECT_VM_USERNAME }}
          password: ${{ secrets.PREFECT_VM_PASSWORD }}
          source: "deploy/*"
          target: "/prefect"
          strip_components: 1
          debug: true

      - name: Execute deployment
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.PREFECT_SERVER_PIP }}
          username: ${{ secrets.PREFECT_VM_USERNAME }}
          password: ${{ secrets.PREFECT_VM_PASSWORD }}
          script: |
            cd /prefect
            chmod +x deployment-prefect.sh
            export KEY_VAULT_NAME=${{ vars.KEY_VAULT_NAME }}
            export ACR_NAME=${{ vars.ACR_NAME }}
            export SERVER_IDENTITY_CLIENT_ID=${{ secrets.SERVER_IDENTITY_CLIENT_ID }}
            ./deployment-prefect.sh