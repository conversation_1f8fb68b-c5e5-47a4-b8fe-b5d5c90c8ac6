name: 'Build Prefect Images'

on:
  # push: 
  #   branches: 
  #     - '**'
  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string
      image_tag: 
        required: true
        type: string
      ref: 
        required: true
        type: string
      matrix_config: 
        required: false
        type: string
        default: >-
          [
            {
              "service": "prefect_server",
              "dockerfile": "backend/infrastructure/_orchaestration/prefect.dockerfile",
              "context": "."
            },
            {
              "service": "prefect_db",
              "dockerfile": "backend/infrastructure/_orchaestration/prefect-postgres.dockerfile",
              "context": "."
            },
            {
              "service": "prefect_backup",
              "dockerfile": "backend/infrastructure/_orchaestration/prefect-backup.dockerfile",
              "context": "."
            },
          ]
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      image_tag: 
        required: true
        type: string
      ref: 
        required: true
        type: string
      matrix_config: 
        required: false
        type: string
        default: >-
          [
            {
              "service": "prefect_server",
              "dockerfile": "backend/infrastructure/orchaestration/prefect.dockerfile",
              "context": "."
            },
            {
              "service": "prefect_db",
              "dockerfile": "backend/infrastructure/orchaestration/prefect-postgres.dockerfile",
              "context": "."
            },
            {
              "service": "prefect_backup",
              "dockerfile": "backend/infrastructure/orchaestration/prefect-backup.dockerfile",
              "context": "."
            },
          ]

jobs:
  build-prefect-images: 
    uses: ./.github/workflows/build-images.yml
    secrets: inherit
    with: 
      environment: ${{ inputs.environment }}
      image_tag: ${{ inputs.image_tag }} # Static for now, to change depending on workflow
      ref: ${{ inputs.ref }}
      matrix_config: ${{ inputs.matrix_config }}
        