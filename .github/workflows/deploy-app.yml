name: Deploy to App VM

on:
  # push: 
  #   branches: 
  #     - '**'
  workflow_dispatch:
    inputs:
      environment:
        required: true
        type: string
      profile:
        required: true
        type: string
      image_tag:
        required: true
        type: string
      ref: 
        required: true
        type: string
        # example: refs/heads/<branch-name>
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
      profile:
        required: true
        type: string
      image_tag:
        required: true
        type: string
      ref: 
        required: true
        type: string

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    environment: ${{ inputs.environment }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.ref }}

      - name: Generate Deployment Files
        run: |
          envsubst < docker-compose.yml > generated-compose.yml
        env:
          IMAGE_TAG: ${{ inputs.image_tag }}
          ACR_NAME: ${{ vars.ACR_NAME }}

      - name: Setup deployment directory
        run: |
          mkdir -p deploy
          cp generated-compose.yml deploy/docker-compose.yml
          cp deployment/deployment.sh deploy/

      - name: Prepare target directory
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.APP_SERVER_PIP }}
          username: ${{ secrets.APP_VM_USERNAME }}
          password: ${{ secrets.APP_VM_PASSWORD }}
          script: |
            sudo mkdir -p /app
            sudo chown ${{ secrets.APP_VM_USERNAME }}:${{ secrets.APP_VM_USERNAME }} /app
            echo "::notice::Directory created"

      - name: Copy deployment files to VM
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.APP_SERVER_PIP }}
          username: ${{ secrets.APP_VM_USERNAME }}
          password: ${{ secrets.APP_VM_PASSWORD }}
          source: "deploy/*"
          target: "/app"
          strip_components: 1
          debug: true

      - name: Execute deployment
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.APP_SERVER_PIP }}
          username: ${{ secrets.APP_VM_USERNAME }}
          password: ${{ secrets.APP_VM_PASSWORD }}
          script: |
            cd /app
            chmod +x deployment.sh
            export KEY_VAULT_NAME=${{ vars.KEY_VAULT_NAME }}
            export PROFILE=${{ inputs.profile }}
            export ACR_NAME=${{ vars.ACR_NAME }}
            export SERVER_IDENTITY_CLIENT_ID=${{ secrets.SERVER_IDENTITY_CLIENT_ID }}
            ./deployment.sh