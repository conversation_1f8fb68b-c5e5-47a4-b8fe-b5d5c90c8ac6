name: UAT App Deployment

on:
  pull_request:
    types: [closed]
    branches:
      - 'uat**'

jobs:
  build-images:
    if: github.event.pull_request.merged == true
    uses: ./.github/workflows/build-app-images.yml
    secrets: inherit
    with:
      environment: ${{ github.base_ref }}
      image_tag: pr-${{ github.event.pull_request.number }}
      ref: refs/heads/${{ github.base_ref }}
    
  deploy-images:
    needs: build-images
    if: github.event.pull_request.merged == true
    uses: ./.github/workflows/deploy-app.yml
    secrets: inherit
    with:
      environment: ${{ github.base_ref }}
      image_tag: pr-${{ github.event.pull_request.number }}
      ref: refs/heads/${{ github.base_ref }}
      profile: production